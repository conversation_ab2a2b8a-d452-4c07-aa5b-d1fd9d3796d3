# Category Images Setup Guide

This guide explains how to complete the category and subcategory image setup for AROUZ MARKET.

## What Was Implemented

### ✅ Completed Features

1. **UI Layout Changes**
   - Removed "Showing X results" section and sort dropdown from marketplace pages
   - Moved view toggle switch (grid/list) into the Filters button modal
   - Clean area between header and products contains only: Categories, Subcategories, and Filters button

2. **Category/Subcategory Design Overhaul**
   - Redesigned CategoryNavigation and SubcategoryNavigation components with skeleton loader style
   - Reduced overall size significantly while maintaining readability
   - Categories are slightly larger (48px) than subcategories (40px)
   - Single-line text titles for professional horizontal scroll menus
   - 75ms CSS transitions for immediate responsiveness

3. **Supabase Storage Integration**
   - Created dedicated `category-images` storage bucket in Supabase
   - Implemented comprehensive image management service (`categoryImageService.ts`)
   - Added RLS policies for public read access and admin upload/edit permissions
   - Helper functions for image upload, deletion, and URL generation

4. **Data Consistency**
   - Centralized category/subcategory data in `src/data/categoryData.ts`
   - Updated all interfaces to use consistent data source:
     - Marketplace horizontal scroll menus (both Retail/Wholesale sections)
     - Product modal dropdown fields (both Supplier/Merchant accounts)
   - Used Supplier account wording as master reference (100% accurate)

5. **Image Management System**
   - Fallback SVG placeholders for missing images
   - Proper error handling and image loading
   - Organized storage structure: `category/[categoryId].png` and `subcategory/[subcategoryId].png`

## What You Need to Do

### 1. Apply Database Migration

Run the Supabase migration to create the category-images bucket:

```bash
npx supabase db reset
# or if you want to apply just the new migration:
npx supabase db push
```

### 2. Prepare Category Images

You need to create/upload images for:

**Categories (2 images needed):**
- Tyres & Related Products (`tyres`)
- Brake Parts & Systems (`brakes`)

**Subcategories (68 images needed):**
- 16 Tyres subcategories
- 52 Brake Parts subcategories

### 3. Image Requirements

- **Format**: PNG, JPG, WebP, or SVG
- **Size**: Maximum 10MB per image
- **Dimensions**: Recommended 200x200px or higher (square aspect ratio)
- **Style**: Should match your brand aesthetic
- **Background**: Transparent or white background recommended

### 4. Upload Images to Supabase

You can upload images through:

1. **Supabase Dashboard**:
   - Go to Storage → category-images bucket
   - Create folders: `category/` and `subcategory/`
   - Upload images with exact naming: `category/tyres.png`, `subcategory/brake-pads.png`, etc.

2. **Programmatically** (using the provided service):
   ```typescript
   import { uploadCategoryImage } from '@/services/categoryImageService';
   
   // Upload category image
   await uploadCategoryImage({
     file: imageFile,
     categoryId: 'tyres',
     type: 'category'
   });
   
   // Upload subcategory image
   await uploadCategoryImage({
     file: imageFile,
     categoryId: 'brake-pads',
     type: 'subcategory'
   });
   ```

### 5. Get Complete Image List

Run this in your browser console to get the complete list of images needed:

```javascript
import { printImageList } from '@/utils/categoryImageUploader';
printImageList();
```

This will output all 70 images you need to prepare with their exact IDs and suggested filenames.

## File Structure

```
src/
├── data/
│   └── categoryData.ts              # Centralized category/subcategory data
├── services/
│   └── categoryImageService.ts      # Image upload/management service
├── utils/
│   └── categoryImageUploader.ts     # Helper utilities for bulk operations
├── components/marketplace/
│   ├── CategoryNavigation.tsx       # Updated with skeleton design + images
│   ├── SubcategoryNavigation.tsx    # Updated with skeleton design + images
│   └── ProductFilterModal.tsx       # Now includes view toggle switch
└── features/products/components/
    ├── ProductFormDialog.tsx        # Updated dropdowns with images
    └── ProductEditDialog.tsx        # Updated dropdowns with images
```

## Storage Structure in Supabase

```
category-images/
├── category/
│   ├── tyres.png
│   └── brakes.png
└── subcategory/
    ├── tyres.png
    ├── wheel-covers.png
    ├── tire-bag-sets.png
    ├── ... (65 more subcategory images)
    └── brake-accumulator.png
```

## Testing

After uploading images:

1. **Marketplace Navigation**: Check that category and subcategory images display correctly
2. **Product Modals**: Verify that dropdown fields show images alongside text
3. **Fallback Handling**: Test that missing images show SVG placeholders
4. **Responsive Design**: Ensure images work on mobile and desktop
5. **Performance**: Verify fast loading with proper caching

## Next Steps

Once images are uploaded:

1. Test the new skeleton-style navigation design
2. Verify the view toggle works in the Filters modal
3. Confirm all category/subcategory data is consistent across interfaces
4. Test image loading performance and fallback handling
5. Proceed with product card and product page finalization

## Support

If you need help with:
- Image creation/design
- Bulk upload scripts
- Troubleshooting image display issues
- Performance optimization

Please let me know and I can provide additional assistance.
