-- EMERGENCY FIX: Create a robust trigger function that handles all edge cases
-- This will fix the production issue immediately

-- Drop the existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create a new, more robust trigger function with extensive error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
  user_phone TEXT;
  user_full_name TEXT;
  user_first_name TEXT;
  user_last_name TEXT;
  user_company_name TEXT;
  user_store_name TEXT;
  user_store_address TEXT;
BEGIN
  -- Log the trigger execution
  RAISE NOTICE 'Trigger handle_new_user called for user: %', NEW.id;
  
  -- Extract data from metadata with extensive null checking
  BEGIN
    user_role := COALESCE(NEW.raw_user_meta_data->>'role', 'consumer');
    user_phone := NEW.phone;
    user_full_name := COALESCE(NEW.raw_user_meta_data->>'fullName', '');
    user_first_name := COALESCE(NEW.raw_user_meta_data->>'firstName', '');
    user_last_name := COALESCE(NEW.raw_user_meta_data->>'lastName', '');
    user_company_name := CASE WHEN user_role = 'supplier' THEN NEW.raw_user_meta_data->>'companyName' ELSE NULL END;
    user_store_name := CASE WHEN user_role = 'merchant' THEN NEW.raw_user_meta_data->>'storeName' ELSE NULL END;
    user_store_address := CASE WHEN user_role = 'merchant' THEN NEW.raw_user_meta_data->>'storeAddress' ELSE NULL END;
    
    RAISE NOTICE 'Extracted metadata - Role: %, Phone: %, FullName: %', user_role, user_phone, user_full_name;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Error extracting metadata: % %', SQLSTATE, SQLERRM;
      -- Set defaults if metadata extraction fails
      user_role := 'consumer';
      user_phone := NEW.phone;
      user_full_name := '';
      user_first_name := '';
      user_last_name := '';
      user_company_name := NULL;
      user_store_name := NULL;
      user_store_address := NULL;
  END;
  
  -- Format phone number if it exists
  IF user_phone IS NOT NULL AND user_phone != '' THEN
    BEGIN
      IF user_phone LIKE '+213%' THEN
        -- Already formatted
        user_phone := user_phone;
      ELSIF user_phone LIKE '0%' THEN
        -- Remove leading 0 and add country code
        user_phone := '+213' || SUBSTRING(user_phone FROM 2);
      ELSE
        -- Add country code
        user_phone := '+213' || user_phone;
      END IF;
      RAISE NOTICE 'Formatted phone: %', user_phone;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE NOTICE 'Error formatting phone: % %', SQLSTATE, SQLERRM;
        user_phone := NEW.phone; -- Use original if formatting fails
    END;
  END IF;
  
  -- Insert the profile with comprehensive error handling
  BEGIN
    INSERT INTO public.profiles (
      id,
      email,
      phone,
      role,
      full_name,
      first_name,
      last_name,
      company_name,
      store_name,
      store_address,
      email_verified,
      phone_verified,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.email,
      user_phone,
      user_role,
      user_full_name,
      user_first_name,
      user_last_name,
      user_company_name,
      user_store_name,
      user_store_address,
      false,
      false,
      now(),
      now()
    );
    
    RAISE NOTICE 'Profile created successfully for user: %', NEW.id;
    
  EXCEPTION
    WHEN unique_violation THEN
      -- Profile already exists, update it instead
      RAISE NOTICE 'Profile already exists, updating for user: %', NEW.id;
      BEGIN
        UPDATE public.profiles SET
          email = NEW.email,
          phone = user_phone,
          role = user_role,
          full_name = user_full_name,
          first_name = user_first_name,
          last_name = user_last_name,
          company_name = user_company_name,
          store_name = user_store_name,
          store_address = user_store_address,
          updated_at = now()
        WHERE id = NEW.id;
        
        RAISE NOTICE 'Profile updated successfully for user: %', NEW.id;
      EXCEPTION
        WHEN OTHERS THEN
          RAISE NOTICE 'Error updating existing profile: % %', SQLSTATE, SQLERRM;
      END;
      
    WHEN OTHERS THEN
      RAISE NOTICE 'Error inserting profile: % %', SQLSTATE, SQLERRM;
      -- Try a minimal insert as fallback
      BEGIN
        INSERT INTO public.profiles (id, email, role, created_at, updated_at)
        VALUES (NEW.id, NEW.email, user_role, now(), now())
        ON CONFLICT (id) DO UPDATE SET
          email = EXCLUDED.email,
          role = EXCLUDED.role,
          updated_at = now();
        RAISE NOTICE 'Minimal profile created as fallback for user: %', NEW.id;
      EXCEPTION
        WHEN OTHERS THEN
          RAISE NOTICE 'Even minimal profile creation failed: % %', SQLSTATE, SQLERRM;
      END;
  END;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Verify the trigger was created
SELECT 
  trigger_name,
  event_manipulation,
  event_object_table
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

RAISE NOTICE 'Emergency trigger fix applied successfully';
