import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Jetour models file
const jetourModelsPath = path.join(__dirname, 'src', 'services', 'data', 'jetour-models.json');
const jetourModels = JSON.parse(fs.readFileSync(jetourModelsPath, 'utf8'));

// Create the Jetour brand object
const jetourBrand = {
  "brand": "Jetour",
  "image_url": "/images/brands/jetour.png",
  "models": jetourModels
};

// Add the Jetour brand to the cars.json file
carsJson.push(jetourBrand);

// Write the updated cars.json file
fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

console.log(`Added Jetour brand with ${jetourModels.length} models to the cars.json file.`);

// Clean up the temporary file
fs.unlinkSync(jetourModelsPath);

console.log('Temporary file cleaned up.');
