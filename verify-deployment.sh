#!/bin/bash

# 🔍 AROUZ MARKET Deployment Verification Script
# This script verifies that your deployment is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}"
    echo "🔍 AROUZ MARKET DEPLOYMENT VERIFICATION"
    echo "======================================"
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if a URL is accessible
check_url() {
    local url=$1
    local name=$2
    local timeout=${3:-10}
    
    print_info "Checking $name..."
    
    if curl -f -s -m $timeout "$url" > /dev/null; then
        print_success "$name is accessible"
        return 0
    else
        print_error "$name is not accessible"
        return 1
    fi
}

# Check if a URL returns specific content
check_url_content() {
    local url=$1
    local expected=$2
    local name=$3
    local timeout=${4:-10}
    
    print_info "Checking $name content..."
    
    local content=$(curl -f -s -m $timeout "$url" 2>/dev/null || echo "")
    
    if [[ "$content" == *"$expected"* ]]; then
        print_success "$name contains expected content"
        return 0
    else
        print_error "$name does not contain expected content"
        return 1
    fi
}

# Check SSL certificate
check_ssl() {
    local domain=$1
    local name=$2
    
    print_info "Checking SSL certificate for $name..."
    
    local expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null | grep notAfter | cut -d= -f2)
    
    if [ -n "$expiry_date" ]; then
        local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s 2>/dev/null)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [ $days_until_expiry -gt 30 ]; then
            print_success "SSL certificate is valid ($days_until_expiry days remaining)"
            return 0
        else
            print_warning "SSL certificate expires soon ($days_until_expiry days)"
            return 1
        fi
    else
        print_error "Could not check SSL certificate"
        return 1
    fi
}

# Check Supabase connectivity
check_supabase() {
    local supabase_url="https://irkwpzcskeqtasutqnxp.supabase.co"
    
    print_info "Checking Supabase backend..."
    
    if check_url "$supabase_url/rest/v1/" "Supabase API"; then
        print_success "Supabase backend is accessible"
        
        # Check Edge Functions
        if check_url "$supabase_url/functions/v1/send-sms-otp" "SMS OTP Function"; then
            print_success "SMS OTP function is deployed"
        else
            print_warning "SMS OTP function may not be deployed"
        fi
        
        return 0
    else
        print_error "Supabase backend is not accessible"
        return 1
    fi
}

# Check GitHub Actions status
check_github_actions() {
    print_info "Checking GitHub Actions status..."
    
    if command -v gh &> /dev/null; then
        if gh auth status &> /dev/null; then
            local latest_run=$(gh run list --limit 1 --json status,conclusion,url 2>/dev/null || echo "[]")
            
            if [ "$latest_run" != "[]" ]; then
                print_success "GitHub Actions is accessible"
                echo "Latest run status: $latest_run"
            else
                print_warning "No recent GitHub Actions runs found"
            fi
        else
            print_warning "GitHub CLI not authenticated. Run 'gh auth login' to check Actions status"
        fi
    else
        print_warning "GitHub CLI not installed. Cannot check Actions status"
    fi
}

# Main verification function
main() {
    print_header
    
    local errors=0
    
    # Check development environment
    echo -e "\n${BLUE}🔧 DEVELOPMENT ENVIRONMENT${NC}"
    echo "─────────────────────────────"
    
    if ! check_url "https://dev.arouzmarket.com" "Development Site"; then
        ((errors++))
    fi
    
    # Check production environment
    echo -e "\n${BLUE}🏭 PRODUCTION ENVIRONMENT${NC}"
    echo "─────────────────────────────"
    
    if ! check_url "https://arouzmarket.com" "Production Site"; then
        ((errors++))
    fi
    
    if ! check_ssl "arouzmarket.com" "Production SSL"; then
        ((errors++))
    fi
    
    # Check critical pages
    echo -e "\n${BLUE}📄 CRITICAL PAGES${NC}"
    echo "─────────────────────────"
    
    local pages=("/" "/about" "/contact" "/marketplace")
    for page in "${pages[@]}"; do
        if ! check_url "https://arouzmarket.com$page" "Page: $page"; then
            ((errors++))
        fi
    done
    
    # Check backend services
    echo -e "\n${BLUE}🗄️ BACKEND SERVICES${NC}"
    echo "─────────────────────────"
    
    if ! check_supabase; then
        ((errors++))
    fi
    
    # Check CI/CD
    echo -e "\n${BLUE}🔄 CI/CD PIPELINE${NC}"
    echo "─────────────────────────"
    
    check_github_actions
    
    # Check if AROUZ MARKET branding is present
    echo -e "\n${BLUE}🎨 BRANDING VERIFICATION${NC}"
    echo "─────────────────────────────"
    
    if check_url_content "https://arouzmarket.com" "AROUZ MARKET" "AROUZ MARKET Branding"; then
        print_success "AROUZ MARKET branding is present"
    else
        print_warning "AROUZ MARKET branding may not be visible"
        ((errors++))
    fi
    
    # Final summary
    echo -e "\n${BLUE}📊 VERIFICATION SUMMARY${NC}"
    echo "─────────────────────────"
    
    if [ $errors -eq 0 ]; then
        print_success "All checks passed! 🎉"
        echo -e "\n${GREEN}🚀 AROUZ MARKET is successfully deployed and operational!${NC}"
        echo -e "${GREEN}🌐 Production URL: https://arouzmarket.com${NC}"
        echo -e "${GREEN}🔧 Development URL: https://dev.arouzmarket.com${NC}"
    else
        print_error "$errors issues found"
        echo -e "\n${YELLOW}⚠️  Please review the issues above and fix them before going live.${NC}"
        echo -e "${YELLOW}📖 Check the COMPLETE-DEPLOYMENT-GUIDE.md for troubleshooting steps.${NC}"
        exit 1
    fi
    
    # Performance recommendations
    echo -e "\n${BLUE}⚡ PERFORMANCE RECOMMENDATIONS${NC}"
    echo "─────────────────────────────────"
    echo "• Monitor site performance with Lighthouse"
    echo "• Set up monitoring alerts for downtime"
    echo "• Regular database backups"
    echo "• Monitor Supabase usage to stay within free tier"
    echo "• Consider CDN for faster global loading"
    
    echo -e "\n${GREEN}✅ Verification complete!${NC}"
}

# Run verification
main "$@"
