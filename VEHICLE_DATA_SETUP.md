# Vehicle Data Setup Guide

This guide explains how to set up the vehicle data backend using Supabase.

## Overview

The AROUZ MARKET Compatibility Hub uses a structured JSON format for vehicle data, stored securely in Supabase. This approach provides:

1. Secure storage of vehicle data that cannot be accessed directly from the browser
2. Efficient querying and filtering of vehicle data
3. Easy updates to the vehicle database as new vehicles are added

## Data Structure

The vehicle data is organized in separate JSON files for each vehicle type:

- `cars.json` - Data for cars
- `motorcycles.json` - Data for motorcycles (to be implemented)
- `trucks.json` - Data for trucks (to be implemented)

Each file follows a specific schema as described below.

### Cars JSON Schema

```json
[
  {
    "brand": "BMW",
    "image_url": "https://cdn.example.com/logos/bmw.png",
    "models": [
      {
        "model": "1 Series",
        "generations": [
          {
            "code": "F20",
            "start": "11.2010",
            "end": "06.2019",
            "engines": [
              {
                "code": "114d",
                "fuel_type": "Diesel",
                "power_kW": 70,
                "power_HP": 95,
                "start": "07.2015",
                "end": "06.2019"
              }
            ]
          }
        ]
      }
    ]
  }
]
```

## Supabase Setup

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com/) and sign in or create an account
2. Create a new project
3. Note your project URL and anon key for later use

### 2. Create the Vehicle Data Table

Execute the following SQL in the Supabase SQL Editor:

```sql
-- Create the vehicle_data table
CREATE TABLE vehicle_data (
  id SERIAL PRIMARY KEY,
  type TEXT NOT NULL UNIQUE,
  data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on the type column for faster lookups
CREATE INDEX vehicle_data_type_idx ON vehicle_data (type);

-- Set up Row Level Security (RLS)
ALTER TABLE vehicle_data ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows anyone to read the data
CREATE POLICY "Allow public read access" 
  ON vehicle_data 
  FOR SELECT 
  USING (true);

-- Create a policy that only allows authenticated users to modify the data
CREATE POLICY "Allow authenticated users to modify data" 
  ON vehicle_data 
  FOR ALL 
  USING (auth.role() = 'authenticated');
```

### 3. Configure Environment Variables

Create a `.env` file in the root of your project with the following variables:

```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 4. Upload the Vehicle Data

Run the upload script to populate the Supabase database with the vehicle data:

```bash
node scripts/uploadVehicleData.js
```

## Updating Vehicle Data

To update the vehicle data:

1. Modify the appropriate JSON file (`cars.json`, `motorcycles.json`, or `trucks.json`)
2. Run the upload script again to update the data in Supabase

## Security Considerations

- The vehicle data is stored securely in Supabase and cannot be accessed directly from the browser
- Row Level Security (RLS) ensures that only authorized users can modify the data
- The frontend only receives the data it needs for the current view, reducing data exposure

## Troubleshooting

If you encounter issues with the vehicle data setup:

1. Check that your Supabase URL and anon key are correct
2. Verify that the vehicle_data table exists and has the correct structure
3. Ensure that the JSON files are valid and follow the correct schema
4. Check the browser console for any error messages

For further assistance, please contact the development team.
