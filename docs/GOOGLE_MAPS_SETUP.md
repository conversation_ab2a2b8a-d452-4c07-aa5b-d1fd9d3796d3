# 🗺️ Google Maps API Setup Guide (100% FREE)

## Quick Setup (5 minutes)

### Step 1: Get Your FREE Google Maps API Key

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create a New Project**
   - Click "Select a project" → "New Project"
   - Name: "AROUZ MARKET Maps"
   - Click "Create"

3. **Enable Required APIs**
   - Go to "APIs & Services" → "Library"
   - Search and enable:
     - ✅ **Maps JavaScript API**
     - ✅ **Places API**

4. **Create API Key**
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "API Key"
   - Copy your API key (starts with `AIza...`)

### Step 2: Configure Your Project

1. **Add to Environment Variables**
   ```bash
   # Add to your .env file
   VITE_GOOGLE_MAPS_API_KEY=AIzaSyC-your-actual-api-key-here
   ```

2. **Restart Development Server**
   ```bash
   npm run dev
   ```

3. **Test the Integration**
   - Visit: http://localhost:8080/google-maps-test
   - You should see "API Key Configured ✅"

### Step 3: Secure Your API Key (Recommended)

1. **Restrict by Domain**
   - In Google Cloud Console → Credentials
   - Click your API key → "Application restrictions"
   - Select "HTTP referrers (web sites)"
   - Add: `localhost:8080/*` and your domain

2. **Restrict by API**
   - In "API restrictions"
   - Select "Restrict key"
   - Choose: Maps JavaScript API, Places API

## 💰 Cost Breakdown (FREE!)

### Google Maps Free Tier
- **Maps JavaScript API**: 28,000 loads/month FREE
- **Places API**: 17,000 requests/month FREE
- **Our expected usage**: ~5,000 loads/month
- **Total cost**: $0/month 🎉

### Usage Monitoring
- Monitor usage in Google Cloud Console
- Set up billing alerts (optional)
- We're well under free limits

## 🎯 What You Get

### ✅ With Google Maps API Key
```
🔍 Rich location search with autocomplete
📍 Detailed Arabic place names
🗺️ Street-level accuracy
🏢 Landmarks and businesses
🎨 Familiar Google Maps interface
✅ 100% accurate wilaya detection
```

### 📍 Without API Key (Fallback)
```
🗺️ OpenStreetMap (basic functionality)
📍 Click-based location selection
✅ 100% accurate wilaya detection
💡 Hint to add API key for enhanced features
```

## 🔧 Troubleshooting

### "Map not loading"
- ✅ Check internet connection
- ✅ Verify API key in .env file
- ✅ Restart development server

### "API Key error"
- ✅ Ensure APIs are enabled (Maps JavaScript + Places)
- ✅ Check API key restrictions
- ✅ Verify billing account (even for free tier)

### "Quota exceeded"
- ✅ Check usage in Google Cloud Console
- ✅ We're well under free limits
- ✅ Contact support if needed

## 🚀 Advanced Configuration

### Custom Map Styling
```javascript
// In GoogleMapLocationSelector.tsx
styles: [
  {
    featureType: 'poi',
    elementType: 'labels',
    stylers: [{ visibility: 'on' }]
  }
]
```

### Language & Region
```javascript
// Already configured for Algeria
language: 'ar',  // Arabic language
region: 'DZ'     // Algeria region
```

## 📊 Monitoring Usage

### Google Cloud Console
1. Go to "APIs & Services" → "Dashboard"
2. View usage charts
3. Set up alerts if needed

### Expected Monthly Usage
- **Map loads**: ~5,000 (well under 28,000 limit)
- **Place searches**: ~2,000 (well under 17,000 limit)
- **Cost**: $0 💰

## 🔒 Security Best Practices

### ✅ DO
- Restrict API key by domain
- Restrict API key by API type
- Monitor usage regularly
- Use environment variables

### ❌ DON'T
- Commit API key to version control
- Share API key publicly
- Use unrestricted API keys
- Ignore usage monitoring

## 🎉 Success!

Once configured, you'll have:
- 🗺️ **World-class location selection**
- 🇩🇿 **100% accurate Algeria coverage**
- 🔍 **Rich Arabic place search**
- 💰 **Completely FREE**
- 🚀 **Production-ready**

Visit `/google-maps-test` to see it in action! 🎯
