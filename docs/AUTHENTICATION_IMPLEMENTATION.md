# AROUZ MARKET Authentication Implementation Guide

This document provides step-by-step instructions for implementing the multi-role authentication system for AROUZ MARKET using Supabase.

## Overview

The authentication system supports four user roles:
- Supplier & Manufacturer (B2B)
- Merchant Retailer (B2C)
- Consumer
- Distribution

The implementation uses the existing profiles table in Supabase, with modifications to support all roles.

## Implementation Steps

### Step 1: Update the Profiles Table Schema

1. Connect to your Supabase project
2. Go to the SQL Editor
3. Run the migration script:
   ```bash
   # Navigate to the migration directory
   cd supabase/migrations
   
   # Run the migration script through the Supabase dashboard SQL Editor
   # Copy and paste the contents of 20231102000000_update_profiles_table.sql
   ```

4. Verify the migration completed successfully:
   - Check the profiles table structure
   - Verify RLS policies are in place

### Step 2: Configure Environment Variables

1. Create or update your `.env` file:
   ```
   VITE_SUPABASE_URL=https://irkwpzcskeqtasutqnxp.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   VITE_USE_SUPABASE=true
   VITE_APP_ENV=development
   ```

2. Restart your development server to apply the changes

### Step 3: Test the Authentication System

1. Follow the testing guide in `docs/AUTHENTICATION_TESTING.md`
2. Verify all authentication flows work correctly
3. Check that user profiles are created properly in the database

### Step 4: Prepare for Production

1. Follow the deployment guide in `docs/AUTHENTICATION_DEPLOYMENT.md`
2. Configure production environment variables
3. Set up SMS provider for phone authentication
4. Customize email templates

## Code Changes Made

### 1. Updated TypeScript Interfaces

The `UserProfile` interface has been updated to match the existing table structure:

```typescript
export interface UserProfile {
  id: string;
  email?: string;
  phone?: string;
  role: UserRole;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  store_name?: string;
  store_address?: string;
  tax_id?: string;
  address?: string;
  fleet_size?: number;
  avatar_url?: string;
  last_login?: string;
  email_verified?: boolean;
  created_at?: string;
  updated_at?: string;
}
```

The `RegistrationData` interface has been updated to include first and last name:

```typescript
export interface RegistrationData {
  email: string;
  phone: string;
  password: string;
  role: UserRole;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  storeName?: string;
  storeAddress?: string;
}
```

### 2. Updated Authentication Service

The `authService.ts` file has been modified to:
- Use the environment variable `VITE_USE_SUPABASE` to determine whether to use real Supabase or development mode
- Split full name into first and last name for database storage
- Update profile creation to match the existing table structure
- Add proper error handling and logging

### 3. Updated RoleAuthModal Component

The `RoleAuthModal.tsx` component has been updated to:
- Split full name into first and last name for database storage
- Remove redundant development mode checks
- Use the authService for all authentication operations

### 4. Database Migration Script

A migration script has been created to:
- Add missing columns to the profiles table
- Set up proper role constraints
- Implement Row Level Security policies
- Create triggers for automatic profile creation

## Troubleshooting

### Common Issues

1. **Authentication fails silently**
   - Check browser console for errors
   - Verify Supabase URL and anon key are correct
   - Check if `VITE_USE_SUPABASE` is set to `true`

2. **OTP verification fails**
   - Check phone number format (should be +213 format)
   - Verify SMS provider is configured in Supabase
   - Check rate limits for OTP sending

3. **Profile not created**
   - Check for errors in browser console
   - Verify the profiles table exists with correct schema
   - Check RLS policies allow profile creation

### Development Mode

For testing without making actual API calls:
1. Set `VITE_USE_SUPABASE=false` in your `.env` file
2. Restart the development server
3. Use the application normally - it will simulate API calls

## Next Steps

1. Implement profile management features
2. Add role-specific dashboards
3. Implement email verification workflow
4. Set up audit logging for authentication events

## Additional Resources

- [Supabase Authentication Documentation](https://supabase.com/docs/guides/auth)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Phone Authentication Setup](https://supabase.com/docs/guides/auth/phone-login)
