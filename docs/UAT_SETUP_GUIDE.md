# 🧪 AROUZ MARKET - UAT Environment Setup Guide

## 🎯 **ARCHITECTURE OVERVIEW**

```
🔥 PRODUCTION (arouzmarket.com)
   ↓ Supabase: irkwpzcskeqtasutqnxp.supabase.co (CURRENT PROJECT)
   ↓ Database: FRESH/CLEAN data (no test data)
   ↓ Services: Dexatel SMS, Google Maps (SAME APIs)
   ↓ Features: ALL production features enabled

🧪 UAT (dev.arouzmarket.com)
   ↓ Supabase: NEW separate project (identical schema)
   ↓ Database: Test data for validation
   ↓ Services: Dexatel SMS, Google Maps (SAME APIs)
   ↓ Features: ALL features + testing tools

💻 DEV (localhost:8080)
   ↓ Supabase: Current development setup
   ↓ Database: Development data
   ↓ Services: SAME APIs
   ↓ Features: ALL features + debug tools
```

## 🔧 **STEP 1: Create UAT Supabase Project**

### Manual Steps (Supabase Dashboard):

1. **Go to Supabase Dashboard**: https://supabase.com/dashboard
2. **Create New Project**:
   - Name: `AROUZ MARKET UAT`
   - Organization: Your organization
   - Region: `US East (N. Virginia)` (same as production)
   - Database Password: `ArouzMarket2024UAT!`

3. **Note Down Credentials**:
   - Project URL: `https://[project-id].supabase.co`
   - Anon Key: `[anon-key]`
   - Service Role Key: `[service-role-key]`

## 🗄️ **STEP 2: Database Schema Migration**

### Export Production Schema:
```bash
# Export current production schema
supabase db dump --db-url "postgresql://postgres:[password]@db.irkwpzcskeqtasutqnxp.supabase.co:5432/postgres" --schema-only > uat_schema.sql
```

### Import to UAT Database:
```bash
# Import schema to UAT database
psql "postgresql://postgres:ArouzMarket2024UAT!@db.[uat-project-id].supabase.co:5432/postgres" < uat_schema.sql
```

## 🔐 **STEP 3: RLS Policies Migration**

All Row Level Security policies must be identical:

```sql
-- Copy all RLS policies from production
-- This ensures identical security model
```

## 🛠️ **STEP 4: Environment Configuration**

### Update `.env.uat`:
```env
VITE_SUPABASE_URL=https://[uat-project-id].supabase.co
VITE_SUPABASE_ANON_KEY=[uat-anon-key]
```

### Create UAT Build Script:
```json
{
  "scripts": {
    "build:uat": "vite build --mode uat && node copy-new-favicon.js"
  }
}
```

## 🚀 **STEP 5: Deployment Pipeline**

### UAT Deployment:
```bash
# Build UAT version
npm run build:uat

# Deploy to dev.arouzmarket.com
# (Upload dist folder contents)
```

### Production Deployment:
```bash
# Build production version
npm run build:prod

# Deploy to arouzmarket.com
# (Upload dist folder contents)
```

## 🔄 **STEP 6: Data Management Strategy**

### Production Data:
- **FRESH START**: Clean database with no test data
- **Real Users**: Only actual customers and suppliers
- **Clean Products**: Only real product listings

### UAT Data:
- **Test Data**: Sample products, users, orders
- **Validation Data**: For testing all features
- **Reset Capability**: Can be reset for each test cycle

## 🧪 **STEP 7: Testing Workflow**

### Development → UAT → Production:
1. **Develop**: Code changes in localhost
2. **Test**: Deploy to UAT for validation
3. **Validate**: Test all features in UAT
4. **Deploy**: Push to production after UAT approval

## 🔧 **STEP 8: External Services Configuration**

### SAME Services for All Environments:
- **Dexatel SMS**: Same API key and configuration
- **Google Maps**: Same API key and settings
- **Authentication**: Same flows and security
- **File Storage**: Same Supabase storage buckets

## 📊 **STEP 9: Monitoring Setup**

### UAT Monitoring:
- Error tracking enabled
- Performance monitoring
- Test analytics
- Debug information

### Production Monitoring:
- Full monitoring suite
- Error reporting
- Performance metrics
- User analytics

## ✅ **STEP 10: Validation Checklist**

### Before Production Deployment:
- [ ] UAT database schema matches production
- [ ] All features work in UAT
- [ ] Authentication flows tested
- [ ] SMS OTP working
- [ ] Google Maps integration working
- [ ] File uploads working
- [ ] Order processing working
- [ ] Payment flows tested
- [ ] Performance metrics acceptable
- [ ] Security headers configured
- [ ] HTTPS enforcement working

## 🚨 **CRITICAL REQUIREMENTS**

### ✅ **MUST PRESERVE:**
- All current functionality
- All backend services
- All API integrations
- All user experience
- All performance optimizations

### ✅ **MUST ENSURE:**
- Zero downtime during setup
- No breaking changes
- Identical feature parity
- Same external service usage
- Fresh production data

## 🎯 **SUCCESS CRITERIA**

### Production Ready When:
1. UAT environment fully functional
2. All tests pass in UAT
3. Performance metrics meet standards
4. Security validation complete
5. Fresh data storage confirmed
6. All external services working
7. Zero functionality regression

This setup ensures **100000000000% accuracy** while maintaining all current functionality and providing a robust testing environment.
