# Authentication Deployment Guide for AROUZ MARKET

This guide provides step-by-step instructions for deploying the Supabase authentication system for AROUZ MARKET to production.

## Prerequisites

1. A Supabase account with admin access
2. Access to the AROUZ MARKET codebase
3. A production environment for deploying the application
4. A domain name for the production environment

## 1. Supabase Production Configuration

### 1.1 Configure Authentication Settings

1. Go to Supabase dashboard → Authentication → Settings
2. Update Site URL to your production domain (e.g., https://arouz-market.com)
3. Add your production domain to Redirect URLs
4. Configure Email Templates:
   - Go to Authentication → Email Templates
   - Update the templates with AROUZ MARKET branding
   - Customize the email content for:
     - Confirmation
     - Invitation
     - Magic Link
     - Reset Password
     - Change Email

### 1.2 Configure SMS Provider for Phone Authentication

1. Go to Authentication → Phone Providers
2. Set up a production SMS provider:
   - For Twilio:
     - Account SID
     - Auth Token
     - Messaging Service ID
   - For other providers, follow their specific setup instructions
3. Test the SMS provider with a real phone number

### 1.3 Configure Security Settings

1. Go to Authentication → Settings → Security
2. Set appropriate session timeouts:
   - JWT expiry: 3600 seconds (1 hour) recommended
   - Enable refresh token rotation
   - Set refresh token reuse interval to 10 seconds
3. Configure rate limiting:
   - Enable rate limiting for signup, login, and password reset
   - Set appropriate limits based on expected traffic

## 2. Database Configuration

### 2.1 Run Migration Scripts

1. Connect to your production Supabase instance
2. Run the migration script to update the profiles table:
   - Go to SQL Editor
   - Run the contents of `supabase/migrations/20231102000000_update_profiles_table.sql`
3. Verify the migration completed successfully:
   - Check the profiles table structure
   - Verify RLS policies are in place

### 2.2 Verify Row Level Security

1. Go to Table Editor → profiles → Policies
2. Ensure these policies exist:
   - "Users can view their own profile"
   - "Users can update their own profile"
   - "Users can insert their own profile"
3. Test the policies with a test user to ensure they work correctly

## 3. Application Deployment

### 3.1 Configure Environment Variables

1. Set up production environment variables:
   ```
   VITE_SUPABASE_URL=https://your-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   VITE_USE_SUPABASE=true
   VITE_APP_ENV=production
   VITE_APP_URL=https://your-production-domain.com
   ```

2. Ensure these variables are securely stored in your deployment platform

### 3.2 Build and Deploy the Application

1. Build the application for production:
   ```bash
   npm run build
   ```

2. Deploy the built application to your hosting provider
3. Verify the application is accessible at your production URL

## 4. Post-Deployment Verification

### 4.1 Test Authentication Flows

1. Test Supplier registration
2. Test Merchant registration
3. Test phone authentication
4. Test email/password authentication
5. Test password reset
6. Verify all flows work correctly in production

### 4.2 Verify Security Measures

1. Test CSRF protection
2. Verify rate limiting works
3. Check that sessions expire correctly
4. Ensure RLS policies are enforced

### 4.3 Monitor for Issues

1. Set up logging for authentication events
2. Configure alerts for authentication failures
3. Monitor Supabase usage and performance

## 5. Backup and Recovery

### 5.1 Configure Database Backups

1. Go to Supabase dashboard → Project Settings → Database
2. Enable Point-in-Time Recovery (PITR)
3. Set an appropriate backup schedule

### 5.2 Document Recovery Procedures

1. Create a document outlining steps to recover from:
   - Database corruption
   - Authentication service outage
   - Data loss scenarios

## 6. Maintenance Plan

### 6.1 Regular Updates

1. Schedule regular updates for:
   - Supabase client libraries
   - Authentication-related dependencies
   - Security patches

### 6.2 Monitoring and Auditing

1. Implement regular audits of:
   - Authentication logs
   - User access patterns
   - Failed login attempts
   - Unusual activity

## 7. Troubleshooting Production Issues

### 7.1 Authentication Failures

1. Check Supabase status page
2. Verify environment variables are correct
3. Check application logs for errors
4. Verify network connectivity to Supabase

### 7.2 Database Issues

1. Check Supabase dashboard for database health
2. Verify RLS policies haven't been modified
3. Check for database connection limits

### 7.3 SMS/Email Delivery Issues

1. Check SMS provider dashboard for errors
2. Verify email delivery logs
3. Test alternative providers if necessary

## 8. Scaling Considerations

### 8.1 Database Performance

1. Monitor database performance as user base grows
2. Consider adding indexes for frequently queried columns
3. Optimize queries if performance issues arise

### 8.2 Authentication Rate Limits

1. Adjust rate limits based on actual usage patterns
2. Monitor for legitimate users hitting rate limits
3. Implement progressive rate limiting if needed

## 9. Compliance and Legal Considerations

### 9.1 Data Protection

1. Ensure user data is properly protected
2. Implement data retention policies
3. Provide mechanisms for users to export or delete their data

### 9.2 Privacy Policy

1. Update privacy policy to reflect authentication data usage
2. Clearly communicate how user data is stored and processed
3. Provide contact information for data-related inquiries
