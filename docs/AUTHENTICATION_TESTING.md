# Authentication Testing Guide for AROUZ MARKET

This guide provides step-by-step instructions for testing the Supabase authentication system for AROUZ MARKET.

## Prerequisites

1. Access to the AROUZ MARKET codebase
2. Access to the Supabase dashboard for your project
3. A development environment with the application running
4. Multiple test devices or browsers for multi-session testing

## 1. Environment Setup Verification

### 1.1 Verify Environment Variables

1. Check your `.env` file to ensure it contains:
   ```
   VITE_SUPABASE_URL=https://irkwpzcskeqtasutqnxp.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   VITE_USE_SUPABASE=true
   ```

2. Start the application and check browser console for:
   ```
   Supabase Configuration: {url: "...", hasAnonKey: true, useSupabase: true, env: "development"}
   Supabase auth client initialized successfully
   ```

### 1.2 Verify Database Schema

1. Go to Supabase dashboard → Table Editor → profiles
2. Verify the table has all required columns:
   - `id` (UUID, Primary Key)
   - `email` (Text)
   - `phone` (Text)
   - `role` (Text)
   - `first_name` (Text)
   - `last_name` (Text)
   - `company_name` (Text)
   - `store_name` (Text)
   - `store_address` (Text)
   - `tax_id` (Text)
   - `address` (Text)
   - `fleet_size` (Integer)
   - `avatar_url` (Text)
   - `last_login` (Timestamp)
   - `email_verified` (Boolean)
   - `created_at` (Timestamp)
   - `updated_at` (Timestamp)

3. Verify RLS policies:
   - Go to Table Editor → profiles → Policies
   - Confirm these policies exist:
     - "Users can view their own profile"
     - "Users can update their own profile"
     - "Users can insert their own profile"

## 2. Testing Authentication Flows

### 2.1 Supplier Registration Testing

1. Navigate to the Partners page
2. Click "Sign Up" under Supplier & Manufacturer
3. Fill in all required fields:
   - Full Name: "Test Supplier"
   - Company Name: "Test Company"
   - Email: "<EMAIL>"
   - Password: "StrongP@ssw0rd123"
   - Phone: A valid Algerian phone number
4. Submit the form
5. Verify OTP screen appears
6. Check Supabase dashboard → Authentication → Users
7. Verify a new user appears with the email "<EMAIL>"
8. Check profiles table for a new record with:
   - `first_name`: "Test"
   - `last_name`: "Supplier"
   - `role`: "supplier"
   - `company_name`: "Test Company"

### 2.2 Merchant Registration Testing

1. Navigate to the Partners page
2. Click "Sign Up" under Merchant Retailer
3. Fill in all required fields:
   - Full Name: "Test Merchant"
   - Store Name: "Test Store"
   - Store Address: "123 Test Street, Algiers"
   - Email: "<EMAIL>"
   - Password: "StrongP@ssw0rd123"
   - Phone: A valid Algerian phone number
4. Submit the form
5. Verify OTP screen appears
6. Check Supabase dashboard → Authentication → Users
7. Verify a new user appears with the email "<EMAIL>"
8. Check profiles table for a new record with:
   - `first_name`: "Test"
   - `last_name`: "Merchant"
   - `role`: "merchant"
   - `store_name`: "Test Store"
   - `store_address`: "123 Test Street, Algiers"
   - `address`: "123 Test Street, Algiers"

### 2.3 Phone Authentication Testing

1. Navigate to the login form
2. Switch to "Login with Phone" tab
3. Enter a phone number for an existing account
4. Submit the form
5. Verify OTP screen appears
6. For testing in development:
   - Enter any 6-digit code (e.g., "123456")
   - Verify successful login
7. For testing in production:
   - Check Supabase Auth Logs for the OTP
   - Enter the correct OTP
   - Verify successful login

### 2.4 Email/Password Authentication Testing

1. Navigate to the login form
2. Enter email and password for an existing account
3. Submit the form
4. Verify successful login
5. Check profiles table in Supabase
6. Verify the `last_login` timestamp is updated

### 2.5 Password Reset Testing

1. Navigate to the login form
2. Click "Forgot Password"
3. Enter the email for a test account
4. Submit the form
5. Verify success message appears
6. Check Supabase dashboard → Authentication → Users → Auth Logs
7. Verify a password reset email was triggered

## 3. Testing Row Level Security (RLS)

### 3.1 Basic RLS Testing

1. Create two test users in different browsers or incognito windows
2. Log in with both users
3. In the Supabase SQL Editor, run this query as each user:
   ```sql
   SELECT * FROM profiles;
   ```
4. Verify each user can only see their own profile

### 3.2 Advanced RLS Testing

1. Try to update another user's profile with this query:
   ```sql
   UPDATE profiles 
   SET first_name = 'Hacked Name' 
   WHERE id != auth.uid();
   ```
2. Verify the query fails due to RLS policy

## 4. Testing Multi-Role Functionality

### 4.1 Role Conflict Testing

1. Log in as a Supplier in one browser
2. Without logging out, try to log in as a Merchant
3. Verify the role conflict dialog appears
4. Choose to continue with the new role
5. Verify the previous role is properly cleared
6. Verify successful login as Merchant

### 4.2 Role-Specific Features Testing

1. Log in as a Supplier
2. Verify Supplier-specific features are available
3. Log out and log in as a Merchant
4. Verify Merchant-specific features are available
5. Verify Supplier-specific features are not available

## 5. Development Mode Testing

### 5.1 Testing with Development Mode Toggle

1. Open the application
2. Navigate to the signup/login modal
3. Toggle on "Dev Mode" in the top-left corner
4. Fill in minimal information (just required fields)
5. Submit the form
6. Verify you can proceed without strict validation

### 5.2 Testing with VITE_USE_SUPABASE=false

1. Temporarily set `VITE_USE_SUPABASE=false` in `.env`
2. Restart the development server
3. Attempt to sign up
4. Check console logs for:
   ```
   DEV MODE: Simulating successful signup for: [data]
   ```
5. Verify you can complete the flow without actual API calls
6. Reset `VITE_USE_SUPABASE=true` when done

## 6. Error Handling Testing

### 6.1 Invalid Credentials Testing

1. Attempt login with incorrect password
2. Verify appropriate error message
3. Try multiple failed attempts
4. Verify rate limiting kicks in after too many attempts

### 6.2 Duplicate Email Testing

1. Attempt signup with an email that already exists
2. Verify appropriate error message

### 6.3 Invalid OTP Testing

1. Enter incorrect OTP during verification
2. Verify appropriate error message

### 6.4 Network Issues Testing

1. Disable network connection
2. Attempt authentication action
3. Verify appropriate offline error message

## 7. Troubleshooting Common Issues

### 7.1 Authentication Fails Silently

1. Check browser console for errors
2. Verify Supabase URL and anon key are correct
3. Check if `VITE_USE_SUPABASE` is set to `true`
4. Verify the user exists in Supabase Authentication

### 7.2 OTP Verification Fails

1. Check phone number format (should be +213 format)
2. Verify SMS provider is configured in Supabase
3. Check rate limits for OTP sending

### 7.3 Profile Not Created

1. Check for errors in browser console
2. Verify the profiles table exists with correct schema
3. Check RLS policies allow profile creation
4. Verify the trigger function for new user creation exists

## 8. Production Readiness Checklist

Before deploying to production:

1. Verify all authentication flows work end-to-end
2. Ensure proper error handling for all edge cases
3. Confirm email templates are configured in Supabase
4. Set up a production SMS provider for phone authentication
5. Test on multiple devices and browsers
6. Verify security measures (CSRF protection, rate limiting)
7. Ensure proper logging and monitoring is in place
