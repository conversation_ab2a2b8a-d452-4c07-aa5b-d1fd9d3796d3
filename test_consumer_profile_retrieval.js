// Test script to verify consumer profile retrieval
// Run this in browser console to test the profile fetching

async function testConsumerProfileRetrieval() {
  console.log('🧪 Testing Consumer Profile Retrieval...');
  
  try {
    // Check localStorage session
    const phoneSession = localStorage.getItem('phone_auth_session');
    console.log('📱 Phone session in localStorage:', phoneSession ? 'Found' : 'Not found');
    
    if (phoneSession) {
      const session = JSON.parse(phoneSession);
      console.log('👤 Session data:', {
        user_phone: session.user?.phone,
        profile_phone: session.profile?.phone,
        profile_full_name: session.profile?.full_name,
        profile_original_full_name: session.profile?.original_full_name,
        profile_role: session.profile?.role
      });
      
      const phone = session.user?.phone || session.profile?.phone;
      
      if (phone) {
        console.log('🔍 Fetching latest profile from database for phone:', phone);
        
        // Import Supabase client
        const { supabase } = await import('/src/services/authService.ts');
        
        // Fetch latest profile data
        const { data: profileData, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('phone', phone)
          .eq('role', 'consumer')
          .single();
          
        if (error) {
          console.error('❌ Error fetching profile:', error);
        } else {
          console.log('✅ Latest profile from database:', {
            id: profileData.id,
            full_name: profileData.full_name,
            original_full_name: profileData.original_full_name,
            phone: profileData.phone,
            role: profileData.role,
            created_at: profileData.created_at,
            updated_at: profileData.updated_at
          });
          
          // Compare localStorage vs database
          console.log('🔄 Comparison:');
          console.log('  localStorage full_name:', session.profile?.full_name);
          console.log('  Database full_name:', profileData.full_name);
          console.log('  Match:', session.profile?.full_name === profileData.full_name);
        }
      } else {
        console.log('❌ No phone number found in session');
      }
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testConsumerProfileRetrieval();
