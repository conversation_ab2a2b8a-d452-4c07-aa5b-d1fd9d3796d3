# =============================================================================
# AROUZ MARKET - PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains production-specific environment variables
# Used for: arouzmarket.com deployment and live production environment

# Application Configuration
VITE_APP_ENV=production
VITE_APP_URL=https://arouzmarket.com
VITE_APP_NAME=AROUZ MARKET

# Feature Flags - Production has ALL features enabled
VITE_ENABLE_PRODUCT_FEATURES=true
VITE_ENABLE_REAL_TIME_UPDATES=true
VITE_ENABLE_IMAGE_UPLOAD=true
VITE_ENABLE_DEBUG_MODE=false
VITE_SHOW_PERFORMANCE_METRICS=false
VITE_ENABLE_TESTING_TOOLS=false

# Supabase Configuration - Production Database
VITE_SUPABASE_URL=https://irkwpzcskeqtasutqnxp.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94
VITE_USE_SUPABASE=true
VITE_USE_SUPABASE_BACKEND=true

# External Services - Production Keys
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8
VITE_DEXATEL_API_KEY=4595fd59fb0655b92b79c6fa00fb5d36

# Production-specific settings
VITE_API_TIMEOUT=5000
VITE_ENABLE_MOCK_DATA=false
VITE_LOG_LEVEL=error
VITE_ENABLE_ANALYTICS=true

# Build Configuration
VITE_BUILD_TARGET=production
VITE_SOURCE_MAPS=false
VITE_MINIFY=true

# Security Settings (Strict for production)
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_SECURITY_HEADERS=true
VITE_ENABLE_XSS_PROTECTION=true
VITE_ENABLE_CONTENT_TYPE_PROTECTION=true
VITE_ENABLE_FRAME_PROTECTION=true
VITE_DISABLE_CONSOLE_LOGS=true
VITE_ENABLE_CSRF_PROTECTION=true

# Performance Settings
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_BUNDLE_ANALYZER=false

# SEO and Marketing
VITE_ENABLE_SEO_OPTIMIZATION=true
VITE_ENABLE_SOCIAL_SHARING=true
VITE_ENABLE_STRUCTURED_DATA=true

# Monitoring and Error Tracking
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_USER_ANALYTICS=true
