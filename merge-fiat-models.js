import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the missing Fiat models files
const missingFiatModels1 = JSON.parse(fs.readFileSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models.json'), 'utf8'));
const missingFiatModels2 = JSON.parse(fs.readFileSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-2.json'), 'utf8'));
const missingFiatModels3 = JSON.parse(fs.readFileSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-3.json'), 'utf8'));
const missingFiatModels4 = JSON.parse(fs.readFileSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-4.json'), 'utf8'));
const missingFiatModels5 = JSON.parse(fs.readFileSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-5.json'), 'utf8'));

// Combine all missing Fiat models
const allMissingFiatModels = [
  ...missingFiatModels1,
  ...missingFiatModels2,
  ...missingFiatModels3,
  ...missingFiatModels4,
  ...missingFiatModels5
];

// Find the Fiat brand in the cars.json file
const fiatBrandIndex = carsJson.findIndex(brand => brand.brand === 'Fiat');

if (fiatBrandIndex !== -1) {
  // Add the missing models to the Fiat brand
  carsJson[fiatBrandIndex].models = [
    ...carsJson[fiatBrandIndex].models,
    ...allMissingFiatModels
  ];

  // Sort the models alphabetically
  carsJson[fiatBrandIndex].models.sort((a, b) => a.model.localeCompare(b.model));

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Added ${allMissingFiatModels.length} missing Fiat models to the cars.json file.`);
} else {
  console.error('Fiat brand not found in the cars.json file.');
}

// Clean up the temporary files
fs.unlinkSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models.json'));
fs.unlinkSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-2.json'));
fs.unlinkSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-3.json'));
fs.unlinkSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-4.json'));
fs.unlinkSync(path.join(__dirname, 'src', 'services', 'data', 'missing-fiat-models-5.json'));

console.log('Temporary files cleaned up.');
