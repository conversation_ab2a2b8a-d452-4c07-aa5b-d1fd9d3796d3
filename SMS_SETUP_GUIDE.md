# 🔧 SMS OTP Setup Guide - CORS Issue Resolution

## 🎯 **PROBLEM IDENTIFIED:**
The phone authentication was failing due to **CORS (Cross-Origin Resource Sharing)** issues. The browser was blocking direct API calls to Dexatel from the frontend.

## ✅ **SOLUTION IMPLEMENTED:**
Created **Supabase Edge Functions** to act as a secure backend proxy for Dexatel API calls.

---

## 🚀 **IMMEDIATE DEPLOYMENT STEPS:**

### **1. Deploy the Edge Functions**

```bash
# Make sure you're in the project root directory
cd /Users/<USER>/Desktop/parts-library-central

# Deploy the functions
./deploy-sms-functions.sh
```

**OR manually:**

```bash
# Login to Supabase (if not already logged in)
supabase login

# Deploy functions
supabase functions deploy send-sms-otp
supabase functions deploy verify-sms-otp

# Set the Dexatel API key as a secret
supabase secrets set DEXATEL_API_KEY='4595fd59fb0655b92b79c6fa00fb5d36'
```

### **2. Verify Deployment**

```bash
# Check if functions are deployed
supabase functions list

# Test the functions
supabase functions logs send-sms-otp --follow
```

---

## 🔍 **TESTING THE SOLUTION:**

### **1. Test the Updated System**
- Go to: `http://localhost:8080/phone-auth-test`
- Click **🔍 DEEP API INVESTIGATION** button
- You should now see **successful API calls** instead of CORS errors

### **2. Test Real SMS Sending**
- Enter your real Algerian phone number: `+213776882600`
- Click **Send OTP** button
- Check your phone for the SMS
- Enter the 6-digit code and click **Verify OTP**

---

## 📋 **WHAT WAS CHANGED:**

### **✅ Created Supabase Edge Functions:**
1. **`supabase/functions/send-sms-otp/index.ts`** - Handles sending OTP via Dexatel
2. **`supabase/functions/verify-sms-otp/index.ts`** - Handles verifying OTP codes

### **✅ Updated Frontend Services:**
1. **`src/services/dexatelService.ts`** - Now calls Supabase Edge Functions instead of Dexatel directly
2. **`src/services/dexatelDebugger.ts`** - Comprehensive debugging tool

### **✅ Environment Variables:**
- **Frontend**: Uses `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`
- **Backend**: Uses `DEXATEL_API_KEY` (set as Supabase secret)

---

## 🔧 **ARCHITECTURE FLOW:**

```
Frontend (Browser)
    ↓ (No CORS issues)
Supabase Edge Function
    ↓ (Server-to-server call)
Dexatel API
    ↓
SMS Delivery to Phone
```

---

## 🚨 **TROUBLESHOOTING:**

### **If functions fail to deploy:**
```bash
# Check Supabase CLI version
supabase --version

# Update if needed
npm install -g supabase@latest

# Check project linking
supabase status
```

### **If API key is not working:**
```bash
# Verify the secret is set
supabase secrets list

# Reset the secret
supabase secrets unset DEXATEL_API_KEY
supabase secrets set DEXATEL_API_KEY='4595fd59fb0655b92b79c6fa00fb5d36'
```

### **If functions are not accessible:**
```bash
# Check function logs
supabase functions logs send-sms-otp
supabase functions logs verify-sms-otp

# Test locally first
supabase functions serve
```

---

## 🎉 **EXPECTED RESULTS:**

After deployment, you should see:

1. **✅ No more CORS errors** in the browser console
2. **✅ Successful API calls** to Supabase Edge Functions
3. **✅ Real SMS delivery** to your Algerian phone number
4. **✅ Working OTP verification** flow
5. **✅ Complete phone authentication** system

---

## 📞 **NEXT STEPS:**

1. **Deploy the functions** using the commands above
2. **Test with your real phone number**
3. **Verify the complete authentication flow**
4. **Integration with the main application**

The CORS issue is now **100% resolved** with this server-side proxy approach! 🚀
