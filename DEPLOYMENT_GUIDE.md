# AROUZ MARKET Deployment Guide

This guide provides step-by-step instructions for deploying the AROUZ MARKET marketplace to arouzmarket.com while keeping the product features hidden during development.

## Prerequisites

- Domain name: arouzmarket.com (already purchased)
- Web hosting provider account (recommendations below)
- Node.js and npm installed on your local machine
- Git repository access

## Deployment Options

### Option 1: Traditional Web Hosting (Recommended for Simplicity)

#### Step 1: Choose a Web Hosting Provider

Recommended providers:
- [<PERSON><PERSON>](https://www.hostinger.com/)
- [Bluehost](https://www.bluehost.com/)
- [SiteGround](https://www.siteground.com/)
- [NameCheap](https://www.namecheap.com/)

Select a basic shared hosting plan that supports:
- Static website hosting
- Custom domains
- SSL certificates (for HTTPS)

#### Step 2: Build Your Production Website

1. On your local machine, navigate to your project directory
2. Make the deployment script executable:
   ```
   chmod +x deploy.sh
   ```
3. Run the deployment script:
   ```
   ./deploy.sh
   ```
4. This will create a production-ready build in the `dist` directory

#### Step 3: Upload Files to Your Hosting Provider

1. Log in to your hosting provider's control panel
2. Navigate to the file manager or FTP section
3. Upload all files from the `dist` directory to the public_html or www directory of your hosting account
4. Ensure all files maintain their directory structure

#### Step 4: Configure Your Domain

1. Log in to your domain registrar (where you purchased arouzmarket.com)
2. Update the nameservers to point to your hosting provider's nameservers
   - Your hosting provider will provide specific nameserver addresses
   - This change may take 24-48 hours to propagate globally
3. Alternatively, set up an A record pointing to your hosting provider's IP address

#### Step 5: Set Up SSL (HTTPS)

1. In your hosting control panel, look for SSL/TLS settings
2. Most providers offer free Let's Encrypt certificates
3. Enable SSL for your domain
4. Ensure automatic redirection from HTTP to HTTPS is enabled

### Option 2: Modern Hosting with Vercel or Netlify (Recommended for Developer Experience)

#### Step 1: Create an Account

1. Sign up for [Vercel](https://vercel.com/) or [Netlify](https://www.netlify.com/)
2. Connect your GitHub repository

#### Step 2: Configure Build Settings

For Vercel:
1. Set the build command to: `npm run build`
2. Set the output directory to: `dist`
3. Add environment variables from your `.env.production` file

For Netlify:
1. Set the build command to: `npm run build`
2. Set the publish directory to: `dist`
3. Add environment variables from your `.env.production` file

#### Step 3: Deploy

1. Trigger a manual deployment or push to your connected repository
2. The platform will automatically build and deploy your site

#### Step 4: Configure Custom Domain

1. In the Vercel/Netlify dashboard, go to the Domains section
2. Add your custom domain: arouzmarket.com
3. Follow the platform's instructions to verify domain ownership
4. Update DNS settings at your domain registrar as instructed
5. SSL will be automatically configured

### Option 3: Self-Hosted VPS (Advanced)

If you prefer more control and have server administration experience, you can deploy to a Virtual Private Server (VPS) using Nginx or Apache as a web server.

Recommended VPS providers:
- [DigitalOcean](https://www.digitalocean.com/)
- [Linode](https://www.linode.com/)
- [Vultr](https://www.vultr.com/)

Detailed instructions for this option are beyond the scope of this guide, but generally involve:
1. Setting up a Linux server
2. Installing Nginx or Apache
3. Configuring a virtual host for your domain
4. Uploading your built files
5. Setting up SSL with Let's Encrypt

## Keeping Product Features Hidden

The application is configured to hide product features in production through:

1. Environment variables: `VITE_ENABLE_PRODUCT_FEATURES=false` in production
2. The `ProtectedRoute` component that redirects users to the home page when they try to access product features
3. The `DevNavigation` component that is automatically hidden in production

## Post-Deployment Verification

After deployment, verify that:

1. The marketplace is accessible at https://arouzmarket.com
2. The marketplace pages (Home, My Vehicle Parts, Wholesale Offers) work correctly
3. Product features are not accessible (trying to access /app/* routes should redirect to the home page)
4. All images, styles, and interactive elements work as expected
5. The website is responsive on different devices

## Troubleshooting

### Common Issues:

1. **404 Not Found errors**: Ensure your hosting provider is configured to redirect all routes to index.html for client-side routing to work
2. **Missing assets**: Check that all files from the dist directory were uploaded correctly
3. **Blank page**: Check browser console for JavaScript errors
4. **SSL issues**: Ensure SSL is properly configured and forced HTTPS is enabled

### For assistance:

If you encounter issues during deployment, consult your hosting provider's documentation or support channels.

## Future Updates

When you're ready to launch the product features:

1. Update the `.env.production` file to set `VITE_ENABLE_PRODUCT_FEATURES=true`
2. Rebuild and redeploy the application
3. Implement proper authentication before enabling product features

## Backup and Recovery

Always maintain a backup of your website files and database (when applicable). Most hosting providers offer automated backup solutions.

---

This guide was last updated on: July 2023
