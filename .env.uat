# =============================================================================
# AROUZ MARKET - UAT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains UAT-specific environment variables
# Used for: dev.arouzmarket.com deployment and UAT testing environment

# Application Configuration
VITE_APP_ENV=uat
VITE_APP_URL=https://dev.arouzmarket.com
VITE_APP_NAME=AROUZ MARKET (UAT)

# Feature Flags - UAT has ALL features enabled for testing
VITE_ENABLE_PRODUCT_FEATURES=true
VITE_ENABLE_REAL_TIME_UPDATES=true
VITE_ENABLE_IMAGE_UPLOAD=true
VITE_ENABLE_DEBUG_MODE=true
VITE_SHOW_PERFORMANCE_METRICS=true
VITE_ENABLE_TESTING_TOOLS=true

# Supabase Configuration - UAT+DEV Database (SHARED between UAT and DEV)
# TODO: Replace with actual UAT+DEV Supabase project details after creation
VITE_SUPABASE_URL=https://vpqwfmpohjaqlrlufaga.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZwcXdmbXBvaGphcWxybHVmYWdhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2ODU3MjQsImV4cCI6MjA2NjI2MTcyNH0.VanH7phBdiuLUefRtXi2p9I54qKYurbyuf48pYnOgDY
VITE_USE_SUPABASE=true
VITE_USE_SUPABASE_BACKEND=true

# External Services - SAME as production (shared services)
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8
VITE_DEXATEL_API_KEY=your-dexatel-api-key

# UAT-specific settings
VITE_API_TIMEOUT=10000
VITE_ENABLE_MOCK_DATA=false
VITE_LOG_LEVEL=debug
VITE_ENABLE_ANALYTICS=true

# Build Configuration
VITE_BUILD_TARGET=uat
VITE_SOURCE_MAPS=true
VITE_MINIFY=true

# Security Settings (Production-like for UAT)
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_SECURITY_HEADERS=true
VITE_ENABLE_XSS_PROTECTION=true
VITE_ENABLE_CONTENT_TYPE_PROTECTION=true
VITE_ENABLE_FRAME_PROTECTION=true
VITE_DISABLE_CONSOLE_LOGS=false
VITE_ENABLE_CSRF_PROTECTION=true

# Performance Settings
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_BUNDLE_ANALYZER=false

# SEO and Marketing
VITE_ENABLE_SEO_OPTIMIZATION=true
VITE_ENABLE_SOCIAL_SHARING=true
VITE_ENABLE_STRUCTURED_DATA=true

# Monitoring and Error Tracking
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_USER_ANALYTICS=true

# UAT Testing Features
VITE_ENABLE_UAT_BANNER=true
VITE_ENABLE_TEST_DATA_RESET=true
VITE_ENABLE_ADMIN_TOOLS=true

# Database Strategy
VITE_DB_STRATEGY=separate_uat_database
VITE_PRESERVE_PROD_DATA=true
VITE_ENABLE_DATA_MIGRATION=true
