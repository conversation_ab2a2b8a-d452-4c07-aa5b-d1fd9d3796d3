# Manual Category Images Bucket Setup

**RECOMMENDED APPROACH** - This manual setup is guaranteed to work and avoids all permission issues with SQL migrations.

## Step 1: Create the Storage Bucket

1. **Open your Supabase Dashboard** at https://supabase.com/dashboard
2. **Select your AROUZ MARKET project**
3. **Navigate to Storage**:
   - Click **"Storage"** in the left sidebar
   - You'll see the Storage overview page
4. **Create the bucket**:
   - Click the **"New bucket"** button (green button in top right)
   - Fill in the bucket configuration:
     - **Name**: `category-images` (exactly this name)
     - **Public bucket**: ✅ **MUST be checked/enabled**
     - **File size limit**: `10 MB` (or 10485760 bytes)
     - **Allowed MIME types** (click "Add MIME type" for each):
       - `image/jpeg`
       - `image/png`
       - `image/webp`
       - `image/svg+xml`
5. **Click "Create bucket"**
6. **Verify creation**: You should see "category-images" appear in your buckets list

## Step 2: Set Up RLS Policies

1. **Navigate to Policies**:
   - In your Supabase Dashboard, click **"Authentication"** in the left sidebar
   - Click **"Policies"** in the Authentication submenu
   - You'll see a list of tables with RLS policies
2. **Find the storage.objects table**:
   - Look for `storage.objects` in the table list
   - Click on it to expand the policies section
3. **Create the following 4 policies** (click "New Policy" for each):

### Policy 1: Public Read Access
- **Click "New Policy"** → **"Create a policy from scratch"**
- **Policy name**: `Public read access for category images`
- **Allowed operation**: Select **"SELECT"** from dropdown
- **Target roles**: Select **"public"** from dropdown
- **USING expression** (paste this exactly):
  ```sql
  bucket_id = 'category-images'
  ```
- **Click "Save policy"**

### Policy 2: Authenticated Upload
- **Click "New Policy"** → **"Create a policy from scratch"**
- **Policy name**: `Authenticated users can upload category images`
- **Allowed operation**: Select **"INSERT"** from dropdown
- **Target roles**: Select **"authenticated"** from dropdown
- **WITH CHECK expression** (paste this exactly):
  ```sql
  bucket_id = 'category-images'
  ```
- **Click "Save policy"**

### Policy 3: Authenticated Update
- **Click "New Policy"** → **"Create a policy from scratch"**
- **Policy name**: `Authenticated users can update category images`
- **Allowed operation**: Select **"UPDATE"** from dropdown
- **Target roles**: Select **"authenticated"** from dropdown
- **USING expression** (paste this exactly):
  ```sql
  bucket_id = 'category-images'
  ```
- **WITH CHECK expression** (paste this exactly):
  ```sql
  bucket_id = 'category-images'
  ```
- **Click "Save policy"**

### Policy 4: Authenticated Delete
- **Click "New Policy"** → **"Create a policy from scratch"**
- **Policy name**: `Authenticated users can delete category images`
- **Allowed operation**: Select **"DELETE"** from dropdown
- **Target roles**: Select **"authenticated"** from dropdown
- **USING expression** (paste this exactly):
  ```sql
  bucket_id = 'category-images'
  ```
- **Click "Save policy"**

## Step 3: Create Folder Structure

1. **Navigate to your bucket**:
   - Go to **Storage** → **category-images** bucket
   - You should see an empty bucket
2. **Create the category folder**:
   - Click **"Create folder"** or **"New folder"**
   - Name it exactly: `category`
   - Click **"Create"**
3. **Create the subcategory folder**:
   - Click **"Create folder"** or **"New folder"**
   - Name it exactly: `subcategory`
   - Click **"Create"**
4. **Verify structure**: You should now see two folders: `category/` and `subcategory/`

## Step 4: Test the Setup

1. **Test upload**:
   - Click on the `category/` folder
   - Try uploading a test image (any PNG/JPG)
   - Verify the image appears in the folder
2. **Test public access**:
   - Click on the uploaded image
   - Copy the public URL
   - Open the URL in a new browser tab (should display the image)
3. **Test in your app**:
   - Open your AROUZ MARKET application
   - Navigate to the marketplace
   - Check that category navigation loads (with placeholder images for now)

## Verification

Once setup is complete, you can verify everything works by:

1. **Testing image upload**:
   ```javascript
   import { uploadCategoryImage } from '@/services/categoryImageService';

   // Test upload (in browser console)
   const testFile = new File(['test'], 'test.png', { type: 'image/png' });
   uploadCategoryImage({
     file: testFile,
     categoryId: 'tyres',
     type: 'category'
   });
   ```

2. **Testing image URL generation**:
   ```javascript
   import { getCategoryImageUrl } from '@/data/categoryData';

   // Test URL generation
   console.log(getCategoryImageUrl('tyres'));
   ```

3. **Testing in the UI**:
   - Navigate to the marketplace
   - Check that category navigation shows placeholder images
   - Open Add Product modal and verify dropdown images

## Troubleshooting

If you still encounter issues:

1. **Check bucket permissions**: Ensure the bucket is public
2. **Verify RLS policies**: Make sure all 4 policies are created and enabled
3. **Test with simple upload**: Try uploading via the Supabase Dashboard first
4. **Check browser console**: Look for any CORS or permission errors
5. **Verify authentication**: Ensure you're logged in when testing uploads

## Next Steps

Once the bucket is set up successfully:
1. Upload your 70 category and subcategory images
2. Test the marketplace navigation
3. Verify product modal dropdowns show images
4. Proceed with the rest of your marketplace development
