import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * This script copies the new favicon to the dist directory
 */
async function copyNewFavicon() {
  try {
    console.log('Starting new favicon copy process...');
    
    // Source favicon path
    const sourceFaviconPath = path.join(__dirname, 'public', 'favicon-new.png');
    
    // Check if the source favicon exists
    if (!fs.existsSync(sourceFaviconPath)) {
      console.error('Source favicon not found at:', sourceFaviconPath);
      return;
    }
    
    // Read the source favicon
    const faviconData = fs.readFileSync(sourceFaviconPath);
    
    // Ensure the dist directory exists
    const distDir = path.join(__dirname, 'dist');
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }
    
    // Write to the dist directory
    fs.writeFileSync(path.join(distDir, 'favicon-new.png'), faviconData);
    console.log('New favicon copied to dist directory');
    
    console.log('Favicon copy complete!');
  } catch (error) {
    console.error('Error copying favicon:', error);
  }
}

copyNewFavicon();
