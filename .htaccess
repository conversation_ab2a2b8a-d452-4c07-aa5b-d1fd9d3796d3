RewriteEngine On
RewriteBase /

# Handle Front Controller Pattern
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# Set proper MIME types
AddType text/css .css
AddType application/javascript .js
AddType image/png .png
AddType image/jpeg .jpg .jpeg
AddType image/svg+xml .svg

# Allow access to all files
<FilesMatch ".*">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Set proper permissions
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"

    # Cache control for favicon
    <FilesMatch "favicon.*\.png$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>
