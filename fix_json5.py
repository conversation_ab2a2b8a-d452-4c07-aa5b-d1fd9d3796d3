import json
import sys

try:
    with open('src/services/data/cars.json', 'r') as f:
        content = f.read()
    
    # Fix the specific issue at line 20783
    lines = content.split('\n')
    
    # Replace the problematic line with a comma
    problematic_line = 20782  # 0-based index for line 20783
    lines[problematic_line] = '              ],'
    
    # Write the fixed content back to a new file
    with open('fixed_cars.json', 'w') as f:
        f.write('\n'.join(lines))
    
    # Validate the new file
    try:
        with open('fixed_cars.json', 'r') as f:
            json.load(f)
        print("Fixed JSON is valid")
        
        # Replace the original file with the fixed one
        import os
        os.rename('fixed_cars.json', 'src/services/data/cars.json')
        print("Successfully replaced the original file with the fixed one")
    except json.JSONDecodeError as e:
        print(f"Fixed JSON is still invalid: {e}")
    
except Exception as e:
    print(f"Error: {e}")
