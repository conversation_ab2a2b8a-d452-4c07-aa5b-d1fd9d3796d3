/**
 * CRITICAL INVESTIGATION: Find EXACT sender names and test verification API
 */

const DEXATEL_API_KEY = '4595fd59fb0655b92b79c6fa00fb5d36';

async function investigateEverything() {
  console.log('🔍 COMPREHENSIVE DEXATEL INVESTIGATION...\n');

  // 1. Get all senders
  try {
    console.log('1️⃣ FETCHING SENDERS...');
    const sendersResponse = await fetch('https://api.dexatel.com/v1/senders', {
      method: 'GET',
      headers: {
        'X-Dexatel-Key': DEXATEL_API_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (sendersResponse.ok) {
      const sendersData = await sendersResponse.json();
      console.log('📋 SENDERS RESPONSE:');
      console.log(JSON.stringify(sendersData, null, 2));

      if (sendersData.data && sendersData.data.length > 0) {
        console.log('\n✅ EXACT SENDER NAMES TO USE:');
        sendersData.data.forEach((sender, index) => {
          console.log(`${index + 1}. "${sender.name}" (Status: ${sender.status})`);
        });
      }
    }
  } catch (error) {
    console.error('❌ Senders error:', error.message);
  }

  // 2. Test verification API with AROUZ sender (the correct one)
  console.log('\n2️⃣ TESTING VERIFICATION API WITH AROUZ SENDER...');

  try {
    console.log('\n🧪 Testing with AROUZ sender (your approved sender)');

    const payload = {
      data: {
        phone: "+213555123456",
        template: "a78f74ec-bb4a-435d-93f1-43c563260578",
        sender: "AROUZ",
        code_length: "6"
      }
    };

    console.log('📤 Sending payload:', JSON.stringify(payload, null, 2));

    const response = await fetch('https://api.dexatel.com/v1/verifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Dexatel-Key': DEXATEL_API_KEY,
      },
      body: JSON.stringify(payload)
    });

    const responseText = await response.text();

    if (response.ok) {
      console.log('✅ SUCCESS with AROUZ sender!');
      console.log('📋 Full Response:', responseText);

      // Parse and show the structure
      const responseData = JSON.parse(responseText);
      console.log('\n🔍 RESPONSE STRUCTURE:');
      console.log('- ID:', responseData.data?.message_id || responseData.id || 'NOT FOUND');
      console.log('- Account ID:', responseData.data?.account_id || 'NOT FOUND');
      console.log('- Phone:', responseData.data?.phone || 'NOT FOUND');
      console.log('- Create Date:', responseData.data?.create_date || 'NOT FOUND');
      console.log('- Expire Date:', responseData.data?.expire_date || 'NOT FOUND');

    } else {
      console.log(`❌ FAILED: ${response.status}`);
      console.log('Error:', responseText);
    }

  } catch (error) {
    console.log(`❌ Exception: ${error.message}`);
  }
}

// Run comprehensive investigation
investigateEverything();
