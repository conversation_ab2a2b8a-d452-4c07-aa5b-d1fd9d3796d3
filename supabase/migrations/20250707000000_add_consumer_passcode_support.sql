-- Migration: Add Consumer Passcode Support
-- This migration adds secure passcode functionality to the consumer authentication system
-- while maintaining 100% backward compatibility with existing accounts

-- Add passcode_hash column to profiles table for secure passcode storage
DO $$
BEGIN
    -- Add passcode_hash column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'passcode_hash') THEN
        ALTER TABLE profiles ADD COLUMN passcode_hash TEXT;
        
        -- Add comment for documentation
        COMMENT ON COLUMN profiles.passcode_hash IS 'Bcrypt hash of consumer passcode (4-6 digits) for enhanced security';
        
        -- Log the addition
        RAISE NOTICE 'Added passcode_hash column to profiles table';
    ELSE
        RAISE NOTICE 'passcode_hash column already exists in profiles table';
    END IF;
END $$;

-- Create index for faster passcode lookups (optional, for performance)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'profiles_passcode_hash_idx') THEN
        CREATE INDEX profiles_passcode_hash_idx ON profiles(passcode_hash) WHERE passcode_hash IS NOT NULL;
        RAISE NOTICE 'Created index on passcode_hash column';
    ELSE
        RAISE NOTICE 'Index on passcode_hash already exists';
    END IF;
END $$;

-- Update RLS policies to ensure passcode_hash is properly protected
-- The existing policies should already cover this, but let's be explicit

-- Ensure service role can access passcode_hash for authentication operations
-- (This should already be covered by existing service_role_all_access policy)

-- Ensure users can only access their own passcode_hash
-- (This should already be covered by existing user policies)

-- Add helpful comments
COMMENT ON TABLE profiles IS 'User profiles for AROUZ MARKET with multi-role support and secure passcode authentication for consumers';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Consumer passcode support migration completed successfully';
    RAISE NOTICE 'Existing consumer accounts can continue using phone-only authentication';
    RAISE NOTICE 'New consumer accounts can optionally set a 4-6 digit passcode for enhanced security';
END $$;
