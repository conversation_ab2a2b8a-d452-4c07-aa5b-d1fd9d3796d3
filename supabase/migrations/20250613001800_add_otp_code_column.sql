-- Add otp_code column to otp_verifications table for local verification
-- This allows us to store the actual OTP code and verify it locally instead of using Dexatel's verification API

-- Add the otp_code column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'otp_verifications' 
        AND column_name = 'otp_code'
    ) THEN
        ALTER TABLE otp_verifications 
        ADD COLUMN otp_code VARCHAR(6);
        
        -- Add index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_otp_verifications_code 
        ON otp_verifications(otp_code);
        
        -- Add index for phone + status lookups
        CREATE INDEX IF NOT EXISTS idx_otp_verifications_phone_status 
        ON otp_verifications(phone, status);
        
        -- Add verified_at column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'otp_verifications' 
            AND column_name = 'verified_at'
        ) THEN
            ALTER TABLE otp_verifications 
            ADD COLUMN verified_at TIMESTAMPTZ;
        END IF;
        
    END IF;
END $$;

-- Update any existing records to have a default status if null
UPDATE otp_verifications 
SET status = 'pending' 
WHERE status IS NULL;

-- Add comment to document the change
COMMENT ON COLUMN otp_verifications.otp_code IS 'The actual 6-digit OTP code sent to the user for local verification';
COMMENT ON COLUMN otp_verifications.verified_at IS 'Timestamp when the OTP was successfully verified';
