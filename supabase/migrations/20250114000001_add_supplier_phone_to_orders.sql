-- Migration: Add supplier phone to orders system for shipping companies
-- This migration adds the supplier_phone column to order_items table
-- and updates the create_complete_order function to populate it

-- 1. Add supplier_phone column to order_items table
ALTER TABLE order_items 
ADD COLUMN IF NOT EXISTS supplier_phone VARCHAR(20);

-- 2. Add supplier_contact_info column to shipments table
ALTER TABLE shipments 
ADD COLUMN IF NOT EXISTS supplier_contact_info JSONB;

-- 3. Drop the old function if it exists
DROP FUNCTION IF EXISTS create_order_with_items(text, text, text, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS create_order_with_items(text, payment_method, text, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS create_order_with_items(text, character varying, text, text, text, jsonb, text, text, text);
DROP FUNCTION IF EXISTS create_order_with_items(character varying, character varying, character varying, character varying, character varying, jsonb, character varying, character varying, character varying);

-- 4. Create the new function with supplier phone enhancement
CREATE OR REPLACE FUNCTION create_complete_order(
  p_consumer_phone TEXT,
  p_payment_method TEXT,
  p_delivery_address TEXT,
  p_delivery_wilaya TEXT,
  p_google_maps_url TEXT,
  p_order_items JSONB,
  p_consumer_name TEXT DEFAULT NULL,
  p_delivery_coordinates TEXT DEFAULT NULL,
  p_special_instructions TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  new_order_id UUID;
  order_number TEXT;
  calc_subtotal DECIMAL(10,2) := 0;
  item JSONB;
  coord_point POINT;
  payment_method_enum payment_method;
BEGIN
  -- Convert text payment method to enum
  payment_method_enum := p_payment_method::payment_method;
  
  -- Convert text coordinates to POINT if provided
  IF p_delivery_coordinates IS NOT NULL AND p_delivery_coordinates != '' THEN
    coord_point := p_delivery_coordinates::POINT;
  ELSE
    coord_point := NULL;
  END IF;

  -- Generate unique order number
  order_number := 'ARZ-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD((EXTRACT(EPOCH FROM NOW()) % 10000)::TEXT, 4, '0');

  -- Create the main order
  INSERT INTO orders (
    order_number,
    consumer_phone,
    consumer_name,
    payment_method,
    delivery_address,
    delivery_coordinates,
    delivery_wilaya,
    google_maps_url,
    special_instructions,
    status
  ) VALUES (
    order_number,
    p_consumer_phone,
    p_consumer_name,
    payment_method_enum,
    p_delivery_address,
    coord_point,
    p_delivery_wilaya,
    p_google_maps_url,
    p_special_instructions,
    'pending'
  ) RETURNING id INTO new_order_id;

  -- Add order items WITH SUPPLIER PHONE LOOKUP
  FOR item IN SELECT * FROM jsonb_array_elements(p_order_items)
  LOOP
    INSERT INTO order_items (
      order_id,
      product_id,
      product_name,
      product_image,
      quantity,
      unit_price,
      total_price,
      supplier_account_id,
      supplier_name,
      supplier_phone,
      supplier_city,
      supplier_wilaya,
      marketplace_section,
      category,
      subcategory
    ) VALUES (
      new_order_id,
      item->>'product_id',
      item->>'product_name',
      item->>'product_image',
      (item->>'quantity')::INTEGER,
      (item->>'unit_price')::DECIMAL(10,2),
      (item->>'total_price')::DECIMAL(10,2),
      CASE WHEN item->>'supplier_account_id' IS NOT NULL AND item->>'supplier_account_id' != '' AND item->>'supplier_account_id' != 'null' THEN (item->>'supplier_account_id')::UUID ELSE NULL END,
      item->>'supplier_name',
      -- CRITICAL: Get supplier phone from profiles table for shipping company
      COALESCE(
        (SELECT phone FROM profiles WHERE id = (item->>'supplier_account_id')::UUID AND role IN ('supplier', 'merchant')),
        item->>'supplier_phone'
      ),
      item->>'supplier_city',
      item->>'supplier_wilaya',
      (item->>'marketplace_section')::marketplace_section,
      item->>'category',
      item->>'subcategory'
    );

    calc_subtotal := calc_subtotal + (item->>'total_price')::DECIMAL(10,2);
  END LOOP;

  -- Update order totals
  UPDATE orders
  SET subtotal = calc_subtotal,
      total_amount = calc_subtotal
  WHERE id = new_order_id;

  -- Create initial unassigned shipment WITH SUPPLIER CONTACT INFO
  INSERT INTO shipments (
    order_id,
    order_item_ids,
    supplier_contact_info
  )
  SELECT
    new_order_id,
    ARRAY_AGG(oi.id),
    -- CRITICAL: Aggregate supplier contact info for shipping companies
    jsonb_agg(DISTINCT jsonb_build_object(
      'supplier_name', oi.supplier_name,
      'supplier_phone', oi.supplier_phone,
      'supplier_city', oi.supplier_city,
      'supplier_wilaya', oi.supplier_wilaya,
      'supplier_account_id', oi.supplier_account_id
    )) FILTER (WHERE oi.supplier_phone IS NOT NULL)
  FROM order_items oi
  WHERE oi.order_id = new_order_id;

  RETURN new_order_id;
END;
$$ LANGUAGE plpgsql;

-- 5. Add comments for documentation
COMMENT ON COLUMN order_items.supplier_phone IS 'CRITICAL: Supplier contact phone for shipping companies to coordinate pickup';
COMMENT ON COLUMN shipments.supplier_contact_info IS 'CRITICAL: Aggregated supplier contact information for shipping companies';

-- 6. Create index for supplier phone lookups
CREATE INDEX IF NOT EXISTS idx_order_items_supplier_phone ON order_items(supplier_phone) WHERE supplier_phone IS NOT NULL;
