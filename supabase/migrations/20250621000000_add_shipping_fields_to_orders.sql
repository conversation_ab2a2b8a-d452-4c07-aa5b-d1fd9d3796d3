-- Add shipping company fields to orders table
-- This migration adds the missing shipping fields that are used during checkout

-- Add shipping company fields to orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_company_id UUID REFERENCES shipping_companies(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_cost DECIMAL(10,2) DEFAULT 0;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_notes TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_method VARCHAR(50) DEFAULT 'standard';

-- Add index for shipping company lookups
CREATE INDEX IF NOT EXISTS idx_orders_shipping_company ON orders(shipping_company_id);

-- Add constraint to ensure shipping cost is non-negative
ALTER T<PERSON>LE orders ADD CONSTRAINT IF NOT EXISTS positive_shipping_cost CHECK (shipping_cost >= 0);

-- Update existing orders to have default shipping values
UPDATE orders 
SET 
  shipping_cost = 0,
  shipping_method = 'standard'
WHERE 
  shipping_cost IS NULL 
  OR shipping_method IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN orders.shipping_company_id IS 'Reference to shipping company selected during checkout';
COMMENT ON COLUMN orders.shipping_cost IS 'Shipping cost selected during checkout (consumer-facing)';
COMMENT ON COLUMN orders.shipping_notes IS 'Special shipping instructions from consumer';
COMMENT ON COLUMN orders.shipping_method IS 'Shipping method selected (standard, express, etc.)';
