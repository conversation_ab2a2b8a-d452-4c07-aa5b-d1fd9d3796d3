-- Migration: Create Admin User for AROUZ MARKET Admin Panel
-- This creates an admin user account for accessing the admin panel

-- 1. First, ensure the profiles table supports admin role
DO $$ BEGIN
    -- Check if admin role is already in the enum
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'admin' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
    ) THEN
        -- Add admin to the user_role enum if it doesn't exist
        ALTER TYPE user_role ADD VALUE 'admin';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Create admin user profile
-- Note: You'll need to create the actual auth user in Supabase Auth dashboard first
-- This just creates the profile entry

-- Insert admin profile (replace with actual admin user ID from Supabase Auth)
INSERT INTO profiles (
    id,
    email,
    full_name,
    role,
    phone,
    company_name,
    business_address,
    wilaya,
    is_verified,
    created_at,
    updated_at
) VALUES (
    '********-0000-0000-0000-************', -- Replace with actual admin user ID
    '<EMAIL>',
    'AROUZ MARKET Administrator',
    'admin',
    '+************',
    'AROUZ MARKET',
    'Headquarters, Algiers',
    'Alger',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    role = 'admin',
    is_verified = true,
    updated_at = NOW();

-- 3. Create RLS policies for admin access

-- Allow admins to view all orders
CREATE POLICY "Admins can view all orders" ON orders
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to update order status
CREATE POLICY "Admins can update orders" ON orders
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to view all order items
CREATE POLICY "Admins can view all order items" ON order_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to view all shipments
CREATE POLICY "Admins can view all shipments" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to create and update shipments
CREATE POLICY "Admins can manage shipments" ON shipments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to view all shipping companies
CREATE POLICY "Admins can view shipping companies" ON shipping_companies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to manage shipping companies
CREATE POLICY "Admins can manage shipping companies" ON shipping_companies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow admins to view all profiles
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- 4. Create admin dashboard statistics function
CREATE OR REPLACE FUNCTION get_admin_dashboard_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    total_orders INTEGER;
    pending_orders INTEGER;
    confirmed_orders INTEGER;
    shipped_orders INTEGER;
    delivered_orders INTEGER;
    unassigned_orders INTEGER;
    total_revenue DECIMAL(10,2);
    this_month_revenue DECIMAL(10,2);
    active_shipping_companies INTEGER;
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    -- Get order statistics
    SELECT COUNT(*) INTO total_orders FROM orders;
    SELECT COUNT(*) INTO pending_orders FROM orders WHERE status = 'pending';
    SELECT COUNT(*) INTO confirmed_orders FROM orders WHERE status = 'confirmed';
    SELECT COUNT(*) INTO shipped_orders FROM orders WHERE status = 'shipped';
    SELECT COUNT(*) INTO delivered_orders FROM orders WHERE status = 'delivered';
    
    -- Get unassigned orders (confirmed but no shipment)
    SELECT COUNT(*) INTO unassigned_orders 
    FROM orders o
    WHERE o.status = 'confirmed'
    AND NOT EXISTS (
        SELECT 1 FROM shipments s WHERE s.order_id = o.id
    );

    -- Get revenue statistics
    SELECT COALESCE(SUM(total_amount), 0) INTO total_revenue FROM orders;
    SELECT COALESCE(SUM(total_amount), 0) INTO this_month_revenue 
    FROM orders 
    WHERE created_at >= DATE_TRUNC('month', CURRENT_DATE);

    -- Get active shipping companies
    SELECT COUNT(*) INTO active_shipping_companies 
    FROM shipping_companies 
    WHERE is_active = true;

    -- Build result JSON
    result := json_build_object(
        'total_orders', total_orders,
        'pending_orders', pending_orders,
        'confirmed_orders', confirmed_orders,
        'shipped_orders', shipped_orders,
        'delivered_orders', delivered_orders,
        'unassigned_orders', unassigned_orders,
        'total_revenue', total_revenue,
        'this_month_revenue', this_month_revenue,
        'active_shipping_companies', active_shipping_companies
    );

    RETURN result;
END;
$$;

-- 5. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_admin_dashboard_stats() TO authenticated;

-- 6. Add helpful comments
COMMENT ON FUNCTION get_admin_dashboard_stats() IS 'Returns dashboard statistics for admin panel - requires admin role';

-- 7. Create admin audit log table for tracking admin actions
CREATE TABLE IF NOT EXISTS admin_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_user_id UUID NOT NULL REFERENCES auth.users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for audit log queries
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_admin_user ON admin_audit_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_created_at ON admin_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action ON admin_audit_log(action);

-- Enable RLS on audit log
ALTER TABLE admin_audit_log ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Admins can view audit logs" ON admin_audit_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- Allow inserting audit logs
CREATE POLICY "Allow audit log creation" ON admin_audit_log
  FOR INSERT WITH CHECK (true);

COMMENT ON TABLE admin_audit_log IS 'Audit log for tracking admin actions in AROUZ MARKET admin panel';
