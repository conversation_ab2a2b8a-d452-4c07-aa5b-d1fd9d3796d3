-- Migration: Create Consumer Reviews System
-- This creates a comprehensive reviews system for consumer accounts
-- 🎯 CRITICAL: This preserves existing authentication system - NO CHANGES to auth flow

-- 1. Create consumer_reviews table
CREATE TABLE IF NOT EXISTS consumer_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Consumer identification (using phone from profiles table)
  consumer_phone TEXT NOT NULL,
  consumer_name TEXT,
  
  -- Product identification
  product_id TEXT NOT NULL,
  product_name TEXT NOT NULL,
  
  -- Review content
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title TEXT,
  review_text TEXT,
  
  -- Review metadata
  is_verified_purchase BOOLEAN DEFAULT FALSE,
  is_approved BOOLEAN DEFAULT TRUE,
  helpful_count INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_rating CHECK (rating BETWEEN 1 AND 5),
  CONSTRAINT non_empty_review CHECK (
    (title IS NOT NULL AND LENGTH(TRIM(title)) > 0) OR 
    (review_text IS NOT NULL AND LENGTH(TRIM(review_text)) > 0)
  )
);

-- 2. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_consumer_reviews_phone ON consumer_reviews(consumer_phone);
CREATE INDEX IF NOT EXISTS idx_consumer_reviews_product_id ON consumer_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_consumer_reviews_rating ON consumer_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_consumer_reviews_created_at ON consumer_reviews(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_consumer_reviews_approved ON consumer_reviews(is_approved) WHERE is_approved = true;

-- 3. Enable Row Level Security
ALTER TABLE consumer_reviews ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies for consumer reviews

-- Policy: Consumers can view all approved reviews
CREATE POLICY "Anyone can view approved reviews" ON consumer_reviews
  FOR SELECT 
  USING (is_approved = true);

-- Policy: Consumers can insert their own reviews (must be authenticated)
CREATE POLICY "Authenticated consumers can create reviews" ON consumer_reviews
  FOR INSERT 
  WITH CHECK (
    -- Check if user is authenticated by looking up their phone in profiles
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_reviews.consumer_phone 
      AND role = 'consumer'
    )
  );

-- Policy: Consumers can update their own reviews
CREATE POLICY "Consumers can update their own reviews" ON consumer_reviews
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_reviews.consumer_phone 
      AND role = 'consumer'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_reviews.consumer_phone 
      AND role = 'consumer'
    )
  );

-- Policy: Consumers can delete their own reviews
CREATE POLICY "Consumers can delete their own reviews" ON consumer_reviews
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_reviews.consumer_phone 
      AND role = 'consumer'
    )
  );

-- 5. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_consumer_reviews_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Create trigger for updated_at
CREATE TRIGGER trigger_update_consumer_reviews_updated_at
  BEFORE UPDATE ON consumer_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_consumer_reviews_updated_at();

-- 7. Add helpful comments for documentation
COMMENT ON TABLE consumer_reviews IS 'Consumer product reviews with phone-based authentication integration';
COMMENT ON COLUMN consumer_reviews.consumer_phone IS 'Links to profiles.phone for consumer identification';
COMMENT ON COLUMN consumer_reviews.product_id IS 'References the product being reviewed';
COMMENT ON COLUMN consumer_reviews.rating IS 'Star rating from 1-5';
COMMENT ON COLUMN consumer_reviews.is_verified_purchase IS 'Whether this review is from a verified purchase';
COMMENT ON COLUMN consumer_reviews.is_approved IS 'Admin moderation flag for review approval';
