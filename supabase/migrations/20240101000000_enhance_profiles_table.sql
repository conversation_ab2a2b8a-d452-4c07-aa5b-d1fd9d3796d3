-- Migration: Enhance profiles table for complete user data storage
-- This migration updates the profiles table to ensure all user data is properly stored

-- First, create a backup of the existing profiles table
CREATE TABLE IF NOT EXISTS profiles_backup_20240101 AS SELECT * FROM profiles;

-- Add missing columns to the profiles table
-- Check if columns exist before adding them
DO $$
BEGIN
    -- Add full_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'full_name') THEN
        ALTER TABLE profiles ADD COLUMN full_name TEXT;
    END IF;

    -- Add first_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'first_name') THEN
        ALTER TABLE profiles ADD COLUMN first_name TEXT;
    END IF;

    -- Add last_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'last_name') THEN
        ALTER TABLE profiles ADD COLUMN last_name TEXT;
    END IF;

    -- Add address column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'address') THEN
        ALTER TABLE profiles ADD COLUMN address TEXT;
    END IF;

    -- Add phone_verified column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                  WHERE table_name = 'profiles' AND column_name = 'phone_verified') THEN
        ALTER TABLE profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Create a comment to document the table structure
COMMENT ON TABLE profiles IS 'User profiles for AROUZ MARKET with complete data storage for all roles';

-- Add comments for all columns
COMMENT ON COLUMN profiles.id IS 'Primary key, references auth.users(id)';
COMMENT ON COLUMN profiles.email IS 'User email address';
COMMENT ON COLUMN profiles.phone IS 'User phone number (optional)';
COMMENT ON COLUMN profiles.role IS 'User role: supplier, merchant, consumer, or distribution';
COMMENT ON COLUMN profiles.full_name IS 'User full name';
COMMENT ON COLUMN profiles.first_name IS 'User first name';
COMMENT ON COLUMN profiles.last_name IS 'User last name';
COMMENT ON COLUMN profiles.company_name IS 'Company name (for supplier role)';
COMMENT ON COLUMN profiles.store_name IS 'Store name (for merchant role)';
COMMENT ON COLUMN profiles.store_address IS 'Store address (for merchant role)';
COMMENT ON COLUMN profiles.address IS 'User address';
COMMENT ON COLUMN profiles.avatar_url IS 'URL to user avatar image';
COMMENT ON COLUMN profiles.last_login IS 'Timestamp of last login';
COMMENT ON COLUMN profiles.email_verified IS 'Whether the email has been verified';
COMMENT ON COLUMN profiles.phone_verified IS 'Whether the phone has been verified';
COMMENT ON COLUMN profiles.created_at IS 'Timestamp of profile creation';
COMMENT ON COLUMN profiles.updated_at IS 'Timestamp of last profile update';

-- Update the handle_new_user function to properly set the role and initial data
-- First, check which columns exist in the profiles table
DO $$
DECLARE
  has_full_name BOOLEAN;
  has_first_name BOOLEAN;
  has_last_name BOOLEAN;
  has_phone_verified BOOLEAN;
BEGIN
  -- Check if columns exist
  SELECT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'profiles' AND column_name = 'full_name') INTO has_full_name;

  SELECT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'profiles' AND column_name = 'first_name') INTO has_first_name;

  SELECT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'profiles' AND column_name = 'last_name') INTO has_last_name;

  SELECT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'profiles' AND column_name = 'phone_verified') INTO has_phone_verified;

  -- Create the appropriate function based on which columns exist
  IF has_full_name AND has_first_name AND has_last_name AND has_phone_verified THEN
    -- All columns exist, create the full function
    EXECUTE $FUNC$
    CREATE OR REPLACE FUNCTION public.handle_new_user()
    RETURNS TRIGGER AS $$
    DECLARE
      user_role TEXT;
      user_email TEXT;
      user_phone TEXT;
      user_full_name TEXT;
      user_first_name TEXT;
      user_last_name TEXT;
    BEGIN
      -- Extract data from the user metadata
      user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');
      user_email := new.email;
      user_phone := new.phone;
      user_full_name := COALESCE(new.raw_user_meta_data->>'fullName', '');
      user_first_name := COALESCE(new.raw_user_meta_data->>'firstName', '');
      user_last_name := COALESCE(new.raw_user_meta_data->>'lastName', '');

      -- Insert the new profile with all available data
      INSERT INTO public.profiles (
        id,
        email,
        phone,
        role,
        full_name,
        first_name,
        last_name,
        company_name,
        store_name,
        store_address,
        email_verified,
        phone_verified,
        created_at,
        updated_at
      )
      VALUES (
        new.id,
        user_email,
        user_phone,
        user_role,
        user_full_name,
        user_first_name,
        user_last_name,
        CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
        CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
        CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
        false,
        false,
        now(),
        now()
      )
      ON CONFLICT (id) DO NOTHING;

      RETURN new;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    $FUNC$;
  ELSE
    -- Some columns don't exist, create a simpler function
    EXECUTE $FUNC$
    CREATE OR REPLACE FUNCTION public.handle_new_user()
    RETURNS TRIGGER AS $$
    DECLARE
      user_role TEXT;
    BEGIN
      -- Extract role from the user metadata
      user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');

      -- Insert the new profile with basic data
      INSERT INTO public.profiles (
        id,
        email,
        phone,
        role,
        company_name,
        store_name,
        store_address,
        email_verified,
        created_at,
        updated_at
      )
      VALUES (
        new.id,
        new.email,
        new.phone,
        user_role,
        CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
        CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
        CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
        false,
        now(),
        now()
      )
      ON CONFLICT (id) DO NOTHING;

      RETURN new;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    $FUNC$;
  END IF;
END $$;

-- Create or update indexes for faster lookups
DO $$
BEGIN
  -- Create email index if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'profiles_email_idx') THEN
    CREATE INDEX profiles_email_idx ON profiles(email);
  END IF;

  -- Create role index if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'profiles_role_idx') THEN
    CREATE INDEX profiles_role_idx ON profiles(role);
  END IF;

  -- Create combined email-role index if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'profiles_email_role_idx') THEN
    CREATE INDEX profiles_email_role_idx ON profiles(email, role);
  END IF;
END $$;

-- Add a unique constraint on email and role to prevent duplicate registrations
-- This ensures a user can't register with the same email for different roles
-- First check if the constraint already exists
DO $$
BEGIN
  -- Drop the constraint if it exists
  BEGIN
    ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_email_role_unique;
  EXCEPTION
    WHEN undefined_object THEN
      -- Constraint doesn't exist, do nothing
  END;

  -- Add the constraint
  BEGIN
    ALTER TABLE profiles ADD CONSTRAINT profiles_email_role_unique UNIQUE (email, role);
  EXCEPTION
    WHEN duplicate_table THEN
      -- Constraint already exists, do nothing
    WHEN undefined_column THEN
      -- One of the columns doesn't exist, do nothing
    WHEN others THEN
      RAISE NOTICE 'Could not add unique constraint: %', SQLERRM;
  END;
END $$;
