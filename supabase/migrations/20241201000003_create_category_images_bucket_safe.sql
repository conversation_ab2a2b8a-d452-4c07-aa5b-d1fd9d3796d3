-- Safe Category Images Storage Bucket Creation
-- This migration only performs operations allowed in Supabase's managed environment

-- 1. Create the category-images bucket (this is the main operation we need)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Create RLS policies for the category-images bucket
-- Note: We don't try to enable RLS on storage.objects as it's already enabled by Supabase

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Public read access for category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete category images" ON storage.objects;

-- Create RLS policy for public read access to category images
CREATE POLICY "Public read access for category images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

-- Create RLS policy for authenticated users to upload category images
CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Create RLS policy for authenticated users to update category images
CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Create RLS policy for authenticated users to delete category images
CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- 3. Verify the bucket was created successfully
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'category-images') THEN
    RAISE NOTICE 'SUCCESS: Category images bucket created successfully';
    RAISE NOTICE 'Bucket ID: category-images';
    RAISE NOTICE 'Public access: enabled';
    RAISE NOTICE 'File size limit: 10MB';
    RAISE NOTICE 'Allowed types: image/jpeg, image/png, image/webp, image/svg+xml';
  ELSE
    RAISE EXCEPTION 'FAILED: Could not create category images bucket';
  END IF;
END $$;

-- 4. Display setup completion message
DO $$
BEGIN
  RAISE NOTICE '=== CATEGORY IMAGES BUCKET SETUP COMPLETE ===';
  RAISE NOTICE 'Next steps:';
  RAISE NOTICE '1. Upload category images to: category-images/category/[categoryId].png';
  RAISE NOTICE '2. Upload subcategory images to: category-images/subcategory/[subcategoryId].png';
  RAISE NOTICE '3. Test the marketplace category navigation';
  RAISE NOTICE '4. Verify product modal dropdowns show images';
END $$;
