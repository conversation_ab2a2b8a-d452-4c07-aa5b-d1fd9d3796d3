-- Fix profile creation RLS issues for production
-- This migration addresses the "Profile creation failed" error during signup

-- 1. First, let's check the current state of the profiles table and policies
DO $$
DECLARE
  policy_count INTEGER;
  trigger_exists BOOLEAN;
BEGIN
  -- Check if R<PERSON> is enabled
  SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles' INTO policy_count;
  RAISE NOTICE 'Current number of RLS policies on profiles table: %', policy_count;
  
  -- Check if trigger exists
  SELECT EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'on_auth_user_created' 
    AND event_object_table = 'users'
    AND event_object_schema = 'auth'
  ) INTO trigger_exists;
  RAISE NOTICE 'Trigger on_auth_user_created exists: %', trigger_exists;
END $$;

-- 2. Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can do anything" ON profiles;
DROP POLICY IF EXISTS "Postgres role can do anything" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert access for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update access for users based on email" ON profiles;

-- 3. Create comprehensive RLS policies that work for both triggers and user operations

-- Allow service role to do anything (needed for triggers and admin operations)
CREATE POLICY "service_role_all_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Allow authenticated users to view their own profile
CREATE POLICY "users_select_own_profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Allow authenticated users to update their own profile
CREATE POLICY "users_update_own_profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- CRITICAL: Allow authenticated users to insert their own profile
-- This is needed for the signup process when the trigger creates the initial profile
-- and then the application updates it with additional data
CREATE POLICY "users_insert_own_profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 4. Ensure the handle_new_user function is properly configured
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Extract role from the user metadata
  user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');
  
  -- Insert the new profile with comprehensive data from metadata
  INSERT INTO public.profiles (
    id,
    email,
    phone,
    role,
    full_name,
    first_name,
    last_name,
    company_name,
    store_name,
    store_address,
    email_verified,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.email,
    -- Format phone number if it exists
    CASE
      WHEN new.phone IS NOT NULL AND new.phone != '' THEN
        CASE
          WHEN new.phone LIKE '+213%' THEN new.phone
          WHEN new.phone LIKE '0%' THEN '+213' || SUBSTRING(new.phone FROM 2)
          ELSE '+213' || new.phone
        END
      ELSE NULL
    END,
    user_role,
    COALESCE(new.raw_user_meta_data->>'fullName', ''),
    COALESCE(new.raw_user_meta_data->>'firstName', ''),
    COALESCE(new.raw_user_meta_data->>'lastName', ''),
    CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
    false,
    now(),
    now()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    full_name = EXCLUDED.full_name,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    company_name = EXCLUDED.company_name,
    store_name = EXCLUDED.store_name,
    store_address = EXCLUDED.store_address,
    updated_at = now();

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Grant necessary permissions
GRANT ALL ON profiles TO postgres;
GRANT ALL ON profiles TO service_role;
GRANT SELECT, UPDATE, INSERT ON profiles TO authenticated;
GRANT SELECT ON profiles TO anon;

-- 7. Add helpful comments
COMMENT ON POLICY "service_role_all_access" ON profiles IS 'Allows service role full access for triggers and admin operations';
COMMENT ON POLICY "users_select_own_profile" ON profiles IS 'Allows users to view their own profile';
COMMENT ON POLICY "users_update_own_profile" ON profiles IS 'Allows users to update their own profile';
COMMENT ON POLICY "users_insert_own_profile" ON profiles IS 'Allows users to insert their own profile during signup';

-- 8. Final verification
DO $$
DECLARE
  policy_count INTEGER;
  trigger_exists BOOLEAN;
BEGIN
  -- Check final policy count
  SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles' INTO policy_count;
  RAISE NOTICE 'Final number of RLS policies on profiles table: %', policy_count;
  
  -- Check if trigger exists
  SELECT EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'on_auth_user_created' 
    AND event_object_table = 'users'
    AND event_object_schema = 'auth'
  ) INTO trigger_exists;
  RAISE NOTICE 'Trigger on_auth_user_created exists after migration: %', trigger_exists;
  
  IF policy_count < 4 THEN
    RAISE WARNING 'Expected at least 4 RLS policies, but found %', policy_count;
  END IF;
  
  IF NOT trigger_exists THEN
    RAISE WARNING 'Trigger on_auth_user_created does not exist!';
  END IF;
END $$;
