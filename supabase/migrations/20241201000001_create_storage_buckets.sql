-- Migration: Create storage buckets for product images with proper RLS
-- This migration sets up Supabase Storage for product images with account isolation

-- 1. Create storage bucket for product images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'product-images',
  'product-images',
  true,
  ********, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO NOTHING;

-- 2. Create storage bucket for manufacturer logos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'manufacturer-logos',
  'manufacturer-logos',
  true,
  ********, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
) ON CONFLICT (id) DO NOTHING;

-- 3. Create storage bucket for category and subcategory images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  ********, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
) ON CONFLICT (id) DO NOTHING;

-- 3. Create RLS policies for product-images bucket

-- Allow authenticated users to upload images to their own folder
CREATE POLICY "Users can upload to their own folder" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'product-images' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to view images in their own folder
CREATE POLICY "Users can view their own images" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'product-images' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow authenticated users to update images in their own folder
CREATE POLICY "Users can update their own images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'product-images' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to delete images in their own folder
CREATE POLICY "Users can delete their own images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'product-images' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    auth.role() = 'authenticated'
  );

-- 4. Create RLS policies for manufacturer-logos bucket

-- Allow authenticated users to upload logos to their own folder
CREATE POLICY "Users can upload logos to their own folder" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'manufacturer-logos' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to view logos in their own folder
CREATE POLICY "Users can view their own logos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'manufacturer-logos' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow authenticated users to update logos in their own folder
CREATE POLICY "Users can update their own logos" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'manufacturer-logos' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to delete logos in their own folder
CREATE POLICY "Users can delete their own logos" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'manufacturer-logos' AND
    auth.uid()::text = (storage.foldername(name))[1] AND
    auth.role() = 'authenticated'
  );

-- 5. Create RLS policies for category-images bucket

-- Allow public read access to category images (no authentication required)
CREATE POLICY "Public read access to category images" ON storage.objects
  FOR SELECT USING (bucket_id = 'category-images');

-- Allow authenticated admin users to upload category images
CREATE POLICY "Admin users can upload category images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'category-images' AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated admin users to update category images
CREATE POLICY "Admin users can update category images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'category-images' AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated admin users to delete category images
CREATE POLICY "Admin users can delete category images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'category-images' AND
    auth.role() = 'authenticated'
  );

-- 6. Create helper functions for storage operations

-- Function to generate a unique filename for product images
CREATE OR REPLACE FUNCTION generate_product_image_path(
  user_id UUID,
  product_id TEXT,
  file_extension TEXT,
  image_type TEXT DEFAULT 'primary'
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/' || product_id || '/' || image_type || '_' ||
         extract(epoch from now())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to generate a unique filename for manufacturer logos
CREATE OR REPLACE FUNCTION generate_manufacturer_logo_path(
  user_id UUID,
  manufacturer_name TEXT,
  file_extension TEXT
)
RETURNS TEXT AS $$
BEGIN
  RETURN user_id::text || '/logos/' ||
         lower(regexp_replace(manufacturer_name, '[^a-zA-Z0-9]', '_', 'g')) ||
         '_' || extract(epoch from now())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to get the public URL for a storage object
CREATE OR REPLACE FUNCTION get_storage_public_url(
  bucket_name TEXT,
  file_path TEXT
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'https://irkwpzcskeqtasutqnxp.supabase.co/storage/v1/object/public/' ||
         bucket_name || '/' || file_path;
END;
$$ LANGUAGE plpgsql;

-- 6. Create function to clean up orphaned images
CREATE OR REPLACE FUNCTION cleanup_orphaned_product_images()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
  image_record RECORD;
BEGIN
  -- Find images in storage that don't have corresponding products
  FOR image_record IN
    SELECT name FROM storage.objects
    WHERE bucket_id = 'product-images'
    AND NOT EXISTS (
      SELECT 1 FROM products
      WHERE primary_image LIKE '%' || storage.objects.name || '%'
      OR additional_images::text LIKE '%' || storage.objects.name || '%'
    )
  LOOP
    -- Delete the orphaned image
    DELETE FROM storage.objects
    WHERE bucket_id = 'product-images' AND name = image_record.name;

    deleted_count := deleted_count + 1;
  END LOOP;

  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate category image paths
CREATE OR REPLACE FUNCTION generate_category_image_path(
  category_type TEXT, -- 'category' or 'subcategory'
  category_id TEXT,
  file_extension TEXT
)
RETURNS TEXT AS $$
BEGIN
  -- Generate path: category_type/category_id.extension
  RETURN category_type || '/' || category_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- 7. Create function to validate image file types
CREATE OR REPLACE FUNCTION validate_image_file(
  file_name TEXT,
  file_size BIGINT,
  mime_type TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check file extension
  IF NOT (file_name ~* '\.(jpg|jpeg|png|webp|gif)$') THEN
    RETURN FALSE;
  END IF;

  -- Check MIME type
  IF mime_type NOT IN ('image/jpeg', 'image/png', 'image/webp', 'image/gif') THEN
    RETURN FALSE;
  END IF;

  -- Check file size (50MB for product images, 10MB for logos)
  IF file_size > ******** THEN
    RETURN FALSE;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 8. Add comments for documentation
COMMENT ON FUNCTION generate_product_image_path IS 'Generates unique file paths for product images with user isolation';
COMMENT ON FUNCTION generate_manufacturer_logo_path IS 'Generates unique file paths for manufacturer logos with user isolation';
COMMENT ON FUNCTION generate_category_image_path IS 'Generates file paths for category and subcategory images';
COMMENT ON FUNCTION get_storage_public_url IS 'Returns the public URL for accessing stored files';
COMMENT ON FUNCTION cleanup_orphaned_product_images IS 'Removes images that are no longer referenced by any products';
COMMENT ON FUNCTION validate_image_file IS 'Validates image files before upload';

-- 9. Create indexes for better performance on storage queries
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_id ON storage.objects(bucket_id);
CREATE INDEX IF NOT EXISTS idx_storage_objects_name ON storage.objects(name);

-- 10. Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
