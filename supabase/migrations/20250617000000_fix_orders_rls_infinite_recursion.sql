-- Migration: Fix infinite recursion in orders RLS policies
-- This migration fixes the circular dependency between orders, order_items, and shipments policies

-- 1. Drop all existing problematic policies
DROP POLICY IF EXISTS "Order items inherit order access" ON order_items;
DROP POLICY IF EXISTS "Shipments inherit order access" ON shipments;

-- 2. Create fixed order_items policies without circular dependency
CREATE POLICY "Order items access for consumers" ON order_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.phone = (SELECT consumer_phone FROM orders WHERE id = order_items.order_id)
      AND p.role = 'consumer'
      AND p.id = auth.uid()
    )
  );

CREATE POLICY "Order items access for suppliers" ON order_items
  FOR SELECT USING (
    supplier_account_id = auth.uid()
  );

CREATE POLICY "Order items insert for order creation" ON order_items
  FOR INSERT WITH CHECK (true);

-- 3. Create fixed shipments policies without circular dependency
CREATE POLICY "Shipments access for consumers" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.phone = (SELECT consumer_phone FROM orders WHERE id = shipments.order_id)
      AND p.role = 'consumer'
      AND p.id = auth.uid()
    )
  );

CREATE POLICY "Shipments access for suppliers" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = shipments.order_id
      AND oi.supplier_account_id = auth.uid()
    )
  );

CREATE POLICY "Shipments insert for order creation" ON shipments
  FOR INSERT WITH CHECK (true);

-- 4. Add missing INSERT policy for orders table
CREATE POLICY "Allow order creation" ON orders
  FOR INSERT WITH CHECK (true);

-- 5. Add helpful comment
COMMENT ON POLICY "Order items insert for order creation" ON order_items IS 'Allows order creation function to insert order items';
COMMENT ON POLICY "Shipments insert for order creation" ON shipments IS 'Allows order creation function to insert shipments';
COMMENT ON POLICY "Allow order creation" ON orders IS 'Allows authenticated users to create orders';
