-- =====================================================
-- AROUZ MARKET - Insert Engine Subcategories Part 2
-- Created: 2024-12-20
-- Purpose: Insert Engine subcategories 51-100
-- =====================================================

-- Insert Engine subcategories (Part 2: 51-100)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('timing-chain', 'Timing Chain', 'Timing Chain', 'engine', 51),
  ('timing-belt-tensioner', 'Timing Belt Tensioner', 'Timing Belt Tensioner', 'engine', 52),
  ('timing-chain-tensioner', 'Timing Chain Tensioner', 'Timing Chain Tensioner', 'engine', 53),
  ('timing-belt-idler-pulley', 'Timing Belt Idler Pulley', 'Timing Belt Idler Pulley', 'engine', 54),
  ('crankshaft-pulley', 'Crankshaft Pulley', 'Crankshaft Pulley', 'engine', 55),
  ('camshaft', 'Camshaft', 'Camshaft', 'engine', 56),
  ('crankshaft', 'Crankshaft', 'Crankshaft', 'engine', 57),
  ('connecting-rod', 'Connecting Rod', 'Connecting Rod', 'engine', 58),
  ('piston', 'Piston', 'Piston', 'engine', 59),
  ('piston-ring', 'Piston Ring', 'Piston Ring', 'engine', 60),
  ('cylinder-head', 'Cylinder Head', 'Cylinder Head', 'engine', 61),
  ('engine-block', 'Engine Block', 'Engine Block', 'engine', 62),
  ('valve-intake-exhaust', 'Valve (Intake/Exhaust)', 'Valve (Intake/Exhaust)', 'engine', 63),
  ('valve-spring', 'Valve Spring', 'Valve Spring', 'engine', 64),
  ('valve-guide', 'Valve Guide', 'Valve Guide', 'engine', 65),
  ('valve-seat', 'Valve Seat', 'Valve Seat', 'engine', 66),
  ('rocker-arm', 'Rocker Arm', 'Rocker Arm', 'engine', 67),
  ('push-rod', 'Push Rod', 'Push Rod', 'engine', 68),
  ('lifter-tappet', 'Lifter/Tappet', 'Lifter/Tappet', 'engine', 69),
  ('flywheel', 'Flywheel', 'Flywheel', 'engine', 70),
  ('flexplate', 'Flexplate', 'Flexplate', 'engine', 71),
  ('engine-mount', 'Engine Mount', 'Engine Mount', 'engine', 72),
  ('transmission-mount', 'Transmission Mount', 'Transmission Mount', 'engine', 73),
  ('starter-motor', 'Starter Motor', 'Starter Motor', 'engine', 74),
  ('alternator', 'Alternator', 'Alternator', 'engine', 75),
  ('serpentine-belt', 'Serpentine Belt', 'Serpentine Belt', 'engine', 76),
  ('drive-belt', 'Drive Belt', 'Drive Belt', 'engine', 77),
  ('belt-tensioner', 'Belt Tensioner', 'Belt Tensioner', 'engine', 78),
  ('idler-pulley', 'Idler Pulley', 'Idler Pulley', 'engine', 79),
  ('power-steering-pump', 'Power Steering Pump', 'Power Steering Pump', 'engine', 80),
  ('air-conditioning-compressor', 'Air Conditioning Compressor', 'Air Conditioning Compressor', 'engine', 81),
  ('egr-valve', 'EGR Valve', 'EGR Valve', 'engine', 82),
  ('pcv-valve', 'PCV Valve', 'PCV Valve', 'engine', 83),
  ('idle-air-control-valve', 'Idle Air Control Valve', 'Idle Air Control Valve', 'engine', 84),
  ('fuel-vapor-canister', 'Fuel Vapor Canister', 'Fuel Vapor Canister', 'engine', 85),
  ('catalytic-converter', 'Catalytic Converter', 'Catalytic Converter', 'engine', 86),
  ('muffler', 'Muffler', 'Muffler', 'engine', 87),
  ('exhaust-pipe', 'Exhaust Pipe', 'Exhaust Pipe', 'engine', 88),
  ('exhaust-gasket', 'Exhaust Gasket', 'Exhaust Gasket', 'engine', 89),
  ('exhaust-clamp', 'Exhaust Clamp', 'Exhaust Clamp', 'engine', 90),
  ('exhaust-hanger', 'Exhaust Hanger', 'Exhaust Hanger', 'engine', 91),
  ('diesel-particulate-filter-dpf', 'Diesel Particulate Filter (DPF)', 'Diesel Particulate Filter (DPF)', 'engine', 92),
  ('selective-catalytic-reduction-scr', 'Selective Catalytic Reduction (SCR)', 'Selective Catalytic Reduction (SCR)', 'engine', 93),
  ('def-adblue-tank', 'DEF/AdBlue Tank', 'DEF/AdBlue Tank', 'engine', 94),
  ('def-adblue-pump', 'DEF/AdBlue Pump', 'DEF/AdBlue Pump', 'engine', 95),
  ('def-adblue-injector', 'DEF/AdBlue Injector', 'DEF/AdBlue Injector', 'engine', 96),
  ('def-adblue-heater', 'DEF/AdBlue Heater', 'DEF/AdBlue Heater', 'engine', 97),
  ('def-adblue-sensor', 'DEF/AdBlue Sensor', 'DEF/AdBlue Sensor', 'engine', 98),
  ('turbo-wastegate-actuator', 'Turbo Wastegate Actuator', 'Turbo Wastegate Actuator', 'engine', 99),
  ('turbo-boost-pressure-sensor', 'Turbo Boost Pressure Sensor', 'Turbo Boost Pressure Sensor', 'engine', 100);

-- Success message for Part 2
SELECT 'Engine subcategories Part 2 (51-100/165) inserted successfully!' as status;
