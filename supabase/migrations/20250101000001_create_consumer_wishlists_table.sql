-- Migration: Create Consumer Wishlist System
-- This creates a comprehensive wishlist system for consumer accounts
-- 🎯 CRITICAL: This preserves existing authentication system - NO CHANGES to auth flow

-- 1. Create consumer_wishlists table
CREATE TABLE IF NOT EXISTS consumer_wishlists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Consumer identification (using phone from profiles table)
  consumer_phone TEXT NOT NULL,
  
  -- Product identification
  product_id TEXT NOT NULL,
  product_name TEXT NOT NULL,
  product_image TEXT,
  product_price DECIMAL(10,2),
  product_manufacturer TEXT,
  
  -- Wishlist metadata
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  priority INTEGER DEFAULT 1 CHECK (priority BETWEEN 1 AND 5),
  
  -- Prevent duplicate entries
  UNIQUE(consumer_phone, product_id)
);

-- 2. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_consumer_wishlists_phone ON consumer_wishlists(consumer_phone);
CREATE INDEX IF NOT EXISTS idx_consumer_wishlists_product_id ON consumer_wishlists(product_id);
CREATE INDEX IF NOT EXISTS idx_consumer_wishlists_added_at ON consumer_wishlists(added_at DESC);
CREATE INDEX IF NOT EXISTS idx_consumer_wishlists_priority ON consumer_wishlists(priority DESC);

-- 3. Enable Row Level Security
ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies for consumer wishlists

-- Policy: Consumers can only view their own wishlist items
CREATE POLICY "Consumers can view their own wishlist" ON consumer_wishlists
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_wishlists.consumer_phone 
      AND role = 'consumer'
    )
  );

-- Policy: Consumers can add items to their own wishlist (must be authenticated)
CREATE POLICY "Authenticated consumers can add to wishlist" ON consumer_wishlists
  FOR INSERT 
  WITH CHECK (
    -- Check if user is authenticated by looking up their phone in profiles
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_wishlists.consumer_phone 
      AND role = 'consumer'
    )
  );

-- Policy: Consumers can update their own wishlist items
CREATE POLICY "Consumers can update their own wishlist items" ON consumer_wishlists
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_wishlists.consumer_phone 
      AND role = 'consumer'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_wishlists.consumer_phone 
      AND role = 'consumer'
    )
  );

-- Policy: Consumers can remove items from their own wishlist
CREATE POLICY "Consumers can remove from their own wishlist" ON consumer_wishlists
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE phone = consumer_wishlists.consumer_phone 
      AND role = 'consumer'
    )
  );

-- 5. Create function to get wishlist count for a consumer
CREATE OR REPLACE FUNCTION get_consumer_wishlist_count(phone_number TEXT)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER 
    FROM consumer_wishlists 
    WHERE consumer_phone = phone_number
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to check if product is in consumer's wishlist
CREATE OR REPLACE FUNCTION is_product_in_wishlist(phone_number TEXT, product_id_param TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM consumer_wishlists 
    WHERE consumer_phone = phone_number 
    AND product_id = product_id_param
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Add helpful comments for documentation
COMMENT ON TABLE consumer_wishlists IS 'Consumer product wishlists with phone-based authentication integration';
COMMENT ON COLUMN consumer_wishlists.consumer_phone IS 'Links to profiles.phone for consumer identification';
COMMENT ON COLUMN consumer_wishlists.product_id IS 'References the product in wishlist';
COMMENT ON COLUMN consumer_wishlists.priority IS 'User-defined priority from 1 (low) to 5 (high)';
COMMENT ON COLUMN consumer_wishlists.notes IS 'Optional consumer notes about the wishlist item';

-- 8. Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON consumer_wishlists TO authenticated;
GRANT EXECUTE ON FUNCTION get_consumer_wishlist_count(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION is_product_in_wishlist(TEXT, TEXT) TO authenticated;
