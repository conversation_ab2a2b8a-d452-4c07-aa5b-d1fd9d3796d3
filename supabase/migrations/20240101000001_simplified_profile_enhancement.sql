-- Simplified migration to enhance profiles table
-- This migration is designed to work with the existing structure

-- First, check the current structure of the profiles table
DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  -- Check if the profiles table exists
  SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'profiles'
  ) INTO column_exists;

  IF NOT column_exists THEN
    RAISE EXCEPTION 'The profiles table does not exist!';
  END IF;

  -- Add full_name column if it doesn't exist
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'full_name'
  ) INTO column_exists;

  IF NOT column_exists THEN
    ALTER TABLE profiles ADD COLUMN full_name TEXT;
    RAISE NOTICE 'Added full_name column to profiles table';
  END IF;

  -- Add first_name column if it doesn't exist
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'first_name'
  ) INTO column_exists;

  IF NOT column_exists THEN
    ALTER TABLE profiles ADD COLUMN first_name TEXT;
    RAISE NOTICE 'Added first_name column to profiles table';
  END IF;

  -- Add last_name column if it doesn't exist
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'last_name'
  ) INTO column_exists;

  IF NOT column_exists THEN
    ALTER TABLE profiles ADD COLUMN last_name TEXT;
    RAISE NOTICE 'Added last_name column to profiles table';
  END IF;

  -- Add address column if it doesn't exist
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'address'
  ) INTO column_exists;

  IF NOT column_exists THEN
    ALTER TABLE profiles ADD COLUMN address TEXT;
    RAISE NOTICE 'Added address column to profiles table';
  END IF;

  -- Add phone_verified column if it doesn't exist
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'phone_verified'
  ) INTO column_exists;

  IF NOT column_exists THEN
    ALTER TABLE profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'Added phone_verified column to profiles table';
  END IF;
END $$;

-- Create or update the handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
  column_exists BOOLEAN;
BEGIN
  -- Extract role from the user metadata
  user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');

  -- Check if full_name column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'full_name'
  ) INTO column_exists;

  -- Insert the new profile with basic data
  IF column_exists THEN
    -- Full version with full_name
    INSERT INTO public.profiles (
      id,
      email,
      phone,
      role,
      full_name,
      first_name,
      last_name,
      company_name,
      store_name,
      store_address,
      email_verified,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.email,
      -- Format phone number if it exists
      CASE
        WHEN new.phone IS NOT NULL AND new.phone != '' THEN
          CASE
            WHEN new.phone LIKE '+213%' THEN new.phone
            WHEN new.phone LIKE '0%' THEN '+213' || SUBSTRING(new.phone FROM 2)
            ELSE '+213' || new.phone
          END
        ELSE NULL
      END,
      user_role,
      COALESCE(new.raw_user_meta_data->>'fullName', ''),
      COALESCE(new.raw_user_meta_data->>'firstName', ''),
      COALESCE(new.raw_user_meta_data->>'lastName', ''),
      CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
      CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
      CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
      false,
      now(),
      now()
    )
    ON CONFLICT (id) DO NOTHING;
  ELSE
    -- Fallback version without full_name
    INSERT INTO public.profiles (
      id,
      email,
      phone,
      role,
      first_name,
      last_name,
      company_name,
      store_name,
      store_address,
      email_verified,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.email,
      -- Format phone number if it exists
      CASE
        WHEN new.phone IS NOT NULL AND new.phone != '' THEN
          CASE
            WHEN new.phone LIKE '+213%' THEN new.phone
            WHEN new.phone LIKE '0%' THEN '+213' || SUBSTRING(new.phone FROM 2)
            ELSE '+213' || new.phone
          END
        ELSE NULL
      END,
      user_role,
      COALESCE(new.raw_user_meta_data->>'firstName', ''),
      COALESCE(new.raw_user_meta_data->>'lastName', ''),
      CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
      CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
      CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
      false,
      now(),
      now()
    )
    ON CONFLICT (id) DO NOTHING;
  END IF;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create indexes for faster lookups
DO $$
BEGIN
  -- Create email index if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'profiles_email_idx') THEN
    CREATE INDEX profiles_email_idx ON profiles(email);
    RAISE NOTICE 'Created email index on profiles table';
  END IF;

  -- Create role index if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'profiles_role_idx') THEN
    CREATE INDEX profiles_role_idx ON profiles(role);
    RAISE NOTICE 'Created role index on profiles table';
  END IF;
END $$;

-- Create a function to check if a column exists in a table
CREATE OR REPLACE FUNCTION public.check_column_exists(table_name text, column_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  column_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = check_column_exists.table_name
      AND column_name = check_column_exists.column_name
  ) INTO column_exists;

  RETURN column_exists;
END;
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.check_column_exists TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_column_exists TO anon;
GRANT EXECUTE ON FUNCTION public.check_column_exists TO service_role;

-- Try to add a unique constraint on email and role
DO $$
BEGIN
  -- Check if both email and role columns exist
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'email'
  ) AND EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'role'
  ) THEN
    -- Try to add the constraint
    BEGIN
      ALTER TABLE profiles ADD CONSTRAINT profiles_email_role_unique UNIQUE (email, role);
      RAISE NOTICE 'Added unique constraint on email and role';
    EXCEPTION
      WHEN duplicate_table THEN
        RAISE NOTICE 'Unique constraint already exists';
      WHEN others THEN
        RAISE NOTICE 'Could not add unique constraint: %', SQLERRM;
    END;
  END IF;
END $$;
