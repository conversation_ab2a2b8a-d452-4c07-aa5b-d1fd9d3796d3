-- Migration: Create marketplace-specific RLS policies
-- This migration adds policies to allow marketplace visibility while maintaining admin isolation

-- 1. Add marketplace read policies for products table
-- Allow public read access to products with active or out_of_stock status for marketplace display
CREATE POLICY "Allow marketplace read access to active products" ON products
  FOR SELECT USING (
    status IN ('active', 'out_of_stock')
  );

-- 2. Add marketplace read policies for wholesale pricing tiers
-- Allow public read access to pricing tiers for products visible in marketplace
CREATE POLICY "Allow marketplace read access to pricing tiers" ON wholesale_pricing_tiers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 3. Add marketplace read policies for tyre specifications
-- Allow public read access to tyre specs for products visible in marketplace
CREATE POLICY "Allow marketplace read access to tyre specs" ON tyre_specifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 4. Add marketplace read policies for vehicle compatibility
-- Allow public read access to vehicle compatibility for products visible in marketplace
CREATE POLICY "Allow marketplace read access to vehicle compatibility" ON vehicle_compatibility
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 5. Add marketplace read policies for storage objects (product images)
-- Allow public read access to product images for marketplace display
CREATE POLICY "Allow marketplace read access to product images" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'product-images' AND
    EXISTS (
      SELECT 1 FROM products
      WHERE (
        products.primary_image = storage.objects.name OR
        products.primary_image LIKE '%' || storage.objects.name OR
        storage.objects.name = ANY(products.additional_images)
      )
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 6. Add marketplace read policies for manufacturer logos
-- Allow public read access to manufacturer logos for marketplace display
CREATE POLICY "Allow marketplace read access to manufacturer logos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'manufacturer-logos' AND
    EXISTS (
      SELECT 1 FROM products
      WHERE products.manufacturer_logo = storage.objects.name
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 7. Create indexes to optimize marketplace queries
CREATE INDEX IF NOT EXISTS idx_products_marketplace_status ON products(status) WHERE status IN ('active', 'out_of_stock');
CREATE INDEX IF NOT EXISTS idx_products_marketplace_category_status ON products(category, status) WHERE status IN ('active', 'out_of_stock');
CREATE INDEX IF NOT EXISTS idx_products_marketplace_retail_price ON products(retail_price) WHERE retail_price > 0 AND status IN ('active', 'out_of_stock');

-- 8. Add comments for documentation
COMMENT ON POLICY "Allow marketplace read access to active products" ON products IS 'Allows public read access to products with active or out_of_stock status for marketplace display';
COMMENT ON POLICY "Allow marketplace read access to pricing tiers" ON wholesale_pricing_tiers IS 'Allows public read access to pricing tiers for marketplace-visible products';
COMMENT ON POLICY "Allow marketplace read access to tyre specs" ON tyre_specifications IS 'Allows public read access to tyre specifications for marketplace-visible products';
COMMENT ON POLICY "Allow marketplace read access to vehicle compatibility" ON vehicle_compatibility IS 'Allows public read access to vehicle compatibility for marketplace-visible products';
