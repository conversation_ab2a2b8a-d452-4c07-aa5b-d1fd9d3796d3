-- Rollback: Revert changes to profiles table
-- Only use this if you need to roll back the changes

-- First, check if backup exists
DO $$ 
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles_backup') THEN
    -- Drop the modified table
    DROP TABLE profiles;
    
    -- Rename the backup table back to profiles
    ALTER TABLE profiles_backup RENAME TO profiles;
    
    RAISE NOTICE 'Rollback completed successfully. The profiles table has been restored from backup.';
  ELSE
    RAISE EXCEPTION 'Cannot roll back: profiles_backup table does not exist.';
  END IF;
END $$;
