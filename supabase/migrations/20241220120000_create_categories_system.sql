-- =====================================================
-- AROUZ MARKET - Categories System Migration
-- Created: 2024-12-20
-- Purpose: Migrate from localStorage to Supabase categories
-- =====================================================

-- Drop existing tables if they exist to start fresh
DROP TABLE IF EXISTS subcategories CASCADE;
DROP TABLE IF EXISTS categories CASCADE;

-- Create categories table
CREATE TABLE categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  id_prefix TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0
);

-- Create subcategories table
CREATE TABLE subcategories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  category_id TEXT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0
);

-- Create indexes for performance
CREATE INDEX idx_categories_active ON categories(is_active);
CREATE INDEX idx_categories_sort ON categories(sort_order);
CREATE INDEX idx_subcategories_category ON subcategories(category_id);
CREATE INDEX idx_subcategories_active ON subcategories(is_active);
CREATE INDEX idx_subcategories_sort ON subcategories(category_id, sort_order);

-- Enable RLS (Row Level Security)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE subcategories ENABLE ROW LEVEL SECURITY;

-- RLS Policies: Categories are globally readable, admin-only writable
CREATE POLICY "Categories are viewable by everyone" ON categories
  FOR SELECT USING (true);

CREATE POLICY "Categories are insertable by authenticated users" ON categories
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Categories are updatable by authenticated users" ON categories
  FOR UPDATE USING (auth.role() = 'authenticated');

-- RLS Policies: Subcategories are globally readable, admin-only writable
CREATE POLICY "Subcategories are viewable by everyone" ON subcategories
  FOR SELECT USING (true);

CREATE POLICY "Subcategories are insertable by authenticated users" ON subcategories
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Subcategories are updatable by authenticated users" ON subcategories
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Insert existing categories (COMPLETE LIST - 10 categories total)
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('all', 'All Categories', 'All Categories', 'Browse all available product categories', 'PROD', 0),
  ('tyres', 'Tyres & Related Products', 'Tyres', 'Tires, wheel covers, and related accessories', 'TYR', 1),
  ('brakes', 'Brake Parts & Systems', 'Brake Parts', 'Brake discs, pads, calipers, and related brake system components', 'PROD', 2),
  ('filters', 'Filters & Filtration', 'Filters', 'Air filters, oil filters, fuel filters, and all filtration systems', 'PROD', 3),
  ('oils-fluids', 'Oils & Fluids', 'Oils & Fluids', 'Engine oils, hydraulic fluids, coolants, and automotive fluids', 'PROD', 4),
  ('engine', 'Engine', 'Engine', 'Engine components, internal parts, cooling, lubrication, and ignition systems', 'PROD', 5),
  ('window-cleaning', 'Window Cleaning', 'Window Cleaning', 'Windshield wipers, washer systems, and window cleaning components', 'PROD', 6),
  ('glow-plug-ignition', 'Glow Plug & Ignition System', 'Glow Plug & Ignition', 'Spark plugs, ignition coils, glow plugs, and ignition system components', 'PROD', 7),
  ('wishbones-suspension', 'Wishbones & Suspension', 'Wishbones & Suspension', 'Suspension components, control arms, shock absorbers, and steering parts', 'PROD', 8),
  ('electrical-systems', 'Electrical Systems', 'Electrical Systems', 'Electrical components, sensors, lighting, wiring, and electronic control units', 'PROD', 9)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Success message
SELECT 'Categories system migration completed successfully!' as status;
