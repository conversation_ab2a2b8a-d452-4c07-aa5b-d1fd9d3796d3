-- Simple Category Images Storage Bucket Creation
-- This is a simplified version that focuses on the essential bucket setup

-- 1. Create the category-images bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Enable RLS on storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 3. Create RLS policy for public read access to category images
DROP POLICY IF EXISTS "Public read access for category images" ON storage.objects;
CREATE POLICY "Public read access for category images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

-- 4. Create RLS policy for authenticated users to upload category images
DROP POLICY IF EXISTS "Authenticated users can upload category images" ON storage.objects;
CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- 5. Create RLS policy for authenticated users to update category images
DROP POLICY IF EXISTS "Authenticated users can update category images" ON storage.objects;
CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- 6. Create RLS policy for authenticated users to delete category images
DROP POLICY IF EXISTS "Authenticated users can delete category images" ON storage.objects;
CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- 7. Create basic indexes for performance
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_id ON storage.objects(bucket_id);
CREATE INDEX IF NOT EXISTS idx_storage_objects_name ON storage.objects(name);

-- 8. Verify the bucket was created successfully
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'category-images') THEN
    RAISE NOTICE 'Category images bucket created successfully';
  ELSE
    RAISE EXCEPTION 'Failed to create category images bucket';
  END IF;
END $$;
