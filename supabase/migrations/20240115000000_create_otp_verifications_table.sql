-- Migration: Create OTP verifications table for phone authentication
-- This table stores OTP verification records for secure phone-only authentication

-- Create OTP verifications table
CREATE TABLE IF NOT EXISTS otp_verifications (
  id TEXT PRIMARY KEY,
  phone TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'verified', 'failed', 'expired')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  verified_at TIMESTAMP WITH TIME ZONE,
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS otp_verifications_phone_idx ON otp_verifications(phone);
CREATE INDEX IF NOT EXISTS otp_verifications_status_idx ON otp_verifications(status);
CREATE INDEX IF NOT EXISTS otp_verifications_expires_at_idx ON otp_verifications(expires_at);
CREATE INDEX IF NOT EXISTS otp_verifications_created_at_idx ON otp_verifications(created_at);

-- Enable RLS
ALTER TABLE otp_verifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Allow service role to manage all records (for backend operations)
CREATE POLICY "Service role can manage all otp_verifications"
  ON otp_verifications
  USING (auth.role() = 'service_role');

-- Allow authenticated users to view their own verification records
CREATE POLICY "Users can view their own otp_verifications"
  ON otp_verifications
  FOR SELECT
  USING (auth.uid()::text = ANY(
    SELECT id::text FROM profiles WHERE phone = otp_verifications.phone
  ));

-- Add phone_verified column to profiles table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'profiles' AND column_name = 'phone_verified') THEN
    ALTER TABLE profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'profiles' AND column_name = 'phone_verification_method') THEN
    ALTER TABLE profiles ADD COLUMN phone_verification_method TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'profiles' AND column_name = 'phone_verified_at') THEN
    ALTER TABLE profiles ADD COLUMN phone_verified_at TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Create function to clean up expired OTP records
CREATE OR REPLACE FUNCTION cleanup_expired_otps()
RETURNS void AS $$
BEGIN
  UPDATE otp_verifications 
  SET status = 'expired' 
  WHERE status = 'pending' 
    AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to handle successful phone verification
CREATE OR REPLACE FUNCTION handle_phone_verification(
  p_phone TEXT,
  p_verification_id TEXT
)
RETURNS JSON AS $$
DECLARE
  v_user_id UUID;
  v_profile_exists BOOLEAN;
  v_result JSON;
BEGIN
  -- Check if user with this phone already exists
  SELECT id INTO v_user_id
  FROM profiles 
  WHERE phone = p_phone 
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- Update existing profile
    UPDATE profiles 
    SET 
      phone_verified = TRUE,
      phone_verification_method = 'sms_otp',
      phone_verified_at = NOW(),
      last_login = NOW(),
      updated_at = NOW()
    WHERE id = v_user_id;
    
    v_result := json_build_object(
      'user_id', v_user_id,
      'action', 'login',
      'phone', p_phone,
      'verified_at', NOW()
    );
  ELSE
    -- Generate new user ID
    v_user_id := gen_random_uuid();

    -- Create profile only (no auth.users insertion needed for phone auth)
    INSERT INTO profiles (
      id,
      phone,
      role,
      phone_verified,
      phone_verification_method,
      phone_verified_at,
      last_login,
      created_at,
      updated_at,
      full_name
    ) VALUES (
      v_user_id,
      p_phone,
      'consumer',
      TRUE,
      'sms_otp',
      NOW(),
      NOW(),
      NOW(),
      NOW(),
      NULL -- Will be updated by frontend after name collection
    );
    
    v_result := json_build_object(
      'user_id', v_user_id,
      'action', 'signup',
      'phone', p_phone,
      'verified_at', NOW()
    );
  END IF;
  
  -- Mark OTP as verified
  UPDATE otp_verifications 
  SET 
    status = 'verified',
    verified_at = NOW()
  WHERE id = p_verification_id;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE otp_verifications IS 'OTP verification records for phone-only authentication';
COMMENT ON COLUMN otp_verifications.id IS 'Unique verification ID';
COMMENT ON COLUMN otp_verifications.phone IS 'Phone number in international format (+213XXXXXXXXX)';
COMMENT ON COLUMN otp_verifications.status IS 'Verification status: pending, verified, failed, expired';
COMMENT ON COLUMN otp_verifications.attempts IS 'Number of verification attempts';
COMMENT ON COLUMN otp_verifications.max_attempts IS 'Maximum allowed attempts before blocking';

COMMENT ON FUNCTION cleanup_expired_otps() IS 'Marks expired OTP verifications as expired';
COMMENT ON FUNCTION handle_phone_verification(TEXT, TEXT) IS 'Handles successful phone verification and user creation/login';
