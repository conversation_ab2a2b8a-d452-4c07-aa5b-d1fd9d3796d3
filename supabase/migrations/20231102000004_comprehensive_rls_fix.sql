-- Migration: Comprehensive fix for RLS policies and profile creation
-- This migration completely rebuilds the RLS policies and ensures the handle_new_user function works correctly

-- 1. First, make sure the phone column is optional
ALTER TABLE profiles ALTER COLUMN phone DROP NOT NULL;

-- 2. Temporarily disable <PERSON><PERSON> to ensure we can modify everything
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 3. Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Service role can do anything" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert access for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update access for users based on email" ON profiles;

-- 4. Recreate the handle_new_user function with SECURITY DEFINER
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON>URNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    email, 
    phone,
    role, 
    created_at, 
    updated_at,
    email_verified
  )
  VALUES (
    new.id, 
    new.email,
    new.phone,
    COALESCE(new.raw_user_meta_data->>'role', 'consumer'), 
    now(), 
    now(),
    false
  );
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Make sure the trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 7. Create a policy that allows the postgres role (used by triggers) to do anything
CREATE POLICY "Postgres role can do anything" ON profiles
  FOR ALL
  USING (current_user = 'postgres')
  WITH CHECK (current_user = 'postgres');

-- 8. Create a policy that allows the service role to do anything
CREATE POLICY "Service role can do anything" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- 9. Create a policy that allows authenticated users to read their own profile
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- 10. Create a policy that allows authenticated users to update their own profile
CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- 11. Create a policy that allows authenticated users to insert their own profile
CREATE POLICY "Users can insert their own profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 12. Grant necessary permissions
GRANT ALL ON profiles TO postgres;
GRANT ALL ON profiles TO service_role;
GRANT SELECT, UPDATE ON profiles TO authenticated;
GRANT SELECT ON profiles TO anon;

-- 13. Create a test user to verify everything works
-- This is just for testing and can be removed in production
INSERT INTO auth.users (id, email, raw_user_meta_data)
VALUES 
  ('00000000-0000-0000-0000-000000000000', '<EMAIL>', '{"role":"consumer"}'::jsonb)
ON CONFLICT (id) DO NOTHING;

-- 14. Verify the trigger works by checking if a profile was created for the test user
SELECT * FROM profiles WHERE id = '00000000-0000-0000-0000-000000000000';
