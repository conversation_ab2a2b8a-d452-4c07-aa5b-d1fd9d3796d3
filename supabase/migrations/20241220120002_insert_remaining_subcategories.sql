-- =====================================================
-- AROUZ MARKET - Insert Remaining Subcategories (Part 2)
-- Created: 2024-12-20
-- Purpose: Insert Window Cleaning, Glow Plug & Ignition subcategories
-- =====================================================

-- Insert Window Cleaning subcategories
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('front-windshield-wiper-blades', 'Front Windshield Wiper Blades', 'Front Windshield Wiper Blades', 'window-cleaning', 1),
  ('rear-window-wiper-blade', 'Rear Window Wiper Blade', 'Rear Window Wiper Blade', 'window-cleaning', 2),
  ('wiper-arm-assembly', 'Wiper Arm Assembly', 'Wiper Arm Assembly', 'window-cleaning', 3),
  ('wiper-rubber-refill-strip', 'Wiper Rubber Refill Strip', 'Wiper Rubber Refill Strip', 'window-cleaning', 4),
  ('headlight-wiper-blades', 'Headlight Wiper Blades', 'Headlight Wiper Blades', 'window-cleaning', 5),
  ('windshield-wiper-motor', 'Windshield Wiper Motor', 'Windshield Wiper Motor', 'window-cleaning', 6),
  ('wiper-linkage-mechanical-assembly', 'Wiper Linkage / Mechanical Assembly', 'Wiper Linkage / Mechanical Assembly', 'window-cleaning', 7),
  ('windshield-washer-pump', 'Windshield Washer Pump', 'Windshield Washer Pump', 'window-cleaning', 8),
  ('windshield-washer-fluid', 'Windshield Washer Fluid', 'Windshield Washer Fluid', 'window-cleaning', 9),
  ('washer-fluid-reservoir', 'Washer Fluid Reservoir', 'Washer Fluid Reservoir', 'window-cleaning', 10),
  ('windshield-washer-nozzles', 'Windshield Washer Nozzles', 'Windshield Washer Nozzles', 'window-cleaning', 11),
  ('headlight-washer-fluid-nozzles', 'Headlight Washer Fluid Nozzles', 'Headlight Washer Fluid Nozzles', 'window-cleaning', 12),
  ('headlight-washer-pump', 'Headlight Washer Pump', 'Headlight Washer Pump', 'window-cleaning', 13),
  ('washer-fluid-hose-connector', 'Washer Fluid Hose Connector', 'Washer Fluid Hose Connector', 'window-cleaning', 14),
  ('washer-fluid-level-sensor', 'Washer Fluid Level Sensor', 'Washer Fluid Level Sensor', 'window-cleaning', 15),
  ('steering-column-wiper-switch', 'Steering Column Wiper Switch', 'Steering Column Wiper Switch', 'window-cleaning', 16),
  ('windshield-wiper-relay', 'Windshield Wiper Relay', 'Windshield Wiper Relay', 'window-cleaning', 17),
  ('rain-precipitation-sensor', 'Rain/Precipitation Sensor', 'Rain/Precipitation Sensor', 'window-cleaning', 18);

-- Insert Glow Plug & Ignition System subcategories
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('spark-plugs', 'Spark Plugs', 'Spark Plugs', 'glow-plug-ignition', 1),
  ('ignition-coil', 'Ignition Coil', 'Ignition Coil', 'glow-plug-ignition', 2),
  ('spark-plug-connector', 'Spark Plug Connector', 'Spark Plug Connector', 'glow-plug-ignition', 3),
  ('spark-plug-wire-set', 'Spark Plug Wire Set', 'Spark Plug Wire Set', 'glow-plug-ignition', 4),
  ('distributor-cap', 'Distributor Cap', 'Distributor Cap', 'glow-plug-ignition', 5),
  ('distributor-rotor', 'Distributor Rotor', 'Distributor Rotor', 'glow-plug-ignition', 6),
  ('ignition-control-module', 'Ignition Control Module', 'Ignition Control Module', 'glow-plug-ignition', 7),
  ('knock-sensor', 'Knock Sensor', 'Knock Sensor', 'glow-plug-ignition', 8),
  ('crankshaft-position-sensor', 'Crankshaft Position Sensor', 'Crankshaft Position Sensor', 'glow-plug-ignition', 9),
  ('camshaft-position-sensor', 'Camshaft Position Sensor', 'Camshaft Position Sensor', 'glow-plug-ignition', 10),
  ('glow-plug', 'Glow Plug', 'Glow Plug', 'glow-plug-ignition', 11),
  ('glow-plug-control-unit', 'Glow Plug Control Unit', 'Glow Plug Control Unit', 'glow-plug-ignition', 12),
  ('engine-control-unit-ecu', 'Engine Control Unit (ECU)', 'Engine Control Unit (ECU)', 'glow-plug-ignition', 13),
  ('power-distributor-assembly', 'Power Distributor Assembly', 'Power Distributor Assembly', 'glow-plug-ignition', 14),
  ('dust-cover-for-distributor', 'Dust Cover for Distributor', 'Dust Cover for Distributor', 'glow-plug-ignition', 15),
  ('distributor-housing-repair-kit', 'Distributor Housing Repair Kit', 'Distributor Housing Repair Kit', 'glow-plug-ignition', 16),
  ('flow-distributor-suppression-unit', 'Flow Distributor Suppression Unit', 'Flow Distributor Suppression Unit', 'glow-plug-ignition', 17),
  ('distribution-crankcase-gasket', 'Distribution Crankcase Gasket', 'Distribution Crankcase Gasket', 'glow-plug-ignition', 18),
  ('glow-plug-ignition-tool-set', 'Glow Plug & Ignition Tool Set', 'Glow Plug & Ignition Tool Set', 'glow-plug-ignition', 19);

-- Insert Engine subcategories
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('engine-block', 'Engine Block', 'Engine Block', 'engine', 1),
  ('cylinder-head', 'Cylinder Head', 'Cylinder Head', 'engine', 2),
  ('pistons', 'Pistons', 'Pistons', 'engine', 3),
  ('connecting-rods', 'Connecting Rods', 'Connecting Rods', 'engine', 4),
  ('crankshaft', 'Crankshaft', 'Crankshaft', 'engine', 5),
  ('camshaft', 'Camshaft', 'Camshaft', 'engine', 6),
  ('valves', 'Valves', 'Valves', 'engine', 7),
  ('valve-springs', 'Valve Springs', 'Valve Springs', 'engine', 8),
  ('timing-belt', 'Timing Belt', 'Timing Belt', 'engine', 9),
  ('timing-chain', 'Timing Chain', 'Timing Chain', 'engine', 10),
  ('water-pump', 'Water Pump', 'Water Pump', 'engine', 11),
  ('oil-pump', 'Oil Pump', 'Oil Pump', 'engine', 12),
  ('fuel-pump', 'Fuel Pump', 'Fuel Pump', 'engine', 13),
  ('radiator', 'Radiator', 'Radiator', 'engine', 14),
  ('thermostat', 'Thermostat', 'Thermostat', 'engine', 15),
  ('gaskets', 'Gaskets', 'Gaskets', 'engine', 16),
  ('seals', 'Seals', 'Seals', 'engine', 17),
  ('bearings', 'Bearings', 'Bearings', 'engine', 18);

-- Insert Wishbones & Suspension subcategories
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('control-arm', 'Control Arm', 'Control Arm', 'wishbones-suspension', 1),
  ('ball-joint', 'Ball Joint', 'Ball Joint', 'wishbones-suspension', 2),
  ('shock-absorber', 'Shock Absorber', 'Shock Absorber', 'wishbones-suspension', 3),
  ('strut', 'Strut', 'Strut', 'wishbones-suspension', 4),
  ('coil-spring', 'Coil Spring', 'Coil Spring', 'wishbones-suspension', 5),
  ('leaf-spring', 'Leaf Spring', 'Leaf Spring', 'wishbones-suspension', 6),
  ('stabilizer-bar', 'Stabilizer Bar', 'Stabilizer Bar', 'wishbones-suspension', 7),
  ('tie-rod', 'Tie Rod', 'Tie Rod', 'wishbones-suspension', 8),
  ('steering-rack', 'Steering Rack', 'Steering Rack', 'wishbones-suspension', 9),
  ('power-steering-pump', 'Power Steering Pump', 'Power Steering Pump', 'wishbones-suspension', 10),
  ('wheel-bearing', 'Wheel Bearing', 'Wheel Bearing', 'wishbones-suspension', 11),
  ('hub-assembly', 'Hub Assembly', 'Hub Assembly', 'wishbones-suspension', 12),
  ('cv-joint', 'CV Joint', 'CV Joint', 'wishbones-suspension', 13),
  ('drive-shaft', 'Drive Shaft', 'Drive Shaft', 'wishbones-suspension', 14),
  ('suspension-bushing', 'Suspension Bushing', 'Suspension Bushing', 'wishbones-suspension', 15),
  ('steering-wheel', 'Steering Wheel', 'Steering Wheel', 'wishbones-suspension', 16),
  ('steering-column', 'Steering Column', 'Steering Column', 'wishbones-suspension', 17),
  ('alignment-kit', 'Alignment Kit', 'Alignment Kit', 'wishbones-suspension', 18);

-- Insert Electrical Systems subcategories
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('battery', 'Battery', 'Battery', 'electrical-systems', 1),
  ('alternator', 'Alternator', 'Alternator', 'electrical-systems', 2),
  ('starter-motor', 'Starter Motor', 'Starter Motor', 'electrical-systems', 3),
  ('headlights', 'Headlights', 'Headlights', 'electrical-systems', 4),
  ('taillights', 'Taillights', 'Taillights', 'electrical-systems', 5),
  ('turn-signals', 'Turn Signals', 'Turn Signals', 'electrical-systems', 6),
  ('fuses', 'Fuses', 'Fuses', 'electrical-systems', 7),
  ('relays', 'Relays', 'Relays', 'electrical-systems', 8),
  ('wiring-harness', 'Wiring Harness', 'Wiring Harness', 'electrical-systems', 9),
  ('sensors', 'Sensors', 'Sensors', 'electrical-systems', 10),
  ('ecu-modules', 'ECU Modules', 'ECU Modules', 'electrical-systems', 11),
  ('horn', 'Horn', 'Horn', 'electrical-systems', 12),
  ('dashboard-lights', 'Dashboard Lights', 'Dashboard Lights', 'electrical-systems', 13),
  ('interior-lights', 'Interior Lights', 'Interior Lights', 'electrical-systems', 14),
  ('exterior-lights', 'Exterior Lights', 'Exterior Lights', 'electrical-systems', 15),
  ('switches', 'Switches', 'Switches', 'electrical-systems', 16),
  ('connectors', 'Connectors', 'Connectors', 'electrical-systems', 17),
  ('cables', 'Cables', 'Cables', 'electrical-systems', 18);

-- Success message
SELECT 'All subcategories inserted successfully!' as status;
