-- Migration: FIN<PERSON> RLS fix - Completely eliminate circular dependencies
-- This creates the simplest possible policies with NO cross-table references

-- 1. DROP ALL EXISTING POLICIES AGAIN
DROP POLICY IF EXISTS "orders_select_by_consumer_phone" ON orders;
DROP POLICY IF EXISTS "orders_select_by_supplier" ON orders;
DROP POLICY IF EXISTS "orders_insert_authenticated" ON orders;

DROP POLICY IF EXISTS "order_items_select_by_consumer" ON order_items;
DROP POLICY IF EXISTS "order_items_select_by_supplier" ON order_items;
DROP POLICY IF EXISTS "order_items_insert_authenticated" ON order_items;

DROP POLICY IF EXISTS "shipments_select_by_consumer" ON shipments;
DROP POLICY IF EXISTS "shipments_select_by_supplier" ON shipments;
DROP POLICY IF EXISTS "shipments_insert_authenticated" ON shipments;
DROP POLICY IF EXISTS "shipments_update_by_shipping_company" ON shipments;

-- 2. CREATE ULTRA-SIMPLE POLICIES WITH NO CROSS-TABLE REFERENCES

-- Orders table - ONLY references profiles table
CREATE POLICY "orders_consumer_access" ON orders
  FOR SELECT USING (
    consumer_phone IN (
      SELECT phone FROM profiles 
      WHERE id = auth.uid() AND role = 'consumer'
    )
  );

CREATE POLICY "orders_allow_insert" ON orders
  FOR INSERT WITH CHECK (true);

-- Order items table - ONLY references own columns and profiles
CREATE POLICY "order_items_supplier_access" ON order_items
  FOR SELECT USING (
    supplier_account_id = auth.uid()
  );

CREATE POLICY "order_items_allow_insert" ON order_items
  FOR INSERT WITH CHECK (true);

-- Shipments table - ONLY references own columns and shipping_companies
CREATE POLICY "shipments_allow_select" ON shipments
  FOR SELECT USING (true);

CREATE POLICY "shipments_allow_insert" ON shipments
  FOR INSERT WITH CHECK (true);

-- Allow suppliers/merchants to update shipments for their orders
CREATE POLICY "shipments_supplier_update" ON shipments
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = shipments.order_id
      AND oi.supplier_account_id = auth.uid()
    )
  );

CREATE POLICY "shipments_shipping_company_update" ON shipments
  FOR UPDATE USING (
    shipping_company_id IN (
      SELECT id FROM shipping_companies 
      WHERE login_code = current_setting('app.shipping_company_code', true)
    )
  );

-- 3. Add comments
COMMENT ON POLICY "orders_allow_insert" ON orders IS 'Allows order creation - no restrictions';
COMMENT ON POLICY "order_items_allow_insert" ON order_items IS 'Allows order item creation - no restrictions';
COMMENT ON POLICY "shipments_allow_insert" ON shipments IS 'Allows shipment creation - no restrictions';
