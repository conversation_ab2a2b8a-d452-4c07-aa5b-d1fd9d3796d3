-- Migration: Add Consumer Privacy Protection System
-- Date: 2025-01-17
-- Purpose: Implement consumer privacy protection with original_full_name storage
--
-- PRIVACY PROTECTION SYSTEM:
-- - full_name: Privacy-protected name visible to suppliers/merchants/shipping (e.g., "<PERSON><PERSON>")
-- - original_full_name: Original full name for internal use only (e.g., "<PERSON><PERSON> Palorino")

-- Step 1: Add original_full_name column to profiles table
DO $$
BEGIN
  -- Check if original_full_name column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'profiles'
    AND column_name = 'original_full_name'
  ) THEN
    ALTER TABLE profiles ADD COLUMN original_full_name TEXT;
    RAISE NOTICE 'Added original_full_name column to profiles table';
  ELSE
    RAISE NOTICE 'original_full_name column already exists';
  END IF;
END $$;

-- Step 2: Add comments to explain the privacy protection system
COMMENT ON COLUMN profiles.full_name IS 'Privacy-protected name visible to suppliers/merchants/shipping (e.g., "<PERSON><PERSON>")';
COMMENT ON COLUMN profiles.original_full_name IS 'Original full name for internal use only (e.g., "<PERSON><PERSON> Palorino")';

-- Step 3: Create index for better performance on name searches
CREATE INDEX IF NOT EXISTS idx_profiles_original_full_name ON profiles(original_full_name);
CREATE INDEX IF NOT EXISTS idx_profiles_full_name ON profiles(full_name);

-- Step 4: Function to get privacy-protected consumer name for orders
CREATE OR REPLACE FUNCTION get_consumer_display_name(consumer_phone TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  display_name TEXT;
BEGIN
  -- Get privacy-protected name from profiles
  SELECT full_name INTO display_name
  FROM profiles
  WHERE phone = consumer_phone AND role = 'consumer'
  LIMIT 1;

  -- Return display name or fallback
  RETURN COALESCE(display_name, 'Consumer');
END;
$$;

-- Step 5: Function to get original consumer name for internal use
CREATE OR REPLACE FUNCTION get_consumer_original_name(consumer_phone TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  original_name TEXT;
BEGIN
  -- Get original full name from profiles (internal use only)
  SELECT original_full_name INTO original_name
  FROM profiles
  WHERE phone = consumer_phone AND role = 'consumer'
  LIMIT 1;

  -- Return original name or fallback to display name
  IF original_name IS NULL OR original_name = '' THEN
    SELECT full_name INTO original_name
    FROM profiles
    WHERE phone = consumer_phone AND role = 'consumer'
    LIMIT 1;
  END IF;

  RETURN COALESCE(original_name, 'Consumer');
END;
$$;

-- Step 6: Function to convert full name to privacy-protected format
CREATE OR REPLACE FUNCTION convert_to_privacy_protected_name(full_name_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
  name_parts TEXT[];
  first_name TEXT;
  last_initial TEXT;
BEGIN
  -- Handle null or empty input
  IF full_name_input IS NULL OR TRIM(full_name_input) = '' THEN
    RETURN 'Consumer';
  END IF;

  -- Split name into parts
  name_parts := string_to_array(TRIM(full_name_input), ' ');

  -- If only one name part, return as is
  IF array_length(name_parts, 1) = 1 THEN
    RETURN name_parts[1];
  END IF;

  -- Get first name and last initial
  first_name := name_parts[1];
  last_initial := UPPER(SUBSTRING(name_parts[array_length(name_parts, 1)] FROM 1 FOR 1));

  -- Return privacy-protected format
  RETURN first_name || ' ' || last_initial;
END;
$$;

-- Step 7: Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_consumer_display_name(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_consumer_original_name(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION convert_to_privacy_protected_name(TEXT) TO authenticated;

-- Step 8: Update existing consumer profiles to have privacy-protected names
DO $$
DECLARE
  consumer_record RECORD;
  protected_name TEXT;
BEGIN
  -- Process all consumer profiles that have full_name but no original_full_name
  FOR consumer_record IN
    SELECT id, full_name
    FROM profiles
    WHERE role = 'consumer'
    AND full_name IS NOT NULL
    AND full_name != ''
    AND (original_full_name IS NULL OR original_full_name = '')
  LOOP
    -- Convert to privacy-protected format
    protected_name := convert_to_privacy_protected_name(consumer_record.full_name);

    -- Update the profile
    UPDATE profiles
    SET
      original_full_name = consumer_record.full_name,  -- Store original
      full_name = protected_name,                      -- Store privacy-protected
      updated_at = NOW()
    WHERE id = consumer_record.id;

    RAISE NOTICE 'Updated consumer profile: % -> %', consumer_record.full_name, protected_name;
  END LOOP;
END $$;

-- Step 9: Create trigger to automatically handle new consumer registrations
CREATE OR REPLACE FUNCTION handle_consumer_privacy_protection()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process consumer role updates
  IF NEW.role = 'consumer' AND NEW.full_name IS NOT NULL AND NEW.full_name != '' THEN
    -- If original_full_name is not set, this is a new registration
    IF NEW.original_full_name IS NULL OR NEW.original_full_name = '' THEN
      -- Store original name and convert full_name to privacy-protected format
      NEW.original_full_name := NEW.full_name;
      NEW.full_name := convert_to_privacy_protected_name(NEW.full_name);
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new consumer registrations
DROP TRIGGER IF EXISTS trigger_consumer_privacy_protection ON profiles;
CREATE TRIGGER trigger_consumer_privacy_protection
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION handle_consumer_privacy_protection();

-- Step 10: Final verification and success messages
DO $$
BEGIN
  RAISE NOTICE '✅ Consumer Privacy Protection System installed successfully!';
  RAISE NOTICE 'Privacy-protected names will be visible to suppliers/merchants/shipping companies';
  RAISE NOTICE 'Original full names are stored securely for internal use only';
END $$;

-- Step 11: Verification queries (for testing)
-- Uncomment these to verify the migration worked correctly:

-- SELECT 'Consumer Profiles with Privacy Protection' as check_type;
-- SELECT
--   id,
--   role,
--   full_name as privacy_protected_name,
--   original_full_name as internal_full_name,
--   phone
-- FROM profiles
-- WHERE role = 'consumer'
-- AND full_name IS NOT NULL
-- ORDER BY created_at DESC
-- LIMIT 5;

-- SELECT 'Test Privacy Protection Function' as check_type;
-- SELECT convert_to_privacy_protected_name('Ahmed Benali') as test_result;
