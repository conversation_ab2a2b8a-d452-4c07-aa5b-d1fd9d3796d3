-- Migration: Fix phone authentication by adding unique constraint on phone column
-- This prevents duplicate profiles with the same phone number

-- First, clean up any existing duplicate profiles
-- Keep the most recent profile for each phone number
WITH duplicates AS (
  SELECT 
    phone,
    id,
    ROW_NUMBER() OVER (PARTITION BY phone ORDER BY created_at DESC) as rn
  FROM profiles 
  WHERE phone IS NOT NULL 
    AND phone != ''
    AND role = 'consumer'
)
DELETE FROM profiles 
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Add unique constraint on phone column for consumer role
-- This ensures only one consumer profile per phone number
DO $$
BEGIN
  -- Check if constraint already exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'profiles_phone_consumer_unique'
  ) THEN
    -- Add unique constraint for phone + consumer role
    ALTER TABLE profiles 
    ADD CONSTRAINT profiles_phone_consumer_unique 
    UNIQUE (phone, role) 
    DEFERRABLE INITIALLY DEFERRED;
    
    RAISE NOTICE 'Added unique constraint on phone and role';
  ELSE
    RAISE NOTICE 'Unique constraint already exists';
  END IF;
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Could not add unique constraint: %', SQLERRM;
END $$;

-- Update the handle_phone_verification function to handle duplicates properly
CREATE OR REPLACE FUNCTION handle_phone_verification(
  p_phone TEXT,
  p_verification_id TEXT
)
RETURNS JSON AS $$
DECLARE
  v_user_id UUID;
  v_profile_exists BOOLEAN;
  v_result JSON;
BEGIN
  -- Check if user with this phone already exists (with proper handling)
  SELECT id INTO v_user_id
  FROM profiles 
  WHERE phone = p_phone 
    AND role = 'consumer'
  ORDER BY created_at DESC
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- Update existing profile
    UPDATE profiles 
    SET 
      phone_verified = TRUE,
      phone_verification_method = 'sms_otp',
      phone_verified_at = NOW(),
      last_login = NOW(),
      updated_at = NOW()
    WHERE id = v_user_id;
    
    v_result := json_build_object(
      'user_id', v_user_id,
      'action', 'login',
      'phone', p_phone,
      'verified_at', NOW()
    );
  ELSE
    -- Generate new user ID
    v_user_id := gen_random_uuid();

    -- Create profile with ON CONFLICT handling
    INSERT INTO profiles (
      id,
      phone,
      role,
      phone_verified,
      phone_verification_method,
      phone_verified_at,
      last_login,
      created_at,
      updated_at
    ) VALUES (
      v_user_id,
      p_phone,
      'consumer',
      TRUE,
      'sms_otp',
      NOW(),
      NOW(),
      NOW(),
      NOW()
    )
    ON CONFLICT (phone, role) DO UPDATE SET
      phone_verified = TRUE,
      phone_verification_method = 'sms_otp',
      phone_verified_at = NOW(),
      last_login = NOW(),
      updated_at = NOW()
    RETURNING id INTO v_user_id;
    
    v_result := json_build_object(
      'user_id', v_user_id,
      'action', 'signup',
      'phone', p_phone,
      'verified_at', NOW()
    );
  END IF;
  
  -- Mark OTP as verified
  UPDATE otp_verifications 
  SET 
    status = 'verified',
    verified_at = NOW()
  WHERE id = p_verification_id;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS profiles_phone_role_idx ON profiles(phone, role) WHERE phone IS NOT NULL;

-- Add comment
COMMENT ON CONSTRAINT profiles_phone_consumer_unique ON profiles IS 'Ensures unique phone numbers per role to prevent duplicate consumer profiles';
