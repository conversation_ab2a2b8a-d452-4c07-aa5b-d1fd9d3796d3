-- Fix RLS issue for order items retrieval
-- The problem: Multiple RLS policies were conflicting and filtering out order items
-- The solution: Fix the consumer policy to be more permissive

-- Drop existing conflicting policies
DROP POLICY IF EXISTS "Order items access for consumers" ON order_items;
DROP POLICY IF EXISTS "Order items access for suppliers" ON order_items;

-- Create a single, comprehensive policy for order items access
CREATE POLICY "Order items comprehensive access" ON order_items
  FOR SELECT USING (
    -- Allow consumers to see all items in their orders
    EXISTS (
      SELECT 1 FROM orders o
      JOIN profiles p ON p.phone = o.consumer_phone
      WHERE o.id = order_items.order_id
      AND p.role = 'consumer'
      AND p.id = auth.uid()
    )
    OR
    -- Allow suppliers to see their own items
    supplier_account_id = auth.uid()
    OR
    -- Allow merchants to see their own items
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = supplier_account_id
      AND p.role = 'merchant'
      AND p.id = auth.uid()
    )
  );

-- Keep the insert policy for order creation
-- (This should already exist but let's make sure)
DROP POLICY IF EXISTS "Order items insert for order creation" ON order_items;
CREATE POLICY "Order items insert for order creation" ON order_items
  FOR INSERT WITH CHECK (true);

-- Add comment for documentation
COMMENT ON POLICY "Order items comprehensive access" ON order_items IS 'Allows consumers to see ALL items in their orders, and suppliers/merchants to see their own items';
