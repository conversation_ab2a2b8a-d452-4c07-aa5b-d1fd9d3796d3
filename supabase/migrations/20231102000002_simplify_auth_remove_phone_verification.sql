-- Migration: Simplify authentication by removing phone verification requirement
-- This migration modifies the profiles table and authentication triggers to make phone optional

-- 1. First, modify the profiles table to make phone optional if it's not already
ALTER TABLE profiles
ALTER COLUMN phone DROP NOT NULL;

-- 2. Update the handle_new_user function to not require phone
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    email, 
    phone,
    role, 
    created_at, 
    updated_at,
    email_verified
  )
  VALUES (
    new.id, 
    new.email,
    new.phone,
    COALESCE(new.raw_user_meta_data->>'role', 'consumer'), 
    now(), 
    now(),
    false
  )
  ON CONFLICT (id) DO NOTHING;
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Fix the RLS policies to ensure they work correctly
-- First, drop existing policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;

-- Then recreate them with proper permissions
-- Policy for users to view their own profile
CREATE POLICY "Users can view their own profile"
  ON profiles
  FOR SELECT
  USING (auth.uid() = id::uuid);

-- Policy for users to update their own profile
CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id::uuid);

-- Policy for inserting new profiles (needed for sign-up)
CREATE POLICY "Users can insert their own profile"
  ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id::uuid);

-- 4. Add a policy to allow the service role to manage all profiles
-- This is important for the handle_new_user trigger function to work
CREATE POLICY "Service role can manage all profiles"
  ON profiles
  USING (auth.role() = 'service_role');

-- 5. Ensure the trigger for new user creation exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 6. Update any existing users to ensure they have profiles
INSERT INTO profiles (id, email, role, created_at, updated_at)
SELECT 
  id, 
  email, 
  COALESCE(raw_user_meta_data->>'role', 'consumer')::text, 
  created_at, 
  created_at
FROM 
  auth.users
ON CONFLICT (id) DO NOTHING;
