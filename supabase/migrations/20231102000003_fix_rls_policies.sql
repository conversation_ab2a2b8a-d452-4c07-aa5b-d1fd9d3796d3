-- Migration: Fix RLS policies to allow proper profile creation
-- This migration fixes the RLS policies that are preventing new user registration

-- First, enable RLS on the profiles table if it's not already enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;

-- Create a policy to allow the service role to do anything
-- This is critical for the auth triggers to work properly
CREATE POLICY "Service role can do anything" ON profiles
  USING (auth.role() = 'service_role');

-- Create a policy to allow authenticated users to read their own profile
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Create a policy to allow authenticated users to update their own profile
CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Create a policy to allow authenticated users to insert their own profile
-- This is needed for manual profile creation (though most profiles are created by the trigger)
CREATE POLICY "Users can insert their own profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Update the handle_new_user function to use the service role properly
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    email, 
    phone,
    role, 
    created_at, 
    updated_at,
    email_verified
  )
  VALUES (
    new.id, 
    new.email,
    new.phone,
    COALESCE(new.raw_user_meta_data->>'role', 'consumer'), 
    now(), 
    now(),
    false
  )
  ON CONFLICT (id) DO NOTHING;
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is properly set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions to the authenticated role
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, UPDATE ON public.profiles TO authenticated;

-- Grant necessary permissions to the anon role for public access
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON public.profiles TO anon;

-- Grant all permissions to the service role
GRANT ALL ON public.profiles TO service_role;
