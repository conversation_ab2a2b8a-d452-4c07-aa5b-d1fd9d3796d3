-- Migration: Add unique constraint to prevent duplicate reviews
-- This ensures one consumer can only review each product once
-- 🎯 CRITICAL: This prevents multiple reviews from same consumer for same product

-- 1. First, clean up any existing duplicate reviews
-- Keep the most recent review for each consumer-product combination
WITH duplicates AS (
  SELECT 
    consumer_phone,
    product_id,
    id,
    ROW_NUMBER() OVER (
      PARTITION BY consumer_phone, product_id 
      ORDER BY created_at DESC
    ) as rn
  FROM consumer_reviews 
  WHERE consumer_phone IS NOT NULL 
    AND consumer_phone != ''
    AND product_id IS NOT NULL
    AND product_id != ''
)
DELETE FROM consumer_reviews 
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- 2. Add unique constraint to prevent future duplicates
-- This ensures only one review per consumer per product
DO $$
BEGIN
  -- Check if constraint already exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'consumer_reviews_phone_product_unique'
      AND table_name = 'consumer_reviews'
  ) THEN
    -- Add unique constraint for consumer_phone + product_id
    ALTER TABLE consumer_reviews 
    ADD CONSTRAINT consumer_reviews_phone_product_unique 
    UNIQUE (consumer_phone, product_id) 
    DEFERRABLE INITIALLY DEFERRED;
    
    RAISE NOTICE 'Added unique constraint on consumer_phone and product_id';
  ELSE
    RAISE NOTICE 'Unique constraint already exists';
  END IF;
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Could not add unique constraint: %', SQLERRM;
END $$;

-- 3. Add index for performance (if not already covered by unique constraint)
CREATE INDEX IF NOT EXISTS idx_consumer_reviews_phone_product 
ON consumer_reviews(consumer_phone, product_id);

-- 4. Add comment for documentation
COMMENT ON CONSTRAINT consumer_reviews_phone_product_unique ON consumer_reviews 
IS 'Ensures one review per consumer per product to prevent duplicate reviews';

-- 5. Update the existing constraint check to make title and review_text optional
-- Since we now allow reviews with only rating (no title or text required)
DO $$
BEGIN
  -- Drop the existing constraint that required title OR review_text
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'non_empty_review'
      AND table_name = 'consumer_reviews'
  ) THEN
    ALTER TABLE consumer_reviews DROP CONSTRAINT non_empty_review;
    RAISE NOTICE 'Removed non_empty_review constraint - title and text are now optional';
  END IF;
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Could not remove non_empty_review constraint: %', SQLERRM;
END $$;

-- 6. Add comment about optional fields
COMMENT ON COLUMN consumer_reviews.title IS 'Optional review title - can be NULL';
COMMENT ON COLUMN consumer_reviews.review_text IS 'Optional review text - can be NULL';
COMMENT ON COLUMN consumer_reviews.rating IS 'Required star rating from 1-5 - this is the only required field';
