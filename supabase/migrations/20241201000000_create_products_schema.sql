-- Migration: Create comprehensive products schema with account isolation
-- This migration creates all tables needed for the AROUZ MARKET parts library system

-- 1. Create ENUM types for better data integrity
CREATE TYPE product_status AS ENUM ('active', 'draft', 'pending_approval', 'out_of_stock', 'discontinued');
CREATE TYPE marketplace_section AS ENUM ('wholesale', 'retail');
CREATE TYPE season_type AS ENUM ('Summer', 'Winter', 'All-Season');

-- 2. Create products table with complete field mapping
CREATE TABLE IF NOT EXISTS products (
  -- Core Identity
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  sku TEXT NOT NULL,
  part_article_number TEXT,

  -- User Association (CRITICAL for account isolation)
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Categorization
  category TEXT NOT NULL,
  subcategory TEXT,
  description_and_specifications TEXT NOT NULL,

  -- Images (stored as URLs to Supabase Storage)
  primary_image TEXT,
  additional_images TEXT[] DEFAULT '{}',
  manufacturer_logo TEXT,

  -- Business Information
  manufacturer TEXT NOT NULL,
  supplier_name TEXT,
  stock_quantity INTEGER NOT NULL DEFAULT 0,

  -- Pricing & Orders
  retail_price DECIMAL(10,2),
  minimum_order_quantity INTEGER,
  quotation_request_enabled BOOLEAN DEFAULT FALSE,

  -- Certifications & Compliance
  certifications TEXT[] DEFAULT '{}',
  production_capacity INTEGER,

  -- Shipping & Logistics
  shipping_origin TEXT,
  estimated_lead_time TEXT,
  available_shipping_methods TEXT[] DEFAULT '{}',
  packaging_details TEXT,

  -- System Fields
  inventory_update_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status product_status NOT NULL DEFAULT 'draft',
  admin_notes TEXT,
  marketplace_section marketplace_section NOT NULL DEFAULT 'retail',

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Legacy compatibility fields
  images TEXT[] DEFAULT '{}',
  specifications JSONB DEFAULT '{}',
  compatible_vehicles TEXT[] DEFAULT '{}'
);

-- 3. Create wholesale pricing tiers table
CREATE TABLE IF NOT EXISTS wholesale_pricing_tiers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  min_quantity INTEGER NOT NULL,
  max_quantity INTEGER,
  price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure logical constraints
  CONSTRAINT valid_quantity_range CHECK (
    max_quantity IS NULL OR max_quantity >= min_quantity
  ),
  CONSTRAINT positive_values CHECK (
    min_quantity > 0 AND price > 0
  )
);

-- 4. Create tyre-specific specifications table
CREATE TABLE IF NOT EXISTS tyre_specifications (
  product_id TEXT PRIMARY KEY REFERENCES products(id) ON DELETE CASCADE,

  -- Tyre Specifications
  width INTEGER,
  aspect_ratio INTEGER,
  rim_diameter INTEGER,
  load_index INTEGER,
  speed_rating TEXT,

  -- Tyre Performance
  season season_type,
  tread_life TEXT,
  traction_rating TEXT,
  temperature_rating TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create vehicle compatibility table
CREATE TABLE IF NOT EXISTS vehicle_compatibility (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,

  -- Vehicle Information
  vehicle_id TEXT NOT NULL,
  vehicle_type TEXT NOT NULL,
  brand TEXT NOT NULL,
  model TEXT NOT NULL,
  generation TEXT,
  engine_type TEXT,
  display_name TEXT NOT NULL,

  -- Engine Details (stored as JSONB for flexibility)
  engine_details JSONB DEFAULT '{}',

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Prevent duplicate vehicle associations
  UNIQUE(product_id, vehicle_id)
);

-- 6. Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_marketplace_section ON products(marketplace_section);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_products_user_category ON products(user_id, category);
CREATE INDEX IF NOT EXISTS idx_products_user_status ON products(user_id, status);

CREATE INDEX IF NOT EXISTS idx_wholesale_pricing_product_id ON wholesale_pricing_tiers(product_id);
CREATE INDEX IF NOT EXISTS idx_tyre_specs_product_id ON tyre_specifications(product_id);
CREATE INDEX IF NOT EXISTS idx_vehicle_compat_product_id ON vehicle_compatibility(product_id);

-- 7. Enable Row Level Security (RLS) on all tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE wholesale_pricing_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE tyre_specifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicle_compatibility ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for complete account isolation

-- Products table policies
CREATE POLICY "Users can only see their own products" ON products
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can only insert their own products" ON products
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can only update their own products" ON products
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can only delete their own products" ON products
  FOR DELETE USING (auth.uid() = user_id);

-- Wholesale pricing tiers policies
CREATE POLICY "Users can only see pricing for their own products" ON wholesale_pricing_tiers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only insert pricing for their own products" ON wholesale_pricing_tiers
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only update pricing for their own products" ON wholesale_pricing_tiers
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only delete pricing for their own products" ON wholesale_pricing_tiers
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.user_id = auth.uid()
    )
  );

-- Tyre specifications policies
CREATE POLICY "Users can only see specs for their own products" ON tyre_specifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only insert specs for their own products" ON tyre_specifications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only update specs for their own products" ON tyre_specifications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only delete specs for their own products" ON tyre_specifications
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.user_id = auth.uid()
    )
  );

-- Vehicle compatibility policies
CREATE POLICY "Users can only see compatibility for their own products" ON vehicle_compatibility
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only insert compatibility for their own products" ON vehicle_compatibility
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only update compatibility for their own products" ON vehicle_compatibility
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can only delete compatibility for their own products" ON vehicle_compatibility
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.user_id = auth.uid()
    )
  );

-- 9. Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Create triggers for automatic timestamp updates
CREATE TRIGGER update_products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tyre_specs_updated_at
  BEFORE UPDATE ON tyre_specifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 11. Create function to validate product data integrity
CREATE OR REPLACE FUNCTION validate_product_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Ensure marketplace_section matches user role
  -- This will be enforced at the application level, but we can add basic validation here

  -- Ensure tyre products have tyre specifications if category is 'tyres'
  IF NEW.category = 'tyres' AND TG_OP = 'INSERT' THEN
    -- This will be handled by the application layer
    NULL;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Create trigger for data validation
CREATE TRIGGER validate_product_data_trigger
  BEFORE INSERT OR UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION validate_product_data();

-- 13. Add comments for documentation
COMMENT ON TABLE products IS 'Main products table with complete account isolation via RLS';
COMMENT ON TABLE wholesale_pricing_tiers IS 'Wholesale pricing tiers for supplier products';
COMMENT ON TABLE tyre_specifications IS 'Tyre-specific technical specifications';
COMMENT ON TABLE vehicle_compatibility IS 'Vehicle compatibility information for products';

COMMENT ON COLUMN products.user_id IS 'CRITICAL: Links product to user account for complete isolation';
COMMENT ON COLUMN products.marketplace_section IS 'Determines if product appears in wholesale or retail marketplace';
COMMENT ON COLUMN products.id IS 'Product ID with role-based suffix (e.g., TYR-100001-WHOLESALE)';
