-- Migration: Add custom config function for shipping company authentication
-- This function allows setting PostgreSQL configuration variables for RLS policies

-- Create custom set_app_config function for shipping company context
CREATE OR REPLACE FUNCTION set_app_config(
  setting_name TEXT,
  setting_value TEXT,
  is_local BOOLEAN DEFAULT false
)
R<PERSON><PERSON>NS TEXT AS $$
BEGIN
  -- Set the configuration variable using the built-in set_config function
  PERFORM set_config(setting_name, setting_value, is_local);

  -- Return the value that was set
  RETURN setting_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION set_app_config(TEXT, TEXT, BOOLEAN) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION set_app_config(TEXT, TEXT, BOOLEAN) IS
'Sets PostgreSQL configuration variables for RLS policies. Used by shipping company authentication to set app.shipping_company_code.';
