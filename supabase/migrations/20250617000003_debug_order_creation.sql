-- Migration: Debug order creation with detailed error logging
-- This migration temporarily disables <PERSON><PERSON> and adds comprehensive debugging

-- 1. TEMPORARILY DISABLE RLS ON ORDERS TABLES FOR DEBUGGING
ALTER TABLE orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE order_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE shipments DISABLE ROW LEVEL SECURITY;

-- 2. Create a debug function to test order creation step by step
CREATE OR REPLACE FUNCTION debug_order_creation(
  p_consumer_phone VARCHAR(20),
  p_consumer_name VARCHAR(100),
  p_delivery_address TEXT,
  p_delivery_wilaya VARCHAR(100),
  p_delivery_commune VARCHAR(100),
  p_cart_items JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_order_id UUID;
  v_item JSONB;
  v_debug_info JSONB := '{}';
  v_step_counter INTEGER := 0;
BEGIN
  -- Step 1: Log authentication info
  v_step_counter := v_step_counter + 1;
  v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
    jsonb_build_object(
      'step', 'auth_check',
      'auth_uid', auth.uid(),
      'auth_role', auth.role(),
      'current_user', current_user,
      'session_user', session_user
    )
  );

  -- Step 2: Try to create order
  v_step_counter := v_step_counter + 1;
  BEGIN
    INSERT INTO orders (
      consumer_phone,
      consumer_name,
      delivery_address,
      delivery_wilaya,
      delivery_commune,
      status,
      total_amount,
      created_at
    ) VALUES (
      p_consumer_phone,
      p_consumer_name,
      p_delivery_address,
      p_delivery_wilaya,
      p_delivery_commune,
      'pending',
      0, -- Will be updated later
      NOW()
    ) RETURNING id INTO v_order_id;

    v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
      jsonb_build_object(
        'step', 'order_creation',
        'success', true,
        'order_id', v_order_id
      )
    );
  EXCEPTION WHEN OTHERS THEN
    v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
      jsonb_build_object(
        'step', 'order_creation',
        'success', false,
        'error_code', SQLSTATE,
        'error_message', SQLERRM,
        'error_detail', SQLSTATE || ': ' || SQLERRM
      )
    );
    RETURN v_debug_info;
  END;

  -- Step 3: Try to create order items
  v_step_counter := v_step_counter + 1;
  BEGIN
    FOR v_item IN SELECT * FROM jsonb_array_elements(p_cart_items)
    LOOP
      INSERT INTO order_items (
        order_id,
        product_id,
        product_name,
        quantity,
        unit_price,
        total_price,
        supplier_account_id,
        supplier_name
      ) VALUES (
        v_order_id,
        v_item->>'product_id',
        v_item->>'product_name',
        (v_item->>'quantity')::INTEGER,
        (v_item->>'unit_price')::DECIMAL,
        (v_item->>'total_price')::DECIMAL,
        (v_item->>'supplier_account_id')::UUID,
        v_item->>'supplier_name'
      );
    END LOOP;

    v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
      jsonb_build_object(
        'step', 'order_items_creation',
        'success', true,
        'items_count', jsonb_array_length(p_cart_items)
      )
    );
  EXCEPTION WHEN OTHERS THEN
    v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
      jsonb_build_object(
        'step', 'order_items_creation',
        'success', false,
        'error_code', SQLSTATE,
        'error_message', SQLERRM
      )
    );
    RETURN v_debug_info;
  END;

  -- Step 4: Try to create shipment
  v_step_counter := v_step_counter + 1;
  BEGIN
    INSERT INTO shipments (
      order_id,
      status,
      arouz_shipping_fee,
      created_at
    ) VALUES (
      v_order_id,
      'unassigned',
      15.00,
      NOW()
    );

    v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
      jsonb_build_object(
        'step', 'shipment_creation',
        'success', true
      )
    );
  EXCEPTION WHEN OTHERS THEN
    v_debug_info := jsonb_set(v_debug_info, ARRAY['step_' || v_step_counter], 
      jsonb_build_object(
        'step', 'shipment_creation',
        'success', false,
        'error_code', SQLSTATE,
        'error_message', SQLERRM
      )
    );
    RETURN v_debug_info;
  END;

  -- Step 5: Success
  v_debug_info := jsonb_set(v_debug_info, ARRAY['final_result'], 
    jsonb_build_object(
      'success', true,
      'order_id', v_order_id,
      'message', 'Order created successfully'
    )
  );

  RETURN v_debug_info;
END;
$$;

-- 3. Grant execute permission
GRANT EXECUTE ON FUNCTION debug_order_creation TO authenticated, anon;

-- 4. Add comment
COMMENT ON FUNCTION debug_order_creation IS 'Debug function to test order creation step by step with detailed error reporting';
