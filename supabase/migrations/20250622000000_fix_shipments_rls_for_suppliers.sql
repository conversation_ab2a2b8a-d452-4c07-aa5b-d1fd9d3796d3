-- Fix shipments RLS policies to allow suppliers to assign orders to shipping companies
-- This migration specifically addresses the "new row violates row-level security policy" error

-- 1. Drop any conflicting shipments policies
DROP POLICY IF EXISTS "shipments_allow_insert" ON shipments;
DROP POLICY IF EXISTS "shipments_allow_select" ON shipments;
DROP POLICY IF EXISTS "shipments_supplier_update" ON shipments;
DROP POLICY IF EXISTS "shipments_shipping_company_update" ON shipments;
DROP POLICY IF EXISTS "Shipments access for consumers" ON shipments;
DROP POLICY IF EXISTS "Shipments access for suppliers" ON shipments;
DROP POLICY IF EXISTS "Shipments insert for order creation" ON shipments;
DROP POLICY IF EXISTS "Shipping companies can update their shipments" ON shipments;
DROP POLICY IF EXISTS "Ad<PERSON> can view all shipments" ON shipments;
DROP POLICY IF EXISTS "Ad<PERSON> can manage shipments" ON shipments;

-- 2. Create comprehensive shipments policies

-- Allow consumers to view shipments for their orders
CREATE POLICY "shipments_consumer_select" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM orders o
      JOIN profiles p ON p.phone = o.consumer_phone
      WHERE o.id = shipments.order_id
      AND p.role = 'consumer'
      AND p.id = auth.uid()
    )
  );

-- Allow suppliers/merchants to view shipments for their orders
CREATE POLICY "shipments_supplier_select" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = shipments.order_id
      AND oi.supplier_account_id = auth.uid()
    )
  );

-- Allow shipping companies to view their assigned shipments
CREATE POLICY "shipments_shipping_company_select" ON shipments
  FOR SELECT USING (
    shipping_company_id IN (
      SELECT id FROM shipping_companies 
      WHERE login_code = current_setting('app.shipping_company_code', true)
    )
  );

-- CRITICAL: Allow authenticated users to insert shipments (for order creation and assignment)
CREATE POLICY "shipments_authenticated_insert" ON shipments
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
  );

-- Allow suppliers/merchants to update shipments for their orders (assignment)
CREATE POLICY "shipments_supplier_update" ON shipments
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = shipments.order_id
      AND oi.supplier_account_id = auth.uid()
    )
  );

-- Allow shipping companies to update their assigned shipments
CREATE POLICY "shipments_shipping_company_update" ON shipments
  FOR UPDATE USING (
    shipping_company_id IN (
      SELECT id FROM shipping_companies 
      WHERE login_code = current_setting('app.shipping_company_code', true)
    )
  );

-- Allow admins to manage all shipments
CREATE POLICY "shipments_admin_all" ON shipments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- 3. Add helpful comments
COMMENT ON POLICY "shipments_authenticated_insert" ON shipments IS 'Allows any authenticated user to create shipments - needed for order creation and supplier assignment';
COMMENT ON POLICY "shipments_supplier_update" ON shipments IS 'Allows suppliers/merchants to assign their orders to shipping companies';
COMMENT ON POLICY "shipments_shipping_company_update" ON shipments IS 'Allows shipping companies to update their assigned shipments';
