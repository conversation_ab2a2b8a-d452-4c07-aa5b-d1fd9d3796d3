-- Migration: Update profiles table for multi-role authentication
-- This migration modifies the existing profiles table to support all user roles

-- First, create a backup of the existing profiles table
CREATE TABLE IF NOT EXISTS profiles_backup AS SELECT * FROM profiles;

-- Add missing columns to the profiles table
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS store_address TEXT,
ADD COLUMN IF NOT EXISTS avatar_url TEXT,
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;

-- Set default values for existing records
UPDATE profiles
SET email_verified = FALSE
WHERE email_verified IS NULL;

-- Add a check constraint to the role column
-- First, ensure all existing roles are valid
UPDATE profiles
SET role = 'consumer'
WHERE role IS NULL OR role NOT IN ('supplier', 'merchant', 'consumer', 'distribution');

-- Then add the constraint
ALTER TABLE profiles
DROP CONSTRAINT IF EXISTS profiles_role_check;

ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('supplier', 'merchant', 'consumer', 'distribution'));

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for row-level security
-- Policy for users to view their own profile
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
CREATE POLICY "Users can view their own profile"
  ON profiles
  FOR SELECT
  USING (auth.uid() = id::uuid);

-- Policy for users to update their own profile
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
CREATE POLICY "Users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (auth.uid() = id::uuid);

-- Policy for inserting new profiles (needed for sign-up)
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
CREATE POLICY "Users can insert their own profile"
  ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id::uuid);

-- Create or update the function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id,
    email,
    role,
    created_at,
    updated_at,
    email_verified
  )
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'role', 'consumer'),
    now(),
    now(),
    false
  )
  ON CONFLICT (id) DO NOTHING;

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation if it doesn't exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create index for faster lookups if it doesn't exist
CREATE INDEX IF NOT EXISTS profiles_role_idx ON profiles(role);

-- Add comments for documentation
COMMENT ON TABLE profiles IS 'User profiles for AROUZ MARKET with multi-role support';
COMMENT ON COLUMN profiles.role IS 'User role: supplier, merchant, consumer, or distribution';
COMMENT ON COLUMN profiles.email_verified IS 'Whether the user has verified their email address';
COMMENT ON COLUMN profiles.last_login IS 'Timestamp of the user''s last login';
