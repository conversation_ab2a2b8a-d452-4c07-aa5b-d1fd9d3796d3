-- Migration: Create Comprehensive Orders & Shipments System
-- This creates the complete checkout and shipment flow for AROUZ MARKET
-- 🎯 CRITICAL: Unified database structure for maximum efficiency and scalable growth

-- 1. Create ENUM types for better data integrity (with IF NOT EXISTS protection)
DO $$ BEGIN
    CREATE TYPE order_status AS ENUM ('pending', 'confirmed', 'shipped', 'delivered', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payment_method AS ENUM ('cash_on_delivery', 'store_pickup');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE shipment_status AS ENUM ('unassigned', 'assigned', 'picked_up', 'in_transit', 'delivered');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE marketplace_section AS ENUM ('wholesale', 'retail');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Create orders table (main order management)
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Consumer Information
  consumer_phone VARCHAR(20) NOT NULL,
  consumer_name TEXT,
  order_number VARCHAR(50) UNIQUE NOT NULL,
  
  -- Order Status & Payment
  status order_status DEFAULT 'pending',
  payment_method payment_method NOT NULL,
  
  -- Delivery Information
  delivery_address TEXT NOT NULL,
  delivery_coordinates POINT, -- lat, lng from Google Maps
  delivery_wilaya TEXT NOT NULL,
  google_maps_url TEXT NOT NULL,
  
  -- Financial Information
  subtotal DECIMAL(10,2) NOT NULL,
  total_shipping_cost DECIMAL(10,2) DEFAULT 0,
  total_arouz_fees DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  
  -- Metadata
  special_instructions TEXT,
  estimated_delivery_date DATE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  confirmed_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  CONSTRAINT positive_amounts CHECK (
    subtotal > 0 AND 
    total_shipping_cost >= 0 AND 
    total_arouz_fees >= 0 AND 
    total_amount > 0
  ),
  CONSTRAINT valid_phone CHECK (consumer_phone ~ '^\+213[0-9]{9}$')
);

-- 3. Create order_items table (products in each order)
CREATE TABLE IF NOT EXISTS order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  
  -- Product Information
  product_id VARCHAR(100) NOT NULL,
  product_name VARCHAR(255) NOT NULL,
  product_image TEXT,
  
  -- Quantity & Pricing
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  
  -- Supplier Information
  supplier_account_id UUID, -- Links to supplier profile
  supplier_name TEXT NOT NULL,
  supplier_phone VARCHAR(20), -- CRITICAL: Supplier contact phone for shipping companies
  supplier_city VARCHAR(100),
  supplier_wilaya VARCHAR(100),
  
  -- Product Classification
  marketplace_section marketplace_section NOT NULL,
  category TEXT,
  subcategory TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT positive_values CHECK (
    quantity > 0 AND 
    unit_price > 0 AND 
    total_price > 0
  ),
  CONSTRAINT correct_total CHECK (total_price = unit_price * quantity)
);

-- 4. Create shipping_companies table (partner management)
CREATE TABLE IF NOT EXISTS shipping_companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Company Information
  company_name VARCHAR(100) UNIQUE NOT NULL,
  contact_email VARCHAR(255),
  contact_phone VARCHAR(20),
  website_url TEXT,
  
  -- Access Control
  login_code VARCHAR(50) UNIQUE NOT NULL, -- Secret access code
  
  -- Service Coverage
  coverage_areas TEXT[] DEFAULT '{}', -- Array of wilayas they serve
  service_types TEXT[] DEFAULT '{}', -- home_delivery, bureau_pickup, express
  
  -- Business Information
  commission_rate DECIMAL(5,2) DEFAULT 0.00, -- Percentage commission
  minimum_order_value DECIMAL(10,2) DEFAULT 0,
  maximum_weight_kg DECIMAL(8,2),
  
  -- Status & Performance
  is_active BOOLEAN DEFAULT true,
  total_orders_completed INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0.00,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  CONSTRAINT valid_commission CHECK (commission_rate >= 0 AND commission_rate <= 100),
  CONSTRAINT valid_rating CHECK (average_rating >= 0 AND average_rating <= 5)
);

-- 5. Create shipments table (handles delivery logistics)
CREATE TABLE IF NOT EXISTS shipments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  
  -- Shipping Company Assignment
  shipping_company_id UUID REFERENCES shipping_companies(id),
  company_name VARCHAR(100),
  assigned_by TEXT DEFAULT 'unassigned', -- 'company', 'arouz_market', 'unassigned'
  
  -- Shipment Status
  status shipment_status DEFAULT 'unassigned',
  
  -- Financial Information
  delivery_cost DECIMAL(10,2),
  arouz_shipping_fee DECIMAL(10,2) DEFAULT 15.00, -- Fixed AROUZ fee per shipment
  total_shipping_cost DECIMAL(10,2),
  
  -- Tracking Information
  tracking_number VARCHAR(100),
  tracking_url TEXT,
  support_phone VARCHAR(20),
  
  -- Driver & Vehicle Information
  driver_name VARCHAR(100),
  driver_phone VARCHAR(20),
  vehicle_type VARCHAR(50),
  vehicle_capacity VARCHAR(50),
  
  -- Delivery Scheduling
  estimated_delivery_date DATE,
  pickup_time_window VARCHAR(50),
  delivery_time_window VARCHAR(50),
  
  -- Special Instructions
  special_instructions TEXT,
  handling_instructions TEXT,
  
  -- Order Items in this Shipment
  order_item_ids UUID[] NOT NULL DEFAULT '{}',

  -- Supplier Contact Information (CRITICAL for shipping companies)
  supplier_contact_info JSONB, -- Array of {supplier_name, supplier_phone, supplier_city, supplier_wilaya}
  
  -- Timestamps
  assigned_at TIMESTAMP WITH TIME ZONE,
  picked_up_at TIMESTAMP WITH TIME ZONE,
  in_transit_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT positive_costs CHECK (
    delivery_cost >= 0 AND 
    arouz_shipping_fee >= 0 AND 
    total_shipping_cost >= 0
  ),
  CONSTRAINT valid_assignment CHECK (
    (shipping_company_id IS NOT NULL AND company_name IS NOT NULL) OR
    (shipping_company_id IS NULL AND status = 'unassigned')
  )
);

-- 6. Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_orders_consumer_phone ON orders(consumer_phone);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_wilaya ON orders(delivery_wilaya);

CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_supplier ON order_items(supplier_account_id);
CREATE INDEX IF NOT EXISTS idx_order_items_marketplace_section ON order_items(marketplace_section);

CREATE INDEX IF NOT EXISTS idx_shipments_order_id ON shipments(order_id);
CREATE INDEX IF NOT EXISTS idx_shipments_company_id ON shipments(shipping_company_id);
CREATE INDEX IF NOT EXISTS idx_shipments_status ON shipments(status);
CREATE INDEX IF NOT EXISTS idx_shipments_tracking ON shipments(tracking_number);

CREATE INDEX IF NOT EXISTS idx_shipping_companies_active ON shipping_companies(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_shipping_companies_login_code ON shipping_companies(login_code);

-- 7. Enable Row Level Security (RLS) on all tables
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_companies ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for secure access

-- Orders table policies
CREATE POLICY "Consumers can view their own orders" ON orders
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE phone = orders.consumer_phone
      AND role = 'consumer'
      AND id = auth.uid()
    )
  );

CREATE POLICY "Suppliers can view orders containing their products" ON orders
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM order_items oi
      JOIN profiles p ON p.id = oi.supplier_account_id
      WHERE oi.order_id = orders.id
      AND p.id = auth.uid()
      AND p.role IN ('supplier', 'merchant')
    )
  );

CREATE POLICY "Shipping companies can view assigned orders" ON orders
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM shipments s
      JOIN shipping_companies sc ON sc.id = s.shipping_company_id
      WHERE s.order_id = orders.id
      AND sc.login_code = current_setting('app.shipping_company_code', true)
    )
  );

-- CRITICAL: Allow order creation for authenticated users
CREATE POLICY "Allow order creation" ON orders
  FOR INSERT WITH CHECK (true);

-- Order items policies - FIXED: No circular dependency
CREATE POLICY "Order items access for consumers" ON order_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.phone = (SELECT consumer_phone FROM orders WHERE id = order_items.order_id)
      AND p.role = 'consumer'
      AND p.id = auth.uid()
    )
  );

CREATE POLICY "Order items access for suppliers" ON order_items
  FOR SELECT USING (
    supplier_account_id = auth.uid()
  );

CREATE POLICY "Order items insert for order creation" ON order_items
  FOR INSERT WITH CHECK (true);

-- Shipments policies - FIXED: No circular dependency
CREATE POLICY "Shipments access for consumers" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.phone = (SELECT consumer_phone FROM orders WHERE id = shipments.order_id)
      AND p.role = 'consumer'
      AND p.id = auth.uid()
    )
  );

CREATE POLICY "Shipments access for suppliers" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = shipments.order_id
      AND oi.supplier_account_id = auth.uid()
    )
  );

CREATE POLICY "Shipments insert for order creation" ON shipments
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Shipping companies can update their shipments" ON shipments
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM shipping_companies sc
      WHERE sc.id = shipments.shipping_company_id
      AND sc.login_code = current_setting('app.shipping_company_code', true)
    )
  );

-- Shipping companies policies
CREATE POLICY "Active shipping companies are visible" ON shipping_companies
  FOR SELECT USING (is_active = true);

-- 9. Create database functions for order management

-- Function to generate unique order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
  order_num TEXT;
  counter INTEGER;
BEGIN
  -- Generate order number: ARZ-YYYYMMDD-NNNN
  SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 'ARZ-[0-9]{8}-([0-9]{4})') AS INTEGER)), 0) + 1
  INTO counter
  FROM orders
  WHERE order_number LIKE 'ARZ-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-%';

  order_num := 'ARZ-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0');

  RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate order totals
CREATE OR REPLACE FUNCTION calculate_order_totals(order_id_param UUID)
RETURNS TABLE(
  subtotal DECIMAL(10,2),
  shipping_cost DECIMAL(10,2),
  arouz_fees DECIMAL(10,2),
  total_amount DECIMAL(10,2)
) AS $$
DECLARE
  calc_subtotal DECIMAL(10,2);
  calc_shipping DECIMAL(10,2);
  calc_arouz_fees DECIMAL(10,2);
  calc_total DECIMAL(10,2);
BEGIN
  -- Calculate subtotal from order items
  SELECT COALESCE(SUM(oi.total_price), 0)
  INTO calc_subtotal
  FROM order_items oi
  WHERE oi.order_id = order_id_param;

  -- Calculate total shipping cost from shipments
  SELECT COALESCE(SUM(s.delivery_cost), 0)
  INTO calc_shipping
  FROM shipments s
  WHERE s.order_id = order_id_param;

  -- Calculate total AROUZ fees from shipments
  SELECT COALESCE(SUM(s.arouz_shipping_fee), 0)
  INTO calc_arouz_fees
  FROM shipments s
  WHERE s.order_id = order_id_param;

  -- Calculate total amount
  calc_total := calc_subtotal + calc_shipping + calc_arouz_fees;

  RETURN QUERY SELECT calc_subtotal, calc_shipping, calc_arouz_fees, calc_total;
END;
$$ LANGUAGE plpgsql;

-- Function to create order with items and initial shipment (UPDATED WITH SUPPLIER PHONE)
CREATE OR REPLACE FUNCTION create_complete_order(
  p_consumer_phone TEXT,
  p_payment_method TEXT,
  p_delivery_address TEXT,
  p_delivery_wilaya TEXT,
  p_google_maps_url TEXT,
  p_order_items JSONB,
  p_consumer_name TEXT DEFAULT NULL,
  p_delivery_coordinates TEXT DEFAULT NULL,
  p_special_instructions TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  new_order_id UUID;
  order_number TEXT;
  calc_subtotal DECIMAL(10,2) := 0;
  item JSONB;
  coord_point POINT;
  payment_method_enum payment_method;
BEGIN
  -- Convert text payment method to enum
  payment_method_enum := p_payment_method::payment_method;

  -- Convert text coordinates to POINT if provided
  IF p_delivery_coordinates IS NOT NULL AND p_delivery_coordinates != '' THEN
    coord_point := p_delivery_coordinates::POINT;
  ELSE
    coord_point := NULL;
  END IF;

  -- Generate unique order number
  order_number := 'ARZ-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD((EXTRACT(EPOCH FROM NOW()) % 10000)::TEXT, 4, '0');

  -- Create the main order
  INSERT INTO orders (
    order_number,
    consumer_phone,
    consumer_name,
    payment_method,
    delivery_address,
    delivery_coordinates,
    delivery_wilaya,
    google_maps_url,
    special_instructions,
    status
  ) VALUES (
    order_number,
    p_consumer_phone,
    p_consumer_name,
    payment_method_enum,
    p_delivery_address,
    coord_point,
    p_delivery_wilaya,
    p_google_maps_url,
    p_special_instructions,
    'pending'
  ) RETURNING id INTO new_order_id;

  -- Add order items WITH SUPPLIER PHONE LOOKUP
  FOR item IN SELECT * FROM jsonb_array_elements(p_order_items)
  LOOP
    INSERT INTO order_items (
      order_id,
      product_id,
      product_name,
      product_image,
      quantity,
      unit_price,
      total_price,
      supplier_account_id,
      supplier_name,
      supplier_phone,
      supplier_city,
      supplier_wilaya,
      marketplace_section,
      category,
      subcategory
    ) VALUES (
      new_order_id,
      item->>'product_id',
      item->>'product_name',
      item->>'product_image',
      (item->>'quantity')::INTEGER,
      (item->>'unit_price')::DECIMAL(10,2),
      (item->>'total_price')::DECIMAL(10,2),
      CASE WHEN item->>'supplier_account_id' IS NOT NULL AND item->>'supplier_account_id' != '' AND item->>'supplier_account_id' != 'null' THEN (item->>'supplier_account_id')::UUID ELSE NULL END,
      item->>'supplier_name',
      -- CRITICAL: Get supplier phone from profiles table for shipping company
      COALESCE(
        (SELECT phone FROM profiles WHERE id = (item->>'supplier_account_id')::UUID AND role IN ('supplier', 'merchant')),
        item->>'supplier_phone'
      ),
      item->>'supplier_city',
      item->>'supplier_wilaya',
      (item->>'marketplace_section')::marketplace_section,
      item->>'category',
      item->>'subcategory'
    );

    calc_subtotal := calc_subtotal + (item->>'total_price')::DECIMAL(10,2);
  END LOOP;

  -- Update order totals
  UPDATE orders
  SET subtotal = calc_subtotal,
      total_amount = calc_subtotal
  WHERE id = new_order_id;

  -- Create initial unassigned shipment WITH SUPPLIER CONTACT INFO
  INSERT INTO shipments (
    order_id,
    order_item_ids,
    supplier_contact_info
  )
  SELECT
    new_order_id,
    ARRAY_AGG(oi.id),
    -- CRITICAL: Aggregate supplier contact info for shipping companies
    jsonb_agg(DISTINCT jsonb_build_object(
      'supplier_name', oi.supplier_name,
      'supplier_phone', oi.supplier_phone,
      'supplier_city', oi.supplier_city,
      'supplier_wilaya', oi.supplier_wilaya,
      'supplier_account_id', oi.supplier_account_id
    )) FILTER (WHERE oi.supplier_phone IS NOT NULL)
  FROM order_items oi
  WHERE oi.order_id = new_order_id;

  RETURN new_order_id;
END;
$$ LANGUAGE plpgsql;

-- 10. Insert partnered shipping companies with login codes
INSERT INTO shipping_companies (
  company_name,
  website_url,
  login_code,
  coverage_areas,
  service_types,
  is_active
) VALUES
  ('Yalidine Express', 'https://yalidine-express.com.dz', 'YAL_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Alger', 'Oran', 'Constantine', 'Annaba', 'Blida', 'Batna', 'Djelfa', 'Sétif', 'Sidi Bel Abbès', 'Biskra'],
   ARRAY['home_delivery', 'bureau_pickup'], true),

  ('ZR Express', 'https://zrexpress.com', 'ZR_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Alger', 'Oran', 'Tlemcen', 'Mostaganem', 'Chlef', 'Tiaret', 'Mascara', 'Relizane'],
   ARRAY['home_delivery', 'bureau_pickup'], true),

  ('Speed Mail', 'https://m.facebook.com/LIVRAISON.SPEED/', 'SPD_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Alger', 'Boumerdès', 'Tizi Ouzou', 'Béjaïa', 'Bouira', 'Médéa'],
   ARRAY['home_delivery', 'express'], true),

  ('World Express', 'https://www.worldexpresscourier.com/', 'WLD_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Constantine', 'Annaba', 'Guelma', 'Souk Ahras', 'Tébessa', 'Khenchela', 'Oum El Bouaghi'],
   ARRAY['home_delivery', 'bureau_pickup'], true),

  ('Godyma Express', 'https://godyma-express.com', 'GOD_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Oran', 'Sidi Bel Abbès', 'Tlemcen', 'Ain Témouchent', 'Mostaganem'],
   ARRAY['home_delivery', 'bureau_pickup'], true),

  ('NOEST Express', 'https://noest-dz.com', 'NOE_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Sétif', 'Bordj Bou Arréridj', 'Mila', 'Jijel', 'Béjaïa'],
   ARRAY['home_delivery', 'bureau_pickup'], true),

  ('Easy & Speed', 'https://www.easyandspeed.dz', 'EAS_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Alger', 'Blida', 'Tipaza', 'Boumerdès', 'Ain Defla'],
   ARRAY['home_delivery', 'bureau_pickup', 'express'], true),

  ('Tawsil Star', 'https://www.tawsilstar.dz', 'TAW_' || EXTRACT(EPOCH FROM NOW())::TEXT,
   ARRAY['Ouargla', 'Biskra', 'El Oued', 'Ghardaïa', 'Laghouat'],
   ARRAY['home_delivery', 'bureau_pickup'], true)
ON CONFLICT (company_name) DO NOTHING;

-- 11. Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shipments_updated_at BEFORE UPDATE ON shipments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shipping_companies_updated_at BEFORE UPDATE ON shipping_companies
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 12. Add helpful comments for documentation
COMMENT ON TABLE orders IS 'Main orders table for AROUZ MARKET checkout system with consumer phone authentication';
COMMENT ON TABLE order_items IS 'Individual products within each order, supporting multi-supplier orders';
COMMENT ON TABLE shipments IS 'Shipping logistics management with manual cost entry per order';
COMMENT ON TABLE shipping_companies IS 'Partnered delivery companies with secret login access';

COMMENT ON COLUMN orders.consumer_phone IS 'Links to profiles.phone for consumer identification (+213 format)';
COMMENT ON COLUMN orders.google_maps_url IS 'Official Google Maps URL for delivery address confirmation';
COMMENT ON COLUMN orders.order_number IS 'Unique order identifier: ARZ-YYYYMMDD-NNNN format';
COMMENT ON COLUMN order_items.supplier_phone IS 'CRITICAL: Supplier contact phone for shipping companies to coordinate pickup';
COMMENT ON COLUMN shipments.supplier_contact_info IS 'CRITICAL: Aggregated supplier contact information for shipping companies';
COMMENT ON COLUMN shipments.arouz_shipping_fee IS 'Fixed AROUZ MARKET fee per shipment (15 DZD default)';
COMMENT ON COLUMN shipments.delivery_cost IS 'Shipping company cost set manually per order';
COMMENT ON COLUMN shipping_companies.login_code IS 'Secret access code for shipping company portal authentication';
