# Supabase Setup for AROUZ MARKET

This document provides instructions for setting up Supabase for the AROUZ MARKET authentication system.

## Prerequisites

1. A Supabase account
2. Access to the Supabase project dashboard

## Setup Steps

### 1. Create a Supabase Project

If you haven't already, create a new Supabase project:

1. Go to [https://app.supabase.io/](https://app.supabase.io/)
2. Click "New Project"
3. Enter project details:
   - Name: AROUZ MARKET
   - Database Password: (create a secure password)
   - Region: Choose a region close to your users (e.g., eu-west-1 for Europe)
4. Click "Create new project"

### 2. Configure Authentication

1. In the Supabase dashboard, go to "Authentication" → "Settings"
2. Under "Email Auth", ensure it's enabled
3. Under "Phone Auth", enable it and configure:
   - Enable "SMS OTP"
   - Configure a Twilio account or other SMS provider
4. Set up redirect URLs:
   - Site URL: Your production URL (e.g., https://arouz-market.com)
   - Redirect URLs: Add your development and production URLs

### 3. Create Database Schema

1. Go to "SQL Editor" in the Supabase dashboard
2. Create a new query
3. Paste the contents of the `migrations/20231101000000_create_profiles_table.sql` file
4. Run the query to create the profiles table and related functions

### 4. Set Up Row Level Security (RLS)

The migration script already includes RLS policies, but verify they are active:

1. Go to "Table Editor" → "profiles"
2. Click "Policies"
3. Ensure the following policies exist:
   - "Users can view their own profile"
   - "Users can update their own profile"

### 5. Get API Keys

1. Go to "Settings" → "API"
2. Copy the "URL" and "anon/public" key
3. Add these to your `.env` file:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_anon_key
   VITE_USE_SUPABASE=true
   ```

## Testing Authentication

To test that authentication is working correctly:

1. Set `VITE_USE_SUPABASE=true` in your `.env` file
2. Run the application in development mode
3. Try to sign up as a Merchant or Supplier
4. Check the Supabase dashboard under "Authentication" → "Users" to see if the user was created
5. Check the "profiles" table to see if a profile was created for the user

## Troubleshooting

If you encounter issues:

1. Check the browser console for error messages
2. Verify your Supabase URL and anon key are correct
3. Ensure phone authentication is properly configured if using phone sign-in
4. Check the Supabase logs under "Database" → "Logs"

## Production Deployment

Before deploying to production:

1. Update the Site URL and redirect URLs in Supabase to your production domain
2. Set up proper email templates for authentication emails
3. Configure a production SMS provider for phone authentication
4. Set up proper CORS settings in Supabase
