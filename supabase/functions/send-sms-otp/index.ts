import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const DEXATEL_API_KEY = Deno.env.get('DEXATEL_API_KEY')
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
    const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!DEXATEL_API_KEY || !SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('Missing environment variables')
    }

    const { phone } = await req.json()
    console.log('📱 Received phone:', phone)

    if (!phone) {
      return new Response(JSON.stringify({ error: 'Phone required' }), {
        status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Format phone number for Dexatel (must be in international format)
    let formattedPhone = phone.toString().trim()

    // Remove any spaces, dashes, or parentheses
    formattedPhone = formattedPhone.replace(/[\s\-\(\)]/g, '')

    // If it starts with 0, replace with +213 (Algeria)
    if (formattedPhone.startsWith('0')) {
      formattedPhone = '+213' + formattedPhone.substring(1)
    }
    // If it starts with 213 but no +, add +
    else if (formattedPhone.startsWith('213') && !formattedPhone.startsWith('+')) {
      formattedPhone = '+' + formattedPhone
    }
    // If it doesn't start with +, assume it's Algerian and add +213
    else if (!formattedPhone.startsWith('+')) {
      formattedPhone = '+213' + formattedPhone
    }

    console.log('📱 Formatted phone for Dexatel:', formattedPhone)

    // Generate our own 6-digit OTP code
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString()
    console.log('🔢 Generated OTP code:', otpCode)

    // Send OTP via Dexatel messaging API (not verification API)
    console.log('📤 Calling Dexatel Messaging API with sender: AROUZ')
    const dexatelResponse = await fetch('https://api.dexatel.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Dexatel-Key': DEXATEL_API_KEY,
      },
      body: JSON.stringify({
        data: {
          from: "AROUZ",
          to: [formattedPhone],
          text: `Code: ${otpCode}`,
          channel: "sms"
        }
      })
    })

    if (!dexatelResponse.ok) {
      const errorText = await dexatelResponse.text()
      console.error('❌ Dexatel API error:', dexatelResponse.status, errorText)
      return new Response(JSON.stringify({
        error: `Dexatel error: ${dexatelResponse.status} - ${errorText}`
      }), {
        status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const dexatelData = await dexatelResponse.json()
    console.log('✅ Dexatel response:', JSON.stringify(dexatelData, null, 2))

    // Extract the message_id from Dexatel messaging response
    // Based on Dexatel docs, response should be: { data: [{ id: "...", ... }] }
    let messageId = null

    if (dexatelData.data && Array.isArray(dexatelData.data) && dexatelData.data.length > 0) {
      messageId = dexatelData.data[0].id
    } else if (dexatelData.data && dexatelData.data.id) {
      messageId = dexatelData.data.id
    } else if (dexatelData.id) {
      messageId = dexatelData.id
    }

    if (!messageId) {
      console.error('❌ No message_id in Dexatel response:', JSON.stringify(dexatelData, null, 2))
      return new Response(JSON.stringify({
        error: 'Invalid response from SMS service'
      }), {
        status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    console.log('✅ Message sent with ID:', messageId)

    // Get detailed message status from Dexatel
    console.log('🔍 Checking message delivery status...')
    try {
      const statusResponse = await fetch(`https://api.dexatel.com/v1/messages/${messageId}`, {
        method: 'GET',
        headers: {
          'X-Dexatel-Key': DEXATEL_API_KEY,
        }
      })

      if (statusResponse.ok) {
        const statusData = await statusResponse.json()
        console.log('📊 Message delivery status:', JSON.stringify(statusData, null, 2))
      } else {
        console.log('⚠️ Could not fetch delivery status:', statusResponse.status)
      }
    } catch (statusError) {
      console.log('⚠️ Status check error:', statusError.message)
    }

    // Store in Supabase
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000)

    await supabase.from('otp_verifications').insert({
      id: messageId,
      phone: formattedPhone,
      otp_code: otpCode,
      status: 'pending',
      expires_at: expiresAt.toISOString(),
      created_at: new Date().toISOString()
    })

    return new Response(JSON.stringify({
      success: true,
      data: {
        id: messageId,
        expires_at: expiresAt.toISOString()
      }
    }), {
      status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message || 'Internal server error'
    }), {
      status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/send-sms-otp' \
    --header 'Authorization: Bearer [YOUR_ANON_KEY]' \
    --header 'Content-Type: application/json' \
    --data '{"phone":"+213555123456"}'

*/
