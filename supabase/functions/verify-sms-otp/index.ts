/**
 * Supabase Edge Function: Verify SMS OTP locally
 *
 * This function handles verifying OTP codes against our database
 * No longer uses Dexatel verification API (which doesn't work)
 * Updated for local verification flow
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface VerifyOTPRequest {
  verificationId: string;
  code: string;
}

interface DexatelVerifyResponse {
  id: string;
  status: string;
  verified_at?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get environment variables
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
    const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('Supabase configuration missing')
    }

    // Parse request body
    const requestBody = await req.json()
    console.log('🔍 Raw request body received:', JSON.stringify(requestBody, null, 2))

    const { verificationId, code }: VerifyOTPRequest = requestBody

    console.log('🔍 Extracted values:', {
      verificationId: verificationId || 'MISSING',
      code: code || 'MISSING',
      verificationIdType: typeof verificationId,
      codeType: typeof code,
      verificationIdLength: verificationId?.length || 0,
      codeLength: code?.length || 0
    })

    if (!verificationId || !code) {
      console.error('❌ Missing required fields:', {
        hasVerificationId: !!verificationId,
        hasCode: !!code,
        verificationId: verificationId || 'UNDEFINED',
        code: code || 'UNDEFINED'
      })
      return new Response(
        JSON.stringify({ error: 'Verification ID and code are required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Validate code format
    if (!/^\d{6}$/.test(code)) {
      return new Response(
        JSON.stringify({ error: 'Invalid code format. Please enter a 6-digit code.' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('🔐 Verifying OTP locally against database:', {
      verificationId,
      code: '******'
    })

    // Create Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Get the OTP record from database
    const { data: otpRecord, error: fetchError } = await supabase
      .from('otp_verifications')
      .select('*')
      .eq('id', verificationId)
      .eq('status', 'pending')
      .single()

    if (fetchError || !otpRecord) {
      console.error('❌ OTP record not found or expired:', fetchError)
      return new Response(
        JSON.stringify({ error: 'Invalid or expired verification code' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Check if OTP has expired
    const now = new Date()
    const expiresAt = new Date(otpRecord.expires_at)
    if (now > expiresAt) {
      console.error('❌ OTP has expired:', { now, expiresAt })

      // Update status to expired
      await supabase
        .from('otp_verifications')
        .update({ status: 'expired' })
        .eq('id', verificationId)

      return new Response(
        JSON.stringify({ error: 'Verification code has expired' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Verify the OTP code
    if (otpRecord.otp_code !== code) {
      console.error('❌ Invalid OTP code provided')
      return new Response(
        JSON.stringify({ error: 'Invalid verification code' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // OTP is valid! Update status to verified
    console.log('🎉 OTP verified successfully:', { id: verificationId })

    const { error: updateError } = await supabase
      .from('otp_verifications')
      .update({
        status: 'verified',
        verified_at: new Date().toISOString()
      })
      .eq('id', verificationId)

    if (updateError) {
      console.error('❌ Failed to update OTP status:', updateError)
      return new Response(
        JSON.stringify({ error: 'Failed to update verification status' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          id: verificationId,
          status: 'verified',
          verified_at: new Date().toISOString()
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('🚨 Function error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/verify-sms-otp' \
    --header 'Authorization: Bearer [YOUR_ANON_KEY]' \
    --header 'Content-Type: application/json' \
    --data '{"verificationId":"your_verification_id","code":"123456"}'

*/
