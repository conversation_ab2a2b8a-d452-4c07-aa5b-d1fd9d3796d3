-- Test the trigger function directly to see if it works
-- This will help us identify if the issue is with the trigger or RLS policies

-- First, let's check if the trigger exists
SELECT 
  trigger_name,
  event_manipulation,
  event_object_table,
  action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- Check if the function exists
SELECT 
  routine_name,
  routine_type,
  routine_definition
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user';

-- Test the function directly (this simulates what happens when a user signs up)
-- We'll create a test user record and see if the trigger works
DO $$
DECLARE
  test_user_id UUID := gen_random_uuid();
  test_email TEXT := '<EMAIL>';
  test_metadata JSONB := '{"role": "supplier", "fullName": "Test User", "firstName": "Test", "lastName": "User", "companyName": "Test Company"}';
BEGIN
  -- Try to insert a test record into auth.users (this should trigger our function)
  -- Note: This is just a test, we'll clean it up
  
  RAISE NOTICE 'Testing trigger with user ID: %', test_user_id;
  RAISE NOTICE 'Test email: %', test_email;
  RAISE NOTICE 'Test metadata: %', test_metadata;
  
  -- Call the function directly to test it
  PERFORM public.handle_new_user();
  
  RAISE NOTICE 'Function call completed without errors';
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error testing trigger: % %', SQLSTATE, SQLERRM;
END $$;
