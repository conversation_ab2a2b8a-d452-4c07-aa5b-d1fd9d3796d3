#!/bin/bash

# 🚀 AROUZ MARKET Production Deployment Script
# This script deploys to production arouzmarket.com

set -e

echo "🚀 AROUZ MARKET Production Deployment"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Safety check
print_warning "PRODUCTION DEPLOYMENT - This will update arouzmarket.com"
read -p "Are you sure you want to deploy to PRODUCTION? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    print_info "Deployment cancelled"
    exit 0
fi

# Build production version
print_info "Building production version..."
npm run build:prod

print_status "Production build completed"

# Create deployment package
print_info "Creating production deployment package..."
cd dist
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
PACKAGE_NAME="arouz-prod-${TIMESTAMP}.zip"
zip -r "../${PACKAGE_NAME}" . > /dev/null 2>&1
cd ..

print_status "Production package created: ${PACKAGE_NAME}"

# Display deployment instructions
echo ""
print_info "PRODUCTION DEPLOYMENT INSTRUCTIONS:"
echo "1. 📦 Upload ${PACKAGE_NAME} to Octenium hosting"
echo "2. 📂 Extract to public_html/ directory"
echo "3. 🌐 arouzmarket.com will be updated immediately"
echo ""
print_warning "IMPORTANT: Test thoroughly on dev.arouzmarket.com first!"
echo ""
print_status "Production deployment package ready!"
echo "🌐 Once uploaded, your site will be live at: https://arouzmarket.com"
