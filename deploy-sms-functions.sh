#!/bin/bash

# Deploy SMS OTP Functions to Supabase
# This script deploys the Dexatel SMS functions and sets up environment variables

echo "🚀 Deploying SMS OTP Functions to Supabase..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if logged in to Supabase
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase. Please login first:"
    echo "supabase login"
    exit 1
fi

# Deploy the functions
echo "📦 Deploying send-sms-otp function..."
supabase functions deploy send-sms-otp

echo "📦 Deploying verify-sms-otp function..."
supabase functions deploy verify-sms-otp

# Set environment variables
echo "🔧 Setting up environment variables..."

# Get the Dexatel API key from .env file
if [ -f .env ]; then
    DEXATEL_API_KEY=$(grep VITE_DEXATEL_API_KEY .env | cut -d '=' -f2 | tr -d '"' | tr -d "'")
    if [ -n "$DEXATEL_API_KEY" ]; then
        echo "Setting DEXATEL_API_KEY..."
        supabase secrets set DEXATEL_API_KEY="$DEXATEL_API_KEY"
    else
        echo "⚠️  DEXATEL_API_KEY not found in .env file"
    fi
else
    echo "⚠️  .env file not found"
fi

# Set Supabase environment variables (these should already be available in the function environment)
echo "✅ Supabase environment variables (SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY) should be automatically available"

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Verify the functions are deployed: supabase functions list"
echo "2. Test the functions using the test page: http://localhost:8080/phone-auth-test"
echo "3. Check function logs: supabase functions logs send-sms-otp"
echo ""
echo "🔗 Function URLs:"
echo "Send OTP: https://[your-project-id].supabase.co/functions/v1/send-sms-otp"
echo "Verify OTP: https://[your-project-id].supabase.co/functions/v1/verify-sms-otp"
echo ""
echo "🔑 Make sure your Dexatel API key is set as a secret:"
echo "supabase secrets set DEXATEL_API_KEY='your_api_key_here'"
