-- EMERGENCY: Create the profiles table that's missing in production
-- This is why signup is failing - the table doesn't exist!

-- Drop the table if it exists to recreate it properly
DROP TABLE IF EXISTS public.profiles CASCADE;

-- Create the profiles table with all necessary columns
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  phone TEXT,
  role TEXT,
  company_name TEXT,
  store_name TEXT,
  tax_id TEXT,
  address TEXT,
  fleet_size INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  store_address TEXT,
  avatar_url TEXT,
  last_login TIMESTAMP WITH TIME ZONE,
  email_verified BOOLEAN DEFAULT false,
  full_name TEXT,
  phone_verified BOOLEAN DEFAULT false,
  phone_verified_at TIMESTAMP WITH TIME ZONE,
  verification_method TEXT DEFAULT 'dexatel_sms'::text,
  last_otp_sent_at TIMESTAMP WITH TIME ZONE,
  otp_attempts INTEGER DEFAULT 0,
  phone_verification_method TEXT,
  original_full_name CHARACTER VARYING
);

-- Enable RLS on the profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_insert_own_profile" ON profiles;

-- Create RLS policies
CREATE POLICY "service_role_all_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "users_select_own_profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "users_update_own_profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

CREATE POLICY "users_insert_own_profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Grant permissions
GRANT ALL ON profiles TO postgres;
GRANT ALL ON profiles TO service_role;
GRANT SELECT, UPDATE, INSERT ON profiles TO authenticated;
GRANT SELECT ON profiles TO anon;

-- Create the trigger function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Extract role from the user metadata
  user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');
  
  -- Insert the new profile
  INSERT INTO public.profiles (
    id,
    email,
    phone,
    role,
    full_name,
    first_name,
    last_name,
    company_name,
    store_name,
    store_address,
    email_verified,
    phone_verified,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.email,
    -- Format phone number if it exists
    CASE
      WHEN new.phone IS NOT NULL AND new.phone != '' THEN
        CASE
          WHEN new.phone LIKE '+213%' THEN new.phone
          WHEN new.phone LIKE '0%' THEN '+213' || SUBSTRING(new.phone FROM 2)
          ELSE '+213' || new.phone
        END
      ELSE NULL
    END,
    user_role,
    COALESCE(new.raw_user_meta_data->>'fullName', ''),
    COALESCE(new.raw_user_meta_data->>'firstName', ''),
    COALESCE(new.raw_user_meta_data->>'lastName', ''),
    CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
    false,
    false,
    now(),
    now()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    full_name = EXCLUDED.full_name,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    company_name = EXCLUDED.company_name,
    store_name = EXCLUDED.store_name,
    store_address = EXCLUDED.store_address,
    updated_at = now();

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Verify everything was created
SELECT 'Profiles table created successfully' as status;
SELECT 'Trigger created successfully' as status;
