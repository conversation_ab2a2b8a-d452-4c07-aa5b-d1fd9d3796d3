import json
import sys

try:
    with open('src/services/data/cars.json', 'r') as f:
        content = f.read()
    
    # Check the character at position 728635
    print(f"Character at position 728635: '{content[728634]}'")
    print(f"Character at position 728634: '{content[728633]}'")
    print(f"Character at position 728633: '{content[728632]}'")
    
    # Fix the specific issue by replacing the problematic character
    fixed_content = content[:728634] + ',' + content[728634:]
    
    # Write the fixed content back to the file
    with open('src/services/data/cars.json', 'w') as f:
        f.write(fixed_content)
    
    print("\nFixed the JSON file by inserting a comma at position 728635")
    
except Exception as e:
    print(f"Error: {e}")
