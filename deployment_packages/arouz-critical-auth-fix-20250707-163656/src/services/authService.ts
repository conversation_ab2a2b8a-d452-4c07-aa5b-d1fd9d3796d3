import { createClient, SupabaseClient, User, AuthResponse } from '@supabase/supabase-js';
import { UserRole } from '@/contexts/UserContext';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

// Log configuration for debugging - ENHANCED FOR PRODUCTION
console.log('🔐 [AUTH_SERVICE] Supabase Configuration:', {
  url: supabaseUrl,
  hasAnonKey: !!supabaseAnonKey,
  anonKeyLength: supabaseAnonKey?.length || 0,
  useSupabase: useSupabase,
  env: import.meta.env.MODE,
  isProduction: import.meta.env.VITE_APP_ENV === 'production',
  timestamp: new Date().toISOString()
});

// Additional production debugging
if (import.meta.env.VITE_APP_ENV === 'production') {
  console.log('🚨 [PRODUCTION_AUTH] Authentication service initializing in production mode');
  console.log('🚨 [PRODUCTION_AUTH] Supabase URL valid:', supabaseUrl.includes('supabase.co'));
  console.log('🚨 [PRODUCTION_AUTH] Anon key present:', !!supabaseAnonKey);
}

// Create a single instance of the Supabase client
export let supabase: SupabaseClient;

try {
  supabase = createClient(supabaseUrl, supabaseAnonKey);
  console.log('✅ [AUTH_SERVICE] Supabase auth client initialized successfully');

  // Test the connection in production
  if (import.meta.env.VITE_APP_ENV === 'production') {
    console.log('🚨 [PRODUCTION_AUTH] Testing Supabase connection...');
    supabase.auth.getSession().then(({ data, error }) => {
      if (error) {
        console.error('🚨 [PRODUCTION_AUTH] Connection test failed:', error);
      } else {
        console.log('✅ [PRODUCTION_AUTH] Supabase connection test successful');
      }
    });
  }
} catch (error) {
  console.error('❌ [AUTH_SERVICE] Failed to initialize Supabase auth client:', error);
  console.error('❌ [AUTH_SERVICE] URL:', supabaseUrl);
  console.error('❌ [AUTH_SERVICE] Key length:', supabaseAnonKey?.length || 0);

  // Create a dummy client that won't throw errors when methods are called
  supabase = createClient('https://placeholder.supabase.co', 'placeholder-key');
}

// Interface for user profile data
export interface UserProfile {
  id: string;
  email?: string;
  phone?: string;
  role: UserRole;
  full_name?: string;
  first_name?: string;
  last_name?: string;

  // Supplier-specific fields
  company_name?: string;

  // Merchant-specific fields
  store_name?: string;
  store_address?: string;

  // Additional fields
  tax_id?: string;
  address?: string;
  fleet_size?: number;
  avatar_url?: string;
  last_login?: string;
  email_verified?: boolean;
  phone_verified?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Interface for registration data
export interface RegistrationData {
  email: string;
  phone?: string; // Now optional
  password: string;
  role: UserRole;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  storeName?: string;
  storeAddress?: string;
}

/**
 * Check if an email is already associated with any account
 * @param email The email to check
 * @param currentRole Optional role to exclude from the check (useful for checking if email exists in other roles)
 * @returns Object containing exists flag, role if found, and any error
 */
export const checkEmailExists = async (email: string, currentRole?: UserRole): Promise<{
  exists: boolean;
  role?: UserRole;
  error: Error | null
}> => {
  try {
    console.log('Checking if email exists:', email, currentRole ? `(excluding ${currentRole} role)` : '');

    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating email check for:', email);
      // For testing, you can return true for specific test emails
      if (email.includes('existing@')) {
        return { exists: true, role: 'supplier', error: null };
      }
      return { exists: false, error: null };
    }

    // APPROACH 1: Skip the auth check and go directly to the profiles table
    // This is more reliable since we're primarily concerned with verified accounts
    console.log('Skipping auth check and going directly to profiles table check');
    let emailExists = false;

    // APPROACH 2: Check in profiles table to get the role and verification status
    // This is the primary method to determine if an email exists
    console.log('Checking if email exists in profiles table');
    let query = supabase
      .from('profiles')
      .select('id, email, role, email_verified');

    // Filter by email
    query = query.eq('email', email);

    // If currentRole is provided, exclude it from the check
    if (currentRole) {
      query = query.neq('role', currentRole);
    }

    const { data: profiles, error: profilesError } = await query;

    if (profilesError) {
      console.error('Error checking profiles table:', profilesError);
      // Return false on error to avoid false positives
      return { exists: false, error: profilesError };
    } else if (profiles && profiles.length > 0) {
      // Found a profile with this email in a different role
      const profile = profiles[0];
      console.log('Found profile for email:', email, 'with role:', profile.role);

      // Check if the profile is verified
      if (profile.email_verified) {
        console.log('Email is verified and associated with role:', profile.role);
        return { exists: true, role: profile.role as UserRole, error: null };
      } else {
        console.log('Email exists but is not verified, associated with role:', profile.role);
        // Even if not verified, we still consider it exists to prevent duplicate registrations
        return { exists: true, role: profile.role as UserRole, error: null };
      }
    }

    // APPROACH 3: Check if the user has a session
    // This is useful to check if the user is currently logged in
    console.log('Checking if user has a session with this email');
    const { data: session, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error checking session:', sessionError);
    } else if (session?.session?.user?.email === email) {
      console.log('User is currently logged in with this email:', email);

      // Try to get the role from the session metadata
      const role = session.session?.user?.user_metadata?.role as UserRole;
      if (role && role !== currentRole) {
        return { exists: true, role, error: null };
      }

      return { exists: true, error: null };
    }

    // If we get here, the email doesn't exist in profiles or session
    console.log('Email not found in profiles or session:', email);
    return { exists: false, error: null };
  } catch (error) {
    console.error('Error checking if email exists:', error);
    return { exists: false, error: error as Error };
  }
};

/**
 * Sign up a new user with email and password (phone is optional)
 */
export const signUpWithPhone = async (data: RegistrationData): Promise<{ user: User | null; error: Error | null }> => {
  try {
    console.log('signUpWithPhone called with data:', {
      email: data.email,
      role: data.role,
      phone: data.phone || 'Not provided',
      fullName: data.fullName,
      hasCompanyName: !!data.companyName,
      hasStoreName: !!data.storeName,
      hasStoreAddress: !!data.storeAddress
    });

    // First, check if the email already exists in any role
    console.log('Checking if email already exists before signup:', data.email);
    const { exists, role: existingRole, error: existsError } = await checkEmailExists(data.email);

    if (existsError) {
      console.error('Error checking if email exists:', existsError);
      // Continue with signup attempt even if check fails
      console.log('Continuing with signup despite email check error');
    } else if (exists) {
      console.log('Email already exists:', data.email, existingRole ? `with role: ${existingRole}` : '');

      // If the email exists with a different role, show a specific error message
      if (existingRole && existingRole !== data.role) {
        return {
          user: null,
          error: new Error(`This email is already registered as a ${existingRole === 'supplier' ? 'Supplier & Manufacturer' : 'Merchant Retailer'}. Please use a different email address or login with the correct role.`)
        };
      } else {
        // Generic message if the email exists but we don't know the role
        return {
          user: null,
          error: new Error('This email is already registered. Please use the login option instead.')
        };
      }
    } else {
      console.log('Email check passed, proceeding with signup:', data.email);
    }

    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // Only use development mode if explicitly disabled AND not in production testing
    const isProductionTesting = import.meta.env.VITE_APP_ENV === 'production-testing';
    if (import.meta.env.DEV && !useSupabase && !isProductionTesting) {
      console.log('DEV MODE: Simulating successful signup for:', data);

      // Use provided firstName and lastName if available, otherwise split fullName
      let firstName = data.firstName || '';
      let lastName = data.lastName || '';

      // If firstName/lastName not provided but fullName is, split it
      if ((!firstName || !lastName) && data.fullName) {
        const nameParts = data.fullName.split(' ');
        firstName = firstName || nameParts[0] || '';
        lastName = lastName || nameParts.slice(1).join(' ') || '';
      }

      // Create a fake user object for development testing
      const fakeUser = {
        id: 'dev-user-' + Date.now(),
        email: data.email,
        phone: data.phone, // This might be undefined
        app_metadata: { provider: 'email' },
        user_metadata: {
          role: data.role,
          phone: data.phone, // This might be undefined
          email: data.email,
          first_name: firstName,
          last_name: lastName,
          ...(data.role === 'supplier' ? { company_name: data.companyName } : {}),
          ...(data.role === 'merchant' ? {
            store_name: data.storeName,
            store_address: data.storeAddress,
            address: data.storeAddress
          } : {})
        },
        aud: 'authenticated',
        created_at: new Date().toISOString()
      } as unknown as User;

      // Simulate a delay to mimic network latency
      await new Promise(resolve => setTimeout(resolve, 1000));

      return { user: fakeUser, error: null };
    }

    console.log('Using real Supabase integration for signup');

    // Format the phone number if provided
    let formattedPhone = undefined;
    if (data.phone) {
      formattedPhone = formatPhoneForSupabase(data.phone);
      console.log('Formatted phone number:', formattedPhone);
    } else {
      console.log('No phone number provided');
    }

    // Sign up the user with Supabase Auth
    console.log('Calling supabase.auth.signUp...');
    let authData: { user: User };
    try {
      // Log the request data for debugging
      console.log('Signup request data:', {
        email: data.email,
        options: {
          data: {
            role: data.role,
            phone: formattedPhone ? 'provided' : 'not provided',
            full_name: data.fullName,
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        }
      });

      const result = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            // Common fields
            role: data.role,
            phone: formattedPhone,
            fullName: data.fullName,
            firstName: data.firstName,
            lastName: data.lastName,

            // Role-specific fields
            ...(data.role === 'supplier' ? {
              companyName: data.companyName
            } : {}),

            ...(data.role === 'merchant' ? {
              storeName: data.storeName,
              storeAddress: data.storeAddress
            } : {})
          },
          // Remove email confirmation requirement for production
          emailRedirectTo: undefined,
        },
      });

      console.log('Supabase signup response:', result);

      if (result.error) {
        console.error('Error signing up user:', result.error);

        // Check if the error is due to the email already being in use
        if (result.error.message.includes('already registered') ||
            result.error.message.includes('already in use') ||
            result.error.message.includes('already exists') ||
            result.error.message.includes('User already registered')) {

          console.log('Email already exists according to Supabase error:', data.email);

          // Double-check if the email exists in the profiles table
          const { exists, role: existingRole } = await checkEmailExists(data.email);
          console.log('Double-check result:', { exists, existingRole });

          if (exists) {
            // If we found the email in profiles, return a specific message
            console.log('Email is verified, returning appropriate error message');
            if (existingRole) {
              return {
                user: null,
                error: new Error(`This email is already registered as a ${existingRole === 'supplier' ? 'Supplier & Manufacturer' : 'Merchant Retailer'}. Please use a different email address or login with the correct role.`)
              };
            } else {
              return {
                user: null,
                error: new Error('This email is already registered. Please use the login option instead.')
              };
            }
          } else {
            // If the email is not in profiles but Supabase says it exists,
            // it might be a race condition or an unverified account
            console.log('Email exists in auth but not in profiles, returning generic error');
            return {
              user: null,
              error: new Error('There was an issue with this email address. Please try a different one or contact support.')
            };
          }
        } else if (result.error.message.includes('rate limit') || result.error.message.includes('too many')) {
          // DISABLED: Rate limiting completely removed - allow unlimited attempts
          console.log('Rate limit error detected but ignoring:', result.error.message);
          // Continue with normal signup flow instead of blocking
        } else if (result.error.message.includes('invalid') || result.error.message.includes('format')) {
          // Handle format errors
          console.log('Format error detected:', result.error.message);
          return {
            user: null,
            error: new Error('Please check your email and password format and try again.')
          };
        } else {
          // For any other error, provide a user-friendly message
          console.log('Supabase signup error (other):', result.error.message);
          return {
            user: null,
            error: new Error('Registration failed. Please try again or contact support if the problem persists.')
          };
        }
      }

      authData = result.data;

      // For production, we skip email confirmation and treat user as immediately confirmed
      console.log('User signup successful, proceeding without email confirmation:', authData.user?.id);

      console.log('Auth signup successful, user created:', authData.user?.id);

      if (!authData.user) {
        console.error('No user returned from auth.signUp');
        return { user: null, error: new Error('User creation failed') };
      }
    } catch (signupError) {
      console.error('Exception during signup:', signupError);
      return { user: null, error: signupError as Error };
    }

    // The profile will be created automatically by the database trigger
    console.log('Profile will be created by the database trigger for user:', authData.user.id);

    // Use provided firstName and lastName if available, otherwise split fullName
    let firstName = data.firstName || '';
    let lastName = data.lastName || '';

    // If firstName/lastName not provided but fullName is, split it
    if ((!firstName || !lastName) && data.fullName) {
      const nameParts = data.fullName.split(' ');
      firstName = firstName || nameParts[0] || '';
      lastName = lastName || nameParts.slice(1).join(' ') || '';
    }

    // CRITICAL FIX: Wait for profile creation synchronously instead of using setTimeout
    // This ensures the profile is fully created before returning from signup
    try {
      console.log('Waiting for profile creation and updating with comprehensive data for user:', authData.user.id);

      // Wait for the database trigger to create the profile (with retries)
      let profileExists = false;
      let retryCount = 0;
      const maxRetries = 10;

      while (!profileExists && retryCount < maxRetries) {
        const { data: existingProfile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', authData.user.id)
          .single();

        if (!profileError && existingProfile) {
          profileExists = true;
          console.log('Profile found after', retryCount, 'retries:', {
            id: existingProfile.id,
            email: existingProfile.email,
            role: existingProfile.role,
            has_full_name: !!existingProfile.full_name
          });
        } else {
          retryCount++;
          console.log(`Profile not found yet, retry ${retryCount}/${maxRetries}...`);
          // Wait 200ms before retrying
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      if (!profileExists) {
        console.error('Profile was not created by database trigger after', maxRetries, 'retries');
        return { user: null, error: new Error('Profile creation failed. Please try again.') };
      }
      // Now update the profile with comprehensive data
      console.log('Updating profile with comprehensive data for user:', authData.user.id);

      // Prepare comprehensive profile data
      const profileData = {
        // Common fields for all roles
        first_name: firstName,
        last_name: lastName,
        full_name: data.fullName, // Always include, Supabase will ignore if column doesn't exist
        last_login: new Date().toISOString(),

        // Make sure email is set correctly
        email: data.email,

        // Set phone if provided
        ...(formattedPhone ? { phone: formattedPhone } : {}),

        // Role-specific fields
        ...(data.role === 'supplier' ? {
          company_name: data.companyName,
          // Clear any merchant-specific fields that might exist
          store_name: null,
          store_address: null,
        } : {}),

        ...(data.role === 'merchant' ? {
          store_name: data.storeName,
          store_address: data.storeAddress,
          address: data.storeAddress, // Use store address as the default address
          // Clear any supplier-specific fields that might exist
          company_name: null,
        } : {})
      };

      console.log('Updating profile with comprehensive data:', profileData);
      const { error: updateError } = await supabase.from('profiles').update(profileData).eq('id', authData.user.id);

      if (updateError) {
        console.error('Error updating profile with additional data:', updateError);
        return { user: null, error: new Error('Profile update failed. Please try again.') };
      } else {
        console.log('Profile updated successfully, verifying data integrity');

        // Verify that all profile data was correctly stored
        try {
          const verificationResult = await verifyProfileDataIntegrity(authData.user.id, data);
          if (!verificationResult.success) {
            console.error('Profile data integrity verification failed:', verificationResult.issues);
            return { user: null, error: new Error('Profile verification failed. Please try again.') };
          } else {
            console.log('Profile data integrity verified successfully');
          }
        } catch (verifyError) {
          console.error('Error during profile data verification:', verifyError);
          return { user: null, error: new Error('Profile verification failed. Please try again.') };
        }
      }
    } catch (error) {
      console.error('Error updating profile with additional data:', error);
      return { user: null, error: new Error('Profile creation failed. Please try again.') };
    }

    console.log('Profile created and verified successfully');
    return { user: authData.user, error: null };
  } catch (error) {
    console.error('Unexpected error during signup:', error);
    return { user: null, error: error as Error };
  }
};

/**
 * Sign in a user with phone and OTP
 */
export const signInWithPhone = async (phone: string): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating successful OTP send to:', phone);
      return { success: true, error: null };
    }

    console.log('Using real Supabase integration for OTP send');

    const formattedPhone = formatPhoneForSupabase(phone);

    const { error } = await supabase.auth.signInWithOtp({
      phone: formattedPhone,
    });

    if (error) {
      console.error('Error sending OTP:', error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Unexpected error during sign in:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Sign in a user with email and password
 */
export const signInWithPassword = async (email: string, password: string, role?: UserRole): Promise<{ user: User | null; error: Error | null }> => {
  try {
    console.log('Attempting to sign in with password for email:', email, role ? `with role: ${role}` : '');

    // If a role is specified, check for role conflicts using enhanced detection
    if (role) {
      console.log('Checking for role conflicts with enhanced detection');
      const conflictResult = await checkForConflictingRole(role, email);

      if (conflictResult.hasConflict && conflictResult.conflictType === 'email_role_mismatch') {
        console.log('Email role mismatch detected:', conflictResult.conflictingRole);
        return {
          user: null,
          error: new Error(conflictResult.errorMessage || 'Role mismatch detected')
        };
      }
    }

    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // Only use development mode if explicitly disabled AND not in production testing
    const isProductionTesting = import.meta.env.VITE_APP_ENV === 'production-testing';
    if (import.meta.env.DEV && !useSupabase && !isProductionTesting) {
      console.log('DEV MODE: Simulating successful login for:', email);

      // Create a fake user object for development testing
      const fakeUser = {
        id: 'dev-user-' + Date.now(),
        email: email,
        app_metadata: { provider: 'email' },
        user_metadata: {
          email: email,
          role: role || 'consumer', // Use the provided role or default to consumer
        },
        aud: 'authenticated',
        created_at: new Date().toISOString()
      } as unknown as User;

      // Simulate a delay to mimic network latency
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('DEV MODE: Login successful, returning fake user');
      return { user: fakeUser, error: null };
    }

    console.log('Using real Supabase integration for password login');

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Error signing in with password:', error);
      return { user: null, error };
    }

    if (!data.user) {
      console.error('No user returned from auth.signInWithPassword');
      return { user: null, error: new Error('Login failed') };
    }

    console.log('Login successful for user:', data.user.id);

    // If a role is specified, verify that the user has the correct role
    if (role) {
      console.log('Verifying user role matches requested role:', role);

      // Get the user's profile to check their role
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', data.user.id)
        .single();

      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        // Continue with login but log the error
      } else if (profileData && profileData.role !== role) {
        console.error('Role mismatch. User role:', profileData.role, 'Requested role:', role);

        // CRITICAL FIX: Use admin-only logout for role mismatch to preserve other sessions
        await supabase.auth.signOut();

        const roleNames = {
          supplier: 'Supplier & Manufacturer',
          merchant: 'Merchant Retailer'
        };

        return {
          user: null,
          error: new Error(`This email is registered as a ${roleNames[profileData.role as keyof typeof roleNames]} account. Please use the ${roleNames[profileData.role as keyof typeof roleNames]} login section.`)
        };
      }
    }

    try {
      // Update last login timestamp
      console.log('Updating last login timestamp for user:', data.user.id);
      const { error: updateError } = await supabase.from('profiles')
        .update({ last_login: new Date().toISOString() })
        .eq('id', data.user.id);

      if (updateError) {
        console.warn('Failed to update last login timestamp:', updateError);
      } else {
        console.log('Last login timestamp updated successfully');
      }
    } catch (updateError) {
      console.warn('Exception updating last login timestamp:', updateError);
      // Don't fail the login if this update fails
    }

    return { user: data.user, error: null };
  } catch (error) {
    console.error('Unexpected error during sign in with password:', error);
    return { user: null, error: error as Error };
  }
};

/**
 * Verify the OTP code sent to the user's phone
 */
export const verifyOtp = async (phone: string, otp: string): Promise<AuthResponse> => {
  const formattedPhone = formatPhoneForSupabase(phone);

  // Check if we should use the real Supabase integration
  const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

  // In development mode, allow any 6-digit code to work for testing
  if (import.meta.env.DEV && !useSupabase && otp.length === 6 && /^\d+$/.test(otp)) {
    console.log('DEV MODE: Bypassing OTP verification with code:', otp);

    // For testing purposes, we'll simulate a successful verification
    // This is only for development and should never be used in production
    return {
      data: {
        session: {
          access_token: 'fake-access-token-for-dev-mode',
          refresh_token: 'fake-refresh-token-for-dev-mode',
          expires_in: 3600,
          expires_at: new Date(Date.now() + 3600 * 1000).toISOString(),
          token_type: 'bearer',
          user: {
            id: 'fake-user-id-' + Date.now(),
            app_metadata: { provider: 'phone' },
            user_metadata: { phone: formattedPhone },
            aud: 'authenticated',
            created_at: new Date().toISOString(),
          }
        },
        user: {
          id: 'fake-user-id-' + Date.now(),
          app_metadata: { provider: 'phone' },
          user_metadata: { phone: formattedPhone },
          aud: 'authenticated',
          created_at: new Date().toISOString(),
        }
      },
      error: null
    } as AuthResponse;
  }

  console.log('Using real Supabase integration for OTP verification');

  // In production or if the code doesn't match our test criteria, use the real verification
  return await supabase.auth.verifyOtp({
    phone: formattedPhone,
    token: otp,
    type: 'sms',
  });
};

/**
 * Sign out the current user
 * @param role Optional role to sign out from. If not provided, signs out from all roles.
 */
export const signOut = async (role?: UserRole): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating successful sign out', role ? `for role: ${role}` : 'for all roles');

      if (role) {
        // Only clear the specified role
        if (role === 'supplier') {
          localStorage.setItem('supplierRole', 'false');
        } else if (role === 'merchant') {
          localStorage.setItem('merchantRole', 'false');
        }

        // If no roles are active after this, clear the current role
        const supplierActive = role !== 'supplier' && localStorage.getItem('supplierRole') === 'true';
        const merchantActive = role !== 'merchant' && localStorage.getItem('merchantRole') === 'true';

        if (!supplierActive && !merchantActive) {
          localStorage.removeItem('userRole');
        } else if (!supplierActive && merchantActive) {
          localStorage.setItem('userRole', 'merchant');
        } else if (supplierActive && !merchantActive) {
          localStorage.setItem('userRole', 'supplier');
        }
      } else {
        // Clear all roles
        localStorage.removeItem('dev_profiles');
        localStorage.removeItem('userRole');
        localStorage.setItem('supplierRole', 'false');
        localStorage.setItem('merchantRole', 'false');
      }

      return { success: true, error: null };
    }

    console.log('Using real Supabase integration for sign out');

    // In production, handle Supabase signout
    if (role) {
      // For specific role signout, we would need to implement a custom solution
      // that preserves other role sessions. This would require a more complex
      // implementation with Supabase, possibly involving custom claims or multiple tokens.

      // For now, we'll simulate this by updating local storage
      if (role === 'supplier') {
        localStorage.setItem('supplierRole', 'false');
      } else if (role === 'merchant') {
        localStorage.setItem('merchantRole', 'false');
      }

      return { success: true, error: null };
    } else {
      // Sign out completely
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Error signing out:', error);
        return { success: false, error };
      }

      // Clear ONLY admin role flags - DO NOT touch consumer or shipping company sessions
      localStorage.setItem('supplierRole', 'false');
      localStorage.setItem('merchantRole', 'false');

      // CRITICAL FIX: DO NOT clear consumer phone session during admin logout
      // Consumer authentication should remain independent and isolated
      // Only clear admin-specific session data

      return { success: true, error: null };
    }
  } catch (error) {
    console.error('Unexpected error during sign out:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * CRITICAL: Admin-only logout function that preserves consumer and shipping company sessions
 * This function ensures complete isolation between authentication systems
 */
export const signOutAdminOnly = async (role?: 'supplier' | 'merchant') => {
  try {
    console.log('🔐 [ADMIN_LOGOUT] Starting admin-only logout, preserving other sessions...');

    if (role) {
      // Role-specific logout - only clear that specific role
      if (role === 'supplier') {
        localStorage.setItem('supplierRole', 'false');
        console.log('✅ [ADMIN_LOGOUT] Supplier role cleared');
      } else if (role === 'merchant') {
        localStorage.setItem('merchantRole', 'false');
        console.log('✅ [ADMIN_LOGOUT] Merchant role cleared');
      }

      return { success: true, error: null };
    } else {
      // Complete admin logout - clear Supabase session and admin roles only
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('❌ [ADMIN_LOGOUT] Error signing out from Supabase:', error);
        return { success: false, error };
      }

      // Clear ONLY admin role flags
      localStorage.setItem('supplierRole', 'false');
      localStorage.setItem('merchantRole', 'false');

      // Clear admin-specific session data only
      localStorage.removeItem('lastActivity');
      localStorage.removeItem('rememberMe');

      console.log('✅ [ADMIN_LOGOUT] Admin session cleared, consumer and shipping sessions preserved');
      return { success: true, error: null };
    }
  } catch (error) {
    console.error('❌ [ADMIN_LOGOUT] Unexpected error during admin logout:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Get the current user session
 */
export const getCurrentSession = async () => {
  // CRITICAL FIX: Only return Supabase sessions for admin authentication
  // Consumer phone authentication should NOT interfere with admin authentication
  const supabaseSession = await supabase.auth.getSession();

  // Do NOT check for phone authentication sessions here
  // Phone authentication is handled separately by the consumer authentication system
  // This ensures complete isolation between admin and consumer authentication

  return supabaseSession;
};

/**
 * Get the current user
 */
/**
 * Check if an email is already used by a different role
 * @param email The email to check
 * @param role The role to exclude from the check
 * @returns Object containing exists flag, role if found, and any error
 */
export const checkEmailUsedByDifferentRole = async (email: string, role: UserRole): Promise<{
  exists: boolean;
  role?: UserRole;
  error: Error | null
}> => {
  try {
    console.log(`Checking if email ${email} is used by a role other than ${role}`);

    // Use the checkEmailExists function with the current role excluded
    const result = await checkEmailExists(email, role);

    if (result.exists && result.role && result.role !== role) {
      console.log(`Email ${email} is already used by role ${result.role}`);
      return { exists: true, role: result.role, error: null };
    }

    return { exists: false, error: null };
  } catch (error) {
    console.error('Error checking if email is used by different role:', error);
    return { exists: false, error: error as Error };
  }
};

export const getCurrentUser = async () => {
  // CRITICAL FIX: Only return Supabase users for admin authentication
  // Consumer phone authentication should NOT interfere with admin authentication
  const supabaseUser = await supabase.auth.getUser();

  // Do NOT check for phone authentication sessions here
  // Phone authentication is handled separately by the consumer authentication system
  // This ensures complete isolation between admin and consumer authentication

  return supabaseUser;
};

/**
 * Conflict types for enhanced role conflict detection
 */
export type ConflictType = 'email_role_mismatch' | 'session_conflict' | 'none';

/**
 * Enhanced role conflict detection result
 */
export interface RoleConflictResult {
  hasConflict: boolean;
  conflictType: ConflictType;
  conflictingRole?: UserRole;
  errorMessage?: string;
  userFriendlyMessage?: string;
}

/**
 * Check if there's a conflicting role active with enhanced detection and messaging
 * @param role The role being checked
 * @param email Optional email to check for conflicts across accounts
 * @returns Enhanced object containing conflict status, type, and user-friendly messages
 */
export const checkForConflictingRole = async (role: UserRole, email?: string): Promise<RoleConflictResult> => {
  // First check if the email is already used with a different role
  if (email) {
    console.log(`Checking if email ${email} is already used with a different role than ${role}`);
    const { exists, role: existingRole, error } = await checkEmailExists(email, role);

    if (error) {
      console.error('Error checking email for role conflicts:', error);
      // Continue with session conflict check even if email check fails
    } else if (exists && existingRole && existingRole !== role) {
      console.log(`Email ${email} is already used with role ${existingRole}`);

      const roleNames = {
        supplier: 'Supplier & Manufacturer',
        merchant: 'Merchant Retailer'
      };

      return {
        hasConflict: true,
        conflictType: 'email_role_mismatch',
        conflictingRole: existingRole,
        errorMessage: `This email is registered as a ${roleNames[existingRole as keyof typeof roleNames]}. Please use the ${roleNames[existingRole as keyof typeof roleNames]} login section.`,
        userFriendlyMessage: `This email is registered as a ${roleNames[existingRole as keyof typeof roleNames]} account. Please use the ${roleNames[existingRole as keyof typeof roleNames]} login section.`
      };
    }
  }

  // Check localStorage for active roles
  const supplierActive = localStorage.getItem('supplierRole') === 'true';
  const merchantActive = localStorage.getItem('merchantRole') === 'true';

  // If trying to log in as supplier but merchant is active
  if (role === 'supplier' && merchantActive) {
    return {
      hasConflict: true,
      conflictType: 'session_conflict',
      conflictingRole: 'merchant',
      errorMessage: 'You are currently logged in as a Merchant. Please logout from your Merchant account before logging in as a Supplier.',
      userFriendlyMessage: 'You are currently logged in as a Merchant Retailer. Please logout from your Merchant account before logging in as a Supplier & Manufacturer.'
    };
  }

  // If trying to log in as merchant but supplier is active
  if (role === 'merchant' && supplierActive) {
    return {
      hasConflict: true,
      conflictType: 'session_conflict',
      conflictingRole: 'supplier',
      errorMessage: 'You are currently logged in as a Supplier. Please logout from your Supplier account before logging in as a Merchant.',
      userFriendlyMessage: 'You are currently logged in as a Supplier & Manufacturer. Please logout from your Supplier account before logging in as a Merchant Retailer.'
    };
  }

  // No conflict
  return {
    hasConflict: false,
    conflictType: 'none'
  };
};

/**
 * Comprehensive authentication attempt handler with enhanced role conflict detection
 * @param email User email
 * @param password User password
 * @param role Target role for authentication
 * @returns Enhanced result with conflict information
 */
export const attemptAuthentication = async (
  email: string,
  password: string,
  role: UserRole
): Promise<{
  success: boolean;
  user?: User | null;
  error?: Error | null;
  conflictResult?: RoleConflictResult;
}> => {
  try {
    console.log(`Attempting authentication for ${email} as ${role}`);

    // First, perform comprehensive role conflict checking
    const conflictResult = await checkForConflictingRole(role, email);

    if (conflictResult.hasConflict) {
      console.log('Role conflict detected:', conflictResult);
      return {
        success: false,
        conflictResult,
        error: new Error(conflictResult.errorMessage || 'Role conflict detected')
      };
    }

    // No conflicts, proceed with normal authentication
    const { user, error } = await signInWithPassword(email, password, role);

    if (error) {
      return {
        success: false,
        error
      };
    }

    return {
      success: true,
      user
    };
  } catch (error) {
    console.error('Unexpected error during authentication attempt:', error);
    return {
      success: false,
      error: error as Error
    };
  }
};

/**
 * Get the user's profile data
 */
export const getUserProfile = async (userId: string): Promise<{ profile: UserProfile | null; error: Error | null }> => {
  try {
    console.log('Fetching profile for user:', userId);

    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Fetching profile for user:', userId);
      const profile = devProfiles[userId];

      if (profile) {
        return { profile, error: null };
      } else {
        console.log('DEV MODE: Profile not found for user:', userId);
        return { profile: null, error: new Error('Profile not found') };
      }
    }

    console.log('Using real Supabase integration for fetching user profile');

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return { profile: null, error };
    }

    // Log the retrieved profile data for verification
    console.log('Retrieved profile data:', {
      id: data.id,
      email: data.email,
      role: data.role,
      full_name: data.full_name,
      first_name: data.first_name,
      last_name: data.last_name,
      company_name: data.company_name,
      store_name: data.store_name,
      store_address: data.store_address,
      phone: data.phone,
      email_verified: data.email_verified,
      phone_verified: data.phone_verified,
      created_at: data.created_at,
      updated_at: data.updated_at
    });

    return { profile: data as UserProfile, error: null };
  } catch (error) {
    console.error('Unexpected error fetching profile:', error);
    return { profile: null, error: error as Error };
  }
};

/**
 * Update the user's profile data
 */
export const updateUserProfile = async (userId: string, data: Partial<UserProfile>): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Updating profile for user:', userId, data);

      if (!devProfiles[userId]) {
        devProfiles[userId] = {
          id: userId,
          role: data.role || 'consumer',
          ...data
        } as UserProfile;
      } else {
        devProfiles[userId] = {
          ...devProfiles[userId],
          ...data,
          updated_at: new Date().toISOString()
        };
      }

      return { success: true, error: null };
    }

    console.log('Using real Supabase integration for updating user profile');

    const { error } = await supabase
      .from('profiles')
      .update(data)
      .eq('id', userId);

    if (error) {
      console.error('Error updating user profile:', error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Unexpected error updating profile:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Format phone number for Supabase (ensure it has the correct international format)
 */
export const formatPhoneForSupabase = (phone?: string): string | undefined => {
  // If no phone is provided or it's an empty string, return undefined
  if (!phone || phone.trim() === '') {
    return undefined;
  }

  // Remove any non-digit characters except the plus sign at the beginning
  const cleanedPhone = phone.trim();
  const hasPlus = cleanedPhone.startsWith('+');
  const digitsOnly = cleanedPhone.replace(/\D/g, '');

  // If no digits remain, return undefined
  if (!digitsOnly) {
    return undefined;
  }

  // If it's just zeros (like "000" or "0"), return undefined
  if (/^0+$/.test(digitsOnly)) {
    return undefined;
  }

  // Log the phone formatting process for debugging
  console.log('Phone formatting:', {
    original: phone,
    cleaned: cleanedPhone,
    hasPlus,
    digitsOnly
  });

  // Ensure the phone number has the correct format for Algeria (+213)
  // If it already starts with the country code, use it as is
  if (digitsOnly.startsWith('213')) {
    return `+${digitsOnly}`;
  }

  // If it starts with a 0, remove it and add the country code
  if (digitsOnly.startsWith('0')) {
    return `+213${digitsOnly.substring(1)}`;
  }

  // Otherwise, add the country code
  return `+213${digitsOnly}`;
};

// Development mode helpers
const devProfiles: Record<string, UserProfile> = {};

// In development mode, we'll store profiles in memory
if (import.meta.env.DEV) {
  // Add a listener to localStorage to persist profiles between page reloads
  window.addEventListener('beforeunload', () => {
    localStorage.setItem('dev_profiles', JSON.stringify(devProfiles));
  });

  // Load profiles from localStorage on init
  const savedProfiles = localStorage.getItem('dev_profiles');
  if (savedProfiles) {
    try {
      const parsed = JSON.parse(savedProfiles);
      Object.assign(devProfiles, parsed);
      console.log('DEV MODE: Loaded profiles from localStorage:', Object.keys(devProfiles).length);
    } catch (e) {
      console.error('DEV MODE: Failed to parse profiles from localStorage');
    }
  }
}

// Email rate limiting storage (in production, use Redis)
const emailRateLimitStore = new Map<string, { attempts: number; windowStart: number; blocked: boolean; lastAttempt: number }>();

/**
 * Check email rate limit for password reset - DISABLED: Always allow unlimited attempts
 */
function checkEmailRateLimit(email: string): { allowed: boolean; remaining: number; resetTime?: number; waitTime?: number } {
  // DISABLED: Rate limiting completely removed - always allow unlimited attempts
  return { allowed: true, remaining: 999 };
}

/**
 * Send a password reset email with rate limiting
 */
export const sendPasswordResetEmail = async (email: string): Promise<{ success: boolean; error: Error | null; rateLimitInfo?: any }> => {
  try {
    // Check rate limiting first
    const rateLimit = checkEmailRateLimit(email);
    if (!rateLimit.allowed) {
      let errorMessage = '';

      if (rateLimit.waitTime) {
        errorMessage = `Please wait ${rateLimit.waitTime} seconds before requesting another reset email.`;
      } else if (rateLimit.resetTime) {
        const resetTime = new Date(rateLimit.resetTime);
        const minutes = Math.ceil((rateLimit.resetTime - Date.now()) / (60 * 1000));
        errorMessage = `Too many reset attempts. Please try again in ${minutes} minutes.`;
      } else {
        errorMessage = 'Too many reset attempts. Please try again later.';
      }

      return {
        success: false,
        error: new Error(errorMessage),
        rateLimitInfo: {
          remainingAttempts: rateLimit.remaining,
          resetTime: rateLimit.resetTime ? new Date(rateLimit.resetTime).toISOString() : undefined,
          waitTime: rateLimit.waitTime
        }
      };
    }

    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating password reset email sent to:', email);
      return {
        success: true,
        error: null,
        rateLimitInfo: {
          remainingAttempts: rateLimit.remaining
        }
      };
    }

    console.log('Using real Supabase integration for password reset');

    // Make sure the redirect URL is correct
    console.log('Sending password reset email with redirect to:', `${window.location.origin}/auth/reset-password`);
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    if (error) {
      console.error('Error sending password reset email:', error);

      // DISABLED: Rate limit error handling removed - continue with normal flow
      if (error.message.toLowerCase().includes('rate limit') || error.message.toLowerCase().includes('too many')) {
        console.log('Rate limit error detected but ignoring:', error.message);
        // Continue with normal flow instead of blocking
      }

      return { success: false, error };
    }

    return {
      success: true,
      error: null,
      rateLimitInfo: {
        remainingAttempts: rateLimit.remaining
      }
    };
  } catch (error) {
    console.error('Unexpected error sending password reset email:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Update user's password
 */
export const updatePassword = async (password: string): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating password update');
      return { success: true, error: null };
    }

    console.log('Using real Supabase integration for password update');

    const { error } = await supabase.auth.updateUser({
      password,
    });

    if (error) {
      console.error('Error updating password:', error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Unexpected error updating password:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Send email verification
 */
export const sendEmailVerification = async (email: string): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating email verification sent to:', email);
      return { success: true, error: null };
    }

    console.log('Using real Supabase integration for email verification');

    const { error } = await supabase.auth.resend({
      type: 'signup',
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/verify`,
      },
    });

    if (error) {
      console.error('Error sending email verification:', error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Unexpected error sending email verification:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Verify email verification token
 */
export const verifyEmailToken = async (token: string): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Check if we should use the real Supabase integration
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === 'true';

    // In development mode, we can still use the real Supabase if VITE_USE_SUPABASE is true
    if (import.meta.env.DEV && !useSupabase) {
      console.log('DEV MODE: Simulating email verification with token:', token);
      return { success: true, error: null };
    }

    console.log('Using real Supabase integration for email token verification');

    // This is handled automatically by Supabase when the user clicks the link in the email
    // We just need to update the email_verified flag in the profile
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      console.error('Error getting user:', userError);
      return { success: false, error: userError || new Error('User not found') };
    }

    const { error: updateError } = await supabase.from('profiles')
      .update({ email_verified: true })
      .eq('id', userData.user.id);

    if (updateError) {
      console.error('Error updating email_verified flag:', updateError);
      return { success: false, error: updateError };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Unexpected error verifying email:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Check if password meets strength requirements
 */
export const checkPasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
  score: number; // 0-4, with 4 being strongest
} => {
  const errors = [];
  let score = 0;

  // Check length
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  // Check for uppercase
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Check for lowercase
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Check for numbers
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Check for special characters
  if (!/[^A-Za-z0-9]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  return {
    isValid: errors.length === 0,
    errors,
    score: Math.min(4, score) // Cap score at 4
  };
};

/**
 * Verify that all profile data is correctly stored and can be retrieved
 * This function is used for testing purposes
 */
export const verifyProfileDataIntegrity = async (userId: string, registrationData: RegistrationData): Promise<{ success: boolean; issues: string[] }> => {
  try {
    console.log('Verifying profile data integrity for user:', userId);

    // Get the user's profile
    const { profile, error } = await getUserProfile(userId);

    if (error || !profile) {
      console.error('Error fetching profile for verification:', error);
      return {
        success: false,
        issues: [`Failed to fetch profile: ${error?.message || 'Unknown error'}`]
      };
    }

    // Check for missing or incorrect data
    const issues: string[] = [];

    // Check common fields
    if (!profile.full_name && registrationData.fullName) {
      issues.push('full_name is missing but was provided during registration');
    } else if (profile.full_name !== registrationData.fullName) {
      issues.push(`full_name mismatch: expected "${registrationData.fullName}", got "${profile.full_name}"`);
    }

    if (!profile.first_name && registrationData.firstName) {
      issues.push('first_name is missing but was provided during registration');
    } else if (profile.first_name !== registrationData.firstName) {
      issues.push(`first_name mismatch: expected "${registrationData.firstName}", got "${profile.first_name}"`);
    }

    if (!profile.last_name && registrationData.lastName) {
      issues.push('last_name is missing but was provided during registration');
    } else if (profile.last_name !== registrationData.lastName) {
      issues.push(`last_name mismatch: expected "${registrationData.lastName}", got "${profile.last_name}"`);
    }

    if (profile.email !== registrationData.email) {
      issues.push(`email mismatch: expected "${registrationData.email}", got "${profile.email}"`);
    }

    // Check role-specific fields
    if (registrationData.role === 'supplier') {
      if (!profile.company_name && registrationData.companyName) {
        issues.push('company_name is missing but was provided during registration');
      } else if (profile.company_name !== registrationData.companyName) {
        issues.push(`company_name mismatch: expected "${registrationData.companyName}", got "${profile.company_name}"`);
      }
    } else if (registrationData.role === 'merchant') {
      if (!profile.store_name && registrationData.storeName) {
        issues.push('store_name is missing but was provided during registration');
      } else if (profile.store_name !== registrationData.storeName) {
        issues.push(`store_name mismatch: expected "${registrationData.storeName}", got "${profile.store_name}"`);
      }

      if (!profile.store_address && registrationData.storeAddress) {
        issues.push('store_address is missing but was provided during registration');
      } else if (profile.store_address !== registrationData.storeAddress) {
        issues.push(`store_address mismatch: expected "${registrationData.storeAddress}", got "${profile.store_address}"`);
      }
    }

    // Check phone number if provided
    if (registrationData.phone && !profile.phone) {
      issues.push('phone is missing but was provided during registration');
    }

    // Log the verification results
    if (issues.length > 0) {
      console.error('Profile data integrity issues found:', issues);
      return { success: false, issues };
    } else {
      console.log('Profile data integrity verified successfully');
      return { success: true, issues: [] };
    }
  } catch (error) {
    console.error('Unexpected error during profile data verification:', error);
    return {
      success: false,
      issues: [`Unexpected error during verification: ${(error as Error).message || 'Unknown error'}`]
    };
  }
};

export default supabase;
