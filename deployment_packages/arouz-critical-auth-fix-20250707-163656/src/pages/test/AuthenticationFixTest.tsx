import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import { getCurrentUser, getCurrentSession } from '@/services/authService';
import { getCurrentConsumerSession, logoutConsumer } from '@/services/simplifiedConsumerAuth';
import { useAuth } from '@/contexts/AuthContext';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

export default function AuthenticationFixTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    try {
      // Test 1: Check admin authentication isolation
      console.log('🧪 Testing admin authentication isolation...');
      const adminUser = await getCurrentUser();
      const adminSession = await getCurrentSession();
      
      if (!adminUser.data.user && !adminSession.data.session) {
        results.push({
          name: 'Admin Authentication Isolation',
          status: 'pass',
          message: 'Admin authentication correctly returns null when no admin is logged in',
          details: 'getCurrentUser and getCurrentSession properly isolated from consumer auth'
        });
      } else {
        results.push({
          name: 'Admin Authentication Isolation',
          status: 'fail',
          message: 'Admin authentication incorrectly returning user/session data',
          details: `User: ${!!adminUser.data.user}, Session: ${!!adminSession.data.session}`
        });
      }

      // Test 2: Check consumer authentication independence
      console.log('🧪 Testing consumer authentication independence...');
      const consumerSession = getCurrentConsumerSession();
      
      if (consumerSession) {
        results.push({
          name: 'Consumer Authentication Independence',
          status: 'pass',
          message: 'Consumer authentication working independently',
          details: `Consumer: ${consumerSession.profile.full_name || 'Unknown'}`
        });
      } else {
        results.push({
          name: 'Consumer Authentication Independence',
          status: 'warning',
          message: 'No consumer session found (expected if no consumer is logged in)',
          details: 'This is normal if no consumer is currently authenticated'
        });
      }

      // Test 3: Check AuthContext state
      console.log('🧪 Testing AuthContext state...');
      results.push({
        name: 'AuthContext State',
        status: isAuthenticated ? 'warning' : 'pass',
        message: isAuthenticated 
          ? 'AuthContext shows authenticated (admin user logged in)' 
          : 'AuthContext correctly shows not authenticated',
        details: `User ID: ${user?.id || 'None'}, Email: ${user?.email || 'None'}`
      });

      // Test 4: Test consumer logout functionality
      console.log('🧪 Testing consumer logout functionality...');
      try {
        // This should not throw an error
        logoutConsumer();
        results.push({
          name: 'Consumer Logout Function',
          status: 'pass',
          message: 'Consumer logout function executes without errors',
          details: 'logoutConsumer() completed successfully'
        });
      } catch (error) {
        results.push({
          name: 'Consumer Logout Function',
          status: 'fail',
          message: 'Consumer logout function threw an error',
          details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }

    } catch (error) {
      results.push({
        name: 'Test Execution',
        status: 'fail',
        message: 'Error during test execution',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setTestResults(results);
    setIsRunning(false);

    // Show summary toast
    const passCount = results.filter(r => r.status === 'pass').length;
    const failCount = results.filter(r => r.status === 'fail').length;
    
    toast({
      title: "Authentication Tests Complete",
      description: `${passCount} passed, ${failCount} failed`,
      variant: failCount > 0 ? "destructive" : "default"
    });
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'fail':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'bg-green-100 text-green-800';
      case 'fail':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  useEffect(() => {
    // Run tests automatically on component mount
    runTests();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Authentication Fix Verification</h1>
        <p className="text-gray-600">Testing consumer logout and admin authentication isolation fixes</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Test Results
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              variant="outline"
              size="sm"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Re-run Tests
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <h3 className="font-semibold">{result.name}</h3>
                  </div>
                  <Badge className={getStatusColor(result.status)}>
                    {result.status.toUpperCase()}
                  </Badge>
                </div>
                <p className="text-gray-700 mb-2">{result.message}</p>
                {result.details && (
                  <p className="text-sm text-gray-500 bg-gray-50 p-2 rounded">
                    {result.details}
                  </p>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Expected Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Admin Authentication Isolation:</strong> Should PASS - No admin user/session when not logged in</p>
            <p><strong>Consumer Authentication Independence:</strong> Should PASS or WARNING - Consumer auth works separately</p>
            <p><strong>AuthContext State:</strong> Should PASS when no admin logged in</p>
            <p><strong>Consumer Logout Function:</strong> Should PASS - No errors when calling logoutConsumer()</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
