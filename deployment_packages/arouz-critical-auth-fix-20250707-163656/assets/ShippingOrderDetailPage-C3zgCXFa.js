import{q as Z,ag as q,a as M,r as d,j as e,K as _,B as i,A as O,P as S,m as E,a6 as U,C as g,k as u,Q as j,_ as z,l as v,a3 as K,a9 as F,D as Q,d as W,e as J,f as X,g as Y,L as o,I as b,T as $,h as ee,U as T,N as L,w as se}from"./index-DjNptb3k.js";import{S as ae,a as re,b as te,c as ie,d as f}from"./select-B_ZRrAqd.js";import{b as le,u as ne}from"./shippingCompanyOrderService-Bl2FeP-t.js";import{g as de,l as ce}from"./shippingCompanyAuthService-CmVuORbo.js";import{S as me}from"./square-pen-uwspbZPZ.js";import{P as oe}from"./phone-Bnuz4FGX.js";import{C as xe}from"./credit-card-BocuRT-P.js";import{<PERSON> as he}from"./external-link-CFrxpCj2.js";import{C as pe}from"./calendar-DQkywbJQ.js";function De(){const{orderId:x}=Z(),c=q(),{toast:h}=M(),[l,ge]=d.useState(de()),[t,A]=d.useState(null),[R,C]=d.useState(!0),[w,k]=d.useState(!1),[B,N]=d.useState(!1),[n,m]=d.useState({status:"",tracking_number:"",driver_name:"",driver_phone:"",estimated_delivery_date:"",notes:""});d.useEffect(()=>{if(!l){console.log("🚚 No valid session, redirecting to login..."),c("/shipping/login");return}console.log("🚚 Authenticated as:",l.company_name)},[l,c]);const D=async()=>{if(!(!x||!l))try{C(!0),console.log("🔍 [SHIPPING_ORDER_DETAIL] Loading order details for:",x);const s=await le(x);if(s.success&&s.order){A(s.order);const r=s.order.shipments?.[0];m({status:r?.status||"assigned",tracking_number:r?.tracking_number||"",driver_name:r?.driver_name||"",driver_phone:r?.driver_phone||"",estimated_delivery_date:r?.estimated_delivery_date||"",notes:r?.notes||""})}else h({title:"Failed to load order",description:s.error||"Please try again",variant:"destructive"}),c("/shipping/dashboard")}catch(s){console.error("❌ [SHIPPING_ORDER_DETAIL] Error loading order:",s),h({title:"Failed to load order",description:"Please try again",variant:"destructive"})}finally{C(!1)}},H=async()=>{if(!(!t||!l))try{k(!0),console.log("🔄 [SHIPPING_ORDER_DETAIL] Updating order status...",n);const s=await ne(t.id,n);s.success?(h({title:"Order Updated Successfully",description:"Order status and details have been updated."}),await D(),N(!1)):h({title:"Update Failed",description:s.error||"Failed to update order",variant:"destructive"})}catch(s){console.error("❌ [SHIPPING_ORDER_DETAIL] Error updating order:",s),h({title:"Update Failed",description:"Please try again",variant:"destructive"})}finally{k(!1)}},P=()=>{ce(),c("/shipping/login")};d.useEffect(()=>{l&&x&&D()},[l,x]);const y=s=>new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s),p=s=>new Date(s).toLocaleDateString("fr-DZ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),I=s=>{switch(s){case"assigned":return"secondary";case"picked_up":return"default";case"in_transit":return"default";case"delivered":return"default";case"failed_delivery":return"destructive";default:return"secondary"}},G=s=>{switch(s){case"assigned":return L;case"picked_up":return S;case"in_transit":return _;case"delivered":return T;case"failed_delivery":return se;default:return L}};if(R)return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:e.jsx("div",{className:"container mx-auto px-4 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-[#fa7b00] rounded-lg flex items-center justify-center",children:e.jsx(_,{className:"h-6 w-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:l?.company_name||"Shipping Portal"}),e.jsx("p",{className:"text-gray-600",children:"Order Details"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(i,{variant:"outline",onClick:()=>c("/shipping/dashboard"),children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),e.jsx(i,{variant:"outline",onClick:P,children:"Logout"})]})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"grid gap-6",children:[...Array(3)].map((s,r)=>e.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"},r))})})]});if(!t)return e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(S,{className:"h-8 w-8 text-red-600"})}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Order not found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"The order you're looking for doesn't exist or is not assigned to your company."}),e.jsx(i,{onClick:()=>c("/shipping/dashboard"),className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90",children:"Back to Dashboard"})]})});const a=t.shipments?.[0],V=G(a?.status||"assigned");return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:e.jsx("div",{className:"container mx-auto px-4 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-[#fa7b00] rounded-lg flex items-center justify-center",children:e.jsx(_,{className:"h-6 w-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:l?.company_name||"Shipping Portal"}),e.jsx("p",{className:"text-gray-600",children:"Order Details"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(i,{variant:"outline",onClick:()=>c("/shipping/dashboard"),children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),e.jsx(i,{variant:"outline",onClick:P,children:"Logout"})]})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[e.jsx(V,{className:"h-8 w-8 text-[#fa7b00]"}),t.order_number]}),e.jsxs("p",{className:"text-gray-600 mt-1",children:["Order placed on ",p(t.created_at)]})]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(E,{variant:I(a?.status||"assigned"),className:"text-sm px-3 py-1",children:(a?.status||"assigned").replace("_"," ").toUpperCase()}),e.jsxs(i,{onClick:()=>N(!0),className:"bg-[#fa7b00] hover:bg-[#e56b00] text-white",children:[e.jsx(me,{className:"h-4 w-4 mr-2"}),"Update Status"]}),e.jsxs(i,{onClick:()=>D(),variant:"outline",size:"sm",className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-4 w-4"}),"Refresh"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(g,{children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5"}),"Customer Information"]})}),e.jsx(v,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Name"}),e.jsx("p",{className:"font-medium text-gray-900",children:t.consumer_name||"Consumer"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(oe,{className:"h-4 w-4 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Phone"}),e.jsx("p",{className:"font-medium text-gray-900",children:t.consumer_phone})]})]})]})})]}),e.jsxs(g,{children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(K,{className:"h-5 w-5"}),"Delivery Information"]})}),e.jsxs(v,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Wilaya"}),e.jsx("p",{className:"font-medium text-gray-900",children:t.delivery_wilaya})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(xe,{className:"h-4 w-4 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("p",{className:"font-medium text-gray-900",children:t.payment_method==="cash_on_delivery"?"Cash on Delivery":"Store Pickup"})]})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full mt-2"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Address"}),e.jsx("p",{className:"font-medium text-gray-900 mt-1",children:t.delivery_address})]})]}),t.special_instructions&&e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full mt-2"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Special Instructions"}),e.jsx("p",{className:"font-medium text-gray-900 mt-1",children:t.special_instructions})]})]}),e.jsx("div",{className:"pt-2",children:e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>window.open(t.google_maps_url,"_blank"),className:"text-[#fa7b00] border-[#fa7b00] hover:bg-[#fa7b00] hover:text-white",children:[e.jsx(he,{className:"h-4 w-4 mr-2"}),"View on Google Maps"]})})]})]}),e.jsxs(g,{children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-5 w-5"}),"Order Items (",t.order_items?.length||0,")"]})}),e.jsxs(v,{children:[e.jsx("div",{className:"space-y-4",children:t.order_items?.map(s=>e.jsxs("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[e.jsx("img",{src:s.product_image||"/placeholder.svg",alt:s.product_name,className:"w-16 h-16 object-cover rounded-md"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"font-medium text-gray-900 truncate",children:s.product_name}),e.jsxs("p",{className:"text-sm text-gray-500",children:[s.quantity," × ",y(s.unit_price)]}),e.jsxs("p",{className:"text-xs text-gray-400",children:["Supplier: ",s.supplier_name," • ",s.supplier_wilaya]}),e.jsxs("p",{className:"text-xs text-gray-400",children:["Phone: ",s.supplier_phone]})]}),e.jsx("p",{className:"font-semibold text-lg",children:y(s.total_price)})]},s.id))}),e.jsx(F,{className:"my-4"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"font-semibold text-lg",children:"Total Order Value:"}),e.jsx("span",{className:"font-bold text-xl text-[#fa7b00]",children:y(t.total_amount)})]})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(g,{children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),"Shipment Details"]})}),e.jsxs(v,{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Status"}),e.jsx(E,{variant:I(a?.status||"assigned"),children:(a?.status||"assigned").replace("_"," ").toUpperCase()})]}),a?.tracking_number&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Tracking Number"}),e.jsx("span",{className:"font-medium",children:a.tracking_number})]}),a?.driver_name&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Driver"}),e.jsx("span",{className:"font-medium",children:a.driver_name})]}),a?.driver_phone&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Driver Phone"}),e.jsx("span",{className:"font-medium",children:a.driver_phone})]}),a?.estimated_delivery_date&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Est. Delivery"}),e.jsx("span",{className:"font-medium",children:new Date(a.estimated_delivery_date).toLocaleDateString("fr-DZ")})]}),e.jsx(F,{}),e.jsxs("div",{className:"flex justify-between font-semibold",children:[e.jsx("span",{children:"Delivery Fee"}),e.jsx("span",{children:y(a?.delivery_cost||500)})]})]})]}),e.jsxs(g,{children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(pe,{className:"h-5 w-5"}),"Order Timeline"]})}),e.jsx(v,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-3 h-3 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-sm",children:"Order Placed"}),e.jsx("p",{className:"text-xs text-gray-500",children:p(t.created_at)})]})]}),a?.assigned_at&&e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-sm",children:"Assigned to Shipping"}),e.jsx("p",{className:"text-xs text-gray-500",children:p(a.assigned_at)})]})]}),a?.picked_up_at&&e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-sm",children:"Picked Up"}),e.jsx("p",{className:"text-xs text-gray-500",children:p(a.picked_up_at)})]})]}),a?.delivered_at&&e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-sm",children:"Delivered"}),e.jsx("p",{className:"text-xs text-gray-500",children:p(a.delivered_at)})]})]})]})})]})]})]}),e.jsx(Q,{open:B,onOpenChange:N,children:e.jsxs(W,{className:"sm:max-w-[500px]",children:[e.jsxs(J,{children:[e.jsx(X,{children:"Update Order Status"}),e.jsx(Y,{children:"Update the shipment status and details for this order."})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"status",children:"Status"}),e.jsxs(ae,{value:n.status,onValueChange:s=>m(r=>({...r,status:s})),children:[e.jsx(re,{children:e.jsx(te,{placeholder:"Select status"})}),e.jsxs(ie,{children:[e.jsx(f,{value:"assigned",children:"Assigned"}),e.jsx(f,{value:"picked_up",children:"Picked Up"}),e.jsx(f,{value:"in_transit",children:"In Transit"}),e.jsx(f,{value:"delivered",children:"Delivered"}),e.jsx(f,{value:"failed_delivery",children:"Failed Delivery"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"tracking_number",children:"Tracking Number"}),e.jsx(b,{id:"tracking_number",value:n.tracking_number,onChange:s=>m(r=>({...r,tracking_number:s.target.value})),placeholder:"Enter tracking number"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"driver_name",children:"Driver Name"}),e.jsx(b,{id:"driver_name",value:n.driver_name,onChange:s=>m(r=>({...r,driver_name:s.target.value})),placeholder:"Enter driver name"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"driver_phone",children:"Driver Phone"}),e.jsx(b,{id:"driver_phone",value:n.driver_phone,onChange:s=>m(r=>({...r,driver_phone:s.target.value})),placeholder:"Enter driver phone"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"estimated_delivery_date",children:"Estimated Delivery Date"}),e.jsx(b,{id:"estimated_delivery_date",type:"date",value:n.estimated_delivery_date,onChange:s=>m(r=>({...r,estimated_delivery_date:s.target.value}))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(o,{htmlFor:"notes",children:"Notes"}),e.jsx($,{id:"notes",value:n.notes,onChange:s=>m(r=>({...r,notes:s.target.value})),placeholder:"Enter any additional notes...",rows:3})]})]}),e.jsxs(ee,{children:[e.jsx(i,{variant:"outline",onClick:()=>N(!1),disabled:w,children:"Cancel"}),e.jsx(i,{onClick:H,disabled:w,className:"bg-[#fa7b00] hover:bg-[#e56b00]",children:w?e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"h-4 w-4 mr-2 animate-spin"}),"Updating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Update Order"]})})]})]})})]})})]})}export{De as default};
