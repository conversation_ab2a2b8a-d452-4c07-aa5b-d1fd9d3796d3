import{c as V,r as x,Y as J,j as e,Z as fe,u as O,C as M,k as T,Q as B,_ as R,l as I,$ as C,B as w,O as ce,U as _,m as D,a0 as Y,a1 as Q,a2 as oe,a3 as q,L as E,a4 as ye,T as K,I as je,w as X,A as Z,K as P,a5 as W,N as de,a6 as Ne,P as me,S as be,a7 as he,a8 as ve,a9 as U,aa as we,ab as ke,H as _e,ac as se,ad as te,ae,z as re,af as ie,ag as Ce,a as Se,s as Ae,ah as Fe,ai as Ee}from"./index-DjNptb3k.js";import{P as H}from"./phone-Bnuz4FGX.js";import{E as xe}from"./external-link-CFrxpCj2.js";import{R as pe,a as ue}from"./radio-group-CTXzh65n.js";import{C as ee}from"./credit-card-BocuRT-P.js";import{c as ge,a as De}from"./orderService-D895sbV7.js";import{B as Pe}from"./building-2-BMEfI4Am.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=V("Banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=V("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=V("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);function Te({children:a,onAuthSuccess:c}){const[l,t]=x.useState(!1),{isAuthenticated:n}=J(),s=x.useCallback(()=>{if(n){console.log("🔒 User already authenticated, skipping modal");return}console.log("🔘 Triggering checkout authentication modal"),t(!0)},[n]),o=x.useCallback(()=>{console.log("❌ Closing checkout authentication modal"),t(!1)},[]),f=x.useCallback((d,p)=>{console.log("✅ Checkout authentication successful:",{user:d,action:p}),t(!1),c?.(d,p)},[c]);return e.jsxs(e.Fragment,{children:[a(s),e.jsx(fe,{isOpen:l,onClose:o,onSuccess:f})]})}function Be({onComplete:a,onBack:c}){const{t:l}=O(),{isAuthenticated:t,isLoading:n}=J(),[s,o]=x.useState(null),f=async()=>{try{const p=localStorage.getItem("phone_auth_session");if(!p){o(null);return}const g=JSON.parse(p),y=g.user?.phone||g.profile?.phone;if(!y){o(null);return}const{data:h,error:j}=await Q.from("profiles").select("*").eq("phone",y).eq("role","consumer").single();j?(console.error("Error fetching consumer profile:",j),o({full_name:g.profile?.full_name||g.profile?.first_name||"Consumer",original_full_name:g.profile?.original_full_name||"",phone:y,...g.profile})):o({full_name:h.full_name||h.first_name||"Consumer",original_full_name:h.original_full_name||"",phone:h.phone||y,...h})}catch(p){console.error("Error in fetchConsumerProfile:",p),o(null)}};x.useEffect(()=>{t&&f()},[t]),x.useEffect(()=>{if(t&&!n){const p=setTimeout(()=>{a()},1500);return()=>clearTimeout(p)}},[t,n,a]);const d=(p,g)=>{console.log("✅ Authentication successful in checkout:",{user:p,action:g})};return e.jsx(Te,{onAuthSuccess:d,children:p=>e.jsxs(M,{className:"w-full",children:[e.jsxs(T,{children:[e.jsxs(B,{className:"flex items-center space-x-2",children:[e.jsx(R,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:l("checkout.step1.title")})]}),e.jsx("p",{className:"text-gray-600",children:l("checkout.step1.description")})]}),e.jsxs(I,{className:"space-y-6",children:[t?e.jsxs(C.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"space-y-6",children:[e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(_,{className:"h-10 w-10 text-green-600"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:l("checkout.step1.authSuccess")}),e.jsx("p",{className:"text-gray-600 mb-4",children:l("checkout.step1.welcomeBack")}),e.jsx("div",{className:"bg-green-50 rounded-lg p-4 max-w-md mx-auto",children:e.jsxs("div",{className:"flex items-center justify-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-200 rounded-full flex items-center justify-center",children:e.jsx(R,{className:"h-5 w-5 text-green-700"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"font-medium text-green-900",children:s?.full_name||l("checkout.step1.consumer")}),e.jsx("p",{className:"text-sm text-green-700",children:s?.phone})]}),e.jsx(D,{variant:"secondary",className:"bg-green-200 text-green-800",children:l("checkout.step1.verified")})]})})]}),e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center space-x-2 text-sm text-gray-600",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-[#fa7b00] border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:l("checkout.step1.proceeding")})]})})]}):e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-20 h-20 bg-[#fa7b00]/10 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(H,{className:"h-10 w-10 text-[#fa7b00]"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:l("checkout.step1.authRequired")}),e.jsx("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:l("checkout.step1.authDescription")}),e.jsxs(w,{onClick:g=>{g.preventDefault(),g.stopPropagation(),console.log("🔘 Verify Phone Number button clicked - triggering single modal"),p()},size:"lg",className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white px-8 cursor-pointer",type:"button",children:[e.jsx(H,{className:"h-5 w-5 mr-2"}),l("checkout.step1.verifyPhone")]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h4",{className:"font-medium text-gray-900 mb-4 flex items-center",children:[e.jsx(ce,{className:"h-5 w-5 text-green-600 mr-2"}),l("checkout.step1.securityFeatures")]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(_,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),e.jsx("span",{className:"text-sm text-gray-700",children:l("checkout.step1.feature1")})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(_,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),e.jsx("span",{className:"text-sm text-gray-700",children:l("checkout.step1.feature2")})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(_,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),e.jsx("span",{className:"text-sm text-gray-700",children:l("checkout.step1.feature3")})]})]})]})]}),e.jsxs("div",{className:"flex justify-between items-center pt-8 border-t border-gray-200",children:[e.jsx(w,{variant:"outline",onClick:c,disabled:!0,className:"px-6 py-3 border-2 border-gray-200 text-gray-400 cursor-not-allowed opacity-50 rounded-lg font-medium",children:l("common.back")}),t&&e.jsxs(w,{onClick:a,className:"px-8 py-3 bg-gradient-to-r from-[#fa7b00] to-[#ff9500] hover:from-[#e56f00] hover:to-[#e58500] text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[l("common.continue"),e.jsx(Y,{className:"h-4 w-4 ml-2"})]})]})]})]})})}function Le({data:a,onComplete:c,onBack:l}){const{t}=O(),{selectedLocation:n,getLocationString:s}=oe(),[o,f]=x.useState({deliveryAddress:a.deliveryAddress||"",deliveryCoordinates:a.deliveryCoordinates||null,deliveryWilaya:a.deliveryWilaya||"",googleMapsUrl:a.googleMapsUrl||"",specialInstructions:a.specialInstructions||""}),[d,p]=x.useState(!1),[g,y]=x.useState("");x.useEffect(()=>{if(n){const i=n.coordinates;console.log("🗺️ Location context updated, updating form data:",n),f(N=>({...N,deliveryAddress:n.formatted_address,deliveryCoordinates:i,deliveryWilaya:n.wilaya,googleMapsUrl:`https://maps.google.com/?q=${i.lat},${i.lng}`})),y("")}},[n]);const h=(i,N)=>{console.log("🗺️ DeliveryAddressStep: Location selected from map:",{lat:i,lng:N})},j=(i,N)=>{f(m=>({...m,[i]:N})),y("")},S=async()=>{p(!0),y("");try{if(!o.deliveryAddress.trim())throw new Error(t("checkout.step2.errors.addressRequired"));if(!o.deliveryWilaya.trim())throw new Error(t("checkout.step2.errors.wilayaRequired"));if(!o.deliveryCoordinates)throw new Error(t("checkout.step2.errors.coordinatesRequired"));if(o.deliveryAddress.length<10)throw new Error(t("checkout.step2.errors.addressTooShort"));let i=o.googleMapsUrl;!i&&o.deliveryCoordinates&&(i=`https://maps.google.com/?q=${o.deliveryCoordinates.lat},${o.deliveryCoordinates.lng}`),await new Promise(N=>setTimeout(N,1e3)),c({deliveryAddress:o.deliveryAddress.trim(),deliveryCoordinates:o.deliveryCoordinates,deliveryWilaya:o.deliveryWilaya.trim(),googleMapsUrl:i,specialInstructions:o.specialInstructions.trim()})}catch(i){y(i instanceof Error?i.message:t("checkout.step2.errors.validationFailed"))}finally{p(!1)}},A=()=>o.deliveryAddress.trim()&&o.deliveryWilaya.trim()&&o.deliveryCoordinates;return e.jsxs(M,{className:"w-full",children:[e.jsxs(T,{children:[e.jsxs(B,{className:"flex items-center space-x-2",children:[e.jsx(q,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:t("checkout.step2.title")})]}),e.jsx("p",{className:"text-gray-600",children:t("checkout.step2.description")})]}),e.jsx(I,{className:"space-y-6",children:e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(E,{className:"text-base font-medium",children:t("checkout.step2.selectLocation")}),e.jsx("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:e.jsx(ye,{height:"300px",showCurrentLocationButton:!0,showSearch:!0,className:"w-full",onLocationSelect:h})}),n&&e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-green-600",children:[e.jsx(_,{className:"h-4 w-4"}),e.jsx("span",{children:t("checkout.step2.locationSelected")})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(E,{htmlFor:"delivery-address",className:"text-base font-medium",children:[t("checkout.step2.deliveryAddress")," *"]}),e.jsx(K,{id:"delivery-address",placeholder:t("checkout.step2.addressPlaceholder"),value:o.deliveryAddress,onChange:i=>j("deliveryAddress",i.target.value),className:"min-h-[80px]",required:!0})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(E,{htmlFor:"delivery-wilaya",className:"text-base font-medium",children:[t("checkout.step2.wilaya")," *"]}),e.jsx(je,{id:"delivery-wilaya",placeholder:t("checkout.step2.wilayaPlaceholder"),value:o.deliveryWilaya,onChange:i=>j("deliveryWilaya",i.target.value),required:!0})]}),o.googleMapsUrl&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{className:"text-base font-medium",children:t("checkout.step2.googleMapsUrl")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(D,{variant:"outline",className:"text-green-600 border-green-600",children:[e.jsx(_,{className:"h-3 w-3 mr-1"}),t("checkout.step2.confirmed")]}),e.jsxs(w,{variant:"ghost",size:"sm",onClick:()=>window.open(o.googleMapsUrl,"_blank"),className:"text-blue-600 hover:text-blue-800",children:[e.jsx(xe,{className:"h-4 w-4 mr-1"}),t("checkout.step2.viewOnMaps")]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(E,{htmlFor:"special-instructions",className:"text-base font-medium",children:t("checkout.step2.specialInstructions")}),e.jsx(K,{id:"special-instructions",placeholder:t("checkout.step2.instructionsPlaceholder"),value:o.specialInstructions,onChange:i=>j("specialInstructions",i.target.value),className:"min-h-[60px]"}),e.jsx("p",{className:"text-xs text-gray-500",children:t("checkout.step2.instructionsHelp")})]})]}),o.deliveryAddress&&e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[e.jsxs("h4",{className:"font-medium text-blue-900 mb-2 flex items-center",children:[e.jsx(Oe,{className:"h-4 w-4 mr-2"}),t("checkout.step2.deliverySummary")]}),e.jsxs("div",{className:"text-sm text-blue-800",children:[e.jsx("p",{className:"font-medium",children:o.deliveryWilaya}),e.jsx("p",{className:"mt-1",children:o.deliveryAddress}),o.specialInstructions&&e.jsxs("p",{className:"mt-2 text-blue-700",children:[e.jsxs("span",{className:"font-medium",children:[t("checkout.step2.instructions"),":"]})," ",o.specialInstructions]})]})]}),g&&e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-2 text-red-800",children:[e.jsx(X,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm font-medium",children:g})]})}),e.jsxs("div",{className:"flex justify-between items-center pt-8 border-t border-gray-200",children:[e.jsxs(w,{variant:"outline",onClick:l,disabled:d,className:"px-6 py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 rounded-lg font-medium",children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),t("common.back")]}),e.jsx(w,{onClick:S,disabled:!A()||d,className:"px-8 py-3 bg-gradient-to-r from-[#fa7b00] to-[#ff9500] hover:from-[#e56f00] hover:to-[#e58500] text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),t("checkout.step2.validating")]}):e.jsxs(e.Fragment,{children:[t("common.continue"),e.jsx(Y,{className:"h-4 w-4 ml-2"})]})})]})]})})]})}const ne=[{id:"cash_on_delivery",name:"Cash on Delivery (Express 24h)",description:"Pay when your order is delivered to your address",icon:P,features:["Express 24-48 hour delivery","Pay directly to the delivery driver","Inspect products before payment","Available in all 58 wilayas"],badge:"Most Popular",badgeColor:"bg-green-100 text-green-800"},{id:"store_pickup",name:"Store Pickup & Pay",description:"Pick up your order from the store and pay in person",icon:W,features:["No delivery fees","Flexible pickup times","Direct interaction with seller","Immediate product inspection"],badge:"Save Money",badgeColor:"bg-blue-100 text-blue-800"}];function $e({data:a,onComplete:c,onBack:l}){const{t}=O(),[n,s]=x.useState(a.paymentMethod),o=d=>{s(d)},f=()=>{n&&c({paymentMethod:n})};return e.jsxs(M,{className:"w-full",children:[e.jsxs(T,{children:[e.jsxs(B,{className:"flex items-center space-x-2",children:[e.jsx(ee,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:t("checkout.step3.title")})]}),e.jsx("p",{className:"text-gray-600",children:t("checkout.step3.description")})]}),e.jsx(I,{className:"space-y-6",children:e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[e.jsx(pe,{value:n||"",onValueChange:d=>o(d),className:"space-y-4",children:ne.map(d=>e.jsxs(C.div,{whileHover:{scale:1.02},whileTap:{scale:.98},className:`
                  relative border-2 rounded-lg p-6 cursor-pointer transition-all
                  ${n===d.id?"border-[#fa7b00] bg-[#fa7b00]/5":"border-gray-200 hover:border-gray-300"}
                `,onClick:()=>o(d.id),children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx(ue,{value:d.id,id:d.id,className:"mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`
                          w-12 h-12 rounded-lg flex items-center justify-center
                          ${n===d.id?"bg-[#fa7b00] text-white":"bg-gray-100 text-gray-600"}
                        `,children:e.jsx(d.icon,{className:"h-6 w-6"})}),e.jsxs("div",{children:[e.jsx(E,{htmlFor:d.id,className:"text-lg font-semibold cursor-pointer",children:d.name}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:d.description})]})]}),e.jsx(D,{className:d.badgeColor,children:d.badge})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 mt-4",children:d.features.map((p,g)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"h-4 w-4 text-green-600 flex-shrink-0"}),e.jsx("span",{className:"text-sm text-gray-700",children:p})]},g))}),d.id==="cash_on_delivery"&&e.jsxs("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-yellow-800",children:[e.jsx(Me,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm font-medium",children:t("checkout.step3.codNote")})]}),e.jsx("p",{className:"text-xs text-yellow-700 mt-1",children:t("checkout.step3.codDescription")})]}),d.id==="store_pickup"&&e.jsxs("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-blue-800",children:[e.jsx(q,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm font-medium",children:t("checkout.step3.pickupNote")})]}),e.jsx("p",{className:"text-xs text-blue-700 mt-1",children:t("checkout.step3.pickupDescription")})]})]})]}),n===d.id&&e.jsx(C.div,{initial:{scale:0},animate:{scale:1},className:"absolute top-4 right-4 w-6 h-6 bg-[#fa7b00] rounded-full flex items-center justify-center",children:e.jsx(_,{className:"h-4 w-4 text-white"})})]},d.id))}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-gray-800 mb-2",children:[e.jsx(ce,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"font-medium",children:t("checkout.step3.securityTitle")})]}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[e.jsxs("p",{children:["• ",t("checkout.step3.security1")]}),e.jsxs("p",{children:["• ",t("checkout.step3.security2")]}),e.jsxs("p",{children:["• ",t("checkout.step3.security3")]})]})]}),n&&e.jsxs(C.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"bg-[#fa7b00]/10 border border-[#fa7b00]/20 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-[#fa7b00] mb-2",children:[e.jsx(_,{className:"h-5 w-5"}),e.jsx("span",{className:"font-medium",children:t("checkout.step3.selectedMethod")})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[n==="cash_on_delivery"?e.jsx(P,{className:"h-5 w-5 text-[#fa7b00]"}):e.jsx(W,{className:"h-5 w-5 text-[#fa7b00]"}),e.jsx("span",{className:"font-medium text-gray-900",children:ne.find(d=>d.id===n)?.name})]})]}),e.jsxs("div",{className:"text-center py-4 border-t border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-gray-500 mb-2",children:[e.jsx(de,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:t("checkout.step3.comingSoon")})]}),e.jsx("p",{className:"text-xs text-gray-400",children:t("checkout.step3.futurePayments")})]}),e.jsxs("div",{className:"flex justify-between items-center pt-8 border-t border-gray-200",children:[e.jsxs(w,{variant:"outline",onClick:l,className:"px-6 py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 rounded-lg font-medium",children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),t("common.back")]}),e.jsxs(w,{onClick:f,disabled:!n,className:"px-8 py-3 bg-gradient-to-r from-[#fa7b00] to-[#ff9500] hover:from-[#e56f00] hover:to-[#e58500] text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:[t("common.continue"),e.jsx(Y,{className:"h-4 w-4 ml-2"})]})]})]})})]})}async function qe(){try{console.log("🚚 Fetching active shipping companies...");const{data:a,error:c}=await Q.from("shipping_companies").select("*").eq("is_active",!0).order("company_name");if(c)throw console.error("❌ Error fetching shipping companies:",c),c;return console.log("✅ Shipping companies fetched:",a?.length||0),a||[]}catch(a){throw console.error("❌ Exception in getActiveShippingCompanies:",a),a}}async function Re(a,c,l){try{console.log("🧮 Calculating shipping cost:",{orderValue:a,deliveryWilaya:c,companyId:l});const t=await qe(),n=[];for(const s of t){if(l&&s.id,!(s.coverage_areas.includes("All Wilayas")||s.coverage_areas.includes(c))){console.log(`⚠️ ${s.company_name} doesn't serve ${c}`);continue}if(a<s.minimum_order_value){console.log(`⚠️ Order value ${a} below minimum ${s.minimum_order_value} for ${s.company_name}`);continue}const d=50*s.cost_per_km,p=s.base_delivery_cost+d;let g="2-3 business days";s.service_types.includes("Same Day")?g="Same day delivery":s.service_types.includes("Express")&&(g="1-2 business days"),n.push({company_id:s.id,company_name:s.company_name,base_cost:s.base_delivery_cost,distance_cost:d,total_cost:p,estimated_delivery:g,notes:`Minimum order: ${s.minimum_order_value} DZD`,service_types:s.service_types,rating:s.average_rating})}return console.log("✅ Shipping calculations completed:",n.length),n}catch(t){throw console.error("❌ Exception in calculateShippingCost:",t),t}}async function Ue(a,c){try{console.log("🛒 Getting shipping options for checkout:",{orderValue:a,deliveryWilaya:c});const l=await Re(a,c);return l.sort((t,n)=>t.total_cost-n.total_cost),console.log("✅ Checkout shipping options ready:",l.length),l}catch(l){return console.error("❌ Exception in getShippingOptionsForCheckout:",l),[]}}function Ze({data:a,orderValue:c,onComplete:l,onBack:t}){O();const[n,s]=x.useState([]),[o,f]=x.useState(""),[d,p]=x.useState(""),[g,y]=x.useState(!0),[h,j]=x.useState(null);x.useEffect(()=>{S()},[a.deliveryWilaya,c]);const S=async()=>{try{y(!0),j(null),console.log("🚚 Loading shipping options for:",{wilaya:a.deliveryWilaya,orderValue:c});const m=await Ue(c,a.deliveryWilaya);console.log("✅ Shipping options loaded:",m),s(m),m.length>0&&f(m[0].company_id)}catch(m){console.error("❌ Error loading shipping options:",m),j("Failed to load shipping options. Please try again.")}finally{y(!1)}},A=()=>{const m=n.find(k=>k.company_id===o);if(!m){j("Please select a shipping option");return}const F={shippingInfo:{company_id:m.company_id,company_name:m.company_name,shipping_cost:m.total_cost,shipping_method:"standard",estimated_delivery:m.estimated_delivery,shipping_notes:d}};console.log("✅ Shipping selection completed:",F),l(F)},i=m=>new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(m),N=m=>Array.from({length:5},(F,k)=>e.jsx(be,{className:`h-4 w-4 ${k<Math.floor(m)?"text-yellow-400 fill-current":"text-gray-300"}`},k));return g?e.jsx(M,{className:"w-full",children:e.jsx(I,{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-8 h-8 border-2 border-[#fa7b00] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Loading shipping options..."})]})})}):e.jsxs(M,{className:"w-full",children:[e.jsxs(T,{children:[e.jsxs(B,{className:"flex items-center space-x-2",children:[e.jsx(P,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:"Select Shipping Method"})]}),e.jsxs("p",{className:"text-gray-600",children:["Choose your preferred shipping company for delivery to ",e.jsx("strong",{children:a.deliveryWilaya})]})]}),e.jsxs(I,{className:"space-y-6",children:[h&&e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3",children:[e.jsx(X,{className:"h-5 w-5 text-red-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-red-800 font-medium",children:"Error"}),e.jsx("p",{className:"text-red-700 text-sm mt-1",children:h}),e.jsxs(w,{variant:"outline",size:"sm",onClick:S,className:"mt-3 text-red-700 border-red-300 hover:bg-red-50",children:[e.jsx(Ne,{className:"h-4 w-4 mr-2"}),"Retry"]})]})]}),n.length===0&&!h?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(P,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 font-medium",children:"No shipping options available"}),e.jsxs("p",{className:"text-gray-500 text-sm mt-1",children:["Unfortunately, no shipping companies serve ",a.deliveryWilaya," for orders of this value."]})]}):e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-4",children:[e.jsx(pe,{value:o,onValueChange:f,children:n.map(m=>e.jsx("div",{className:"relative",children:e.jsxs(E,{htmlFor:m.company_id,className:`flex items-center space-x-4 p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${o===m.company_id?"border-[#fa7b00] bg-orange-50":"border-gray-200 hover:border-[#fa7b00]/50 hover:bg-gray-50"}`,children:[e.jsx(ue,{value:m.company_id,id:m.company_id}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:m.company_name}),m.rating>0&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"flex",children:N(m.rating)}),e.jsxs("span",{className:"text-sm text-gray-600",children:["(",m.rating.toFixed(1),")"]})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:"font-bold text-lg text-[#fa7b00]",children:i(m.total_cost)})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(de,{className:"h-4 w-4"}),e.jsx("span",{children:m.estimated_delivery})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(me,{className:"h-4 w-4"}),e.jsx("span",{children:m.service_types.join(", ")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(q,{className:"h-4 w-4"}),e.jsxs("span",{children:["To ",a.deliveryWilaya]})]})]}),m.notes&&e.jsx("p",{className:"text-xs text-gray-500 mt-2 bg-gray-100 px-2 py-1 rounded",children:m.notes})]})]})},m.company_id))}),e.jsxs("div",{className:"space-y-2 pt-4 border-t border-gray-200",children:[e.jsx(E,{htmlFor:"shipping-notes",children:"Special Shipping Instructions (Optional)"}),e.jsx(K,{id:"shipping-notes",placeholder:"Any special instructions for the shipping company (e.g., call before delivery, leave at reception, etc.)",value:d,onChange:m=>p(m.target.value),className:"min-h-[80px] resize-none"})]})]}),e.jsxs("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[e.jsx(w,{variant:"outline",onClick:t,className:"px-6 py-3",children:"Back to Payment"}),e.jsx(w,{onClick:A,disabled:!o||n.length===0,className:"px-8 py-3 bg-[#fa7b00] hover:bg-[#fa7b00]/90 disabled:opacity-50 disabled:cursor-not-allowed",children:"Continue to Review"})]})]})]})}function We({data:a,cartItems:c,onConfirm:l,onBack:t,isProcessing:n}){const{t:s}=O();he();const[o,f]=x.useState(!1),[d,p]=x.useState(null),[g,y]=x.useState(!0),[h,j]=x.useState(null),[S,A]=x.useState(!1),i=async()=>{try{y(!0),console.log("🔍 [OrderConfirmation] Starting consumer profile fetch...");const r=localStorage.getItem("phone_auth_session");if(!r){console.log("❌ [OrderConfirmation] No phone session found in localStorage"),p(null);return}const u=JSON.parse(r);console.log("📱 [OrderConfirmation] Phone session data:",{user_phone:u.user?.phone,profile_phone:u.profile?.phone,profile_full_name:u.profile?.full_name,profile_original_full_name:u.profile?.original_full_name,profile_role:u.profile?.role});const b=u.user?.phone||u.profile?.phone;if(!b){console.log("❌ [OrderConfirmation] No phone found in session"),p(null);return}console.log("🔍 [OrderConfirmation] Fetching latest consumer profile for phone:",b);const{data:v,error:L}=await Q.from("profiles").select("*").eq("phone",b).eq("role","consumer").single();if(L){console.error("❌ [OrderConfirmation] Error fetching consumer profile from database:",L),console.log("🔄 [OrderConfirmation] Falling back to localStorage data");const $={full_name:u.profile?.full_name||u.profile?.first_name||"Consumer",original_full_name:u.profile?.original_full_name||"",phone:b,...u.profile};console.log("📋 [OrderConfirmation] Using fallback profile:",$),p($)}else{console.log("✅ [OrderConfirmation] Latest consumer profile fetched from database:",{id:v.id,full_name:v.full_name,original_full_name:v.original_full_name,phone:v.phone,role:v.role});const $={full_name:v.full_name||v.first_name||"Consumer",original_full_name:v.original_full_name||"",phone:v.phone||b,...v};console.log("📋 [OrderConfirmation] Final profile to display:",$),p($)}}catch(r){console.error("❌ [OrderConfirmation] Error in fetchConsumerProfile:",r),p(null)}finally{y(!1)}};x.useEffect(()=>{i()},[]),x.useEffect(()=>{const r=()=>{console.log("🔄 [OrderConfirmation] Auth success event received, refreshing profile..."),setTimeout(()=>{i()},1e3)};return window.addEventListener("auth:success",r),()=>window.removeEventListener("auth:success",r)},[]),x.useEffect(()=>{N()},[a.shippingInfo,c]);const N=async()=>{try{A(!0),console.log("💰 [OrderConfirmation] Calculating fees...");const r=c.reduce((v,L)=>v+L.price*L.quantity,0),u=a.shippingInfo?.shipping_cost||0,b=await ge(r,u,c);console.log("💰 [OrderConfirmation] Fee calculation result:",b),j(b)}catch(r){console.error("❌ [OrderConfirmation] Error calculating fees:",r),j(null)}finally{A(!1)}},m=c.reduce((r,u)=>r+u.price*u.quantity,0),F=a.shippingInfo?.shipping_cost||0,k=h?.total_fees||0,z=m+F+k;c.reduce((r,u)=>{const b=u.shippingOrigin||"Unknown Location";return r[b]||(r[b]={items:[],location:u.shippingOrigin||"Location TBD"}),r[b].items.push(u),r},{});const G=()=>{o&&l()};return e.jsxs(M,{className:"w-full",children:[e.jsxs(T,{children:[e.jsxs(B,{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:s("checkout.step4.title")})]}),e.jsx("p",{className:"text-gray-600",children:s("checkout.step4.description")})]}),e.jsx(I,{className:"space-y-6",children:e.jsxs(C.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center text-lg",children:[e.jsx(R,{className:"h-5 w-5 mr-3 text-[#fa7b00]"}),s("checkout.step4.customerInfo")]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("checkout.step4.name")}),e.jsx("p",{className:"font-medium text-gray-900",children:g?e.jsx("span",{className:"text-gray-500",children:"Loading..."}):d?.full_name||s("checkout.step4.consumer")})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(H,{className:"h-4 w-4 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("checkout.step4.phone")}),e.jsx("p",{className:"font-medium text-gray-900",children:d?.phone})]})]})]})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center text-lg",children:[e.jsx(q,{className:"h-5 w-5 mr-3 text-[#fa7b00]"}),s("checkout.step4.deliveryInfo")]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("checkout.step4.wilaya")}),e.jsx("p",{className:"font-medium text-gray-900",children:a.deliveryWilaya})]})]})}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full mt-2"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("checkout.step4.address")}),e.jsx("p",{className:"font-medium text-gray-900 mt-1",children:a.deliveryAddress})]})]}),a.specialInstructions&&e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full mt-2"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("checkout.step4.instructions")}),e.jsx("p",{className:"font-medium text-gray-900 mt-1",children:a.specialInstructions})]})]}),e.jsx("div",{className:"pt-2",children:e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>window.open(a.googleMapsUrl,"_blank"),className:"text-[#fa7b00] border-[#fa7b00] hover:bg-[#fa7b00] hover:text-white transition-colors",children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),s("checkout.step4.viewOnMaps")]})})]})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center text-lg",children:[e.jsx(ee,{className:"h-5 w-5 mr-3 text-[#fa7b00]"}),s("checkout.step4.paymentMethod")]}),e.jsx("div",{className:"flex items-center space-x-4",children:a.paymentMethod==="cash_on_delivery"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(P,{className:"h-6 w-6 text-green-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:s("checkout.step4.cashOnDelivery")}),e.jsx("p",{className:"text-sm text-gray-600",children:s("checkout.step4.express24h")})]})]}),e.jsx(D,{className:"bg-green-100 text-green-800 border-green-200",children:s("checkout.step4.express24h")})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(W,{className:"h-6 w-6 text-blue-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:s("checkout.step4.storePickup")}),e.jsx("p",{className:"text-sm text-gray-600",children:s("checkout.step4.noDeliveryFee")})]})]}),e.jsx(D,{className:"bg-blue-100 text-blue-800 border-blue-200",children:s("checkout.step4.noDeliveryFee")})]})})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center text-lg",children:[e.jsx(Pe,{className:"h-5 w-5 mr-3 text-[#fa7b00]"}),s("checkout.step4.shippingOrigins")]}),e.jsx("div",{className:"space-y-3",children:c.map((r,u)=>{const b=ve(r.shippingOrigin)||r.shippingOrigin||"Location TBD";return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("img",{src:r.image||"/placeholder.svg",alt:r.name,className:"w-10 h-10 object-cover rounded-md"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900 text-sm",children:r.name}),e.jsxs("p",{className:"text-xs text-gray-600",children:["Ships from: ",b]})]})]}),e.jsx(D,{variant:"outline",className:"border-gray-300 text-gray-600 text-xs",children:b})]},`${r.id}-${u}`)})})]})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(me,{className:"h-5 w-5 mr-2"}),s("checkout.step4.orderSummary")]}),e.jsx("div",{className:"space-y-3 mb-4",children:c.map(r=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("img",{src:r.image||"/placeholder.svg",alt:r.name,className:"w-12 h-12 object-cover rounded-md"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:r.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[r.quantity," × ",new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(r.price)]})]}),e.jsx("p",{className:"text-sm font-medium",children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(r.price*r.quantity)})]},r.id))}),e.jsx(U,{}),e.jsxs("div",{className:"space-y-2 mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:s("checkout.step4.subtotal")}),e.jsx("span",{children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(m)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:s("checkout.step4.shipping")}),e.jsx("span",{children:S?e.jsx("span",{className:"text-gray-500",children:"Calculating..."}):F>0?new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(F):e.jsx("span",{className:"text-green-600",children:"Free"})})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:s("checkout.step4.arouzFee")}),e.jsx("span",{children:S?e.jsx("span",{className:"text-gray-500",children:"Calculating..."}):k>0?new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(k):e.jsx("span",{className:"text-green-600",children:"Free"})})]})]}),e.jsx(U,{className:"my-4"}),e.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[e.jsx("span",{children:s("checkout.step4.total")}),e.jsx("span",{className:"text-[#fa7b00]",children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(z)})]})]}),e.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx(we,{id:"terms",checked:o,onCheckedChange:r=>f(r),className:"mt-1 border-[#fa7b00] data-[state=checked]:bg-[#fa7b00] data-[state=checked]:border-[#fa7b00]"}),e.jsx("div",{className:"flex-1",children:e.jsxs("label",{htmlFor:"terms",className:"text-sm text-gray-700 cursor-pointer leading-relaxed",children:[s("checkout.step4.termsText")," "," ",e.jsx("button",{className:"text-[#fa7b00] hover:underline font-medium",children:s("checkout.step4.termsLink")})," "," ",s("checkout.step4.and")," "," ",e.jsx("button",{className:"text-[#fa7b00] hover:underline font-medium",children:s("checkout.step4.privacyLink")})]})})]})}),e.jsx("div",{className:"bg-white border border-amber-200 rounded-lg p-6 shadow-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx(ke,{className:"h-6 w-6 text-amber-600 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-semibold text-amber-800 mb-2",children:s("checkout.step4.warningTitle")}),e.jsx("p",{className:"text-sm text-amber-700 leading-relaxed",children:s("checkout.step4.warningText")})]})]})}),e.jsxs("div",{className:"flex justify-between items-center pt-8 border-t border-gray-200",children:[e.jsxs(w,{variant:"outline",onClick:t,disabled:n,className:"px-6 py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 rounded-lg font-medium disabled:opacity-50",children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),s("common.back")]}),e.jsx(w,{onClick:G,disabled:!o||n,className:"px-10 py-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-lg",children:n?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"}),s("checkout.step4.processing")]}):e.jsxs(e.Fragment,{children:[e.jsx(_,{className:"h-5 w-5 mr-3"}),s("checkout.step4.confirmOrder")]})})]})]})})]})}function ze({cartItems:a,checkoutData:c,totalPrice:l,currentStep:t}){const{t:n}=O(),[s,o]=x.useState(null),[f,d]=x.useState(!1),[p,g]=x.useState(null);x.useEffect(()=>{t>=4&&c.shippingInfo&&y()},[c.shippingInfo,l,a,t]);const y=async()=>{try{d(!0),g(null),console.log("💰 Calculating fees for order summary...");const i=c.shippingInfo?.shipping_cost||0,N=await ge(l,i,a);o(N),console.log("✅ Fee calculation completed for order summary:",N)}catch(i){console.error("❌ Error calculating fees:",i),g("Failed to calculate fees")}finally{d(!1)}},h=i=>new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(i),j=()=>t<4||!c.shippingInfo?e.jsx("span",{className:"text-gray-500",children:n("checkout.calculatedAtCheckout")}):e.jsx("span",{className:"font-medium",children:h(c.shippingInfo.shipping_cost)}),S=()=>t<4||!s?e.jsx("span",{className:"text-gray-500",children:n("checkout.calculatedAtCheckout")}):f?e.jsx("span",{className:"text-gray-500",children:"Calculating..."}):p?e.jsx("span",{className:"text-red-500 text-xs",children:"Error"}):e.jsx("span",{className:"font-medium",children:h(s.total_fees)}),A=()=>{if(s)return s.grand_total;const i=c.shippingInfo?.shipping_cost||0;return l+i};return e.jsxs(M,{className:"sticky top-32",children:[e.jsx(T,{children:e.jsxs(B,{className:"flex items-center space-x-2",children:[e.jsx(_e,{className:"h-5 w-5"}),e.jsx("span",{children:n("checkout.orderSummary")}),f&&e.jsx("div",{className:"w-4 h-4 border-2 border-[#fa7b00] border-t-transparent rounded-full animate-spin"})]})}),e.jsxs(I,{className:"space-y-4",children:[e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:a.map(i=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("img",{src:i.image||"/placeholder.svg",alt:i.name,className:"w-12 h-12 object-cover rounded-md"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:i.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i.quantity," × ",h(i.price)]})]}),e.jsx("p",{className:"text-sm font-medium",children:h(i.price*i.quantity)})]},i.id))}),e.jsx(U,{}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:n("checkout.subtotal")}),e.jsx("span",{className:"font-medium",children:h(l)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:n("checkout.shipping")}),c.shippingInfo&&e.jsx(se,{children:e.jsxs(te,{children:[e.jsx(ae,{children:e.jsx(re,{className:"h-3 w-3 text-gray-400"})}),e.jsxs(ie,{children:[e.jsx("p",{className:"text-xs",children:c.shippingInfo.company_name}),e.jsx("p",{className:"text-xs",children:c.shippingInfo.estimated_delivery})]})]})})]}),j()]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:n("checkout.arouzFee")}),e.jsx(se,{children:e.jsxs(te,{children:[e.jsx(ae,{children:e.jsx(re,{className:"h-3 w-3 text-gray-400"})}),e.jsx(ie,{children:e.jsxs("div",{className:"text-xs space-y-1",children:[e.jsx("p",{children:"Marketplace commission and service fees"}),s&&s.fees.map((i,N)=>e.jsxs("p",{children:[i.fee_name,": ",h(i.calculated_amount)]},N))]})})]})})]}),S()]}),s&&s.fees.length>0&&e.jsxs(C.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"bg-orange-50 rounded-lg p-3 space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-xs font-medium text-[#fa7b00]",children:[e.jsx(Ie,{className:"h-3 w-3"}),e.jsx("span",{children:"Fee Breakdown"})]}),s.fees.map((i,N)=>e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsxs("span",{className:"text-gray-600",children:[i.fee_name,i.calculation_method==="percentage"&&` (${i.rate}%)`]}),e.jsx("span",{className:"font-medium",children:h(i.calculated_amount)})]},N))]})]}),e.jsx(U,{}),e.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[e.jsx("span",{children:n("checkout.total")}),e.jsx("span",{className:"text-[#fa7b00]",children:h(A())})]}),c.paymentMethod&&e.jsx("div",{className:"pt-2",children:e.jsx(D,{variant:"outline",className:"w-full justify-center py-2",children:c.paymentMethod==="cash_on_delivery"?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"h-4 w-4 mr-2"})," ",n("checkout.cashOnDelivery")]}):e.jsxs(e.Fragment,{children:[e.jsx(W,{className:"h-4 w-4 mr-2"})," ",n("checkout.storePickup")]})})}),c.shippingInfo&&e.jsx("div",{children:e.jsxs(D,{variant:"outline",className:"w-full justify-center py-2 bg-blue-50 border-blue-200",children:[e.jsx(P,{className:"h-4 w-4 mr-2 text-blue-600"}),e.jsx("span",{className:"text-blue-700",children:c.shippingInfo.company_name})]})}),p&&e.jsxs("div",{className:"flex items-center space-x-2 text-xs text-red-600 bg-red-50 p-2 rounded",children:[e.jsx(X,{className:"h-3 w-3"}),e.jsx("span",{children:"Fee calculation error"}),e.jsx(w,{variant:"ghost",size:"sm",onClick:y,className:"ml-auto h-6 px-2 text-xs",children:"Retry"})]}),s&&!p&&e.jsxs("div",{className:"flex items-center space-x-2 text-xs text-green-600 bg-green-50 p-2 rounded",children:[e.jsx(_,{className:"h-3 w-3"}),e.jsx("span",{children:"All fees calculated"})]})]})]})}const le=[{id:1,name:"Authentication",icon:R,description:"Verify your phone number"},{id:2,name:"Delivery Address",icon:q,description:"Confirm delivery location"},{id:3,name:"Payment Method",icon:ee,description:"Choose payment option"},{id:4,name:"Shipping Method",icon:P,description:"Select shipping company"},{id:5,name:"Confirmation",icon:_,description:"Review and confirm order"}];function Xe(){const{t:a}=O(),c=Ce(),{toast:l}=Se(),{items:t,totalItems:n,totalPrice:s,clearCart:o}=Ae(),{isAuthenticated:f,isLoading:d}=J(),{user:p,profile:g}=he(),{selectedLocation:y}=oe(),[h,j]=x.useState(1),[S,A]=x.useState(!1),[i,N]=x.useState({deliveryAddress:"",deliveryCoordinates:null,deliveryWilaya:"",googleMapsUrl:"",paymentMethod:null,specialInstructions:""});x.useEffect(()=>{n===0&&c("/my-vehicle-parts")},[n,c]),x.useEffect(()=>{!f&&h>1&&(j(1),l({title:a("checkout.authRequired"),description:a("checkout.pleaseSignIn"),variant:"destructive",duration:3e3}))},[f,h,l,a]),x.useEffect(()=>{if(f&&h===1){const r=setTimeout(()=>{j(2)},2e3);return()=>clearTimeout(r)}},[f]),x.useEffect(()=>{y&&!i.deliveryAddress&&N(r=>({...r,deliveryAddress:y.formatted_address,deliveryCoordinates:y.coordinates,deliveryWilaya:y.wilaya,googleMapsUrl:`https://maps.google.com/?q=${y.coordinates.lat},${y.coordinates.lng}`}))},[y,i.deliveryAddress]);const m=r=>{if(!f){l({title:a("checkout.authRequired"),description:a("checkout.pleaseSignInFirst"),variant:"destructive",duration:3e3}),j(1);return}F(h,r)&&(N(u=>({...u,...r})),h<5&&j(u=>u+1))},F=(r,u)=>{switch(r){case 1:return f?!0:(l({title:a("checkout.authRequired"),description:a("checkout.completeAuthentication"),variant:"destructive"}),!1);case 2:return u.deliveryAddress&&u.googleMapsUrl?!0:(l({title:a("checkout.deliveryRequired"),description:a("checkout.selectDeliveryLocation"),variant:"destructive"}),!1);case 3:return u.paymentMethod?!0:(l({title:a("checkout.paymentRequired"),description:a("checkout.selectPaymentMethod"),variant:"destructive"}),!1);case 4:return u.shippingInfo?!0:(l({title:"Shipping Required",description:"Please select a shipping method",variant:"destructive"}),!1);default:return!0}},k=()=>{h>1&&j(r=>r-1)},z=async()=>{console.log("🚀 [BASKET_PAGE] Order submission started..."),A(!0);try{console.log("🚀 [BASKET_PAGE] Creating order with data:",i),console.log("🚀 [BASKET_PAGE] Cart items:",t),console.log("🚀 [BASKET_PAGE] Current authentication state:",{isAuthenticated:f,user:p?.id,profile:g?.id}),console.log("🚀 [BASKET_PAGE] Calling createOrder service...");const r=await De(i,t);console.log("🚀 [BASKET_PAGE] Order service result:",r),r.success&&r.order?(console.log("✅ [BASKET_PAGE] Order created successfully:",r.order),l({title:a("checkout.orderCreated"),description:`${a("checkout.orderNumber")}: ${r.order.order_number}`,duration:5e3}),o(),c(`/order-success?orderId=${r.order.id}`)):(console.error("❌ [BASKET_PAGE] Order creation failed:",r.error),l({title:a("checkout.orderFailed"),description:r.error||a("checkout.orderFailedDescription"),variant:"destructive",duration:5e3}))}catch(r){console.error("❌ [BASKET_PAGE] Unexpected error creating order:",r),l({title:a("checkout.orderFailed"),description:a("checkout.unexpectedError"),variant:"destructive",duration:5e3})}finally{console.log("🏁 [BASKET_PAGE] Order submission completed, setting isProcessing to false"),A(!1)}},G=r=>{switch(r){case 1:return f;case 2:return i.deliveryAddress&&i.googleMapsUrl;case 3:return i.paymentMethod!==null;case 4:return i.shippingInfo!==void 0;case 5:return!1;default:return!1}};return n===0?null:e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-gradient-to-r from-white to-gray-50 border-b border-gray-200 sticky top-0 z-40 shadow-sm",children:e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs(w,{variant:"ghost",size:"sm",onClick:()=>c(-1),className:"text-gray-600 hover:text-[#fa7b00] hover:bg-orange-50 transition-all duration-200 rounded-lg px-4 py-2",children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),a("common.back")]}),e.jsxs("div",{className:"border-l border-gray-300 pl-6",children:[e.jsx("h1",{className:"text-3xl font-bold text-[#071c44] tracking-tight",children:a("checkout.title")}),e.jsxs("p",{className:"text-sm text-gray-600 mt-1 font-medium",children:[n," ",a(n===1?"marketplace.item":"marketplace.items")," •",e.jsx("span",{className:"text-[#fa7b00] font-semibold ml-1",children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s)})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3 bg-green-50 px-4 py-2 rounded-full border border-green-200",children:[e.jsx(Fe,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{className:"text-sm text-green-700 font-semibold",children:a("checkout.secureCheckout")})]})]})})}),e.jsx("div",{className:"bg-white border-b border-gray-200",children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"flex items-center justify-between relative",children:[e.jsx("div",{className:"absolute top-6 left-0 right-0 h-1 bg-gray-200 rounded-full",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-[#fa7b00] to-[#ff9500] rounded-full transition-all duration-500 ease-out",style:{width:`${(h-1)/(le.length-1)*100}%`}})}),le.map((r,u)=>{const b=G(r.id),v=h===r.id;return h<r.id,e.jsxs("div",{className:"relative flex flex-col items-center z-10",children:[e.jsx("div",{className:`
                      w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ease-out transform
                      ${b?"bg-gradient-to-r from-[#fa7b00] to-[#ff9500] text-white shadow-lg scale-110":v?"bg-white border-4 border-[#fa7b00] text-[#fa7b00] shadow-lg scale-110":"bg-white border-2 border-gray-300 text-gray-400 hover:border-gray-400"}
                    `,children:b?e.jsx(C.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.3},children:e.jsx(_,{className:"h-6 w-6"})}):e.jsx("span",{className:`text-lg font-bold ${v?"text-[#fa7b00]":""}`,children:r.id})}),e.jsxs("div",{className:"mt-3 text-center max-w-[120px]",children:[e.jsx("p",{className:`text-sm font-semibold transition-colors duration-200 ${b||v?"text-[#fa7b00]":"text-gray-500"}`,children:r.name}),e.jsx("p",{className:`text-xs mt-1 hidden sm:block transition-colors duration-200 ${b||v?"text-gray-700":"text-gray-400"}`,children:r.description})]}),v&&e.jsx(C.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},className:"absolute -bottom-2 w-2 h-2 bg-[#fa7b00] rounded-full"})]},r.id)})]})})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsx(Ee,{mode:"wait",children:e.jsxs(C.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:[h===1&&e.jsx(Be,{onComplete:()=>m({}),onBack:k}),h===2&&e.jsx(Le,{data:i,onComplete:m,onBack:k}),h===3&&e.jsx($e,{data:i,onComplete:m,onBack:k}),h===4&&e.jsx(Ze,{data:i,orderValue:s,onComplete:m,onBack:k}),h===5&&e.jsx(We,{data:i,cartItems:t,onConfirm:z,onBack:k,isProcessing:S})]},h)})}),e.jsx("div",{className:"lg:col-span-1",children:e.jsx(ze,{cartItems:t,checkoutData:i,totalPrice:s,currentStep:h})})]})})]})}export{Xe as BasketPage};
