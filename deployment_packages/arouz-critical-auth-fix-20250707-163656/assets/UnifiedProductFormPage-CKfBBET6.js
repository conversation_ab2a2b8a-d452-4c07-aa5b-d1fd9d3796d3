import{r as n,bn as B,bo as L,ay as P,q as J,ag as Q,u as I,j as u,B as A,A as T,i as K}from"./index-DjNptb3k.js";import{A as q}from"./AdminLayout-DKbCMQsb.js";import{B as R}from"./BarcodeScanner-D4mKTwX3.js";import{U}from"./ProductFormSelector-Cw79fh1d.js";import{S as M}from"./ProductFormDialog-D6mgwDQ3.js";import"./house-CifaDVdO.js";import"./settings-C2Lkvf25.js";import"./ProductEditDialog-DDRc6U-W.js";import"./select-B_ZRrAqd.js";import"./progress-wrYgTL2X.js";import"./upload-bhq3H-yM.js";import"./enhanced-dropdown-BWor6myK.js";import"./idGenerator-DvJoXqJW.js";import"./categories-k_ueMvEL.js";const z=async()=>{await new Promise(o=>setTimeout(o,800));const h=[],g=["tyres","brakes"];for(const o of g){const a=`products-${o}`,S=localStorage.getItem(a);if(S)try{const l=JSON.parse(S);h.push(...l)}catch(l){console.error(`Error parsing ${o} products:`,l)}}return h},G=(h={})=>{const[g,o]=n.useState([]),[a,S]=n.useState(h),l=B(),{data:f=[],isLoading:k,isError:v,error:$,refetch:j}=L({queryKey:["all-products"],queryFn:z,staleTime:5*60*1e3}),N=n.useCallback(()=>f.filter(e=>{if(a.search&&a.search.trim()!==""){const r=a.search.toLowerCase();if(![e.name,e.description,e.sku,e.barcode,e.manufacturer,e.supplierName].filter(Boolean).map(t=>t?.toLowerCase()).some(t=>t?.includes(r)))return!1}if(a.category&&a.category!=="all"&&e.category!==a.category||a.status&&a.status!=="all"&&e.status!==a.status)return!1;if(a.priceRange){const[r,s]=a.priceRange,t=e.retailPrice||0;if(t<r||t>s)return!1}return!(a.manufacturer&&a.manufacturer!=="all"&&e.manufacturer!==a.manufacturer||a.inStock!==void 0&&a.inStock&&e.stockQuantity<=0)}),[f,a]),C=n.useCallback(e=>{S(r=>({...r,...e}))},[]),b=n.useCallback(()=>{S({})},[]),D=n.useCallback(e=>{o(r=>r.includes(e)?r.filter(s=>s!==e):[...r,e])},[]),p=n.useCallback(()=>{const e=N().map(r=>r.id);o(e)},[N]),E=n.useCallback(()=>{o([])},[]),O=n.useCallback(e=>{const r=f.find(i=>i.id===e);if(!r)return;const s=r.category,t=`products-${s}`,c=localStorage.getItem(t);if(c)try{const y=JSON.parse(c).filter(m=>m.id!==e);localStorage.setItem(t,JSON.stringify(y)),l.setQueryData(["products",s],y),l.setQueryData(["all-products"],(m=[])=>m.filter(x=>x.id!==e)),o(m=>m.filter(x=>x!==e)),P.success("Product deleted successfully")}catch(i){console.error("Error deleting product:",i),P.error("Failed to delete product")}},[f,l]),w=n.useCallback(e=>{const r={};e.forEach(s=>{const t=f.find(i=>i.id===s);if(!t)return;const c=t.category;r[c]||(r[c]=[]),r[c].push(s)}),Object.entries(r).forEach(([s,t])=>{const c=`products-${s}`,i=localStorage.getItem(c);if(i)try{const m=JSON.parse(i).filter(x=>!t.includes(x.id));localStorage.setItem(c,JSON.stringify(m)),l.setQueryData(["products",s],m)}catch(y){console.error(`Error deleting products from ${s}:`,y)}}),l.setQueryData(["all-products"],(s=[])=>s.filter(t=>!e.includes(t.id))),o([]),P.success(`${e.length} products deleted successfully`)},[f,l]),d=n.useCallback((e,r)=>{const s={};e.forEach(t=>{const c=f.find(y=>y.id===t);if(!c)return;const i=c.category;s[i]||(s[i]=[]),s[i].push(t)}),Object.entries(s).forEach(([t,c])=>{const i=`products-${t}`,y=localStorage.getItem(i);if(y)try{const x=JSON.parse(y).map(F=>c.includes(F.id)?{...F,status:r,updatedAt:new Date}:F);localStorage.setItem(i,JSON.stringify(x)),l.setQueryData(["products",t],x)}catch(m){console.error(`Error updating products in ${t}:`,m)}}),l.setQueryData(["all-products"],(t=[])=>t.map(c=>e.includes(c.id)?{...c,status:r,updatedAt:new Date}:c)),P.success(`Status updated for ${e.length} products`)},[f,l]);return{products:N(),allProducts:f,isLoading:k,isError:v,error:$,refetch:j,filters:a,updateFilters:C,resetFilters:b,selectedProductIds:g,toggleProductSelection:D,selectAllProducts:p,deselectAllProducts:E,deleteProduct:O,deleteProducts:w,updateProductsStatus:d}};function ne(){const{id:h}=J(),g=Q(),{t:o}=I(),[a,S]=n.useState(!1),[l,f]=n.useState(null),[k,v]=n.useState(!1),[$,j]=n.useState(!1),[N,C]=n.useState(!1),{allProducts:b,isLoading:D}=G(),p=!!h&&h!=="new";n.useEffect(()=>{if(p&&b.length>0){const d=b.find(e=>e.id===h);d?(f(d),C(!0)):(P.error(o("products.productNotFound","Product not found")),g("/app/products"))}else p||C(!0)},[p,h,b,g,o]);const E=d=>{S(!1);const e=b.find(r=>r.partArticleNumber===d);if(e){P.info(o("products.barcodeExists","A product with this part article number already exists")),g(`/app/products/edit/${e.id}`);return}f(r=>({...r,partArticleNumber:d})),P.success(o("products.barcodeScanned","Part article number scanned successfully"))},O=async d=>{j(!0);try{console.log("Saving product:",d),await new Promise(t=>setTimeout(t,800));const e=`products-${d.category}`,r=localStorage.getItem(e);let s=[];r&&(s=JSON.parse(r)),p?s=s.map(t=>t.id===d.id?d:t):s.push(d),localStorage.setItem(e,JSON.stringify(s)),P.success(p?o("products.productUpdated","Product updated successfully!"):o("products.productCreated","Product created successfully!")),g(`/app/products-table/${d.category}`)}catch(e){console.error("Error saving product:",e),P.error(o("products.errorSaving","Error saving product"))}finally{j(!1)}},w=()=>{g("/app/products")};return u.jsxs(q,{children:[u.jsxs("div",{className:"flex flex-col gap-6",children:[u.jsxs("div",{className:"flex justify-between items-center",children:[u.jsxs("div",{className:"flex items-center gap-4",children:[u.jsx(A,{variant:"ghost",size:"icon",onClick:()=>g(-1),className:"rounded-full",children:u.jsx(T,{className:"h-5 w-5"})}),u.jsxs("div",{children:[u.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:o(p?"products.editProduct":"products.addProduct")}),u.jsx("p",{className:"text-muted-foreground mt-1",children:p?o("products.editProductDescription","Make changes to the product information"):o("products.addProductDescription","Fill in the details to create a new product")})]})]}),!p&&u.jsxs(A,{variant:"outline",className:"flex items-center gap-2",onClick:()=>S(!0),children:[u.jsx(M,{className:"h-4 w-4"}),o("products.scanBarcode")]})]}),(D||k)&&u.jsxs("div",{className:"flex justify-center items-center h-64",children:[u.jsx(K,{className:"h-8 w-8 animate-spin text-primary"}),u.jsx("span",{className:"ml-2 text-lg",children:o("common.loading","Loading...")})]}),N&&u.jsx(U,{isOpen:!0,onClose:w,onSave:O,initialProduct:l,isEditMode:p,categories:[{id:"tyres",name:"Tyres & Related Products"},{id:"brakes",name:"Brake Parts & Systems"}]})]}),u.jsx(R,{isOpen:a,onClose:()=>S(!1),onScan:E})]})}export{ne as default};
