import{a1 as l}from"./index-DjNptb3k.js";import{i as m}from"./shippingCompanyAuthService-CmVuORbo.js";async function I(e){try{if(console.log("🚚 [SHIPPING_ORDERS] Getting assigned orders with filters:",e),!await m())return{success:!1,error:"Authentication required"};let t=l.from("orders").select(`
        *,
        order_items (
          id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section
        ),
        shipments (
          id,
          status,
          tracking_number,
          driver_name,
          driver_phone,
          estimated_delivery_date,
          supplier_contact_info
        )
      `);e?.status&&(t=t.eq("status",e.status)),e?.shipment_status&&(t=t.eq("shipments.status",e.shipment_status)),e?.date_from&&(t=t.gte("created_at",e.date_from)),e?.date_to&&(t=t.lte("created_at",e.date_to)),e?.search&&(t=t.or(`order_number.ilike.%${e.search}%,consumer_name.ilike.%${e.search}%,consumer_phone.ilike.%${e.search}%`)),e?.limit&&(t=t.limit(e.limit)),e?.offset&&(t=t.range(e.offset,e.offset+(e.limit||10)-1)),t=t.order("created_at",{ascending:!1});const{data:o,error:c,count:s}=await t;if(c)return console.error("❌ [SHIPPING_ORDERS] Database error:",c),{success:!1,error:"Failed to fetch orders"};if(console.log("✅ [SHIPPING_ORDERS] Raw orders fetched:",o?.length||0),!o||o.length===0)return{success:!0,orders:[],total_count:0};const i=o.map(n=>{const a=n.shipments[0],_=new Map;return n.order_items.forEach(d=>{const p=`${d.supplier_name}-${d.supplier_wilaya}`;_.has(p)||_.set(p,{supplier_name:d.supplier_name,supplier_phone:d.supplier_phone,supplier_city:d.supplier_city,supplier_wilaya:d.supplier_wilaya,item_count:0,total_value:0});const u=_.get(p);u.item_count+=d.quantity,u.total_value+=d.total_price}),{...n,shipment_id:a?.id,shipment_status:a?.status,tracking_number:a?.tracking_number,driver_name:a?.driver_name,driver_phone:a?.driver_phone,estimated_delivery_date:a?.estimated_delivery_date,supplier_contacts:Array.from(_.values())}});return console.log("✅ [SHIPPING_ORDERS] Processed orders:",{total:i.length,sample:i[0]?{order_number:i[0].order_number,supplier_contacts:i[0].supplier_contacts.length,shipment_status:i[0].shipment_status}:null}),{success:!0,orders:i,total_count:s||i.length}}catch(r){return console.error("❌ [SHIPPING_ORDERS] Exception:",r),{success:!1,error:"An unexpected error occurred"}}}async function P(e){try{if(console.log("🚚 [SHIPPING_ORDER_DETAIL] Getting order details for:",e),!await m())return{success:!1,error:"Authentication required"};const{data:t,error:o}=await l.from("orders").select(`
        *,
        order_items (
          id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section
        ),
        shipments (
          id,
          status,
          tracking_number,
          driver_name,
          driver_phone,
          estimated_delivery_date,
          supplier_contact_info
        )
      `).eq("id",e).single();if(o||!t)return console.error("❌ [SHIPPING_ORDER_DETAIL] Order not found:",o),{success:!1,error:"Order not found or not assigned to your company"};const c=new Map;t.order_items.forEach(n=>{const a=`${n.supplier_name}-${n.supplier_wilaya}`;c.has(a)||c.set(a,{supplier_name:n.supplier_name,supplier_phone:n.supplier_phone,supplier_city:n.supplier_city,supplier_wilaya:n.supplier_wilaya,item_count:0,total_value:0});const _=c.get(a);_.item_count+=n.quantity,_.total_value+=n.total_price});const s=t.shipments[0],i={...t,shipment_id:s?.id,shipment_status:s?.status,tracking_number:s?.tracking_number,driver_name:s?.driver_name,driver_phone:s?.driver_phone,estimated_delivery_date:s?.estimated_delivery_date,supplier_contacts:Array.from(c.values())};return console.log("✅ [SHIPPING_ORDER_DETAIL] Order details retrieved:",{order_number:i.order_number,supplier_contacts:i.supplier_contacts.length,shipment_status:i.shipment_status}),{success:!0,order:i}}catch(r){return console.error("❌ [SHIPPING_ORDER_DETAIL] Exception:",r),{success:!1,error:"An unexpected error occurred"}}}async function E(e,r){try{if(console.log("🔄 [UPDATE_SHIPPING_ORDER_STATUS] Updating order status:",{orderId:e,updateData:r}),!await m())return{success:!1,error:"Authentication required"};const{data:o,error:c}=await l.from("shipments").select("id").eq("order_id",e).single();if(c||!o)return console.error("❌ [UPDATE_SHIPPING_ORDER_STATUS] Shipment not found:",c),{success:!1,error:"Shipment not found for this order"};const s={status:r.status,updated_at:new Date().toISOString()};r.tracking_number&&(s.tracking_number=r.tracking_number),r.driver_name&&(s.driver_name=r.driver_name),r.driver_phone&&(s.driver_phone=r.driver_phone),r.estimated_delivery_date&&(s.estimated_delivery_date=r.estimated_delivery_date),r.notes&&(s.notes=r.notes),r.status==="picked_up"?s.picked_up_at=new Date().toISOString():r.status==="delivered"&&(s.delivered_at=new Date().toISOString());const{error:i}=await l.from("shipments").update(s).eq("id",o.id);if(i)return console.error("❌ [UPDATE_SHIPPING_ORDER_STATUS] Database error:",i),{success:!1,error:"Failed to update shipment status"};if(r.status==="delivered"){const{error:n}=await l.from("orders").update({status:"delivered",delivered_at:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("id",e);n&&console.error("❌ [UPDATE_SHIPPING_ORDER_STATUS] Error updating order status:",n)}return console.log("✅ [UPDATE_SHIPPING_ORDER_STATUS] Order status updated successfully"),{success:!0}}catch(t){return console.error("❌ [UPDATE_SHIPPING_ORDER_STATUS] Exception:",t),{success:!1,error:"An unexpected error occurred"}}}async function T(){try{if(console.log("📊 [SHIPPING_STATS] Getting shipping company statistics..."),!await m())return{success:!1,error:"Authentication required"};const{data:r,error:t}=await l.from("orders").select(`
        id,
        status,
        total_amount,
        created_at,
        delivered_at,
        shipments!inner (
          id,
          status,
          created_at,
          delivery_cost
        )
      `);if(t)return console.error("❌ [SHIPPING_STATS] Database error:",t),{success:!1,error:"Failed to fetch statistics"};if(!r||r.length===0)return{success:!0,stats:{total_assigned_orders:0,pending_pickup:0,in_transit:0,delivered_today:0,total_revenue:0,average_delivery_time:0}};const o=new Date,c=new Date(o.getFullYear(),o.getMonth(),o.getDate());let s=0,i=0,n=0,a=0;const _={unassigned:0,assigned:0,picked_up:0,in_transit:0,delivered:0};r.forEach(u=>{const h=u.shipments[0];if(h&&(_[h.status]++,s+=h.delivery_cost||0,u.delivered_at)){const g=new Date(u.delivered_at);g>=c&&i++;const S=new Date(u.created_at),f=g.getTime()-S.getTime();n+=f,a++}});const d=a>0?Math.round(n/a/(1e3*60*60*24)):0,p={total_assigned_orders:r.length,pending_pickup:_.assigned,in_transit:_.picked_up+_.in_transit,delivered_today:i,total_revenue:s,average_delivery_time:d};return console.log("✅ [SHIPPING_STATS] Statistics calculated:",p),{success:!0,stats:p}}catch(e){return console.error("❌ [SHIPPING_STATS] Exception:",e),{success:!1,error:"An unexpected error occurred"}}}export{I as a,P as b,T as g,E as u};
