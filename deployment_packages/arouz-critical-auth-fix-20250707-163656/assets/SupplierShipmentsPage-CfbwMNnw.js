import{u as L,a as D,r as a,j as e,B as u,a6 as M,C as y,k as S,Q as _,l as b,aw as R,I as B,P as h,m as U,N as C,U as F,K as w,w as z}from"./index-DjNptb3k.js";import{A as G}from"./AdminLayout-DKbCMQsb.js";import{S as H,a as V,b as K,c as O,d as i}from"./select-B_ZRrAqd.js";import{S as Q}from"./skeleton-DsZ9ndsd.js";import{b as Y}from"./supplierShippingService-C437vNMy.js";import{B as $}from"./building-2-BMEfI4Am.js";import{C as q}from"./calendar-DQkywbJQ.js";import{P as J}from"./phone-Bnuz4FGX.js";import{E as W}from"./external-link-CFrxpCj2.js";import"./house-CifaDVdO.js";import"./settings-C2Lkvf25.js";const X=t=>{switch(t){case"unassigned":return z;case"assigned":return C;case"picked_up":return w;case"in_transit":return w;case"delivered":return F;default:return h}},Z=t=>{switch(t){case"unassigned":return"secondary";case"assigned":return"default";case"picked_up":return"default";case"in_transit":return"default";case"delivered":return"default";default:return"secondary"}},k=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});function oe(){L();const{toast:t}=D(),[p,P]=a.useState([]),[T,f]=a.useState(!0),[N,v]=a.useState(!1),[d,E]=a.useState(""),[n,I]=a.useState("all"),[r,g]=a.useState(1),[m,A]=a.useState(0),c=10,j=async(s=!1)=>{try{s?v(!0):f(!0),console.log("📦 [SUPPLIER_SHIPMENTS_PAGE] Loading shipments...");const l={status:n==="all"?void 0:n,search:d||void 0,limit:c,offset:(r-1)*c},x=await Y(l);x.success?(P(x.shipments||[]),A(x.total_count||0)):t({title:"Failed to load shipments",description:x.error||"Please try again",variant:"destructive"})}catch(l){console.error("❌ [SUPPLIER_SHIPMENTS_PAGE] Error loading shipments:",l),t({title:"Failed to load shipments",description:"Please try again",variant:"destructive"})}finally{f(!1),v(!1)}};a.useEffect(()=>{j()},[r,n]),a.useEffect(()=>{const s=setTimeout(()=>{r===1?j():g(1)},500);return()=>clearTimeout(s)},[d]);const o=Math.ceil(m/c);return e.jsx(G,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Shipments Management"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Track and manage shipments for your orders"})]}),e.jsxs(u,{onClick:()=>j(!0),disabled:N,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(M,{className:`h-4 w-4 ${N?"animate-spin":""}`}),"Refresh"]})]}),e.jsxs(y,{children:[e.jsx(S,{children:e.jsx(_,{className:"text-lg",children:"Filter Shipments"})}),e.jsx(b,{children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(R,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx(B,{placeholder:"Search by order number, company, or tracking number...",value:d,onChange:s=>E(s.target.value),className:"pl-10"})]})}),e.jsx("div",{className:"w-full sm:w-48",children:e.jsxs(H,{value:n,onValueChange:I,children:[e.jsx(V,{children:e.jsx(K,{placeholder:"Filter by status"})}),e.jsxs(O,{children:[e.jsx(i,{value:"all",children:"All Statuses"}),e.jsx(i,{value:"unassigned",children:"Unassigned"}),e.jsx(i,{value:"assigned",children:"Assigned"}),e.jsx(i,{value:"picked_up",children:"Picked Up"}),e.jsx(i,{value:"in_transit",children:"In Transit"}),e.jsx(i,{value:"delivered",children:"Delivered"})]})]})})]})})]}),e.jsxs(y,{children:[e.jsx(S,{children:e.jsxs(_,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5 text-[#fa7b00]"}),"Shipments (",m,")"]})}),e.jsxs(b,{children:[T?e.jsx("div",{className:"space-y-4",children:[...Array(5)].map((s,l)=>e.jsx(Q,{className:"h-32"},l))}):p.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(h,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No shipments found"}),e.jsx("p",{className:"text-gray-600",children:d||n!=="all"?"Try adjusting your filters or search terms":"Shipments for your orders will appear here"})]}):e.jsx("div",{className:"space-y-4",children:p.map(s=>{const l=X(s.status);return e.jsxs("div",{className:"border rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(l,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold text-lg",children:["Order #",s.orders?.order_number||"N/A"]}),e.jsxs("p",{className:"text-gray-600 text-sm",children:["Shipment ID: ",s.id.slice(0,8),"..."]})]})]}),e.jsx(U,{variant:Z(s.status),className:"text-sm px-3 py-1",children:s.status.charAt(0).toUpperCase()+s.status.slice(1).replace("_"," ")})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4",children:[s.company_name&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-4 w-4 text-gray-400"}),e.jsx("span",{className:"text-sm font-medium",children:s.company_name})]}),s.tracking_number&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm",children:["Tracking: ",s.tracking_number]})]}),s.estimated_delivery_date&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm",children:["ETA: ",new Date(s.estimated_delivery_date).toLocaleDateString()]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm",children:["Created: ",k(s.created_at)]})]})]}),s.driver_name&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("h4",{className:"font-medium text-sm mb-2",children:"Driver Information"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm",children:s.driver_name}),s.driver_phone&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(J,{className:"h-3 w-3 text-gray-400"}),e.jsx("span",{className:"text-sm",children:s.driver_phone})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Assigned by: ",s.assigned_by==="supplier"?"You":s.assigned_by,s.assigned_at&&e.jsxs("span",{className:"ml-2",children:["on ",k(s.assigned_at)]})]}),s.tracking_url&&e.jsxs(u,{variant:"outline",size:"sm",onClick:()=>window.open(s.tracking_url,"_blank"),className:"flex items-center gap-2",children:[e.jsx(W,{className:"h-4 w-4"}),"Track Package"]})]})]},s.id)})}),o>1&&e.jsxs("div",{className:"flex items-center justify-between mt-6 pt-6 border-t",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Showing ",(r-1)*c+1," to ",Math.min(r*c,m)," of ",m," shipments"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(u,{variant:"outline",size:"sm",onClick:()=>g(s=>Math.max(1,s-1)),disabled:r===1,children:"Previous"}),e.jsxs("span",{className:"text-sm px-3 py-1 bg-gray-100 rounded",children:[r," of ",o]}),e.jsx(u,{variant:"outline",size:"sm",onClick:()=>g(s=>Math.min(o,s+1)),disabled:r===o,children:"Next"})]})]})]})]})]})})}export{oe as default};
