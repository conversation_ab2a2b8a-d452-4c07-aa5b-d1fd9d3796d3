import{c as re,j as e,L as V,R as K,cq as Q,D as ne,d as oe,e as te,f as se,aZ as le,h as de,B as X,r as h,cr as ie,cs as ce,ct as H,cu as G,cv as me,d5 as I}from"./index-DjNptb3k.js";import{E as B,V as ge}from"./enhanced-dropdown-BWor6myK.js";import{S as C}from"./skeleton-DsZ9ndsd.js";import{T as ye,a as pe,b as q,c as O,d as ue,e as $}from"./table-DaiMNlDc.js";import{A as he}from"./AdminLayout-DKbCMQsb.js";import"./house-CifaDVdO.js";import"./settings-C2Lkvf25.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=re("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]),xe=({brands:a,models:t,engines:r,filters:s,onFilterChange:l})=>{const d=s.brand,m=a.map(n=>({value:n.name,label:n.name,image_url:n.image_url})),i=t.filter(n=>n&&n.uniqueId&&n.model&&n.generation&&n.bodyType);i.length<t.length&&console.warn(`SearchFilters: Filtered out ${t.length-i.length} invalid models`);const x=i.map(n=>({value:n.uniqueId,label:n.fullName||`${n.model} (${n.generation})`,model:n.model,generation:n.generation,bodyType:n.bodyType,dateRange:n.dateRange}));if(console.log(`Rendering ${x.length} model options`),x.length>0){const n=[...new Set(x.map(w=>w.model))];if(console.log(`SearchFilters: Unique model names (${n.length}):`,n.join(", ")),d==="KIA"){const j=["PICANTO","SPORTAGE","CERATO","SORENTO","CEE'D","OPTIMA","CARENS","SOUL","BONGO","STONIC","SELTOS","NIRO","PEGAS","PROCEED","MAGENTIS","XCEED","VENGA","PRIDE","SHUMA / SEPHIA / MENTOR","SEDONA / CARNIVAL","PREGIO","BESTA","CLARUS","AVELLA","RETONA","JOICE","ENTERPRISE","ELAN","POTENTIA","OPIRUS","QUORIS / K9 / K900","STINGER","EV6","EV9","RIO"].filter(k=>!n.includes(k));j.length>0?console.warn(`SearchFilters: Missing KIA models (${j.length}):`,j.join(", ")):console.log("SearchFilters: All expected KIA models are present")}}const b=r.map(n=>({value:n.fullName,label:n.fullName,code:n.code,fuel_type:n.fuel_type,power_kW:n.power_kW,power_HP:n.power_HP}));console.log(`Rendering ${b.length} engine options`);const P=n=>!n||!n.model?(console.warn("Invalid option in groupModelsByName:",n),"Unknown"):n.model,c=n=>n.fuel_type||"Other",g=n=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex-shrink-0 w-7 h-7 rounded-md bg-gray-100 flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium text-gray-500",children:n.label.charAt(0)})}),e.jsx("span",{className:"font-medium",children:n.label})]}),F=n=>e.jsxs("div",{className:"flex flex-col py-0.5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium",children:n.model}),e.jsx("span",{className:"text-xs bg-gray-100 px-1 rounded",children:n.generation}),e.jsx("span",{className:"text-xs text-blue-600",children:n.bodyType})]}),e.jsx("span",{className:"text-xs text-muted-foreground",children:n.dateRange})]}),D=n=>{const w=n.label.match(/^(.*?)\s*\(/),j=w?w[1].trim():"",k=n.label.match(/\(([^)]+)\)/),L=k?k[1]:"",v=n.label.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/),R=v?`${v[1]} kW / ${v[2]} HP`:"",E=n.label.match(/\(([^)]+)\)$/),p=E?E[1]:"";return e.jsxs("div",{className:"flex flex-col py-0.5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium text-sm",children:j}),e.jsx("span",{className:"text-xs bg-gray-100 px-1 rounded",children:L})]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[e.jsx("span",{className:"text-blue-600",children:R}),e.jsx("span",{className:"text-gray-500",children:p})]})]})};return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(V,{htmlFor:"brand-select",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-orange-500 text-white mr-2",children:"1"}),e.jsx("span",{children:"Select car brand"})]})}),e.jsx(B,{options:m,value:s.brand,onChange:n=>l("brand",n),placeholder:"Search brands...",searchPlaceholder:"Search for a brand...",renderOption:g,emptyMessage:"No brands found",maxHeight:"200px"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(V,{htmlFor:"model-select",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2",children:"2"}),e.jsx("span",{children:"Select Model"})]})}),e.jsx(B,{options:x,value:s.model,onChange:n=>l("model",n),placeholder:"Search models...",searchPlaceholder:"Search for a model...",renderOption:F,groupBy:P,emptyMessage:"No models found",disabled:!s.brand,maxHeight:"200px"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(V,{htmlFor:"engine-select",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2",children:"3"}),e.jsx("span",{children:"Select engine (type)"})]})}),e.jsx(B,{options:b,value:s.engineType,onChange:n=>l("engineType",n),placeholder:"Search engine types...",searchPlaceholder:"Search for an engine...",renderOption:D,groupBy:c,emptyMessage:"No engines found",disabled:!s.model,maxHeight:"200px"})]})]})},be=()=>e.jsx("div",{className:"h-full rounded-2xl overflow-hidden border border-gray-200 bg-white",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsxs("div",{className:"flex items-start mb-5",children:[e.jsx("div",{className:"flex-shrink-0 mr-4",children:e.jsx(C,{className:"w-12 h-12 rounded-xl"})}),e.jsxs("div",{children:[e.jsx(C,{className:"h-6 w-32 mb-2"}),e.jsx(C,{className:"h-4 w-24 mb-2"}),e.jsx(C,{className:"h-4 w-20"})]})]}),e.jsxs("div",{className:"border-t border-gray-100 pt-5",children:[e.jsx(C,{className:"h-12 w-full rounded-xl mb-5"}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex flex-col bg-gray-50 rounded-xl p-3",children:[e.jsx(C,{className:"h-3 w-20 mb-2"}),e.jsx(C,{className:"h-6 w-8"})]}),e.jsxs("div",{className:"flex flex-col bg-gray-50 rounded-xl p-3",children:[e.jsx(C,{className:"h-3 w-20 mb-2"}),e.jsx(C,{className:"h-6 w-8"})]})]})]})]})})}),fe=({vehicle:a,onMarketDemand:t})=>e.jsx("div",{className:"h-full rounded-2xl overflow-hidden border border-gray-200 bg-white transition-all duration-300 hover:shadow-lg hover:translate-y-[-4px]",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsxs("div",{className:"flex items-start mb-5",children:[e.jsx("div",{className:"flex-shrink-0 mr-4",children:e.jsx("div",{className:"w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center",children:e.jsx(Q,{className:"h-6 w-6 text-gray-500"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-1",children:a.brand}),e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("p",{className:"text-base text-gray-600",children:a.model}),a.generation&&e.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full",children:a.generation})]}),e.jsxs("p",{className:"text-sm text-gray-500 mb-2",children:[a.bodyType&&`${a.bodyType} • `,a.yearFrom&&a.yearTo?`${a.yearFrom}${a.yearTo!=="..."?"–"+a.yearTo:"–present"}`:""]}),a.engineDetails?e.jsxs("div",{className:"mt-1 bg-gray-50 px-2 py-1.5 rounded-lg",children:[e.jsxs("p",{className:"text-sm text-gray-600 font-medium flex items-center",children:[e.jsx("span",{className:`w-2 h-2 rounded-full mr-1.5 ${a.engineDetails.fuel_type==="Petrol"?"bg-green-500":a.engineDetails.fuel_type==="Diesel"?"bg-blue-500":a.engineDetails.fuel_type==="Electric"?"bg-purple-500":a.engineDetails.fuel_type==="Hybrid"?"bg-teal-500":"bg-gray-500"}`}),a.engineDetails.fuel_type," Engine"]}),a.engineDetails.power_kW&&a.engineDetails.power_HP&&e.jsxs("p",{className:"text-sm text-gray-500",children:[a.engineDetails.power_kW," kW / ",a.engineDetails.power_HP," HP"]}),a.engineDetails.code&&a.engineDetails.code!=="Unknown"&&e.jsxs("p",{className:"text-xs text-gray-400 mt-0.5",children:["Code: ",a.engineDetails.code]})]}):a.engineType?e.jsx("p",{className:"text-sm text-gray-500",children:a.engineType}):null]})]}),e.jsxs("div",{className:"border-t border-gray-100 pt-5",children:[e.jsxs("button",{type:"button",className:"w-full mb-5 py-3 px-4 rounded-xl bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-800 font-medium flex items-center justify-center gap-2 transition-colors",onClick:()=>t(a.id),children:[e.jsx(Z,{className:"h-4 w-4 text-primary"})," Market Demand"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex flex-col bg-gray-50 rounded-xl p-3",children:[e.jsx("span",{className:"text-xs text-gray-500 mb-1",children:"My Listings"}),e.jsx("span",{className:"text-lg font-bold text-gray-900",children:a.myProductCount})]}),e.jsxs("div",{className:"flex flex-col bg-gray-50 rounded-xl p-3",children:[e.jsx("span",{className:"text-xs text-gray-500 mb-1",children:"All Listings"}),e.jsx("span",{className:"text-lg font-bold text-gray-900",children:a.allProductCount})]})]})]})]})})}),Te=({vehicles:a,loading:t,onMarketDemand:r})=>{const[s,l]=K.useState(1),d=18;K.useEffect(()=>{l(1)},[a.length]);const m=Math.ceil(a.length/d),i=()=>{const c=(s-1)*d,g=c+d;return a.slice(c,g)},x=()=>{if(m<=5)return Array.from({length:m},(n,w)=>w+1);const g=[1],F=Math.max(2,s-1),D=Math.min(m-1,s+1);F>2&&g.push("...");for(let n=F;n<=D;n++)g.push(n);return D<m-1&&g.push("..."),m>1&&g.push(m),g},b=c=>{c!=="..."&&(l(c),window.scrollTo({top:document.getElementById("vehicle-grid").offsetTop-100,behavior:"smooth"}))};if(t)return e.jsx("div",{id:"vehicle-grid",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array(9).fill(0).map((c,g)=>e.jsx("div",{children:e.jsx(be,{})},`skeleton-${g}`))});if(a.length===0)return e.jsxs("div",{id:"vehicle-grid",className:"text-center py-16 px-4 bg-white rounded-2xl border border-gray-200 shadow-sm",children:[e.jsx("div",{className:"w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-6",children:e.jsx(Q,{className:"h-10 w-10 text-gray-400"})}),e.jsx("h3",{className:"text-xl font-medium mb-3 text-gray-900",children:"No vehicles found"}),e.jsx("p",{className:"text-gray-500 max-w-md mx-auto",children:"Try adjusting your filters or selecting a different vehicle type to find compatible vehicles."})]});const P=i();return e.jsxs("div",{children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Showing ",P.length," of ",a.length," vehicles",m>1&&` (Page ${s} of ${m})`]})}),e.jsx("div",{id:"vehicle-grid",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:P.map(c=>e.jsx("div",{children:e.jsx(fe,{vehicle:c,onMarketDemand:r})},c.id))}),m>1&&e.jsx("div",{className:"flex justify-center mt-10",children:e.jsxs("nav",{className:"flex items-center gap-2",children:[e.jsx("button",{type:"button",onClick:()=>b(Math.max(1,s-1)),disabled:s===1,className:"h-10 w-10 rounded-full flex items-center justify-center border-2 border-gray-200 text-gray-600 disabled:opacity-40 disabled:cursor-not-allowed hover:border-gray-300 transition-colors",children:"<"}),x().map((c,g)=>e.jsx("button",{type:"button",onClick:()=>b(c),disabled:c==="...",className:`h-10 min-w-10 px-2 rounded-full flex items-center justify-center border-2 transition-colors ${c==="..."?"border-transparent text-gray-600 cursor-default":s===c?"border-primary bg-primary/5 text-primary font-medium":"border-gray-200 text-gray-600 hover:border-gray-300"}`,children:c},`page-${g}-${c}`)),e.jsx("button",{type:"button",onClick:()=>b(Math.min(m,s+1)),disabled:s===m,className:"h-10 w-10 rounded-full flex items-center justify-center border-2 border-gray-200 text-gray-600 disabled:opacity-40 disabled:cursor-not-allowed hover:border-gray-300 transition-colors",children:">"})]})})]})},je=({open:a,onOpenChange:t,vehicle:r,demandData:s})=>r?e.jsx(ne,{open:a,onOpenChange:t,children:e.jsxs(oe,{className:"sm:max-w-[700px]",children:[e.jsx(te,{children:e.jsxs(se,{className:"flex items-center gap-2",children:[e.jsx(Z,{className:"h-5 w-5 text-primary"}),"Market Demand for ",r.brand," ",r.model]})}),e.jsxs("div",{className:"mb-6 pb-4 border-b",children:[e.jsxs("h4",{className:"text-lg font-semibold mb-1",children:[r.brand," ",r.model]}),e.jsxs("p",{className:"text-muted-foreground",children:["Engine: ",r.engineType]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-base font-medium mb-4",children:"Top Searched Parts"}),s&&s.length>0?e.jsxs(ye,{children:[e.jsx(pe,{children:e.jsxs(q,{children:[e.jsx(O,{children:"Part Category"}),e.jsx(O,{children:"Search Volume"}),e.jsx(O,{children:"Trend"})]})}),e.jsx(ue,{children:s.map((l,d)=>e.jsxs(q,{children:[e.jsx($,{children:l.category}),e.jsx($,{children:l.volume}),e.jsxs($,{className:le(l.trend==="up"?"text-green-600 font-medium":"text-red-600 font-medium"),children:[l.trend==="up"?"↑":"↓"," ",l.trendPercentage,"%"]})]},d))})]}):e.jsx("p",{className:"text-center py-6 text-muted-foreground italic",children:"No market demand data available for this vehicle."})]}),e.jsx(de,{children:e.jsx(X,{variant:"outline",onClick:()=>t(!1),children:"Close"})})]})}):null,W=[{id:"alg-1",type:"car",brand:"Renault",model:"Symbol",engineType:"1.6L 16V",yearFrom:2017,yearTo:2023},{id:"alg-2",type:"car",brand:"Renault",model:"Symbol",engineType:"1.5 dCi",yearFrom:2017,yearTo:2023},{id:"alg-3",type:"car",brand:"Renault",model:"Clio",engineType:"1.2L 16V",yearFrom:2016,yearTo:2022},{id:"alg-4",type:"car",brand:"Renault",model:"Clio",engineType:"1.5 dCi",yearFrom:2016,yearTo:2022},{id:"alg-5",type:"car",brand:"Renault",model:"Megane",engineType:"1.6L 16V",yearFrom:2016,yearTo:2022},{id:"alg-6",type:"car",brand:"Renault",model:"Duster",engineType:"1.6L 16V",yearFrom:2018,yearTo:2023},{id:"alg-7",type:"car",brand:"Renault",model:"Duster",engineType:"1.5 dCi",yearFrom:2018,yearTo:2023},{id:"alg-8",type:"car",brand:"Dacia",model:"Logan",engineType:"1.6L MPI",yearFrom:2016,yearTo:2022},{id:"alg-9",type:"car",brand:"Dacia",model:"Logan",engineType:"1.5 dCi",yearFrom:2016,yearTo:2022},{id:"alg-10",type:"car",brand:"Dacia",model:"Sandero",engineType:"1.6L MPI",yearFrom:2017,yearTo:2023},{id:"alg-11",type:"car",brand:"Dacia",model:"Sandero",engineType:"1.5 dCi",yearFrom:2017,yearTo:2023},{id:"alg-12",type:"car",brand:"Dacia",model:"Duster",engineType:"1.6L 16V",yearFrom:2018,yearTo:2023},{id:"alg-13",type:"car",brand:"Peugeot",model:"208",engineType:"1.2 PureTech",yearFrom:2019,yearTo:2023},{id:"alg-14",type:"car",brand:"Peugeot",model:"208",engineType:"1.6 HDi",yearFrom:2019,yearTo:2023},{id:"alg-15",type:"car",brand:"Peugeot",model:"301",engineType:"1.6 VTi",yearFrom:2017,yearTo:2023},{id:"alg-16",type:"car",brand:"Peugeot",model:"301",engineType:"1.6 HDi",yearFrom:2017,yearTo:2023},{id:"alg-17",type:"car",brand:"Peugeot",model:"3008",engineType:"1.6 THP",yearFrom:2018,yearTo:2023},{id:"alg-18",type:"car",brand:"Citroen",model:"C3",engineType:"1.2 PureTech",yearFrom:2018,yearTo:2023},{id:"alg-19",type:"car",brand:"Citroen",model:"C-Elysée",engineType:"1.6 VTi",yearFrom:2017,yearTo:2023},{id:"alg-20",type:"car",brand:"Citroen",model:"C-Elysée",engineType:"1.6 HDi",yearFrom:2017,yearTo:2023},{id:"alg-21",type:"car",brand:"Hyundai",model:"i10",engineType:"1.2L Kappa",yearFrom:2018,yearTo:2023},{id:"alg-22",type:"car",brand:"Hyundai",model:"Grand i10",engineType:"1.2L Kappa",yearFrom:2018,yearTo:2023},{id:"alg-23",type:"car",brand:"Hyundai",model:"Accent",engineType:"1.6L MPI",yearFrom:2017,yearTo:2023},{id:"alg-24",type:"car",brand:"Hyundai",model:"Elantra",engineType:"1.6L MPI",yearFrom:2017,yearTo:2023},{id:"alg-25",type:"car",brand:"Hyundai",model:"Tucson",engineType:"2.0L MPI",yearFrom:2018,yearTo:2023},{id:"alg-26",type:"car",brand:"Hyundai",model:"Creta",engineType:"1.6L MPI",yearFrom:2019,yearTo:2023},{id:"alg-27",type:"car",brand:"Kia",model:"Picanto",engineType:"1.2L Kappa",yearFrom:2018,yearTo:2023},{id:"alg-28",type:"car",brand:"Kia",model:"Rio",engineType:"1.4L MPI",yearFrom:2017,yearTo:2023},{id:"alg-29",type:"car",brand:"Kia",model:"Cerato",engineType:"1.6L MPI",yearFrom:2018,yearTo:2023},{id:"alg-30",type:"car",brand:"Kia",model:"Sportage",engineType:"2.0L MPI",yearFrom:2018,yearTo:2023},{id:"alg-31",type:"car",brand:"Volkswagen",model:"Polo",engineType:"1.6L MPI",yearFrom:2018,yearTo:2023},{id:"alg-32",type:"car",brand:"Volkswagen",model:"Golf",engineType:"1.6L MPI",yearFrom:2017,yearTo:2023},{id:"alg-33",type:"car",brand:"Volkswagen",model:"Tiguan",engineType:"2.0 TDI",yearFrom:2018,yearTo:2023},{id:"alg-34",type:"car",brand:"Volkswagen",model:"Passat",engineType:"2.0 TDI",yearFrom:2017,yearTo:2023},{id:"alg-35",type:"car",brand:"Toyota",model:"Yaris",engineType:"1.5L",yearFrom:2018,yearTo:2023},{id:"alg-36",type:"car",brand:"Toyota",model:"Corolla",engineType:"1.6L",yearFrom:2017,yearTo:2023},{id:"alg-37",type:"car",brand:"Toyota",model:"Hilux",engineType:"2.4L Diesel",yearFrom:2016,yearTo:2023},{id:"alg-38",type:"car",brand:"Toyota",model:"Fortuner",engineType:"2.7L",yearFrom:2017,yearTo:2023},{id:"alg-39",type:"car",brand:"Toyota",model:"Land Cruiser",engineType:"4.0L V6",yearFrom:2016,yearTo:2023},{id:"alg-40",type:"car",brand:"Seat",model:"Ibiza",engineType:"1.6L MPI",yearFrom:2018,yearTo:2023},{id:"alg-41",type:"car",brand:"Seat",model:"Leon",engineType:"1.6L MPI",yearFrom:2018,yearTo:2023},{id:"alg-42",type:"car",brand:"Seat",model:"Arona",engineType:"1.6L MPI",yearFrom:2019,yearTo:2023},{id:"alg-43",type:"car",brand:"Fiat",model:"Tipo",engineType:"1.4L Fire",yearFrom:2018,yearTo:2023},{id:"alg-44",type:"car",brand:"Fiat",model:"Tipo",engineType:"1.6L E.torQ",yearFrom:2018,yearTo:2023},{id:"alg-45",type:"car",brand:"Fiat",model:"500",engineType:"1.2L Fire",yearFrom:2018,yearTo:2023},{id:"alg-46",type:"car",brand:"Chevrolet",model:"Spark",engineType:"1.2L",yearFrom:2017,yearTo:2022},{id:"alg-47",type:"car",brand:"Chevrolet",model:"Aveo",engineType:"1.4L",yearFrom:2017,yearTo:2022},{id:"alg-48",type:"car",brand:"Chevrolet",model:"Aveo",engineType:"1.6L",yearFrom:2017,yearTo:2022},{id:"alg-49",type:"motorcycle",brand:"Honda",model:"CB125F",engineType:"125cc",yearFrom:2018,yearTo:2023},{id:"alg-50",type:"motorcycle",brand:"Honda",model:"CB150R",engineType:"150cc",yearFrom:2018,yearTo:2023},{id:"alg-51",type:"motorcycle",brand:"Yamaha",model:"YBR125",engineType:"125cc",yearFrom:2018,yearTo:2023},{id:"alg-52",type:"motorcycle",brand:"Yamaha",model:"FZ-S",engineType:"150cc",yearFrom:2018,yearTo:2023},{id:"alg-53",type:"motorcycle",brand:"Sym",model:"Symphony",engineType:"150cc",yearFrom:2018,yearTo:2023},{id:"alg-54",type:"motorcycle",brand:"Vespa",model:"Primavera",engineType:"125cc",yearFrom:2018,yearTo:2023},{id:"alg-55",type:"motorcycle",brand:"Vespa",model:"GTS",engineType:"300cc",yearFrom:2018,yearTo:2023},{id:"alg-56",type:"motorcycle",brand:"Bajaj",model:"Pulsar",engineType:"150cc",yearFrom:2018,yearTo:2023},{id:"alg-57",type:"motorcycle",brand:"Bajaj",model:"Pulsar",engineType:"180cc",yearFrom:2018,yearTo:2023},{id:"alg-58",type:"motorcycle",brand:"Bajaj",model:"Boxer",engineType:"150cc",yearFrom:2018,yearTo:2023},{id:"alg-59",type:"truck",brand:"Isuzu",model:"NMR85",engineType:"3.0L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-60",type:"truck",brand:"Isuzu",model:"NPR75",engineType:"4.0L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-61",type:"truck",brand:"Isuzu",model:"FVR",engineType:"7.8L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-62",type:"truck",brand:"Hyundai",model:"HD65",engineType:"3.9L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-63",type:"truck",brand:"Hyundai",model:"HD72",engineType:"3.9L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-64",type:"truck",brand:"Hyundai",model:"HD78",engineType:"3.9L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-65",type:"truck",brand:"Mitsubishi",model:"Canter",engineType:"3.9L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-66",type:"truck",brand:"Mitsubishi",model:"Fuso",engineType:"4.9L Diesel",yearFrom:2017,yearTo:2023},{id:"alg-67",type:"truck",brand:"Mercedes-Benz",model:"Actros",engineType:"12.8L Diesel",yearFrom:2016,yearTo:2023},{id:"alg-68",type:"truck",brand:"Mercedes-Benz",model:"Atego",engineType:"7.7L Diesel",yearFrom:2016,yearTo:2023},{id:"alg-69",type:"truck",brand:"Renault",model:"Kerax",engineType:"11.0L Diesel",yearFrom:2016,yearTo:2023},{id:"alg-70",type:"truck",brand:"Renault",model:"Midlum",engineType:"7.2L Diesel",yearFrom:2016,yearTo:2023},{id:"alg-71",type:"truck",brand:"MAN",model:"TGS",engineType:"12.4L Diesel",yearFrom:2016,yearTo:2023},{id:"alg-72",type:"truck",brand:"MAN",model:"TGX",engineType:"12.4L Diesel",yearFrom:2016,yearTo:2023}],Ne="https://vpic.nhtsa.dot.gov/api/vehicles",z=async(a,t={})=>{try{const r=new URLSearchParams({format:"json",...t}).toString(),s=`${Ne}${a}?${r}`,l=await fetch(s);if(!l.ok)throw new Error(`NHTSA API error: ${l.status}`);return await l.json()}catch(r){return console.error("Error fetching from NHTSA API:",r),{Results:[]}}},Y=a=>{const t=a.id||(a.Make+a.Model).length,r=Math.max(5,Math.floor(t*7%30)),s=Math.max(r,Math.floor(t*13%50));return{myProductCount:r,allProductCount:s}},_=(a,t)=>t==="nhtsa"?{id:a.MakeId+"-"+a.ModelId,type:"car",brand:a.Make_Name||a.Make,model:a.Model_Name||a.Model,engineType:a.EngineConfiguration||"Standard",yearFrom:a.ModelYear||2015,yearTo:a.ModelYear?parseInt(a.ModelYear)+5:2023,...Y(a)}:t==="algerian"?{id:a.id||`alg-${a.brand}-${a.model}`.replace(/\s+/g,"-").toLowerCase(),type:a.type||"car",brand:a.brand,model:a.model,engineType:a.engineType,yearFrom:a.yearFrom||2015,yearTo:a.yearTo||2023,...Y(a)}:a,Fe=async(a={})=>{try{let t=[...W].map(r=>_(r,"algerian"));if(!a.brand||!t.some(r=>r.brand===a.brand)){let r=[];if(a.brand)r=(await z("/GetModelsForMake/"+a.brand)).Results||[];else{const d=["Toyota","Volkswagen","Ford","Honda","Hyundai","Renault","Peugeot"];for(const m of d){const i=await z("/GetModelsForMake/"+m);r=[...r,...i.Results||[]]}}const s=r.map(d=>_(d,"nhtsa")),l=new Set(t.map(d=>d.id));for(const d of s)l.has(d.id)||(t.push(d),l.add(d.id))}return a.type&&(t=t.filter(r=>r.type===a.type)),a.brand&&(t=t.filter(r=>r.brand===a.brand)),a.model&&(t=t.filter(r=>r.model===a.model)),a.engineType&&(t=t.filter(r=>r.engineType===a.engineType)),t}catch(t){return console.error("Error fetching vehicles:",t),W.map(r=>_(r,"algerian"))}},we=async a=>{try{const r=(await Fe()).find(i=>i.id===a);if(!r)return[];const s=typeof a=="string"?a.length:a,l={car:["Brake Pads","Oil Filters","Air Filters","Spark Plugs","Wiper Blades","Headlights","Batteries"],motorcycle:["Chain Kits","Brake Pads","Oil Filters","Spark Plugs","Tires","Batteries","Exhaust Systems"],truck:["Brake Pads","Air Filters","Oil Filters","Fuel Filters","Batteries","Alternators","Suspension Parts"]};return(l[r.type]||l.car).map((i,x)=>{const b=(s+x)*(x+1),P=200+Math.floor(b%1500),c=b%2===0,g=2+Math.floor(b%20);return{category:i,volume:P,trend:c?"up":"down",trendPercentage:g}}).sort((i,x)=>x.volume-i.volume)}catch(t){return console.error("Error fetching market demand:",t),[]}},Ee=()=>{const[a,t]=h.useState("car"),[r,s]=h.useState({brand:"",model:"",engineType:""}),[l,d]=h.useState([]),[m,i]=h.useState(!0),[x,b]=h.useState(!1),[P,c]=h.useState(null),[g,F]=h.useState([]),[D,n]=h.useState([]),[w,j]=h.useState([]),[k,L]=h.useState([]);h.useEffect(()=>{(async()=>{i(!0);try{if(a==="car"){const o=await ie();n(o)}else n([])}catch(o){console.error("Error fetching brands:",o),n([])}finally{i(!1)}})(),s({brand:"",model:"",engineType:""})},[a]),h.useEffect(()=>{(async()=>{if(!r.brand){j([]);return}i(!0);try{if(a==="car"){const o=await ce(r.brand);if(console.log(`CompatibilityHub: Received ${o.length} models for ${r.brand}`),o.length>0){const u=[...new Set(o.map(y=>y.model))];console.log(`CompatibilityHub: Unique model names (${u.length}):`,u.join(", "))}else console.warn(`CompatibilityHub: No models received for ${r.brand}`);j(o)}else j([])}catch(o){console.error("Error fetching models:",o),j([])}finally{i(!1)}})()},[a,r.brand]),h.useEffect(()=>{(async()=>{if(!r.brand||!r.model){L([]);return}i(!0);try{if(a==="car"){const o=r.model;console.log("CompatibilityHub: Fetching engines for model with uniqueId:",o);const{brand:u,model:y,generationCode:T}=H(o),N=u||r.brand;console.log("CompatibilityHub: Using brand:",N,"model:",y,"generation:",T);const M=await G(N,y,T);console.log(`CompatibilityHub: Received ${M.length} engines for ${N} ${y} ${T}`),L(M)}else L([])}catch(o){console.error("Error fetching engines:",o),L([])}finally{i(!1)}})()},[a,r.brand,r.model]),h.useEffect(()=>{(async()=>{i(!0);try{if(a==="car"){let o=[];if(r.brand&&r.model&&r.engineType){console.log("Fetching specific vehicle with engine:",r.engineType),console.log("Selected filters:",r);const{brand:u,model:y,generationCode:T}=H(r.model),N=u||r.brand;console.log("Using brand:",N,"for vehicle lookup");const M=await me(N,r.model,r.engineType);if(M)o=[M],console.log("Found specific vehicle with engine:",M),console.log("Engine details:",M.engineDetails);else{console.warn("No vehicle found with the selected engine, falling back to model search"),console.log("Selected filters that failed:",r);const{brand:J,model:ee,generationCode:ae}=H(r.model),U=J||r.brand,A=await I(U,r.model),f=(await G(U,ee,ae)).find(S=>S.fullName===r.engineType);f?(console.log("Found selected engine:",f),o=A.filter(S=>S.engineDetails?!!(f.code&&S.engineDetails.code===f.code||f.power_kW&&f.power_HP&&S.engineDetails.power_kW===f.power_kW&&S.engineDetails.power_HP===f.power_HP||f.fuel_type&&S.engineDetails.fuel_type===f.fuel_type&&(S.engineDetails.fullName.includes(f.fullName)||f.fullName.includes(S.engineDetails.fullName))):!1),o.length===0&&(console.warn("No vehicles found matching the selected engine properties, showing all model vehicles"),o=A)):(console.warn("Selected engine not found in engines list, showing all model vehicles"),o=A)}}else if(r.brand&&r.model){console.log("Fetching all vehicles for model:",r.model);const{brand:u,model:y,generationCode:T}=H(r.model),N=u||r.brand;console.log("CompatibilityHub: Parsed model uniqueId for vehicle fetching:",{brand:N,model:y,generation:T}),o=await I(N,r.model),console.log(`CompatibilityHub: Received ${o.length} vehicles for brand ${N}, model ${y}`),o.length===0&&console.warn("No vehicles found for the selected model")}else r.brand?(console.log("Fetching all vehicles for brand:",r.brand),o=await I(r.brand),o.sort((u,y)=>u.model.localeCompare(y.model)),o.length===0&&console.warn("No vehicles found for the selected brand")):(console.log("Fetching all vehicles using direct method"),o=await I(),o.sort((u,y)=>{const T=u.brand.localeCompare(y.brand);return T!==0?T:u.model.localeCompare(y.model)}),console.log(`Showing all ${o.length} vehicles`));console.log(`Setting ${o.length} vehicles in state`),d(o)}else d([])}catch(o){console.error("Error fetching vehicles:",o),d([])}finally{i(!1)}})()},[a,r.brand,r.model,r.engineType]);const v=p=>{p!==a&&(i(!0),s({brand:"",model:"",engineType:""}),t(p))},R=(p,o)=>{s(p==="brand"?{brand:o,model:"",engineType:""}:p==="model"?{...r,model:o,engineType:""}:{...r,[p]:o})},E=async p=>{try{const o=l.find(T=>T.id===p);if(!o)return;c(o),b(!0),F([]);const u=setTimeout(()=>{F([{category:"Loading...",volume:"-",trend:"up",trendPercentage:0}])},200),y=await we(p);clearTimeout(u),F(y)}catch(o){console.error("Error fetching market demand:",o),F([{category:"Error loading data",volume:"-",trend:"down",trendPercentage:0}])}};return e.jsx(he,{children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Compatibility Hub"}),e.jsx("p",{className:"text-muted-foreground",children:"Find vehicles compatible with your products and explore market opportunities"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-3xl border shadow-lg",children:[e.jsx(ge,{selectedType:a,onTypeChange:v}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-medium mb-2 md:mb-0",children:"Find Your Vehicle"}),!m&&l.length>0&&e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:!r.brand&&!r.model&&!r.engineType?`Showing all ${l.length} vehicles in the database`:`Showing ${l.length} vehicles matching your filters`})]}),e.jsx("div",{className:"flex gap-2",children:(r.brand||r.model||r.engineType)&&e.jsx(X,{variant:"outline",size:"sm",onClick:()=>s({brand:"",model:"",engineType:""}),className:"self-start md:self-auto rounded-full",children:"Reset Filters"})})]}),e.jsx(xe,{brands:D,models:w,engines:k,filters:r,onFilterChange:R})]})]}),e.jsx("div",{children:e.jsx(Te,{vehicles:l,loading:m,onMarketDemand:E})}),e.jsx(je,{open:x,onOpenChange:b,vehicle:P,demandData:g})]})})};export{Ee as default};
