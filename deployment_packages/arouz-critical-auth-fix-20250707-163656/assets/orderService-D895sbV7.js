const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/phoneAuthService-D3qmvqKw.js","assets/index-DjNptb3k.js","assets/dexatelService-DQVeE1hW.js"])))=>i.map(i=>d[i]);
import{a1 as u,as as T}from"./index-DjNptb3k.js";async function N(){try{console.log("💰 Fetching active AROUZ fees...");const{data:e,error:r}=await u.from("arouz_fees").select("*").eq("is_active",!0).order("fee_type, minimum_order_value");if(r)throw console.error("❌ Error fetching AROUZ fees:",r),r;return console.log("✅ AROUZ fees fetched:",e?.length||0),e||[]}catch(e){throw console.error("❌ Exception in getActiveArouzFees:",e),e}}async function A(e,r,o,s=[]){try{console.log("🧮 Calculating AROUZ fees:",{subtotal:e,shippingCost:r,marketplaceSection:o,categories:s});const a=await N(),c=[];let t=0;const _={marketplace_commission:0,service_fee:0,processing_fee:0};for(const p of a){if(!F(p,e,o,s))continue;const d=v(p,e);c.push(d),t+=d.calculated_amount,p.fee_type==="marketplace_commission"?_.marketplace_commission+=d.calculated_amount:p.fee_type==="service_fee"?_.service_fee+=d.calculated_amount:p.fee_type==="processing_fee"&&(_.processing_fee+=d.calculated_amount)}const l=e+r+t,i={subtotal:e,shipping_cost:r,fees:c,total_fees:t,grand_total:l,fee_summary:_};return console.log("✅ AROUZ fee calculation completed:",i),i}catch(a){return console.error("❌ Exception in calculateArouzFees:",a),{subtotal:e,shipping_cost:r,fees:[],total_fees:0,grand_total:e+r,fee_summary:{marketplace_commission:0,service_fee:0,processing_fee:0}}}}function F(e,r,o,s){return!(r<e.minimum_order_value||e.maximum_order_value&&r>e.maximum_order_value||e.applies_to_sections.length>0&&!e.applies_to_sections.includes(o)||e.applies_to_categories.length>0&&!s.some(c=>e.applies_to_categories.includes(c)))}function v(e,r){let o=0,s=0;switch(e.calculation_method){case"percentage":s=e.percentage_rate*100,o=r*e.percentage_rate;break;case"fixed":o=e.fixed_amount;break;case"tiered":o=e.fixed_amount;break;default:o=0}return{fee_type:e.fee_type,fee_name:M(e.fee_type),calculation_method:e.calculation_method,base_amount:r,rate:s,calculated_amount:Math.round(o*100)/100,description:e.description}}function M(e){switch(e){case"marketplace_commission":return"AROUZ Commission";case"service_fee":return"Service Fee";case"processing_fee":return"Processing Fee";default:return"AROUZ Fee"}}async function V(e,r,o){try{const s=U(o),a=G(o);return await A(e,r,s,a)}catch(s){return console.error("❌ Exception in calculateCheckoutFees:",s),{subtotal:e,shipping_cost:r,fees:[],total_fees:0,grand_total:e+r,fee_summary:{marketplace_commission:0,service_fee:0,processing_fee:0}}}}function U(e){return e.some(o=>o.marketplace_section==="wholesale"||o.section==="wholesale")?"wholesale":"retail"}function G(e){const r=new Set;return e.forEach(o=>{o.category&&r.add(o.category),o.subcategory&&r.add(o.subcategory)}),Array.from(r)}async function S(){try{console.log("🔍 [CONSUMER_INFO] Getting current consumer info..."),console.log("🔍 [CONSUMER_INFO] Checking Supabase auth...");const{data:{user:e}}=await u.auth.getUser();if(console.log("🔍 [CONSUMER_INFO] Supabase user:",{id:e?.id,phone:e?.phone,email:e?.email,role:e?.user_metadata?.role}),e?.phone){console.log("🔍 [CONSUMER_INFO] User has phone, getting profile...");const{data:s}=await u.from("profiles").select("*").eq("id",e.id).single();return console.log("🔍 [CONSUMER_INFO] Supabase auth profile:",s),{phone:e.phone,name:s?.full_name||s?.first_name||null,profile:s}}console.log("🔍 [CONSUMER_INFO] No Supabase phone, checking phone auth session...");const{getStoredPhoneSession:r}=await T(async()=>{const{getStoredPhoneSession:s}=await import("./phoneAuthService-D3qmvqKw.js");return{getStoredPhoneSession:s}},__vite__mapDeps([0,1,2])),o=r();if(console.log("🔍 [CONSUMER_INFO] Phone session:",{hasSession:!!o,userPhone:o?.user?.phone,profilePhone:o?.profile?.phone,profileName:o?.profile?.full_name}),o?.user?.phone){console.log("🔍 [CONSUMER_INFO] Phone session found, getting profile from database...");const{data:s,error:a}=await u.from("profiles").select("*").eq("phone",o.user.phone).eq("role","consumer").single();return console.log("🔍 [CONSUMER_INFO] Phone auth profile query result:",{profile:s,profileError:a}),{phone:o.user.phone,name:s?.full_name||s?.first_name||null,profile:s}}return console.log("❌ [CONSUMER_INFO] No consumer info found"),{phone:null,name:null,profile:null}}catch(e){return console.error("Error getting consumer info:",e),{phone:null,name:null,profile:null}}}async function C(){const{phone:e}=await S();return e}function q(e){return e.map(r=>{const o={product_id:r.originalProductId||r.id.toString(),product_name:r.name,product_image:r.image||null,quantity:r.quantity,unit_price:r.price,total_price:r.price*r.quantity,supplier_account_id:r.supplierAccountId||"",supplier_name:r.supplierName||"Unknown Supplier",supplier_phone:"",supplier_city:"",supplier_wilaya:r.shippingOrigin||"",marketplace_section:r.marketplaceSection||"retail",category:r.category||"",subcategory:r.subcategory||""};return console.log("🔄 Transformed cart item to order item:",{product_name:o.product_name,supplier_name:o.supplier_name,supplier_wilaya:o.supplier_wilaya,supplier_account_id:o.supplier_account_id}),o})}async function k(e,r){try{console.log("🚀 [ORDER_SERVICE] Starting order creation..."),console.log("🚀 [ORDER_SERVICE] Checkout data:",e),console.log("🚀 [ORDER_SERVICE] Cart items count:",r.length),console.log("🔍 [ORDER_SERVICE] Getting consumer info...");const{phone:o,name:s,profile:a}=await S();if(console.log("🔍 [ORDER_SERVICE] Consumer info result:",{phone:o,name:s,profile:a?"Found":"Not found"}),!o)return console.error("❌ [ORDER_SERVICE] No consumer phone found - authentication required"),{success:!1,error:"Authentication required"};if(console.log("✅ [ORDER_SERVICE] Validating checkout data..."),!e.deliveryAddress||!e.deliveryWilaya||!e.paymentMethod)return console.error("❌ [ORDER_SERVICE] Missing required checkout information:",{deliveryAddress:!!e.deliveryAddress,deliveryWilaya:!!e.deliveryWilaya,paymentMethod:!!e.paymentMethod}),{success:!1,error:"Missing required checkout information"};if(r.length===0)return console.error("❌ [ORDER_SERVICE] Cart is empty"),{success:!1,error:"Cart is empty"};console.log("✅ [ORDER_SERVICE] Validation passed"),console.log("🔄 [ORDER_SERVICE] Transforming cart items..."),console.log("🔄 [ORDER_SERVICE] ORIGINAL CART ITEMS:",r.map(n=>({id:n.id,name:n.name,price:n.price,quantity:n.quantity,supplierName:n.supplierName,supplierAccountId:n.supplierAccountId,marketplaceSection:n.marketplaceSection,shippingOrigin:n.shippingOrigin})));const c=q(r);console.log("🔄 [ORDER_SERVICE] TRANSFORMED ORDER ITEMS:",c.map(n=>({product_name:n.product_name,supplier_name:n.supplier_name,supplier_account_id:n.supplier_account_id,marketplace_section:n.marketplace_section,supplier_wilaya:n.supplier_wilaya,total_price:n.total_price}))),console.log("📋 [ORDER_SERVICE] Preparing order data...");const t={consumer_phone:o,consumer_name:s,payment_method:e.paymentMethod,delivery_address:e.deliveryAddress,delivery_coordinates:e.deliveryCoordinates,delivery_wilaya:e.deliveryWilaya,google_maps_url:e.googleMapsUrl,special_instructions:e.specialInstructions||null,order_items:c,shipping_company_id:e.shippingInfo?.company_id,shipping_cost:e.shippingInfo?.shipping_cost,shipping_notes:e.shippingInfo?.shipping_notes,shipping_method:e.shippingInfo?.shipping_method};console.log("📋 [ORDER_SERVICE] Final order data being sent to database:",t),console.log("💾 [ORDER_SERVICE] Creating order directly (bypassing RLS)...");const _=`ARZ-${new Date().toISOString().slice(0,10).replace(/-/g,"")}-${Math.floor(Math.random()*1e4).toString().padStart(4,"0")}`,l=c.reduce((n,w)=>n+w.total_price,0),i=t.shipping_cost||0;console.log("💰 [ORDER_SERVICE] Calculating AROUZ fees...");const p=await V(l,i,r),d=p.total_fees,f=l+i+d;console.log("💾 [ORDER_SERVICE] Order calculations:",{subtotal:l,shippingCost:i,totalArouzFees:d,totalAmount:f,feeBreakdown:p.fee_summary});const{data:R,error:m}=await u.from("orders").insert({order_number:_,consumer_phone:t.consumer_phone,consumer_name:t.consumer_name,payment_method:t.payment_method,delivery_address:t.delivery_address,delivery_wilaya:t.delivery_wilaya,google_maps_url:t.google_maps_url,special_instructions:t.special_instructions,subtotal:l,total_amount:f,status:"pending",shipping_company_id:t.shipping_company_id,shipping_cost:t.shipping_cost||0,shipping_notes:t.shipping_notes,shipping_method:t.shipping_method||"standard",total_arouz_fees:d,arouz_commission:p.fee_summary.marketplace_commission,arouz_service_fee:p.fee_summary.service_fee,arouz_processing_fee:p.fee_summary.processing_fee}).select().single();if(console.log("💾 [ORDER_SERVICE] Order creation result:",{orderRecord:R,orderError:m}),m)return console.error("❌ [ORDER_SERVICE] Error creating order:",m),console.error("❌ [ORDER_SERVICE] Full error details:",JSON.stringify(m,null,2)),{success:!1,error:`Failed to create order: ${m.message||"Unknown error"}`};const g=R.id;console.log("✅ [ORDER_SERVICE] Order created successfully with ID:",g),console.log("💾 [ORDER_SERVICE] Creating order items...");const h=c.map(n=>({order_id:g,product_id:n.product_id,product_name:n.product_name,product_image:n.product_image,quantity:n.quantity,unit_price:n.unit_price,total_price:n.total_price,supplier_account_id:n.supplier_account_id,supplier_name:n.supplier_name,supplier_phone:n.supplier_phone,supplier_city:n.supplier_city,supplier_wilaya:n.supplier_wilaya,marketplace_section:n.marketplace_section,category:n.category,subcategory:n.subcategory}));console.log("💾 [ORDER_SERVICE] ORDER ITEMS DATA TO INSERT:",h.map(n=>({product_name:n.product_name,supplier_name:n.supplier_name,supplier_account_id:n.supplier_account_id,marketplace_section:n.marketplace_section,supplier_wilaya:n.supplier_wilaya,total_price:n.total_price})));const{data:I,error:E}=await u.from("order_items").insert(h).select();if(E)return console.error("❌ [ORDER_SERVICE] Error creating order items:",E),await u.from("orders").delete().eq("id",g),{success:!1,error:`Failed to create order items: ${E.message}`};console.log("✅ [ORDER_SERVICE] Order items created successfully"),console.log("✅ [ORDER_SERVICE] INSERTED ITEMS RESULT:",I?.map(n=>({id:n.id,product_name:n.product_name,supplier_name:n.supplier_name,supplier_account_id:n.supplier_account_id,marketplace_section:n.marketplace_section,supplier_wilaya:n.supplier_wilaya,total_price:n.total_price}))),console.log("💾 [ORDER_SERVICE] Creating shipment record...");const{error:O}=await u.from("shipments").insert({order_id:g,status:"unassigned",arouz_shipping_fee:15});O?console.error("❌ [ORDER_SERVICE] Error creating shipment:",O):console.log("✅ [ORDER_SERVICE] Shipment created successfully");const{data:D,error:y}=await u.from("orders").select(`
        *,
        order_items (*)
      `).eq("id",g).single();return y?(console.error("Error fetching created order:",y),{success:!1,error:"Order created but failed to retrieve details"}):{success:!0,order:D}}catch(o){return console.error("Error in createOrder:",o),{success:!1,error:"An unexpected error occurred"}}}async function P(){try{console.log("🔍 [GET_CONSUMER_ORDERS] Starting getConsumerOrders...");const e=await C();if(console.log("🔍 [GET_CONSUMER_ORDERS] Consumer phone:",e),!e)return console.error("❌ [GET_CONSUMER_ORDERS] No consumer phone - authentication required"),{success:!1,error:"Authentication required"};console.log("🔍 [GET_CONSUMER_ORDERS] Fetching orders from database...");const{data:r,error:o}=await u.from("orders").select("*").eq("consumer_phone",e).order("created_at",{ascending:!1});return o?(console.error("❌ [GET_CONSUMER_ORDERS] Database error:",o),{success:!1,error:"Failed to fetch orders"}):(console.log("✅ [GET_CONSUMER_ORDERS] Orders fetched successfully:",{count:r?.length||0,orders:r}),{success:!0,orders:r})}catch(e){return console.error("❌ [GET_CONSUMER_ORDERS] Exception in getConsumerOrders:",e),{success:!1,error:"An unexpected error occurred"}}}async function x(e){try{console.log("🔍 [GET_ORDER] Starting getOrderById for:",e);const r=await C();if(console.log("🔍 [GET_ORDER] Consumer phone:",r),!r)return console.error("❌ [GET_ORDER] No consumer phone - authentication required"),{success:!1,error:"Authentication required"};console.log("🔍 [GET_ORDER] Fetching order from database..."),console.log("🔍 [GET_ORDER] Step 1: Fetching order...");const{data:o,error:s}=await u.from("orders").select("*").eq("id",e).eq("consumer_phone",r).single();if(s)return console.error("❌ [GET_ORDER] Error fetching order:",s),{success:!1,error:"Order not found"};console.log("✅ [GET_ORDER] Order fetched successfully:",o),console.log("🔍 [GET_ORDER] Step 2: Fetching order items...");const{data:a,error:c}=await u.from("order_items").select("*").eq("order_id",e);c?(console.error("❌ [GET_ORDER] Error fetching order items:",c),console.error("❌ [GET_ORDER] Full error details:",JSON.stringify(c,null,2)),console.log("⚠️ [GET_ORDER] Continuing without order items...")):(console.log("✅ [GET_ORDER] Order items fetched successfully"),console.log("✅ [GET_ORDER] RETRIEVED ORDER ITEMS:",a?.map(i=>({id:i.id,product_name:i.product_name,supplier_name:i.supplier_name,supplier_account_id:i.supplier_account_id,marketplace_section:i.marketplace_section,supplier_wilaya:i.supplier_wilaya,total_price:i.total_price}))),console.log("✅ [GET_ORDER] TOTAL ITEMS RETRIEVED:",a?.length||0)),console.log("🔍 [GET_ORDER] Step 3: Fetching shipments...");const{data:t,error:_}=await u.from("shipments").select("*").eq("order_id",e);_?console.error("❌ [GET_ORDER] Error fetching shipments:",_):console.log("✅ [GET_ORDER] Shipments fetched successfully:",t);const l={...o,order_items:a||[],shipments:t||[]};return console.log("✅ [GET_ORDER] Final combined order data:",{id:l.id,order_number:l.order_number,order_items_count:l.order_items?.length||0,order_items:l.order_items,shipments_count:l.shipments?.length||0}),{success:!0,order:l}}catch(r){return console.error("❌ [GET_ORDER] Exception in getOrderById:",r),{success:!1,error:"An unexpected error occurred"}}}export{k as a,P as b,V as c,x as g};
