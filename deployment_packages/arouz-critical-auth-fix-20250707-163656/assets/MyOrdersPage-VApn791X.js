import{u as S,ag as A,a as D,r as c,j as e,F as d,v as o,C as x,l as m,al as O,P as h,B as u,A as P,k as _,Q as F,m as I,a3 as L,K as j,aq as E,w as B,ar as M,U as p,N as T}from"./index-DjNptb3k.js";import{S as l}from"./skeleton-DsZ9ndsd.js";import{b as U}from"./orderService-D895sbV7.js";import{C as Z}from"./calendar-DQkywbJQ.js";function J(){const{t:g}=S(),a=A(),{toast:n}=D(),[t,N]=c.useState([]),[f,y]=c.useState(!0),[V,b]=c.useState(!1);c.useEffect(()=>{(async()=>{try{const r=localStorage.getItem("phone_auth_session");if(!(r&&JSON.parse(r).profile?.role==="consumer")){a("/");return}b(!0);const i=await U();i.success?N(i.orders||[]):n({title:"Failed to load orders",description:i.error||"Please try again",variant:"destructive"})}catch(r){console.error("Error loading orders:",r),n({title:"Failed to load orders",description:"Please try again",variant:"destructive"})}finally{y(!1)}})()},[a,n]);const v=s=>{switch(s){case"pending":return e.jsx(T,{className:"h-4 w-4 text-yellow-600"});case"confirmed":return e.jsx(p,{className:"h-4 w-4 text-blue-600"});case"shipped":return e.jsx(j,{className:"h-4 w-4 text-purple-600"});case"delivered":return e.jsx(p,{className:"h-4 w-4 text-green-600"});case"cancelled":return e.jsx(M,{className:"h-4 w-4 text-red-600"});default:return e.jsx(B,{className:"h-4 w-4 text-gray-600"})}},w=s=>{switch(s){case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"confirmed":return"bg-blue-100 text-blue-800 border-blue-200";case"shipped":return"bg-purple-100 text-purple-800 border-purple-200";case"delivered":return"bg-green-100 text-green-800 border-green-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},C=s=>new Date(s).toLocaleDateString("fr-DZ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),k=s=>new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s);return f?e.jsx(d,{children:e.jsx(o,{children:e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(l,{className:"h-8 w-48"}),[1,2,3].map(s=>e.jsx(x,{children:e.jsx(m,{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(l,{className:"h-6 w-32"}),e.jsx(l,{className:"h-4 w-full"}),e.jsx(l,{className:"h-4 w-3/4"})]})})},s))]})})})})}):e.jsx(O,{action:"orders",fallback:e.jsx(d,{children:e.jsx(o,{children:e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(h,{className:"h-16 w-16 text-[#fa7b00] mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Your Orders"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"View and track your order history."}),e.jsx("button",{onClick:()=>{const s=document.getElementById("auth-modal-trigger");s&&s.click()},className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white px-6 py-3 rounded-lg font-medium",children:g("auth.loginOrSignUp")})]})})})}),children:e.jsx(d,{children:e.jsx(o,{children:e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"flex items-center gap-4 mb-4",children:e.jsxs(u,{variant:"ghost",onClick:()=>a("/"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900",children:[e.jsx(P,{className:"h-4 w-4"}),g("actions.back")]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[e.jsx(h,{className:"h-8 w-8 text-[#fa7b00]"}),"My Orders"]}),e.jsxs("p",{className:"text-gray-600 mt-2",children:[t.length," order",t.length!==1?"s":""]})]})})]}),e.jsx("div",{className:"space-y-6",children:t.length===0?e.jsx(x,{children:e.jsxs(m,{className:"p-12 text-center",children:[e.jsx(h,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No orders yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Start shopping to see your orders here."}),e.jsx(u,{onClick:()=>a("/"),className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90",children:"Start Shopping"})]})}):t.map(s=>e.jsxs(x,{className:"hover:shadow-md transition-shadow",children:[e.jsx(_,{className:"pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(F,{className:"text-lg font-semibold",children:["Order #",s.order_number]}),e.jsxs("p",{className:"text-sm text-gray-600 flex items-center gap-2 mt-1",children:[e.jsx(Z,{className:"h-4 w-4"}),C(s.created_at)]})]}),e.jsx("div",{className:"flex items-center gap-3",children:e.jsxs(I,{className:`${w(s.status)} flex items-center gap-1`,children:[v(s.status),s.status.charAt(0).toUpperCase()+s.status.slice(1)]})})]})}),e.jsxs(m,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(L,{className:"h-4 w-4"}),e.jsx("span",{children:s.delivery_address})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(j,{className:"h-4 w-4"}),e.jsx("span",{children:s.payment_method==="cash_on_delivery"?"Cash on Delivery":"Store Pickup"})]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-semibold text-[#fa7b00]",children:k(s.total_amount)}),e.jsx("p",{className:"text-sm text-gray-600",children:"Total Amount"})]}),e.jsxs(u,{onClick:()=>a(`/order-success?orderId=${s.id}`),variant:"outline",size:"sm",className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4"}),"View Details"]})]})]})]},s.id))})]})})})})})}export{J as default};
