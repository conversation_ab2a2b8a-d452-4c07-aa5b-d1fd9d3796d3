import{a_ as C,r as n,a$ as w,j as t,b0 as y,b1 as x,b2 as K,b3 as V,b4 as T,b5 as E,b6 as U,b7 as $,b8 as z,b9 as B,aZ as j,ba as H}from"./index-DjNptb3k.js";var g="Radio",[W,G]=C(g),[X,Y]=W(g),I=n.forwardRef((o,s)=>{const{__scopeRadio:e,name:c,checked:r=!1,required:a,disabled:i,value:u="on",onCheck:l,form:p,...m}=o,[d,f]=n.useState(null),R=w(s,h=>f(h)),v=n.useRef(!1),b=d?p||!!d.closest("form"):!0;return t.jsxs(X,{scope:e,checked:r,disabled:i,children:[t.jsx(y.button,{type:"button",role:"radio","aria-checked":r,"data-state":S(r),"data-disabled":i?"":void 0,disabled:i,value:u,...m,ref:R,onClick:x(o.onClick,h=>{r||l?.(),b&&(v.current=h.isPropagationStopped(),v.current||h.stopPropagation())})}),b&&t.jsx(Z,{control:d,bubbles:!v.current,name:c,value:u,checked:r,required:a,disabled:i,form:p,style:{transform:"translateX(-100%)"}})]})});I.displayName=g;var N="RadioIndicator",P=n.forwardRef((o,s)=>{const{__scopeRadio:e,forceMount:c,...r}=o,a=Y(N,e);return t.jsx(K,{present:c||a.checked,children:t.jsx(y.span,{"data-state":S(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:s})})});P.displayName=N;var Z=o=>{const{control:s,checked:e,bubbles:c=!0,...r}=o,a=n.useRef(null),i=V(e),u=T(s);return n.useEffect(()=>{const l=a.current,p=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(p,"checked").set;if(i!==e&&d){const f=new Event("click",{bubbles:c});d.call(l,e),l.dispatchEvent(f)}},[i,e,c]),t.jsx("input",{type:"radio","aria-hidden":!0,defaultChecked:e,...r,tabIndex:-1,ref:a,style:{...o.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function S(o){return o?"checked":"unchecked"}var J=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],k="RadioGroup",[Q,ie]=C(k,[E,G]),_=E(),A=G(),[ee,oe]=Q(k),D=n.forwardRef((o,s)=>{const{__scopeRadioGroup:e,name:c,defaultValue:r,value:a,required:i=!1,disabled:u=!1,orientation:l,dir:p,loop:m=!0,onValueChange:d,...f}=o,R=_(e),v=U(p),[b,h]=$({prop:a,defaultProp:r,onChange:d});return t.jsx(ee,{scope:e,name:c,required:i,disabled:u,value:b,onValueChange:h,children:t.jsx(z,{asChild:!0,...R,orientation:l,dir:v,loop:m,children:t.jsx(y.div,{role:"radiogroup","aria-required":i,"aria-orientation":l,"data-disabled":u?"":void 0,dir:v,...f,ref:s})})})});D.displayName=k;var M="RadioGroupItem",O=n.forwardRef((o,s)=>{const{__scopeRadioGroup:e,disabled:c,...r}=o,a=oe(M,e),i=a.disabled||c,u=_(e),l=A(e),p=n.useRef(null),m=w(s,p),d=a.value===r.value,f=n.useRef(!1);return n.useEffect(()=>{const R=b=>{J.includes(b.key)&&(f.current=!0)},v=()=>f.current=!1;return document.addEventListener("keydown",R),document.addEventListener("keyup",v),()=>{document.removeEventListener("keydown",R),document.removeEventListener("keyup",v)}},[]),t.jsx(B,{asChild:!0,...u,focusable:!i,active:d,children:t.jsx(I,{disabled:i,required:a.required,checked:d,...l,...r,name:a.name,ref:m,onCheck:()=>a.onValueChange(r.value),onKeyDown:x(R=>{R.key==="Enter"&&R.preventDefault()}),onFocus:x(r.onFocus,()=>{f.current&&p.current?.click()})})})});O.displayName=M;var re="RadioGroupIndicator",F=n.forwardRef((o,s)=>{const{__scopeRadioGroup:e,...c}=o,r=A(e);return t.jsx(P,{...r,...c,ref:s})});F.displayName=re;var q=D,L=O,ae=F;const te=n.forwardRef(({className:o,...s},e)=>t.jsx(q,{className:j("grid gap-2",o),...s,ref:e}));te.displayName=q.displayName;const se=n.forwardRef(({className:o,...s},e)=>t.jsx(L,{ref:e,className:j("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o),...s,children:t.jsx(ae,{className:"flex items-center justify-center",children:t.jsx(H,{className:"h-2.5 w-2.5 fill-current text-current"})})}));se.displayName=L.displayName;export{te as R,se as a};
