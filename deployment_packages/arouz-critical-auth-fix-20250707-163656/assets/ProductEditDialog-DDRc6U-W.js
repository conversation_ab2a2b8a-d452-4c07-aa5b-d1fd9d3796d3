import{c as Qs,R as L,r as p,bM as Ft,j as e,D as Ks,d as Gs,e as Js,f as Zs,aA as Ot,aB as Lt,h as et,B as Z,i as ms,ay as ne,ar as Ms,aZ as Tt,cn as dr,aJ as ur,aK as mr,aL as ds,aM as us,co as Ir,bs as Pr,cp as _t,G as Xs,w as hr,L as J,aw as fr,I as ye,m as gr,cq as gt,cr as Fr,cs as Tr,ct as Xt,cu as _r,cv as Vr,bb as Dr,aF as Mr,cw as Rr,cx as Or,cy as Lr,T as $r,cz as qr,cA as Wr,C as Ur,l as zr,ci as Br,cB as Hr,cC as Yr}from"./index-DjNptb3k.js";import{S as Is,a as Ps,b as Fs,c as Ts,d as he}from"./select-B_ZRrAqd.js";import{P as xr}from"./progress-wrYgTL2X.js";import{U as mt}from"./upload-bhq3H-yM.js";import{V as Xr,E as wt}from"./enhanced-dropdown-BWor6myK.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qt=Qs("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=Qs("ImageOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M10.41 10.41a2 2 0 1 1-2.83-2.83",key:"1bzlo9"}],["line",{x1:"13.5",x2:"6",y1:"13.5",y2:"21",key:"1q0aeu"}],["line",{x1:"18",x2:"21",y1:"12",y2:"15",key:"5mozeu"}],["path",{d:"M3.59 3.59A1.99 1.99 0 0 0 3 5v14a2 2 0 0 0 2 2h14c.55 0 1.052-.22 1.41-.59",key:"mmje98"}],["path",{d:"M21 15V5a2 2 0 0 0-2-2H9",key:"43el77"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=Qs("Images",[["path",{d:"M18 22H4a2 2 0 0 1-2-2V6",key:"pblm9e"}],["path",{d:"m22 13-1.296-1.296a2.41 2.41 0 0 0-3.408 0L11 18",key:"nf6bnh"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}],["rect",{width:"16",height:"16",x:"6",y:"2",rx:"2",key:"12espp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $t=Qs("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=Qs("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xt=Qs("WandSparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);var lt=s=>s.type==="checkbox",Vs=s=>s instanceof Date,Ie=s=>s==null;const pr=s=>typeof s=="object";var pe=s=>!Ie(s)&&!Array.isArray(s)&&pr(s)&&!Vs(s),Kr=s=>pe(s)&&s.target?lt(s.target)?s.target.checked:s.target.value:s,Gr=s=>s.substring(0,s.search(/\.\d+(\.|$)/))||s,Jr=(s,a)=>s.has(Gr(a)),Zr=s=>{const a=s.constructor&&s.constructor.prototype;return pe(a)&&a.hasOwnProperty("isPrototypeOf")},qt=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Le(s){let a;const t=Array.isArray(s);if(s instanceof Date)a=new Date(s);else if(s instanceof Set)a=new Set(s);else if(!(qt&&(s instanceof Blob||s instanceof FileList))&&(t||pe(s)))if(a=t?[]:{},!t&&!Zr(s))a=s;else for(const i in s)s.hasOwnProperty(i)&&(a[i]=Le(s[i]));else return s;return a}var jt=s=>Array.isArray(s)?s.filter(Boolean):[],xe=s=>s===void 0,S=(s,a,t)=>{if(!a||!pe(s))return t;const i=jt(a.split(/[,[\].]+?/)).reduce((c,n)=>Ie(c)?c:c[n],s);return xe(i)||i===s?xe(s[a])?t:s[a]:i},Ke=s=>typeof s=="boolean",Wt=s=>/^\w*$/.test(s),vr=s=>jt(s.replace(/["|']|\]/g,"").split(/\.|\[/)),ue=(s,a,t)=>{let i=-1;const c=Wt(a)?[a]:vr(a),n=c.length,d=n-1;for(;++i<n;){const x=c[i];let m=t;if(i!==d){const j=s[x];m=pe(j)||Array.isArray(j)?j:isNaN(+c[i+1])?{}:[]}if(x==="__proto__")return;s[x]=m,s=s[x]}return s};const Kt={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},We={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},cs={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};L.createContext(null);var ea=(s,a,t,i=!0)=>{const c={defaultValues:a._defaultValues};for(const n in s)Object.defineProperty(c,n,{get:()=>{const d=n;return a._proxyFormState[d]!==We.all&&(a._proxyFormState[d]=!i||We.all),s[d]}});return c},Fe=s=>pe(s)&&!Object.keys(s).length,sa=(s,a,t,i)=>{t(s);const{name:c,...n}=s;return Fe(n)||Object.keys(n).length>=Object.keys(a).length||Object.keys(n).find(d=>a[d]===We.all)},ht=s=>Array.isArray(s)?s:[s];function ta(s){const a=L.useRef(s);a.current=s,L.useEffect(()=>{const t=!s.disabled&&a.current.subject&&a.current.subject.subscribe({next:a.current.next});return()=>{t&&t.unsubscribe()}},[s.disabled])}var Ge=s=>typeof s=="string",ra=(s,a,t,i,c)=>Ge(s)?(i&&a.watch.add(s),S(t,s,c)):Array.isArray(s)?s.map(n=>(i&&a.watch.add(n),S(t,n))):(i&&(a.watchAll=!0),t),aa=(s,a,t,i,c)=>a?{...t[s],types:{...t[s]&&t[s].types?t[s].types:{},[i]:c||!0}}:{},Gt=s=>({isOnSubmit:!s||s===We.onSubmit,isOnBlur:s===We.onBlur,isOnChange:s===We.onChange,isOnAll:s===We.all,isOnTouch:s===We.onTouched}),Jt=(s,a,t)=>!t&&(a.watchAll||a.watch.has(s)||[...a.watch].some(i=>s.startsWith(i)&&/^\.\w+/.test(s.slice(i.length))));const nt=(s,a,t,i)=>{for(const c of t||Object.keys(s)){const n=S(s,c);if(n){const{_f:d,...x}=n;if(d){if(d.refs&&d.refs[0]&&a(d.refs[0],c)&&!i)return!0;if(d.ref&&a(d.ref,d.name)&&!i)return!0;if(nt(x,a))break}else if(pe(x)&&nt(x,a))break}}};var ia=(s,a,t)=>{const i=ht(S(s,t));return ue(i,"root",a[t]),ue(s,t,i),s},Ut=s=>s.type==="file",hs=s=>typeof s=="function",pt=s=>{if(!qt)return!1;const a=s?s.ownerDocument:0;return s instanceof(a&&a.defaultView?a.defaultView.HTMLElement:HTMLElement)},ft=s=>Ge(s),zt=s=>s.type==="radio",vt=s=>s instanceof RegExp;const Zt={value:!1,isValid:!1},er={value:!0,isValid:!0};var yr=s=>{if(Array.isArray(s)){if(s.length>1){const a=s.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:a,isValid:!!a.length}}return s[0].checked&&!s[0].disabled?s[0].attributes&&!xe(s[0].attributes.value)?xe(s[0].value)||s[0].value===""?er:{value:s[0].value,isValid:!0}:er:Zt}return Zt};const sr={isValid:!1,value:null};var br=s=>Array.isArray(s)?s.reduce((a,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:a,sr):sr;function tr(s,a,t="validate"){if(ft(s)||Array.isArray(s)&&s.every(ft)||Ke(s)&&!s)return{type:t,message:ft(s)?s:"",ref:a}}var Bs=s=>pe(s)&&!vt(s)?s:{value:s,message:""},rr=async(s,a,t,i,c)=>{const{ref:n,refs:d,required:x,maxLength:m,minLength:j,min:b,max:h,pattern:U,validate:V,name:T,valueAsNumber:B,mount:P,disabled:F}=s._f,E=S(a,T);if(!P||F)return{};const X=d?d[0]:n,te=N=>{i&&X.reportValidity&&(X.setCustomValidity(Ke(N)?"":N||""),X.reportValidity())},K={},re=zt(n),me=lt(n),_=re||me,R=(B||Ut(n))&&xe(n.value)&&xe(E)||pt(n)&&n.value===""||E===""||Array.isArray(E)&&!E.length,O=aa.bind(null,T,t,K),D=(N,A,z,W=cs.maxLength,Q=cs.minLength)=>{const u=N?A:z;K[T]={type:N?W:Q,message:u,ref:n,...O(N?W:Q,u)}};if(c?!Array.isArray(E)||!E.length:x&&(!_&&(R||Ie(E))||Ke(E)&&!E||me&&!yr(d).isValid||re&&!br(d).isValid)){const{value:N,message:A}=ft(x)?{value:!!x,message:x}:Bs(x);if(N&&(K[T]={type:cs.required,message:A,ref:X,...O(cs.required,A)},!t))return te(A),K}if(!R&&(!Ie(b)||!Ie(h))){let N,A;const z=Bs(h),W=Bs(b);if(!Ie(E)&&!isNaN(E)){const Q=n.valueAsNumber||E&&+E;Ie(z.value)||(N=Q>z.value),Ie(W.value)||(A=Q<W.value)}else{const Q=n.valueAsDate||new Date(E),u=H=>new Date(new Date().toDateString()+" "+H),C=n.type=="time",M=n.type=="week";Ge(z.value)&&E&&(N=C?u(E)>u(z.value):M?E>z.value:Q>new Date(z.value)),Ge(W.value)&&E&&(A=C?u(E)<u(W.value):M?E<W.value:Q<new Date(W.value))}if((N||A)&&(D(!!N,z.message,W.message,cs.max,cs.min),!t))return te(K[T].message),K}if((m||j)&&!R&&(Ge(E)||c&&Array.isArray(E))){const N=Bs(m),A=Bs(j),z=!Ie(N.value)&&E.length>+N.value,W=!Ie(A.value)&&E.length<+A.value;if((z||W)&&(D(z,N.message,A.message),!t))return te(K[T].message),K}if(U&&!R&&Ge(E)){const{value:N,message:A}=Bs(U);if(vt(N)&&!E.match(N)&&(K[T]={type:cs.pattern,message:A,ref:n,...O(cs.pattern,A)},!t))return te(A),K}if(V){if(hs(V)){const N=await V(E,a),A=tr(N,X);if(A&&(K[T]={...A,...O(cs.validate,A.message)},!t))return te(A.message),K}else if(pe(V)){let N={};for(const A in V){if(!Fe(N)&&!t)break;const z=tr(await V[A](E,a),X,A);z&&(N={...z,...O(A,z.message)},te(z.message),t&&(K[T]=N))}if(!Fe(N)&&(K[T]={ref:X,...N},!t))return K}}return te(!0),K};function na(s,a){const t=a.slice(0,-1).length;let i=0;for(;i<t;)s=xe(s)?i++:s[a[i++]];return s}function la(s){for(const a in s)if(s.hasOwnProperty(a)&&!xe(s[a]))return!1;return!0}function je(s,a){const t=Array.isArray(a)?a:Wt(a)?[a]:vr(a),i=t.length===1?s:na(s,t),c=t.length-1,n=t[c];return i&&delete i[n],c!==0&&(pe(i)&&Fe(i)||Array.isArray(i)&&la(i))&&je(s,t.slice(0,-1)),s}var St=()=>{let s=[];return{get observers(){return s},next:c=>{for(const n of s)n.next&&n.next(c)},subscribe:c=>(s.push(c),{unsubscribe:()=>{s=s.filter(n=>n!==c)}}),unsubscribe:()=>{s=[]}}},Vt=s=>Ie(s)||!pr(s);function Ns(s,a){if(Vt(s)||Vt(a))return s===a;if(Vs(s)&&Vs(a))return s.getTime()===a.getTime();const t=Object.keys(s),i=Object.keys(a);if(t.length!==i.length)return!1;for(const c of t){const n=s[c];if(!i.includes(c))return!1;if(c!=="ref"){const d=a[c];if(Vs(n)&&Vs(d)||pe(n)&&pe(d)||Array.isArray(n)&&Array.isArray(d)?!Ns(n,d):n!==d)return!1}}return!0}var jr=s=>s.type==="select-multiple",ca=s=>zt(s)||lt(s),Et=s=>pt(s)&&s.isConnected,Nr=s=>{for(const a in s)if(hs(s[a]))return!0;return!1};function yt(s,a={}){const t=Array.isArray(s);if(pe(s)||t)for(const i in s)Array.isArray(s[i])||pe(s[i])&&!Nr(s[i])?(a[i]=Array.isArray(s[i])?[]:{},yt(s[i],a[i])):Ie(s[i])||(a[i]=!0);return a}function wr(s,a,t){const i=Array.isArray(s);if(pe(s)||i)for(const c in s)Array.isArray(s[c])||pe(s[c])&&!Nr(s[c])?xe(a)||Vt(t[c])?t[c]=Array.isArray(s[c])?yt(s[c],[]):{...yt(s[c])}:wr(s[c],Ie(a)?{}:a[c],t[c]):t[c]=!Ns(s[c],a[c]);return t}var at=(s,a)=>wr(s,a,yt(a)),Cr=(s,{valueAsNumber:a,valueAsDate:t,setValueAs:i})=>xe(s)?s:a?s===""?NaN:s&&+s:t&&Ge(s)?new Date(s):i?i(s):s;function At(s){const a=s.ref;if(!(s.refs?s.refs.every(t=>t.disabled):a.disabled))return Ut(a)?a.files:zt(a)?br(s.refs).value:jr(a)?[...a.selectedOptions].map(({value:t})=>t):lt(a)?yr(s.refs).value:Cr(xe(a.value)?s.ref.value:a.value,s)}var oa=(s,a,t,i)=>{const c={};for(const n of s){const d=S(a,n);d&&ue(c,n,d._f)}return{criteriaMode:t,names:[...s],fields:c,shouldUseNativeValidation:i}},it=s=>xe(s)?s:vt(s)?s.source:pe(s)?vt(s.value)?s.value.source:s.value:s;const ar="AsyncFunction";var da=s=>(!s||!s.validate)&&!!(hs(s.validate)&&s.validate.constructor.name===ar||pe(s.validate)&&Object.values(s.validate).find(a=>a.constructor.name===ar)),ua=s=>s.mount&&(s.required||s.min||s.max||s.maxLength||s.minLength||s.pattern||s.validate);function ir(s,a,t){const i=S(s,t);if(i||Wt(t))return{error:i,name:t};const c=t.split(".");for(;c.length;){const n=c.join("."),d=S(a,n),x=S(s,n);if(d&&!Array.isArray(d)&&t!==n)return{name:t};if(x&&x.type)return{name:n,error:x};c.pop()}return{name:t}}var ma=(s,a,t,i,c)=>c.isOnAll?!1:!t&&c.isOnTouch?!(a||s):(t?i.isOnBlur:c.isOnBlur)?!s:(t?i.isOnChange:c.isOnChange)?s:!0,ha=(s,a)=>!jt(S(s,a)).length&&je(s,a);const fa={mode:We.onSubmit,reValidateMode:We.onChange,shouldFocusError:!0};function ga(s={}){let a={...fa,...s},t={submitCount:0,isDirty:!1,isLoading:hs(a.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1},i={},c=pe(a.defaultValues)||pe(a.values)?Le(a.defaultValues||a.values)||{}:{},n=a.shouldUnregister?{}:Le(c),d={action:!1,mount:!1,watch:!1},x={mount:new Set,unMount:new Set,array:new Set,watch:new Set},m,j=0;const b={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},h={values:St(),array:St(),state:St()},U=Gt(a.mode),V=Gt(a.reValidateMode),T=a.criteriaMode===We.all,B=r=>l=>{clearTimeout(j),j=setTimeout(r,l)},P=async r=>{if(!s.disabled&&(b.isValid||r)){const l=a.resolver?Fe((await _()).errors):await O(i,!0);l!==t.isValid&&h.state.next({isValid:l})}},F=(r,l)=>{!s.disabled&&(b.isValidating||b.validatingFields)&&((r||Array.from(x.mount)).forEach(o=>{o&&(l?ue(t.validatingFields,o,l):je(t.validatingFields,o))}),h.state.next({validatingFields:t.validatingFields,isValidating:!Fe(t.validatingFields)}))},E=(r,l=[],o,w,y=!0,v=!0)=>{if(w&&o&&!s.disabled){if(d.action=!0,v&&Array.isArray(S(i,r))){const I=o(S(i,r),w.argA,w.argB);y&&ue(i,r,I)}if(v&&Array.isArray(S(t.errors,r))){const I=o(S(t.errors,r),w.argA,w.argB);y&&ue(t.errors,r,I),ha(t.errors,r)}if(b.touchedFields&&v&&Array.isArray(S(t.touchedFields,r))){const I=o(S(t.touchedFields,r),w.argA,w.argB);y&&ue(t.touchedFields,r,I)}b.dirtyFields&&(t.dirtyFields=at(c,n)),h.state.next({name:r,isDirty:N(r,l),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else ue(n,r,l)},X=(r,l)=>{ue(t.errors,r,l),h.state.next({errors:t.errors})},te=r=>{t.errors=r,h.state.next({errors:t.errors,isValid:!1})},K=(r,l,o,w)=>{const y=S(i,r);if(y){const v=S(n,r,xe(o)?S(c,r):o);xe(v)||w&&w.defaultChecked||l?ue(n,r,l?v:At(y._f)):W(r,v),d.mount&&P()}},re=(r,l,o,w,y)=>{let v=!1,I=!1;const q={name:r};if(!s.disabled){const G=!!(S(i,r)&&S(i,r)._f&&S(i,r)._f.disabled);if(!o||w){b.isDirty&&(I=t.isDirty,t.isDirty=q.isDirty=N(),v=I!==q.isDirty);const ae=G||Ns(S(c,r),l);I=!!(!G&&S(t.dirtyFields,r)),ae||G?je(t.dirtyFields,r):ue(t.dirtyFields,r,!0),q.dirtyFields=t.dirtyFields,v=v||b.dirtyFields&&I!==!ae}if(o){const ae=S(t.touchedFields,r);ae||(ue(t.touchedFields,r,o),q.touchedFields=t.touchedFields,v=v||b.touchedFields&&ae!==o)}v&&y&&h.state.next(q)}return v?q:{}},me=(r,l,o,w)=>{const y=S(t.errors,r),v=b.isValid&&Ke(l)&&t.isValid!==l;if(s.delayError&&o?(m=B(()=>X(r,o)),m(s.delayError)):(clearTimeout(j),m=null,o?ue(t.errors,r,o):je(t.errors,r)),(o?!Ns(y,o):y)||!Fe(w)||v){const I={...w,...v&&Ke(l)?{isValid:l}:{},errors:t.errors,name:r};t={...t,...I},h.state.next(I)}},_=async r=>{F(r,!0);const l=await a.resolver(n,a.context,oa(r||x.mount,i,a.criteriaMode,a.shouldUseNativeValidation));return F(r),l},R=async r=>{const{errors:l}=await _(r);if(r)for(const o of r){const w=S(l,o);w?ue(t.errors,o,w):je(t.errors,o)}else t.errors=l;return l},O=async(r,l,o={valid:!0})=>{for(const w in r){const y=r[w];if(y){const{_f:v,...I}=y;if(v){const q=x.array.has(v.name),G=y._f&&da(y._f);G&&b.validatingFields&&F([w],!0);const ae=await rr(y,n,T,a.shouldUseNativeValidation&&!l,q);if(G&&b.validatingFields&&F([w]),ae[v.name]&&(o.valid=!1,l))break;!l&&(S(ae,v.name)?q?ia(t.errors,ae,v.name):ue(t.errors,v.name,ae[v.name]):je(t.errors,v.name))}!Fe(I)&&await O(I,l,o)}}return o.valid},D=()=>{for(const r of x.unMount){const l=S(i,r);l&&(l._f.refs?l._f.refs.every(o=>!Et(o)):!Et(l._f.ref))&&ce(r)}x.unMount=new Set},N=(r,l)=>!s.disabled&&(r&&l&&ue(n,r,l),!Ns(k(),c)),A=(r,l,o)=>ra(r,x,{...d.mount?n:xe(l)?c:Ge(r)?{[r]:l}:l},o,l),z=r=>jt(S(d.mount?n:c,r,s.shouldUnregister?S(c,r,[]):[])),W=(r,l,o={})=>{const w=S(i,r);let y=l;if(w){const v=w._f;v&&(!v.disabled&&ue(n,r,Cr(l,v)),y=pt(v.ref)&&Ie(l)?"":l,jr(v.ref)?[...v.ref.options].forEach(I=>I.selected=y.includes(I.value)):v.refs?lt(v.ref)?v.refs.length>1?v.refs.forEach(I=>(!I.defaultChecked||!I.disabled)&&(I.checked=Array.isArray(y)?!!y.find(q=>q===I.value):y===I.value)):v.refs[0]&&(v.refs[0].checked=!!y):v.refs.forEach(I=>I.checked=I.value===y):Ut(v.ref)?v.ref.value="":(v.ref.value=y,v.ref.type||h.values.next({name:r,values:{...n}})))}(o.shouldDirty||o.shouldTouch)&&re(r,y,o.shouldTouch,o.shouldDirty,!0),o.shouldValidate&&H(r)},Q=(r,l,o)=>{for(const w in l){const y=l[w],v=`${r}.${w}`,I=S(i,v);(x.array.has(r)||pe(y)||I&&!I._f)&&!Vs(y)?Q(v,y,o):W(v,y,o)}},u=(r,l,o={})=>{const w=S(i,r),y=x.array.has(r),v=Le(l);ue(n,r,v),y?(h.array.next({name:r,values:{...n}}),(b.isDirty||b.dirtyFields)&&o.shouldDirty&&h.state.next({name:r,dirtyFields:at(c,n),isDirty:N(r,v)})):w&&!w._f&&!Ie(v)?Q(r,v,o):W(r,v,o),Jt(r,x)&&h.state.next({...t}),h.values.next({name:d.mount?r:void 0,values:{...n}})},C=async r=>{d.mount=!0;const l=r.target;let o=l.name,w=!0;const y=S(i,o),v=()=>l.type?At(y._f):Kr(r),I=q=>{w=Number.isNaN(q)||Vs(q)&&isNaN(q.getTime())||Ns(q,S(n,o,q))};if(y){let q,G;const ae=v(),ke=r.type===Kt.BLUR||r.type===Kt.FOCUS_OUT,Cs=!ua(y._f)&&!a.resolver&&!S(t.errors,o)&&!y._f.deps||ma(ke,S(t.touchedFields,o),t.isSubmitted,V,U),fs=Jt(o,x,ke);ue(n,o,ae),ke?(y._f.onBlur&&y._f.onBlur(r),m&&m(0)):y._f.onChange&&y._f.onChange(r);const gs=re(o,ae,ke,!1),Rs=!Fe(gs)||fs;if(!ke&&h.values.next({name:o,type:r.type,values:{...n}}),Cs)return b.isValid&&(s.mode==="onBlur"?ke&&P():P()),Rs&&h.state.next({name:o,...fs?{}:gs});if(!ke&&fs&&h.state.next({...t}),a.resolver){const{errors:Se}=await _([o]);if(I(ae),w){const Os=ir(t.errors,i,o),Je=ir(Se,i,Os.name||o);q=Je.error,o=Je.name,G=Fe(Se)}}else F([o],!0),q=(await rr(y,n,T,a.shouldUseNativeValidation))[o],F([o]),I(ae),w&&(q?G=!1:b.isValid&&(G=await O(i,!0)));w&&(y._f.deps&&H(y._f.deps),me(o,G,q,gs))}},M=(r,l)=>{if(S(t.errors,l)&&r.focus)return r.focus(),1},H=async(r,l={})=>{let o,w;const y=ht(r);if(a.resolver){const v=await R(xe(r)?r:y);o=Fe(v),w=r?!y.some(I=>S(v,I)):o}else r?(w=(await Promise.all(y.map(async v=>{const I=S(i,v);return await O(I&&I._f?{[v]:I}:I)}))).every(Boolean),!(!w&&!t.isValid)&&P()):w=o=await O(i);return h.state.next({...!Ge(r)||b.isValid&&o!==t.isValid?{}:{name:r},...a.resolver||!r?{isValid:o}:{},errors:t.errors}),l.shouldFocus&&!w&&nt(i,M,r?y:x.mount),w},k=r=>{const l={...d.mount?n:c};return xe(r)?l:Ge(r)?S(l,r):r.map(o=>S(l,o))},fe=(r,l)=>({invalid:!!S((l||t).errors,r),isDirty:!!S((l||t).dirtyFields,r),error:S((l||t).errors,r),isValidating:!!S(t.validatingFields,r),isTouched:!!S((l||t).touchedFields,r)}),se=r=>{r&&ht(r).forEach(l=>je(t.errors,l)),h.state.next({errors:r?t.errors:{}})},ee=(r,l,o)=>{const w=(S(i,r,{_f:{}})._f||{}).ref,y=S(t.errors,r)||{},{ref:v,message:I,type:q,...G}=y;ue(t.errors,r,{...G,...l,ref:w}),h.state.next({name:r,errors:t.errors,isValid:!1}),o&&o.shouldFocus&&w&&w.focus&&w.focus()},we=(r,l)=>hs(r)?h.values.subscribe({next:o=>r(A(void 0,l),o)}):A(r,l,!0),ce=(r,l={})=>{for(const o of r?ht(r):x.mount)x.mount.delete(o),x.array.delete(o),l.keepValue||(je(i,o),je(n,o)),!l.keepError&&je(t.errors,o),!l.keepDirty&&je(t.dirtyFields,o),!l.keepTouched&&je(t.touchedFields,o),!l.keepIsValidating&&je(t.validatingFields,o),!a.shouldUnregister&&!l.keepDefaultValue&&je(c,o);h.values.next({values:{...n}}),h.state.next({...t,...l.keepDirty?{isDirty:N()}:{}}),!l.keepIsValid&&P()},oe=({disabled:r,name:l,field:o,fields:w,value:y})=>{if(Ke(r)&&d.mount||r){const v=r?void 0:xe(y)?At(o?o._f:S(w,l)._f):y;ue(n,l,v),re(l,v,!1,!1,!0)}},Pe=(r,l={})=>{let o=S(i,r);const w=Ke(l.disabled)||Ke(s.disabled);return ue(i,r,{...o||{},_f:{...o&&o._f?o._f:{ref:{name:r}},name:r,mount:!0,...l}}),x.mount.add(r),o?oe({field:o,disabled:Ke(l.disabled)?l.disabled:s.disabled,name:r,value:l.value}):K(r,!0,l.value),{...w?{disabled:l.disabled||s.disabled}:{},...a.progressive?{required:!!l.required,min:it(l.min),max:it(l.max),minLength:it(l.minLength),maxLength:it(l.maxLength),pattern:it(l.pattern)}:{},name:r,onChange:C,onBlur:C,ref:y=>{if(y){Pe(r,l),o=S(i,r);const v=xe(y.value)&&y.querySelectorAll&&y.querySelectorAll("input,select,textarea")[0]||y,I=ca(v),q=o._f.refs||[];if(I?q.find(G=>G===v):v===o._f.ref)return;ue(i,r,{_f:{...o._f,...I?{refs:[...q.filter(Et),v,...Array.isArray(S(c,r))?[{}]:[]],ref:{type:v.type,name:r}}:{ref:v}}}),K(r,!1,void 0,v)}else o=S(i,r,{}),o._f&&(o._f.mount=!1),(a.shouldUnregister||l.shouldUnregister)&&!(Jr(x.array,r)&&d.action)&&x.unMount.add(r)}}},Ce=()=>a.shouldFocusError&&nt(i,M,x.mount),ge=r=>{Ke(r)&&(h.state.next({disabled:r}),nt(i,(l,o)=>{const w=S(i,o);w&&(l.disabled=w._f.disabled||r,Array.isArray(w._f.refs)&&w._f.refs.forEach(y=>{y.disabled=w._f.disabled||r}))},0,!1))},Ue=(r,l)=>async o=>{let w;o&&(o.preventDefault&&o.preventDefault(),o.persist&&o.persist());let y=Le(n);if(h.state.next({isSubmitting:!0}),a.resolver){const{errors:v,values:I}=await _();t.errors=v,y=I}else await O(i);if(je(t.errors,"root"),Fe(t.errors)){h.state.next({errors:{}});try{await r(y,o)}catch(v){w=v}}else l&&await l({...t.errors},o),Ce(),setTimeout(Ce);if(h.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Fe(t.errors)&&!w,submitCount:t.submitCount+1,errors:t.errors}),w)throw w},ze=(r,l={})=>{S(i,r)&&(xe(l.defaultValue)?u(r,Le(S(c,r))):(u(r,l.defaultValue),ue(c,r,Le(l.defaultValue))),l.keepTouched||je(t.touchedFields,r),l.keepDirty||(je(t.dirtyFields,r),t.isDirty=l.defaultValue?N(r,Le(S(c,r))):N()),l.keepError||(je(t.errors,r),b.isValid&&P()),h.state.next({...t}))},Ve=(r,l={})=>{const o=r?Le(r):c,w=Le(o),y=Fe(r),v=y?c:w;if(l.keepDefaultValues||(c=o),!l.keepValues){if(l.keepDirtyValues){const I=new Set([...x.mount,...Object.keys(at(c,n))]);for(const q of Array.from(I))S(t.dirtyFields,q)?ue(v,q,S(n,q)):u(q,S(v,q))}else{if(qt&&xe(r))for(const I of x.mount){const q=S(i,I);if(q&&q._f){const G=Array.isArray(q._f.refs)?q._f.refs[0]:q._f.ref;if(pt(G)){const ae=G.closest("form");if(ae){ae.reset();break}}}}i={}}n=s.shouldUnregister?l.keepDefaultValues?Le(c):{}:Le(v),h.array.next({values:{...v}}),h.values.next({values:{...v}})}x={mount:l.keepDirtyValues?x.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!b.isValid||!!l.keepIsValid||!!l.keepDirtyValues,d.watch=!!s.shouldUnregister,h.state.next({submitCount:l.keepSubmitCount?t.submitCount:0,isDirty:y?!1:l.keepDirty?t.isDirty:!!(l.keepDefaultValues&&!Ns(r,c)),isSubmitted:l.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:y?{}:l.keepDirtyValues?l.keepDefaultValues&&n?at(c,n):t.dirtyFields:l.keepDefaultValues&&r?at(c,r):l.keepDirty?t.dirtyFields:{},touchedFields:l.keepTouched?t.touchedFields:{},errors:l.keepErrors?t.errors:{},isSubmitSuccessful:l.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},Be=(r,l)=>Ve(hs(r)?r(n):r,l);return{control:{register:Pe,unregister:ce,getFieldState:fe,handleSubmit:Ue,setError:ee,_executeSchema:_,_getWatch:A,_getDirty:N,_updateValid:P,_removeUnmounted:D,_updateFieldArray:E,_updateDisabledField:oe,_getFieldArray:z,_reset:Ve,_resetDefaultValues:()=>hs(a.defaultValues)&&a.defaultValues().then(r=>{Be(r,a.resetOptions),h.state.next({isLoading:!1})}),_updateFormState:r=>{t={...t,...r}},_disableForm:ge,_subjects:h,_proxyFormState:b,_setErrors:te,get _fields(){return i},get _formValues(){return n},get _state(){return d},set _state(r){d=r},get _defaultValues(){return c},get _names(){return x},set _names(r){x=r},get _formState(){return t},set _formState(r){t=r},get _options(){return a},set _options(r){a={...a,...r}}},trigger:H,register:Pe,handleSubmit:Ue,watch:we,setValue:u,getValues:k,reset:Be,resetField:ze,clearErrors:se,unregister:ce,setError:ee,setFocus:(r,l={})=>{const o=S(i,r),w=o&&o._f;if(w){const y=w.refs?w.refs[0]:w.ref;y.focus&&(y.focus(),l.shouldSelect&&y.select())}},getFieldState:fe}}function xa(s={}){const a=L.useRef(),t=L.useRef(),[i,c]=L.useState({isDirty:!1,isValidating:!1,isLoading:hs(s.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1,defaultValues:hs(s.defaultValues)?void 0:s.defaultValues});a.current||(a.current={...ga(s),formState:i});const n=a.current.control;return n._options=s,ta({subject:n._subjects.state,next:d=>{sa(d,n._proxyFormState,n._updateFormState)&&c({...n._formState})}}),L.useEffect(()=>n._disableForm(s.disabled),[n,s.disabled]),L.useEffect(()=>{if(n._proxyFormState.isDirty){const d=n._getDirty();d!==i.isDirty&&n._subjects.state.next({isDirty:d})}},[n,i.isDirty]),L.useEffect(()=>{s.values&&!Ns(s.values,t.current)?(n._reset(s.values,n._options.resetOptions),t.current=s.values,c(d=>({...d}))):n._resetDefaultValues()},[s.values,n]),L.useEffect(()=>{s.errors&&n._setErrors(s.errors)},[s.errors,n]),L.useEffect(()=>{n._state.mount||(n._updateValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),L.useEffect(()=>{s.shouldUnregister&&n._subjects.values.next({values:n._getWatch()})},[s.shouldUnregister,n]),L.useEffect(()=>{a.current&&(a.current.watch=a.current.watch.bind({}))},[i]),a.current.formState=ea(i,n),a.current}function Ne(){return Ne=Object.assign?Object.assign.bind():function(s){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var i in t)({}).hasOwnProperty.call(t,i)&&(s[i]=t[i])}return s},Ne.apply(null,arguments)}function Nt(s,a){if(s==null)return{};var t={};for(var i in s)if({}.hasOwnProperty.call(s,i)){if(a.includes(i))continue;t[i]=s[i]}return t}function Ds(s){var a=p.useRef({fn:s,curr:void 0}).current;if(a.fn=s,!a.curr){var t=Object.create(null);Object.keys(s).forEach(function(i){t[i]=function(){var c;return(c=a.fn[i]).call.apply(c,[a.fn].concat([].slice.call(arguments)))}}),a.curr=t}return a.curr}function bt(s){return p.useReducer(function(a,t){return Ne({},a,typeof t=="function"?t(a):t)},s)}var kr=p.createContext(void 0),nr="cubic-bezier(0.25, 0.8, 0.25, 1)",Hs=20,os=typeof window<"u"&&"ontouchstart"in window,Dt=function(s,a,t){return Math.max(Math.min(s,t),a)},dt=function(s,a,t){return a===void 0&&(a=0),t===void 0&&(t=0),Dt(s,1*(1-t),Math.max(6,a)*(1+t))},Mt=typeof window>"u"||/ServerSideRendering/.test(navigator&&navigator.userAgent)?p.useEffect:p.useLayoutEffect;function Ys(s,a,t){var i=p.useRef(a);i.current=a,p.useEffect(function(){function c(n){i.current(n)}return s&&window.addEventListener(s,c,t),function(){s&&window.removeEventListener(s,c)}},[s])}var pa=["container"];function va(s){var a=s.container,t=a===void 0?document.body:a,i=Nt(s,pa);return Ft.createPortal(L.createElement("div",Ne({},i)),t)}function ya(s){return L.createElement("svg",Ne({width:"44",height:"44",viewBox:"0 0 768 768"},s),L.createElement("path",{d:"M607.5 205.5l-178.5 178.5 178.5 178.5-45 45-178.5-178.5-178.5 178.5-45-45 178.5-178.5-178.5-178.5 45-45 178.5 178.5 178.5-178.5z"}))}function ba(s){return L.createElement("svg",Ne({width:"44",height:"44",viewBox:"0 0 768 768"},s),L.createElement("path",{d:"M640.5 352.5v63h-390l178.5 180-45 45-256.5-256.5 256.5-256.5 45 45-178.5 180h390z"}))}function ja(s){return L.createElement("svg",Ne({width:"44",height:"44",viewBox:"0 0 768 768"},s),L.createElement("path",{d:"M384 127.5l256.5 256.5-256.5 256.5-45-45 178.5-180h-390v-63h390l-178.5-180z"}))}function Na(){return p.useEffect(function(){var s=document.body.style,a=s.overflow;return s.overflow="hidden",function(){s.overflow=a}},[]),null}function lr(s){var a=s.touches[0],t=a.clientX,i=a.clientY;if(s.touches.length>=2){var c=s.touches[1],n=c.clientX,d=c.clientY;return[(t+n)/2,(i+d)/2,Math.sqrt(Math.pow(n-t,2)+Math.pow(d-i,2))]}return[t,i,0]}var js=function(s,a,t,i){var c,n=t*a,d=(n-i)/2,x=s;return n<=i?(c=1,x=0):s>0&&d-s<=0?(c=2,x=d):s<0&&d+s<=0&&(c=3,x=-d),[c,x]};function It(s,a,t,i,c,n,d,x,m,j){d===void 0&&(d=innerWidth/2),x===void 0&&(x=innerHeight/2),m===void 0&&(m=0),j===void 0&&(j=0);var b=js(s,n,t,innerWidth)[0],h=js(a,n,i,innerHeight),U=innerWidth/2,V=innerHeight/2;return{x:d-n/c*(d-(U+s))-U+(i/t>=3&&t*n===innerWidth?0:b?m/2:m),y:x-n/c*(x-(V+a))-V+(h[0]?j/2:j),lastCX:d,lastCY:x}}function Rt(s,a,t){var i=s%180!=0;return i?[t,a,i]:[a,t,i]}function Pt(s,a,t){var i=Rt(t,innerWidth,innerHeight),c=i[0],n=i[1],d=0,x=c,m=n,j=s/a*n,b=a/s*c;return s<c&&a<n?(x=s,m=a):s<c&&a>=n?x=j:s>=c&&a<n||s/a>c/n?m=b:a/s>=3&&!i[2]?d=((m=b)-n)/2:x=j,{width:x,height:m,x:0,y:d,pause:!0}}function ut(s,a){var t=a.leading,i=t!==void 0&&t,c=a.maxWait,n=a.wait,d=n===void 0?c||0:n,x=p.useRef(s);x.current=s;var m=p.useRef(0),j=p.useRef(),b=function(){return j.current&&clearTimeout(j.current)},h=p.useCallback(function(){var U=[].slice.call(arguments),V=Date.now();function T(){m.current=V,b(),x.current.apply(null,U)}var B=m.current,P=V-B;if(B===0&&(i&&T(),m.current=V),c!==void 0){if(P>c)return void T()}else P<d&&(m.current=V);b(),j.current=setTimeout(function(){T(),m.current=0},d)},[d,c,i]);return h.cancel=b,h}var cr=function(s,a,t){return _s(s,a,t,100,function(i){return i},function(){return _s(a,s,t)})},wa=function(s){return 1-Math.pow(1-s,4)};function _s(s,a,t,i,c,n){i===void 0&&(i=400),c===void 0&&(c=wa);var d=a-s;if(d!==0){var x=Date.now(),m=0,j=function(){var h=Math.min(1,(Date.now()-x)/i);t(s+c(h)*d)&&h<1?b():(cancelAnimationFrame(m),h>=1&&n&&n())};b()}function b(){m=requestAnimationFrame(j)}}var Ca={T:0,L:0,W:0,H:0,FIT:void 0},Sr=function(){var s=p.useRef(!1);return p.useEffect(function(){return s.current=!0,function(){s.current=!1}},[]),s},ka=["className"];function Sa(s){var a=s.className,t=a===void 0?"":a,i=Nt(s,ka);return L.createElement("div",Ne({className:"PhotoView__Spinner "+t},i),L.createElement("svg",{viewBox:"0 0 32 32",width:"36",height:"36",fill:"white"},L.createElement("path",{opacity:".25",d:"M16 0 A16 16 0 0 0 16 32 A16 16 0 0 0 16 0 M16 4 A12 12 0 0 1 16 28 A12 12 0 0 1 16 4"}),L.createElement("path",{d:"M16 0 A16 16 0 0 1 32 16 L28 16 A12 12 0 0 0 16 4z"})))}var Ea=["src","loaded","broken","className","onPhotoLoad","loadingElement","brokenElement"];function Aa(s){var a=s.src,t=s.loaded,i=s.broken,c=s.className,n=s.onPhotoLoad,d=s.loadingElement,x=s.brokenElement,m=Nt(s,Ea),j=Sr();return a&&!i?L.createElement(L.Fragment,null,L.createElement("img",Ne({className:"PhotoView__Photo"+(c?" "+c:""),src:a,onLoad:function(b){var h=b.target;j.current&&n({loaded:!0,naturalWidth:h.naturalWidth,naturalHeight:h.naturalHeight})},onError:function(){j.current&&n({broken:!0})},draggable:!1,alt:""},m)),!t&&(d?L.createElement("span",{className:"PhotoView__icon"},d):L.createElement(Sa,{className:"PhotoView__icon"}))):x?L.createElement("span",{className:"PhotoView__icon"},typeof x=="function"?x({src:a}):x):null}var Ia={naturalWidth:void 0,naturalHeight:void 0,width:void 0,height:void 0,loaded:void 0,broken:!1,x:0,y:0,touched:!1,maskTouched:!1,rotate:0,scale:1,CX:0,CY:0,lastX:0,lastY:0,lastCX:0,lastCY:0,lastScale:1,touchTime:0,touchLength:0,pause:!0,stopRaf:!0,reach:void 0};function Pa(s){var a=s.item,t=a.src,i=a.render,c=a.width,n=c===void 0?0:c,d=a.height,x=d===void 0?0:d,m=a.originRef,j=s.visible,b=s.speed,h=s.easing,U=s.wrapClassName,V=s.className,T=s.style,B=s.loadingElement,P=s.brokenElement,F=s.onPhotoTap,E=s.onMaskTap,X=s.onReachMove,te=s.onReachUp,K=s.onPhotoResize,re=s.isActive,me=s.expose,_=bt(Ia),R=_[0],O=_[1],D=p.useRef(0),N=Sr(),A=R.naturalWidth,z=A===void 0?n:A,W=R.naturalHeight,Q=W===void 0?x:W,u=R.width,C=u===void 0?n:u,M=R.height,H=M===void 0?x:M,k=R.loaded,fe=k===void 0?!t:k,se=R.broken,ee=R.x,we=R.y,ce=R.touched,oe=R.stopRaf,Pe=R.maskTouched,Ce=R.rotate,ge=R.scale,Ue=R.CX,ze=R.CY,Ve=R.lastX,Be=R.lastY,le=R.lastCX,ws=R.lastCY,f=R.lastScale,r=R.touchTime,l=R.touchLength,o=R.pause,w=R.reach,y=Ds({onScale:function(g){return v(dt(g))},onRotate:function(g){Ce!==g&&(me({rotate:g}),O(Ne({rotate:g},Pt(z,Q,g))))}});function v(g,$,ie){ge!==g&&(me({scale:g}),O(Ne({scale:g},It(ee,we,C,H,ge,g,$,ie),g<=1&&{x:0,y:0})))}var I=ut(function(g,$,ie){if(ie===void 0&&(ie=0),(ce||Pe)&&re){var Ze=Rt(Ce,C,H),es=Ze[0],De=Ze[1];if(ie===0&&D.current===0){var Te=Math.abs(g-Ue)<=20,_e=Math.abs($-ze)<=20;if(Te&&_e)return void O({lastCX:g,lastCY:$});D.current=Te?$>ze?3:2:1}var Ye,ks=g-le,xs=$-ws;if(ie===0){var Ss=js(ks+Ve,ge,es,innerWidth)[0],$s=js(xs+Be,ge,De,innerHeight);Ye=function(ps,ts,rs,Xe){return ts&&ps===1||Xe==="x"?"x":rs&&ps>1||Xe==="y"?"y":void 0}(D.current,Ss,$s[0],w),Ye!==void 0&&X(Ye,g,$,ge)}if(Ye==="x"||Pe)return void O({reach:"x"});var ss=dt(ge+(ie-l)/100/2*ge,z/C,.2);me({scale:ss}),O(Ne({touchLength:ie,reach:Ye,scale:ss},It(ee,we,C,H,ge,ss,g,$,ks,xs)))}},{maxWait:8});function q(g){return!oe&&!ce&&(N.current&&O(Ne({},g,{pause:j})),N.current)}var G,ae,ke,Cs,fs,gs,Rs,Se,Os=(fs=function(g){return q({x:g})},gs=function(g){return q({y:g})},Rs=function(g){return N.current&&(me({scale:g}),O({scale:g})),!ce&&N.current},Se=Ds({X:function(g){return fs(g)},Y:function(g){return gs(g)},S:function(g){return Rs(g)}}),function(g,$,ie,Ze,es,De,Te,_e,Ye,ks,xs){var Ss=Rt(ks,es,De),$s=Ss[0],ss=Ss[1],ps=js(g,_e,$s,innerWidth),ts=ps[0],rs=ps[1],Xe=js($,_e,ss,innerHeight),qs=Xe[0],ot=Xe[1],Ws=Date.now()-xs;if(Ws>=200||_e!==Te||Math.abs(Ye-Te)>1){var vs=It(g,$,es,De,Te,_e),as=vs.x,Es=vs.y,Me=ts?rs:as!==g?as:null,Us=qs?ot:Es!==$?Es:null;return Me!==null&&_s(g,Me,Se.X),Us!==null&&_s($,Us,Se.Y),void(_e!==Te&&_s(Te,_e,Se.S))}var As=(g-ie)/Ws,ys=($-Ze)/Ws,bs=Math.sqrt(Math.pow(As,2)+Math.pow(ys,2)),is=!1,ns=!1;(function($e,Ee){var Ae,qe=$e,ls=0,Re=0,zs=function(tt){Ae||(Ae=tt);var rt=tt-Ae,Bt=Math.sign($e),Ht=-.001*Bt,Yt=Math.sign(-qe)*Math.pow(qe,2)*2e-4,Ar=qe*rt+(Ht+Yt)*Math.pow(rt,2)/2;ls+=Ar,Ae=tt,Bt*(qe+=(Ht+Yt)*rt)<=0?Oe():Ee(ls)?Qe():Oe()};function Qe(){Re=requestAnimationFrame(zs)}function Oe(){cancelAnimationFrame(Re)}Qe()})(bs,function($e){var Ee=g+$e*(As/bs),Ae=$+$e*(ys/bs),qe=js(Ee,Te,$s,innerWidth),ls=qe[0],Re=qe[1],zs=js(Ae,Te,ss,innerHeight),Qe=zs[0],Oe=zs[1];if(ls&&!is&&(is=!0,ts?_s(Ee,Re,Se.X):cr(Re,Ee+(Ee-Re),Se.X)),Qe&&!ns&&(ns=!0,qs?_s(Ae,Oe,Se.Y):cr(Oe,Ae+(Ae-Oe),Se.Y)),is&&ns)return!1;var tt=is||Se.X(Re),rt=ns||Se.Y(Oe);return tt&&rt})}),Je=(G=F,ae=function(g,$){w||v(ge!==1?1:Math.max(2,z/C),g,$)},ke=p.useRef(0),Cs=ut(function(){ke.current=0,G.apply(void 0,[].slice.call(arguments))},{wait:300}),function(){var g=[].slice.call(arguments);ke.current+=1,Cs.apply(void 0,g),ke.current>=2&&(Cs.cancel(),ke.current=0,ae.apply(void 0,g))});function Ls(g,$){if(D.current=0,(ce||Pe)&&re){O({touched:!1,maskTouched:!1,pause:!1,stopRaf:!1,reach:void 0});var ie=dt(ge,z/C);if(Os(ee,we,Ve,Be,C,H,ge,ie,f,Ce,r),te(g,$),Ue===g&&ze===$){if(ce)return void Je(g,$);Pe&&E(g,$)}}}function st(g,$,ie){ie===void 0&&(ie=0),O({touched:!0,CX:g,CY:$,lastCX:g,lastCY:$,lastX:ee,lastY:we,lastScale:ge,touchLength:ie,touchTime:Date.now()})}function ct(g){O({maskTouched:!0,CX:g.clientX,CY:g.clientY,lastX:ee,lastY:we})}Ys(os?void 0:"mousemove",function(g){g.preventDefault(),I(g.clientX,g.clientY)}),Ys(os?void 0:"mouseup",function(g){Ls(g.clientX,g.clientY)}),Ys(os?"touchmove":void 0,function(g){g.preventDefault();var $=lr(g);I.apply(void 0,$)},{passive:!1}),Ys(os?"touchend":void 0,function(g){var $=g.changedTouches[0];Ls($.clientX,$.clientY)},{passive:!1}),Ys("resize",ut(function(){fe&&!ce&&(O(Pt(z,Q,Ce)),K())},{maxWait:8})),Mt(function(){re&&me(Ne({scale:ge,rotate:Ce},y))},[re]);var He=function(g,$,ie,Ze,es,De,Te,_e,Ye,ks){var xs=function(as,Es,Me,Us,As){var ys=p.useRef(!1),bs=bt({lead:!0,scale:Me}),is=bs[0],ns=is.lead,$e=is.scale,Ee=bs[1],Ae=ut(function(qe){try{return As(!0),Ee({lead:!1,scale:qe}),Promise.resolve()}catch(ls){return Promise.reject(ls)}},{wait:Us});return Mt(function(){ys.current?(As(!1),Ee({lead:!0}),Ae(Me)):ys.current=!0},[Me]),ns?[as*$e,Es*$e,Me/$e]:[as*Me,Es*Me,1]}(De,Te,_e,Ye,ks),Ss=xs[0],$s=xs[1],ss=xs[2],ps=function(as,Es,Me,Us,As){var ys=p.useState(Ca),bs=ys[0],is=ys[1],ns=p.useState(0),$e=ns[0],Ee=ns[1],Ae=p.useRef(),qe=Ds({OK:function(){return as&&Ee(4)}});function ls(Re){As(!1),Ee(Re)}return p.useEffect(function(){if(Ae.current||(Ae.current=Date.now()),Me){if(function(Re,zs){var Qe=Re&&Re.current;if(Qe&&Qe.nodeType===1){var Oe=Qe.getBoundingClientRect();zs({T:Oe.top,L:Oe.left,W:Oe.width,H:Oe.height,FIT:Qe.tagName==="IMG"?getComputedStyle(Qe).objectFit:void 0})}}(Es,is),as)return Date.now()-Ae.current<250?(Ee(1),requestAnimationFrame(function(){Ee(2),requestAnimationFrame(function(){return ls(3)})}),void setTimeout(qe.OK,Us)):void Ee(4);ls(5)}},[as,Me]),[$e,bs]}(g,$,ie,Ye,ks),ts=ps[0],rs=ps[1],Xe=rs.W,qs=rs.FIT,ot=innerWidth/2,Ws=innerHeight/2,vs=ts<3||ts>4;return[vs?Xe?rs.L:ot:Ze+(ot-De*_e/2),vs?Xe?rs.T:Ws:es+(Ws-Te*_e/2),Ss,vs&&qs?Ss*(rs.H/Xe):$s,ts===0?ss:vs?Xe/(De*_e)||.01:ss,vs?qs?1:0:1,ts,qs]}(j,m,fe,ee,we,C,H,ge,b,function(g){return O({pause:g})}),Y=He[4],de=He[6],be="transform "+b+"ms "+h,ve={className:V,onMouseDown:os?void 0:function(g){g.stopPropagation(),g.button===0&&st(g.clientX,g.clientY,0)},onTouchStart:os?function(g){g.stopPropagation(),st.apply(void 0,lr(g))}:void 0,onWheel:function(g){if(!w){var $=dt(ge-g.deltaY/100/2,z/C);O({stopRaf:!0}),v($,g.clientX,g.clientY)}},style:{width:He[2]+"px",height:He[3]+"px",opacity:He[5],objectFit:de===4?void 0:He[7],transform:Ce?"rotate("+Ce+"deg)":void 0,transition:de>2?be+", opacity "+b+"ms ease, height "+(de<4?b/2:de>4?b:0)+"ms "+h:void 0}};return L.createElement("div",{className:"PhotoView__PhotoWrap"+(U?" "+U:""),style:T,onMouseDown:!os&&re?ct:void 0,onTouchStart:os&&re?function(g){return ct(g.touches[0])}:void 0},L.createElement("div",{className:"PhotoView__PhotoBox",style:{transform:"matrix("+Y+", 0, 0, "+Y+", "+He[0]+", "+He[1]+")",transition:ce||o?void 0:be,willChange:re?"transform":void 0}},t?L.createElement(Aa,Ne({src:t,loaded:fe,broken:se},ve,{onPhotoLoad:function(g){O(Ne({},g,g.loaded&&Pt(g.naturalWidth||0,g.naturalHeight||0,Ce)))},loadingElement:B,brokenElement:P})):i&&i({attrs:ve,scale:Y,rotate:Ce})))}var or={x:0,touched:!1,pause:!1,lastCX:void 0,lastCY:void 0,bg:void 0,lastBg:void 0,overlay:!0,minimal:!0,scale:1,rotate:0};function Fa(s){var a=s.loop,t=a===void 0?3:a,i=s.speed,c=s.easing,n=s.photoClosable,d=s.maskClosable,x=d===void 0||d,m=s.maskOpacity,j=m===void 0?1:m,b=s.pullClosable,h=b===void 0||b,U=s.bannerVisible,V=U===void 0||U,T=s.overlayRender,B=s.toolbarRender,P=s.className,F=s.maskClassName,E=s.photoClassName,X=s.photoWrapClassName,te=s.loadingElement,K=s.brokenElement,re=s.images,me=s.index,_=me===void 0?0:me,R=s.onIndexChange,O=s.visible,D=s.onClose,N=s.afterClose,A=s.portalContainer,z=bt(or),W=z[0],Q=z[1],u=p.useState(0),C=u[0],M=u[1],H=W.x,k=W.touched,fe=W.pause,se=W.lastCX,ee=W.lastCY,we=W.bg,ce=we===void 0?j:we,oe=W.lastBg,Pe=W.overlay,Ce=W.minimal,ge=W.scale,Ue=W.rotate,ze=W.onScale,Ve=W.onRotate,Be=s.hasOwnProperty("index"),le=Be?_:C,ws=Be?R:M,f=p.useRef(le),r=re.length,l=re[le],o=typeof t=="boolean"?t:r>t,w=function(Y,de){var be=p.useReducer(function(ie){return!ie},!1)[1],ve=p.useRef(0),g=function(ie){var Ze=p.useRef(ie);function es(De){Ze.current=De}return p.useMemo(function(){(function(De){Y?(De(Y),ve.current=1):ve.current=2})(es)},[ie]),[Ze.current,es]}(Y),$=g[1];return[g[0],ve.current,function(){be(),ve.current===2&&($(!1),de&&de()),ve.current=0}]}(O,N),y=w[0],v=w[1],I=w[2];Mt(function(){if(y)return Q({pause:!0,x:le*-(innerWidth+Hs)}),void(f.current=le);Q(or)},[y]);var q=Ds({close:function(Y){Ve&&Ve(0),Q({overlay:!0,lastBg:ce}),D(Y)},changeIndex:function(Y,de){de===void 0&&(de=!1);var be=o?f.current+(Y-le):Y,ve=r-1,g=Dt(be,0,ve),$=o?be:g,ie=innerWidth+Hs;Q({touched:!1,lastCX:void 0,lastCY:void 0,x:-ie*$,pause:de}),f.current=$,ws&&ws(o?Y<0?ve:Y>ve?0:Y:g)}}),G=q.close,ae=q.changeIndex;function ke(Y){return Y?G():Q({overlay:!Pe})}function Cs(){Q({x:-(innerWidth+Hs)*le,lastCX:void 0,lastCY:void 0,pause:!0}),f.current=le}function fs(Y,de,be,ve){Y==="x"?function(g){if(se!==void 0){var $=g-se,ie=$;!o&&(le===0&&$>0||le===r-1&&$<0)&&(ie=$/2),Q({touched:!0,lastCX:se,x:-(innerWidth+Hs)*f.current+ie,pause:!1})}else Q({touched:!0,lastCX:g,x:H,pause:!1})}(de):Y==="y"&&function(g,$){if(ee!==void 0){var ie=j===null?null:Dt(j,.01,j-Math.abs(g-ee)/100/4);Q({touched:!0,lastCY:ee,bg:$===1?ie:j,minimal:$===1})}else Q({touched:!0,lastCY:g,bg:ce,minimal:!0})}(be,ve)}function gs(Y,de){var be=Y-(se??Y),ve=de-(ee??de),g=!1;if(be<-40)ae(le+1);else if(be>40)ae(le-1);else{var $=-(innerWidth+Hs)*f.current;Math.abs(ve)>100&&Ce&&h&&(g=!0,G()),Q({touched:!1,x:$,lastCX:void 0,lastCY:void 0,bg:j,overlay:!!g||Pe})}}Ys("keydown",function(Y){if(O)switch(Y.key){case"ArrowLeft":ae(le-1,!0);break;case"ArrowRight":ae(le+1,!0);break;case"Escape":G()}});var Rs=function(Y,de,be){return p.useMemo(function(){var ve=Y.length;return be?Y.concat(Y).concat(Y).slice(ve+de-1,ve+de+2):Y.slice(Math.max(de-1,0),Math.min(de+2,ve+1))},[Y,de,be])}(re,le,o);if(!y)return null;var Se=Pe&&!v,Os=O?ce:oe,Je=ze&&Ve&&{images:re,index:le,visible:O,onClose:G,onIndexChange:ae,overlayVisible:Se,overlay:l&&l.overlay,scale:ge,rotate:Ue,onScale:ze,onRotate:Ve},Ls=i?i(v):400,st=c?c(v):nr,ct=i?i(3):600,He=c?c(3):nr;return L.createElement(va,{className:"PhotoView-Portal"+(Se?"":" PhotoView-Slider__clean")+(O?"":" PhotoView-Slider__willClose")+(P?" "+P:""),role:"dialog",onClick:function(Y){return Y.stopPropagation()},container:A},O&&L.createElement(Na,null),L.createElement("div",{className:"PhotoView-Slider__Backdrop"+(F?" "+F:"")+(v===1?" PhotoView-Slider__fadeIn":v===2?" PhotoView-Slider__fadeOut":""),style:{background:Os?"rgba(0, 0, 0, "+Os+")":void 0,transitionTimingFunction:st,transitionDuration:(k?0:Ls)+"ms",animationDuration:Ls+"ms"},onAnimationEnd:I}),V&&L.createElement("div",{className:"PhotoView-Slider__BannerWrap"},L.createElement("div",{className:"PhotoView-Slider__Counter"},le+1," / ",r),L.createElement("div",{className:"PhotoView-Slider__BannerRight"},B&&Je&&B(Je),L.createElement(ya,{className:"PhotoView-Slider__toolbarIcon",onClick:G}))),Rs.map(function(Y,de){var be=o||le!==0?f.current-1+de:le+de;return L.createElement(Pa,{key:o?Y.key+"/"+Y.src+"/"+be:Y.key,item:Y,speed:Ls,easing:st,visible:O,onReachMove:fs,onReachUp:gs,onPhotoTap:function(){return ke(n)},onMaskTap:function(){return ke(x)},wrapClassName:X,className:E,style:{left:(innerWidth+Hs)*be+"px",transform:"translate3d("+H+"px, 0px, 0)",transition:k||fe?void 0:"transform "+ct+"ms "+He},loadingElement:te,brokenElement:K,onPhotoResize:Cs,isActive:f.current===be,expose:Q})}),!os&&V&&L.createElement(L.Fragment,null,(o||le!==0)&&L.createElement("div",{className:"PhotoView-Slider__ArrowLeft",onClick:function(){return ae(le-1,!0)}},L.createElement(ba,null)),(o||le+1<r)&&L.createElement("div",{className:"PhotoView-Slider__ArrowRight",onClick:function(){return ae(le+1,!0)}},L.createElement(ja,null))),T&&Je&&L.createElement("div",{className:"PhotoView-Slider__Overlay"},T(Je)))}var Ta=["children","onIndexChange","onVisibleChange"],_a={images:[],visible:!1,index:0};function Ba(s){var a=s.children,t=s.onIndexChange,i=s.onVisibleChange,c=Nt(s,Ta),n=bt(_a),d=n[0],x=n[1],m=p.useRef(0),j=d.images,b=d.visible,h=d.index,U=Ds({nextId:function(){return m.current+=1},update:function(B){var P=j.findIndex(function(E){return E.key===B.key});if(P>-1){var F=j.slice();return F.splice(P,1,B),void x({images:F})}x(function(E){return{images:E.images.concat(B)}})},remove:function(B){x(function(P){var F=P.images.filter(function(E){return E.key!==B});return{images:F,index:Math.min(F.length-1,h)}})},show:function(B){var P=j.findIndex(function(F){return F.key===B});x({visible:!0,index:P}),i&&i(!0,P,d)}}),V=Ds({close:function(){x({visible:!1}),i&&i(!1,h,d)},changeIndex:function(B){x({index:B}),t&&t(B,d)}}),T=p.useMemo(function(){return Ne({},d,U)},[d,U]);return L.createElement(kr.Provider,{value:T},a,L.createElement(Fa,Ne({images:j,visible:b,index:h,onIndexChange:V.changeIndex,onClose:V.close},c)))}var Va=function(s){var a,t,i=s.src,c=s.render,n=s.overlay,d=s.width,x=s.height,m=s.triggers,j=m===void 0?["onClick"]:m,b=s.children,h=p.useContext(kr),U=(a=function(){return h.nextId()},(t=p.useRef({sign:!1,fn:void 0}).current).sign||(t.sign=!0,t.fn=a()),t.fn),V=p.useRef(null);p.useImperativeHandle(b?.ref,function(){return V.current}),p.useEffect(function(){return function(){h.remove(U)}},[]);var T=Ds({render:function(P){return c&&c(P)},show:function(P,F){h.show(U),function(E,X){if(b){var te=b.props[E];te&&te(X)}}(P,F)}}),B=p.useMemo(function(){var P={};return j.forEach(function(F){P[F]=T.show.bind(null,F)}),P},[]);return p.useEffect(function(){h.update({key:U,src:i,originRef:V,render:T.render,overlay:n,width:d,height:x})},[i]),b?p.Children.only(p.cloneElement(b,Ne({},B,{ref:V}))):null};const Er=({imageUrl:s,onEnhancedImage:a,isOpen:t,onClose:i})=>{const[c,n]=p.useState(!1),[d,x]=p.useState(0),[m,j]=p.useState(null),b=async()=>{if(!s){j("No image provided for enhancement");return}n(!0),x(0),j(null);try{const h=setInterval(()=>{x(V=>{const T=V+5;return T>=95?95:T})},100);await new Promise(V=>setTimeout(V,2e3)),clearInterval(h),x(100),a(s),ne.success("Image enhanced successfully"),setTimeout(()=>{i(),n(!1)},500)}catch(h){console.error("Error enhancing image:",h),j("Failed to enhance image. Please try again."),n(!1)}};return e.jsx(Ks,{open:t,onOpenChange:h=>!h&&i(),children:e.jsxs(Gs,{className:"sm:max-w-md",children:[e.jsx(Js,{children:e.jsxs(Zs,{className:"flex items-center gap-2",children:[e.jsx(xt,{className:"h-5 w-5"}),"AI Image Enhancement"]})}),e.jsxs("div",{className:"space-y-4 py-4",children:[m&&e.jsx(Ot,{variant:"destructive",className:"mb-4",children:e.jsx(Lt,{children:m})}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-4",children:[s?e.jsx("div",{className:"relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden",children:e.jsx("img",{src:s,alt:"Original",className:"max-w-full max-h-full object-contain",onError:h=>{h.currentTarget.src="/placeholder.svg"}})}):e.jsxs("div",{className:"flex flex-col items-center justify-center w-full h-64 bg-muted/20 rounded-md",children:[e.jsx(Qr,{className:"h-12 w-12 text-muted-foreground mb-2"}),e.jsx("p",{className:"text-muted-foreground",children:"No image available"})]}),c&&e.jsxs("div",{className:"w-full space-y-2",children:[e.jsx(xr,{value:d,className:"h-2"}),e.jsxs("p",{className:"text-sm text-center text-muted-foreground",children:["Enhancing image... ",d,"%"]})]}),e.jsxs("div",{className:"text-sm text-muted-foreground text-center",children:[e.jsx("p",{children:"Our AI will:"}),e.jsxs("ul",{className:"list-disc list-inside mt-2 text-left",children:[e.jsx("li",{children:"Remove the background"}),e.jsx("li",{children:"Replace with a clean white background"}),e.jsx("li",{children:"Enhance image quality"}),e.jsx("li",{children:"Preserve the product exactly as it appears"})]})]})]})]}),e.jsxs(et,{children:[e.jsx(Z,{variant:"outline",onClick:i,disabled:c,children:"Cancel"}),e.jsx(Z,{onClick:b,disabled:c||!s,children:c?e.jsxs(e.Fragment,{children:[e.jsx(ms,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(xt,{className:"mr-2 h-4 w-4"}),"Enhance Image"]})})]})]})})},Da=p.memo(({value:s,onChange:a,onTemporaryImageChange:t,isEditable:i=!1,productId:c})=>{const n=s||"/placeholder.svg",[d,x]=p.useState(!1),[m,j]=p.useState(!1),[b,h]=p.useState(s),[U,V]=p.useState(null),[T,B]=p.useState(!1),P=p.useRef(null),F=p.useRef(!1);p.useEffect(()=>{h(s),V(null)},[s]);const E=async _=>{const R=_.target.files?.[0];if(R){if(!R.type.startsWith("image/")){ne.error("Please select an image file"),P.current&&(P.current.value="");return}if(R.size>5*1024*1024){ne.error("Image size should be less than 5MB"),P.current&&(P.current.value="");return}x(!0),ne.info("Processing image...");try{const O=await dr(R);if(h(O),V(R),t){const D={id:`temp_${Date.now()}`,file:R,dataUrl:O,type:"primary"};t(D)}ne.success("Image processed successfully (will be uploaded when you save the product)")}catch(O){console.error("❌ Error processing image:",O),ne.error(`Failed to process image: ${O instanceof Error?O.message:"Unknown error"}`)}finally{x(!1),P.current&&(P.current.value="")}}},X=()=>{h(""),V(null),t&&t(null),ne.success("Image removed")},te=()=>{a&&b!==s&&(a(b||""),ne.success("Image changes saved (will be uploaded when you save the product)")),j(!1)},K=p.useCallback(_=>{_?F.current||j(!0):(h(s),V(null),t&&t(null),j(!1))},[s,t]),re=p.useCallback(()=>e.jsx(Ks,{open:m,onOpenChange:K,modal:!0,children:e.jsxs(Gs,{className:"sm:max-w-md max-h-[90vh] overflow-hidden flex flex-col",style:{zIndex:9999,position:"fixed"},onPointerDownOutside:_=>{_.preventDefault()},onEscapeKeyDown:_=>{d&&_.preventDefault()},onClick:_=>{_.stopPropagation()},children:[e.jsx(Js,{children:e.jsx(Zs,{children:"Image"})}),e.jsxs("div",{className:"flex-1 overflow-auto p-4",children:[e.jsx("div",{className:"relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden",children:d?e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(ms,{className:"h-8 w-8 animate-spin text-primary"}),e.jsx("span",{className:"text-sm mt-2",children:"Processing image..."})]}):b?e.jsx(Va,{src:b,children:e.jsx("img",{src:b,alt:"Product",className:"max-w-full max-h-full object-contain cursor-zoom-in",onError:_=>{_.currentTarget.src="/placeholder.svg"}})}):e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(Qt,{className:"w-12 h-12"}),e.jsx("span",{className:"text-sm mt-2",children:"No image available"})]})}),i&&!d&&e.jsxs("div",{className:"mt-4 flex flex-col gap-2",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Drag and drop an image here or click the button below to upload."}),e.jsxs(Z,{variant:"outline",className:"w-full",onClick:_=>{_.stopPropagation(),P.current?.click()},disabled:d,children:[e.jsx(mt,{className:"mr-2 h-4 w-4"}),b?"Change Image":"Upload Image"]})]})]}),i&&e.jsxs(et,{className:"flex sm:justify-between border-t pt-4",children:[e.jsx("div",{className:"flex gap-2",children:b&&!d&&e.jsxs(e.Fragment,{children:[e.jsxs(Z,{variant:"destructive",size:"sm",onClick:_=>{_.stopPropagation(),X()},children:[e.jsx(Ms,{className:"mr-2 h-4 w-4"}),"Remove"]}),e.jsxs(Z,{variant:"outline",size:"sm",onClick:_=>{_.stopPropagation(),B(!0)},children:[e.jsx(xt,{className:"mr-2 h-4 w-4"}),"AI Enhance"]})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Z,{variant:"secondary",size:"sm",onClick:_=>{_.stopPropagation(),K(!1)},disabled:d,children:"Cancel"}),e.jsxs(Z,{variant:"default",size:"sm",onClick:_=>{_.stopPropagation(),te()},disabled:d,children:[e.jsx($t,{className:"mr-2 h-4 w-4"}),"Save"]})]})]})]})}),[m,b,d,i,K,X,te,P]),me=p.useCallback(_=>{_.stopPropagation(),_.preventDefault(),!m&&!F.current&&(F.current=!0,setTimeout(()=>{j(!0),F.current=!1},50))},[m]);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:Tt("relative w-12 h-12 rounded-md overflow-hidden border border-border flex items-center justify-center group",!s&&"bg-muted","cursor-pointer hover:bg-accent/50 transition-colors"),onClick:me,onMouseDown:_=>_.stopPropagation(),onMouseUp:_=>_.stopPropagation(),role:"button",tabIndex:0,"aria-label":s?"View or edit image":"Add image",children:d?e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(ms,{className:"h-5 w-5 animate-spin text-primary"}),e.jsx("span",{className:"text-[10px] mt-1",children:"Processing..."})]}):s?e.jsx("div",{className:"w-full h-full flex items-center justify-center p-1",children:e.jsx("img",{src:n,alt:"Product",className:"max-w-full max-h-full object-contain group-hover:scale-105 transition-transform",onError:_=>{_.currentTarget.src="/placeholder.svg"}})}):e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(Qt,{className:"w-5 h-5"}),e.jsx("span",{className:"text-[10px] mt-0.5",children:"Add Image"})]})}),i&&e.jsx("input",{ref:P,type:"file",accept:"image/*",className:"hidden",onChange:E,"aria-hidden":"true"}),typeof window<"u"&&m&&Ft.createPortal(e.jsx(re,{}),document.body),T&&b&&typeof window<"u"&&Ft.createPortal(e.jsx(Er,{imageUrl:b,onEnhancedImage:_=>{h(_)},isOpen:T,onClose:()=>B(!1)}),document.body)]})}),Ma=p.memo(({value:s=[],onChange:a,onTemporaryImagesChange:t,isEditable:i=!1,productId:c})=>{const[n,d]=p.useState(!1),[x,m]=p.useState(!1),[j,b]=p.useState(0),[h,U]=p.useState(s?.length?s:[]),[V,T]=p.useState([]),[B,P]=p.useState(!1),F=p.useRef(null),E=p.useRef(!1);p.useEffect(()=>{U(s?.length?s:[]),T([])},[s]);const X=s?.length?s:[],te=async D=>{const N=D.target.files;if(!N||N.length===0)return;if(h.length+N.length>50){ne.error("You can upload a maximum of 50 images. Please select fewer images."),F.current&&(F.current.value="");return}const A=Array.from(N).filter(z=>z.type.startsWith("image/")?z.size>5*1024*1024?(ne.error(`File ${z.name} exceeds 5MB limit`),!1):!0:(ne.error(`File ${z.name} is not an image`),!1));if(A.length===0){F.current&&(F.current.value="");return}d(!0),ne.info(`Processing ${A.length} image(s)...`);try{const W=[],Q=[];for(let u=0;u<A.length;u+=5){const C=A.slice(u,u+5);(await Promise.allSettled(C.map(H=>dr(H)))).forEach((H,k)=>{H.status==="fulfilled"?(W.push(H.value),Q.push(C[k])):(console.error(`Failed to process ${C[k].name}:`,H.reason),ne.error(`Failed to process ${C[k].name}`))})}if(W.length>0){if(U(u=>[...u,...W]),T(u=>[...u,...Q]),t){const u=Q.map((C,M)=>({id:`temp_${Date.now()}_${M}`,file:C,dataUrl:W[M],type:"additional"}));t(u)}ne.success(`${W.length} image(s) processed successfully (will be uploaded when you save the product)`)}else ne.error("Failed to process any images")}catch(z){console.error("Error processing images:",z),ne.error("An error occurred while processing images")}finally{d(!1),F.current&&(F.current.value="")}},K=D=>{const N=[...h],A=[...V];if(N.splice(D,1),D<A.length&&A.splice(D,1),U(N),T(A),j>=N.length&&b(Math.max(0,N.length-1)),t&&A.length>0){const z=A.map((W,Q)=>({id:`temp_${Date.now()}_${Q}`,file:W,dataUrl:N[Q],type:"additional"}));t(z)}else t&&t([]);ne.success("Image removed")},re=()=>{a&&JSON.stringify(h)!==JSON.stringify(s)&&(a(h),ne.success("Image changes saved (will be uploaded when you save the product)")),m(!1)},me=p.useCallback(D=>{D?E.current||m(!0):(U(s?.length?s:[]),T([]),t&&t([]),m(!1))},[s,t]),_=D=>{b(D==="prev"?N=>N>0?N-1:N:N=>N<h.length-1?N+1:N)},R=p.useCallback(()=>e.jsx(Ks,{open:x,onOpenChange:me,modal:!0,children:e.jsxs(Gs,{className:"sm:max-w-xl max-h-[90vh] overflow-hidden flex flex-col",onPointerDownOutside:D=>{D.preventDefault()},onEscapeKeyDown:D=>{n&&D.preventDefault()},onClick:D=>{D.stopPropagation()},children:[e.jsx(Js,{children:e.jsxs(Zs,{children:["Images (",h.length,")"]})}),e.jsxs(ur,{defaultValue:"gallery",className:"flex-1 overflow-hidden flex flex-col",children:[e.jsxs(mr,{className:"grid w-full grid-cols-2",children:[e.jsx(ds,{value:"gallery",children:"Gallery View"}),e.jsx(ds,{value:"grid",children:"Grid View"})]}),e.jsx(us,{value:"gallery",className:"flex-1 overflow-auto",children:h.length>0?e.jsxs("div",{className:"flex flex-col gap-4 p-1",children:[e.jsxs("div",{className:"relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden",children:[n?e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(ms,{className:"h-8 w-8 animate-spin text-primary"}),e.jsx("span",{className:"text-sm mt-2",children:"Processing images..."})]}):e.jsx("img",{src:h[j],alt:`Product ${j+1}`,className:"max-w-full max-h-full object-contain",onError:D=>{D.currentTarget.src="/placeholder.svg"}}),h.length>1&&!n&&e.jsxs(e.Fragment,{children:[e.jsx(Z,{variant:"ghost",size:"icon",className:"absolute left-2 bg-black/20 hover:bg-black/40 text-white rounded-full",onClick:()=>_("prev"),disabled:j===0,children:e.jsx(Ir,{className:"h-4 w-4"})}),e.jsx(Z,{variant:"ghost",size:"icon",className:"absolute right-2 bg-black/20 hover:bg-black/40 text-white rounded-full",onClick:()=>_("next"),disabled:j===h.length-1,children:e.jsx(Pr,{className:"h-4 w-4"})})]}),i&&!n&&e.jsx(Z,{variant:"destructive",size:"icon",className:"absolute top-2 right-2 h-7 w-7",onClick:()=>K(j),children:e.jsx(Ms,{className:"h-4 w-4"})})]}),e.jsx(_t,{className:"w-full",orientation:"horizontal",children:e.jsxs("div",{className:"flex gap-2 pb-2 min-w-max",children:[h.map((D,N)=>e.jsx("div",{className:Tt("h-14 w-14 rounded-md overflow-hidden border cursor-pointer flex-shrink-0",j===N?"border-primary border-2":"border-border"),onClick:()=>b(N),children:e.jsx("img",{src:D,alt:`Thumbnail ${N+1}`,className:"h-full w-full object-cover",onError:A=>{A.currentTarget.src="/placeholder.svg"}})},N)),i&&!n&&e.jsx("div",{className:"h-14 w-14 rounded-md border border-dashed border-muted-foreground/30 flex items-center justify-center cursor-pointer hover:bg-muted/50 flex-shrink-0",onClick:()=>F.current?.click(),children:e.jsx(Xs,{className:"h-6 w-6 text-muted-foreground"})})]})})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 gap-4",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(Ct,{className:"w-16 h-16"}),e.jsx("span",{className:"text-sm mt-2",children:"No images available"})]}),i&&!n&&e.jsxs(Z,{variant:"outline",onClick:()=>F.current?.click(),disabled:n,children:[e.jsx(mt,{className:"mr-2 h-4 w-4"}),"Upload Images"]}),n&&e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(ms,{className:"h-8 w-8 animate-spin text-primary"}),e.jsx("span",{className:"text-sm mt-2",children:"Processing images..."})]})]})}),e.jsx(us,{value:"grid",className:"flex-1 overflow-auto",children:e.jsx("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2 p-1 min-w-max",children:h.length>0?e.jsxs(e.Fragment,{children:[h.map((D,N)=>e.jsxs("div",{className:"relative aspect-square rounded-md overflow-hidden border border-border group h-20 w-20",children:[e.jsx("img",{src:D,alt:`Image ${N+1}`,className:"h-full w-full object-cover",onError:A=>{A.currentTarget.src="/placeholder.svg"},onClick:()=>{b(N),document.querySelector('[data-value="gallery"]')?.click()}}),i&&!n&&e.jsx(Z,{variant:"destructive",size:"icon",className:"absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity",onClick:A=>{A.stopPropagation(),K(N)},children:e.jsx(Ms,{className:"h-3 w-3"})})]},N)),i&&!n&&e.jsx("div",{className:"aspect-square rounded-md border border-dashed border-muted-foreground/30 flex items-center justify-center cursor-pointer hover:bg-muted/50",onClick:()=>F.current?.click(),children:e.jsx(Xs,{className:"h-8 w-8 text-muted-foreground"})})]}):e.jsxs("div",{className:"col-span-full flex flex-col items-center justify-center p-8 gap-4",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(Ct,{className:"w-16 h-16"}),e.jsx("span",{className:"text-sm mt-2",children:"No images available"})]}),i&&!n&&e.jsxs(Z,{variant:"outline",onClick:()=>F.current?.click(),disabled:n,children:[e.jsx(mt,{className:"mr-2 h-4 w-4"}),"Upload Images"]}),n&&e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(ms,{className:"h-8 w-8 animate-spin text-primary"}),e.jsx("span",{className:"text-sm mt-2",children:"Processing images..."})]})]})})})]}),i&&e.jsxs(et,{className:"flex sm:justify-between mt-4",children:[e.jsx("div",{className:"flex gap-2",children:h.length>0&&!n&&e.jsxs(e.Fragment,{children:[e.jsxs(Z,{variant:"outline",size:"sm",onClick:()=>F.current?.click(),disabled:n,children:[e.jsx(mt,{className:"mr-2 h-4 w-4"}),"Add More"]}),e.jsxs(Z,{variant:"outline",size:"sm",onClick:()=>P(!0),disabled:n||j>=h.length,children:[e.jsx(xt,{className:"mr-2 h-4 w-4"}),"AI Enhance"]})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Z,{variant:"secondary",size:"sm",onClick:()=>me(!1),disabled:n,children:"Cancel"}),e.jsxs(Z,{variant:"default",size:"sm",onClick:re,disabled:n,children:[e.jsx($t,{className:"mr-2 h-4 w-4"}),"Save"]})]})]})]})}),[x,h,n,i,j,me,K,re,F,_]),O=p.useCallback(D=>{D.stopPropagation(),D.preventDefault(),!x&&!E.current&&(E.current=!0,setTimeout(()=>{m(!0),E.current=!1},50))},[x]);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:Tt("relative h-12 rounded-md overflow-hidden border border-border flex items-center group",X.length===0&&"bg-muted justify-center w-12",X.length>0&&"p-1 w-auto min-w-[100px] max-w-[200px]","cursor-pointer hover:bg-accent/50 transition-colors"),style:{minWidth:X.length===0?"48px":"100px"},onClick:O,onMouseDown:D=>D.stopPropagation(),onMouseUp:D=>D.stopPropagation(),role:"button",tabIndex:0,"aria-label":X.length>0?"View or edit images":"Add images",children:n?e.jsxs("div",{className:"flex flex-col items-center justify-center w-full text-muted-foreground",children:[e.jsx(ms,{className:"h-5 w-5 animate-spin text-primary"}),e.jsx("span",{className:"text-[10px] mt-1",children:"Processing..."})]}):X.length>0?e.jsx(_t,{className:"w-full h-full",orientation:"horizontal",children:e.jsx("div",{className:"flex items-center h-full space-x-1 p-0.5",children:X.map((D,N)=>e.jsx("div",{className:"relative h-8 w-8 flex-shrink-0 rounded-sm overflow-hidden flex items-center justify-center",children:e.jsx("img",{src:D,alt:`Product ${N+1}`,className:"h-full w-full object-cover",onError:A=>{A.currentTarget.src="/placeholder.svg"}})},N))})}):e.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[e.jsx(Ct,{className:"w-5 h-5"}),e.jsx("span",{className:"text-[10px] mt-0.5",children:"Add Images"})]})}),i&&e.jsx("input",{ref:F,type:"file",accept:"image/*",multiple:!0,className:"hidden",onChange:te,"aria-hidden":"true"}),e.jsx(R,{}),B&&h.length>0&&j<h.length&&e.jsx(Er,{imageUrl:h[j],onEnhancedImage:D=>{const N=[...h];N[j]=D,U(N)},isOpen:B,onClose:()=>P(!1)})]})}),Ra=({primaryImage:s,additionalImages:a=[],description:t,onAutoFill:i,disabled:c=!1})=>{const[n,d]=p.useState(!1),[x,m]=p.useState(!1),[j,b]=p.useState(0),[h,U]=p.useState(null),V=!!s||a.length>0,T=!!t&&t.trim().length>0,B=async()=>{if(!V&&!T){U("Please upload at least one image or add a product description before using Magic AI");return}m(!0),b(0),U(null);try{const P=setInterval(()=>{b(E=>{const X=E+5;return X>=95?95:X})},150);await new Promise(E=>setTimeout(E,3e3));const F={name:T?"AI Generated Product Name":"Premium All-Season Tire",manufacturer:"Continental",width:205,aspectRatio:55,rimDiameter:16,loadIndex:91,speedRating:"V",season:"All-Season",treadLife:"60,000 miles",tractionRating:"A",temperatureRating:"A",vehicleTypeCompatibility:["Sedan","Compact SUV"],certifications:["DOT","ECE"]};clearInterval(P),b(100),i(F),ne.success("Product information auto-filled successfully"),setTimeout(()=>{d(!1),m(!1)},500)}catch(P){console.error("Error using Magic AI:",P),U("Failed to analyze product. Please try again."),m(!1)}};return e.jsxs(e.Fragment,{children:[e.jsxs(Z,{variant:"outline",size:"sm",className:"gap-1",onClick:()=>d(!0),disabled:c||!V&&!T,children:[e.jsx(kt,{className:"h-4 w-4 text-amber-500"}),"Magic AI"]}),e.jsx(Ks,{open:n,onOpenChange:d,children:e.jsxs(Gs,{className:"sm:max-w-md",children:[e.jsx(Js,{children:e.jsxs(Zs,{className:"flex items-center gap-2",children:[e.jsx(kt,{className:"h-5 w-5 text-amber-500"}),"Magic AI"]})}),e.jsxs("div",{className:"space-y-4 py-4",children:[h&&e.jsxs(Ot,{variant:"destructive",children:[e.jsx(hr,{className:"h-4 w-4"}),e.jsx(Lt,{children:h})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm",children:"Magic AI will analyze your product images and description to automatically fill in product details."}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsx("p",{className:"font-medium",children:"Available data for analysis:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[e.jsx("li",{className:V?"text-green-600 dark:text-green-400":"text-muted-foreground",children:V?`${a.length+(s?1:0)} images available`:"No images available"}),e.jsx("li",{className:T?"text-green-600 dark:text-green-400":"text-muted-foreground",children:T?"Product description available":"No product description available"})]})]}),x&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(xr,{value:j,className:"h-2"}),e.jsxs("p",{className:"text-xs text-center text-muted-foreground",children:["Analyzing product... ",j,"%"]})]}),e.jsxs("div",{className:"rounded-md bg-muted/50 p-3 text-sm",children:[e.jsx("p",{className:"font-medium mb-2",children:"Magic AI will attempt to identify:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 pl-2 text-muted-foreground",children:[e.jsx("li",{children:"Product name and manufacturer"}),e.jsx("li",{children:"Technical specifications (size, dimensions, etc.)"}),e.jsx("li",{children:"Performance ratings and certifications"}),e.jsx("li",{children:"Compatible vehicle types"}),e.jsx("li",{children:"Other relevant product details"})]})]})]})]}),e.jsxs(et,{children:[e.jsx(Z,{variant:"outline",onClick:()=>d(!1),disabled:x,children:"Cancel"}),e.jsx(Z,{onClick:B,disabled:x||!V&&!T,children:x?e.jsxs(e.Fragment,{children:[e.jsx(ms,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(kt,{className:"mr-2 h-4 w-4"}),"Run Magic AI"]})})]})]})})]})},Oa=({value:s=[],onChange:a,isOpen:t,onClose:i})=>{const[c,n]=p.useState(s),[d,x]=p.useState("car"),[m,j]=p.useState({brand:"",model:"",engineType:""}),[b,h]=p.useState(!1),[U,V]=p.useState([]),[T,B]=p.useState([]),[P,F]=p.useState([]),[E,X]=p.useState("");p.useEffect(()=>{(async()=>{h(!0);try{if(d==="car"){const C=await Fr();!C||C.length===0?(console.error("No brands fetched from the database"),V([])):(console.log(`Successfully loaded ${C.length} vehicle brands`),V(C))}else V([])}catch(C){console.error("Error fetching brands:",C),V([])}finally{h(!1)}})(),j({brand:"",model:"",engineType:""})},[d]),p.useEffect(()=>{(async()=>{if(!m.brand){B([]);return}h(!0);try{if(d==="car"){const C=await Tr(m.brand);!C||C.length===0?(console.warn(`No models found for brand: ${m.brand}`),B([])):(console.log(`Successfully loaded ${C.length} models for brand: ${m.brand}`),B(C))}else B([])}catch(C){console.error("Error fetching models:",C),B([])}finally{h(!1)}})()},[d,m.brand]),p.useEffect(()=>{(async()=>{if(!m.brand||!m.model){F([]);return}h(!0);try{if(d==="car"){const C=m.model,{model:M,generationCode:H}=Xt(C);console.log(`Fetching engines for ${m.brand} ${M} ${H||""}`);const k=await _r(m.brand,M,H);!k||k.length===0?(console.warn(`No engines found for ${m.brand} ${M} ${H||""}`),F([])):(console.log(`Successfully loaded ${k.length} engines for ${m.brand} ${M} ${H||""}`),F(k))}else F([])}catch(C){console.error("Error fetching engines:",C),F([])}finally{h(!1)}})()},[d,m.brand,m.model]);const te=(u,C)=>{j(u==="brand"?{brand:C,model:"",engineType:""}:u==="model"?{...m,model:C,engineType:""}:{...m,[u]:C})},K=u=>{u!==d&&(j({brand:"",model:"",engineType:""}),x(u))},re=()=>{j({brand:"",model:"",engineType:""})},me=async()=>{if(m.brand){h(!0);try{if(m.brand&&m.model&&m.engineType){console.log(`Adding vehicle: ${m.brand} ${m.model} ${m.engineType}`);const u=await Vr(m.brand,m.model,m.engineType);if(u)if(c.some(M=>M.id===u.id))console.log(`Vehicle already selected: ${u.displayName}`);else{const M={...u,displayName:`${u.brand} ${u.model} ${u.generation||""} ${u.engineDetails?.fullName||""}`};console.log(`Adding new vehicle: ${M.displayName}`),n(H=>[...H,M])}else console.error(`Failed to get vehicle data for ${m.brand} ${m.model} ${m.engineType}`)}else if(m.brand&&m.model){const{model:u,generationCode:C}=Xt(m.model);if(T.find(H=>H.uniqueId===m.model)){const H=`${m.brand}-${u}-${C}`.replace(/\s+/g,"-").toLowerCase();if(c.some(fe=>fe.id===H))console.log(`Vehicle already selected: ${m.brand} ${u} ${C||""}`);else{const fe={id:H,type:d,brand:m.brand,model:u,generation:C,displayName:`${m.brand} ${u} ${C||""}`};console.log(`Adding new vehicle: ${fe.displayName}`),n(se=>[...se,fe])}}else console.error(`Model option not found for ${m.model}`)}else if(m.brand){const u=`${m.brand}`.replace(/\s+/g,"-").toLowerCase();if(c.some(M=>M.id===u))console.log(`Brand already selected: ${m.brand}`);else{const M={id:u,type:d,brand:m.brand,model:"",displayName:m.brand};console.log(`Adding new brand: ${M.displayName}`),n(H=>[...H,M])}}}catch(u){console.error("Error adding vehicle:",u)}finally{h(!1)}}},_=u=>{n(C=>C.filter(M=>M.id!==u))},R=()=>{a(c),i()},O=()=>{n(s),i()},D=c.filter(u=>u.displayName.toLowerCase().includes(E.toLowerCase())),N=u=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex-shrink-0 w-7 h-7 rounded-md bg-gray-100 flex items-center justify-center",children:u.image_url?e.jsx("img",{src:u.image_url,alt:u.label,className:"w-5 h-5 object-contain"}):e.jsx("span",{className:"text-sm font-medium text-gray-500",children:u.label.charAt(0)})}),e.jsx("span",{className:"font-medium",children:u.label})]}),A=u=>e.jsxs("div",{className:"flex flex-col py-0.5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium",children:u.model}),e.jsx("span",{className:"text-xs bg-gray-100 px-1 rounded",children:u.generation}),e.jsx("span",{className:"text-xs text-blue-600",children:u.bodyType})]}),e.jsx("span",{className:"text-xs text-muted-foreground",children:u.dateRange})]}),z=u=>{const C=u.label.match(/^(.*?)\s*\(/),M=C?C[1].trim():"",H=u.label.match(/\(([^)]+)\)/),k=H?H[1]:"",fe=u.label.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/),se=fe?`${fe[1]} kW / ${fe[2]} HP`:"",ee=u.label.match(/\(([^)]+)\)$/),we=ee?ee[1]:"";return e.jsxs("div",{className:"flex flex-col py-0.5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium text-sm",children:M}),e.jsx("span",{className:"text-xs bg-gray-100 px-1 rounded",children:k})]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[e.jsx("span",{className:"text-blue-600",children:se}),e.jsx("span",{className:"text-gray-500",children:we})]})]})},W=u=>u.model||"Other",Q=u=>u.fuel_type||"Other";return e.jsx(Ks,{open:t,onOpenChange:i,children:e.jsxs(Gs,{className:"sm:max-w-[800px] max-h-[90vh] flex flex-col",children:[e.jsxs(Js,{children:[e.jsx(Zs,{className:"text-2xl font-bold",children:"Vehicle Compatibility"}),e.jsx("p",{className:"text-muted-foreground mt-1",children:"Select compatible vehicles from the database"})]}),e.jsxs("div",{className:"flex-1 overflow-hidden flex flex-col gap-4",children:[e.jsxs("div",{className:"border-b pb-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Vehicle Type"}),e.jsx(Z,{variant:"ghost",size:"sm",onClick:re,className:"h-8 text-xs",children:"Clear Filters"})]}),e.jsx(Xr,{selectedType:d,onTypeChange:K})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"brand-select",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-orange-500 text-white mr-2",children:"1"}),e.jsx("span",{children:"Select car brand"})]})}),e.jsx(wt,{options:U.map(u=>({value:u.name,label:u.name,image_url:u.image_url})),value:m.brand,onChange:u=>te("brand",u),placeholder:"Search brands...",searchPlaceholder:"Search for a brand...",renderOption:N,emptyMessage:"No brands found",maxHeight:"200px",className:"z-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"model-select",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2",children:"2"}),e.jsx("span",{children:"Select Model"})]})}),e.jsx(wt,{options:T.map(u=>({value:u.uniqueId,label:u.fullName||`${u.model} (${u.generation})`,model:u.model,generation:u.generation,bodyType:u.bodyType,dateRange:u.dateRange})),value:m.model,onChange:u=>te("model",u),placeholder:"Search models...",searchPlaceholder:"Search for a model...",renderOption:A,groupBy:W,emptyMessage:"No models found",disabled:!m.brand,maxHeight:"200px",className:"z-40"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(J,{htmlFor:"engine-select",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2",children:"3"}),e.jsx("span",{children:"Select engine (type)"})]})}),e.jsxs(Z,{onClick:me,disabled:!m.brand||b,size:"sm",className:"h-8",children:[b?e.jsx(ms,{className:"h-4 w-4 mr-1 animate-spin"}):e.jsx(Xs,{className:"h-4 w-4 mr-1"}),"Add"]})]}),e.jsx(wt,{options:P.map(u=>({value:u.fullName,label:u.fullName,fullName:u.fullName,code:u.code,fuel_type:u.fuel_type,power_kW:u.power_kW,power_HP:u.power_HP,productionYears:u.productionYears})),value:m.engineType,onChange:u=>te("engineType",u),placeholder:"Search engine types...",searchPlaceholder:"Search for an engine...",renderOption:z,groupBy:Q,emptyMessage:"No engines found",disabled:!m.model,maxHeight:"200px",className:"z-30"})]})]}),e.jsxs("div",{className:"space-y-2 mt-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs(J,{className:"text-base font-medium",children:["Selected Vehicles (",c.length,")"]}),e.jsxs("div",{className:"relative",children:[e.jsx(fr,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(ye,{placeholder:"Search selected vehicles...",className:"pl-8 h-9 rounded-full",value:E,onChange:u=>X(u.target.value)})]})]}),e.jsx("div",{className:"h-[200px] border rounded-xl p-3",children:e.jsx(_t,{className:"h-full w-full",children:D.length>0?e.jsx("div",{className:"flex flex-wrap gap-2 p-1",children:D.map(u=>e.jsx(gr,{variant:"secondary",className:"flex items-center gap-1 py-1.5 px-3 max-w-full rounded-full bg-blue-50 border-blue-200 text-blue-700",children:e.jsxs("div",{className:"flex items-center gap-1 overflow-hidden",children:[e.jsx(gt,{className:"h-3 w-3 mr-1 flex-shrink-0"}),e.jsx("span",{className:"whitespace-normal break-words text-xs font-medium",children:u.displayName}),e.jsx(Z,{variant:"ghost",size:"icon",className:"h-5 w-5 p-0 ml-1 flex-shrink-0 hover:bg-blue-100 rounded-full",onClick:()=>_(u.id),children:e.jsx(Ms,{className:"h-3 w-3"})})]})},u.id))}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-muted-foreground",children:[e.jsx(gt,{className:"h-10 w-10 mb-2 opacity-20"}),e.jsx("p",{className:"text-sm",children:E?"No matching vehicles found":"No vehicles selected"}),!E&&e.jsx("p",{className:"text-xs",children:"Use the filters above to select compatible vehicles"})]})})})]})]}),e.jsxs(et,{className:"flex justify-end gap-2 pt-4 border-t mt-4",children:[e.jsx(Z,{variant:"outline",onClick:O,className:"rounded-full px-6",children:"Cancel"}),e.jsx(Z,{onClick:R,className:"rounded-full px-6 bg-blue-600 hover:bg-blue-700",children:"Save"})]})]})})},La=({value:s=[],onChange:a,label:t="Vehicle Type Compatibility",description:i="Select compatible vehicles from the compatibility hub database."})=>{const[c,n]=p.useState(!1),[d,x]=p.useState(""),m=s.filter(j=>j.displayName.toLowerCase().includes(d.toLowerCase()));return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(J,{children:t}),e.jsxs(Z,{type:"button",variant:"outline",size:"sm",onClick:()=>n(!0),className:"h-8",children:[e.jsx(Xs,{className:"h-4 w-4 mr-1"}),"Add Vehicles"]})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:i}),e.jsxs("div",{className:"border rounded-md p-3 space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:[s.length," ",s.length===1?"vehicle":"vehicles"," selected"]}),s.length>0&&e.jsxs("div",{className:"relative w-[200px]",children:[e.jsx(fr,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(ye,{placeholder:"Search vehicles...",className:"pl-8 h-8",value:d,onChange:j=>x(j.target.value)})]})]}),s.length>0?e.jsx("div",{className:"h-[150px] overflow-y-auto",children:e.jsxs("div",{className:"flex flex-wrap gap-2 p-1",children:[m.map(j=>e.jsx(gr,{variant:"secondary",className:"flex items-center gap-1 py-1 px-2 max-w-full",children:e.jsxs("div",{className:"flex items-center gap-1 overflow-hidden",children:[e.jsx(gt,{className:"h-3 w-3 mr-1 flex-shrink-0"}),e.jsx("span",{className:"whitespace-normal break-words",children:j.displayName}),e.jsx(Z,{type:"button",variant:"ghost",size:"icon",className:"h-4 w-4 p-0 ml-1 flex-shrink-0",onClick:()=>{a(s.filter(b=>b.id!==j.id))},children:e.jsx(Ms,{className:"h-3 w-3"})})]})},j.id)),m.length===0&&d&&e.jsx("div",{className:"w-full text-center text-sm text-muted-foreground py-4",children:"No vehicles match your search"})]})}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-6 text-muted-foreground",children:[e.jsx(gt,{className:"h-10 w-10 mb-2 opacity-20"}),e.jsx("p",{className:"text-sm",children:"No vehicles selected"}),e.jsx("p",{className:"text-xs",children:'Click "Add Vehicles" to select compatible vehicles'})]})]}),e.jsx(Oa,{value:s,onChange:a,isOpen:c,onClose:()=>n(!1)})]})},Ha=({isOpen:s,onClose:a,onSave:t,product:i,categories:c})=>{const{userRole:n,isSupplier:d,isMerchant:x}=Dr(),[m,j]=p.useState(!1),[b,h]=p.useState("basic"),[U,V]=p.useState(i.primaryImage||""),[T,B]=p.useState(i.additionalImages||[]),[P,F]=p.useState(i.wholesalePricingTiers||[]),[E,X]=p.useState(Array.isArray(i.vehicleTypeCompatibility)?typeof i.vehicleTypeCompatibility[0]=="string"?i.vehicleTypeCompatibility.map(f=>({id:f.toLowerCase().replace(/\s+/g,"-"),type:"car",brand:f,model:"",displayName:f})):i.vehicleTypeCompatibility:[]),[te,K]=p.useState(i.availableShippingMethods||[]),[re,me]=p.useState(""),[_,R]=p.useState(null),[O,D]=p.useState(null),[N,A]=p.useState([]),[z,W]=p.useState(""),[Q,u]=p.useState([]),C={name:"basic",sku:"basic",partArticleNumber:"basic",category:"basic",subcategory:"basic",status:"basic",descriptionAndSpecifications:"basic",manufacturer:"details",supplierName:"details",productionCapacity:"details",width:"tyre-attributes",aspectRatio:"tyre-attributes",rimDiameter:"tyre-attributes",loadIndex:"tyre-attributes",speedRating:"tyre-attributes",season:"tyre-attributes",stockQuantity:"inventory",minimumOrderQuantity:"inventory",retailPrice:"inventory",quotationRequestEnabled:"inventory",shippingOrigin:"shipping",primaryImage:"images",additionalImages:"images"},{register:M,handleSubmit:H,formState:{errors:k},reset:fe,setValue:se,watch:ee,unregister:we}=xa({defaultValues:{...i}});p.useEffect(()=>{if(fe(i),V(i.primaryImage||""),B(i.additionalImages||[]),F(i.wholesalePricingTiers||[]),W(i.primaryImage||""),u(i.additionalImages||[]),Array.isArray(i.vehicleTypeCompatibility))if(typeof i.vehicleTypeCompatibility[0]=="string"){const f=i.vehicleTypeCompatibility.map(r=>({id:r.toLowerCase().replace(/\s+/g,"-"),type:"car",brand:r,model:"",displayName:r}));X(f)}else X(i.vehicleTypeCompatibility);else X([]);K(i.availableShippingMethods||[])},[i,fe]);const ce=ee("category"),oe=ee("subcategory");p.useEffect(()=>{ce!==i.category&&(console.log(`Category changed from ${i.category} to ${ce}, resetting subcategory`),se("subcategory",""))},[ce,i.category,se]),p.useEffect(()=>{R(null)},[b]),p.useEffect(()=>{oe!=="Tyres"?(b==="tyre-attributes"&&h("details"),se("width",void 0),se("aspectRatio",void 0),se("rimDiameter",void 0),se("loadIndex",void 0),se("speedRating",""),se("season",void 0),we(["width","aspectRatio","rimDiameter","loadIndex","speedRating","season"]),console.log("Unregistered tyre-specific fields")):console.log("Subcategory is Tyres, fields will be registered with validation"),ce==="tyres"&&oe==="Tyres"&&X([])},[oe,ce,b,se,we]);const Pe=f=>{const r=Object.keys(f);if(r.length===0)return null;for(const l of r)if(l in C)return C[l];return null},Ce=async f=>{if(R(null),console.log("Form submission data:",f),Object.keys(k).length>0){const r=Pe(k);if(r&&r!==b){h(r),R("Please fill in all required fields"),ne.error("Please fill in all required fields",{description:`Missing required information in the ${r.charAt(0).toUpperCase()+r.slice(1)} tab.`,duration:5e3});return}}if(!U){h("images"),R("Please upload a primary image for the product"),ne.error("Image required",{description:"Please upload a primary image for the product.",duration:5e3});return}if(!f.subcategory){h("basic"),R("Please select a subcategory"),ne.error("Subcategory required",{description:"Please select a subcategory for the product.",duration:5e3});return}if(console.log("Form data before processing:",f),f.subcategory!=="Tyres")f.width=void 0,f.aspectRatio=void 0,f.rimDiameter=void 0,f.loadIndex=void 0,f.speedRating="",f.season=void 0,console.log("Cleared tyre-specific fields for non-Tyres subcategory");else if(f.subcategory==="Tyres"){console.log("Validating tyre-specific fields for Tyres subcategory");const r=[];if(f.width||r.push("Width"),f.aspectRatio||r.push("Aspect Ratio"),f.rimDiameter||r.push("Rim Diameter"),f.loadIndex||r.push("Load Index"),f.speedRating||r.push("Speed Rating"),r.length>0){h("tyre-attributes"),R(`Please fill in all required tyre specification fields: ${r.join(", ")}`),ne.error("Tyre specifications required",{description:`Missing required tyre specifications: ${r.join(", ")}`,duration:5e3}),console.log("Tyre fields validation failed:",r);return}}console.log("Form data after processing:",f),j(!0);try{let r=U,l=T;const o=[...O?[O]:[],...N];if(o.length>0){ne.info("Uploading images to cloud storage...");try{const y=[];if(O&&z&&z!==U&&y.push(z),N.length>0){const I=T.filter(G=>!G.startsWith("data:")),q=new Set(I);Q.forEach(G=>{G&&!q.has(G)&&y.push(G)})}y.length>0&&(console.log("🗑️ Deleting old images:",y),await ge(y),ne.info("Cleaned up old images..."));const v=await Hr(i.id,o);if(v.primaryImage&&(r=v.primaryImage),v.additionalImages.length>0){const I=T.filter(ae=>!ae.startsWith("data:")),q=N.length;l=[...I.slice(0,I.length-q),...v.additionalImages]}ne.success("Images uploaded successfully")}catch(y){console.error("Error uploading images:",y),ne.error("Failed to upload images. Product will be saved with temporary images.")}}const w={...f,primaryImage:r,additionalImages:l,vehicleTypeCompatibility:ce==="tyres"&&oe==="Tyres"?void 0:E,availableShippingMethods:te,wholesalePricingTiers:P,updatedAt:new Date};await t(w),ne.success("Product updated successfully"),D(null),A([]),W(""),u([]),a()}catch(r){console.error("Error updating product:",r),ne.error("Failed to update product")}finally{j(!1)}},ge=async f=>{const r=f.filter(l=>l&&l.trim()!=="").map(async l=>{try{await Yr(l),console.log("✅ Deleted old image:",l)}catch(o){console.error("❌ Failed to delete old image:",l,o)}});await Promise.allSettled(r)},Ue=()=>{m||(fe(),D(null),A([]),W(""),u([]),a())},ze=()=>{re.trim()&&!te.includes(re.trim())&&(K([...te,re.trim()]),me(""))},Ve=f=>{K(te.filter(r=>r!==f))},Be=()=>{F([...P,{minQuantity:1,price:0}])},le=(f,r,l)=>{const o=[...P];o[f]={...o[f],[r]:l},F(o)},ws=f=>{F(P.filter((r,l)=>l!==f))};return e.jsx(Ks,{open:s,onOpenChange:f=>!f&&Ue(),children:e.jsxs(Gs,{className:"sm:max-w-3xl max-h-[90vh] overflow-y-auto",children:[e.jsx(Js,{children:e.jsx(Zs,{className:"text-xl",children:"Edit Product"})}),e.jsxs("form",{onSubmit:H(Ce),className:"space-y-6 py-4",children:[_&&e.jsxs(Ot,{variant:"destructive",className:"mb-4",children:[e.jsx(hr,{className:"h-4 w-4"}),e.jsx(Lt,{children:_})]}),e.jsxs(ur,{value:b,onValueChange:h,className:"w-full",children:[e.jsxs(mr,{className:"mb-6 bg-muted",children:[e.jsxs(ds,{value:"basic",className:"data-[state=active]:bg-background",children:["Basic Information",Object.keys(k).some(f=>C[f]==="basic")&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),e.jsxs(ds,{value:"images",className:"data-[state=active]:bg-background",children:["Images",!U&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),e.jsxs(ds,{value:"details",className:"data-[state=active]:bg-background",children:["Product Details",Object.keys(k).some(f=>C[f]==="details")&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),ee("subcategory")==="Tyres"&&e.jsxs(ds,{value:"tyre-attributes",className:"data-[state=active]:bg-background",children:["Tyre Attributes",Object.keys(k).some(f=>C[f]==="tyre-attributes")&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),e.jsxs(ds,{value:"inventory",className:"data-[state=active]:bg-background",children:["Inventory",Object.keys(k).some(f=>C[f]==="inventory")&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),e.jsxs(ds,{value:"shipping",className:"data-[state=active]:bg-background",children:["Shipping",Object.keys(k).some(f=>C[f]==="shipping")&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),e.jsx(ds,{value:"pricing",className:"data-[state=active]:bg-background",children:"Pricing"})]}),e.jsxs(us,{value:"basic",className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"id",children:"Product ID"}),e.jsx(ye,{id:"id",...M("id"),readOnly:!0,className:"bg-muted"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"name",children:["Product Name ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"name",...M("name",{required:"Product name is required"}),placeholder:"Enter product name",className:k.name?"border-destructive":""}),k.name&&e.jsx("p",{className:"text-sm text-destructive",children:k.name.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"sku",children:"SKU"}),e.jsx(ye,{id:"sku",...M("sku"),placeholder:"Enter SKU (optional)",className:k.sku?"border-destructive":""}),k.sku&&e.jsx("p",{className:"text-sm text-destructive",children:k.sku.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"partArticleNumber",children:["Part Article Number ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"partArticleNumber",...M("partArticleNumber",{required:"Part Article Number is required"}),placeholder:"Enter part article number",className:k.partArticleNumber?"border-destructive":""}),k.partArticleNumber&&e.jsx("p",{className:"text-sm text-destructive",children:k.partArticleNumber.message})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"category",children:["Category ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(Is,{value:ee("category"),onValueChange:f=>{console.log(`Category changed to: ${f}`),se("category",f)},children:[e.jsx(Ps,{children:e.jsx(Fs,{placeholder:"Select category"})}),e.jsx(Ts,{children:Mr.filter(f=>f.id!=="all").map(f=>e.jsx(he,{value:f.id,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:Rr(f.id),alt:f.displayName,className:"w-4 h-4 object-contain",onError:r=>{const l=r.target;l.style.display="none"}}),f.name]})},f.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"subcategory",children:["Subcategory ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(Is,{value:ee("subcategory")||"",onValueChange:f=>se("subcategory",f),...M("subcategory",{required:"Subcategory is required"}),children:[e.jsx(Ps,{className:k.subcategory?"border-destructive":"",children:e.jsx(Fs,{placeholder:"Select subcategory"})}),e.jsx(Ts,{children:Or(ce).map(f=>e.jsx(he,{value:f.name,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:Lr(f.id),alt:f.displayName,className:"w-4 h-4 object-contain",onError:r=>{const l=r.target;l.style.display="none"}}),f.displayName]})},f.id))})]}),k.subcategory&&e.jsx("p",{className:"text-sm text-destructive",children:k.subcategory.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"status",children:["Status ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(Is,{value:ee("status"),onValueChange:f=>se("status",f),children:[e.jsx(Ps,{children:e.jsx(Fs,{placeholder:"Select status"})}),e.jsxs(Ts,{children:[e.jsx(he,{value:"active",children:"Active"}),e.jsx(he,{value:"draft",children:"Draft"}),e.jsx(he,{value:"pending_approval",children:"Pending Approval"}),e.jsx(he,{value:"out_of_stock",children:"Out of Stock"}),e.jsx(he,{value:"discontinued",children:"Discontinued"})]})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"descriptionAndSpecifications",children:"Description and Specifications"}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"bg-orange-50 text-orange-700 px-2 py-1 rounded-md text-xs font-medium mr-2",children:"Dual-Section Format"}),e.jsx("span",{className:"text-xs text-muted-foreground",children:"Use structured format with separate description and specifications sections"}),e.jsx(Z,{variant:"ghost",size:"sm",className:"h-6 px-2 ml-auto text-xs",onClick:()=>{const f=ce==="tyres"?`Part 1 - Product Description:
High-performance ${oe||"tyre"} designed for optimal performance and safety. Features advanced compound technology for superior grip and durability. Suitable for various driving conditions and vehicle types.

Part 2 - Specifications:
- Width: [Enter width in mm]
- Aspect Ratio: [Enter aspect ratio]
- Rim Diameter: [Enter rim diameter in inches]
- Load Index: [Enter load index]
- Speed Rating: [Enter speed rating]
- Season: [Summer/Winter/All-Season]
- Tread Pattern: [Enter tread pattern]
- Compound Type: [Enter compound type]
- DOT Compliance: Yes
- ECE Marking: [Enter ECE marking]`:ce==="brakes"?`Part 1 - Product Description:
High-quality ${oe||"brake component"} engineered for reliable braking performance and safety. Manufactured to OEM standards with premium materials for extended service life and consistent performance.

Part 2 - Specifications:
- Part Number: [Enter part number]
- Brand: [Enter brand]
- Component Type: [Enter component type]
- Material: [Enter material composition]
- Dimensions: [Enter dimensions]
- Weight: [Enter weight]
- Compatibility: [Enter vehicle compatibility]
- OE Reference Numbers: [Enter OE numbers]
- Certification: [Enter certifications]
- Installation Notes: [Enter installation requirements]`:`Part 1 - Product Description:
High-quality automotive part designed for reliable performance and durability. Manufactured to meet or exceed OEM specifications with premium materials and advanced engineering.

Part 2 - Specifications:
- Part Number: [Enter part number]
- Brand: [Enter brand]
- Material: [Enter material]
- Dimensions: [Enter dimensions]
- Weight: [Enter weight]
- Compatibility: [Enter compatibility]
- Certification: [Enter certifications]
- Installation: [Enter installation notes]
- Warranty: [Enter warranty information]`;se("descriptionAndSpecifications",f)},children:"Insert Template"})]}),e.jsx($r,{id:"descriptionAndSpecifications",...M("descriptionAndSpecifications"),placeholder:"Enter product description and specifications using the dual-section format above",className:"min-h-[150px]"})]}),(ce==="brakes"||ce==="tyres"&&oe!=="Tyres")&&e.jsx("div",{className:"mt-6",children:e.jsx(La,{value:E,onChange:X,label:"Vehicle Type Compatibility",description:"Select compatible vehicles from the compatibility hub database."})})]}),e.jsx(us,{value:"images",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs(J,{children:["Primary Image ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx("div",{className:`border rounded-md p-4 bg-muted/20 ${U?"":"border-destructive"}`,children:e.jsx(Da,{value:U,onChange:V,onTemporaryImageChange:D,isEditable:!0,productId:i.id})}),!U&&b==="images"&&e.jsx("p",{className:"text-sm text-destructive",children:"Primary image is required"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(J,{children:"Additional Images"}),e.jsx("div",{className:"border rounded-md p-4 bg-muted/20 overflow-hidden",style:{minHeight:"60px"},children:e.jsx(Ma,{value:T,onChange:B,onTemporaryImagesChange:A,isEditable:!0,productId:i.id})})]})]})}),e.jsx(us,{value:"details",className:"space-y-6",children:e.jsx("div",{className:"grid grid-cols-2 gap-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"manufacturer",children:["Manufacturer ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"manufacturer",...M("manufacturer",{required:"Manufacturer is required"}),placeholder:"Enter manufacturer",className:k.manufacturer?"border-destructive":""}),k.manufacturer&&e.jsx("p",{className:"text-sm text-destructive",children:k.manufacturer.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"supplierName",children:"Supplier Name"}),e.jsx(ye,{id:"supplierName",...M("supplierName"),placeholder:"Enter supplier name"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"productionCapacity",children:"Production Capacity (units/month)"}),e.jsx(ye,{id:"productionCapacity",type:"number",...M("productionCapacity",{valueAsNumber:!0}),placeholder:"Enter production capacity"})]})]})})}),ee("subcategory")==="Tyres"&&e.jsx(us,{value:"tyre-attributes",className:"space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-muted/30 p-4 rounded-md mb-4",children:[e.jsx("h3",{className:"text-md font-medium mb-2",children:"Tyre Specifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"These fields are critical for the tyre search engine. Please enter the exact values without any additional symbols or text."})]}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsxs("div",{className:"relative rounded-lg shadow-md border border-muted",style:{maxWidth:"500px"},children:[e.jsx("div",{className:"p-2",children:e.jsx("img",{src:"/images/TyreParameters.png",alt:"Tyre Parameters Diagram",className:"w-full h-auto object-contain",style:{maxHeight:"250px"},draggable:"false"})}),e.jsx("div",{className:"bg-background/80 backdrop-blur-sm p-2 text-center border-t border-muted",children:e.jsx("p",{className:"text-sm font-medium",children:"Tyre Parameters Reference"})})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"width",children:["Width ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"width",type:"number",...M("width",{required:"Width is required",valueAsNumber:!0,min:{value:1,message:"Width must be a positive number"},disabled:oe!=="Tyres"}),placeholder:"e.g., 205",className:k.width?"border-destructive":"",disabled:oe!=="Tyres"}),k.width&&e.jsx("p",{className:"text-sm text-destructive",children:k.width.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"aspectRatio",children:["Aspect Ratio / Height ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"aspectRatio",type:"number",...M("aspectRatio",{required:"Aspect ratio is required",valueAsNumber:!0,min:{value:1,message:"Aspect ratio must be a positive number"},disabled:oe!=="Tyres"}),placeholder:"e.g., 60",className:k.aspectRatio?"border-destructive":"",disabled:oe!=="Tyres"}),k.aspectRatio&&e.jsx("p",{className:"text-sm text-destructive",children:k.aspectRatio.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"rimDiameter",children:["Diameter / Rim Size ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"rimDiameter",type:"number",...M("rimDiameter",{required:"Rim diameter is required",valueAsNumber:!0,min:{value:1,message:"Rim diameter must be a positive number"},disabled:oe!=="Tyres"}),placeholder:"e.g., 16",className:k.rimDiameter?"border-destructive":"",disabled:oe!=="Tyres"}),k.rimDiameter&&e.jsx("p",{className:"text-sm text-destructive",children:k.rimDiameter.message})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"loadIndex",children:["Load Index ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"loadIndex",type:"number",...M("loadIndex",{required:"Load index is required",valueAsNumber:!0,min:{value:1,message:"Load index must be a positive number"},disabled:oe!=="Tyres"}),placeholder:"e.g., 97",className:k.loadIndex?"border-destructive":"",disabled:oe!=="Tyres"}),k.loadIndex&&e.jsx("p",{className:"text-sm text-destructive",children:k.loadIndex.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"speedRating",children:["Speed Rating ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(Is,{onValueChange:f=>se("speedRating",f),defaultValue:ee("speedRating")||"",...M("speedRating",{required:"Speed rating is required",disabled:oe!=="Tyres"}),disabled:oe!=="Tyres",children:[e.jsx(Ps,{className:k.speedRating?"border-destructive":"",children:e.jsx(Fs,{placeholder:"Select speed rating"})}),e.jsxs(Ts,{children:[e.jsx(he,{value:"Q",children:"Q"}),e.jsx(he,{value:"R",children:"R"}),e.jsx(he,{value:"S",children:"S"}),e.jsx(he,{value:"T",children:"T"}),e.jsx(he,{value:"U",children:"U"}),e.jsx(he,{value:"H",children:"H"}),e.jsx(he,{value:"V",children:"V"}),e.jsx(he,{value:"W",children:"W"}),e.jsx(he,{value:"Y",children:"Y"})]})]}),k.speedRating&&e.jsx("p",{className:"text-sm text-destructive",children:k.speedRating.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"season",children:"Season"}),e.jsxs(Is,{onValueChange:f=>se("season",f),defaultValue:ee("season")||"",disabled:oe!=="Tyres",children:[e.jsx(Ps,{children:e.jsx(Fs,{placeholder:"Select season"})}),e.jsxs(Ts,{children:[e.jsx(he,{value:"Summer",children:"Summer"}),e.jsx(he,{value:"Winter",children:"Winter"}),e.jsx(he,{value:"All-Season",children:"All-Season"})]})]})]})]})]})]})}),e.jsx(us,{value:"inventory",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"stockQuantity",children:["Stock Quantity ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(ye,{id:"stockQuantity",type:"number",...M("stockQuantity",{required:"Stock quantity is required",valueAsNumber:!0,min:{value:0,message:"Stock quantity cannot be negative"}}),placeholder:"Enter stock quantity",className:k.stockQuantity?"border-destructive":""}),k.stockQuantity&&e.jsx("p",{className:"text-sm text-destructive",children:k.stockQuantity.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"minimumOrderQuantity",children:"Minimum Order Quantity"}),e.jsx(ye,{id:"minimumOrderQuantity",type:"number",...M("minimumOrderQuantity",{valueAsNumber:!0}),placeholder:"Enter minimum order quantity"})]})]}),e.jsxs("div",{className:"space-y-3",children:[!d()&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"retailPrice",children:"Retail Price (DZD)"}),e.jsx(ye,{id:"retailPrice",type:"number",step:"0.01",...M("retailPrice",{valueAsNumber:!0}),placeholder:"Enter retail price in DZD"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"quotationRequestEnabled",children:"Quotation Request"}),e.jsxs(Is,{defaultValue:i.quotationRequestEnabled?"true":"false",onValueChange:f=>se("quotationRequestEnabled",f==="true"),children:[e.jsx(Ps,{children:e.jsx(Fs,{placeholder:"Select option"})}),e.jsxs(Ts,{children:[e.jsx(he,{value:"true",children:"Enabled"}),e.jsx(he,{value:"false",children:"Disabled"})]})]})]})]})]})}),e.jsx(us,{value:"shipping",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(J,{htmlFor:"shippingOrigin",children:["Shipping Origin ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(Is,{onValueChange:f=>se("shippingOrigin",f),defaultValue:ee("shippingOrigin")||"",...M("shippingOrigin",{required:"Shipping origin is required"}),children:[e.jsx(Ps,{className:k.shippingOrigin?"border-destructive":"",children:e.jsx(Fs,{placeholder:"Select shipping origin",children:ee("shippingOrigin")?qr(ee("shippingOrigin")):"Select shipping origin"})}),e.jsx(Ts,{children:Wr.map(f=>e.jsxs(he,{value:f.code,children:[f.code," - ",f.name]},f.code))})]}),k.shippingOrigin&&e.jsx("p",{className:"text-sm text-destructive",children:k.shippingOrigin.message})]})}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{children:"Available Shipping Methods"}),e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ye,{value:re,onChange:f=>me(f.target.value),placeholder:"Add shipping method",className:"flex-1"}),e.jsx(Z,{type:"button",size:"sm",onClick:ze,children:e.jsx(Xs,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:te.map((f,r)=>e.jsxs("div",{className:"flex items-center bg-muted rounded-md px-2 py-1",children:[e.jsx("span",{className:"text-sm",children:f}),e.jsx(Z,{type:"button",variant:"ghost",size:"sm",className:"h-5 w-5 p-0 ml-1",onClick:()=>Ve(f),children:e.jsx(Ms,{className:"h-3 w-3"})})]},r))})]})})]})}),e.jsx(us,{value:"pricing",className:"space-y-6",children:d()?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(J,{className:"text-lg font-medium",children:"Wholesale Pricing Tiers"}),e.jsxs(Z,{type:"button",variant:"outline",size:"sm",onClick:Be,children:[e.jsx(Xs,{className:"h-4 w-4 mr-2"})," Add Tier"]})]}),P.length===0?e.jsx("div",{className:"text-center py-8 text-muted-foreground",children:e.jsx("p",{children:"No pricing tiers defined. Add a tier to define wholesale pricing."})}):e.jsx("div",{className:"space-y-4",children:P.map((f,r)=>e.jsx(Ur,{className:"overflow-hidden",children:e.jsx(zr,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(J,{htmlFor:`tier-${r}-min`,className:"mb-2 block",children:"Min Quantity"}),e.jsx(ye,{id:`tier-${r}-min`,type:"number",value:f.minQuantity,onChange:l=>le(r,"minQuantity",parseInt(l.target.value)),min:"1"})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx(J,{htmlFor:`tier-${r}-max`,className:"mb-2 block",children:"Max Quantity (optional)"}),e.jsx(ye,{id:`tier-${r}-max`,type:"number",value:f.maxQuantity||"",onChange:l=>{const o=l.target.value?parseInt(l.target.value):void 0;le(r,"maxQuantity",o)},min:f.minQuantity+1,placeholder:"No limit"})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx(J,{htmlFor:`tier-${r}-price`,className:"mb-2 block",children:"Price per Unit"}),e.jsx(ye,{id:`tier-${r}-price`,type:"number",step:"0.01",value:f.price,onChange:l=>le(r,"price",parseFloat(l.target.value)),min:"0"})]}),e.jsx("div",{className:"flex items-end",children:e.jsx(Z,{type:"button",variant:"ghost",size:"icon",className:"text-destructive",onClick:()=>ws(r),children:e.jsx(Br,{className:"h-4 w-4"})})})]})})},r))})]}):e.jsx("div",{className:"text-center py-8 text-muted-foreground",children:e.jsx("p",{children:"Wholesale pricing tiers are only available for supplier accounts."})})})]}),e.jsxs(et,{className:"flex flex-col sm:flex-row gap-2",children:[e.jsx("div",{className:"flex-1 flex justify-start",children:e.jsx(Ra,{primaryImage:U,additionalImages:T,description:ee("description"),onAutoFill:f=>{if(Object.entries(f).forEach(([r,l])=>{l!==void 0&&se(r,l)}),f.vehicleTypeCompatibility?.length)if(typeof f.vehicleTypeCompatibility[0]=="string"){const r=f.vehicleTypeCompatibility.map(l=>({id:l.toLowerCase().replace(/\s+/g,"-"),type:"car",brand:l,model:"",displayName:l}));X(r)}else X(f.vehicleTypeCompatibility);ne.success("Product information auto-filled by AI")},disabled:m||!U&&T.length===0&&!ee("description")})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(Z,{type:"button",variant:"outline",onClick:Ue,disabled:m,children:[e.jsx(Ms,{className:"mr-2 h-4 w-4"}),"Cancel"]}),e.jsx(Z,{type:"submit",disabled:m,children:m?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx($t,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})]})]})]})]})})};export{Ba as $,Qt as C,Da as I,Ma as M,Ha as P,$t as S,Oa as V,xt as W,La as a,Ra as b,xa as u};
