import{a as Y,r as t,R as q,j as e,C as b,k as T,Q as k,_ as z,l as F,B as N,aY as J,O as _,c0 as $,L as u,I as h,U as R,w as I,ah as Q,c7 as G,c8 as K,c9 as M,ca as W,cb as X,cc as Z}from"./index-DjNptb3k.js";function se(){const{toast:r}=Y(),[x,m]=t.useState(!1),[f,D]=t.useState(""),[v,V]=t.useState(""),[l,O]=t.useState(""),[i,p]=t.useState(null),[P,A]=t.useState(""),[a,L]=t.useState(""),[o,g]=t.useState(null),[d,w]=t.useState(null),[C,S]=t.useState(null),E=async()=>{m(!0),p(null);try{const s=`+213${f.replace(/\D/g,"")}`,c={fullName:v.trim(),phone:s};l&&(c.passcode=l),console.log("🧪 Testing authentication with:",c);const j=await G(c);p(j),j.success?(r({title:"Authentication Successful!",description:`Action: ${j.action}`}),await y()):r({title:"Authentication Failed",description:j.error,variant:"destructive"})}catch(s){console.error("Auth test error:",s),p({success:!1,error:String(s)}),r({title:"Test Error",description:String(s),variant:"destructive"})}finally{m(!1)}},B=async()=>{m(!0),g(null);try{const s={newPasscode:a};P&&(s.currentPasscode=P),console.log("🧪 Testing passcode update");const c=await K(s);g(c),c.success?(r({title:"Passcode Updated!",description:"Your passcode has been updated successfully."}),A(""),L(""),await U()):r({title:"Update Failed",description:c.error,variant:"destructive"})}catch(s){console.error("Passcode update test error:",s),g({success:!1,error:String(s)}),r({title:"Test Error",description:String(s),variant:"destructive"})}finally{m(!1)}},y=async()=>{try{const s=M();w(s),s&&await U()}catch(s){console.error("Session check error:",s),w({error:String(s)})}},U=async()=>{try{const s=await W();S(s)}catch(s){console.error("Passcode status check error:",s),S(null)}},H=()=>{X(),w(null),S(null),p(null),g(null),r({title:"Logged Out",description:"Consumer session cleared."})},n=s=>Z(s);return q.useEffect(()=>{y()},[]),e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Consumer Passcode System Test"}),e.jsx("p",{className:"text-gray-600",children:"Test the enhanced consumer authentication with passcode support"})]}),e.jsxs(b,{children:[e.jsx(T,{children:e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5"}),"Current Session Status"]})}),e.jsxs(F,{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{onClick:y,variant:"outline",size:"sm",children:"Refresh Status"}),d&&e.jsxs(N,{onClick:H,variant:"destructive",size:"sm",children:[e.jsx(J,{className:"h-4 w-4 mr-2"}),"Logout"]})]}),d?e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsx("p",{className:"font-medium text-green-800",children:"✅ Logged In"}),e.jsxs("p",{className:"text-sm text-green-600",children:["User ID: ",d.user?.id]}),e.jsxs("p",{className:"text-sm text-green-600",children:["Phone: ",d.user?.phone]}),e.jsxs("p",{className:"text-sm text-green-600",children:["Name: ",d.profile?.full_name]}),e.jsxs("p",{className:"text-sm text-green-600",children:["Has Passcode: ",C===!0?"✅ Yes":C===!1?"❌ No":"⏳ Checking..."]})]}):e.jsx("div",{className:"bg-gray-50 p-4 rounded-lg",children:e.jsx("p",{className:"text-gray-600",children:"❌ Not logged in"})})]})]}),e.jsxs(b,{children:[e.jsxs(T,{children:[e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),"Authentication Test"]}),e.jsx($,{children:"Test consumer authentication with optional passcode"})]}),e.jsxs(F,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(u,{htmlFor:"fullName",children:"Full Name"}),e.jsx(h,{id:"fullName",value:v,onChange:s=>V(s.target.value),placeholder:"Ahmed Benali"})]}),e.jsxs("div",{children:[e.jsx(u,{htmlFor:"phone",children:"Phone (9 digits)"}),e.jsx(h,{id:"phone",value:f,onChange:s=>D(s.target.value.replace(/\D/g,"").slice(0,9)),placeholder:"123456789"})]})]}),e.jsxs("div",{children:[e.jsx(u,{htmlFor:"passcode",children:"Passcode (Optional, 4-6 digits)"}),e.jsx(h,{id:"passcode",type:"password",value:l,onChange:s=>O(s.target.value.replace(/\D/g,"").slice(0,6)),placeholder:"1234"}),l&&e.jsx("p",{className:`text-xs mt-1 ${n(l).isValid?"text-green-600":"text-red-600"}`,children:n(l).isValid?"✅ Valid format":`❌ ${n(l).error}`})]}),e.jsx(N,{onClick:E,disabled:x||!v||!f,className:"w-full",children:x?"Testing...":"Test Authentication"}),i&&e.jsxs("div",{className:`p-4 rounded-lg ${i.success?"bg-green-50":"bg-red-50"}`,children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[i.success?e.jsx(R,{className:"h-5 w-5 text-green-600"}):e.jsx(I,{className:"h-5 w-5 text-red-600"}),e.jsx("span",{className:`font-medium ${i.success?"text-green-800":"text-red-800"}`,children:i.success?"Success":"Failed"})]}),e.jsx("pre",{className:"text-xs bg-white p-2 rounded border overflow-auto",children:JSON.stringify(i,null,2)})]})]})]}),d&&e.jsxs(b,{children:[e.jsxs(T,{children:[e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(Q,{className:"h-5 w-5"}),"Passcode Update Test"]}),e.jsx($,{children:"Test updating passcode for logged-in user"})]}),e.jsxs(F,{className:"space-y-4",children:[C&&e.jsxs("div",{children:[e.jsx(u,{htmlFor:"currentPasscode",children:"Current Passcode"}),e.jsx(h,{id:"currentPasscode",type:"password",value:P,onChange:s=>A(s.target.value.replace(/\D/g,"").slice(0,6)),placeholder:"Current passcode"})]}),e.jsxs("div",{children:[e.jsx(u,{htmlFor:"newPasscode",children:"New Passcode (4-6 digits)"}),e.jsx(h,{id:"newPasscode",type:"password",value:a,onChange:s=>L(s.target.value.replace(/\D/g,"").slice(0,6)),placeholder:"New passcode"}),a&&e.jsx("p",{className:`text-xs mt-1 ${n(a).isValid?"text-green-600":"text-red-600"}`,children:n(a).isValid?"✅ Valid format":`❌ ${n(a).error}`})]}),e.jsx(N,{onClick:B,disabled:x||!a||!n(a).isValid,className:"w-full",children:x?"Updating...":"Update Passcode"}),o&&e.jsxs("div",{className:`p-4 rounded-lg ${o.success?"bg-green-50":"bg-red-50"}`,children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.success?e.jsx(R,{className:"h-5 w-5 text-green-600"}):e.jsx(I,{className:"h-5 w-5 text-red-600"}),e.jsx("span",{className:`font-medium ${o.success?"text-green-800":"text-red-800"}`,children:o.success?"Success":"Failed"})]}),e.jsx("pre",{className:"text-xs bg-white p-2 rounded border overflow-auto",children:JSON.stringify(o,null,2)})]})]})]})]})}export{se as default};
