import{r as p,a7 as O,t as S,j as e,B as R,a6 as g,C as i,k as o,Q as d,l,H as I,aC as $,ab as U,U as q,m as X}from"./index-zSaKdklO.js";import{D as T}from"./database-ps_zAqsB.js";import{U as V}from"./users-CsBuZuVc.js";function F(){const[a,A]=p.useState({supabaseConnection:"checking",authService:"checking",productService:"checking",marketplaceProducts:"checking"}),[u,E]=p.useState({}),[k,N]=p.useState(!1),{isAuthenticated:j,user:L,profile:z}=O(),{products:f,isLoading:B,error:C}=S("tyres"),{products:b,isLoading:J,error:w}=S("brakes"),{products:v,isLoading:M,error:y}=S("filters"),P=async()=>{N(!0);const s={...a},r={};try{const x="https://irkwpzcskeqtasutqnxp.supabase.co",D="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94";if(!(!x||!D))try{const t=$(x,D),{data:c,error:n}=await t.from("products").select("count",{count:"exact",head:!0});n?(s.supabaseConnection="error",r.supabaseConnection=n.message):(s.supabaseConnection="success",r.supabaseConnection=`Connected. Products count: ${c}`)}catch(t){s.supabaseConnection="error",r.supabaseConnection=t.message}try{s.authService=j?"success":"warning",r.authService=j?`Authenticated as ${L?.email} (${z?.role})`:"Not authenticated"}catch(t){s.authService="error",r.authService=t.message}try{const t={tyres:f.length,brakes:b.length,filters:v.length},c=Object.values(t).some(n=>n>0);s.productService=c?"success":"warning",r.productService=`Products: ${JSON.stringify(t)}`,(C||w||y)&&(s.productService="error",r.productService+=` Errors: ${[C,w,y].filter(Boolean).map(n=>n.message).join(", ")}`)}catch(t){s.productService="error",r.productService=t.message}try{const t=B||J||M,c=f.length+b.length+v.length;s.marketplaceProducts=t?"checking":c>0?"success":"warning",r.marketplaceProducts=t?"Loading...":`Total marketplace products: ${c}`}catch(t){s.marketplaceProducts="error",r.marketplaceProducts=t.message}}catch(x){console.error("Diagnostics error:",x)}A(s),E(r),N(!1)};p.useEffect(()=>{P()},[j,f,b,v]);const m=s=>{switch(s){case"success":return e.jsx(q,{className:"h-5 w-5 text-green-600"});case"warning":return e.jsx(U,{className:"h-5 w-5 text-yellow-600"});case"error":return e.jsx(U,{className:"h-5 w-5 text-red-600"});case"checking":return e.jsx(g,{className:"h-5 w-5 text-blue-600 animate-spin"});default:return e.jsx(g,{className:"h-5 w-5 text-gray-600"})}},h=s=>{const r={success:"default",warning:"secondary",error:"destructive",checking:"outline"};return e.jsx(X,{variant:r[s]||"outline",children:s})};return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-3xl font-bold",children:"System Diagnostics"}),e.jsxs(R,{onClick:P,disabled:k,children:[k?e.jsx(g,{className:"h-4 w-4 animate-spin mr-2"}):e.jsx(g,{className:"h-4 w-4 mr-2"}),"Run Diagnostics"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(i,{children:[e.jsx(o,{children:e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(T,{className:"h-5 w-5"}),"Supabase Connection",h(a.supabaseConnection)]})}),e.jsxs(l,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(a.supabaseConnection),e.jsx("span",{className:"font-medium",children:"Database Connection"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:u.supabaseConnection||"Checking connection..."})]})]}),e.jsxs(i,{children:[e.jsx(o,{children:e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5"}),"Authentication Service",h(a.authService)]})}),e.jsxs(l,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(a.authService),e.jsx("span",{className:"font-medium",children:"Auth Status"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:u.authService||"Checking authentication..."})]})]}),e.jsxs(i,{children:[e.jsx(o,{children:e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5"}),"Product Service",h(a.productService)]})}),e.jsxs(l,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(a.productService),e.jsx("span",{className:"font-medium",children:"Product Data"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:u.productService||"Checking product service..."})]})]}),e.jsxs(i,{children:[e.jsx(o,{children:e.jsxs(d,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5"}),"Marketplace Products",h(a.marketplaceProducts)]})}),e.jsxs(l,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(a.marketplaceProducts),e.jsx("span",{className:"font-medium",children:"Marketplace Data"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:u.marketplaceProducts||"Checking marketplace products..."})]})]})]}),e.jsxs(i,{children:[e.jsx(o,{children:e.jsx(d,{children:"Environment Information"})}),e.jsx(l,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Supabase URL:"})," ","https://irkwpzcskeqtasutqnxp.supabase.co"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Use Supabase:"})," ","true"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Use Supabase Backend:"})," ","true"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Environment:"})," ","production"]})]})})]})]})}function Z(){return e.jsx(F,{})}export{Z as default};
