const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/idGenerator-9VrvIwdw.js","assets/index-zSaKdklO.js","assets/categories-k_ueMvEL.js"])))=>i.map(i=>d[i]);
var Z=t=>{throw TypeError(t)};var L=(t,o,r)=>o.has(t)||Z("Cannot "+r);var g=(t,o,r)=>(L(t,o,"read from private field"),r?r.call(t):o.get(t)),_=(t,o,r)=>o.has(t)?Z("Cannot add the same private member more than once"):o instanceof WeakSet?o.add(t):o.set(t,r),K=(t,o,r,a)=>(L(t,o,"write to private field"),a?a.call(t,r):o.set(t,r),r),V=(t,o,r)=>(L(t,o,"access private method"),r);import{c as tt,cw as ot,cx as st,cy as I,cz as nt,cA as et,bn as z,r as d,cB as it,cC as ct,bb as at,bo as rt,cD as lt,as as G,cE as ut,ay as B,aD as dt,cF as ht,cG as mt,cH as pt}from"./index-zSaKdklO.js";import{i as gt}from"./features-CbSLR5ud.js";import{generateProductId as ft}from"./idGenerator-9VrvIwdw.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tt=tt("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ot=tt("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var N,U,w,Q,F,q,j,X,yt=(X=class extends ot{constructor(o,r){super();_(this,F);_(this,N);_(this,U);_(this,w);_(this,Q);K(this,N,o),this.setOptions(r),this.bindMethods(),V(this,F,q).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(o){const r=this.options;this.options=g(this,N).defaultMutationOptions(o),st(this.options,r)||g(this,N).getMutationCache().notify({type:"observerOptionsUpdated",mutation:g(this,w),observer:this}),r?.mutationKey&&this.options.mutationKey&&I(r.mutationKey)!==I(this.options.mutationKey)?this.reset():g(this,w)?.state.status==="pending"&&g(this,w).setOptions(this.options)}onUnsubscribe(){this.hasListeners()||g(this,w)?.removeObserver(this)}onMutationUpdate(o){V(this,F,q).call(this),V(this,F,j).call(this,o)}getCurrentResult(){return g(this,U)}reset(){g(this,w)?.removeObserver(this),K(this,w,void 0),V(this,F,q).call(this),V(this,F,j).call(this)}mutate(o,r){return K(this,Q,r),g(this,w)?.removeObserver(this),K(this,w,g(this,N).getMutationCache().build(g(this,N),this.options)),g(this,w).addObserver(this),g(this,w).execute(o)}},N=new WeakMap,U=new WeakMap,w=new WeakMap,Q=new WeakMap,F=new WeakSet,q=function(){const o=g(this,w)?.state??nt();K(this,U,{...o,isPending:o.status==="pending",isSuccess:o.status==="success",isError:o.status==="error",isIdle:o.status==="idle",mutate:this.mutate,reset:this.reset})},j=function(o){et.batch(()=>{if(g(this,Q)&&this.hasListeners()){const r=g(this,U).variables,a=g(this,U).context;o?.type==="success"?(g(this,Q).onSuccess?.(o.data,r,a),g(this,Q).onSettled?.(o.data,null,r,a)):o?.type==="error"&&(g(this,Q).onError?.(o.error,r,a),g(this,Q).onSettled?.(void 0,o.error,r,a))}this.listeners.forEach(r=>{r(g(this,U))})})},X);function H(t,o){const r=z(),[a]=d.useState(()=>new yt(r,t));d.useEffect(()=>{a.setOptions(t)},[a,t]);const s=d.useSyncExternalStore(d.useCallback(f=>a.subscribe(et.batchCalls(f)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),h=d.useCallback((f,b)=>{a.mutate(f,b).catch(it)},[a]);if(s.error&&ct(a.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:h,mutateAsync:s.mutate}}const Bt=(t,o)=>{if(t==null||isNaN(Number(t)))return"-";const{minimumFractionDigits:r=0,maximumFractionDigits:a=0,compact:s=!1}=o||{};return new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:r,maximumFractionDigits:a,notation:s?"compact":"standard"}).format(t)},Qt=t=>t==null||isNaN(Number(t))?"-":new Intl.NumberFormat("fr-DZ").format(t),Mt=()=>{const t=[],o=50+Math.random()*150;return t.push({minQuantity:1,maxQuantity:10,price:o}),t.push({minQuantity:11,maxQuantity:50,price:o*.9}),t.push({minQuantity:51,price:o*.8}),t},Pt=()=>{const t=["active","draft","pending_approval","out_of_stock","discontinued"],o=[.7,.1,.1,.05,.05],r=Math.random();let a=0;for(let s=0;s<o.length;s++)if(a+=o[s],r<a)return t[s];return"active"},St=t=>{const o=[],r=["Michelin","Bridgestone","Continental","Pirelli","Goodyear","Dunlop","Hankook","Yokohama","Toyo","Maxxis"],a=["Pilot Sport 4","Potenza RE980AS","ExtremeContact DWS06","P Zero","Eagle F1 Asymmetric","SP Sport Maxx","Ventus V12 evo2","ADVAN Sport V105","Proxes T1 Sport","Premitra HP5"],s=[185,195,205,215,225,235,245,255,265,275,285,295,305,315],h=[35,40,45,50,55,60,65,70],f=[15,16,17,18,19,20,21,22],b=["H","V","W","Y","Z","R","S","T"],S=["Summer","Winter","All-Season"],E=["Passenger Car","SUV","Truck","Off-Road","Performance","Luxury","Compact","Sedan"];for(let D=0;D<t;D++){const R=Math.floor(Math.random()*r.length),k=r[R],x=a[Math.floor(Math.random()*a.length)],O=s[Math.floor(Math.random()*s.length)],$=h[Math.floor(Math.random()*h.length)],A=f[Math.floor(Math.random()*f.length)],v=b[Math.floor(Math.random()*b.length)],C=75+Math.floor(Math.random()*30),c=S[Math.floor(Math.random()*S.length)],l=1+Math.floor(Math.random()*3),m=[];for(let y=0;y<l;y++){const e=E[Math.floor(Math.random()*E.length)];m.includes(e)||m.push(e)}const P=Math.floor(Math.random()*100),u=Pt(),p={id:`TYR-${1e5+D}`,name:`${k} ${x} ${O}/${$}R${A}`,sku:`${k.substring(0,3).toUpperCase()}-${O}${$}R${A}-${C}${v}`,barcode:`${Math.floor(Math.random()*1e13)}`,category:"tyres",subcategory:"Tyres",description:`${k} ${x} ${O}/${$}R${A} ${C}${v} ${c} tyre for ${m.join(", ")}`,primaryImage:Math.random()>.7?"/placeholder.svg":void 0,additionalImages:Math.random()>.8?Array(Math.floor(Math.random()*3)+1).fill("/placeholder.svg"):[],manufacturer:k,supplierName:"Global Tyre Distributors",stockQuantity:P,wholesalePricingTiers:Mt(),minimumOrderQuantity:2,quotationRequestEnabled:Math.random()>.5,retailPrice:80+Math.random()*200,width:O,aspectRatio:$,rimDiameter:A,loadIndex:C,speedRating:v,season:c,vehicleTypeCompatibility:m,treadLife:`${3e4+Math.floor(Math.random()*4e4)} miles`,tractionRating:["A","AA","B","C"][Math.floor(Math.random()*4)],temperatureRating:["A","B","C"][Math.floor(Math.random()*3)],certifications:["DOT","ECE","ISO 9001"],shippingOrigin:["China","Japan","Germany","USA","South Korea"][Math.floor(Math.random()*5)],estimatedLeadTime:`${3+Math.floor(Math.random()*10)} days`,packagingDetails:"Individual cardboard boxes, palletized for bulk orders",inventoryUpdateDate:new Date(Date.now()-Math.floor(Math.random()*30)*24*60*60*1e3),status:u,adminNotes:Math.random()>.7?"Special pricing available for bulk orders":void 0,createdAt:new Date(Date.now()-Math.floor(Math.random()*365)*24*60*60*1e3),updatedAt:new Date};o.push(p)}return o},bt=()=>{const t=[],o=30+Math.random()*120;return t.push({minQuantity:1,maxQuantity:10,price:o}),t.push({minQuantity:11,maxQuantity:50,price:o*.9}),t.push({minQuantity:51,price:o*.8}),t},Dt=()=>{const t=["active","draft","pending_approval","out_of_stock","discontinued"],o=[.7,.1,.1,.05,.05],r=Math.random();let a=0;for(let s=0;s<o.length;s++)if(a+=o[s],r<a)return t[s];return"active"},Y=t=>{const o=[],r=["Brembo","ATE","TRW","Bosch","Ferodo","Textar","Zimmermann","Pagid","EBC","Akebono"],a=["High Carbon","Sport","Premium","Coated","Vented","Drilled","Slotted","Composite","Ceramic"],s=["Ceramic","Semi-Metallic","Organic","Low-Dust","Performance","Heavy Duty","Sport","Racing"],h=["Brake Discs","Brake Pads","Brake Caliper","Brake Shoes","Brake Drums","Brake Fluid","Brake Hose","ABS Sensor","Brake Caliper Repair Kit","Master Brake Cylinder"],f=[["BMW 3 Series (E90/E91/E92/E93) 2005-2011","BMW 5 Series (E60/E61) 2003-2010","BMW X1 (E84) 2009-2015"],["Audi A4 (B8) 2008-2015","Audi A6 (C6) 2004-2011","Audi Q5 (8R) 2008-2017"],["Mercedes-Benz C-Class (W204) 2007-2014","Mercedes-Benz E-Class (W212) 2009-2016"],["Volkswagen Golf Mk6 2008-2013","Volkswagen Passat B6 2005-2010","Volkswagen Tiguan 2007-2016"],["Ford Focus Mk3 2011-2018","Ford Mondeo Mk4 2007-2014","Ford Kuga 2008-2012"],["Toyota Corolla E140/E150 2006-2013","Toyota Avensis T27 2008-2018","Toyota RAV4 XA30 2005-2012"],["Honda Civic 8th Gen 2006-2011","Honda Accord 8th Gen 2008-2012","Honda CR-V 3rd Gen 2007-2011"],["Nissan Qashqai J10 2006-2013","Nissan X-Trail T31 2007-2013","Nissan Juke 2010-2019"]],b=["China","Japan","Germany","USA","South Korea","Italy","Spain","France"];for(let S=0;S<t;S++){const E=Math.floor(Math.random()*r.length),D=r[E],R=Math.floor(Math.random()*h.length),k=h[R];let x="";k==="Brake Discs"?x=a[Math.floor(Math.random()*a.length)]:k==="Brake Pads"&&(x=s[Math.floor(Math.random()*s.length)]);const O=Math.floor(Math.random()*f.length),$=f[O],A=Math.floor(Math.random()*100)+1,v=Dt();let C="";k==="Brake Discs"?C=`## Product Identification
- Part Number: ${D.substring(0,3).toUpperCase()}-${1e5+S}
- Brand: ${D}
- Component Type: Brake Disc
- Function: ${Math.random()>.5?"Front Axle":"Rear Axle"}

## Technical Specifications
- Diameter: ${260+Math.floor(Math.random()*80)}mm
- Thickness: ${20+Math.floor(Math.random()*15)}mm
- Minimum Thickness: ${18+Math.floor(Math.random()*10)}mm
- Number of Holes: ${4+Math.floor(Math.random()*2)}
- Hole Circle Ø: ${100+Math.floor(Math.random()*40)}mm
- Centering Diameter: ${60+Math.floor(Math.random()*20)}mm
- Surface: ${Math.random()>.5?"Coated":"Uncoated"}
- Brake Disc Type: ${Math.random()>.5?"Internally Vented":"Solid"}
- Height: ${40+Math.floor(Math.random()*15)}mm

## Vehicle Compatibility
${$.map(l=>`- ${l}`).join(`
`)}

## Additional Information
- ${Math.random()>.5?"High-carbon cast iron construction for improved thermal stability":"Premium quality cast iron for durability and performance"}
- ${Math.random()>.5?"UV-coated for corrosion resistance":"Special anti-corrosion coating"}
- Precision balanced for smooth operation
- Meets or exceeds OE specifications`:k==="Brake Pads"?C=`## Product Identification
- Part Number: ${D.substring(0,3).toUpperCase()}-${1e5+S}
- Brand: ${D}
- Component Type: Brake Pads
- Function: ${Math.random()>.5?"Front Axle":"Rear Axle"}

## Technical Specifications
- Material: ${x}
- Width: ${100+Math.floor(Math.random()*50)}mm
- Height: ${50+Math.floor(Math.random()*30)}mm
- Thickness: ${15+Math.floor(Math.random()*10)}mm
- Wear Indicator: ${Math.random()>.5?"Yes":"No"}
- Brake System: ${Math.random()>.5?"Bosch":"ATE"}
- WVA Number: ${8e4+Math.floor(Math.random()*1e4)}

## Vehicle Compatibility
${$.map(l=>`- ${l}`).join(`
`)}

## Additional Information
- ${Math.random()>.5?"Low dust formulation for cleaner wheels":"High performance compound for improved braking"}
- ${Math.random()>.5?"Includes fitting accessories":"Hardware kit included"}
- Chamfered and slotted for noise reduction
- Meets or exceeds OE specifications`:C=`## Product Identification
- Part Number: ${D.substring(0,3).toUpperCase()}-${1e5+S}
- Brand: ${D}
- Component Type: ${k}
- Function: ${Math.random()>.5?"Front Axle":"Rear Axle"}

## Technical Specifications
- Material: High-grade ${Math.random()>.5?"aluminum":"steel"}
- Weight: ${.5+Math.random()*2.5}kg
- Dimensions: Various
- OE Quality: Yes

## Vehicle Compatibility
${$.map(l=>`- ${l}`).join(`
`)}

## Additional Information
- Direct replacement for original part
- Easy installation
- Meets or exceeds OE specifications
- 2-year manufacturer warranty`;const c={id:`BRK-${1e5+S}`,name:`${D} ${x} ${k} ${Math.random()>.5?"Front":"Rear"} ${Math.random()>.5?"Axle":""}`.trim(),sku:`${D.substring(0,3).toUpperCase()}-${k.substring(0,2).toUpperCase()}-${1e5+S}`,barcode:`${Math.floor(Math.random()*1e13)}`,category:"brakes",subcategory:k,description:C,primaryImage:Math.random()>.3?"/placeholder.svg":void 0,additionalImages:Math.random()>.7?Array(Math.floor(Math.random()*3)+1).fill("/placeholder.svg"):[],manufacturer:D,supplierName:"Global Brake Parts Distributors",stockQuantity:A,wholesalePricingTiers:bt(),minimumOrderQuantity:Math.random()>.5?2:1,quotationRequestEnabled:Math.random()>.7,retailPrice:40+Math.random()*150,vehicleCompatibility:$,certifications:["ECE R90","ISO 9001","TÜV"],shippingOrigin:b[Math.floor(Math.random()*b.length)],availableShippingMethods:["Standard","Express","Economy"],inventoryUpdateDate:new Date,status:v,createdAt:new Date(Date.now()-Math.floor(Math.random()*1e10)),updatedAt:new Date};o.push(c)}return o},kt=async(t,o)=>{await new Promise(a=>setTimeout(a,800));let r=[];if(t==="all-other-categories"){const a=["brakes"];for(const s of a){const h=`products-${s}`,f=localStorage.getItem(h);if(f)try{const S=JSON.parse(f).map(E=>({...E,createdAt:new Date(E.createdAt),updatedAt:new Date(E.updatedAt),inventoryUpdateDate:E.inventoryUpdateDate?new Date(E.inventoryUpdateDate):void 0}));r.push(...S)}catch(b){console.error(`Error parsing stored product data for ${s}:`,b)}else if(s==="brakes"){const b=Y(100);r.push(...b);try{localStorage.setItem(h,JSON.stringify(b))}catch(S){console.error(`Error storing mock data for ${s}:`,S)}}}}else{const a=`products-${t}`,s=localStorage.getItem(a);if(s)try{r=JSON.parse(s).map(f=>({...f,createdAt:new Date(f.createdAt),updatedAt:new Date(f.updatedAt),inventoryUpdateDate:f.inventoryUpdateDate?new Date(f.inventoryUpdateDate):void 0})),console.log(`Using stored product data from localStorage for ${t}`)}catch(h){console.error("Error parsing stored product data:",h),r=[]}if(r.length===0){t==="tyres"?r=St(100):t==="brakes"&&(r=Y(100));try{localStorage.setItem(a,JSON.stringify(r))}catch(h){console.error("Error storing product data in localStorage:",h)}}}return r=r.map(a=>o==="supplier"?{...a,marketplaceSection:"wholesale",wholesalePricingTiers:a.wholesalePricingTiers||[{minQuantity:1,maxQuantity:10,price:a.retailPrice||100},{minQuantity:11,maxQuantity:50,price:(a.retailPrice||100)*.9},{minQuantity:51,price:(a.retailPrice||100)*.8}]}:o==="merchant"?{...a,marketplaceSection:"retail",retailPrice:a.retailPrice||(a.wholesalePricingTiers&&a.wholesalePricingTiers.length>0?a.wholesalePricingTiers[0].price*1.3:100)}:a),r},$t=t=>{const[o,r]=d.useState([]),a=z(),{userRole:s}=at(),{data:h=[],isLoading:f,isError:b,error:S,refetch:E}=rt({queryKey:["products",t,s],queryFn:()=>kt(t,s),staleTime:5*60*1e3}),D=d.useCallback(c=>{r(l=>[...l,c])},[]),R=d.useCallback(c=>{r(l=>l.filter(m=>m!==c))},[]),k=d.useCallback(c=>{r(l=>l.includes(c)?l.filter(m=>m!==c):[...l,c])},[]),x=d.useCallback(()=>{r(h.map(c=>c.id))},[h]),O=d.useCallback(()=>{r([])},[]);d.useEffect(()=>{r([])},[t]);const $=d.useCallback((c,l)=>{a.setQueryData(["products",t,s],(m=[])=>m.map(u=>{if(u.id===c){const p={...u,...l,updatedAt:new Date};if(t==="all-other-categories")try{const e=`products-${p.category}`,n=localStorage.getItem(e);if(n){const M=JSON.parse(n).map(T=>T.id===c?p:T);localStorage.setItem(e,JSON.stringify(M))}}catch(y){console.error("Error updating product in actual category localStorage:",y)}else try{const y=`products-${t}`,e=localStorage.getItem(y);if(e){const i=JSON.parse(e).map(M=>M.id===c?p:M);localStorage.setItem(y,JSON.stringify(i))}}catch(y){console.error("Error updating product data in localStorage:",y)}return p}return u}))},[a,t,s]),A=d.useCallback(async c=>{const l=c.category||(t==="all-other-categories"?"brakes":t);let m={};s==="supplier"?m={marketplaceSection:"wholesale",wholesalePricingTiers:c.wholesalePricingTiers||[{minQuantity:1,maxQuantity:10,price:c.retailPrice||100},{minQuantity:11,maxQuantity:50,price:(c.retailPrice||100)*.9},{minQuantity:51,price:(c.retailPrice||100)*.8}]}:s==="merchant"&&(m={marketplaceSection:"retail",retailPrice:c.retailPrice||(c.wholesalePricingTiers&&c.wholesalePricingTiers.length>0?c.wholesalePricingTiers[0].price*1.3:100)});const P=s==="supplier"?"wholesale":"retail",u={...c,...m,id:c.id||ft(l,P),createdAt:new Date,updatedAt:new Date};console.log(`Adding product to category: ${l} (via ${t} table)`,u);const p=`products-${l}`;try{const y=localStorage.getItem(p);let e=[];y&&(e=JSON.parse(y),e=e.map(i=>({...i,createdAt:new Date(i.createdAt),updatedAt:new Date(i.updatedAt),inventoryUpdateDate:i.inventoryUpdateDate?new Date(i.inventoryUpdateDate):void 0})));const n=[...e,u];localStorage.setItem(p,JSON.stringify(n)),a.setQueryData(["products",t,s],(i=[])=>[...i,u]),t==="all-other-categories"&&l!==t&&a.invalidateQueries({queryKey:["products",l,s]})}catch(y){console.error(`Error adding product to category ${l}:`,y)}return u},[a,t,s]),v=d.useCallback(async c=>{const l=h.find(m=>m.id===c);a.setQueryData(["products",t,s],(m=[])=>{const P=m.filter(u=>u.id!==c);if(t==="all-other-categories"&&l)try{const p=`products-${l.category}`,y=localStorage.getItem(p);if(y){const n=JSON.parse(y).filter(i=>i.id!==c);localStorage.setItem(p,JSON.stringify(n))}}catch(u){console.error("Error deleting product from actual category localStorage:",u)}else try{const u=`products-${t}`;localStorage.setItem(u,JSON.stringify(P))}catch(u){console.error("Error updating product data in localStorage after deletion:",u)}return P}),r(m=>m.filter(P=>P!==c))},[a,t,s,h]),C=d.useCallback(async c=>{const l=h.filter(P=>c.includes(P.id)),m={};l.forEach(P=>{const u=P.category;m[u]||(m[u]=[]),m[u].push(P.id)}),a.setQueryData(["products",t,s],(P=[])=>{const u=P.filter(p=>!c.includes(p.id));if(t==="all-other-categories")Object.entries(m).forEach(([p,y])=>{try{const e=`products-${p}`,n=localStorage.getItem(e);if(n){const M=JSON.parse(n).filter(T=>!y.includes(T.id));localStorage.setItem(e,JSON.stringify(M))}}catch(e){console.error(`Error deleting products from ${p} localStorage:`,e)}});else try{const p=`products-${t}`;localStorage.setItem(p,JSON.stringify(u))}catch(p){console.error("Error updating product data in localStorage after bulk deletion:",p)}return u}),r(P=>P.filter(u=>!c.includes(u)))},[a,t,s,h]);return{products:h,isLoading:f,isError:b,error:S,refetch:E,selectedProductIds:o,setSelectedProductIds:r,selectProduct:D,deselectProduct:R,toggleProductSelection:k,selectAllProducts:x,deselectAllProducts:O,updateProduct:$,addProduct:A,deleteProduct:v,deleteProducts:C}},Ct=t=>{const[o,r]=d.useState([]),a=z(),{userRole:s}=at(),{data:h=[],isLoading:f,isError:b,error:S,refetch:E}=rt({queryKey:["products",t,s],queryFn:()=>lt(t),staleTime:5*60*1e3,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4)}),D=d.useCallback(e=>{r(n=>[...n,e])},[]),R=d.useCallback(e=>{r(n=>n.filter(i=>i!==e))},[]),k=d.useCallback(e=>{r(n=>n.includes(e)?n.filter(i=>i!==e):[...n,e])},[]),x=d.useCallback(()=>{r(h.map(e=>e.id))},[h]),O=d.useCallback(()=>{r([])},[]),$=H({mutationFn:({productId:e,updates:n})=>ut(e,n),onSuccess:e=>{a.setQueryData(["products",t,s],(n=[])=>n.map(i=>i.id===e.id?e:i)),a.setQueryData(["product",e.id],e),a.invalidateQueries({queryKey:["products"]}),B.success("Product updated successfully")},onError:e=>{console.error("Error updating product:",e),B.error("Failed to update product")}}),A=H({mutationFn:e=>dt(e),onSuccess:e=>{a.setQueryData(["products",t,s],(n=[])=>[e,...n]),e.category!==t&&a.invalidateQueries(["products",e.category,s]),a.invalidateQueries({queryKey:["products"]}),B.success("Product created successfully")},onError:e=>{console.error("Error creating product:",e),B.error("Failed to create product")}}),v=H({mutationFn:e=>ht(e),onSuccess:(e,n)=>{a.setQueryData(["products",t,s],(i=[])=>i.filter(M=>M.id!==n)),r(i=>i.filter(M=>M!==n)),a.removeQueries(["product",n]),B.success("Product deleted successfully")},onError:e=>{console.error("Error deleting product:",e),B.error("Failed to delete product")}}),C=H({mutationFn:e=>mt(e),onSuccess:(e,n)=>{a.setQueryData(["products",t,s],(i=[])=>i.filter(M=>!n.includes(M.id))),r(i=>i.filter(M=>!n.includes(M))),n.forEach(i=>{a.removeQueries(["product",i])}),B.success(`${n.length} products deleted successfully`)},onError:e=>{console.error("Error deleting products:",e),B.error("Failed to delete products")}}),c=H({mutationFn:({productIds:e,status:n})=>pt(e,n),onSuccess:(e,{productIds:n,status:i})=>{a.setQueryData(["products",t,s],(M=[])=>M.map(T=>n.includes(T.id)?{...T,status:i,updatedAt:new Date}:T)),B.success(`Product status updated to ${i}`)},onError:e=>{console.error("Error updating product status:",e),B.error("Failed to update product status")}}),l=d.useCallback((e,n)=>{$.mutate({productId:e,updates:n})},[$]),m=d.useCallback(async e=>{if(!e.id){const{generateUniqueProductId:n}=await G(async()=>{const{generateUniqueProductId:W}=await import("./idGenerator-9VrvIwdw.js");return{generateUniqueProductId:W}},__vite__mapDeps([0,1,2]));let i=e.category;i||(t==="all-other-categories"?(console.warn("No category specified for unified table product, defaulting to brakes"),i="brakes"):i=t);const{categoryExists:M}=await G(async()=>{const{categoryExists:W}=await import("./categories-k_ueMvEL.js");return{categoryExists:W}},[]);M(i)||(console.warn(`Invalid category: ${i}, defaulting to brakes`),i="brakes");const J=await n(i,s==="supplier"?"wholesale":"retail");e={...e,id:J,category:i}}return A.mutateAsync(e)},[A,t,s]),P=d.useCallback(async e=>v.mutateAsync(e),[v]),u=d.useCallback(async e=>C.mutateAsync(e),[C]),p=d.useCallback(async(e,n)=>c.mutateAsync({productIds:e,status:n}),[c]),y=d.useCallback(async e=>{const n=a.getQueryData(["product",e]);if(n)return n;try{const{getProductByIdForOwner:i}=await G(async()=>{const{getProductByIdForOwner:T}=await import("./index-zSaKdklO.js").then(J=>J.d6);return{getProductByIdForOwner:T}},[]),M=await i(e);return M&&a.setQueryData(["product",e],M),M}catch(i){return console.error("Error fetching product by ID for owner:",i),null}},[a]);return{products:h,isLoading:f||A.isPending||$.isPending||v.isPending||C.isPending||c.isPending,isError:b,error:S,refetch:E,selectedProductIds:o,setSelectedProductIds:r,selectProduct:D,deselectProduct:R,toggleProductSelection:k,selectAllProducts:x,deselectAllProducts:O,updateProduct:l,addProduct:m,deleteProduct:P,deleteProducts:u,updateProductStatus:p,getProduct:y,isCreating:A.isPending,isUpdating:$.isPending,isDeleting:v.isPending||C.isPending,isUpdatingStatus:c.isPending}};function Et(){const t=gt("useSupabaseBackend");return console.log("🔧 ProductsFactory - Feature flag useSupabaseBackend:",t),console.log("🔧 ProductsFactory - Environment VITE_USE_SUPABASE_BACKEND:","true"),t?(console.log("🔄 Using Supabase backend for products"),Ct):(console.log("💾 Using localStorage backend for products"),$t)}const Ft=Et();export{Tt as D,Ot as S,Qt as a,Bt as f,Ft as u};
