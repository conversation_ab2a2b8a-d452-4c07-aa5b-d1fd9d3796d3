import{as as u}from"./index-zSaKdklO.js";import{getCategoryPrefix as d}from"./categories-k_ueMvEL.js";const f=t=>d(t),s=(t,r="retail",o)=>{let e;if(o!==void 0)e=o;else{const n=Date.now(),c=Math.floor(Math.random()*1e3);e=parseInt((n.toString()+c.toString().padStart(3,"0")).slice(-6)),e<1e5&&(e=1e5+e)}return`${f(t)}-${e}-${r==="wholesale"?"WHOLESALE":"RETAIL"}`},p=(t,r,o)=>s(t,o,1e5+r),I=t=>t.endsWith("-WHOLESALE")?"wholesale":t.endsWith("-RETAIL")?"retail":null,P=t=>/^(TYR|BRK|FLT|PROD)-\d{6}-(WHOLESALE|RETAIL)$/.test(t)?!0:/^(TYR|BRK|FLT|PROD)-\d{6}$/.test(t),E=async(t,r="retail",o=5)=>{try{const{default:e}=await u(async()=>{const{default:a}=await import("./index-zSaKdklO.js").then(n=>n.d5);return{default:a}},[]);for(let a=0;a<o;a++){const n=s(t,r),{data:c,error:l}=await e.from("products").select("id").eq("id",n).maybeSingle();if(l&&l.code!=="PGRST116"&&console.warn(`Error checking product ID uniqueness: ${l.message}`),!c)return console.log(`Generated unique product ID: ${n} (attempt ${a+1})`),n;console.log(`Product ID collision detected: ${n}, retrying... (attempt ${a+1})`)}const i=s(t,r,Date.now()%9e5);return console.warn(`Failed to generate unique ID after ${o} attempts, using fallback: ${i}`),i}catch(e){return console.warn("Could not import supabase for ID collision checking, using regular generation:",e),s(t,r)}};export{I as extractMarketplaceSectionFromId,s as generateProductId,p as generateProductIdForIndex,E as generateUniqueProductId,f as getCategoryPrefix,P as isValidProductId};
