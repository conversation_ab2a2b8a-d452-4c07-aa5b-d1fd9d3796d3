var m=Object.defineProperty;var S=(o,r,e)=>r in o?m(o,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[r]=e;var h=(o,r,e)=>S(o,typeof r!="symbol"?r+"":r,e);import{a1 as f}from"./index-zSaKdklO.js";import{v as d,s as y,a as P}from"./dexatelService-CytZNhyS.js";class l{static set(r,e){try{const s=this.getAll();s[r]=e,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(s))}catch(s){console.error("Error storing phone auth session:",s)}}static get(r){try{const s=this.getAll()[r];return s?new Date>new Date(s.expiresAt)?(this.delete(r),null):s:null}catch(e){return console.error("Error retrieving phone auth session:",e),null}}static delete(r){try{const e=this.getAll();delete e[r],localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e))}catch(e){console.error("Error deleting phone auth session:",e)}}static getAll(){try{const r=localStorage.getItem(this.STORAGE_KEY);return r?JSON.parse(r):{}}catch(r){return console.error("Error parsing phone auth sessions:",r),{}}}static cleanup(){try{const r=this.getAll(),e=new Date;let s=!1;for(const[t,n]of Object.entries(r))e>new Date(n.expiresAt)&&(delete r[t],s=!0);s&&localStorage.setItem(this.STORAGE_KEY,JSON.stringify(r))}catch(r){console.error("Error cleaning up phone auth sessions:",r)}}}h(l,"STORAGE_KEY","phone_auth_sessions");async function A(o){try{console.log("🔐 Starting phone authentication for:",o);const r=d(o);if(!r.isValid)return{success:!1,error:r.error||"Invalid phone number format"};const e=r.formatted;console.log("📱 Formatted phone:",e);const s=await y(e);if(!s.success)return{success:!1,error:s.error,rateLimitInfo:s.rateLimitInfo};const t=`session_${Date.now()}_${Math.random().toString(36).substring(2)}`,n=new Date(Date.now()+5*60*1e3);return l.set(t,{verificationId:s.data.id,phone:e,expiresAt:n.toISOString(),step:1}),console.log("✅ OTP sent successfully, session created:",t),{success:!0,verificationId:t,rateLimitInfo:s.rateLimitInfo}}catch(r){return console.error("❌ Error in sendPhoneOTP:",r),{success:!1,error:"Failed to send verification code. Please try again."}}}async function D(o,r){try{console.log("🔐 Verifying OTP for session:",o),console.log("🔍 Looking for session:",o),console.log("🔍 All stored sessions:",l.getAll());const e=l.get(o);if(console.log("🔍 Retrieved session:",e),!e)return console.error("❌ Session not found or expired:",o),{success:!1,error:"Invalid or expired verification session"};console.log("🔐 Verifying OTP with verification ID:",e.verificationId);const s=await P(e.verificationId,r);if(!s.success)return console.error("❌ OTP verification failed:",s.error),{success:!1,error:s.error};console.log("✅ OTP verified successfully with Dexatel"),console.log("🔐 Proceeding with Supabase authentication for phone:",e.phone);const t=await _(e.phone,e.verificationId);return t.success?(console.log("✅ Phone authentication completed successfully"),l.delete(o)):console.error("❌ Phone authentication failed:",t.error),t}catch(e){return console.error("❌ Error in verifyPhoneOTP:",e),{success:!1,error:"Failed to verify code. Please try again."}}}async function _(o,r){try{const{data:e,error:s}=await f.rpc("handle_phone_verification",{p_phone:o,p_verification_id:r});if(s)return console.error("Database error in phone verification:",s),{success:!1,error:"Authentication failed. Please try again."};const t=e.user_id,n=e.action;if(console.log(`📱 Phone authentication ${n} successful for user:`,t),console.log("📱 Full database result:",e),!t)return console.error("❌ CRITICAL: No user ID returned from database function"),console.error("❌ Database result:",e),{success:!1,error:"Authentication failed. Please try again."};console.log("🔐 Creating manual session for verified phone user");const a=await v(t,o);return a.success?(console.log("✅ Phone session created successfully"),{success:!0,user:a.user,profile:a.profile,action:n}):(console.error("❌ CRITICAL: Failed to create phone session"),console.error("❌ Session result:",a),console.error("❌ User ID:",t),console.error("❌ Phone:",o),{success:!1,error:"Failed to create authentication session"})}catch(e){return console.error("Error in handlePhoneAuthentication:",e),{success:!1,error:"Authentication failed. Please try again."}}}async function v(o,r){try{console.log("🔐 Creating phone session for user:",o,"phone:",r);let e=null,s=null,t=0;const n=5;for(;t<n&&!e;){t++,console.log(`🔍 Attempt ${t}/${n} to fetch profile for user:`,o);let{data:i,error:c}=await f.from("profiles").select("*").eq("id",o).limit(1);if(!i||i.length===0){console.log("🔍 No profile found by ID, trying by phone:",r);const{data:u,error:p}=await f.from("profiles").select("*").eq("phone",r).eq("role","consumer").order("created_at",{ascending:!1}).limit(1);u&&u.length>0?(i=u,c=null,console.log("✅ Found profile by phone:",i[0].id)):c=p}if(i&&i.length>0){e=i[0],console.log("✅ Profile found:",e),i.length>1&&console.warn("⚠️ Multiple profiles found for user:",o,"count:",i.length);break}else c&&(console.log("❌ Profile fetch error:",c),s=c,t<n&&(console.log("⏳ Waiting 1 second before retry..."),await new Promise(u=>setTimeout(u,1e3))))}if(!e)return console.error("❌ Failed to fetch profile after",n,"attempts. Last error:",s),{success:!1};const a={id:o,phone:r,email:e.email||"",app_metadata:{provider:"phone"},user_metadata:{phone:r,role:e.role,phone_verified:!0},aud:"authenticated",created_at:e.created_at,phone_confirmed_at:e.phone_verified_at||new Date().toISOString()},g={user:a,profile:e,authenticated_at:new Date().toISOString(),expires_at:new Date(Date.now()+365*24*60*60*1e3).toISOString()};return localStorage.setItem("phone_auth_session",JSON.stringify(g)),console.log("✅ Phone authentication session stored in localStorage"),console.log("✅ Returning successful session with user:",a.id,"and profile:",e.id),{success:!0,user:a,profile:e}}catch(e){return console.error("❌ CRITICAL ERROR in createPhoneSession:",e),console.error("❌ Error details:",{message:e.message,stack:e.stack,userId:o,phone:r}),{success:!1}}}async function x(o){try{const r=d(o);if(!r.isValid)return{exists:!1,error:r.error};const{data:e,error:s}=await f.from("profiles").select("id, phone_verified").eq("phone",r.formatted).eq("role","consumer").eq("phone_verified",!0).limit(1);return s?(console.error("Error checking phone existence:",s),{exists:!1,error:"Failed to check phone number"}):{exists:e&&e.length>0}}catch(r){return console.error("Error in checkPhoneExists:",r),{exists:!1,error:"Failed to check phone number"}}}function T(){try{const o=localStorage.getItem("phone_auth_session");if(!o)return null;const r=JSON.parse(o);return new Date(r.expires_at)<new Date?(localStorage.removeItem("phone_auth_session"),null):{user:r.user,profile:r.profile}}catch(o){return console.error("Error retrieving phone session:",o),localStorage.removeItem("phone_auth_session"),null}}function b(){localStorage.removeItem("phone_auth_session")}function E(){l.cleanup()}setInterval(E,5*60*1e3);export{x as checkPhoneExists,E as cleanupExpiredSessions,b as clearStoredPhoneSession,T as getStoredPhoneSession,A as sendPhoneOTP,D as verifyPhoneOTP};
