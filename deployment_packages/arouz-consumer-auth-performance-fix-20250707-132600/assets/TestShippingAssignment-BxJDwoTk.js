import{r as l,j as s,C as p,k as g,Q as m,l as d,B as u}from"./index-zSaKdklO.js";import{g as h,a as f}from"./supplierShippingService-DnlLF32t.js";function j(){const[a,n]=l.useState(null),[i,t]=l.useState(!1),c=async()=>{t(!0);try{console.log("🧪 Testing shipping assignment...");const e=await h();if(console.log("📋 Companies result:",e),e.success&&e.companies&&e.companies.length>0){const r=e.companies[0];console.log("🚚 Using first company:",r);const o=await f("22be00c5-6bc4-4f7d-a68b-09b7c57413fa",r.id);console.log("✅ Assignment result:",o),n(o)}else n({success:!1,error:"No shipping companies available"})}catch(e){console.error("❌ Test error:",e),n({success:!1,error:e.message})}finally{t(!1)}};return s.jsx("div",{className:"p-8",children:s.jsxs(p,{children:[s.jsx(g,{children:s.jsx(m,{children:"Test Shipping Assignment"})}),s.jsxs(d,{className:"space-y-4",children:[s.jsx(u,{onClick:c,disabled:i,className:"bg-[#fa7b00] hover:bg-[#e56b00]",children:i?"Testing...":"Test Assignment"}),a&&s.jsxs("div",{className:"mt-4 p-4 border rounded-lg",children:[s.jsx("h3",{className:"font-semibold mb-2",children:"Result:"}),s.jsx("pre",{className:"text-sm bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(a,null,2)})]})]})]})})}export{j as default};
