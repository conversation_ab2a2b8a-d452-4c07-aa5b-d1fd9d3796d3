import{a1 as i}from"./index-zSaKdklO.js";const f=async()=>{try{console.log("🚚 [SUPPLIER_SHIPPING] Fetching active shipping companies...");const{data:e,error:s}=await i.from("shipping_companies").select("*").eq("is_active",!0).order("company_name");return s?(console.error("❌ [SUPPLIER_SHIPPING] Error fetching shipping companies:",s),{success:!1,error:"Failed to fetch shipping companies"}):(console.log("✅ [SUPPLIER_SHIPPING] Successfully fetched shipping companies:",e?.length),{success:!0,companies:e||[]})}catch(e){return console.error("❌ [SUPPLIER_SHIPPING] Exception in getShippingCompaniesForSupplier:",e),{success:!1,error:"Failed to fetch shipping companies"}}},S=async(e,s)=>{try{console.log("🚚 [SUPPLIER_SHIPPING] Assigning order to shipping company...",{orderId:e,shippingCompanyId:s});const{data:{user:n},error:c}=await i.auth.getUser();if(c||!n)return console.error("❌ [SUPPLIER_SHIPPING] Authentication error:",c),{success:!1,error:"Authentication required"};const{data:u,error:l}=await i.from("order_items").select("supplier_account_id").eq("order_id",e).eq("supplier_account_id",n.id);if(l)return console.error("❌ [SUPPLIER_SHIPPING] Error checking order ownership:",l),{success:!1,error:"Failed to verify order ownership"};if(!u||u.length===0)return console.error("❌ [SUPPLIER_SHIPPING] Order does not belong to current supplier"),{success:!1,error:"You can only assign orders containing your products"};const{data:r,error:d}=await i.from("orders").select(`
        *,
        order_items (
          id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          supplier_account_id
        )
      `).eq("id",e).single();if(d||!r)return console.error("❌ [SUPPLIER_SHIPPING] Error fetching order:",d),{success:!1,error:"Order not found"};if(r.status!=="confirmed")return{success:!1,error:"Only confirmed orders can be assigned to shipping companies"};const{data:p,error:_}=await i.from("shipping_companies").select("*").eq("id",s).single();if(_||!p)return console.error("❌ [SUPPLIER_SHIPPING] Error fetching shipping company:",_),{success:!1,error:"Shipping company not found"};const{data:a}=await i.from("shipments").select("id, shipping_company_id, company_name").eq("order_id",e).single();if(a&&a.shipping_company_id)return{success:!1,error:`Order already assigned to ${a.company_name}`};const h=r.order_items.filter(o=>o.supplier_account_id===n.id),P={suppliers:h.map(o=>({name:o.supplier_name,phone:o.supplier_phone,city:o.supplier_city,wilaya:o.supplier_wilaya}))},m=h.map(o=>o.id);if(console.log("🔍 [SUPPLIER_SHIPPING] Order item IDs for shipment:",{supplierItemsCount:h.length,orderItemIds:m,supplierContactInfo:P}),m.length===0)return console.error("❌ [SUPPLIER_SHIPPING] No order items found for this supplier"),{success:!1,error:"No order items found for this supplier"};let g;if(a){const{data:o,error:t}=await i.from("shipments").update({shipping_company_id:s,company_name:p.company_name,status:"assigned",assigned_by:"supplier",delivery_cost:500,estimated_delivery_date:new Date(Date.now()+1728e5).toISOString().split("T")[0],supplier_contact_info:P,order_item_ids:m,assigned_at:new Date().toISOString()}).eq("id",a.id).select().single();if(t)return console.error("❌ [SUPPLIER_SHIPPING] Error updating shipment:",t),{success:!1,error:"Failed to assign shipping company"};g=o}else{const{data:o,error:t}=await i.from("shipments").insert({order_id:e,shipping_company_id:s,company_name:p.company_name,status:"assigned",assigned_by:"supplier",delivery_cost:500,estimated_delivery_date:new Date(Date.now()+1728e5).toISOString().split("T")[0],supplier_contact_info:P,order_item_ids:m,assigned_at:new Date().toISOString()}).select().single();if(t)return console.error("❌ [SUPPLIER_SHIPPING] Error creating shipment:",{error:t,errorCode:t.code,errorMessage:t.message,errorDetails:t.details,orderItemIds:m,supplierContactInfo:P}),{success:!1,error:`Failed to create shipment record: ${t.message}`};g=o}return console.log("✅ [SUPPLIER_SHIPPING] Successfully assigned order to shipping company:",{orderId:e,shippingCompanyId:s,shipmentId:g.id}),{success:!0,shipment:g}}catch(n){return console.error("❌ [SUPPLIER_SHIPPING] Exception in assignSupplierOrderToShipping:",n),{success:!1,error:"Failed to assign order to shipping company"}}},y=async e=>{try{console.log("📦 [SUPPLIER_SHIPPING] Getting supplier shipments with filters:",e);const{data:{user:s},error:n}=await i.auth.getUser();if(n||!s)return console.error("❌ [SUPPLIER_SHIPPING] Authentication error:",n),{success:!1,error:"Authentication required"};const{data:c,error:u}=await i.from("order_items").select("order_id").eq("supplier_account_id",s.id);if(u)return console.error("❌ [SUPPLIER_SHIPPING] Error fetching supplier order IDs:",u),{success:!1,error:"Failed to fetch shipments"};if(!c||c.length===0)return console.log("📦 [SUPPLIER_SHIPPING] No orders found for supplier, returning empty results"),{success:!0,shipments:[],total_count:0};const l=c.map(a=>a.order_id);console.log("📦 [SUPPLIER_SHIPPING] Found order IDs for supplier:",l);let r=i.from("shipments").select(`
        *,
        orders (
          id,
          order_number,
          status,
          created_at
        )
      `,{count:"exact"}).in("order_id",l);e?.status&&(r=r.eq("status",e.status)),e?.date_from&&(r=r.gte("created_at",e.date_from)),e?.date_to&&(r=r.lte("created_at",e.date_to)),e?.search&&(r=r.or(`orders.order_number.ilike.%${e.search}%,company_name.ilike.%${e.search}%,tracking_number.ilike.%${e.search}%`)),e?.limit&&(r=r.limit(e.limit)),e?.offset&&(r=r.range(e.offset,e.offset+(e.limit||10)-1)),r=r.order("created_at",{ascending:!1});const{data:d,error:p,count:_}=await r;return p?(console.error("❌ [SUPPLIER_SHIPPING] Database error:",p),{success:!1,error:"Failed to fetch shipments"}):(console.log("✅ [SUPPLIER_SHIPPING] Successfully fetched shipments:",d?.length),{success:!0,shipments:d||[],total_count:_||0})}catch(s){return console.error("❌ [SUPPLIER_SHIPPING] Exception in getSupplierShipments:",s),{success:!1,error:"Failed to fetch shipments"}}};export{S as a,y as b,f as g};
