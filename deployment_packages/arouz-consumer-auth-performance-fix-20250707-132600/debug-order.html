<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Order Creation - AROUZ MARKET</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-title {
            color: #fa7b00;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .test-button {
            background: #fa7b00;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #e56a00;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 600px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #fa7b00;
            background: #fff3e0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-title">🔧 Order Creation Debug Tool</div>
        
        <p>This tool will help us identify exactly what's preventing order creation.</p>
        
        <button class="test-button" onclick="testOrderCreation()">🧪 Test Order Creation</button>
        <button class="test-button" onclick="testAuthentication()">🔐 Test Authentication</button>
        <button class="test-button" onclick="clearOutput()">🗑️ Clear Output</button>
        
        <div id="debugOutput" class="debug-output">
            Click "Test Order Creation" to start debugging...
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlyand3emNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE2NzQ4NzQsImV4cCI6MjA0NzI1MDg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
        const supabase = supabase.createClient(supabaseUrl, supabaseKey);

        function log(message, type = 'info') {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('debugOutput').innerHTML = 'Output cleared...\n';
        }

        async function testAuthentication() {
            log('🔐 Testing Authentication...', 'info');
            
            try {
                // Get current session
                const { data: session, error: sessionError } = await supabase.auth.getSession();
                
                if (sessionError) {
                    log(`❌ Session Error: ${sessionError.message}`, 'error');
                    return;
                }
                
                if (!session.session) {
                    log('❌ No active session found', 'error');
                    log('🔄 Attempting to sign in anonymously...', 'info');
                    
                    // Try to sign in anonymously for testing
                    const { data: signInData, error: signInError } = await supabase.auth.signInAnonymously();
                    
                    if (signInError) {
                        log(`❌ Anonymous sign-in failed: ${signInError.message}`, 'error');
                        return;
                    }
                    
                    log('✅ Anonymous sign-in successful', 'success');
                    log(`User ID: ${signInData.user.id}`, 'info');
                } else {
                    log('✅ Active session found', 'success');
                    log(`User ID: ${session.session.user.id}`, 'info');
                    log(`User Role: ${session.session.user.role || 'No role'}`, 'info');
                }
                
            } catch (error) {
                log(`❌ Authentication test failed: ${error.message}`, 'error');
            }
        }

        async function testOrderCreation() {
            log('🧪 Starting Order Creation Debug Test...', 'info');
            
            // First test authentication
            await testAuthentication();
            
            // Test data
            const testData = {
                consumer_phone: '+213555123456',
                consumer_name: 'Test User',
                delivery_address: '123 Test Street, Test City',
                delivery_wilaya: 'Alger',
                delivery_commune: 'Alger Centre',
                cart_items: [
                    {
                        product_id: 'TYR-TEST001',
                        product_name: 'Test Tyre',
                        quantity: 2,
                        unit_price: 250.00,
                        total_price: 500.00,
                        supplier_account_id: '********-0000-0000-0000-********0000',
                        supplier_name: 'Test Supplier'
                    }
                ]
            };
            
            log('📋 Test Data:', 'info');
            log(JSON.stringify(testData, null, 2), 'info');
            
            try {
                log('🔄 Calling debug_order_creation function...', 'info');
                
                const { data, error } = await supabase.rpc('debug_order_creation', {
                    p_consumer_phone: testData.consumer_phone,
                    p_consumer_name: testData.consumer_name,
                    p_delivery_address: testData.delivery_address,
                    p_delivery_wilaya: testData.delivery_wilaya,
                    p_delivery_commune: testData.delivery_commune,
                    p_cart_items: testData.cart_items
                });
                
                if (error) {
                    log(`❌ RPC Error: ${error.message}`, 'error');
                    log(`Error Details: ${JSON.stringify(error, null, 2)}`, 'error');
                    return;
                }
                
                log('📊 Debug Results:', 'success');
                
                // Parse and display results step by step
                if (data) {
                    Object.keys(data).forEach(key => {
                        if (key.startsWith('step_')) {
                            const step = data[key];
                            const stepNum = key.replace('step_', '');
                            log(`\n🔸 Step ${stepNum}: ${step.step}`, 'info');
                            
                            if (step.success === false) {
                                log(`  ❌ FAILED: ${step.error_message}`, 'error');
                                log(`  Error Code: ${step.error_code}`, 'error');
                            } else if (step.success === true) {
                                log(`  ✅ SUCCESS`, 'success');
                                if (step.order_id) log(`  Order ID: ${step.order_id}`, 'info');
                                if (step.items_count) log(`  Items Count: ${step.items_count}`, 'info');
                            }
                            
                            // Show auth details for auth step
                            if (step.step === 'auth_check') {
                                log(`  Auth UID: ${step.auth_uid || 'null'}`, 'info');
                                log(`  Auth Role: ${step.auth_role || 'null'}`, 'info');
                                log(`  Current User: ${step.current_user || 'null'}`, 'info');
                            }
                        } else if (key === 'final_result') {
                            const result = data[key];
                            log(`\n🎯 Final Result:`, 'info');
                            if (result.success) {
                                log(`  ✅ ${result.message}`, 'success');
                                log(`  Order ID: ${result.order_id}`, 'success');
                            } else {
                                log(`  ❌ ${result.message}`, 'error');
                            }
                        }
                    });
                } else {
                    log('❌ No data returned from debug function', 'error');
                }
                
            } catch (error) {
                log(`❌ Unexpected error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        // Auto-run authentication test on page load
        window.addEventListener('load', () => {
            log('🚀 Debug tool loaded. Ready for testing!', 'info');
        });
    </script>
</body>
</html>
