# AROUZ MARKET - Production Security & SPA Routing Configuration
# This file ensures all routes are handled by React Router with security hardening

# Enable URL rewriting
RewriteEngine On

# Security Headers - Production Grade
<IfModule mod_headers.c>
    # HTTPS Enforcement
    Header always set Strict-Transport-Security "max-age=********; includeSubDomains; preload"

    # Content Security Policy (CSP) - TEMPORARILY DISABLED FOR DEBUGGING
    # Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://maps.googleapis.com https://www.google.com https://www.gstatic.com https://cdn.optanon.co https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://maps.googleapis.com https://api.dexatel.com; frame-src 'self' https://www.google.com; object-src 'none'; base-uri 'self'; form-action 'self';"

    # XSS Protection
    Header always set X-XSS-Protection "1; mode=block"

    # Content Type Options
    Header always set X-Content-Type-Options "nosniff"

    # Frame Options
    Header always set X-Frame-Options "SAMEORIGIN"

    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(self), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()"

    # Remove Server Information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Force HTTPS Redirect
<IfModule mod_rewrite.c>
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Handle Angular and React Router
# If the requested resource doesn't exist as a file or directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Redirect everything to index.html
RewriteRule . /index.html [L]

# Optional: Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Optional: Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
