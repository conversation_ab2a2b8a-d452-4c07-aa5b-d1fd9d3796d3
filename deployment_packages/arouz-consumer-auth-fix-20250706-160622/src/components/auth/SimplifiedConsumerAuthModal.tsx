/**
 * Simplified Consumer Authentication Modal
 * 
 * This modal provides a simplified consumer registration flow:
 * - No SMS verification required
 * - Direct authentication with name + phone
 * - Zero friction registration
 * - Maintains compatibility with existing features
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Loader2, 
  Phone, 
  User,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { 
  authenticateConsumerSimplified, 
  validateAlgerianPhone,
  SimplifiedAuthRequest 
} from '@/services/simplifiedConsumerAuth';

interface SimplifiedConsumerAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (user: any, action: 'login' | 'signup') => void;
  title?: string;
  description?: string;
}

export function SimplifiedConsumerAuthModal({
  isOpen,
  onClose,
  onSuccess,
  title,
  description
}: SimplifiedConsumerAuthModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // Form state
  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Validation state
  const [nameError, setNameError] = useState('');
  const [phoneError, setPhoneError] = useState('');
  
  // Validate full name
  const validateName = (name: string): boolean => {
    if (!name || name.trim().length < 2) {
      setNameError('Please enter your full name (at least 2 characters)');
      return false;
    }
    setNameError('');
    return true;
  };
  
  // Validate phone number
  const validatePhone = (phoneNumber: string): boolean => {
    const validation = validateAlgerianPhone(phoneNumber);
    if (!validation.isValid) {
      setPhoneError(validation.error || 'Invalid phone number format');
      return false;
    }
    setPhoneError('');
    return true;
  };
  
  // Check if form is valid
  const isFormValid = (): boolean => {
    return fullName.trim().length >= 2 && 
           phone.trim().length > 0 && 
           validateAlgerianPhone(phone).isValid;
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isLoading) return;
    
    // Validate form
    const isNameValid = validateName(fullName);
    const isPhoneValid = validatePhone(phone);
    
    if (!isNameValid || !isPhoneValid) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const request: SimplifiedAuthRequest = {
        fullName: fullName.trim(),
        phone: phone.trim()
      };
      
      console.log('🚀 Submitting simplified consumer authentication:', request);
      
      const result = await authenticateConsumerSimplified(request);
      
      if (result.success) {
        console.log('✅ Simplified authentication successful:', result);
        
        toast({
          title: result.action === 'signup' ? "Account Created!" : "Welcome Back!",
          description: result.action === 'signup' 
            ? "Your account has been created successfully!"
            : "You have been logged in successfully!",
        });
        
        // Trigger auth success event
        window.dispatchEvent(new CustomEvent('auth:success', {
          detail: { user: result.user, action: result.action }
        }));
        
        // Call success callback
        onSuccess?.(result.user!, result.action!);
        
        // Close modal
        onClose();
        
      } else {
        console.error('❌ Simplified authentication failed:', result.error);
        
        toast({
          title: "Authentication Failed",
          description: result.error || "Please try again.",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      console.error('❌ Error in simplified authentication:', error);
      
      toast({
        title: "Authentication Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Reset form when modal closes
  const handleClose = () => {
    setFullName('');
    setPhone('');
    setNameError('');
    setPhoneError('');
    setIsLoading(false);
    onClose();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5 text-[#fa7b00]" />
            <span>{title || "Consumer Registration"}</span>
          </DialogTitle>
          <DialogDescription>
            {description || "Enter your details to continue with your order"}
          </DialogDescription>
        </DialogHeader>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Full Name Input */}
            <div className="space-y-2">
              <Label htmlFor="fullName" className="text-sm font-medium">
                Full Name <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="fullName"
                  type="text"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChange={(e) => {
                    setFullName(e.target.value);
                    if (nameError) validateName(e.target.value);
                  }}
                  onBlur={() => validateName(fullName)}
                  className={cn(
                    "pl-10",
                    nameError ? "border-red-500 focus:border-red-500" : ""
                  )}
                  disabled={isLoading}
                />
              </div>
              {nameError && (
                <p className="text-sm text-red-500 flex items-center space-x-1">
                  <AlertCircle className="h-3 w-3" />
                  <span>{nameError}</span>
                </p>
              )}
            </div>
            
            {/* Phone Number Input */}
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium">
                Phone Number <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  type="tel"
                  placeholder="05XXXXXXXX"
                  value={phone}
                  onChange={(e) => {
                    setPhone(e.target.value);
                    if (phoneError) validatePhone(e.target.value);
                  }}
                  onBlur={() => validatePhone(phone)}
                  className={cn(
                    "pl-10",
                    phoneError ? "border-red-500 focus:border-red-500" : ""
                  )}
                  disabled={isLoading}
                />
              </div>
              {phoneError && (
                <p className="text-sm text-red-500 flex items-center space-x-1">
                  <AlertCircle className="h-3 w-3" />
                  <span>{phoneError}</span>
                </p>
              )}
              <p className="text-xs text-gray-500">
                Enter your 9-digit Algerian mobile number
              </p>
            </div>
            
            {/* Security Features */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <h4 className="text-sm font-medium text-green-800 mb-2 flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                Instant Registration
              </h4>
              <ul className="text-xs text-green-700 space-y-1">
                <li>• No verification codes required</li>
                <li>• Immediate access to your account</li>
                <li>• Secure and privacy-protected</li>
              </ul>
            </div>
            
            {/* Submit Button */}
            <Button
              type="submit"
              disabled={!isFormValid() || isLoading}
              className="w-full bg-gradient-to-r from-[#fa7b00] to-[#ff9500] hover:from-[#e56f00] hover:to-[#e58500] text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <User className="h-4 w-4 mr-2" />
                  Continue
                </>
              )}
            </Button>
          </form>
          
          {/* Terms and Privacy */}
          <p className="text-xs text-gray-500 text-center">
            By continuing, you agree to our{' '}
            <a href="/terms" className="text-[#fa7b00] hover:underline">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="text-[#fa7b00] hover:underline">
              Privacy Policy
            </a>
          </p>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
