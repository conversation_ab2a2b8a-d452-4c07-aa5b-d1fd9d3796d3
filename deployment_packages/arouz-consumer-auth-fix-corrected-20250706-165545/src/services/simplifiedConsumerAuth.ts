/**
 * Simplified Consumer Authentication Service
 * 
 * This service provides a simplified consumer registration flow that:
 * 1. Removes SMS verification requirement
 * 2. Provides direct authentication with name + phone
 * 3. Maintains compatibility with existing consumer features
 * 4. Ensures proper data integrity
 */

import { supabase } from '@/services/authService';

export interface SimplifiedAuthRequest {
  fullName: string;
  phone: string;
}

export interface SimplifiedAuthResponse {
  success: boolean;
  user?: any;
  profile?: any;
  action?: 'login' | 'signup';
  error?: string;
}

/**
 * Validate Algerian phone number format
 */
export function validateAlgerianPhone(phone: string): { isValid: boolean; formatted?: string; error?: string } {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if it's a valid Algerian number
  if (cleanPhone.length === 9 && cleanPhone.startsWith('5')) {
    // Format: +213XXXXXXXXX
    return {
      isValid: true,
      formatted: `+213${cleanPhone}`
    };
  } else if (cleanPhone.length === 10 && cleanPhone.startsWith('05')) {
    // Format: 05XXXXXXXX -> +213XXXXXXXXX
    return {
      isValid: true,
      formatted: `+213${cleanPhone.substring(1)}`
    };
  } else if (cleanPhone.length === 12 && cleanPhone.startsWith('213')) {
    // Format: 213XXXXXXXXX -> +213XXXXXXXXX
    return {
      isValid: true,
      formatted: `+${cleanPhone}`
    };
  } else if (cleanPhone.length === 13 && cleanPhone.startsWith('2135')) {
    // Format: +213XXXXXXXXX (already formatted)
    return {
      isValid: true,
      formatted: `+${cleanPhone}`
    };
  }
  
  return {
    isValid: false,
    error: 'Please enter a valid Algerian phone number (e.g., 05XXXXXXXX)'
  };
}

/**
 * Convert full name to privacy-protected display name
 */
export function convertToPrivacyProtectedName(fullName: string): { displayName: string; fullName: string } {
  const trimmedName = fullName.trim();
  
  if (!trimmedName) {
    return { displayName: '', fullName: '' };
  }
  
  const nameParts = trimmedName.split(' ').filter(part => part.length > 0);
  
  if (nameParts.length === 1) {
    // Single name: show first 3 characters + "..."
    const name = nameParts[0];
    const displayName = name.length > 3 ? `${name.substring(0, 3)}...` : name;
    return { displayName, fullName: trimmedName };
  } else {
    // Multiple names: show first name + last initial
    const firstName = nameParts[0];
    const lastInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();
    const displayName = `${firstName} ${lastInitial}`;
    return { displayName, fullName: trimmedName };
  }
}

/**
 * Simplified consumer authentication - no SMS verification required
 */
export async function authenticateConsumerSimplified(request: SimplifiedAuthRequest): Promise<SimplifiedAuthResponse> {
  try {
    console.log('🚀 Starting simplified consumer authentication for:', request.phone);
    
    // Validate phone number
    const phoneValidation = validateAlgerianPhone(request.phone);
    if (!phoneValidation.isValid) {
      return {
        success: false,
        error: phoneValidation.error || 'Invalid phone number format'
      };
    }
    
    const formattedPhone = phoneValidation.formatted!;
    
    // Validate full name
    if (!request.fullName || request.fullName.trim().length < 2) {
      return {
        success: false,
        error: 'Please enter your full name (at least 2 characters)'
      };
    }
    
    // Check if consumer already exists
    console.log('🔍 Checking if consumer exists for phone:', formattedPhone);
    const { data: existingProfiles, error: checkError } = await supabase
      .from('profiles')
      .select('id, full_name, phone_verified, created_at')
      .eq('phone', formattedPhone)
      .eq('role', 'consumer')
      .limit(1);
    
    if (checkError) {
      console.error('❌ Error checking existing consumer:', checkError);
      return {
        success: false,
        error: 'Failed to verify account. Please try again.'
      };
    }
    
    let userId: string;
    let action: 'login' | 'signup';
    
    if (existingProfiles && existingProfiles.length > 0) {
      // EXISTING CONSUMER: Update and login
      const existingProfile = existingProfiles[0];
      userId = existingProfile.id;
      action = 'login';
      
      console.log('✅ Found existing consumer:', userId);
      
      // Update consumer profile with latest info
      const { displayName, fullName } = convertToPrivacyProtectedName(request.fullName.trim());
      
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: displayName,
          original_full_name: fullName,
          phone_verified: true,
          phone_verified_at: new Date().toISOString(),
          last_login: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
      
      if (updateError) {
        console.error('❌ Error updating consumer profile:', updateError);
        // Continue anyway - login is more important than update
      }
      
    } else {
      // NEW CONSUMER: Create account
      userId = crypto.randomUUID();
      action = 'signup';
      
      console.log('🆕 Creating new consumer:', userId);
      
      const { displayName, fullName } = convertToPrivacyProtectedName(request.fullName.trim());
      
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          phone: formattedPhone,
          role: 'consumer',
          full_name: displayName,
          original_full_name: fullName,
          phone_verified: true,
          phone_verification_method: 'simplified_auth',
          phone_verified_at: new Date().toISOString(),
          last_login: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      if (insertError) {
        console.error('❌ Error creating consumer profile:', insertError);
        return {
          success: false,
          error: 'Failed to create account. Please try again.'
        };
      }
    }
    
    // Fetch the complete profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (profileError || !profile) {
      console.error('❌ Error fetching consumer profile:', profileError);
      return {
        success: false,
        error: 'Authentication failed. Please try again.'
      };
    }
    
    // Create phone session for consumer auth
    const sessionResult = await createSimplifiedPhoneSession(userId, formattedPhone, profile);
    
    if (!sessionResult.success) {
      return {
        success: false,
        error: 'Failed to create session. Please try again.'
      };
    }
    
    console.log(`✅ Simplified consumer authentication ${action} successful for:`, userId);
    
    return {
      success: true,
      user: sessionResult.user,
      profile: sessionResult.profile,
      action
    };
    
  } catch (error) {
    console.error('❌ Error in simplified consumer authentication:', error);
    return {
      success: false,
      error: 'Authentication failed. Please try again.'
    };
  }
}

/**
 * Create simplified phone session (stores in localStorage)
 */
async function createSimplifiedPhoneSession(userId: string, phone: string, profile: any): Promise<{ success: boolean; user?: any; profile?: any }> {
  try {
    console.log('🔐 Creating simplified phone session for user:', userId);
    
    // Create user object for session
    const user = {
      id: userId,
      phone: phone,
      role: 'consumer',
      aud: 'authenticated',
      created_at: profile.created_at,
      updated_at: profile.updated_at
    };
    
    // Store session in localStorage
    const sessionData = {
      user,
      profile,
      expires_at: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
      created_at: Date.now()
    };
    
    localStorage.setItem('phone_auth_session', JSON.stringify(sessionData));
    
    console.log('✅ Simplified phone session created successfully');
    
    return {
      success: true,
      user,
      profile
    };
    
  } catch (error) {
    console.error('❌ Error creating simplified phone session:', error);
    return { success: false };
  }
}

/**
 * Check if consumer is authenticated (simplified)
 */
export function isConsumerAuthenticated(): boolean {
  try {
    const phoneSession = localStorage.getItem('phone_auth_session');
    if (!phoneSession) return false;
    
    const session = JSON.parse(phoneSession);
    
    // Check if session is expired
    if (session.expires_at && Date.now() > session.expires_at) {
      localStorage.removeItem('phone_auth_session');
      return false;
    }
    
    // Check if it's a consumer session
    return session.profile?.role === 'consumer';
    
  } catch (error) {
    console.error('Error checking consumer authentication:', error);
    return false;
  }
}

/**
 * Get current consumer session
 */
export function getCurrentConsumerSession(): { user: any; profile: any } | null {
  try {
    const phoneSession = localStorage.getItem('phone_auth_session');
    if (!phoneSession) return null;
    
    const session = JSON.parse(phoneSession);
    
    // Check if session is expired
    if (session.expires_at && Date.now() > session.expires_at) {
      localStorage.removeItem('phone_auth_session');
      return null;
    }
    
    // Check if it's a consumer session
    if (session.profile?.role === 'consumer') {
      return {
        user: session.user,
        profile: session.profile
      };
    }
    
    return null;
    
  } catch (error) {
    console.error('Error getting consumer session:', error);
    return null;
  }
}

/**
 * Logout consumer (simplified)
 */
export function logoutConsumer(): void {
  try {
    localStorage.removeItem('phone_auth_session');
    console.log('✅ Consumer logged out successfully');
  } catch (error) {
    console.error('Error logging out consumer:', error);
  }
}
