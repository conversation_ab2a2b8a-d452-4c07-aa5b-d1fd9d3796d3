import{c as le,u as J,r as v,j as n,D as je,d as Ne,e as Ce,f as Se,g as Oe,i as _e,B as N,I as q,ar as X,ay as I,aZ as ie,b$ as Me,m as se,T as de,bb as Ve,aa as pe,G as Y,K as Re,cZ as Fe,L as ee,h as Be,ag as Le,q as Ue}from"./index-BqaWQJEu.js";import{A as ze}from"./AdminLayout-DWILQ77Q.js";import{T as He,a as We,b as te,c as T,d as Qe,e as S}from"./table-Dnlym_GZ.js";import{generateProductId as Ge}from"./idGenerator-CHl7dMtk.js";import{g as re}from"./categoryMapping-BSm9D6op.js";import{P as qe,a as $e,I as ge}from"./ProductFormSelector-ilRmcQXU.js";import{I as Ke,a as Ze,L as Xe,A as L,N as ve,C as xe,F as ye,D as ke,H as Ye,b as Je,c as et,M as tt,d as rt,e as nt,f as at,Q as st,S as it}from"./ProductFormDialog-i4pxggHj.js";import{C as Ie}from"./ProductEditDialog-DSpAX0ft.js";import{S as ot,a as ct,b as lt,c as dt,d as ut}from"./select-B4ywZa9P.js";import{U as ue}from"./upload-BR-EFs7e.js";import{T as be}from"./tag-CMPMuWWd.js";import{C as mt}from"./copy-DcoA3jnA.js";import"./house-Bq7RaxAv.js";import"./settings-PBinnTtm.js";import"./categories-k_ueMvEL.js";import"./progress-DKc-eGjJ.js";import"./enhanced-dropdown-BnfdB4dD.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ne=le("Barcode",[["path",{d:"M3 5v14",key:"1nt18q"}],["path",{d:"M8 5v14",key:"1ybrkv"}],["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"M17 5v14",key:"ycjyhj"}],["path",{d:"M21 5v14",key:"nzette"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=le("PencilLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}],["path",{d:"m15 5 3 3",key:"1w25hb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ft=le("Scan",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]]);var pt=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),gt=function(a){pt(t,a);function t(e){var r=a.call(this,e.width,e.height)||this;return r.canvas=e,r.tempCanvasElement=null,r.buffer=t.makeBufferFromCanvasImageData(e),r}return t.makeBufferFromCanvasImageData=function(e){var r=e.getContext("2d");if(!r)throw new Error("Couldn't get canvas context.");var s=r.getImageData(0,0,e.width,e.height);return t.toGrayscaleBuffer(s.data,e.width,e.height)},t.toGrayscaleBuffer=function(e,r,s){for(var i=new Uint8ClampedArray(r*s),c=0,u=0,m=e.length;c<m;c+=4,u++){var l=void 0,d=e[c+3];if(d===0)l=255;else{var f=e[c],p=e[c+1],y=e[c+2];l=306*f+601*p+117*y+512>>10}i[u]=l}return i},t.prototype.getRow=function(e,r){if(e<0||e>=this.getHeight())throw new Ke("Requested row is outside the image: "+e);var s=this.getWidth(),i=e*s;return r===null?r=this.buffer.slice(i,i+s):(r.length<s&&(r=new Uint8ClampedArray(s)),r.set(this.buffer.slice(i,i+s))),r},t.prototype.getMatrix=function(){return this.buffer},t.prototype.isCropSupported=function(){return!0},t.prototype.crop=function(e,r,s,i){return a.prototype.crop.call(this,e,r,s,i),this},t.prototype.isRotateSupported=function(){return!0},t.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},t.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},t.prototype.invert=function(){return new Ze(this)},t.prototype.getTempCanvasElement=function(){if(this.tempCanvasElement===null){var e=this.canvas.ownerDocument.createElement("canvas");e.width=this.canvas.width,e.height=this.canvas.height,this.tempCanvasElement=e}return this.tempCanvasElement},t.prototype.rotate=function(e){var r=this.getTempCanvasElement();if(!r)throw new Error("Could not create a Canvas element.");var s=e*t.DEGREE_TO_RADIANS,i=this.canvas.width,c=this.canvas.height,u=Math.ceil(Math.abs(Math.cos(s))*i+Math.abs(Math.sin(s))*c),m=Math.ceil(Math.abs(Math.sin(s))*i+Math.abs(Math.cos(s))*c);r.width=u,r.height=m;var l=r.getContext("2d");if(!l)throw new Error("Could not create a Canvas Context element.");return l.translate(u/2,m/2),l.rotate(s),l.drawImage(this.canvas,i/-2,c/-2),this.buffer=t.makeBufferFromCanvasImageData(r),this},t.DEGREE_TO_RADIANS=Math.PI/180,t}(Xe);function Te(){return typeof navigator<"u"}function vt(){return Te()&&!!navigator.mediaDevices}function xt(){return!!(vt()&&navigator.mediaDevices.enumerateDevices)}var K=function(){return K=Object.assign||function(a){for(var t,e=1,r=arguments.length;e<r;e++){t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(a[s]=t[s])}return a},K.apply(this,arguments)},b=function(a,t,e,r){function s(i){return i instanceof e?i:new e(function(c){c(i)})}return new(e||(e=Promise))(function(i,c){function u(d){try{l(r.next(d))}catch(f){c(f)}}function m(d){try{l(r.throw(d))}catch(f){c(f)}}function l(d){d.done?i(d.value):s(d.value).then(u,m)}l((r=r.apply(a,t||[])).next())})},w=function(a,t){var e={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,s,i,c;return c={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(c[Symbol.iterator]=function(){return this}),c;function u(l){return function(d){return m([l,d])}}function m(l){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,s&&(i=l[0]&2?s.return:l[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,l[1])).done)return i;switch(s=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return e.label++,{value:l[1],done:!1};case 5:e.label++,s=l[1],l=[0];continue;case 7:l=e.ops.pop(),e.trys.pop();continue;default:if(i=e.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){e=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){e.label=l[1];break}if(l[0]===6&&e.label<i[1]){e.label=i[1],i=l;break}if(i&&e.label<i[2]){e.label=i[2],e.ops.push(l);break}i[2]&&e.ops.pop(),e.trys.pop();continue}l=t.call(a,e)}catch(d){l=[6,d],s=0}finally{r=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}},ce=function(a){var t=typeof Symbol=="function"&&Symbol.iterator,e=t&&a[t],r=0;if(e)return e.call(a);if(a&&typeof a.length=="number")return{next:function(){return a&&r>=a.length&&(a=void 0),{value:a&&a[r++],done:!a}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},yt={delayBetweenScanAttempts:500,delayBetweenScanSuccess:500,tryPlayVideoTimeout:5e3},Z=function(){function a(t,e,r){e===void 0&&(e=new Map),r===void 0&&(r={}),this.reader=t,this.hints=e,this.options=K(K({},yt),r)}return Object.defineProperty(a.prototype,"possibleFormats",{set:function(t){this.hints.set(ke.POSSIBLE_FORMATS,t)},enumerable:!1,configurable:!0}),a.addVideoSource=function(t,e){try{t.srcObject=e}catch{console.error("got interrupted by new loading request")}},a.mediaStreamSetTorch=function(t,e){return b(this,void 0,void 0,function(){return w(this,function(r){switch(r.label){case 0:return[4,t.applyConstraints({advanced:[{fillLightMode:e?"flash":"off",torch:!!e}]})];case 1:return r.sent(),[2]}})})},a.mediaStreamIsTorchCompatible=function(t){var e,r,s=t.getVideoTracks();try{for(var i=ce(s),c=i.next();!c.done;c=i.next()){var u=c.value;if(a.mediaStreamIsTorchCompatibleTrack(u))return!0}}catch(m){e={error:m}}finally{try{c&&!c.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return!1},a.mediaStreamIsTorchCompatibleTrack=function(t){try{var e=t.getCapabilities();return"torch"in e}catch(r){return console.error(r),console.warn("Your browser may be not fully compatible with WebRTC and/or ImageCapture specs. Torch will not be available."),!1}},a.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&t.readyState>2},a.getMediaElement=function(t,e){var r=document.getElementById(t);if(!r)throw new L("element with id '".concat(t,"' not found"));if(r.nodeName.toLowerCase()!==e.toLowerCase())throw new L("element with id '".concat(t,"' must be an ").concat(e," element"));return r},a.createVideoElement=function(t){if(t instanceof HTMLVideoElement)return t;if(typeof t=="string")return a.getMediaElement(t,"video");if(!t&&typeof document<"u"){var e=document.createElement("video");return e.width=200,e.height=200,e}throw new Error("Couldn't get videoElement from videoSource!")},a.prepareImageElement=function(t){if(t instanceof HTMLImageElement)return t;if(typeof t=="string")return a.getMediaElement(t,"img");if(typeof t>"u"){var e=document.createElement("img");return e.width=200,e.height=200,e}throw new Error("Couldn't get imageElement from imageSource!")},a.prepareVideoElement=function(t){var e=a.createVideoElement(t);return e.setAttribute("autoplay","true"),e.setAttribute("muted","true"),e.setAttribute("playsinline","true"),e},a.isImageLoaded=function(t){return!(!t.complete||t.naturalWidth===0)},a.createBinaryBitmapFromCanvas=function(t){var e=new gt(t),r=new Ye(e);return new Je(r)},a.drawImageOnCanvas=function(t,e){t.drawImage(e,0,0)},a.getMediaElementDimensions=function(t){if(t instanceof HTMLVideoElement)return{height:t.videoHeight,width:t.videoWidth};if(t instanceof HTMLImageElement)return{height:t.naturalHeight||t.height,width:t.naturalWidth||t.width};throw new Error("Couldn't find the Source's dimensions!")},a.createCaptureCanvas=function(t){if(!t)throw new L("Cannot create a capture canvas without a media element.");if(typeof document>"u")throw new Error(`The page "Document" is undefined, make sure you're running in a browser.`);var e=document.createElement("canvas"),r=a.getMediaElementDimensions(t),s=r.width,i=r.height;return e.style.width=s+"px",e.style.height=i+"px",e.width=s,e.height=i,e},a.tryPlayVideo=function(t){return b(this,void 0,void 0,function(){var e;return w(this,function(r){switch(r.label){case 0:if(t?.ended)return console.error("Trying to play video that has ended."),[2,!1];if(a.isVideoPlaying(t))return console.warn("Trying to play video that is already playing."),[2,!0];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,t.play()];case 2:return r.sent(),[2,!0];case 3:return e=r.sent(),console.warn("It was not possible to play the video.",e),[2,!1];case 4:return[2]}})})},a.createCanvasFromMediaElement=function(t){var e=a.createCaptureCanvas(t),r=e.getContext("2d");if(!r)throw new Error("Couldn't find Canvas 2D Context.");return a.drawImageOnCanvas(r,t),e},a.createBinaryBitmapFromMediaElem=function(t){var e=a.createCanvasFromMediaElement(t);return a.createBinaryBitmapFromCanvas(e)},a.destroyImageElement=function(t){t.src="",t.removeAttribute("src"),t=void 0},a.listVideoInputDevices=function(){return b(this,void 0,void 0,function(){var t,e,r,s,i,c,u,m,l,d,f,p;return w(this,function(y){switch(y.label){case 0:if(!Te())throw new Error("Can't enumerate devices, navigator is not present.");if(!xt())throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:t=y.sent(),e=[];try{for(r=ce(t),s=r.next();!s.done;s=r.next())i=s.value,c=i.kind==="video"?"videoinput":i.kind,c==="videoinput"&&(u=i.deviceId||i.id,m=i.label||"Video device ".concat(e.length+1),l=i.groupId,d={deviceId:u,label:m,kind:c,groupId:l},e.push(d))}catch(D){f={error:D}}finally{try{s&&!s.done&&(p=r.return)&&p.call(r)}finally{if(f)throw f.error}}return[2,e]}})})},a.findDeviceById=function(t){return b(this,void 0,void 0,function(){var e;return w(this,function(r){switch(r.label){case 0:return[4,a.listVideoInputDevices()];case 1:return e=r.sent(),e?[2,e.find(function(s){return s.deviceId===t})]:[2]}})})},a.cleanVideoSource=function(t){if(t){try{t.srcObject=null}catch{t.src=""}t&&t.removeAttribute("src")}},a.releaseAllStreams=function(){a.streamTracker.length!==0&&a.streamTracker.forEach(function(t){t.getTracks().forEach(function(e){return e.stop()})}),a.streamTracker=[]},a.playVideoOnLoadAsync=function(t,e){return b(this,void 0,void 0,function(){var r;return w(this,function(s){switch(s.label){case 0:return[4,a.tryPlayVideo(t)];case 1:return r=s.sent(),r?[2,!0]:[2,new Promise(function(i,c){var u=setTimeout(function(){a.isVideoPlaying(t)||(c(!1),t.removeEventListener("canplay",m))},e),m=function(){a.tryPlayVideo(t).then(function(l){clearTimeout(u),t.removeEventListener("canplay",m),i(l)})};t.addEventListener("canplay",m)})]}})})},a.attachStreamToVideo=function(t,e,r){return r===void 0&&(r=5e3),b(this,void 0,void 0,function(){var s;return w(this,function(i){switch(i.label){case 0:return s=a.prepareVideoElement(e),a.addVideoSource(s,t),[4,a.playVideoOnLoadAsync(s,r)];case 1:return i.sent(),[2,s]}})})},a._waitImageLoad=function(t){return new Promise(function(e,r){var s=1e4,i=setTimeout(function(){a.isImageLoaded(t)||(t.removeEventListener("load",c),r())},s),c=function(){clearTimeout(i),t.removeEventListener("load",c),e()};t.addEventListener("load",c)})},a.checkCallbackFnOrThrow=function(t){if(!t)throw new L("`callbackFn` is a required parameter, you cannot capture results without it.")},a.disposeMediaStream=function(t){t.getVideoTracks().forEach(function(e){return e.stop()}),t=void 0},a.prototype.decode=function(t){var e=a.createCanvasFromMediaElement(t);return this.decodeFromCanvas(e)},a.prototype.decodeBitmap=function(t){return this.reader.decode(t,this.hints)},a.prototype.decodeFromCanvas=function(t){var e=a.createBinaryBitmapFromCanvas(t);return this.decodeBitmap(e)},a.prototype.decodeFromImageElement=function(t){return b(this,void 0,void 0,function(){var e;return w(this,function(r){switch(r.label){case 0:if(!t)throw new L("An image element must be provided.");return e=a.prepareImageElement(t),[4,this._decodeOnLoadImage(e)];case 1:return[2,r.sent()]}})})},a.prototype.decodeFromImageUrl=function(t){return b(this,void 0,void 0,function(){var e;return w(this,function(r){switch(r.label){case 0:if(!t)throw new L("An URL must be provided.");e=a.prepareImageElement(),e.src=t,r.label=1;case 1:return r.trys.push([1,,3,4]),[4,this.decodeFromImageElement(e)];case 2:return[2,r.sent()];case 3:return a.destroyImageElement(e),[7];case 4:return[2]}})})},a.prototype.decodeFromConstraints=function(t,e,r){return b(this,void 0,void 0,function(){var s,i;return w(this,function(c){switch(c.label){case 0:return a.checkCallbackFnOrThrow(r),[4,this.getUserMedia(t)];case 1:s=c.sent(),c.label=2;case 2:return c.trys.push([2,4,,5]),[4,this.decodeFromStream(s,e,r)];case 3:return[2,c.sent()];case 4:throw i=c.sent(),a.disposeMediaStream(s),i;case 5:return[2]}})})},a.prototype.decodeFromStream=function(t,e,r){return b(this,void 0,void 0,function(){var s,i,c,u,m,l,d,f,p,y=this;return w(this,function(D){switch(D.label){case 0:return a.checkCallbackFnOrThrow(r),s=this.options.tryPlayVideoTimeout,[4,a.attachStreamToVideo(t,e,s)];case 1:return i=D.sent(),c=function(){a.disposeMediaStream(t),a.cleanVideoSource(i)},u=this.scan(i,r,c),m=t.getVideoTracks(),l=K(K({},u),{stop:function(){u.stop()},streamVideoConstraintsApply:function(C,O){return b(this,void 0,void 0,function(){var E,A,B,z,H,g,_;return w(this,function(j){switch(j.label){case 0:E=O?m.filter(O):m,j.label=1;case 1:j.trys.push([1,6,7,8]),A=ce(E),B=A.next(),j.label=2;case 2:return B.done?[3,5]:(z=B.value,[4,z.applyConstraints(C)]);case 3:j.sent(),j.label=4;case 4:return B=A.next(),[3,2];case 5:return[3,8];case 6:return H=j.sent(),g={error:H},[3,8];case 7:try{B&&!B.done&&(_=A.return)&&_.call(A)}finally{if(g)throw g.error}return[7];case 8:return[2]}})})},streamVideoConstraintsGet:function(C){return m.find(C).getConstraints()},streamVideoSettingsGet:function(C){return m.find(C).getSettings()},streamVideoCapabilitiesGet:function(C){return m.find(C).getCapabilities()}}),d=a.mediaStreamIsTorchCompatible(t),d&&(f=m?.find(function(C){return a.mediaStreamIsTorchCompatibleTrack(C)}),p=function(C){return b(y,void 0,void 0,function(){return w(this,function(O){switch(O.label){case 0:return[4,a.mediaStreamSetTorch(f,C)];case 1:return O.sent(),[2]}})})},l.switchTorch=p,l.stop=function(){return b(y,void 0,void 0,function(){return w(this,function(C){switch(C.label){case 0:return u.stop(),[4,p(!1)];case 1:return C.sent(),[2]}})})}),[2,l]}})})},a.prototype.decodeFromVideoDevice=function(t,e,r){return b(this,void 0,void 0,function(){var s,i;return w(this,function(c){switch(c.label){case 0:return a.checkCallbackFnOrThrow(r),t?s={deviceId:{exact:t}}:s={facingMode:"environment"},i={video:s},[4,this.decodeFromConstraints(i,e,r)];case 1:return[2,c.sent()]}})})},a.prototype.decodeFromVideoElement=function(t,e){return b(this,void 0,void 0,function(){var r,s;return w(this,function(i){switch(i.label){case 0:if(a.checkCallbackFnOrThrow(e),!t)throw new L("A video element must be provided.");return r=a.prepareVideoElement(t),s=this.options.tryPlayVideoTimeout,[4,a.playVideoOnLoadAsync(r,s)];case 1:return i.sent(),[2,this.scan(r,e)]}})})},a.prototype.decodeFromVideoUrl=function(t,e){return b(this,void 0,void 0,function(){var r,s,i,c;return w(this,function(u){switch(u.label){case 0:if(a.checkCallbackFnOrThrow(e),!t)throw new L("An URL must be provided.");return r=a.prepareVideoElement(),r.src=t,s=function(){a.cleanVideoSource(r)},i=this.options.tryPlayVideoTimeout,[4,a.playVideoOnLoadAsync(r,i)];case 1:return u.sent(),c=this.scan(r,e,s),[2,c]}})})},a.prototype.decodeOnceFromConstraints=function(t,e){return b(this,void 0,void 0,function(){var r;return w(this,function(s){switch(s.label){case 0:return[4,this.getUserMedia(t)];case 1:return r=s.sent(),[4,this.decodeOnceFromStream(r,e)];case 2:return[2,s.sent()]}})})},a.prototype.decodeOnceFromStream=function(t,e){return b(this,void 0,void 0,function(){var r,s,i;return w(this,function(c){switch(c.label){case 0:return r=!!e,[4,a.attachStreamToVideo(t,e)];case 1:s=c.sent(),c.label=2;case 2:return c.trys.push([2,,4,5]),[4,this.scanOneResult(s)];case 3:return i=c.sent(),[2,i];case 4:return r||a.cleanVideoSource(s),[7];case 5:return[2]}})})},a.prototype.decodeOnceFromVideoDevice=function(t,e){return b(this,void 0,void 0,function(){var r,s;return w(this,function(i){switch(i.label){case 0:return t?r={deviceId:{exact:t}}:r={facingMode:"environment"},s={video:r},[4,this.decodeOnceFromConstraints(s,e)];case 1:return[2,i.sent()]}})})},a.prototype.decodeOnceFromVideoElement=function(t){return b(this,void 0,void 0,function(){var e,r;return w(this,function(s){switch(s.label){case 0:if(!t)throw new L("A video element must be provided.");return e=a.prepareVideoElement(t),r=this.options.tryPlayVideoTimeout,[4,a.playVideoOnLoadAsync(e,r)];case 1:return s.sent(),[4,this.scanOneResult(e)];case 2:return[2,s.sent()]}})})},a.prototype.decodeOnceFromVideoUrl=function(t){return b(this,void 0,void 0,function(){var e,r;return w(this,function(s){switch(s.label){case 0:if(!t)throw new L("An URL must be provided.");e=a.prepareVideoElement(),e.src=t,r=this.decodeOnceFromVideoElement(e),s.label=1;case 1:return s.trys.push([1,,3,4]),[4,r];case 2:return[2,s.sent()];case 3:return a.cleanVideoSource(e),[7];case 4:return[2]}})})},a.prototype.scanOneResult=function(t,e,r,s){var i=this;return e===void 0&&(e=!0),r===void 0&&(r=!0),s===void 0&&(s=!0),new Promise(function(c,u){i.scan(t,function(m,l,d){if(m){c(m),d.stop();return}if(l){if(l instanceof ve&&e||l instanceof xe&&r||l instanceof ye&&s)return;d.stop(),u(l)}})})},a.prototype.scan=function(t,e,r){var s=this;a.checkCallbackFnOrThrow(e);var i=a.createCaptureCanvas(t),c=i.getContext("2d");if(!c)throw new Error("Couldn't create canvas for visual element scan.");var u=function(){c=void 0,i=void 0},m=!1,l,d=function(){m=!0,clearTimeout(l),u(),r&&r()},f={stop:d},p=function(){if(!m)try{a.drawImageOnCanvas(c,t);var y=s.decodeFromCanvas(i);e(y,void 0,f),l=setTimeout(p,s.options.delayBetweenScanSuccess)}catch(E){e(void 0,E,f);var D=E instanceof xe,C=E instanceof ye,O=E instanceof ve;if(D||C||O){l=setTimeout(p,s.options.delayBetweenScanAttempts);return}u(),r&&r(E)}};return p(),f},a.prototype._decodeOnLoadImage=function(t){return b(this,void 0,void 0,function(){var e;return w(this,function(r){switch(r.label){case 0:return e=a.isImageLoaded(t),e?[3,2]:[4,a._waitImageLoad(t)];case 1:r.sent(),r.label=2;case 2:return[2,this.decode(t)]}})})},a.prototype.getUserMedia=function(t){return b(this,void 0,void 0,function(){var e;return w(this,function(r){switch(r.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return e=r.sent(),a.streamTracker.push(e),[2,e]}})})},a.streamTracker=[],a}(),bt=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();(function(a){bt(t,a);function t(e,r){return a.call(this,new et,e,r)||this}return t})(Z);var wt=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();(function(a){wt(t,a);function t(e,r){return a.call(this,new tt(e),e,r)||this}return t})(Z);var jt=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();(function(a){jt(t,a);function t(e,r){return a.call(this,new rt,e,r)||this}return t})(Z);var Nt=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Ct=function(a){Nt(t,a);function t(e,r){var s=this,i=new nt;return i.setHints(e),s=a.call(this,i,e,r)||this,s.reader=i,s}return Object.defineProperty(t.prototype,"possibleFormats",{set:function(e){this.hints.set(ke.POSSIBLE_FORMATS,e),this.reader.setHints(this.hints)},enumerable:!1,configurable:!0}),t.prototype.decodeBitmap=function(e){return this.reader.decodeWithState(e)},t.prototype.setHints=function(e){this.hints=e,this.reader.setHints(this.hints)},t}(Z),St=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();(function(a){St(t,a);function t(e,r){return a.call(this,new at,e,r)||this}return t})(Z);var kt=function(){var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,s){r.__proto__=s}||function(r,s){for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(r[i]=s[i])},a(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");a(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();(function(a){kt(t,a);function t(e,r){return a.call(this,new st,e,r)||this}return t})(Z);function we({isOpen:a,onClose:t,onScan:e}){const{t:r}=J(),s=v.useRef(null),i=v.useRef(null),c=v.useRef(null),[u,m]=v.useState(!1),[l,d]=v.useState(null),[f,p]=v.useState(null),[y,D]=v.useState(""),[C,O]=v.useState(!1),E=v.useRef(!1);v.useEffect(()=>(i.current||(i.current=new Ct),()=>{A()}),[]),v.useEffect(()=>{if(!a)return;let g="",_=0;const j=50,k=V=>{if(document.activeElement&&document.activeElement.tagName==="INPUT")return;const G=new Date().getTime();G-_>j&&(g=""),V.key!=="Enter"?g+=V.key:g&&(g.length>=8&&(e(g.trim()),t(),V.preventDefault()),g=""),_=G};return window.addEventListener("keydown",k),()=>{window.removeEventListener("keydown",k)}},[a,e,t]);const A=()=>{if(console.log("Cleaning up scanner resources"),O(!1),E.current=!1,f&&(f.getTracks().forEach(g=>{try{g.stop()}catch(_){console.error("Error stopping track:",_)}}),p(null)),s.current)try{s.current.srcObject=null}catch(g){console.error("Error clearing video source:",g)}},B=async()=>{if(console.log("Starting camera scanning..."),!s.current){console.error("Video reference not available"),I.error(r("products.cameraError"));return}m(!0);try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)throw new Error("Camera API not supported in this browser");A(),O(!0);const g={video:{facingMode:"environment",width:{ideal:1280},height:{ideal:720}}};console.log("Requesting camera access...");const _=await navigator.mediaDevices.getUserMedia(g);if(console.log("Camera access granted"),p(_),d(!0),s.current&&(s.current.srcObject=_,await new Promise(j=>{if(!s.current)return j();const k=()=>{s.current?.removeEventListener("canplay",k),j()};if(s.current.addEventListener("canplay",k),s.current.readyState>=3){j();return}s.current.play().catch(V=>{console.error("Error playing video:",V),j()})})),i.current&&s.current){console.log("Starting ZXing decoding...");try{E.current=!0,i.current.decodeFromVideoDevice(void 0,s.current,(k,V)=>{if(E.current){if(k){const G=k.getText();console.log("Scanned barcode:",G),A(),e(G),t()}V&&!(V instanceof TypeError)&&console.log("Scanning error (non-critical):",V)}}).catch(k=>{console.error("Error starting ZXing scan:",k),I.error(r("products.scanningError"))})}catch(j){console.error("ZXing scanning error:",j),I.error(r("products.scanningError")),d(!1)}}}catch(g){console.error("Error accessing camera:",g),d(!1),String(g).includes("Permission")?I.error(r("products.cameraPermissionDenied")):I.error(r("products.cameraError"))}finally{m(!1)}};v.useEffect(()=>(a?c.current&&setTimeout(()=>{c.current?.focus()},100):(A(),d(null),D("")),()=>{A()}),[a]);const z=g=>{g.preventDefault(),y.trim()&&(e(y.trim()),t())},H=()=>{console.log("Start camera button clicked"),d(null),setTimeout(()=>{B()},10)};return n.jsx(je,{open:a,onOpenChange:g=>!g&&t(),children:n.jsxs(Ne,{className:"sm:max-w-md",children:[n.jsxs(Ce,{children:[n.jsx(Se,{children:r("products.scanBarcode")}),n.jsx(Oe,{children:r("products.scanOrEnterBarcode")})]}),n.jsxs("div",{className:"flex flex-col items-center gap-4",children:[u?n.jsxs("div",{className:"flex flex-col items-center justify-center p-8",children:[n.jsx(_e,{className:"h-8 w-8 animate-spin text-muted-foreground mb-2"}),n.jsx("p",{children:r("products.accessingCamera")})]}):l===!1?n.jsxs("div",{className:"text-center p-4",children:[n.jsx("p",{className:"text-destructive mb-2",children:r("products.cameraPermissionDenied")}),n.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:r("products.enableCameraPermission")}),n.jsx(N,{variant:"outline",onClick:H,type:"button",className:"focus:ring-2",children:r("products.retryCamera")})]}):l===!0?n.jsxs("div",{className:"relative w-full aspect-video bg-black rounded-md overflow-hidden",children:[n.jsx("video",{ref:s,className:"w-full h-full object-cover",playsInline:!0,muted:!0,autoPlay:!0}),n.jsx("div",{className:"absolute inset-0 border-2 border-white/30 rounded-md pointer-events-none",children:n.jsx("div",{className:"absolute top-1/4 left-1/4 right-1/4 bottom-1/4 border-2 border-white/50 rounded-md pointer-events-none"})})]}):n.jsxs("div",{className:"flex flex-col items-center justify-center p-6 bg-muted/20 rounded-md w-full",children:[n.jsx(Ie,{className:"h-8 w-8 text-muted-foreground mb-2"}),n.jsx("p",{className:"font-medium mb-1",children:r("products.cameraAccess")}),n.jsx("p",{className:"text-sm text-muted-foreground mb-4 text-center",children:r("products.allowCameraAccess")}),n.jsx(N,{onClick:H,type:"button",className:"focus:ring-2",children:r("products.startCamera")})]}),n.jsxs("form",{onSubmit:z,className:"w-full space-y-3",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx(q,{ref:c,type:"text",placeholder:r("products.enterBarcodeManually"),value:y,onChange:g=>D(g.target.value),className:"flex-1"}),n.jsx(N,{type:"submit",variant:"secondary",size:"icon",children:n.jsx(ft,{className:"h-4 w-4"})})]}),n.jsx("div",{className:"text-xs text-muted-foreground text-center",children:r("products.scannerInstructions")})]}),n.jsx("div",{className:"flex justify-end w-full gap-2",children:n.jsxs(N,{variant:"outline",onClick:t,type:"button",children:[n.jsx(X,{className:"mr-2 h-4 w-4"}),r("common.cancel")]})})]})]})})}function It({value:a,onSave:t,inputType:e,options:r=[],className:s}){const[i,c]=v.useState(a),u=v.useRef(null);v.useEffect(()=>{u.current&&(e==="text"||e==="number"||e==="textarea")&&(u.current.focus(),"select"in u.current&&u.current.select())},[e]);const m=d=>{d.key==="Enter"&&e!=="textarea"?(d.preventDefault(),t(i)):d.key==="Escape"&&(d.preventDefault(),t(a))},l=()=>{switch(e){case"select":return n.jsxs(ot,{value:String(i),onValueChange:d=>c(d),defaultOpen:!0,children:[n.jsx(ct,{className:"w-full h-8",children:n.jsx(lt,{placeholder:"Select..."})}),n.jsx(dt,{children:r.map(d=>n.jsx(ut,{value:d.value,children:d.label},d.value))})]});case"textarea":return n.jsx(de,{ref:u,value:String(i),onChange:d=>c(d.target.value),onKeyDown:m,className:"min-h-[80px] w-full",rows:3,autoFocus:!0});case"image":return n.jsxs("div",{className:"flex flex-col gap-2",children:[n.jsx("div",{className:"h-16 w-16 rounded-md border overflow-hidden bg-white flex items-center justify-center mb-1",children:typeof i=="string"&&i?n.jsx("img",{src:String(i),className:"h-full w-full object-contain",alt:"Product"}):n.jsx("div",{className:"flex items-center justify-center h-full w-full bg-gray-50",children:n.jsx(Ie,{className:"h-6 w-6 text-muted-foreground"})})}),n.jsxs(N,{type:"button",variant:"outline",size:"sm",className:"h-8 w-full gap-1 text-xs",onClick:()=>{const d=["/placeholder.svg","https://images.unsplash.com/photo-1619252584172-a83a949b6efd","https://images.unsplash.com/photo-1551816230-ef5d99c0f3b6"],f=d[Math.floor(Math.random()*d.length)];c(f)},children:[n.jsx(ue,{className:"h-3 w-3"}),"Upload"]})]});case"partArticleNumber":return n.jsxs("div",{className:"flex flex-col gap-1",children:[n.jsx(q,{ref:u,type:"text",value:String(i),onChange:d=>c(d.target.value),onKeyDown:m,className:"h-8 w-full font-mono",placeholder:"Scan or enter part article number"}),n.jsx("div",{className:"text-xs text-muted-foreground",children:"Part Article Number"})]});case"badge":return n.jsx("div",{className:"flex flex-wrap gap-2",children:r.map(d=>n.jsx(se,{variant:d.value===String(i)?"default":"outline",className:ie("cursor-pointer",d.value===String(i)?"bg-blue-500":"bg-transparent"),onClick:()=>c(d.value),children:d.label},d.value))});default:return n.jsx(q,{ref:u,type:e,value:String(i),onChange:d=>{const f=e==="number"?parseFloat(d.target.value)||0:d.target.value;c(f)},onKeyDown:m,className:"h-8 w-full",step:e==="number"?"0.01":void 0})}};return n.jsxs("div",{className:ie("flex gap-1 items-start",s),children:[n.jsx("div",{className:"flex-1",children:l()}),n.jsxs("div",{className:"flex flex-col gap-1",children:[n.jsx(N,{type:"button",variant:"ghost",size:"icon",onClick:()=>t(i),className:"h-6 w-6 bg-algerian-green/10 text-algerian-green hover:bg-algerian-green/20",children:n.jsx(Me,{className:"h-3 w-3"})}),n.jsx(N,{type:"button",variant:"ghost",size:"icon",onClick:()=>t(a),className:"h-6 w-6 bg-destructive/10 text-destructive hover:bg-destructive/20",children:n.jsx(X,{className:"h-3 w-3"})})]})]})}const Tt=[{id:"100245",name:"Maxxis PREMITRA 6 HP6",description:"High-performance summer tyre designed for passenger cars with excellent grip and handling characteristics.",sku:"MAX-HP6-2055516",partArticleNumber:"4717784367576",category:"tyres",subcategory:"Tyres",price:89.99,stockQuantity:42,images:["/placeholder.svg"],specifications:{Width:"205mm","Aspect Ratio":"55","Rim Size":"16 inch","Load Index":"91","Speed Rating":"V (240 km/h)"},manufacturer:"Maxxis",supplierName:"Global Tire Distributors",shippingOrigin:"Alger",compatibleVehicles:["Toyota Corolla 2018-2022","Honda Civic 2019-2023"],status:"published",createdAt:new Date("2023-05-15"),updatedAt:new Date("2023-06-02")},{id:"100246",name:"Continental PremiumContact 6",description:"Premium summer tyre with exceptional braking performance and handling characteristics.",sku:"CON-PC6-1955516",barcode:"4019238788532",category:"tyres",subcategory:"Tyres",price:112.99,stockQuantity:36,images:["/placeholder.svg"],specifications:{Width:"195mm","Aspect Ratio":"55","Rim Size":"16 inch","Load Index":"87","Speed Rating":"V (240 km/h)"},manufacturer:"Continental",supplierName:"European Tire Network",shippingOrigin:"Oran",compatibleVehicles:["Volkswagen Golf 2017-2022","Ford Focus 2018-2023"],status:"published",createdAt:new Date("2023-06-10"),updatedAt:new Date("2023-06-15")},{id:"100247",name:"Michelin Pilot Sport 4",description:"Ultra-high performance tyre providing exceptional steering precision and driving enjoyment.",sku:"MIC-PS4-2254518",barcode:"3528704157402",category:"tyres",subcategory:"Tyres",price:145.99,stockQuantity:0,images:["/placeholder.svg"],specifications:{Width:"225mm","Aspect Ratio":"45","Rim Size":"18 inch","Load Index":"95","Speed Rating":"Y (300 km/h)"},manufacturer:"Michelin",supplierName:"Premium Tire Solutions",shippingOrigin:"Constantine",compatibleVehicles:["BMW 3 Series 2018-2023","Mercedes C-Class 2018-2022"],status:"out_of_stock",createdAt:new Date("2023-05-22"),updatedAt:new Date("2023-06-05")},{id:"100248",name:"AutoCare Alloy Wheel Covers",description:"Premium wheel covers for 16-inch alloy wheels, providing stylish protection against dust and debris.",sku:"AC-WC-16INCH",barcode:"5901234123457",category:"tyres",subcategory:"Wheel Covers",price:35.5,stockQuantity:64,images:["/placeholder.svg"],specifications:{Size:"16 inch",Material:"ABS Plastic with Chrome Finish",Fitment:"Universal",Quantity:"Set of 4"},manufacturer:"AutoCare",supplierName:"Global Auto Accessories",compatibleVehicles:["Universal Fit for 16-inch Wheels"],status:"published",createdAt:new Date("2023-06-18"),updatedAt:new Date("2023-06-20")},{id:"100249",name:"TireTech Premium Tire Bag Set",description:"Heavy-duty tire storage bags with handles, perfect for seasonal tire storage and transport.",sku:"TT-TBS-STANDARD",barcode:"7890123456789",category:"tyres",subcategory:"Tire Bag Sets",price:29.95,stockQuantity:23,images:["/placeholder.svg"],specifications:{Size:"Standard (Up to 22-inch tires)",Material:"Waterproof Polyester",Features:"Handles, ID Window",Quantity:"Set of 4"},manufacturer:"TireTech",supplierName:"Auto Storage Solutions",compatibleVehicles:["Universal Fit"],status:"published",createdAt:new Date("2023-06-25"),updatedAt:new Date("2023-06-25")},{id:"100250",name:"Goodyear EfficientGrip Performance 2",description:"Efficient summer tyre delivering excellent wet grip and reduced rolling resistance for improved fuel economy.",sku:"GY-EGP2-2054516",barcode:"5452000759313",category:"tyres",subcategory:"Tyres",price:99.99,stockQuantity:35,images:["/placeholder.svg"],specifications:{Width:"205mm","Aspect Ratio":"45","Rim Size":"16 inch","Load Index":"83","Speed Rating":"W (270 km/h)"},manufacturer:"Goodyear",supplierName:"National Tire Distributors",compatibleVehicles:["Volkswagen Polo 2018-2022","Renault Clio 2019-2023"],status:"published",createdAt:new Date("2023-05-30"),updatedAt:new Date("2023-06-12")},{id:"100251",name:"Pirelli P Zero",description:"Ultra-high performance tire designed for sports cars and high-end vehicles, providing maximum grip and handling.",sku:"PIR-**********",barcode:"8019227271805",category:"tyres",subcategory:"Tyres",price:179.99,stockQuantity:0,images:["/placeholder.svg"],specifications:{Width:"245mm","Aspect Ratio":"35","Rim Size":"19 inch","Load Index":"93","Speed Rating":"Y (300 km/h)"},manufacturer:"Pirelli",supplierName:"Performance Tire Solutions",compatibleVehicles:["Porsche 911 2018-2022","Ferrari 488 2016-2020"],status:"out_of_stock",createdAt:new Date("2023-06-05"),updatedAt:new Date("2023-06-15")},{id:"100252",name:"WheelPro Chrome Valve Caps",description:"Premium chrome-plated valve caps with anti-theft design for a stylish finishing touch to your wheels.",sku:"WP-VC-CHROME",barcode:"6012345678902",category:"tyres",subcategory:"Wheel Accessories",price:12.95,stockQuantity:112,images:["/placeholder.svg"],specifications:{Material:"Chrome-Plated Brass",Features:"Anti-theft Design, Rubber Seal",Compatibility:"Standard Valve Stems",Quantity:"Set of 4"},manufacturer:"WheelPro",supplierName:"Automotive Finishing Accessories",compatibleVehicles:["Universal Fit"],status:"published",createdAt:new Date("2023-06-20"),updatedAt:new Date("2023-06-22")}];function Et({activeCategory:a,visibleColumns:t}){const{t:e,i18n:r}=J(),{userRole:s,isSupplier:i,isMerchant:c}=Ve();r.language;const[u,m]=v.useState(Tt),[l,d]=v.useState([]),[f,p]=v.useState(""),[y,D]=v.useState(!1),[C,O]=v.useState(null),[E,A]=v.useState(!1),[B,z]=v.useState(!1),[H,g]=v.useState(null),[_,j]=v.useState(null),k=a==="all"?u:u.filter(o=>o.category===a);console.log("Active Category:",a),console.log("Filtered Products:",k.length),console.log("All Products:",u.length),console.log("Products with category:",u.map(o=>({id:o.id,category:o.category})));const V=()=>{if(!f.trim()){I.error(e("products.barcodeRequired"));return}if(u.some(o=>o.partArticleNumber===f)){I.error(e("products.barcodeExists"));return}z(!0)},G=o=>{o.key==="Enter"&&(o.preventDefault(),V())},me=o=>{p(o),I.success(e("products.barcodeScanned"))},Ee=()=>{l.length===k.length?d([]):d(k.map(o=>o.id))},Ae=o=>{l.includes(o)?d(l.filter(h=>h!==o)):d([...l,o])},Pe=o=>{O(o),A(!0)},W=(o,h)=>{g({productId:o,field:h})},oe=(o,h,R)=>{const F=u.map(P=>P.id===o?{...P,[h]:R,updatedAt:new Date}:P);m(F),g(null),I.success(e("products.fieldUpdated"))},he=(o,h,R)=>{const F=["/placeholder.svg","https://images.unsplash.com/photo-1619252584172-a83a949b6efd","https://images.unsplash.com/photo-1551816230-ef5d99c0f3b6","https://images.unsplash.com/photo-1641919505555-1089aaf96cae"],P=F[Math.floor(Math.random()*F.length)],$=u.map(U=>{if(U.id===o){if(h==="primaryImage")return{...U,primaryImage:P,updatedAt:new Date};if(h==="additionalImages"){const fe=[...U.additionalImages||[]];return fe.push(P),{...U,additionalImages:fe,updatedAt:new Date}}}return U});m($),j(null),I.success(e("products.imageUploaded"))},De=(o,h)=>{const R=u.map(F=>{if(F.id===o&&F.additionalImages){const P=[...F.additionalImages];return P.splice(h,1),{...F,additionalImages:P,updatedAt:new Date}}return F});m(R),I.success(e("products.imageRemoved"))},x=o=>t.includes(o),M=(o,h)=>{const R=H?.productId===o.id&&H?.field===h,F=_?.productId===o.id&&_?.field===h;if(R)return h==="description"?n.jsx(de,{value:o[h]||"",onChange:P=>{const $=u.map(U=>U.id===o.id?{...U,[h]:P.target.value}:U);m($)},onBlur:()=>oe(o.id,h,o[h]||""),className:"w-full h-24 min-h-[80px]",autoFocus:!0}):h==="primaryImage"?n.jsxs("div",{className:"flex flex-col gap-2 p-2 border rounded-md bg-white",children:[n.jsx("div",{className:"h-24 w-full flex items-center justify-center border-2 border-dashed border-muted-foreground/20 rounded-md p-2",children:n.jsxs(N,{variant:"outline",size:"sm",className:"h-8 flex gap-1",onClick:()=>{g(null),j({productId:o.id,field:h}),setTimeout(()=>he(o.id,h),800)},children:[n.jsx(ue,{className:"h-4 w-4"}),e("products.uploadImage")]})}),n.jsx("div",{className:"flex justify-end gap-1 mt-1",children:n.jsx(N,{variant:"outline",size:"sm",className:"h-7 w-7 p-0 rounded-full",onClick:()=>oe(o.id,h,o[h]||""),children:n.jsx(X,{className:"h-3 w-3"})})})]}):h==="additionalImages"?n.jsxs("div",{className:"flex flex-col gap-2 p-2 border rounded-md bg-white min-w-[200px]",children:[n.jsx("div",{className:"flex flex-wrap gap-2",children:o.additionalImages&&o.additionalImages.length>0?o.additionalImages.map((P,$)=>n.jsxs("div",{className:"relative h-16 w-16 border rounded-md overflow-hidden group",children:[n.jsx("img",{src:P,alt:"",className:"h-full w-full object-contain"}),n.jsx(N,{variant:"destructive",size:"sm",className:"h-5 w-5 p-0 absolute top-0 right-0 opacity-0 group-hover:opacity-100 rounded-full",onClick:()=>De(o.id,$),children:n.jsx(X,{className:"h-3 w-3"})})]},$)):n.jsx("div",{className:"text-muted-foreground italic text-sm",children:e("products.noAdditionalImages")})}),n.jsx("div",{className:"h-16 w-full flex items-center justify-center border-2 border-dashed border-muted-foreground/20 rounded-md p-2",children:n.jsxs(N,{variant:"outline",size:"sm",className:"h-7 flex gap-1 text-xs",onClick:()=>{g(null),j({productId:o.id,field:h}),setTimeout(()=>he(o.id,h),800)},children:[n.jsx(Y,{className:"h-3 w-3"}),e("products.addImage")]})}),n.jsx("div",{className:"flex justify-end gap-1 mt-1",children:n.jsx(N,{variant:"outline",size:"sm",className:"h-7 w-7 p-0 rounded-full",onClick:()=>g(null),children:n.jsx(X,{className:"h-3 w-3"})})})]}):n.jsx("div",{className:"min-w-[120px]",children:n.jsx(It,{value:o[h]||"",onSave:P=>oe(o.id,h,P),inputType:h==="price"||h==="stockQuantity"?"number":h==="status"?"select":"text",options:h==="status"?[{value:"draft",label:e("products.draft")},{value:"published",label:e("products.published")},{value:"out_of_stock",label:e("products.outOfStock")}]:h==="subcategory"?[{value:"Tyres",label:"Tyres"},{value:"Wheel Covers",label:"Wheel Covers"},{value:"Tire Bag Sets",label:"Tire Bag Sets"},{value:"Wheel Accessories",label:"Wheel Accessories"}]:void 0})});if(F)return n.jsxs("div",{className:"flex items-center justify-center p-2",children:[n.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-electric-orange border-t-transparent animate-spin"}),n.jsx("span",{className:"ml-2 text-sm",children:e("products.uploading")})]});if(h==="category")return n.jsx("div",{className:"cursor-pointer hover:bg-gray-50 p-1 rounded",onClick:()=>W(o.id,h),children:n.jsx(se,{variant:"outline",className:"bg-[#f0f7ff] text-[#0073ea] border-[#0073ea]/30",children:"Tyres & Related Products"})});switch(h){case"primaryImage":return n.jsx("div",{className:"h-10 w-10 rounded-md border bg-white overflow-hidden flex items-center justify-center cursor-pointer hover:bg-gray-50",onClick:()=>W(o.id,h),children:n.jsx("img",{src:o.primaryImage||o.images[0]||"/placeholder.svg",className:"h-full w-full object-contain",alt:o.name})});case"additionalImages":return n.jsx("div",{className:"flex gap-1",children:o.additionalImages&&o.additionalImages.length>0?n.jsxs(n.Fragment,{children:[n.jsx("div",{className:"h-8 w-8 rounded-md border bg-white overflow-hidden flex items-center justify-center",children:n.jsx("img",{src:o.additionalImages[0],className:"h-full w-full object-contain",alt:`${o.name} additional`})}),o.additionalImages.length>1&&n.jsxs(se,{variant:"outline",className:"bg-muted",children:["+",o.additionalImages.length-1]})]}):n.jsx(N,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>W(o.id,h),children:n.jsx(Y,{className:"h-4 w-4"})})});case"description":return n.jsx("div",{className:"max-w-xs truncate cursor-pointer hover:text-blue-600",onClick:()=>W(o.id,h),title:o.description||e("products.clickToEdit"),children:o.description?o.description.length>50?`${o.description.substring(0,50)}...`:o.description:n.jsx("span",{className:"text-muted-foreground italic",children:e("products.clickToEdit")})});case"manufacturer":return n.jsxs("div",{className:"flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded",onClick:()=>W(o.id,h),children:[o.manufacturerLogo&&n.jsx("div",{className:"h-6 w-6 rounded-full overflow-hidden",children:n.jsx("img",{src:o.manufacturerLogo,className:"h-full w-full object-contain",alt:o.manufacturer})}),n.jsx("span",{children:o.manufacturer||n.jsx("span",{className:"text-muted-foreground italic",children:e("products.clickToEdit")})})]});case"barcode":return n.jsxs("div",{className:"flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded",onClick:()=>W(o.id,h),children:[n.jsx(ne,{className:"h-4 w-4 text-muted-foreground"}),n.jsx("span",{className:"text-sm font-mono",children:o[h]||n.jsx("span",{className:"text-muted-foreground italic",children:e("products.clickToEdit")})})]});case"status":return n.jsx("div",{onClick:()=>W(o.id,h),className:"cursor-pointer",children:n.jsx(se,{variant:o.status==="published"?"default":o.status==="draft"?"secondary":"destructive",children:o.status==="published"?e("products.published"):o.status==="draft"?e("products.draft"):e("products.outOfStock")})});default:return n.jsx("div",{className:"cursor-pointer hover:bg-gray-50 p-1 rounded",onClick:()=>W(o.id,h),children:o[h]?String(o[h]):n.jsx("span",{className:"text-muted-foreground italic",children:e("products.clickToEdit")})})}},Q=o=>{switch(o){case"id":return null;case"name":return n.jsx(be,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"sku":return n.jsx(mt,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"partArticleNumber":return n.jsx(ne,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"description":return n.jsx(Fe,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"primaryImage":return n.jsx(ge,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"additionalImages":return n.jsx(ge,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"manufacturer":return n.jsx(be,{className:"h-4 w-4 mr-2 text-muted-foreground"});case"supplierName":return n.jsx(Re,{className:"h-4 w-4 mr-2 text-muted-foreground"});default:return null}};return n.jsxs("div",{className:"space-y-4",children:[n.jsx(we,{isOpen:y,onClose:()=>D(!1),onScan:me}),n.jsx("div",{className:"bg-white border border-border rounded-md p-4 shadow-sm",children:n.jsx("div",{className:"border border-border rounded-md overflow-hidden",children:n.jsx("div",{className:"overflow-x-auto",children:n.jsxs(He,{className:"monday-style-table",children:[n.jsx(We,{className:"bg-[#f5f6f8] sticky top-0 z-10",children:n.jsxs(te,{className:"border-b-0 hover:bg-transparent",children:[n.jsx(T,{className:"w-[50px] py-3 px-3 border-r border-[#e1e1e1]",children:n.jsx(pe,{checked:k.length>0&&l.length===k.length,onCheckedChange:Ee,className:"rounded-sm"})}),x("id")&&n.jsx(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[80px]",children:e("products.id")}),x("primaryImage")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[80px]",children:[Q("primaryImage"),e("products.primaryImage")]}),x("name")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[250px]",children:[Q("name"),e("products.name")]}),x("sku")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:[Q("sku"),e("products.sku")]}),x("partArticleNumber")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:[Q("partArticleNumber"),e("products.barcodeEan")]}),x("category")&&n.jsx(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:e("products.category")}),x("subcategory")&&n.jsx(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:e("products.subcategory")}),x("manufacturer")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:[Q("manufacturer"),e("products.manufacturer")]}),x("supplierName")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:[Q("supplierName"),e("products.supplierName")]}),x("description")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[250px]",children:[Q("description"),e("products.description")]}),x("additionalImages")&&n.jsxs(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:[Q("additionalImages"),e("products.additionalImages")]}),x("price")&&n.jsx(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:e("products.price")}),x("stockQuantity")&&n.jsx(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:e("products.stock")}),x("status")&&n.jsx(T,{className:"font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]",children:e("products.status")}),x("actions")&&n.jsx(T,{className:"w-[80px] py-3 px-3 font-medium text-center",children:e("products.actions")})]})}),n.jsxs(Qe,{children:[k.map((o,h)=>n.jsxs(te,{className:ie("group border-b hover:bg-[#f0f3ff]",h%2===0?"bg-white":"bg-[#f9fafc]"),children:[n.jsx(S,{className:"py-2 px-3 border-r border-[#e1e1e1]",children:n.jsx(pe,{checked:l.includes(o.id),onCheckedChange:()=>Ae(o.id),className:"rounded-sm"})}),x("id")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1] font-mono text-sm",children:o.id}),x("primaryImage")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"primaryImage")}),x("name")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1] font-medium",children:M(o,"name")}),x("sku")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1] font-mono text-sm",children:M(o,"sku")}),x("barcode")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"barcode")}),x("category")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"category")}),x("subcategory")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"subcategory")}),x("manufacturer")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"manufacturer")}),x("supplierName")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"supplierName")}),x("description")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"description")}),x("additionalImages")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"additionalImages")}),x("price")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"price")}),x("stockQuantity")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"stockQuantity")}),x("status")&&n.jsx(S,{className:"py-2 px-4 border-r border-[#e1e1e1]",children:M(o,"status")}),x("actions")&&n.jsx(S,{className:"py-2 px-3 text-center",children:n.jsxs(N,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full bg-transparent hover:bg-[#dcdfec] focus:bg-[#dcdfec]",onClick:()=>Pe(o),children:[n.jsx(ht,{className:"h-4 w-4 text-[#676879]"}),n.jsx("span",{className:"sr-only",children:e("products.edit")})]})})]},o.id)),n.jsxs(te,{className:"group border-b hover:bg-[#f0f3ff] bg-white",children:[n.jsx(S,{className:"py-2 px-3 border-r border-[#e1e1e1]",children:n.jsx("div",{className:"w-4 h-4"})}),n.jsx(S,{colSpan:t.length,className:"py-3 px-4 border-r border-[#e1e1e1]",children:n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsxs("div",{className:"relative flex-1",children:[n.jsx(q,{placeholder:e("products.enterBarcode"),value:f,onChange:o=>p(o.target.value),onKeyDown:G,className:"pl-9 pr-9 h-9 text-sm border-[#c3c6d4] focus-visible:ring-[#0073ea]"}),n.jsx(ne,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),n.jsx(N,{variant:"ghost",size:"icon",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7",onClick:()=>D(!0),children:n.jsx(it,{className:"h-4 w-4 text-muted-foreground"})})]}),n.jsxs(N,{variant:f?"default":"ghost",size:"sm",className:ie("h-9",f?"bg-[#0073ea] hover:bg-[#0060b9]":"opacity-50"),onClick:V,disabled:!f,children:[n.jsx(Y,{className:"h-4 w-4 mr-1"}),e("products.add")]})]})})]}),k.length===0&&f===""&&n.jsx(te,{children:n.jsx(S,{colSpan:t.length+1,className:"h-24 text-center",children:n.jsxs("div",{className:"flex flex-col items-center justify-center py-6",children:[n.jsx("div",{className:"h-12 w-12 rounded-full bg-muted/30 flex items-center justify-center mb-3",children:n.jsx(ne,{className:"h-6 w-6 text-muted-foreground"})}),n.jsx("p",{className:"text-lg font-medium mb-1",children:e("products.noProductsFound")}),n.jsx("p",{className:"text-muted-foreground mb-4",children:e("products.enterBarcodeToAdd")})]})})})]})]})})})}),n.jsx("style",{children:`
        .monday-style-table {
          border-collapse: separate;
          border-spacing: 0;
        }

        .monday-style-table th {
          font-weight: 500;
          color: #676879;
          text-transform: capitalize;
        }

        .monday-style-table td, .monday-style-table th {
          text-align: left;
          vertical-align: middle;
        }
        `}),y&&n.jsx(we,{onScan:me,onClose:()=>D(!1)}),B&&n.jsx(qe,{isOpen:B,onClose:()=>z(!1),onSave:async o=>{const h=i()?"wholesale":"retail",R={...o,id:o.id||Ge(o.category||a,h),createdAt:new Date,updatedAt:new Date};m([...u,R]),z(!1),I.success(e("products.productCreated"))},categories:[{id:"tyres",name:re("tyres")},{id:"brakes",name:re("brakes")}],initialBarcode:f,categoryId:a!=="all"?a:"tyres"}),E&&C&&n.jsx($e,{isOpen:E,onClose:()=>A(!1),product:C,onSave:async o=>{const h=u.map(R=>R.id===o.id?{...R,...o}:R);m(h),A(!1),O(null),I.success(e("products.productUpdated"))},categories:[{id:"tyres",name:re("tyres")},{id:"brakes",name:re("brakes")}]})]})}function At({isOpen:a,onClose:t,onSave:e,category:r}){const{t:s}=J(),[i,c]=v.useState({name:r?.name||"",description:r?.description||"",image:r?.image||"",slug:r?.slug||"",parentId:r?.parentId,productCount:0,isDefault:!1}),u=f=>f.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),m=f=>{const p=f.target.value;c({...i,name:p,slug:u(p)})},l=f=>{const{name:p,value:y}=f.target;c({...i,[p]:y})},d=f=>{if(f.preventDefault(),!i.name.trim()){I.error(s("categories.nameRequired"));return}e(i),t(),I.success(s("categories.savedSuccessfully"))};return n.jsx(je,{open:a,onOpenChange:t,children:n.jsxs(Ne,{className:"sm:max-w-[500px]",children:[n.jsx(Ce,{children:n.jsx(Se,{children:s(r?"categories.editCategory":"categories.addCategory")})}),n.jsxs("form",{onSubmit:d,className:"space-y-6 py-4",children:[n.jsxs("div",{className:"grid gap-4",children:[n.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[n.jsx(ee,{htmlFor:"name",className:"text-right",children:s("categories.name")}),n.jsx(q,{id:"name",name:"name",value:i.name,onChange:m,className:"col-span-3",required:!0})]}),n.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[n.jsx(ee,{htmlFor:"slug",className:"text-right",children:s("categories.slug")}),n.jsx(q,{id:"slug",name:"slug",value:i.slug,onChange:l,className:"col-span-3",required:!0})]}),n.jsxs("div",{className:"grid grid-cols-4 items-start gap-4",children:[n.jsx(ee,{htmlFor:"description",className:"text-right",children:s("categories.description")}),n.jsx(de,{id:"description",name:"description",value:i.description,onChange:l,className:"col-span-3",rows:3})]}),n.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[n.jsx(ee,{htmlFor:"image",className:"text-right",children:s("categories.image")}),n.jsxs("div",{className:"col-span-3 flex gap-2",children:[n.jsx(q,{id:"image",name:"image",value:i.image,onChange:l,placeholder:"Image URL",className:"flex-1"}),n.jsx(N,{type:"button",variant:"outline",size:"icon",className:"flex-shrink-0",children:n.jsx(ue,{className:"h-4 w-4"})})]})]}),i.image&&n.jsx("div",{className:"grid grid-cols-4 items-center gap-4",children:n.jsx("div",{className:"col-start-2 col-span-3 flex justify-start",children:n.jsx("div",{className:"relative w-16 h-16 rounded-md overflow-hidden border",children:n.jsx("img",{src:i.image,alt:i.name,className:"object-cover w-full h-full",onError:f=>{f.target.src="/placeholder.svg"}})})})})]}),n.jsxs(Be,{children:[n.jsx(N,{type:"button",variant:"outline",onClick:t,children:s("common.cancel")}),n.jsx(N,{type:"submit",className:"bg-electric-orange hover:bg-electric-orange/90",children:s("common.save")})]})]})]})})}function Pt({categories:a,currentCategory:t,onAddCategory:e,buttonId:r}){const{t:s}=J(),[i,c]=v.useState(!1),u=()=>{c(!0)},m=()=>{c(!1)},l=d=>{e(d),c(!1)};return n.jsxs(n.Fragment,{children:[n.jsxs(N,{id:r,variant:"ghost",className:"gap-2 hover:bg-muted/50",onClick:u,style:{unicodeBidi:"isolate"},children:[n.jsx(Y,{className:"h-4 w-4 flex-shrink-0"}),n.jsx("span",{style:{unicodeBidi:"isolate"},children:s("categories.addCategory")})]}),n.jsx(At,{isOpen:i,onClose:m,onSave:l})]})}const Dt=[{id:"tyres",name:"Tyres & Related Products",description:"Tires, wheel covers, and related accessories",slug:"tyres-related-products",productCount:100,isDefault:!1},{id:"brakes",name:"Brake Parts & Systems",description:"Brake discs, pads, calipers, and related brake system components",slug:"brake-parts-systems",productCount:100,isDefault:!1}],Ot=["id","name","sku","barcode","category","subcategory","manufacturer","supplierName","description","primaryImage","additionalImages"],_t=["id","name","sku","barcode","category","subcategory","manufacturer","supplierName","description","primaryImage","additionalImages"],ae={all:["id","name","sku","barcode","category","subcategory","manufacturer","supplierName","description","primaryImage","additionalImages","retailPrice","stockQuantity","status","actions"],tyres:Ot.concat(["status","actions"]),brakes:_t.concat(["status","actions"])};function Yt(){const{t:a}=J(),t=Le(),{slug:e}=Ue(),[r,s]=v.useState(Dt),[i,c]=v.useState(null),[u,m]=v.useState(ae.all),[l,d]=v.useState("all");v.useEffect(()=>{if(e){const p=r.find(y=>y.slug===e);if(p){c(p),d(p.id);const y=ae[p.id]||ae.all;m(y),console.log("Setting category ID to:",p.id)}else I.error(a("categories.notFound")),t("/app/supplier/products-table")}else d("all"),m(ae.all)},[e,r,t,a]),console.log("CategoryProductsPage - Active Category ID:",l);const f=p=>{const y={...p,id:`category-${Date.now()}`,productCount:0};s([...r,y]),I.success(a("categories.addedSuccessfully"))};return n.jsx(ze,{children:n.jsxs("div",{className:"flex flex-col gap-6",children:[n.jsxs("div",{className:"flex justify-between items-center",children:[n.jsxs("div",{children:[n.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:i?i.name:a("products.productLibrary")}),n.jsx("p",{className:"text-muted-foreground mt-1",children:i?i.description:a("products.libraryDescription")})]}),n.jsxs(N,{className:"gap-2 bg-electric-orange hover:bg-electric-orange/90",onClick:()=>t("/app/supplier/products/new"),children:[n.jsx(Y,{className:"h-4 w-4"}),a("products.addProduct")]})]}),n.jsx(Pt,{categories:r,currentCategory:e,onAddCategory:f}),n.jsx(Et,{activeCategory:l,visibleColumns:u})]})})}export{Yt as default};
