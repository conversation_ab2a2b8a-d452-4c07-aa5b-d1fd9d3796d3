var _=Object.defineProperty;var G=(o,t,a)=>t in o?_(o,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[t]=a;var f=(o,t,a)=>G(o,typeof t!="symbol"?t+"":t,a);import{a as X,a7 as W,r as x,j as e,C as S,k as b,Q as T,O as Y,l as N,m as y,_ as Z,U as I,c0 as C,L as $,I as w,B as p,c5 as E,N as q,w as H,Z as Q,c6 as ee}from"./index-BqaWQJEu.js";import{v as se}from"./dexatelService-jW7m0M-0.js";import{sendPhoneOTP as te,verifyPhoneOTP as ae,checkPhoneExists as ie}from"./phoneAuthService-C9Sn1E6E.js";import{K as ne,W as re}from"./wifi-BLTfmNhW.js";import{D as oe}from"./database-w0IbHezX.js";import{P as ce}from"./phone-DMHWBins.js";class le{constructor(){f(this,"results",[]);f(this,"apiKey");f(this,"supabaseUrl");f(this,"supabaseAnonKey");this.apiKey="4595fd59fb0655b92b79c6fa00fb5d36",this.supabaseUrl="https://irkwpzcskeqtasutqnxp.supabase.co",this.supabaseAnonKey="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94"}addResult(t,a,i,n){const h={step:t,success:a,message:i,data:n,timestamp:new Date().toISOString()};this.results.push(h),console.log(`[${t}] ${a?"✅":"❌"} ${i}`,n||"")}async runComprehensiveTest(){return this.results=[],this.addResult("Environment Check",!!(this.apiKey&&this.supabaseUrl&&this.supabaseAnonKey),this.getEnvironmentMessage(),{hasApiKey:!!this.apiKey,hasSupabaseUrl:!!this.supabaseUrl,hasSupabaseAnonKey:!!this.supabaseAnonKey,keyLength:this.apiKey.length}),!this.supabaseUrl||!this.supabaseAnonKey?(this.addResult("CRITICAL ERROR",!1,"Cannot proceed without Supabase configuration",{}),this.results):(await this.testSupabaseEdgeFunction(),await this.testSMSOTPSend(),await this.testOTPVerification(),await this.testPhoneFormats(),this.results)}getEnvironmentMessage(){const t=[];return this.apiKey||t.push("Dexatel API Key"),this.supabaseUrl||t.push("Supabase URL"),this.supabaseAnonKey||t.push("Supabase Anon Key"),t.length===0?"All environment variables configured correctly":`Missing: ${t.join(", ")}`}async testSupabaseEdgeFunction(){try{const t=await fetch(`${this.supabaseUrl}/functions/v1/send-sms-otp`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.supabaseAnonKey}`,apikey:this.supabaseAnonKey},body:JSON.stringify({phone:"+213555123456",test:!0})}),a=await t.text();let i;try{i=JSON.parse(a)}catch{i=a}this.addResult("Supabase Edge Function",t.ok,`Edge Function ${t.ok?"accessible":"failed"} (Status: ${t.status})`,{status:t.status,response:i,headers:Object.fromEntries(t.headers.entries())})}catch(t){this.addResult("Supabase Edge Function",!1,`Edge Function test failed: ${t.message}`,{error:t.toString()})}}async testSMSOTPSend(){try{const t={phone:"+213555123456"},a=await fetch(`${this.supabaseUrl}/functions/v1/send-sms-otp`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.supabaseAnonKey}`,apikey:this.supabaseAnonKey},body:JSON.stringify(t)}),i=await a.text();let n;try{n=JSON.parse(i)}catch{n=i}this.addResult("SMS OTP Send Test",a.ok,`SMS OTP ${a.ok?"sent successfully":"failed to send"} (Status: ${a.status})`,{payload:t,status:a.status,response:n,headers:Object.fromEntries(a.headers.entries())})}catch(t){this.addResult("SMS OTP Send Test",!1,`SMS OTP send test failed: ${t.message}`,{error:t.toString()})}}async testOTPVerification(){try{const t={phone:"+213555123456",otp:"123456"},a=await fetch(`${this.supabaseUrl}/functions/v1/verify-sms-otp`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.supabaseAnonKey}`,apikey:this.supabaseAnonKey},body:JSON.stringify(t)}),i=await a.text();let n;try{n=JSON.parse(i)}catch{n=i}this.addResult("OTP Verification Test",a.status===200||a.status===400,`OTP Verification ${a.ok?"working":"endpoint accessible"} (Status: ${a.status})`,{payload:t,status:a.status,response:n,headers:Object.fromEntries(a.headers.entries())})}catch(t){this.addResult("OTP Verification Test",!1,`OTP verification test failed: ${t.message}`,{error:t.toString()})}}async testPhoneFormats(){const t=["+213555123456","+213 555 123 456","213555123456","0555123456"];for(const a of t)try{const i={phone:a},n=await fetch(`${this.supabaseUrl}/functions/v1/send-sms-otp`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.supabaseAnonKey}`,apikey:this.supabaseAnonKey},body:JSON.stringify(i)}),h=await n.text();let l;try{l=JSON.parse(h)}catch{l=h}this.addResult(`Phone Format Test (${a})`,n.ok,`Phone format ${a} ${n.ok?"accepted":"rejected"} (Status: ${n.status})`,{phone:a,payload:i,status:n.status,response:l})}catch(i){this.addResult(`Phone Format Test (${a})`,!1,`Phone format test failed: ${i.message}`,{phone:a,error:i.toString()})}}getResults(){return this.results}getFailures(){return this.results.filter(t=>!t.success)}getSummary(){const t=this.results.length,a=this.results.filter(u=>u.success).length,i=t-a,n=[];return(!this.supabaseUrl||!this.supabaseAnonKey)&&n.push("Supabase configuration missing"),this.apiKey||n.push("Dexatel API Key not configured"),this.results.find(u=>u.step==="Supabase Edge Function"&&!u.success)&&n.push("Supabase Edge Function not accessible"),this.results.find(u=>u.step==="SMS OTP Send Test"&&!u.success)&&n.push("SMS OTP sending failed"),{total:t,passed:a,failed:i,criticalIssues:n}}}const k=new le;function ge(){const{toast:o}=X(),{user:t,isAuthenticated:a,profile:i}=W(),[n,h]=x.useState(!1),[l,u]=x.useState(""),[v,F]=x.useState(""),[P,R]=x.useState(""),[g,m]=x.useState(!1),[j,O]=x.useState([]),A=navigator.onLine,c=(s,r,d,J)=>{const L={id:Date.now(),test:s,success:r,message:d,data:J,timestamp:new Date().toLocaleTimeString()};O(B=>[L,...B.slice(0,9)])},K=()=>{["+213555123456","0555123456","555123456","+213 555 123 456","213555123456","invalid","+212555123456"].forEach(r=>{const d=se(r);c("Phone Validation",d.isValid,`${r} → ${d.isValid?d.formatted:d.error}`,d)})},V=async()=>{if(!l.trim()){o({title:"Error",description:"Please enter a phone number to test",variant:"destructive"});return}m(!0);try{const s=await te(l);s.success?(R(s.verificationId),c("Send OTP",!0,`OTP sent to ${l}`,s),o({title:"OTP Sent",description:`Verification code sent to ${l}`})):(c("Send OTP",!1,s.error,s),o({title:"Failed to Send OTP",description:s.error,variant:"destructive"}))}catch(s){c("Send OTP",!1,`Error: ${s}`,{error:s}),o({title:"Error",description:"Failed to send OTP",variant:"destructive"})}finally{m(!1)}},D=async()=>{if(!P||!v.trim()){o({title:"Error",description:"Please send OTP first and enter the verification code",variant:"destructive"});return}m(!0);try{const s=await ae(P,v);s.success?(c("Verify OTP",!0,`Authentication successful: ${s.action}`,s),o({title:"Verification Successful",description:`${s.action==="signup"?"Account created":"Logged in"} successfully!`})):(c("Verify OTP",!1,s.error,s),o({title:"Verification Failed",description:s.error,variant:"destructive"}))}catch(s){c("Verify OTP",!1,`Error: ${s}`,{error:s}),o({title:"Error",description:"Failed to verify OTP",variant:"destructive"})}finally{m(!1)}},M=async()=>{if(!l.trim()){o({title:"Error",description:"Please enter a phone number to test",variant:"destructive"});return}m(!0);try{const s=await ie(l);s.error?c("Phone Exists",!1,s.error,s):c("Phone Exists",!0,`${l} ${s.exists?"exists":"does not exist"} in database`,s)}catch(s){c("Phone Exists",!1,`Error: ${s}`,{error:s})}finally{m(!1)}},z=async()=>{m(!0),c("API Test",!0,"🔍 Starting COMPREHENSIVE API Investigation...",{});try{const s=await k.runComprehensiveTest(),r=k.getSummary();s.forEach(d=>{c(d.step,d.success,d.message,d.data)}),c("Investigation Summary",r.criticalIssues.length===0,`Tests: ${r.passed}/${r.total} passed. ${r.criticalIssues.length>0?`Critical Issues: ${r.criticalIssues.join(", ")}`:"All systems operational!"}`,r),o({title:r.criticalIssues.length===0?"Investigation Complete":"Issues Found",description:`${r.passed}/${r.total} tests passed. ${r.criticalIssues.length>0?`${r.criticalIssues.length} critical issues found.`:"All systems operational!"}`,variant:r.criticalIssues.length===0?"default":"destructive"})}catch(s){console.error("🚨 CRITICAL ERROR in investigation:",s),c("Investigation Error",!1,`❌ INVESTIGATION FAILED: ${s.message}`,{error:s.toString()}),o({title:"Investigation Failed",description:"Failed to run comprehensive API investigation",variant:"destructive"})}finally{m(!1)}},U=(s,r)=>{c("Modal Auth",!0,`${r} successful via modal`,{user:s,action:r}),h(!1)};return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Phone Authentication Test"}),e.jsx("p",{className:"text-gray-600",children:"Test the phone-only authentication system for Algerian consumers"})]}),e.jsxs(S,{children:[e.jsx(b,{children:e.jsxs(T,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5"}),"System Status"]})}),e.jsxs(N,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ne,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Dexatel API:"}),e.jsx(y,{variant:"default",children:"Configured"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(oe,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Supabase:"}),e.jsx(y,{variant:"default",children:"Connected"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(re,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Network:"}),e.jsx(y,{variant:A?"default":"destructive",children:A?"Online":"Offline"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Z,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Auth Status:"}),e.jsx(y,{variant:a?"default":"secondary",children:a?"Authenticated":"Not Authenticated"})]})]}),a&&t&&e.jsxs("div",{className:"p-3 bg-green-50 border border-green-200 rounded-md",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(I,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{className:"text-sm font-medium text-green-800",children:"Current User"})]}),e.jsxs("div",{className:"text-sm text-green-700 space-y-1",children:[e.jsxs("div",{children:["ID: ",t.id]}),e.jsxs("div",{children:["Phone: ",t.phone||i?.phone||"N/A"]}),e.jsxs("div",{children:["Role: ",i?.role||"N/A"]}),e.jsxs("div",{children:["Verified: ",i?.phone_verified?"Yes":"No"]})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(S,{children:[e.jsxs(b,{children:[e.jsxs(T,{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"h-5 w-5"}),"Manual Testing"]}),e.jsx(C,{children:"Test individual components of the phone authentication system"})]}),e.jsxs(N,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx($,{htmlFor:"test-phone",children:"Test Phone Number"}),e.jsx(w,{id:"test-phone",type:"tel",value:l,onChange:s=>u(s.target.value),placeholder:"+213555123456",className:"font-mono"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx($,{htmlFor:"test-otp",children:"OTP Code (for verification test)"}),e.jsx(w,{id:"test-otp",type:"text",value:v,onChange:s=>F(s.target.value.replace(/\D/g,"").slice(0,6)),placeholder:"123456",className:"font-mono text-center",maxLength:6})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{onClick:z,variant:"destructive",size:"sm",className:"w-full",disabled:g,children:"🔍 DEEP API INVESTIGATION"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx(p,{onClick:K,variant:"outline",size:"sm",children:"Test Validation"}),e.jsx(p,{onClick:M,variant:"outline",size:"sm",disabled:g,children:"Check Exists"}),e.jsx(p,{onClick:V,variant:"outline",size:"sm",disabled:g,children:"Send OTP"}),e.jsx(p,{onClick:D,variant:"outline",size:"sm",disabled:g,children:"Verify OTP"})]})]}),e.jsxs("div",{className:"border-t pt-4 space-y-2",children:[e.jsx(p,{onClick:()=>h(!0),className:"w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90",children:"Test Auth Modal"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx(p,{onClick:()=>E("cart_add"),variant:"outline",size:"sm",children:"Trigger Cart Add"}),e.jsx(p,{onClick:()=>E("checkout"),variant:"outline",size:"sm",children:"Trigger Checkout"})]})]})]})]}),e.jsxs(S,{children:[e.jsxs(b,{children:[e.jsxs(T,{className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-5 w-5"}),"Test Results"]}),e.jsx(C,{children:"Recent test results and system responses"})]}),e.jsxs(N,{children:[e.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:j.length===0?e.jsx("p",{className:"text-gray-500 text-center py-4",children:"No test results yet. Run some tests to see results here."}):j.map(s=>e.jsxs("div",{className:`p-3 rounded-md border ${s.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.success?e.jsx(I,{className:"h-4 w-4 text-green-600"}):e.jsx(H,{className:"h-4 w-4 text-red-600"}),e.jsx("span",{className:"font-medium text-sm",children:s.test}),e.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:s.timestamp})]}),e.jsx("p",{className:`text-sm ${s.success?"text-green-700":"text-red-700"}`,children:s.message}),s.data&&e.jsxs("details",{className:"mt-2",children:[e.jsx("summary",{className:"text-xs text-gray-600 cursor-pointer",children:"View Details"}),e.jsx("pre",{className:"text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto",children:JSON.stringify(s.data,null,2)})]})]},s.id))}),j.length>0&&e.jsx(p,{onClick:()=>O([]),variant:"outline",size:"sm",className:"w-full mt-4",children:"Clear Results"})]})]})]}),e.jsx(Q,{isOpen:n,onClose:()=>h(!1),onSuccess:U}),e.jsx(ee,{delay:1e4,triggers:["cart_add","checkout"],minTimeOnSite:5e3,oncePerSession:!1})]})}export{ge as PhoneAuthTest};
