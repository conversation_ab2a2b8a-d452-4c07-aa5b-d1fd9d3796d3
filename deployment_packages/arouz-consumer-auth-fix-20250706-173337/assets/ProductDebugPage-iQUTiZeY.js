import{j as s,v as x,C as h,k as j,Q as g,m as o,l as p,B as r,ay as b}from"./index-BqaWQJEu.js";import{a as N}from"./centralizedProductData-C8mYdbMZ.js";import{C as u}from"./copy-DcoA3jnA.js";import{E as v}from"./external-link-Cb8gaPCH.js";import"./idGenerator-CHl7dMtk.js";import"./categories-k_ueMvEL.js";function T(){const n=N(),c=n.reduce((e,t)=>{const a=t.category,l=t.marketplaceSection||"unknown";return e[a]||(e[a]={}),e[a][l]||(e[a][l]=[]),e[a][l].push(t),e},{}),m=e=>{navigator.clipboard.writeText(e),b.success(`Copied "${e}" to clipboard`)},d=e=>{window.open(`/${e}`,"_blank")};return s.jsx(x,{children:s.jsxs("div",{className:"container py-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Product Debug Page"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"This page shows all available product IDs in the system for testing purposes."}),s.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[s.jsx("h3",{className:"font-semibold text-blue-900 mb-2",children:"Available Product IDs"}),s.jsxs("p",{className:"text-sm text-blue-800",children:["Total products: ",s.jsx("strong",{children:n.length})," | Categories: ",s.jsx("strong",{children:Object.keys(c).join(", ")})]})]})]}),Object.entries(c).map(([e,t])=>s.jsxs("div",{className:"mb-8",children:[s.jsxs("h2",{className:"text-2xl font-bold mb-4 capitalize",children:[e," Products"]}),Object.entries(t).map(([a,l])=>s.jsxs(h,{className:"mb-6",children:[s.jsx(j,{children:s.jsxs(g,{className:"flex items-center justify-between",children:[s.jsxs("span",{className:"capitalize",children:[a," ",e]}),s.jsxs(o,{variant:"outline",children:[l.length," products"]})]})}),s.jsxs(p,{children:[s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:l.slice(0,12).map(i=>s.jsx("div",{className:"p-4 border rounded-lg hover:shadow-md transition-shadow",children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(o,{variant:"secondary",className:"text-xs font-mono",children:i.id}),s.jsxs("div",{className:"flex space-x-1",children:[s.jsx(r,{size:"sm",variant:"ghost",onClick:()=>m(i.id),className:"h-6 w-6 p-0",children:s.jsx(u,{className:"h-3 w-3"})}),s.jsx(r,{size:"sm",variant:"ghost",onClick:()=>d(i.id),className:"h-6 w-6 p-0",children:s.jsx(v,{className:"h-3 w-3"})})]})]}),s.jsx("h4",{className:"text-sm font-medium line-clamp-2",children:i.name}),s.jsx("p",{className:"text-xs text-gray-600",children:i.manufacturer}),s.jsxs("div",{className:"text-xs text-gray-500",children:["Stock: ",i.stockQuantity]})]})},i.id))}),l.length>12&&s.jsx("div",{className:"mt-4 text-center",children:s.jsxs("p",{className:"text-sm text-gray-600",children:["Showing 12 of ",l.length," products"]})})]})]},a))]},e)),s.jsxs("div",{className:"mt-8 p-6 bg-gray-50 rounded-lg",children:[s.jsx("h3",{className:"text-lg font-bold mb-4",children:"Quick Test Links"}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:n.slice(0,8).map(e=>s.jsx(r,{variant:"outline",size:"sm",onClick:()=>d(e.id),className:"text-xs",children:e.id},e.id))})]})]})})}export{T as default};
