import{c as A,j as e,C as m,aZ as v,k as u,l as h,Q as g,m as N,E as Y,bz as K,bB as X,a3 as B,P as w,B as j,U as S,ab as b,u as L,ag as _,a6 as C,H as V,N as J,K as F,a0 as P,bb as ee,a7 as se}from"./index-BqaWQJEu.js";import{u as ae,a as te,A as re}from"./AdminLayout-DWILQ77Q.js";import{T as ie}from"./trending-down-CioHxDIi.js";import{T as Z}from"./trending-up-BJf9ffMX.js";import{T as R,a as T,b as f,c as n,d as O,e as c}from"./table-Dnlym_GZ.js";import{P as le}from"./phone-DMHWBins.js";import{D as I}from"./dollar-sign-DOjqCwj1.js";import{U as H}from"./users-Bo-kpa8Q.js";import{C as ne}from"./credit-card-BNXjSr_E.js";import{T as ce}from"./target-BQi2dBE9.js";import"./house-Bq7RaxAv.js";import"./settings-PBinnTtm.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=A("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=A("Repeat",[["path",{d:"m17 2 4 4-4 4",key:"nntrym"}],["path",{d:"M3 11v-1a4 4 0 0 1 4-4h14",key:"84bu3i"}],["path",{d:"m7 22-4-4 4-4",key:"1wqhfi"}],["path",{d:"M21 13v1a4 4 0 0 1-4 4H3",key:"1rx37r"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=A("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=A("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=A("Warehouse",[["path",{d:"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z",key:"gksnxg"}],["path",{d:"M6 18h12",key:"9pbo8z"}],["path",{d:"M6 14h12",key:"4cwo0f"}],["rect",{width:"12",height:"12",x:"6",y:"10",key:"apd30q"}]]),ue={default:{card:"border-gray-200 hover:border-gray-300",icon:"text-gray-600",background:"bg-gray-50"},revenue:{card:"border-green-200 hover:border-green-300 hover:shadow-green-100",icon:"text-green-600",background:"bg-green-50"},orders:{card:"border-[#fa7b00]/20 hover:border-[#fa7b00]/40 hover:shadow-orange-100",icon:"text-[#fa7b00]",background:"bg-[#ffd9a3]/20"},products:{card:"border-blue-200 hover:border-blue-300 hover:shadow-blue-100",icon:"text-blue-600",background:"bg-blue-50"},customers:{card:"border-[#071c44]/20 hover:border-[#071c44]/40 hover:shadow-blue-100",icon:"text-[#071c44]",background:"bg-[#071c44]/5"}};function o({title:t,value:s,subtitle:a,icon:r,trend:i,loading:l=!1,className:d,variant:x="default",onClick:U}){const D=ue[x],G=y=>typeof y=="number"?y>=1e6?`${(y/1e6).toFixed(1)}M`:y>=1e3?`${(y/1e3).toFixed(1)}K`:y%1!==0?y.toFixed(2):y.toString():y,W=()=>{if(!i)return null;switch(i.direction){case"up":return e.jsx(Z,{className:"h-3 w-3"});case"down":return e.jsx(ie,{className:"h-3 w-3"});default:return e.jsx(Y,{className:"h-3 w-3"})}},Q=()=>{if(!i)return"";switch(i.direction){case"up":return"text-green-600 bg-green-50 border-green-200";case"down":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}};return l?e.jsxs(m,{className:v("transition-all duration-200",D.card,d),children:[e.jsxs(u,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx("div",{className:"h-4 w-24 bg-gray-200 rounded animate-pulse"}),e.jsx("div",{className:v("h-8 w-8 rounded-full animate-pulse",D.background)})]}),e.jsxs(h,{children:[e.jsx("div",{className:"h-8 w-16 bg-gray-200 rounded animate-pulse mb-2"}),e.jsx("div",{className:"h-3 w-20 bg-gray-200 rounded animate-pulse"})]})]}):e.jsxs(m,{className:v("transition-all duration-200 hover:shadow-md",D.card,U&&"cursor-pointer hover:scale-[1.02]",d),onClick:U,children:[e.jsxs(u,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(g,{className:"text-sm font-medium text-gray-700",children:t}),r&&e.jsx("div",{className:v("p-2 rounded-full",D.background),children:e.jsx(r,{className:v("h-4 w-4",D.icon)})})]}),e.jsxs(h,{children:[e.jsxs("div",{className:"flex items-baseline justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:G(s)}),a&&e.jsx("p",{className:"text-xs text-gray-600 mt-1",children:a})]}),i&&e.jsxs(N,{variant:"outline",className:v("flex items-center gap-1 text-xs font-medium",Q()),children:[W(),i.value>0&&"+",i.value,"%"]})]}),i&&i.label&&e.jsx("p",{className:"text-xs text-gray-500 mt-2",children:i.label})]})]})}function p({title:t,value:s,currency:a="DZD",...r}){const i=l=>new Intl.NumberFormat("en-DZ",{style:"currency",currency:a,minimumFractionDigits:0,maximumFractionDigits:0}).format(l);return e.jsx(o,{...r,title:t,value:i(s),variant:"revenue"})}function z({value:t,...s}){return e.jsx(o,{...s,value:`${t.toFixed(1)}%`})}function k({value:t,...s}){return e.jsx(o,{...s,value:t.toLocaleString()})}function M({title:t,subtitle:s,children:a,className:r,loading:i=!1,onViewAll:l}){return i?e.jsxs(m,{className:v("",r),children:[e.jsx(u,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"h-5 w-32 bg-gray-200 rounded animate-pulse mb-2"}),s&&e.jsx("div",{className:"h-3 w-48 bg-gray-200 rounded animate-pulse"})]}),e.jsx("div",{className:"h-8 w-16 bg-gray-200 rounded animate-pulse"})]})}),e.jsx(h,{children:e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((d,x)=>e.jsx("div",{className:"h-12 bg-gray-100 rounded animate-pulse"},x))})})]}):e.jsxs(m,{className:v("",r),children:[e.jsx(u,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(g,{className:"text-lg font-semibold text-gray-900",children:t}),s&&e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:s})]}),l&&e.jsx(j,{variant:"outline",size:"sm",onClick:l,children:"View All"})]})}),e.jsx(h,{className:"p-0",children:a})]})}function he({customers:t,loading:s,onViewAll:a}){return e.jsx(M,{title:"Top Customers",subtitle:"Your most valuable merchant partners",loading:s,onViewAll:a,children:e.jsxs(R,{children:[e.jsx(T,{children:e.jsxs(f,{children:[e.jsx(n,{children:"Customer"}),e.jsx(n,{children:"Orders"}),e.jsx(n,{children:"Total Spent"}),e.jsx(n,{children:"Location"}),e.jsx(n,{children:"Last Order"})]})}),e.jsx(O,{children:t.map(r=>e.jsxs(f,{className:"hover:bg-gray-50",children:[e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(K,{className:"h-8 w-8",children:e.jsx(X,{className:"bg-[#ffd9a3] text-[#fa7b00] text-xs",children:r.name.split(" ").map(i=>i[0]).join("").slice(0,2)})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:r.name}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[e.jsx(le,{className:"h-3 w-3"}),r.phone]})]})]})}),e.jsx(c,{children:e.jsxs(N,{variant:"outline",className:"text-xs",children:[r.totalOrders," orders"]})}),e.jsx(c,{className:"font-medium",children:new Intl.NumberFormat("en-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0}).format(r.totalSpent)}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[e.jsx(B,{className:"h-3 w-3"}),r.wilaya]})}),e.jsx(c,{className:"text-sm text-gray-600",children:new Date(r.lastOrderDate).toLocaleDateString()})]},r.id))})]})})}function $({products:t,loading:s,onViewAll:a}){return e.jsx(M,{title:"Top Selling Products",subtitle:"Your best performing products",loading:s,onViewAll:a,children:e.jsxs(R,{children:[e.jsx(T,{children:e.jsxs(f,{children:[e.jsx(n,{children:"Product"}),e.jsx(n,{children:"Category"}),e.jsx(n,{children:"Sold"}),e.jsx(n,{children:"Revenue"}),e.jsx(n,{children:"Stock"})]})}),e.jsx(O,{children:t.map(r=>e.jsxs(f,{className:"hover:bg-gray-50",children:[e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[r.image?e.jsx("img",{src:r.image,alt:r.name,className:"h-8 w-8 rounded object-cover"}):e.jsx("div",{className:"h-8 w-8 rounded bg-gray-100 flex items-center justify-center",children:e.jsx(w,{className:"h-4 w-4 text-gray-400"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:r.name}),e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",r.id]})]})]})}),e.jsx(c,{children:e.jsx(N,{variant:"outline",className:"text-xs",children:r.category})}),e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium",children:r.totalSold}),r.growth>0&&e.jsx(Z,{className:"h-3 w-3 text-green-600"})]})}),e.jsx(c,{className:"font-medium",children:new Intl.NumberFormat("en-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0}).format(r.revenue)}),e.jsx(c,{children:e.jsxs(N,{variant:r.stockQuantity>10?"default":"destructive",className:"text-xs",children:[r.stockQuantity," units"]})})]},r.id))})]})})}function q({orders:t,loading:s,onViewAll:a}){const r=i=>{const l={pending:{variant:"secondary",label:"Pending"},confirmed:{variant:"default",label:"Confirmed"},shipped:{variant:"outline",label:"Shipped"},delivered:{variant:"default",label:"Delivered"},cancelled:{variant:"destructive",label:"Cancelled"}},d=l[i]||l.pending;return e.jsx(N,{variant:d.variant,className:"text-xs",children:d.label})};return e.jsx(M,{title:"Recent Orders",subtitle:"Latest orders from your customers",loading:s,onViewAll:a,children:e.jsxs(R,{children:[e.jsx(T,{children:e.jsxs(f,{children:[e.jsx(n,{children:"Order"}),e.jsx(n,{children:"Customer"}),e.jsx(n,{children:"Status"}),e.jsx(n,{children:"Amount"}),e.jsx(n,{children:"Date"})]})}),e.jsx(O,{children:t.map(i=>e.jsxs(f,{className:"hover:bg-gray-50",children:[e.jsx(c,{children:e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-sm",children:["#",i.orderNumber]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[i.itemCount," items"]})]})}),e.jsx(c,{children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:i.customerName}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[e.jsx(B,{className:"h-3 w-3"}),i.deliveryWilaya]})]})}),e.jsx(c,{children:r(i.status)}),e.jsx(c,{className:"font-medium",children:new Intl.NumberFormat("en-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0}).format(i.totalAmount)}),e.jsx(c,{className:"text-sm text-gray-600",children:new Date(i.createdAt).toLocaleDateString()})]},i.id))})]})})}function je({alerts:t,loading:s,onViewAll:a}){const r=l=>{switch(l){case"out_of_stock":return e.jsx(b,{className:"h-4 w-4 text-red-600"});case"low_stock":return e.jsx(b,{className:"h-4 w-4 text-yellow-600"});case"reorder_needed":return e.jsx(w,{className:"h-4 w-4 text-orange-600"});default:return e.jsx(S,{className:"h-4 w-4 text-green-600"})}},i=l=>{const d={out_of_stock:{variant:"destructive",label:"Out of Stock"},low_stock:{variant:"secondary",label:"Low Stock"},reorder_needed:{variant:"outline",label:"Reorder Needed"}},x=d[l]||d.low_stock;return e.jsx(N,{variant:x.variant,className:"text-xs",children:x.label})};return e.jsx(M,{title:"Inventory Alerts",subtitle:"Products requiring attention",loading:s,onViewAll:a,children:e.jsxs(R,{children:[e.jsx(T,{children:e.jsxs(f,{children:[e.jsx(n,{children:"Product"}),e.jsx(n,{children:"Alert Type"}),e.jsx(n,{children:"Current Stock"}),e.jsx(n,{children:"Category"}),e.jsx(n,{children:"Action"})]})}),e.jsx(O,{children:t.map(l=>e.jsxs(f,{className:"hover:bg-gray-50",children:[e.jsx(c,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[r(l.alertType),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:l.productName}),e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",l.productId]})]})]})}),e.jsx(c,{children:i(l.alertType)}),e.jsx(c,{children:e.jsxs("span",{className:v("font-medium",l.currentStock===0?"text-red-600":l.currentStock<=5?"text-yellow-600":"text-gray-900"),children:[l.currentStock," units"]})}),e.jsx(c,{children:e.jsx(N,{variant:"outline",className:"text-xs",children:l.category})}),e.jsx(c,{children:e.jsx(j,{variant:"outline",size:"sm",children:"Restock"})})]},l.id))})]})})}function ge(){L();const t=_(),{data:s,isLoading:a,isError:r,error:i,refresh:l}=ae();return r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(b,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error Loading Dashboard"}),e.jsx("p",{className:"text-gray-600 mb-4",children:i?.message||"Failed to load dashboard data"}),e.jsxs(j,{onClick:l,variant:"outline",children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Supplier Dashboard"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Overview of your business performance and key metrics"})]}),e.jsxs(j,{onClick:l,disabled:a,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(C,{className:v("h-4 w-4",a&&"animate-spin")}),"Refresh"]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsx(p,{title:"Total Revenue",value:s?.totalRevenue||0,subtitle:"All time earnings",icon:I,trend:{value:s?.revenueGrowth||0,label:"vs last month",direction:(s?.revenueGrowth||0)>=0?"up":"down"},loading:a}),e.jsx(k,{title:"Total Orders",value:s?.totalOrders||0,subtitle:"All time orders",icon:V,variant:"orders",loading:a}),e.jsx(k,{title:"Active Products",value:s?.activeProducts||0,subtitle:`${s?.totalProducts||0} total products`,icon:w,variant:"products",loading:a}),e.jsx(k,{title:"Total Customers",value:s?.customerInsights?.totalCustomers||0,subtitle:`${s?.customerInsights?.newCustomersThisMonth||0} new this month`,icon:H,variant:"customers",loading:a})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsx(p,{title:"Monthly Revenue",value:s?.monthlyRevenue||0,subtitle:"Current month",loading:a}),e.jsx(p,{title:"Weekly Revenue",value:s?.weeklyRevenue||0,subtitle:"This week",loading:a}),e.jsx(p,{title:"Daily Revenue",value:s?.dailyRevenue||0,subtitle:"Today",loading:a})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsx(o,{title:"Pending Orders",value:s?.pendingOrders||0,subtitle:"Awaiting confirmation",icon:J,variant:"orders",loading:a,onClick:()=>t("/app/orders?status=pending")}),e.jsx(o,{title:"Confirmed Orders",value:s?.confirmedOrders||0,subtitle:"Ready for shipping",icon:S,variant:"orders",loading:a,onClick:()=>t("/app/orders?status=confirmed")}),e.jsx(o,{title:"Shipped Orders",value:s?.shippedOrders||0,subtitle:"In transit",icon:F,loading:a,onClick:()=>t("/app/orders?status=shipped")}),e.jsx(o,{title:"Delivered Orders",value:s?.deliveredOrders||0,subtitle:"Successfully delivered",icon:S,loading:a,onClick:()=>t("/app/orders?status=delivered")})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsx(o,{title:"Out of Stock",value:s?.outOfStockProducts||0,subtitle:"Products need restocking",icon:b,variant:"products",loading:a,onClick:()=>t("/app/products?status=out_of_stock")}),e.jsx(o,{title:"Low Stock",value:s?.lowStockProducts||0,subtitle:"Products running low",icon:E,variant:"products",loading:a,onClick:()=>t("/app/products?status=low_stock")}),e.jsx(o,{title:"Orders Awaiting Shipment",value:s?.ordersAwaitingShipment||0,subtitle:"Need shipping assignment",icon:F,variant:"orders",loading:a,onClick:()=>t("/app/shipping")})]}),e.jsxs(m,{children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(ne,{className:"h-5 w-5 text-[#fa7b00]"}),"Financial Summary"]})}),e.jsx(h,{children:a?e.jsx("div",{className:"grid gap-4 md:grid-cols-4",children:[...Array(4)].map((d,x)=>e.jsx("div",{className:"h-16 bg-gray-200 rounded animate-pulse"},x))}):e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:new Intl.NumberFormat("en-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0}).format(s?.financialSummary?.netRevenue||0)}),e.jsx("div",{className:"text-sm text-gray-600",children:"Net Revenue"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:new Intl.NumberFormat("en-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0}).format(s?.financialSummary?.pendingPayments||0)}),e.jsx("div",{className:"text-sm text-gray-600",children:"Pending Payments"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:s?.financialSummary?.paidOrders||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Paid Orders"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:new Intl.NumberFormat("en-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0}).format(s?.financialSummary?.arouzFees||0)}),e.jsx("div",{className:"text-sm text-gray-600",children:"AROUZ Fees"})]})]})})]}),e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsx(he,{customers:s?.topMerchantCustomers||[],loading:a,onViewAll:()=>t("/app/customers")}),e.jsx($,{products:s?.topSellingProducts||[],loading:a,onViewAll:()=>t("/app/products")})]}),e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsx(q,{orders:s?.recentOrders||[],loading:a,onViewAll:()=>t("/app/orders")}),e.jsx(je,{alerts:s?.inventoryAlerts||[],loading:a,onViewAll:()=>t("/app/inventory")})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs(m,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t("/app/products/add"),children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5 text-[#fa7b00]"}),"Add New Product"]})}),e.jsxs(h,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Add new products to your inventory and start selling"}),e.jsxs(j,{className:"w-full bg-[#fa7b00] hover:bg-[#e56f00]",children:["Add Product ",e.jsx(P,{className:"h-4 w-4 ml-2"})]})]})]}),e.jsxs(m,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t("/app/orders"),children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-5 w-5 text-[#fa7b00]"}),"Manage Orders"]})}),e.jsxs(h,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"View and manage your orders, assign shipping companies"}),e.jsxs(j,{variant:"outline",className:"w-full",children:["View Orders ",e.jsx(P,{className:"h-4 w-4 ml-2"})]})]})]})]})]})}function ve(){L();const t=_(),{data:s,isLoading:a,isError:r,error:i,refresh:l}=te();return r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(b,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Error Loading Dashboard"}),e.jsx("p",{className:"text-gray-600 mb-4",children:i?.message||"Failed to load dashboard data"}),e.jsxs(j,{onClick:l,variant:"outline",children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Merchant Dashboard"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Overview of your store performance and customer insights"})]}),e.jsxs(j,{onClick:l,disabled:a,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(C,{className:v("h-4 w-4",a&&"animate-spin")}),"Refresh"]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsx(p,{title:"Total Revenue",value:s?.totalRevenue||0,subtitle:"All time earnings",icon:I,trend:{value:s?.revenueGrowth||0,label:"vs last month",direction:(s?.revenueGrowth||0)>=0?"up":"down"},loading:a}),e.jsx(k,{title:"Total Orders",value:s?.totalOrders||0,subtitle:"All time orders",icon:V,variant:"orders",loading:a}),e.jsx(k,{title:"Active Products",value:s?.activeProducts||0,subtitle:`${s?.totalProducts||0} total products`,icon:w,variant:"products",loading:a}),e.jsx(k,{title:"Total Customers",value:s?.customerAnalytics?.totalCustomers||0,subtitle:`${s?.customerAnalytics?.newCustomers||0} new this month`,icon:H,variant:"customers",loading:a})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsx(p,{title:"Monthly Revenue",value:s?.monthlyRevenue||0,subtitle:"Current month",loading:a}),e.jsx(p,{title:"Weekly Revenue",value:s?.weeklyRevenue||0,subtitle:"This week",loading:a}),e.jsx(p,{title:"Daily Revenue",value:s?.dailyRevenue||0,subtitle:"Today",loading:a}),e.jsx(p,{title:"Average Order Value",value:s?.averageOrderValue||0,subtitle:"Per order",loading:a})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsx(o,{title:"New Customers",value:s?.customerAnalytics?.newCustomers||0,subtitle:"This month",icon:xe,variant:"customers",loading:a}),e.jsx(o,{title:"Returning Customers",value:s?.customerAnalytics?.returningCustomers||0,subtitle:"Repeat buyers",icon:oe,variant:"customers",loading:a}),e.jsx(z,{title:"Repeat Customer Rate",value:s?.customerAnalytics?.repeatCustomerRate||0,subtitle:"Customer retention",icon:ce,loading:a}),e.jsx(z,{title:"Conversion Rate",value:s?.conversionRate||0,subtitle:"Visitors to buyers",icon:Z,loading:a})]}),e.jsxs(m,{children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(de,{className:"h-5 w-5 text-[#fa7b00]"}),"Store Performance"]})}),e.jsx(h,{children:a?e.jsx("div",{className:"grid gap-4 md:grid-cols-3",children:[...Array(3)].map((d,x)=>e.jsx("div",{className:"h-16 bg-gray-200 rounded animate-pulse"},x))}):e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:s?.storePerformance?.productViews?.toLocaleString()||0}),e.jsx("div",{className:"text-sm text-gray-600",children:"Product Views"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[s?.storePerformance?.conversionRate?.toFixed(1)||0,"%"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Conversion Rate"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[s?.customerAnalytics?.customerLifetimeValue?.toLocaleString()||0," DZD"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Customer Lifetime Value"})]})]})})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsx(o,{title:"In Stock",value:s?.inventoryStatus?.inStock||0,subtitle:"Products available",icon:S,variant:"products",loading:a,onClick:()=>t("/app/products?status=active")}),e.jsx(o,{title:"Low Stock",value:s?.inventoryStatus?.lowStock||0,subtitle:"Need restocking",icon:b,variant:"products",loading:a,onClick:()=>t("/app/products?status=low_stock")}),e.jsx(o,{title:"Out of Stock",value:s?.inventoryStatus?.outOfStock||0,subtitle:"Unavailable products",icon:E,variant:"products",loading:a,onClick:()=>t("/app/products?status=out_of_stock")}),e.jsx(p,{title:"Inventory Value",value:s?.inventoryStatus?.totalValue||0,subtitle:"Total stock value",icon:I,loading:a})]}),e.jsxs(m,{children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5 text-[#fa7b00]"}),"Popular Categories"]})}),e.jsx(h,{children:a?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((d,x)=>e.jsx("div",{className:"h-12 bg-gray-200 rounded animate-pulse"},x))}):e.jsx("div",{className:"space-y-3",children:s?.storePerformance?.popularCategories?.slice(0,5).map((d,x)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(N,{variant:"outline",className:"text-xs",children:["#",x+1]}),e.jsx("span",{className:"font-medium",children:d.category})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-medium",children:[d.views," views"]}),e.jsxs("div",{className:"text-xs text-gray-600",children:[d.sales," sales"]})]})]},d.category))||e.jsx("div",{className:"text-center text-gray-500 py-8",children:"No category data available"})})})]}),e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsx($,{products:s?.topSellingProducts||[],loading:a,onViewAll:()=>t("/app/products")}),e.jsx(q,{orders:s?.recentOrders||[],loading:a,onViewAll:()=>t("/app/orders")})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs(m,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t("/app/products/add"),children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5 text-[#fa7b00]"}),"Add New Product"]})}),e.jsxs(h,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Add new products to your store and expand your catalog"}),e.jsxs(j,{className:"w-full bg-[#fa7b00] hover:bg-[#e56f00]",children:["Add Product ",e.jsx(P,{className:"h-4 w-4 ml-2"})]})]})]}),e.jsxs(m,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t("/app/orders"),children:[e.jsx(u,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5 text-[#fa7b00]"}),"Manage Orders"]})}),e.jsxs(h,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"View and manage customer orders and delivery status"}),e.jsxs(j,{variant:"outline",className:"w-full",children:["View Orders ",e.jsx(P,{className:"h-4 w-4 ml-2"})]})]})]})]})]})}function pe(){const{userRole:t,isSupplier:s,isMerchant:a}=ee(),{isAuthenticated:r}=se(),i=_();return r?!s()&&!a()?e.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:e.jsxs(m,{className:"w-full max-w-md",children:[e.jsxs(u,{className:"text-center",children:[e.jsx(b,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),e.jsx(g,{className:"text-xl text-gray-900",children:"Invalid User Role"})]}),e.jsxs(h,{className:"text-center",children:[e.jsxs("p",{className:"text-gray-600 mb-6",children:["Your account role (",t,") does not have access to this dashboard. Only Suppliers and Merchants can access the dashboard."]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(j,{onClick:()=>i("/partners"),variant:"outline",className:"w-full",children:"Switch Account"}),e.jsxs(j,{onClick:()=>window.location.reload(),variant:"outline",className:"w-full",children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Refresh Page"]})]})]})]})}):s()?e.jsx(ge,{}):a()?e.jsx(ve,{}):e.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:e.jsxs(m,{className:"w-full max-w-md",children:[e.jsxs(u,{className:"text-center",children:[e.jsx(b,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx(g,{className:"text-xl text-gray-900",children:"Dashboard Error"})]}),e.jsxs(h,{className:"text-center",children:[e.jsx("p",{className:"text-gray-600 mb-6",children:"Unable to determine the appropriate dashboard for your account. Please contact support if this issue persists."}),e.jsxs(j,{onClick:()=>window.location.reload(),variant:"outline",className:"w-full",children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Refresh Page"]})]})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:e.jsxs(m,{className:"w-full max-w-md",children:[e.jsxs(u,{className:"text-center",children:[e.jsx(me,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx(g,{className:"text-xl text-gray-900",children:"Authentication Required"})]}),e.jsxs(h,{className:"text-center",children:[e.jsx("p",{className:"text-gray-600 mb-6",children:"Please log in to access your dashboard."}),e.jsx(j,{onClick:()=>i("/partners"),className:"w-full bg-[#fa7b00] hover:bg-[#e56f00]",children:"Go to Login"})]})]})})}function Te(){return L(),e.jsx(re,{children:e.jsx(pe,{})})}export{Te as default};
