import{a as b,r as n,j as e,C as x,k as E,Q as f,l as D,L as G,I,B as y,a1 as l}from"./index-BqaWQJEu.js";import{b as O}from"./supplierOrderService-By3K6t1q.js";import{b as S}from"./shippingCompanyOrderService-BLuCWDMA.js";import"./shippingCompanyAuthService-BKBSbLEP.js";function q(){const{toast:j}=b(),[s,B]=n.useState(""),[d,t]=n.useState(null),[c,i]=n.useState(!1),v=async()=>{if(!s.trim()){j({title:"Error",description:"Please enter an order ID",variant:"destructive"});return}i(!0),t(null);try{console.log("🔍 [DEBUG] Testing order access for:",s);const{data:{user:r},error:o}=await l.auth.getUser();if(console.log("🔍 [DEBUG] Current user:",r),o||!r){t({error:"Not authenticated",user_error:o});return}console.log("🔍 [DEBUG] Testing direct database query...");const{data:u,error:g}=await l.from("orders").select(`
          *,
          order_items (*)
        `).eq("id",s).single();console.log("🔍 [DEBUG] Direct query result:",{directOrder:u,directError:g}),console.log("🔍 [DEBUG] Testing supplier service...");const p=await O(s);console.log("🔍 [DEBUG] Supplier service result:",p),console.log("🔍 [DEBUG] Testing shipping service...");let a=null;try{a=await S(s),console.log("🔍 [DEBUG] Shipping service result:",a)}catch(_){console.log("🔍 [DEBUG] Shipping service error (expected if not shipping company):",_)}console.log("🔍 [DEBUG] Testing order items query...");const{data:m,error:h}=await l.from("order_items").select("*").eq("order_id",s);console.log("🔍 [DEBUG] Order items query result:",{orderItems:m,itemsError:h}),t({user:{id:r.id,email:r.email},direct_query:{order:u,error:g},supplier_service:p,shipping_service:a,order_items:{items:m,error:h}})}catch(r){console.error("❌ [DEBUG] Error during testing:",r),t({error:"Exception during testing",exception:r})}finally{i(!1)}},U=async()=>{i(!0);try{console.log("🔍 [DEBUG] Listing all orders...");const{data:r,error:o}=await l.from("orders").select(`
          id,
          order_number,
          status,
          created_at,
          order_items (
            id,
            supplier_account_id,
            product_name
          )
        `).order("created_at",{ascending:!1}).limit(10);console.log("🔍 [DEBUG] All orders result:",{orders:r,error:o}),t({all_orders:{orders:r,error:o,count:r?.length||0}})}catch(r){console.error("❌ [DEBUG] Error listing orders:",r),t({error:"Exception during listing",exception:r})}finally{i(!1)}};return e.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[e.jsxs(x,{children:[e.jsx(E,{children:e.jsx(f,{children:"Order System Debug Tool"})}),e.jsx(D,{className:"space-y-4",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(G,{htmlFor:"orderId",children:"Order ID"}),e.jsx(I,{id:"orderId",value:s,onChange:r=>B(r.target.value),placeholder:"Enter order ID to test"})]}),e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsx(y,{onClick:v,disabled:c,children:"Test Order Access"}),e.jsx(y,{onClick:U,disabled:c,variant:"outline",children:"List All Orders"})]})]})})]}),d&&e.jsxs(x,{children:[e.jsx(E,{children:e.jsx(f,{children:"Debug Results"})}),e.jsx(D,{children:e.jsx("pre",{className:"bg-gray-100 p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(d,null,2)})})]})]})})}export{q as default};
