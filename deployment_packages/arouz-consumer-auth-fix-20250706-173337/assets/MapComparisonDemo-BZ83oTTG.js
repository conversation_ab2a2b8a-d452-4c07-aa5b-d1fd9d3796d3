import{a2 as x,j as e,m,S as o,C as a,k as l,Q as c,c0 as r,l as i,c1 as h,c2 as j,c3 as p,c4 as g,a3 as t}from"./index-BqaWQJEu.js";import{Z as u}from"./zap-WeyPop26.js";function f(){const{selectedLocation:s,getLocationString:n}=x();return e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"container mx-auto px-4 max-w-6xl",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-midnight-blue mb-2",children:"🗺️ Map Implementation Comparison"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"See the difference between Google Maps and OpenStreetMap implementations"}),e.jsxs(m,{className:"bg-green-100 text-green-800",children:[e.jsx(o,{className:"h-3 w-3 mr-1"}),"Google Maps API Enabled"]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs(a,{className:"h-fit",children:[e.jsxs(l,{children:[e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(u,{className:"h-5 w-5 text-green-500"}),e.jsx("span",{children:"Google Maps (Enhanced)"})]}),e.jsx(r,{children:"Rich location data with Arabic support"})]}),e.jsx(i,{className:"space-y-4",children:e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"🔍 Google Places Search"}),e.jsx(h,{placeholder:"Search for places in Algeria...",onPlaceSelect:d=>{console.log("Google place selected:",d)}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"🗺️ Interactive Google Map"}),e.jsx(j,{height:"300px",showCurrentLocationButton:!0})]}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("h4",{className:"font-medium text-green-700",children:"✅ Features:"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600",children:[e.jsx("li",{children:"• Rich Arabic place names"}),e.jsx("li",{children:"• Google Places autocomplete"}),e.jsx("li",{children:"• Street-level accuracy"}),e.jsx("li",{children:"• Landmarks & businesses"}),e.jsx("li",{children:"• Familiar Google Maps UI"}),e.jsx("li",{children:"• Drag & drop markers"})]})]})]})})]}),e.jsxs(a,{className:"h-fit",children:[e.jsxs(l,{children:[e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(p,{className:"h-5 w-5 text-blue-500"}),e.jsx("span",{children:"OpenStreetMap (Standard)"})]}),e.jsx(r,{children:"Reliable fallback with basic functionality"})]}),e.jsxs(i,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"🗺️ Interactive OpenStreetMap"}),e.jsx(g,{height:"300px",showCurrentLocationButton:!0})]}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("h4",{className:"font-medium text-blue-700",children:"📍 Features:"}),e.jsxs("ul",{className:"text-sm space-y-1 text-gray-600",children:[e.jsx("li",{children:"• Basic map functionality"}),e.jsx("li",{children:"• Click-based selection"}),e.jsx("li",{children:"• No API key required"}),e.jsx("li",{children:"• Always available"}),e.jsx("li",{children:"• Open source maps"}),e.jsx("li",{children:"• Drag & drop markers"})]})]}),e.jsxs("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[e.jsx("h5",{className:"font-medium text-blue-800 mb-1",children:"💡 Advantages:"}),e.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:"• No external dependencies"}),e.jsx("li",{children:"• Works offline (cached)"}),e.jsx("li",{children:"• Privacy-focused"}),e.jsx("li",{children:"• No usage limits"})]})]})]})]})]}),e.jsxs(a,{className:"mb-6",children:[e.jsxs(l,{children:[e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(t,{className:"h-5 w-5 text-[#fa7b00]"}),e.jsx("span",{children:"Shared Features (Both Maps)"})]}),e.jsx(r,{children:"Core functionality available regardless of map provider"})]}),e.jsx(i,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🎯"}),e.jsx("h3",{className:"font-medium text-green-800",children:"100% Accurate"}),e.jsx("p",{className:"text-sm text-green-600",children:"Wilaya detection system"})]}),e.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl mb-2",children:"📱"}),e.jsx("h3",{className:"font-medium text-blue-800",children:"Mobile Optimized"}),e.jsx("p",{className:"text-sm text-blue-600",children:"Touch-friendly interface"})]}),e.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🔄"}),e.jsx("h3",{className:"font-medium text-purple-800",children:"Smart Fallback"}),e.jsx("p",{className:"text-sm text-purple-600",children:"Always functional"})]})]})})]}),s&&e.jsxs(a,{children:[e.jsx(l,{children:e.jsxs(c,{className:"flex items-center space-x-2",children:[e.jsx(t,{className:"h-5 w-5 text-[#fa7b00]"}),e.jsx("span",{children:"Selected Location (Both Maps)"})]})}),e.jsxs(i,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Location Details:"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Address:"})," ",n()]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Wilaya (French):"})," ",s.wilaya]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Wilaya (Arabic):"})," ",s.wilaya_ar]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-800 mb-2",children:"Coordinates:"}),e.jsxs("div",{className:"space-y-2 text-sm font-mono",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Latitude:"})," ",s.coordinates.lat.toFixed(6)]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Longitude:"})," ",s.coordinates.lng.toFixed(6)]})]})]})]}),e.jsx("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{className:"text-sm font-medium text-green-800",children:"✅ Location validated with 100% accurate Algeria boundaries"})]})})]})]})]})})}export{f as MapComparisonDemo};
