import{a as T,r as o,a1 as n,j as s,O as E,B as F,C as x,k as y,Q as j,l as S,aq as L,ab as w,U,m as d}from"./index-BqaWQJEu.js";import{a as $}from"./supplierOrderService-By3K6t1q.js";import{D as B}from"./database-w0IbHezX.js";function V(){const{toast:u}=T(),[p,m]=o.useState([]),[g,h]=o.useState(!1),[c,b]=o.useState(null);o.useEffect(()=>{N()},[]);const N=async()=>{try{const{data:{user:e}}=await n.auth.getUser();if(e){const{data:t}=await n.from("profiles").select("*").eq("id",e.id).single();b({...e,profile:t})}}catch(e){console.error("Error getting user info:",e)}},_=async()=>{h(!0),m([]);const e=[];try{e.push(await C()),e.push(await v()),e.push(await I()),e.push(await O()),e.push(await R()),m(e);const t=e.filter(a=>a.status==="fail").length,r=e.filter(a=>a.status==="warning").length;u(t===0&&r===0?{title:"🛡️ Security Tests Passed!",description:"All security measures are working correctly.",variant:"default"}:{title:"⚠️ Security Issues Found",description:`${t} failed, ${r} warnings`,variant:"destructive"})}catch(t){console.error("Error running security tests:",t),u({title:"Test Error",description:"Failed to run security tests",variant:"destructive"})}finally{h(!1)}},C=async()=>{try{const{data:e,error:t}=await n.rpc("check_rls_status");if(t){const{data:r,error:a}=await n.from("pg_class").select("relname, relrowsecurity").in("relname",["orders","order_items","shipments"]);if(a)return{test:"RLS Status Check",status:"warning",message:"Could not verify RLS status",details:a};const i=r?.filter(l=>!l.relrowsecurity)||[];if(i.length>0)return{test:"RLS Status Check",status:"fail",message:`RLS not enabled on: ${i.map(l=>l.relname).join(", ")}`,details:i}}return{test:"RLS Status Check",status:"pass",message:"RLS is enabled on all critical tables"}}catch(e){return{test:"RLS Status Check",status:"fail",message:"Failed to check RLS status",details:e}}},v=async()=>{try{const e=await $({limit:10});if(!e.success)return{test:"Supplier Order Filtering",status:"fail",message:"Failed to fetch supplier orders",details:e.error};const t=e.orders||[],r=c?.id;let a=!1,i=[];for(const l of t){const f=l.supplier_items||[];f.some(k=>k.supplier_account_id===r)||(a=!0,i.push({order_id:l.id,order_number:l.order_number,supplier_items_count:f.length,current_user_id:r}))}return a?{test:"Supplier Order Filtering",status:"fail",message:`Found ${i.length} orders without supplier's items`,details:i}:{test:"Supplier Order Filtering",status:"pass",message:`Successfully filtered ${t.length} orders containing supplier's items`}}catch(e){return{test:"Supplier Order Filtering",status:"fail",message:"Exception during supplier order filtering test",details:e}}},I=async()=>{try{const{data:e,error:t}=await n.from("orders").select("id, order_number, consumer_name").limit(100);if(t)return{test:"Direct Database Access",status:"pass",message:"Direct access properly blocked by RLS",details:t.message};const r=e?.length||0;return r===0?{test:"Direct Database Access",status:"pass",message:"No orders returned - proper filtering"}:{test:"Direct Database Access",status:"warning",message:`Got ${r} orders via direct access - verify these are legitimate`,details:e?.slice(0,3)}}catch(e){return{test:"Direct Database Access",status:"pass",message:"Direct access blocked by security",details:e}}},O=async()=>{try{const{data:e,error:t}=await n.from("order_items").select("id, supplier_account_id, product_name").limit(50);if(t)return{test:"Order Items Access",status:"warning",message:"Order items access blocked",details:t.message};const r=c?.id,a=e?.filter(i=>i.supplier_account_id!==r)||[];return a.length>0?{test:"Order Items Access",status:"fail",message:`Found ${a.length} items from other suppliers`,details:a.slice(0,3)}:{test:"Order Items Access",status:"pass",message:`Properly filtered ${e?.length||0} order items`}}catch(e){return{test:"Order Items Access",status:"fail",message:"Exception during order items access test",details:e}}},R=async()=>{try{const{data:e}=await n.from("order_items").select("order_id").neq("supplier_account_id",c?.id).limit(1);if(!e||e.length===0)return{test:"Cross-Supplier Access",status:"pass",message:"No other supplier orders found to test"};const t=e[0].order_id,{data:r,error:a}=await n.from("orders").select("*").eq("id",t).single();return a||!r?{test:"Cross-Supplier Access",status:"pass",message:"Cross-supplier access properly blocked"}:{test:"Cross-Supplier Access",status:"fail",message:"Was able to access another supplier's order",details:{order_id:t,order_number:r.order_number}}}catch(e){return{test:"Cross-Supplier Access",status:"pass",message:"Cross-supplier access blocked by security",details:e}}},A=e=>{switch(e){case"pass":return s.jsx(U,{className:"h-5 w-5 text-green-600"});case"fail":return s.jsx(w,{className:"h-5 w-5 text-red-600"});case"warning":return s.jsx(w,{className:"h-5 w-5 text-yellow-600"});default:return s.jsx(L,{className:"h-5 w-5 text-gray-600"})}},D=e=>{switch(e){case"pass":return s.jsx(d,{className:"bg-green-100 text-green-800",children:"PASS"});case"fail":return s.jsx(d,{className:"bg-red-100 text-red-800",children:"FAIL"});case"warning":return s.jsx(d,{className:"bg-yellow-100 text-yellow-800",children:"WARNING"});default:return s.jsx(d,{children:"UNKNOWN"})}};return s.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[s.jsx(E,{className:"h-8 w-8 text-blue-600"}),"Order Security Test"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"Verify that order access controls are working properly"})]}),s.jsxs(F,{onClick:_,disabled:g,className:"flex items-center gap-2",children:[s.jsx(B,{className:"h-4 w-4"}),g?"Running Tests...":"Run Security Tests"]})]}),c&&s.jsxs(x,{children:[s.jsx(y,{children:s.jsx(j,{children:"Current User"})}),s.jsx(S,{children:s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"User ID:"})," ",c.id]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Email:"})," ",c.email]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Role:"})," ",c.profile?.role]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Company:"})," ",c.profile?.company_name]})]})})]}),p.length>0&&s.jsxs(x,{children:[s.jsx(y,{children:s.jsx(j,{children:"Security Test Results"})}),s.jsx(S,{children:s.jsx("div",{className:"space-y-4",children:p.map((e,t)=>s.jsxs("div",{className:"border rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[A(e.status),s.jsx("h3",{className:"font-semibold",children:e.test})]}),D(e.status)]}),s.jsx("p",{className:"text-gray-700 mb-2",children:e.message}),e.details&&s.jsxs("details",{className:"text-xs text-gray-600",children:[s.jsx("summary",{className:"cursor-pointer",children:"View Details"}),s.jsx("pre",{className:"mt-2 p-2 bg-gray-100 rounded overflow-auto",children:JSON.stringify(e.details,null,2)})]})]},t))})})]})]})}export{V as default};
