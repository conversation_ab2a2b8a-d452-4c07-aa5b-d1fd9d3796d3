import{a_ as T,r as o,a$ as H,b7 as I,j as r,b0 as g,b1 as M,b3 as B,b4 as q,aZ as S}from"./index-BqaWQJEu.js";var v="Switch",[z,U]=T(v),[A,O]=z(v),x=o.forwardRef((e,s)=>{const{__scopeSwitch:t,name:a,checked:n,defaultChecked:l,required:i,disabled:c,value:d="on",onCheckedChange:f,form:m,...p}=e,[u,E]=o.useState(null),R=H(s,b=>E(b)),k=o.useRef(!1),w=u?m||!!u.closest("form"):!0,[h=!1,N]=I({prop:n,defaultProp:l,onChange:f});return r.jsxs(A,{scope:t,checked:h,disabled:c,children:[r.jsx(g.button,{type:"button",role:"switch","aria-checked":h,"aria-required":i,"data-state":P(h),"data-disabled":c?"":void 0,disabled:c,value:d,...p,ref:R,onClick:M(e.onClick,b=>{N(_=>!_),w&&(k.current=b.isPropagationStopped(),k.current||b.stopPropagation())})}),w&&r.jsx($,{control:u,bubbles:!k.current,name:a,value:d,checked:h,required:i,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});x.displayName=v;var C="SwitchThumb",y=o.forwardRef((e,s)=>{const{__scopeSwitch:t,...a}=e,n=O(C,t);return r.jsx(g.span,{"data-state":P(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:s})});y.displayName=C;var $=e=>{const{control:s,checked:t,bubbles:a=!0,...n}=e,l=o.useRef(null),i=B(t),c=q(s);return o.useEffect(()=>{const d=l.current,f=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(f,"checked").set;if(i!==t&&p){const u=new Event("click",{bubbles:a});p.call(d,t),d.dispatchEvent(u)}},[i,t,a]),r.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function P(e){return e?"checked":"unchecked"}var j=x,D=y;const F=o.forwardRef(({className:e,...s},t)=>r.jsx(j,{className:S("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:r.jsx(D,{className:S("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));F.displayName=j.displayName;export{F as S};
