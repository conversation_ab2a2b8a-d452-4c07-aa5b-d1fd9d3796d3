import{a1 as w,aF as R,r as j,aG as V,aH as U,j as e,C as f,k as b,Q as y,l as S,m as M,aA as N,aI as A,aB as v,U as _,B as L,a6 as P,w as B,aJ as H,aK as O,aL as E,aM as D}from"./index-BqaWQJEu.js";import{D as $}from"./database-w0IbHezX.js";import{H as q}from"./hard-drive-Bcd4QF3N.js";import{T as G}from"./test-tube-CQDicyCd.js";const J=async()=>{try{const{data:t,error:l}=await w.from("categories").select("id").limit(1);if(l)return{isMigrated:!1,categoriesCount:0,subcategoriesCount:0,error:l.message};const{data:i,error:g}=await w.from("subcategories").select("id").limit(1);if(g)return{isMigrated:!1,categoriesCount:0,subcategoriesCount:0,error:g.message};const{count:n}=await w.from("categories").select("*",{count:"exact",head:!0}),{count:d}=await w.from("subcategories").select("*",{count:"exact",head:!0});return{isMigrated:(n||0)>0&&(d||0)>0,categoriesCount:n||0,subcategoriesCount:d||0}}catch(t){return console.error("Error checking migration status:",t),{isMigrated:!1,categoriesCount:0,subcategoriesCount:0,error:t instanceof Error?t.message:"Unknown error"}}},Q=async()=>{const t=[];try{const{data:l}=await w.from("categories").select("*").eq("is_active",!0),{data:i}=await w.from("subcategories").select("*").eq("is_active",!0),g=R,n=g.flatMap(s=>s.subcategories),d=l?.length||0,h=g.length;d!==h&&t.push(`Category count mismatch: Supabase has ${d}, localStorage has ${h}`);const p=i?.length||0,c=n.length;p!==c&&t.push(`Subcategory count mismatch: Supabase has ${p}, localStorage has ${c}`);const I=new Set(l?.map(s=>s.id)||[]),T=new Set(g.map(s=>s.id));for(const s of T)I.has(s)||t.push(`Missing category in Supabase: ${s}`);const u=new Set(i?.map(s=>s.id)||[]),k=new Set(n.map(s=>s.id)),a=new Map;n.forEach(s=>{a.has(s.categoryId)||a.set(s.categoryId,new Set),a.get(s.categoryId).add(s.id)});const r=new Map;i?.forEach(s=>{r.has(s.category_id)||r.set(s.category_id,new Set),r.get(s.category_id).add(s.id)});for(const s of k){const o=n.find(F=>F.id===s);if(!o)continue;const m=o.categoryId,x=r.get(m)||new Set;if(x.has(s))continue;const C=`${m}-${s}`;x.has(C)||t.push(`Missing subcategory in Supabase: ${s} (category: ${m})`)}return{isValid:t.length===0,issues:t,summary:{categoriesInSupabase:d,subcategoriesInSupabase:p,categoriesInLocalStorage:h,subcategoriesInLocalStorage:c,migrationNote:t.length===0?"Perfect sync achieved with unique subcategory IDs":"Issues found in migration"}}}catch(l){return console.error("Error verifying migration integrity:",l),{isValid:!1,issues:[`Verification failed: ${l instanceof Error?l.message:"Unknown error"}`],summary:{categoriesInSupabase:0,subcategoriesInSupabase:0,categoriesInLocalStorage:R.length,subcategoriesInLocalStorage:R.flatMap(i=>i.subcategories).length}}}},z=()=>`
🎯 AROUZ MARKET - Categories Migration Instructions

STEP 1: Run SQL Migrations in Supabase
1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Execute these files in order:
   - supabase/migrations/20241220120000_create_categories_system.sql
   - supabase/migrations/20241220120001_insert_subcategories.sql
   - supabase/migrations/20241220120002_insert_remaining_subcategories.sql

STEP 2: Verify Migration
1. Check that categories table has 8 rows
2. Check that subcategories table has 100+ rows
3. Verify all data is properly inserted

STEP 3: Test Application
1. Refresh your application
2. Test product creation modal
3. Verify category/subcategory dropdowns work
4. Test marketplace navigation

✅ The application will automatically detect the migration and switch to Supabase data!
❌ If migration fails, the app will fallback to localStorage data seamlessly.

🚀 Zero downtime, 100% backward compatibility guaranteed!
`,K=()=>{const[t,l]=j.useState(null),[i,g]=j.useState(null),[n,d]=j.useState(!1),[h,p]=j.useState(!1),{categories:c,isLoading:I,error:T}=V(),{isMigrated:u,shouldUseFallback:k,categoriesCount:a}=U(),r=async()=>{d(!0);try{const x=await J();if(l(x),x.isMigrated){const C=await Q();g(C)}}catch(x){console.error("Error checking migration status:",x)}finally{d(!1)}};j.useEffect(()=>{r()},[]);const s=()=>n?e.jsx(P,{className:"h-5 w-5 animate-spin"}):u?e.jsx(_,{className:"h-5 w-5 text-green-500"}):t?.error?e.jsx(A,{className:"h-5 w-5 text-red-500"}):e.jsx(B,{className:"h-5 w-5 text-yellow-500"}),o=()=>n?"Checking...":u?"Migration Completed":t?.error?"Migration Failed":"Migration Pending",m=()=>u?"bg-green-100 text-green-800":t?.error?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800";return e.jsx("div",{className:"space-y-6",children:e.jsxs(f,{children:[e.jsx(b,{children:e.jsxs(y,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5"}),"Categories Migration Status"]})}),e.jsxs(S,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[s(),e.jsx("span",{className:"font-medium",children:o()})]}),e.jsx(M,{className:m(),children:u?"Supabase":"localStorage"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-muted/50",children:[e.jsx($,{className:"h-4 w-4 text-blue-500"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"Supabase"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:t?`${t.categoriesCount} categories`:"Checking..."})]})]}),e.jsxs("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-muted/50",children:[e.jsx(q,{className:"h-4 w-4 text-orange-500"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"localStorage"}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[c.length," categories"]})]})]})]}),t?.error&&e.jsxs(N,{variant:"destructive",children:[e.jsx(A,{className:"h-4 w-4"}),e.jsxs(v,{children:["Migration Error: ",t.error]})]}),u&&e.jsxs(N,{children:[e.jsx(_,{className:"h-4 w-4"}),e.jsx(v,{children:"✅ Migration completed successfully! Using Supabase categories system."})]}),i&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Data Integrity Check"}),i.isValid?e.jsxs(N,{children:[e.jsx(_,{className:"h-4 w-4"}),e.jsx(v,{children:"✅ All data integrity checks passed"})]}):e.jsxs(N,{variant:"destructive",children:[e.jsx(A,{className:"h-4 w-4"}),e.jsxs(v,{children:[e.jsx("div",{children:"⚠️ Data integrity issues found:"}),e.jsx("ul",{className:"mt-2 list-disc list-inside text-xs",children:i.issues.map((x,C)=>e.jsx("li",{children:x},C))})]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(L,{variant:"outline",size:"sm",onClick:r,disabled:n,children:[e.jsx(P,{className:`h-4 w-4 mr-2 ${n?"animate-spin":""}`}),"Refresh Status"]}),!u&&e.jsxs(L,{variant:"outline",size:"sm",onClick:()=>p(!h),children:[h?"Hide":"Show"," Migration Instructions"]})]}),h&&!u&&e.jsxs(N,{children:[e.jsx(B,{className:"h-4 w-4"}),e.jsx(v,{children:e.jsx("pre",{className:"text-xs whitespace-pre-wrap font-mono",children:z()})})]})]})]})})};function ee(){const[t,l]=j.useState({}),{categories:i,isLoading:g,error:n}=V(),{isMigrated:d,shouldUseFallback:h,categoriesCount:p}=U(),c=R,I=(a,r)=>{const s=r();return l(o=>({...o,[a]:s})),s},T=()=>{[{name:"Categories Count Match",test:()=>i.length===c.length},{name:"All Categories Present",test:()=>{const r=new Set(i.map(s=>s.id));return c.every(s=>r.has(s.id))}},{name:"Subcategories Count Match",test:()=>{const r=i.reduce((o,m)=>o+m.subcategories.length,0),s=c.reduce((o,m)=>o+m.subcategories.length,0);return r===s}},{name:"Category Names Match",test:()=>c.every(r=>{const s=i.find(o=>o.id===r.id);return s&&s.name===r.name})},{name:"Display Names Match",test:()=>c.every(r=>{const s=i.find(o=>o.id===r.id);return s&&s.displayName===r.displayName})}].forEach(({name:r,test:s})=>I(r,s))},u=a=>a in t?t[a]?e.jsx(_,{className:"h-4 w-4 text-green-500"}):e.jsx(A,{className:"h-4 w-4 text-red-500"}):e.jsx(G,{className:"h-4 w-4 text-gray-400"}),k=a=>a in t?t[a]?e.jsx(M,{className:"bg-green-100 text-green-800",children:"Pass"}):e.jsx(M,{variant:"destructive",children:"Fail"}):e.jsx(M,{variant:"secondary",children:"Not Run"});return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-3xl font-bold",children:"Categories Migration Test"}),e.jsx(M,{className:d?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",children:d?"Migrated":"Not Migrated"})]}),e.jsxs(H,{defaultValue:"status",className:"w-full",children:[e.jsxs(O,{className:"grid w-full grid-cols-4",children:[e.jsx(E,{value:"status",children:"Migration Status"}),e.jsx(E,{value:"comparison",children:"Data Comparison"}),e.jsx(E,{value:"tests",children:"Integrity Tests"}),e.jsx(E,{value:"raw",children:"Raw Data"})]}),e.jsx(D,{value:"status",className:"space-y-4",children:e.jsx(K,{})}),e.jsx(D,{value:"comparison",className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs(f,{children:[e.jsx(b,{children:e.jsxs(y,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-blue-500"}),"Supabase Categories (",i.length,")"]})}),e.jsx(S,{children:g?e.jsx("div",{children:"Loading..."}):n?e.jsxs("div",{className:"text-red-500",children:["Error: ",n.message]}):e.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:i.map(a=>e.jsxs("div",{className:"border rounded p-2",children:[e.jsx("div",{className:"font-medium",children:a.name}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[a.subcategories.length," subcategories"]})]},a.id))})})]}),e.jsxs(f,{children:[e.jsx(b,{children:e.jsxs(y,{className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-5 w-5 text-orange-500"}),"localStorage Categories (",c.length,")"]})}),e.jsx(S,{children:e.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:c.map(a=>e.jsxs("div",{className:"border rounded p-2",children:[e.jsx("div",{className:"font-medium",children:a.name}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[a.subcategories.length," subcategories"]})]},a.id))})})]})]})}),e.jsx(D,{value:"tests",className:"space-y-4",children:e.jsxs(f,{children:[e.jsx(b,{children:e.jsxs(y,{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Data Integrity Tests"}),e.jsx(L,{onClick:T,disabled:g,children:"Run All Tests"})]})}),e.jsx(S,{children:e.jsx("div",{className:"space-y-3",children:["Categories Count Match","All Categories Present","Subcategories Count Match","Category Names Match","Display Names Match"].map(a=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[u(a),e.jsx("span",{children:a})]}),k(a)]},a))})})]})}),e.jsx(D,{value:"raw",className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs(f,{children:[e.jsx(b,{children:e.jsx(y,{children:"Supabase Raw Data"})}),e.jsx(S,{children:e.jsx("pre",{className:"text-xs bg-muted p-4 rounded overflow-auto max-h-96",children:JSON.stringify(i,null,2)})})]}),e.jsxs(f,{children:[e.jsx(b,{children:e.jsx(y,{children:"localStorage Raw Data"})}),e.jsx(S,{children:e.jsx("pre",{className:"text-xs bg-muted p-4 rounded overflow-auto max-h-96",children:JSON.stringify(c,null,2)})})]})]})})]})]})}export{ee as default};
