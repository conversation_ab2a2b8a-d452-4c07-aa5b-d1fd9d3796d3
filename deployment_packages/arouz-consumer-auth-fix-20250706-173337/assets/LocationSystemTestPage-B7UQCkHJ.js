import{r as i,j as e,C as x,k as m,Q as g,l as h,I as R,B as L,m as u,a3 as I,aI as A,U as T,aN as k,aO as U,K as $,ab as Q,aP as V,aQ as B,aR as _,a2 as K,i as O,aw as G,aS as X,aT as Y,aU as J,aV as Z}from"./index-BqaWQJEu.js";import{T as z}from"./test-tube-CQDicyCd.js";import{P as q}from"./progress-DKc-eGjJ.js";function ee(){const[p,b]=i.useState("33.486435"),[y,N]=i.useState("0.829468"),[s,n]=i.useState(null),[S,v]=i.useState(!1),c=[{name:"Critical Bug Fix Test",description:"Coordinates that were incorrectly showing Na<PERSON><PERSON> instead of Laghouat",lat:33.486435,lng:.829468,expectedWilaya:"Laghouat"},{name:"Algiers Center",description:"Capital city center coordinates",lat:36.7631,lng:3.0506,expectedWilaya:"Alger"},{name:"Constantine",description:"Eastern Algeria major city",lat:36.365,lng:6.6147,expectedWilaya:"Constantine"},{name:"Oran",description:"Western Algeria major city",lat:35.6969,lng:-.6331,expectedWilaya:"Oran"},{name:"Tamanrasset",description:"Southern Algeria (Sahara)",lat:22.7851,lng:5.5281,expectedWilaya:"Tamanrasset"}],f=async()=>{v(!0);try{const t=parseFloat(p),d=parseFloat(y);if(isNaN(t)||isNaN(d)){n({error:"Invalid coordinates"});return}const D=k(t,d);n({coordinates:{lat:t,lng:d},detected:D,timestamp:new Date().toLocaleTimeString()})}catch{n({error:"Error detecting location"})}finally{v(!1)}},l=async t=>{b(t.lat.toString()),N(t.lng.toString());const d=k(t.lat,t.lng);n({coordinates:{lat:t.lat,lng:t.lng},detected:d,expected:t.expectedWilaya,testCase:t.name,timestamp:new Date().toLocaleTimeString()})},w=()=>{const t=U();n({systemTest:!0,detected:t,timestamp:new Date().toLocaleTimeString()})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5 text-blue-600"}),"100% Accurate Location Detection Test"]})}),e.jsxs(h,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Latitude"}),e.jsx(R,{type:"number",step:"any",value:p,onChange:t=>b(t.target.value),placeholder:"Enter latitude"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Longitude"}),e.jsx(R,{type:"number",step:"any",value:y,onChange:t=>N(t.target.value),placeholder:"Enter longitude"})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(L,{onClick:f,disabled:S,className:"bg-blue-600 hover:bg-blue-700",children:S?"Testing...":"Test Coordinates"}),e.jsx(L,{onClick:w,variant:"outline",children:"Run System Test"})]})]})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsx(g,{children:"Predefined Test Cases"})}),e.jsx(h,{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:c.map((t,d)=>e.jsxs("div",{className:"p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer",onClick:()=>l(t),children:[e.jsx("div",{className:"font-medium text-sm",children:t.name}),e.jsx("div",{className:"text-xs text-gray-600 mt-1",children:t.description}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[t.lat,", ",t.lng]}),e.jsxs(u,{variant:"outline",className:"mt-2 text-xs",children:["Expected: ",t.expectedWilaya]})]},d))})})]}),s&&e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5 text-green-600"}),"Detection Results"]})}),e.jsx(h,{children:s.error?e.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2 text-red-700",children:[e.jsx(A,{className:"h-4 w-4"}),s.error]})}):e.jsxs("div",{className:"space-y-4",children:[s.coordinates&&e.jsx("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"font-medium text-blue-900",children:["📍 Coordinates: ",s.coordinates.lat,", ",s.coordinates.lng]})}),s.detected&&e.jsx("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-green-900",children:["✅ Detected Wilaya: ",s.detected.name]}),e.jsxs("div",{className:"text-green-700 mt-1",children:["Arabic: ",s.detected.name_ar]}),e.jsxs("div",{className:"text-green-700",children:["French: ",s.detected.name_fr]}),e.jsxs("div",{className:"text-green-700",children:["Code: ",s.detected.code]})]}),e.jsxs(u,{variant:s.detected.confidence>.8?"default":"secondary",className:"ml-2",children:[(s.detected.confidence*100).toFixed(1),"% confidence"]})]})}),s.expected&&e.jsx("div",{className:`p-4 border rounded-lg ${s.detected?.name===s.expected?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[s.detected?.name===s.expected?e.jsx(T,{className:"h-4 w-4 text-green-600"}):e.jsx(A,{className:"h-4 w-4 text-red-600"}),e.jsxs("span",{className:s.detected?.name===s.expected?"text-green-900":"text-red-900",children:["Expected: ",s.expected," | Result: ",s.detected?.name===s.expected?"PASS":"FAIL"]})]})}),s.testCase&&e.jsx("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-lg",children:e.jsxs("div",{className:"text-gray-700",children:[e.jsx("strong",{children:"Test Case:"})," ",s.testCase]})}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Tested at: ",s.timestamp]})]})})]})]})}function se(){const[p,b]=i.useState("33.486435"),[y,N]=i.useState("0.829468"),[s,n]=i.useState(null),[S,v]=i.useState(!1),[c,f]=i.useState(!1),[l,w]=i.useState(null),[t,d]=i.useState(!1);i.useEffect(()=>{(async()=>{console.log("🔄 Initializing precise location system...");const o=await V();f(o),console.log(o?"✅ System ready for precise testing":"❌ System initialization failed")})()},[]);const D=[{name:"Critical Bug Fix",description:"Coordinates that were incorrectly showing Naâma instead of Laghouat",lat:33.486435,lng:.829468,expectedWilaya:"Laghouat",priority:"CRITICAL"},{name:"Algiers Center",description:"Capital city center - must be 100% accurate",lat:36.7631,lng:3.0506,expectedWilaya:"Alger",priority:"HIGH"},{name:"Oran Port",description:"Major port city - critical for shipping",lat:35.6969,lng:-.6331,expectedWilaya:"Oran",priority:"HIGH"},{name:"Constantine",description:"Eastern Algeria hub",lat:36.365,lng:6.6147,expectedWilaya:"Constantine",priority:"HIGH"},{name:"Border Test: Laghouat-Naâma",description:"Critical border area where errors occurred",lat:33.2,lng:.5,expectedWilaya:"Laghouat",priority:"CRITICAL"}],E=async()=>{if(!c){n({error:"System not ready. Please wait for initialization."});return}v(!0);try{const a=parseFloat(p),o=parseFloat(y);if(isNaN(a)||isNaN(o)){n({error:"Invalid coordinates"});return}console.log(`🧪 Testing coordinates: ${a}, ${o}`);const C=await B(a,o);n({coordinates:{lat:a,lng:o},detected:C,timestamp:new Date().toLocaleTimeString()})}catch(a){console.error("Test error:",a),n({error:"Error detecting location"})}finally{v(!1)}},P=async a=>{b(a.lat.toString()),N(a.lng.toString());const o=await B(a.lat,a.lng);n({coordinates:{lat:a.lat,lng:a.lng},detected:o,expected:a.expectedWilaya,testCase:a.name,priority:a.priority,timestamp:new Date().toLocaleTimeString()})},W=async()=>{if(!c){alert("System not ready. Please wait for initialization.");return}d(!0);try{console.log("🚚 Running comprehensive delivery accuracy tests...");const a=await _();w(a)}catch(a){console.error("Error running delivery tests:",a)}finally{d(!1)}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-blue-600"}),"100% Precise Location Detection for Delivery"]})}),e.jsx(h,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:`flex items-center gap-2 ${c?"text-green-600":"text-orange-600"}`,children:[c?e.jsx(T,{className:"h-4 w-4"}):e.jsx(Q,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:c?"System Ready - Polygon Boundaries Loaded":"Initializing System..."})]}),c&&e.jsx(u,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"100% Delivery Accuracy Enabled"})]})})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5 text-blue-600"}),"Manual Coordinate Testing"]})}),e.jsxs(h,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Latitude"}),e.jsx(R,{type:"number",step:"any",value:p,onChange:a=>b(a.target.value),placeholder:"Enter latitude",disabled:!c})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Longitude"}),e.jsx(R,{type:"number",step:"any",value:y,onChange:a=>N(a.target.value),placeholder:"Enter longitude",disabled:!c})]})]}),e.jsx(L,{onClick:E,disabled:S||!c,className:"bg-blue-600 hover:bg-blue-700",children:S?"Testing...":"Test Precise Location"})]})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsx(g,{children:"Critical Delivery Test Cases"})}),e.jsx(h,{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:D.map((a,o)=>e.jsxs("div",{className:`p-3 border rounded-lg hover:bg-gray-50 cursor-pointer ${a.priority==="CRITICAL"?"border-red-200 bg-red-50":"border-gray-200"}`,onClick:()=>P(a),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"font-medium text-sm",children:a.name}),e.jsx(u,{variant:a.priority==="CRITICAL"?"destructive":"secondary",className:"text-xs",children:a.priority})]}),e.jsx("div",{className:"text-xs text-gray-600 mt-1",children:a.description}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[a.lat,", ",a.lng," → Expected: ",a.expectedWilaya]})]},o))})})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-green-600"}),"Comprehensive Delivery Accuracy Tests"]})}),e.jsx(h,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(L,{onClick:W,disabled:t||!c,className:"bg-green-600 hover:bg-green-700",children:t?"Running Tests...":"Run Full Delivery Test Suite"}),l&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:l.totalTests}),e.jsx("div",{className:"text-sm text-blue-700",children:"Total Tests"})]}),e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:l.passedTests}),e.jsx("div",{className:"text-sm text-green-700",children:"Passed"})]}),e.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[e.jsxs("div",{className:"text-2xl font-bold text-purple-600",children:[l.accuracy.toFixed(1),"%"]}),e.jsx("div",{className:"text-sm text-purple-700",children:"Accuracy"})]})]}),e.jsx(q,{value:l.accuracy,className:"w-full"}),e.jsx("div",{className:`p-4 rounded-lg ${l.accuracy>=95?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[l.accuracy>=95?e.jsx(T,{className:"h-4 w-4 text-green-600"}):e.jsx(A,{className:"h-4 w-4 text-red-600"}),e.jsxs("span",{className:`font-medium ${l.accuracy>=95?"text-green-900":"text-red-900"}`,children:["Delivery System: ",l.accuracy>=95?"READY FOR PRODUCTION ✅":"NEEDS IMPROVEMENT ❌"]})]})})]})]})})]}),s&&e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5 text-green-600"}),"Test Results"]})}),e.jsx(h,{children:s.error?e.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2 text-red-700",children:[e.jsx(A,{className:"h-4 w-4"}),s.error]})}):e.jsxs("div",{className:"space-y-4",children:[s.coordinates&&e.jsx("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"font-medium text-blue-900",children:["📍 Coordinates: ",s.coordinates.lat,", ",s.coordinates.lng]})}),s.detected&&e.jsx("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"font-medium text-green-900",children:["✅ Detected Wilaya: ",s.detected.name," (",s.detected.code,")"]}),e.jsxs("div",{className:"text-green-700",children:["Arabic: ",s.detected.name_ar]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(u,{variant:s.detected.accuracy==="perfect"?"default":"secondary",children:[s.detected.accuracy?.toUpperCase()," Accuracy"]}),e.jsx(u,{variant:"outline",children:s.detected.method==="polygon"?"Polygon Boundary":"Distance Fallback"}),e.jsxs(u,{variant:s.detected.confidence>.9?"default":"secondary",children:[(s.detected.confidence*100).toFixed(1),"% confidence"]})]})]})}),s.expected&&e.jsx("div",{className:`p-4 border rounded-lg ${s.detected?.name===s.expected?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[s.detected?.name===s.expected?e.jsx(T,{className:"h-4 w-4 text-green-600"}):e.jsx(A,{className:"h-4 w-4 text-red-600"}),e.jsxs("span",{className:s.detected?.name===s.expected?"text-green-900":"text-red-900",children:["Expected: ",s.expected," | Result: ",s.detected?.name===s.expected?"PASS ✅":"FAIL ❌"]}),s.priority&&e.jsx(u,{variant:s.priority==="CRITICAL"?"destructive":"secondary",className:"ml-2",children:s.priority})]})}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Tested at: ",s.timestamp]})]})})]})]})}function ie(){const[p,b]=i.useState(""),[y,N]=i.useState([]),[s,n]=i.useState(!1),[S,v]=i.useState("loading"),{selectedLocation:c,isLocationLoading:f,locationError:l,hasLocationPermission:w,isFirstVisit:t,autoLocationAttempted:d,getLocationString:D,requestCurrentLocation:E,clearLocation:P,attemptAutoLocation:W}=K();i.useEffect(()=>{(async()=>{n(!0);try{await X(),v("loaded")}catch(j){console.error("Failed to load complete data:",j),v("error")}finally{n(!1)}})()},[]);const a=r=>{if(b(r),r.trim()){const j=Y(r,15);N(j)}else N([])},o=r=>{r.location.coordinates&&console.log("Selected location:",r)},C=J(),F=Z();return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Enhanced Algerian Location System Test"}),e.jsx("p",{className:"text-gray-600",children:"Testing complete administrative divisions and automatic GPS detection"})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(T,{className:"h-5 w-5 text-green-600"}),"System Status"]})}),e.jsxs(h,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[F?e.jsx(T,{className:"h-4 w-4 text-green-600"}):e.jsx(O,{className:"h-4 w-4 text-blue-600 animate-spin"}),e.jsxs("span",{className:"text-sm",children:["Complete Data: ",F?"Loaded":"Loading..."]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[w?e.jsx(T,{className:"h-4 w-4 text-green-600"}):e.jsx(A,{className:"h-4 w-4 text-red-600"}),e.jsxs("span",{className:"text-sm",children:["GPS Permission: ",w?"Granted":"Not Granted"]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(u,{variant:t?"default":"secondary",children:t?"First Visit":"Returning User"})}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(u,{variant:d?"default":"secondary",children:["Auto Location: ",d?"Attempted":"Pending"]})})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Wilayas Loaded:"})," ",C.length," / 58"]})]})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5 text-orange-600"}),"Current Location"]})}),e.jsxs(h,{className:"space-y-4",children:[c?e.jsxs("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[e.jsx("div",{className:"font-medium text-green-900",children:D()}),e.jsxs("div",{className:"text-sm text-green-700 mt-1",children:["Coordinates: ",c.coordinates.lat.toFixed(4),", ",c.coordinates.lng.toFixed(4)]})]}):e.jsx("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-lg",children:e.jsx("div",{className:"text-gray-600",children:"No location selected"})}),l&&e.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:e.jsx("div",{className:"text-red-700",children:l})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(L,{onClick:E,disabled:f,className:"bg-orange-600 hover:bg-orange-700",children:[f?e.jsx(O,{className:"h-4 w-4 animate-spin mr-2"}):e.jsx(I,{className:"h-4 w-4 mr-2"}),"Get Current Location"]}),e.jsx(L,{onClick:W,variant:"outline",disabled:f,children:"Test Auto Location"}),e.jsx(L,{onClick:P,variant:"outline",children:"Clear Location"})]})]})]}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5 text-blue-600"}),"Enhanced Administrative Search"]})}),e.jsxs(h,{className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(G,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(R,{type:"text",value:p,onChange:r=>a(r.target.value),placeholder:"Search wilayas, daïras, or communes (e.g., 'Alger', 'بجاية', 'Constantine')",className:"pl-9"})]}),y.length>0&&e.jsx("div",{className:"max-h-96 overflow-y-auto border border-gray-200 rounded-lg",children:y.map((r,j)=>e.jsx("button",{onClick:()=>o(r),className:"w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-gray-900",children:r.location.name}),e.jsx("div",{className:"text-sm text-gray-600",children:r.location.name_ar}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:r.hierarchy})]}),e.jsx(u,{variant:r.type==="wilaya"?"default":r.type==="daira"?"secondary":"outline",className:"ml-2",children:r.type})]})},`${r.type}-${r.location.code}-${j}`))}),p&&y.length===0&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:['No results found for "',p,'"']})]})]}),e.jsx(se,{}),e.jsx(ee,{}),e.jsxs(x,{children:[e.jsx(m,{children:e.jsx(g,{children:"Data Statistics"})}),e.jsx(h,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C.length}),e.jsx("div",{className:"text-sm text-blue-700",children:"Wilayas"})]}),e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:C.reduce((r,j)=>r+j.dairas.length,0)}),e.jsx("div",{className:"text-sm text-green-700",children:"Daïras"})]}),e.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:C.reduce((r,j)=>r+j.dairas.reduce((H,M)=>H+M.communes.length,0),0)}),e.jsx("div",{className:"text-sm text-purple-700",children:"Communes"})]})]})})]})]})}export{ie as default};
