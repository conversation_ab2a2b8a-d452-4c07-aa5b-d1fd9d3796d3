import{a2 as o,j as e,C as a,k as c,Q as l,l as n,U as t,m as j,c0 as x,c1 as p,a4 as g,a3 as d}from"./index-BqaWQJEu.js";import{K as N,W as f}from"./wifi-BLTfmNhW.js";function y(){const{selectedLocation:s,getLocationString:m}=o(),r=!0;return e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"container mx-auto px-4 max-w-4xl",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-midnight-blue mb-2",children:"🗺️ Google Maps Integration Test"}),e.jsx("p",{className:"text-gray-600",children:"Testing enhanced location selection with Google Maps + 100% accurate Algeria detection"})]}),e.jsxs(a,{className:"mb-6",children:[e.jsx(c,{children:e.jsxs(l,{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-5 w-5"}),e.jsx("span",{children:"Google Maps API Configuration"})]})}),e.jsxs(n,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(e.Fragment,{children:[e.jsx(t,{className:"h-5 w-5 text-green-500"}),e.jsx("span",{className:"text-green-700",children:"API Key Configured"}),e.jsx(j,{variant:"secondary",className:"bg-green-100 text-green-800",children:"Enhanced Mode"})]})}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[e.jsx(f,{className:"h-4 w-4"}),e.jsx("span",{children:"Online"})]})]}),!r]})]}),e.jsxs(a,{className:"mb-6",children:[e.jsxs(c,{children:[e.jsx(l,{children:"🔍 Google Places Search"}),e.jsx(x,{children:"Search for locations in Algeria with rich autocomplete"})]}),e.jsx(n,{children:e.jsx(p,{placeholder:"Try searching for 'Tlemcen', 'Oran', or 'Constantine'...",onPlaceSelect:i=>{console.log("🗺️ Selected place:",i)}})})]}),e.jsxs(a,{className:"mb-6",children:[e.jsxs(c,{children:[e.jsx(l,{children:"🗺️ Smart Map Selector"}),e.jsx(x,{children:"Intelligent map with automatic Google Maps/OpenStreetMap selection"})]}),e.jsx(n,{children:e.jsx(g,{height:"400px",showCurrentLocationButton:!0,showSearch:r,onLocationSelect:(i,h)=>{console.log("📍 Location selected:",{lat:i,lng:h})}})})]}),s&&e.jsxs(a,{className:"mb-6",children:[e.jsx(c,{children:e.jsxs(l,{className:"flex items-center space-x-2",children:[e.jsx(d,{className:"h-5 w-5 text-[#fa7b00]"}),e.jsx("span",{children:"Selected Location"})]})}),e.jsx(n,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Formatted Address:"}),e.jsx("p",{className:"text-gray-900",children:m()})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Wilaya (French):"}),e.jsx("p",{className:"text-gray-900",children:s.wilaya})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Wilaya (Arabic):"}),e.jsx("p",{className:"text-gray-900 text-right",children:s.wilaya_ar})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Latitude:"}),e.jsx("p",{className:"text-gray-900 font-mono",children:s.coordinates.lat.toFixed(6)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Longitude:"}),e.jsx("p",{className:"text-gray-900 font-mono",children:s.coordinates.lng.toFixed(6)})]})]}),e.jsxs("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-md",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(t,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm font-medium text-green-800",children:"✅ 100% Accurate Wilaya Detection"})]}),e.jsx("p",{className:"text-xs text-green-700 mt-1",children:"Location validated against official Algeria administrative boundaries"})]})]})})]}),e.jsxs(a,{children:[e.jsx(c,{children:e.jsx(l,{children:"🆚 Features Comparison"})}),e.jsx(n,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold text-green-700 mb-3 flex items-center space-x-2",children:[e.jsx(t,{className:"h-4 w-4"}),e.jsx("span",{children:"With Google Maps API"})]}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"Rich Arabic place names"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"Google Places search"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"Street-level accuracy"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"Familiar Google Maps UI"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"Landmarks & businesses"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"100% accurate wilaya detection"})]})]})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold text-blue-700 mb-3 flex items-center space-x-2",children:[e.jsx(d,{className:"h-4 w-4"}),e.jsx("span",{children:"OpenStreetMap Fallback"})]}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-blue-500",children:"📍"}),e.jsx("span",{children:"Basic map functionality"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-blue-500",children:"📍"}),e.jsx("span",{children:"Click-based selection"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-blue-500",children:"📍"}),e.jsx("span",{children:"No API key required"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-blue-500",children:"📍"}),e.jsx("span",{children:"Always available"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-blue-500",children:"📍"}),e.jsx("span",{children:"Open source maps"})]}),e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-green-500",children:"✅"}),e.jsx("span",{children:"100% accurate wilaya detection"})]})]})]})]})})]})]})})}export{y as GoogleMapsTest};
