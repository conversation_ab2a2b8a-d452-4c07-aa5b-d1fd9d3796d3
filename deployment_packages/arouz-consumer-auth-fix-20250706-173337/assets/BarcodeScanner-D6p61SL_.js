import{r as a,j as e,D as b,d as w,e as N,f as y,h as D,B as x,ar as S}from"./index-BqaWQJEu.js";import{B,S as h}from"./ProductFormDialog-i4pxggHj.js";const F=({isOpen:o,onClose:i,onScan:g})=>{const n=a.useRef(null),[l,c]=a.useState(null),[v,d]=a.useState(!1),r=a.useRef(null);a.useEffect(()=>(r.current=new B,()=>{r.current&&r.current.reset()}),[]),a.useEffect(()=>(o&&n.current?m():t(),()=>{t()}),[o]);const m=async()=>{if(!(!r.current||!n.current))try{c(null),d(!0);const s=await r.current.listVideoInputDevices();if(s.length===0){c("No camera found. Please connect a camera and try again.");return}const j=s[0].deviceId;r.current.decodeFromVideoDevice(j,n.current,(f,u)=>{if(f){const p=f.getText();g(p),t(),i()}u&&!(u instanceof TypeError)&&console.error("Barcode scanning error:",u)})}catch(s){console.error("Error starting barcode scanner:",s),c("Failed to start camera. Please check camera permissions and try again."),d(!1)}},t=()=>{r.current&&(r.current.reset(),d(!1))};return e.jsx(b,{open:o,onOpenChange:s=>{s||(t(),i())},children:e.jsxs(w,{className:"sm:max-w-md",children:[e.jsx(N,{children:e.jsxs(y,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5"}),"Scan Product Barcode"]})}),e.jsx("div",{className:"flex flex-col items-center gap-4 py-4",children:l?e.jsx("div",{className:"text-destructive text-center p-4 border border-destructive/20 rounded-md bg-destructive/10",children:l}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative w-full aspect-video bg-black rounded-md overflow-hidden",children:[e.jsx("video",{ref:n,className:"w-full h-full object-cover"}),v&&e.jsx("div",{className:"absolute inset-0 border-2 border-primary animate-pulse pointer-events-none"})]}),e.jsx("p",{className:"text-sm text-muted-foreground text-center",children:"Position the barcode within the camera view to scan automatically."})]})}),e.jsxs(D,{className:"flex justify-between",children:[e.jsxs(x,{variant:"outline",onClick:()=>{t(),i()},children:[e.jsx(S,{className:"h-4 w-4 mr-2"}),"Cancel"]}),l&&e.jsxs(x,{onClick:()=>{c(null),m()},children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})]})})};export{F as B};
