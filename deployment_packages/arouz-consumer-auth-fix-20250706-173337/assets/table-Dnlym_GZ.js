import{r as o,j as r,aZ as t}from"./index-BqaWQJEu.js";const l=o.forwardRef(({className:e,...a},s)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:s,className:t("w-full caption-bottom text-sm border-collapse",e),...a})}));l.displayName="Table";const d=o.forwardRef(({className:e,...a},s)=>r.jsx("thead",{ref:s,className:t("bg-[#f5f6f8] sticky top-0 z-20 shadow-sm",e),...a}));d.displayName="TableHeader";const b=o.forwardRef(({className:e,...a},s)=>r.jsx("tbody",{ref:s,className:t("[&_tr:last-child]:border-0",e),...a}));b.displayName="TableBody";const m=o.forwardRef(({className:e,...a},s)=>r.jsx("tfoot",{ref:s,className:t("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a}));m.displayName="TableFooter";const f=o.forwardRef(({className:e,...a},s)=>r.jsx("tr",{ref:s,className:t("border-b transition-colors hover:bg-[#f0f3ff]",e),...a}));f.displayName="TableRow";const c=o.forwardRef(({className:e,...a},s)=>r.jsx("th",{ref:s,className:t("h-12 px-4 text-left align-middle font-medium text-muted-foreground border-r border-[#e1e1e1] last:border-r-0",e),...a}));c.displayName="TableHead";const i=o.forwardRef(({className:e,...a},s)=>r.jsx("td",{ref:s,className:t("p-3 align-middle border-r border-[#e1e1e1] last:border-r-0",e),...a}));i.displayName="TableCell";const n=o.forwardRef(({className:e,...a},s)=>r.jsx("caption",{ref:s,className:t("mt-4 text-sm text-muted-foreground",e),...a}));n.displayName="TableCaption";export{l as T,d as a,f as b,c,b as d,i as e};
