import{u as b,ag as j,bb as v,r as d,j as e,i as w,a1 as h,bH as y}from"./index-BqaWQJEu.js";const F=()=>{const{t:r}=b(),l=j(),{setUserRole:f,setSupplierRole:m,setMerchantRole:x}=v(),[c,a]=d.useState("loading"),[p,t]=d.useState("");return d.useEffect(()=>{(async()=>{try{const{data:{session:u},error:g}=await h.auth.getSession();if(g){console.error("Error getting session:",g),a("error"),t(r("auth.verificationFailed"));return}if(new URLSearchParams(window.location.search).get("duplicate")==="true"){a("error"),t(r("auth.alreadyVerified"));return}if(!u){const{data:n,error:o}=await h.auth.exchangeCodeForSession(window.location.hash.substring(1));if(o){if(console.error("Error exchanging code for session:",o),o.message.includes("invalid")||o.message.includes("expired")){a("error"),t(r("auth.linkExpired"));return}if(o.message.includes("already confirmed")||o.message.includes("already verified")){a("error"),t(r("auth.alreadyVerified"));return}a("error"),t(r("auth.verificationFailed"));return}if(!n.session){a("error"),t(r("auth.verificationFailed"));return}}const{data:{user:i}}=await h.auth.getUser();if(!i){a("error"),t(r("auth.userNotFound"));return}let s=null;if(i.user_metadata&&i.user_metadata.role&&(s=i.user_metadata.role,console.log("Got role from user metadata:",s)),!s){const{profile:n,error:o}=await y(i.id);o&&console.error("Error getting user profile:",o),n&&(s=n.role,console.log("Got role from profile:",s))}s||(console.error("Could not determine user role"),s="supplier"),f(s),s==="supplier"?m(!0):s==="merchant"&&x(!0),a("success"),t(r("auth.verificationSuccess")),setTimeout(()=>{l("/app/dashboard")},1500)}catch(u){console.error("Error in auth callback:",u),a("error"),t(r("auth.unexpectedError"))}})()},[l,f,m,x,r]),e.jsx("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4",children:e.jsx("div",{className:"w-full max-w-md p-8 space-y-6 bg-white rounded-xl shadow-lg",children:e.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[e.jsx("img",{src:"/images/AROUZ Header Logo.png",alt:"AROUZ MARKET",className:"h-16 mb-6"}),c==="loading"&&e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-12 w-12 text-[#fa7b00] animate-spin mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:r("auth.verifyingAccount")}),e.jsx("p",{className:"text-gray-600",children:r("auth.pleaseWait")})]}),c==="success"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4",children:e.jsx("svg",{className:"h-8 w-8 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:r("auth.accountVerified")}),e.jsx("p",{className:"text-gray-600 mb-4",children:r("auth.redirectingToDashboard")}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:"bg-[#fa7b00] h-2.5 rounded-full animate-progress"})})]}),c==="error"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4",children:e.jsx("svg",{className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:r("auth.verificationFailed")}),e.jsx("p",{className:"text-gray-600 mb-6",children:p||r("auth.unexpectedError")}),e.jsx("button",{onClick:()=>l("/partners"),className:"px-6 py-2 bg-[#fa7b00] text-white rounded-lg hover:bg-[#fa7b00]/90 transition-colors",children:r("auth.returnToPartners")})]})]})})})};export{F as default};
