import{r as i,j as d,b0 as $,aZ as I}from"./index-BqaWQJEu.js";function M(e,r=[]){let s=[];function a(c,u){const o=i.createContext(u),n=s.length;s=[...s,u];function l(p){const{scope:m,children:x,...v}=p,j=m?.[e][n]||o,E=i.useMemo(()=>v,Object.values(v));return d.jsx(j.Provider,{value:E,children:x})}function b(p,m){const x=m?.[e][n]||o,v=i.useContext(x);if(v)return v;if(u!==void 0)return u;throw new Error(`\`${p}\` must be used within \`${c}\``)}return l.displayName=c+"Provider",[l,b]}const t=()=>{const c=s.map(u=>i.createContext(u));return function(o){const n=o?.[e]||c;return i.useMemo(()=>({[`__scope${e}`]:{...o,[e]:n}}),[o,n])}};return t.scopeName=e,[a,R(t,...r)]}function R(...e){const r=e[0];if(e.length===1)return r;const s=()=>{const a=e.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(c){const u=a.reduce((o,{useScope:n,scopeName:l})=>{const p=n(c)[`__scope${l}`];return{...o,...p}},{});return i.useMemo(()=>({[`__scope${r.scopeName}`]:u}),[u])}};return s.scopeName=r.scopeName,s}var g="Progress",P=100,[V,B]=M(g),[A,D]=V(g),S=i.forwardRef((e,r)=>{const{__scopeProgress:s,value:a=null,max:t,getValueLabel:c=L,...u}=e;(t||t===0)&&!N(t)&&console.error(O(`${t}`,"Progress"));const o=N(t)?t:P;a!==null&&!h(a,o)&&console.error(T(`${a}`,"Progress"));const n=h(a,o)?a:null,l=f(n)?c(n,o):void 0;return d.jsx(A,{scope:s,value:n,max:o,children:d.jsx($.div,{"aria-valuemax":o,"aria-valuemin":0,"aria-valuenow":f(n)?n:void 0,"aria-valuetext":l,role:"progressbar","data-state":w(n,o),"data-value":n??void 0,"data-max":o,...u,ref:r})})});S.displayName=g;var _="ProgressIndicator",C=i.forwardRef((e,r)=>{const{__scopeProgress:s,...a}=e,t=D(_,s);return d.jsx($.div,{"data-state":w(t.value,t.max),"data-value":t.value??void 0,"data-max":t.max,...a,ref:r})});C.displayName=_;function L(e,r){return`${Math.round(e/r*100)}%`}function w(e,r){return e==null?"indeterminate":e===r?"complete":"loading"}function f(e){return typeof e=="number"}function N(e){return f(e)&&!isNaN(e)&&e>0}function h(e,r){return f(e)&&!isNaN(e)&&e<=r&&e>=0}function O(e,r){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${r}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${P}\`.`}function T(e,r){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${r}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${P} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var y=S,G=C;const X=i.forwardRef(({className:e,value:r,...s},a)=>d.jsx(y,{ref:a,className:I("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:d.jsx(G,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));X.displayName=y.displayName;export{X as P};
