import{j as e,v as h,C as x,k as m,Q as g,m as t,l as j,B as i,at as a}from"./index-BqaWQJEu.js";import{g as l}from"./centralizedProductData-C8mYdbMZ.js";import"./idGenerator-CHl7dMtk.js";import"./categories-k_ueMvEL.js";function T(){const r=l("wholesale","tyres").slice(0,3),n=l("retail","tyres").slice(0,3),c=l("wholesale","brakes").slice(0,2),d=l("retail","brakes").slice(0,2),o=[...r,...n,...c,...d];return e.jsx(h,{children:e.jsxs("div",{className:"container py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Product Page Testing"}),e.jsxs("p",{className:"text-gray-600 mb-6",children:["Click on any product ID below to test the product page functionality. The URL structure follows the pattern: /","{","ProductID","}"," (e.g., /TYR-100001-WHOLESALE)"]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(s=>e.jsxs(x,{className:"hover:shadow-lg transition-shadow",children:[e.jsxs(m,{children:[e.jsx(g,{className:"text-lg",children:s.name}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(t,{variant:"outline",children:s.category}),e.jsx(t,{variant:"secondary",children:s.id})]})]}),e.jsx(j,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Manufacturer:"})," ",s.manufacturer]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"SKU:"})," ",s.sku]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Stock:"})," ",s.stockQuantity," units"]}),e.jsxs("div",{className:"space-y-1",children:[s.retailPrice&&e.jsxs("div",{className:"text-sm",children:[e.jsx("strong",{children:"Retail Price:"})," $",s.retailPrice.toFixed(2)]}),s.wholesalePricingTiers&&s.wholesalePricingTiers.length>0&&e.jsxs("div",{className:"text-sm",children:[e.jsx("strong",{children:"Wholesale Price:"})," $",s.wholesalePricingTiers[0].price.toFixed(2)]})]}),s.category==="tyres"&&"width"in s&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Size:"})," ",s.width,"/",s.aspectRatio,"R",s.rimDiameter]}),e.jsx("div",{className:"pt-3",children:e.jsx(i,{asChild:!0,className:"w-full",children:e.jsx(a,{to:`/${s.id}`,children:"View Product Page"})})})]})})]},s.id))}),e.jsxs("div",{className:"mt-12 p-6 bg-blue-50 rounded-lg",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Testing Instructions"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsx("p",{children:'• Click on any "View Product Page" button to test the product page'}),e.jsxs("p",{children:["• The URL will change to /","{","ProductID","}"," format (e.g., /TYR-100001-WHOLESALE)"]}),e.jsx("p",{children:"• The product page should display all sections: Overview, Specifications, Description, and Vehicle Compatibility"}),e.jsx("p",{children:"• Products with IDs ending in -WHOLESALE should show wholesale pricing"}),e.jsx("p",{children:"• Products with IDs ending in -RETAIL should show retail pricing"}),e.jsx("p",{children:"• Test the back navigation and ensure the product is found correctly"}),e.jsx("p",{children:"• Each product ID is now unique across marketplace sections"})]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Direct Product Links for Testing"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsx(i,{asChild:!0,variant:"outline",children:e.jsx(a,{to:"/TYR-100000-WHOLESALE",children:"TYR-100000-WHOLESALE"})}),e.jsx(i,{asChild:!0,variant:"outline",children:e.jsx(a,{to:"/TYR-100000-RETAIL",children:"TYR-100000-RETAIL"})}),e.jsx(i,{asChild:!0,variant:"outline",children:e.jsx(a,{to:"/BRK-100000-WHOLESALE",children:"BRK-100000-WHOLESALE"})}),e.jsx(i,{asChild:!0,variant:"outline",children:e.jsx(a,{to:"/BRK-100000-RETAIL",children:"BRK-100000-RETAIL"})})]})]})]})})}export{T as default};
