/**
 * Checkout Authentication Trigger
 * 
 * CRITICAL: Ensures only ONE phone verification modal appears during checkout
 * Prevents duplicate modal issues by centralizing modal state management
 */

import React, { useState, useCallback } from 'react';
import { EnhancedPhoneAuthModal } from './EnhancedPhoneAuthModal';
import { useConsumerAuth } from './ConsumerAuthGuard';

interface CheckoutAuthTriggerProps {
  children: (triggerAuth: () => void) => React.ReactNode;
  onAuthSuccess?: (user: any, action: 'login' | 'signup') => void;
}

export function CheckoutAuthTrigger({ children, onAuthSuccess }: CheckoutAuthTriggerProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { isAuthenticated } = useConsumerAuth();

  // Prevent modal from opening if already authenticated
  const triggerAuth = useCallback(() => {
    if (isAuthenticated) {
      console.log('🔒 User already authenticated, skipping modal');
      return;
    }

    console.log('🔘 Triggering checkout authentication modal');
    setIsModalOpen(true);
  }, [isAuthenticated]);

  const handleModalClose = useCallback(() => {
    console.log('❌ Closing checkout authentication modal');
    setIsModalOpen(false);
  }, []);

  const handleAuthSuccess = useCallback((user: any, action: 'login' | 'signup') => {
    console.log('✅ Checkout authentication successful:', { user, action });
    setIsModalOpen(false);
    onAuthSuccess?.(user, action);
  }, [onAuthSuccess]);

  return (
    <>
      {children(triggerAuth)}
      
      {/* Single Modal Instance - No Duplicates */}
      <EnhancedPhoneAuthModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSuccess={handleAuthSuccess}
      />
    </>
  );
}
