import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Chery engines update file
const cheryEnginesUpdatePath = path.join(__dirname, 'src', 'services', 'data', 'chery-engines-update.json');
const cheryEnginesUpdate = JSON.parse(fs.readFileSync(cheryEnginesUpdatePath, 'utf8'));

// Find the Chery brand in the cars.json file
const cheryBrandIndex = carsJson.findIndex(brand => brand.brand === 'Chery');

if (cheryBrandIndex !== -1) {
  // Update the Chery models
  carsJson[cheryBrandIndex].models = cheryEnginesUpdate;

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Updated engine data for ${cheryEnginesUpdate.length} Chery models in the cars.json file.`);
} else {
  console.error('Chery brand not found in the cars.json file.');
}

// Clean up the temporary file
fs.unlinkSync(cheryEnginesUpdatePath);

console.log('Temporary file cleaned up.');
