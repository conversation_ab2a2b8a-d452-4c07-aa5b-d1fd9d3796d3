// Debug script to test image upload functionality
// Run this in the browser console to debug the issue

console.log('🔍 Starting image upload debug...');

// Check environment variables
console.log('Environment Variables:', {
  VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
  VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Present' : 'Missing',
  VITE_USE_SUPABASE_BACKEND: import.meta.env.VITE_USE_SUPABASE_BACKEND,
  VITE_ENABLE_IMAGE_UPLOAD: import.meta.env.VITE_ENABLE_IMAGE_UPLOAD,
});

// Check feature flags
import { isFeatureEnabled, getFeatureFlags } from './src/config/features.ts';
console.log('Feature Flags:', getFeatureFlags());

// Check authentication
import { supabase } from './src/services/authService.ts';
supabase.auth.getUser().then(({ data: { user }, error }) => {
  console.log('Authentication Status:', {
    isAuthenticated: !!user,
    userId: user?.id,
    email: user?.email,
    error: error?.message
  });
});

// Test Supabase Storage connection
async function testStorageConnection() {
  try {
    console.log('🔄 Testing Supabase Storage connection...');
    
    // List buckets
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    console.log('Storage Buckets:', { buckets, error: bucketsError });
    
    // Check product-images bucket
    const { data: files, error: filesError } = await supabase.storage
      .from('product-images')
      .list('', { limit: 10 });
    console.log('Product Images Bucket:', { files, error: filesError });
    
  } catch (error) {
    console.error('❌ Storage connection test failed:', error);
  }
}

testStorageConnection();

console.log('✅ Debug script completed. Check the logs above for issues.');
