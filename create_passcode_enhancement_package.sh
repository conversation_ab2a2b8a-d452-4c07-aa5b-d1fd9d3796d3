#!/bin/bash

# AROUZ MARKET - Consumer Passcode Enhancement Deployment Package Creator
# This script creates a production-ready deployment package for the consumer passcode enhancement

echo "🚀 AROUZ MARKET - Creating Consumer Passcode Enhancement Deployment Package"
echo "=================================================================="

# Create timestamp for unique package naming
timestamp=$(date +"%Y%m%d-%H%M%S")
package_name="arouz-consumer-passcode-enhancement-${timestamp}.zip"

echo "📦 Package name: ${package_name}"
echo ""

# Ensure we're in the project root
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Create deployment_packages directory if it doesn't exist
mkdir -p deployment_packages

echo "🔨 Building project for production..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed! Cannot create deployment package."
    exit 1
fi

echo "✅ Build completed successfully!"
echo ""

echo "📋 Creating deployment package with:"
echo "   - dist/ (production build)"
echo "   - supabase/migrations/20250707000000_add_consumer_passcode_support.sql"
echo "   - PASSCODE_DEPLOYMENT_INSTRUCTIONS.md"
echo ""

# Create deployment instructions
cat > PASSCODE_DEPLOYMENT_INSTRUCTIONS.md << 'EOF'
# AROUZ MARKET - Consumer Passcode Enhancement Deployment Instructions

## 🎯 DEPLOYMENT OVERVIEW
This package contains the Consumer Passcode Enhancement feature that adds optional 4-6 digit passcode support to the existing consumer authentication system.

## 📋 WHAT'S INCLUDED
- **Enhanced Consumer Authentication**: Optional passcode support with backward compatibility
- **Secure Passcode Storage**: bcrypt hashing with 12 salt rounds
- **Rate Limiting**: 5 failed attempts with 15-minute lockout
- **Database Migration**: Adds passcode_hash column to profiles table
- **Test Component**: Comprehensive testing interface at /consumer-passcode-test

## 🔧 DEPLOYMENT STEPS

### 1. BACKUP CURRENT SYSTEM
```bash
# Create backup of current production files
cp -r public_html public_html_backup_$(date +%Y%m%d_%H%M%S)
```

### 2. DEPLOY FILES
```bash
# Extract and deploy the dist/ contents to public_html
unzip arouz-consumer-passcode-enhancement-YYYYMMDD-HHMMSS.zip
cp -r dist/* public_html/
```

### 3. RUN DATABASE MIGRATION
```sql
-- Execute this SQL in your Supabase SQL Editor:
-- File: supabase/migrations/20250707000000_add_consumer_passcode_support.sql

ALTER TABLE profiles ADD COLUMN IF NOT EXISTS passcode_hash TEXT;
COMMENT ON COLUMN profiles.passcode_hash IS 'Bcrypt hash of consumer passcode (4-6 digits) for enhanced security';
CREATE INDEX IF NOT EXISTS profiles_passcode_hash_idx ON profiles(passcode_hash) WHERE passcode_hash IS NOT NULL;
```

### 4. VERIFY DEPLOYMENT
1. Visit your site and test consumer authentication
2. Test the passcode enhancement at: https://yourdomain.com/consumer-passcode-test
3. Verify existing users can still log in without passcode
4. Test new user registration with optional passcode

## ✅ FEATURES IMPLEMENTED

### Core Functionality
- ✅ Optional passcode field in consumer authentication modal
- ✅ Secure bcrypt hashing (12 salt rounds)
- ✅ 100% backward compatibility with existing accounts
- ✅ Rate limiting (5 attempts, 15-minute lockout)
- ✅ Passcode format validation (4-6 digits)
- ✅ Show/hide passcode toggle
- ✅ In-app passcode updates for logged-in users

### Security Features
- ✅ Secure passcode storage (never stored in plain text)
- ✅ Rate limiting to prevent brute force attacks
- ✅ Authentication isolation maintained
- ✅ No external verification required for passcode changes

### User Experience
- ✅ Optional for new users (can skip passcode)
- ✅ Existing users continue without passcode unless they set one
- ✅ Clear visual indicators for passcode requirements
- ✅ Immediate feedback on passcode validity

## 🧪 TESTING INSTRUCTIONS

### Access Test Interface
Visit: `https://yourdomain.com/consumer-passcode-test`

### Test Scenarios
1. **New User with Passcode**: Register with phone + name + passcode
2. **New User without Passcode**: Register with phone + name only
3. **Existing User Login**: Login with existing account (should work normally)
4. **Passcode Update**: Set/change passcode for logged-in user
5. **Rate Limiting**: Test failed passcode attempts

## 🔒 SECURITY NOTES
- Passcodes are hashed using bcrypt with 12 salt rounds
- Rate limiting prevents brute force attacks
- Authentication isolation is maintained across all user types
- No breaking changes to existing authentication flows

## 📞 SUPPORT
If you encounter any issues during deployment, please check:
1. Database migration executed successfully
2. All files deployed to correct directory
3. No console errors in browser developer tools
4. Test interface accessible and functional

## 🎉 COMPLETION
Once deployed and tested, the Consumer Passcode Enhancement will be live and ready for use!
EOF

echo "📝 Created deployment instructions: PASSCODE_DEPLOYMENT_INSTRUCTIONS.md"
echo ""

echo "📦 Creating deployment package..."

# Create the deployment package with proper structure
zip -r "deployment_packages/${package_name}" \
    dist/ \
    supabase/migrations/20250707000000_add_consumer_passcode_support.sql \
    PASSCODE_DEPLOYMENT_INSTRUCTIONS.md \
    -x "*.DS_Store"

if [ $? -eq 0 ]; then
    echo "✅ Deployment package created successfully!"
    echo ""
    echo "📋 Package Details:"
    ls -la "deployment_packages/${package_name}"
    echo ""
    echo "📁 Package Contents:"
    unzip -l "deployment_packages/${package_name}" | head -20
    echo ""
    echo "🎯 DEPLOYMENT READY!"
    echo "Package: deployment_packages/${package_name}"
    echo "Instructions: PASSCODE_DEPLOYMENT_INSTRUCTIONS.md"
    echo ""
    echo "🚀 Next Steps:"
    echo "1. Upload package to production server"
    echo "2. Follow deployment instructions"
    echo "3. Run database migration"
    echo "4. Test functionality"
else
    echo "❌ Failed to create deployment package!"
    exit 1
fi

# Clean up deployment instructions file
rm PASSCODE_DEPLOYMENT_INSTRUCTIONS.md

echo "✨ Consumer Passcode Enhancement deployment package ready!"
