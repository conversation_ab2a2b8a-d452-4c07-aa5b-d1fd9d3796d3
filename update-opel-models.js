import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Opel models files
const opelAstraModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-astra-models.json');
const opelCorsaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-corsa-models.json');
const opelInsigniaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-insignia-models.json');
const opelMokkaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-mokka-models.json');
const opelZafiraModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-zafira-models.json');
const opelVectraModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-vectra-models.json');
const opelBatch2ModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-batch2-models.json');
const opelBatch3ModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-batch3-models.json');
const opelBatch4ModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-batch4-models.json');
const opelBatch5ModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-batch5-models.json');
const opelBatch6ModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-batch6-models.json');

const opelAstraModels = JSON.parse(fs.readFileSync(opelAstraModelsPath, 'utf8'));
const opelCorsaModels = JSON.parse(fs.readFileSync(opelCorsaModelsPath, 'utf8'));
const opelInsigniaModels = JSON.parse(fs.readFileSync(opelInsigniaModelsPath, 'utf8'));
const opelMokkaModels = JSON.parse(fs.readFileSync(opelMokkaModelsPath, 'utf8'));
const opelZafiraModels = JSON.parse(fs.readFileSync(opelZafiraModelsPath, 'utf8'));
const opelVectraModels = JSON.parse(fs.readFileSync(opelVectraModelsPath, 'utf8'));
const opelBatch2Models = JSON.parse(fs.readFileSync(opelBatch2ModelsPath, 'utf8'));
const opelBatch3Models = JSON.parse(fs.readFileSync(opelBatch3ModelsPath, 'utf8'));
const opelBatch4Models = JSON.parse(fs.readFileSync(opelBatch4ModelsPath, 'utf8'));
const opelBatch5Models = JSON.parse(fs.readFileSync(opelBatch5ModelsPath, 'utf8'));
const opelBatch6Models = JSON.parse(fs.readFileSync(opelBatch6ModelsPath, 'utf8'));

// Combine all Opel models
const opelModels = [
  ...opelAstraModels,
  ...opelCorsaModels,
  ...opelInsigniaModels,
  ...opelMokkaModels,
  ...opelZafiraModels,
  ...opelVectraModels,
  ...opelBatch2Models,
  ...opelBatch3Models,
  ...opelBatch4Models,
  ...opelBatch5Models,
  ...opelBatch6Models
];

// Find the Opel brand in the cars.json file
const opelBrandIndex = carsJson.findIndex(brand => brand.brand === 'Opel');

if (opelBrandIndex !== -1) {
  // Update the Opel models
  carsJson[opelBrandIndex].models = opelModels;

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Added ${opelModels.length} Opel models to the cars.json file.`);
} else {
  console.error('Opel brand not found in the cars.json file.');
}

// Clean up the temporary files
fs.unlinkSync(opelAstraModelsPath);
fs.unlinkSync(opelCorsaModelsPath);
fs.unlinkSync(opelInsigniaModelsPath);
fs.unlinkSync(opelMokkaModelsPath);
fs.unlinkSync(opelZafiraModelsPath);
fs.unlinkSync(opelVectraModelsPath);
fs.unlinkSync(opelBatch2ModelsPath);
fs.unlinkSync(opelBatch3ModelsPath);
fs.unlinkSync(opelBatch4ModelsPath);
fs.unlinkSync(opelBatch5ModelsPath);
fs.unlinkSync(opelBatch6ModelsPath);

console.log('Temporary files cleaned up.');
