import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * This script copies the favicon from the source location to the public and dist directories
 * It ensures the favicon is properly updated and not cached by the browser
 */
async function updateFavicon() {
  try {
    console.log('Starting favicon update process...');
    
    // Source favicon path (the one you updated)
    const sourceFaviconPath = path.join(__dirname, 'dist', 'favicon.png');
    
    // Check if the source favicon exists
    if (!fs.existsSync(sourceFaviconPath)) {
      console.error('Source favicon not found at:', sourceFaviconPath);
      return;
    }
    
    // Read the source favicon
    const faviconData = fs.readFileSync(sourceFaviconPath);
    
    // Write to the public directory
    fs.writeFileSync(path.join(__dirname, 'public', 'favicon.png'), faviconData);
    console.log('Favicon updated in public directory');
    
    // Ensure the favicon is also in the dist directory (for production builds)
    fs.writeFileSync(path.join(__dirname, 'dist', 'favicon.png'), faviconData);
    console.log('Favicon updated in dist directory');
    
    console.log('Favicon update complete!');
    console.log('Note: You may need to clear your browser cache or use Ctrl+F5 to see the changes.');
  } catch (error) {
    console.error('Error updating favicon:', error);
  }
}

updateFavicon();
