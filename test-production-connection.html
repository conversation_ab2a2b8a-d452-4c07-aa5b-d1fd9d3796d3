<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Database Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #f8f9fa; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Production Database Connection Test</h1>
        <p>This test will verify that your local app is connected to the production Supabase database.</p>
        
        <div id="status" class="status info">
            ⏳ Ready to test connection...
        </div>
        
        <button onclick="testConnection()">🚀 Test Production Connection</button>
        <button onclick="testSignup()">✍️ Test Signup Flow</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        // Production database credentials
        const PROD_URL = 'https://irkwpzcskeqtasutqnxp.supabase.co';
        const PROD_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';
        
        const supabase = createClient(PROD_URL, PROD_KEY);
        
        window.testConnection = async function() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ Testing production database connection...';
            
            try {
                // Test 1: Basic connection
                statusDiv.innerHTML = '🔗 Testing basic connection...';
                const { data: session, error: sessionError } = await supabase.auth.getSession();
                
                let results = '<h2>📊 Connection Test Results</h2>';
                
                if (sessionError) {
                    results += `<div class="status error">❌ Session Error: ${sessionError.message}</div>`;
                } else {
                    results += '<div class="status success">✅ Basic connection successful</div>';
                }
                
                // Test 2: Database tables
                statusDiv.innerHTML = '📋 Testing database tables...';
                const tables = ['profiles', 'products', 'orders', 'categories'];
                
                for (const table of tables) {
                    try {
                        const { count, error } = await supabase
                            .from(table)
                            .select('*', { count: 'exact', head: true });
                        
                        if (error) {
                            results += `<div class="status error">❌ Table '${table}': ${error.message}</div>`;
                        } else {
                            results += `<div class="status success">✅ Table '${table}': ${count || 0} records</div>`;
                        }
                    } catch (err) {
                        results += `<div class="status error">❌ Table '${table}': ${err.message}</div>`;
                    }
                }
                
                // Test 3: Environment check
                results += '<h3>🔧 Environment Configuration</h3>';
                results += `<div class="status info">🌐 Database URL: ${PROD_URL}</div>`;
                results += `<div class="status info">🔑 API Key: ${PROD_KEY.substring(0, 20)}...</div>`;
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ Connection test completed!';
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Connection test failed: ${error.message}`;
                resultsDiv.innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        };
        
        window.testSignup = async function() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ Testing signup flow...';
            
            const testEmail = `test-${Date.now()}@example.com`;
            const testPassword = 'TestPassword123!';
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: testEmail,
                    password: testPassword,
                    options: {
                        data: {
                            role: 'supplier',
                            fullName: 'Test User',
                            companyName: 'Test Company'
                        }
                    }
                });
                
                let results = '<h2>✍️ Signup Test Results</h2>';
                
                if (error) {
                    results += `<div class="status error">❌ Signup failed: ${error.message}</div>`;
                } else {
                    results += '<div class="status success">✅ Signup successful!</div>';
                    results += `<div class="status info">👤 User ID: ${data.user?.id}</div>`;
                    results += `<div class="status info">📧 Email: ${data.user?.email}</div>`;
                    results += `<div class="status info">✉️ Confirmed: ${data.user?.email_confirmed_at ? 'Yes' : 'No'}</div>`;
                }
                
                statusDiv.className = error ? 'status error' : 'status success';
                statusDiv.innerHTML = error ? '❌ Signup test failed' : '✅ Signup test completed!';
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Signup test failed: ${error.message}`;
                resultsDiv.innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        };
        
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('status').className = 'status info';
            document.getElementById('status').innerHTML = '⏳ Ready to test connection...';
        };
    </script>
</body>
</html>
