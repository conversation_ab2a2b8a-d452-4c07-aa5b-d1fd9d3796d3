<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        input { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Flow Test</h1>
        <p>Test signup and login with production database</p>
        
        <div id="status" class="status info">
            Ready to test authentication...
        </div>
        
        <div class="form-group">
            <label>Test Email:</label>
            <input type="email" id="testEmail" placeholder="<EMAIL>" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label>Test Password:</label>
            <input type="password" id="testPassword" placeholder="Password123!" value="TestPassword123!">
        </div>
        
        <button onclick="testSignup()">🆕 Test Signup</button>
        <button onclick="testLogin()">🔑 Test Login</button>
        <button onclick="clearResults()">🧹 Clear</button>
        
        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        const PROD_URL = 'https://irkwpzcskeqtasutqnxp.supabase.co';
        const PROD_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';
        
        const supabase = createClient(PROD_URL, PROD_KEY);
        
        window.testSignup = async function() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ Testing signup flow...';
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                    options: {
                        data: {
                            role: 'supplier',
                            fullName: 'Test User',
                            companyName: 'Test Company'
                        }
                    }
                });
                
                let results = '<h2>🆕 Signup Test Results</h2>';
                
                if (error) {
                    results += `<div class="status error">❌ Signup failed: ${error.message}</div>`;
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ Signup test failed';
                } else {
                    results += '<div class="status success">✅ Signup successful!</div>';
                    results += `<div class="status info">👤 User ID: ${data.user?.id}</div>`;
                    results += `<div class="status info">📧 Email: ${data.user?.email}</div>`;
                    results += `<div class="status info">✉️ Confirmed: ${data.user?.email_confirmed_at ? 'Yes' : 'No (Expected for production)'}</div>`;
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ Signup test completed!';
                }
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Signup test failed: ${error.message}`;
                resultsDiv.innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        };
        
        window.testLogin = async function() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ Testing login flow...';
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                let results = '<h2>🔑 Login Test Results</h2>';
                
                if (error) {
                    results += `<div class="status error">❌ Login failed: ${error.message}</div>`;
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ Login test failed';
                } else {
                    results += '<div class="status success">✅ Login successful!</div>';
                    results += `<div class="status info">👤 User ID: ${data.user?.id}</div>`;
                    results += `<div class="status info">📧 Email: ${data.user?.email}</div>`;
                    results += `<div class="status info">🕐 Last Sign In: ${data.user?.last_sign_in_at}</div>`;
                    
                    // Test profile fetch
                    try {
                        const { data: profile, error: profileError } = await supabase
                            .from('profiles')
                            .select('*')
                            .eq('id', data.user.id)
                            .single();
                        
                        if (profileError) {
                            results += `<div class="status error">❌ Profile fetch failed: ${profileError.message}</div>`;
                        } else {
                            results += '<div class="status success">✅ Profile fetched successfully!</div>';
                            results += `<div class="status info">👤 Role: ${profile.role}</div>`;
                            results += `<div class="status info">📛 Name: ${profile.full_name || 'Not set'}</div>`;
                        }
                    } catch (profileErr) {
                        results += `<div class="status error">❌ Profile fetch error: ${profileErr.message}</div>`;
                    }
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '✅ Login test completed!';
                }
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ Login test failed: ${error.message}`;
                resultsDiv.innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        };
        
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('status').className = 'status info';
            document.getElementById('status').innerHTML = 'Ready to test authentication...';
        };
    </script>
</body>
</html>
