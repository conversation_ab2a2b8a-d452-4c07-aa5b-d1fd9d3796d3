// Debug script to check consumer profile data flow
// Run this in browser console after authentication

async function debugConsumerProfile() {
  console.log('🔍 DEBUGGING CONSUMER PROFILE DATA FLOW');
  console.log('=====================================');
  
  try {
    // 1. Check localStorage session
    console.log('\n1️⃣ CHECKING LOCALSTORAGE SESSION:');
    const phoneSession = localStorage.getItem('phone_auth_session');
    if (phoneSession) {
      const session = JSON.parse(phoneSession);
      console.log('✅ Phone session found');
      console.log('📱 User phone:', session.user?.phone);
      console.log('👤 Profile data:', {
        full_name: session.profile?.full_name,
        original_full_name: session.profile?.original_full_name,
        first_name: session.profile?.first_name,
        last_name: session.profile?.last_name,
        phone: session.profile?.phone,
        role: session.profile?.role,
        id: session.profile?.id
      });
    } else {
      console.log('❌ No phone session found');
      return;
    }

    // 2. Check database structure
    console.log('\n2️⃣ CHECKING DATABASE STRUCTURE:');
    const { supabase } = await import('/src/services/authService.ts');
    
    // Check if original_full_name column exists
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'profiles')
      .eq('table_schema', 'public');
      
    if (columnsError) {
      console.log('❌ Error checking columns:', columnsError);
    } else {
      const columnNames = columns.map(col => col.column_name);
      console.log('📋 Profiles table columns:', columnNames);
      console.log('✅ Has full_name column:', columnNames.includes('full_name'));
      console.log('✅ Has original_full_name column:', columnNames.includes('original_full_name'));
    }

    // 3. Fetch current profile from database
    console.log('\n3️⃣ FETCHING PROFILE FROM DATABASE:');
    const session = JSON.parse(phoneSession);
    const phone = session.user?.phone || session.profile?.phone;
    
    if (phone) {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('phone', phone)
        .eq('role', 'consumer')
        .single();
        
      if (profileError) {
        console.log('❌ Error fetching profile:', profileError);
      } else {
        console.log('✅ Profile found in database:');
        console.log('📋 Database profile:', {
          id: profileData.id,
          full_name: profileData.full_name,
          original_full_name: profileData.original_full_name,
          first_name: profileData.first_name,
          last_name: profileData.last_name,
          phone: profileData.phone,
          role: profileData.role,
          created_at: profileData.created_at,
          updated_at: profileData.updated_at
        });
      }
    }

    // 4. Test privacy protection function
    console.log('\n4️⃣ TESTING PRIVACY PROTECTION:');
    const testName = 'Ahmed Benali';
    try {
      const { data: result, error: funcError } = await supabase
        .rpc('convert_to_privacy_protected_name', { full_name_input: testName });
        
      if (funcError) {
        console.log('❌ Privacy protection function error:', funcError);
      } else {
        console.log('✅ Privacy protection test:', testName, '→', result);
      }
    } catch (err) {
      console.log('❌ Privacy protection function not available:', err.message);
    }

    // 5. Check what OrderConfirmationStep would see
    console.log('\n5️⃣ SIMULATING ORDER CONFIRMATION LOGIC:');
    const session2 = JSON.parse(phoneSession);
    const phone2 = session2.user?.phone || session2.profile?.phone;
    
    if (phone2) {
      const { data: profileData2, error: error2 } = await supabase
        .from('profiles')
        .select('*')
        .eq('phone', phone2)
        .eq('role', 'consumer')
        .single();
        
      if (error2) {
        console.log('❌ Database fetch failed, using localStorage fallback:');
        const fallback = {
          full_name: session2.profile?.full_name || session2.profile?.first_name || 'Consumer',
          original_full_name: session2.profile?.original_full_name || '',
          phone: phone2
        };
        console.log('📋 Fallback data:', fallback);
      } else {
        console.log('✅ Database fetch successful:');
        const final = {
          full_name: profileData2.full_name || profileData2.first_name || 'Consumer',
          original_full_name: profileData2.original_full_name || '',
          phone: profileData2.phone || phone2
        };
        console.log('📋 Final display data:', final);
        console.log('🎯 CONSUMER NAME WILL SHOW AS:', final.full_name);
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugConsumerProfile();
