#!/bin/bash

# 🚀 AROUZ MARKET Development Deployment Script
# This script deploys the latest build to dev.arouzmarket.com

set -e

echo "🚀 AROUZ MARKET Development Deployment"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if build exists
if [ ! -d "dist" ]; then
    print_error "Build directory 'dist' not found. Run 'npm run build:dev' first."
    exit 1
fi

print_info "Found build directory"

# Create deployment package
print_info "Creating deployment package..."
cd dist
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
PACKAGE_NAME="arouz-dev-${TIMESTAMP}.zip"
zip -r "../${PACKAGE_NAME}" . > /dev/null 2>&1
cd ..

print_status "Deployment package created: ${PACKAGE_NAME}"

# Display deployment instructions
echo ""
print_info "DEPLOYMENT INSTRUCTIONS:"
echo "1. 📦 Upload ${PACKAGE_NAME} to your hosting provider"
echo "2. 📂 Extract the contents to your web root directory"
echo "3. 🌐 Point dev.arouzmarket.com to the extracted files"
echo ""
print_info "For Octenium hosting:"
echo "1. Login to panel.octenium.com"
echo "2. Go to File Manager"
echo "3. Upload and extract ${PACKAGE_NAME} to public_html/"
echo ""
print_status "Development deployment package ready!"
echo "🌐 Once uploaded, your site will be live at: https://dev.arouzmarket.com"
