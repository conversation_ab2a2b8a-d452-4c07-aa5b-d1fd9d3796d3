import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the DFSK models file
const dfskModelsPath = path.join(__dirname, 'src', 'services', 'data', 'dfsk-models.json');
const dfskModels = JSON.parse(fs.readFileSync(dfskModelsPath, 'utf8'));

// Find the DFSK brand in the cars.json file
const dfskBrandIndex = carsJson.findIndex(brand => brand.brand === 'DFSK');

if (dfskBrandIndex !== -1) {
  // Update the DFSK models
  carsJson[dfskBrandIndex].models = dfskModels;
  console.log(`Updated ${dfskModels.length} DFSK models in the cars.json file.`);
} else {
  // Add the DFSK brand
  carsJson.push({
    brand: 'DFSK',
    image_url: '/images/brands/dfsk.png',
    models: dfskModels
  });
  console.log(`Added DFSK brand with ${dfskModels.length} models to the cars.json file.`);
}

// Write the updated cars.json file
fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

// Clean up the temporary file
fs.unlinkSync(dfskModelsPath);

console.log('Temporary file cleaned up.');
