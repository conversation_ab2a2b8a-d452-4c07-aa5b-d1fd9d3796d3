#!/bin/bash

# AROUZ MARKET - UAT Environment Deployment Script
# This script deploys to UAT environment (dev.arouzmarket.com)

set -e

echo "🧪 AROUZ MARKET - UAT Environment Deployment"
echo "============================================="
echo "🎯 Target: dev.arouzmarket.com"
echo "🗄️ Database: Separate UAT Supabase project"
echo "🔧 Services: SAME external services (Dexatel, Google Maps)"
echo "✅ Features: ALL features + testing tools enabled"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if .env.uat exists
if [ ! -f ".env.uat" ]; then
    print_error ".env.uat file not found!"
    print_info "Please create .env.uat with UAT Supabase credentials"
    exit 1
fi

print_status ".env.uat configuration found"

# Safety check
print_warning "UAT DEPLOYMENT - This will update dev.arouzmarket.com"
read -p "Are you sure you want to deploy to UAT? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    print_info "Deployment cancelled"
    exit 0
fi

# Build UAT version
print_info "Building UAT version..."
npm run build:uat

print_status "UAT build completed"

# Check if dist folder exists
if [ ! -d "dist" ]; then
    print_error "dist folder not found. Build may have failed."
    exit 1
fi

# Check if .htaccess exists in dist
if [ ! -f "dist/.htaccess" ]; then
    print_error ".htaccess file not found in dist folder."
    exit 1
fi

print_status ".htaccess file found in dist folder"

# Create UAT deployment package
print_info "Creating UAT deployment package..."
cd dist
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
PACKAGE_NAME="arouz-uat-${TIMESTAMP}.zip"
zip -r "../${PACKAGE_NAME}" . > /dev/null 2>&1
cd ..

print_status "UAT package created: ${PACKAGE_NAME}"

echo ""
echo "📁 Contents of dist folder:"
ls -la dist/

echo ""
echo "🔧 .htaccess Configuration:"
echo "=========================="
head -20 dist/.htaccess

echo ""
echo "🎯 UAT DEPLOYMENT INSTRUCTIONS:"
echo "==============================="
echo "1. Upload the ENTIRE 'dist' folder contents to dev.arouzmarket.com"
echo "2. Make sure the .htaccess file is uploaded to the root directory"
echo "3. Ensure your hosting supports Apache mod_rewrite and mod_headers"
echo ""
echo "📋 Critical Files for UAT:"
echo "- index.html (Main app entry point)"
echo "- .htaccess (SPA routing + Security headers)"
echo "- assets/ folder (70+ optimized chunks)"
echo "- images/ folder (Brand assets)"
echo "- All other files in dist/"
echo ""
echo "🔍 After UAT deployment, test these URLs:"
echo "- https://dev.arouzmarket.com/"
echo "- https://dev.arouzmarket.com/partners"
echo "- https://dev.arouzmarket.com/marketplace"
echo "- https://dev.arouzmarket.com/basket"
echo "- https://dev.arouzmarket.com/wishlist"
echo "- https://dev.arouzmarket.com/app/dashboard"
echo ""
echo "🧪 UAT Testing Features:"
echo "- Debug mode enabled"
echo "- Performance metrics visible"
echo "- Testing tools available"
echo "- Detailed error logging"
echo "- All production features"
echo ""
echo "🗄️ Database Configuration:"
echo "- Separate UAT Supabase project"
echo "- Identical schema to production"
echo "- Test data for validation"
echo "- Safe testing environment"
echo ""
echo "🔧 External Services (SAME as production):"
echo "- Dexatel SMS OTP service"
echo "- Google Maps API"
echo "- All authentication flows"
echo "- File upload services"
echo ""
echo "✅ UAT Environment Ready for Testing!"
echo ""
echo "🔄 Testing Workflow:"
echo "1. Test all features in UAT environment"
echo "2. Validate user flows and functionality"
echo "3. Check performance and security"
echo "4. Approve changes for production"
echo "5. Deploy to production with confidence"
echo ""
echo "📦 Deployment package: ${PACKAGE_NAME}"
echo "   Upload this to dev.arouzmarket.com"

print_status "UAT deployment package ready!"
print_info "Next step: Upload to dev.arouzmarket.com and test thoroughly"
