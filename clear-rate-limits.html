<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Rate Limits</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px; border-radius: 4px; margin: 10px 0; }
        button { background: #dc3545; color: white; border: none; padding: 15px 30px; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #c82333; }
        .cleared-item { background: #f8f9fa; padding: 5px; margin: 2px 0; border-left: 3px solid #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear All Rate Limiting Data</h1>
        <p>This will remove all rate limiting restrictions and allow unlimited login attempts.</p>
        
        <button onclick="clearAllRateLimits()">🚀 Clear All Rate Limits</button>
        
        <div id="results"></div>
    </div>

    <script>
        function clearAllRateLimits() {
            const resultsDiv = document.getElementById('results');
            let clearedItems = [];
            
            console.log('🧹 Clearing all rate limiting data...');
            
            // Clear specific rate limit keys
            const rateLimitKeys = [
                'loginAttempts',
                'lastLoginAttempt',
                'rememberMe'
            ];
            
            rateLimitKeys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    clearedItems.push(key);
                    console.log('Removed:', key);
                }
            });
            
            // Clear any keys that contain rate limiting patterns
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.includes('rateLimit') || 
                    key.includes('LOGIN_IP') || 
                    key.includes('LOGIN_USER') || 
                    key.includes('API_IP') || 
                    key.includes('ADMIN_ACTION') ||
                    key.includes('attempts') ||
                    key.includes('blocked') ||
                    key.includes('windowStart') ||
                    key.includes('rateLimitStore')) {
                    localStorage.removeItem(key);
                    clearedItems.push(key);
                    console.log('Removed:', key);
                }
            });
            
            // Clear sessionStorage as well
            const sessionKeys = Object.keys(sessionStorage);
            sessionKeys.forEach(key => {
                if (key.includes('rateLimit') || 
                    key.includes('attempts') ||
                    key.includes('blocked')) {
                    sessionStorage.removeItem(key);
                    clearedItems.push(`[Session] ${key}`);
                    console.log('Removed from session:', key);
                }
            });
            
            let html = '<div class="success">✅ All rate limiting data cleared!</div>';
            html += '<div class="success">🚀 You can now make unlimited login attempts!</div>';
            
            if (clearedItems.length > 0) {
                html += '<h3>Cleared Items:</h3>';
                clearedItems.forEach(item => {
                    html += `<div class="cleared-item">${item}</div>`;
                });
            } else {
                html += '<div class="success">No rate limiting data found to clear.</div>';
            }
            
            html += '<div class="success">Please refresh the page to ensure all changes take effect.</div>';
            
            resultsDiv.innerHTML = html;
            
            console.log('✅ All rate limiting data cleared!');
            console.log('🚀 You can now make unlimited login attempts!');
            
            // Auto-refresh after 2 seconds
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
    </script>
</body>
</html>
