# Supabase User Verification Guide

This guide explains how to verify new users who sign up as Supplier/Manufacturer or Merchant Retailer in your Supabase project.

## Accessing the Supabase Dashboard

1. Go to [https://app.supabase.io/](https://app.supabase.io/) and log in with your credentials
2. Select the "AROUZ MARKET Project" with ID: irkwpzcskeqtasutqnxp

## Viewing and Verifying New Users

### Method 1: Using the Authentication Tab

1. In the Supabase dashboard, navigate to the **Authentication** section in the left sidebar
2. Click on **Users** to see a list of all registered users
3. New users will have their email verification status shown in the "Email Confirmed" column
4. To manually verify a user:
   - Click on the user's row to open their details
   - In the user details panel, you can toggle the "Email confirmed" status to "Yes"
   - Click "Save" to apply the changes

### Method 2: Using SQL Editor

You can also verify users using SQL queries:

1. In the Supabase dashboard, navigate to the **SQL Editor** section in the left sidebar
2. Create a new query and use the following SQL to view all unverified users:

```sql
SELECT
  id,
  email,
  phone,
  role,
  full_name,
  company_name,
  store_name,
  email_verified,
  created_at
FROM profiles
WHERE email_verified = false
ORDER BY created_at DESC;
```

3. To verify a specific user, run:

```sql
-- Update auth.users table to confirm email
UPDATE auth.users
SET email_confirmed_at = CURRENT_TIMESTAMP
WHERE email = '<EMAIL>';

-- Update profiles table to mark as verified
UPDATE profiles
SET email_verified = true
WHERE email = '<EMAIL>';
```

Replace `'<EMAIL>'` with the actual email of the user you want to verify.

## Checking User Roles and Details

To see all users with their roles:

```sql
SELECT
  p.id,
  p.email,
  p.role,
  p.full_name,
  p.company_name,
  p.store_name,
  p.email_verified,
  p.created_at
FROM profiles p
ORDER BY p.created_at DESC;
```

To filter by role:

```sql
-- For Supplier/Manufacturer users
SELECT * FROM profiles WHERE role = 'supplier';

-- For Merchant Retailer users
SELECT * FROM profiles WHERE role = 'merchant';
```

## Troubleshooting Common Issues

### User Can't Log In After Verification

If a user still can't log in after being verified, check:

1. Ensure both the `auth.users` table and the `profiles` table have been updated
2. Verify that the user's account hasn't been disabled (check the `is_banned` field in `auth.users`)
3. Check for any role conflicts in the application (a user trying to log in with a different role)

### Missing Profile Information

If a user's profile information is missing:

```sql
-- Check if profile exists
SELECT * FROM profiles WHERE id = 'user-uuid-here';

-- Create profile if missing
INSERT INTO profiles (id, email, role, full_name, email_verified)
VALUES ('user-uuid-here', '<EMAIL>', 'supplier', 'User Name', true);
```

## Security Best Practices

1. Always verify the identity of users before manually confirming their accounts
2. For B2B users (Supplier/Manufacturer), consider implementing additional verification steps
3. Regularly audit user accounts and remove any suspicious or inactive accounts
4. Use Row Level Security (RLS) policies to ensure users can only access their authorized data

## Additional Resources

- [Supabase Authentication Documentation](https://supabase.com/docs/guides/auth)
- [Managing User Data in Supabase](https://supabase.com/docs/guides/auth/managing-user-data)
- [Row Level Security in Supabase](https://supabase.com/docs/guides/auth/row-level-security)
