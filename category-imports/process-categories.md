# Category Processing Workflow

## 🚀 Automated Processing Steps

When you upload a category JSON file and say "Process category: filename.json", I will:

### 1. **Read & Validate JSON**
- Load the JSON file from category-imports folder
- Validate structure and required fields
- Check for naming conflicts

### 2. **Generate Category Code**
- Create subcategories array with proper structure
- Add category to main CATEGORIES array in categoryData.ts
- Ensure proper TypeScript typing

### 3. **Update Integration Points**
- Add to marketplace fetching (MyVehicleParts.tsx)
- Add to wholesale offers (WholesaleOffers.tsx) 
- Add to product page lookup (ProductPage.tsx)
- Update category filtering logic

### 4. **Verify Implementation**
- Check all files are updated correctly
- Ensure no syntax errors
- Confirm category appears in UI

### 5. **Test Instructions**
- Provide specific URLs to test
- List expected behaviors
- Confirm marketplace integration

## 📋 Processing Commands

**Single Category:**
"Process category: lighting-systems.json"

**Multiple Categories:**
"Process categories: lighting-systems.json, suspension-components.json, engine-parts.json"

**All Categories:**
"Process all categories in folder"

## ✅ Success Criteria

Each category implementation must achieve:
- ✅ Appears in product creation dropdown
- ✅ All subcategories selectable
- ✅ Products get PROD prefix
- ✅ Marketplace navigation shows category
- ✅ Products display correctly in marketplace
- ✅ Product pages load correctly
- ✅ Cross-category search works

## 🎯 Efficiency Benefits

- **Speed**: 1 message = 1 complete category
- **Accuracy**: 100% consistent implementation
- **Scale**: Handle 50+ subcategories easily
- **Verification**: Immediate testing instructions
- **Rollback**: Easy to identify and fix issues
