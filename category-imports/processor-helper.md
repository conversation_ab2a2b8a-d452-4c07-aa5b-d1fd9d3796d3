# Category Processor Helper

## 🔧 Automatic Processing Logic

When you upload a JSON file like:
```json
{
  "Engine": [
    "Vacuum Pump Assembly",
    "Engine Control Unit (ECU)", 
    "Oxygen Sensor (Lambda, NOx)"
  ]
}
```

I will automatically:

### 1. **Generate Category Data**
- **Category ID**: `engine` (lowercase, hyphenated)
- **Category Name**: `Engine` (from JSON key)
- **Display Name**: `Engine` (same as name)
- **Description**: Auto-generated based on category type

### 2. **Generate Subcategory Data**
- **Subcategory IDs**: Auto-generated from names
  - "Vacuum Pump Assembly" → `vacuum-pump-assembly`
  - "Engine Control Unit (ECU)" → `engine-control-unit-ecu`
  - "Oxygen Sensor (Lambda, NOx)" → `oxygen-sensor-lambda-nox`

### 3. **Create Full Implementation**
```typescript
// Generated subcategories array
const engineSubcategories: SubcategoryData[] = [
  { id: 'vacuum-pump-assembly', name: 'Vacuum Pump Assembly', displayName: 'Vacuum Pump Assembly', categoryId: 'engine' },
  { id: 'engine-control-unit-ecu', name: 'Engine Control Unit (ECU)', displayName: 'Engine Control Unit (ECU)', categoryId: 'engine' },
  { id: 'oxygen-sensor-lambda-nox', name: 'Oxygen Sensor (Lambda, NOx)', displayName: 'Oxygen Sensor (Lambda, NOx)', categoryId: 'engine' }
];

// Generated category
{
  id: 'engine',
  name: 'Engine',
  displayName: 'Engine', 
  description: 'Engine components and internal parts',
  subcategories: engineSubcategories
}
```

### 4. **Update All Integration Points**
- Add to `categoryData.ts`
- Update marketplace pages
- Update product page lookup
- Add to wholesale offers
- Update category filtering

## 🎯 Benefits of Simplified Structure

✅ **Ultra Simple**: Just category name + array of subcategories
✅ **No Manual ID Generation**: Automatic kebab-case conversion
✅ **No Repetitive Structure**: Minimal JSON required
✅ **Copy-Paste Friendly**: Easy to create from lists
✅ **Error-Proof**: Less chance of typos or structure mistakes

## 📋 Processing Commands

**Single Category:**
"Process category: engine.json"

**Multiple Categories:**  
"Process categories: engine.json, suspension.json, electrical.json"

**Batch Processing:**
"Process all categories in folder"
