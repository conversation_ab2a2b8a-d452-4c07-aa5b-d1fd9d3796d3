# Category Import System

## 📋 Instructions for Adding Categories

### JSON File Format
Each category should be a separate JSON file named: `{category-id}.json`

### Required JSON Structure:
```json
{
  "Category Name": [
    "Subcategory 1",
    "Subcategory 2",
    "Subcategory 3"
  ]
}
```

### Example File: `engine.json`
```json
{
  "Engine": [
    "Vacuum Pump Assembly",
    "Engine Control Unit (ECU)",
    "Oxygen Sensor (Lambda, NOx)",
    "Engine Knock Sensor",
    "Engine Mount Bracket",
    "Crankshaft Oil Seal",
    "Piston Ring Set",
    "Crankshaft Main Bearings",
    "Connecting Rod Bearings",
    "Full Engine Gasket Kit",
    "Piston Assembly",
    "Undershield / Skid Plate",
    "Flywheel Assembly"
  ]
}
```

### Notes:
- All categories will use "PROD" prefix
- Files will be processed in alphabetical order
- Each category will be automatically integrated into:
  - Product creation dropdowns
  - Marketplace navigation
  - All Other Categories table
  - Product page lookup

### Upload Process:
1. Create JSON file: `{category-name}.json`
2. Use simple structure: `{"Category Name": ["Sub1", "Sub2", "Sub3"]}`
3. Upload to this folder
4. Send message: "Process category: {filename}"
5. AI will automatically:
   - Generate category ID from name
   - Create all subcategory IDs
   - Add to marketplace integration
   - Implement with 100% accuracy
