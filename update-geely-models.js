import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Geely models file
const geelyModelsPath = path.join(__dirname, 'src', 'services', 'data', 'geely-models.json');
const geelyModels = JSON.parse(fs.readFileSync(geelyModelsPath, 'utf8'));

// Find the Geely brand in the cars.json file
const geelyBrandIndex = carsJson.findIndex(brand => brand.brand === 'Geely');

if (geelyBrandIndex !== -1) {
  // Update the Geely models
  carsJson[geelyBrandIndex].models = geelyModels;

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Added ${geelyModels.length} Geely models to the cars.json file.`);
} else {
  console.error('Geely brand not found in the cars.json file.');
}

// Clean up the temporary file
fs.unlinkSync(geelyModelsPath);

console.log('Temporary file cleaned up.');
