diff --git a/arouz-website.zip b/arouz-website.zip
new file mode 100644
index 0000000..598a328
Binary files /dev/null and b/arouz-website.zip differ
diff --git a/convert-favicon.js b/convert-favicon.js
new file mode 100644
index 0000000..42f8594
--- /dev/null
+++ b/convert-favicon.js
@@ -0,0 +1,37 @@
+import sharp from 'sharp';
+import fs from 'fs';
+import path from 'path';
+import { fileURLToPath } from 'url';
+
+const __filename = fileURLToPath(import.meta.url);
+const __dirname = path.dirname(__filename);
+
+async function convertPngToIco() {
+  try {
+    // Read the PNG file
+    const pngPath = path.join(__dirname, 'public', 'images', 'Favicon1.png');
+
+    // Create resized versions for favicon (16x16, 32x32, 48x48)
+    const sizes = [16, 32, 48];
+
+    for (const size of sizes) {
+      await sharp(pngPath)
+        .resize(size, size)
+        .toFile(path.join(__dirname, 'public', `favicon-${size}.png`));
+
+      console.log(`Created favicon-${size}.png`);
+    }
+
+    // Copy the original PNG to the public root directory
+    await sharp(pngPath)
+      .toFile(path.join(__dirname, 'public', 'favicon.png'));
+
+    console.log('Created favicon.png in public directory');
+
+    console.log('Favicon conversion complete!');
+  } catch (error) {
+    console.error('Error converting favicon:', error);
+  }
+}
+
+convertPngToIco();
diff --git a/index.html b/index.html
index 68bdc8d..2b97a84 100644
--- a/index.html
+++ b/index.html
@@ -4,11 +4,26 @@
   <head>
     <meta charset="UTF-8" />
     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
+    <meta name="theme-color" content="#ff8000" />
+    <link rel="preload" href="/favicon.png" as="image" type="image/png" />
     <title>AROUZ MARKET - Algeria's First Intelligent Auto Parts Ecosystem</title>
     <meta name="description" content="The first and only Algerian platform to integrate auto parts manufacturers, suppliers, merchants, consumers, and distribution services into a single ecosystem." />
     <meta name="author" content="AROUZ MARKET" />
     <meta name="keywords" content="auto parts, Algeria, automotive, parts marketplace, suppliers, merchants, POS, inventory management" />
-    <meta property="og:image" content="/og-image.png" />
+    <meta property="og:title" content="AROUZ MARKET - Algeria's First Intelligent Auto Parts Ecosystem" />
+    <meta property="og:description" content="The first and only Algerian platform to integrate auto parts manufacturers, suppliers, merchants, consumers, and distribution services into a single ecosystem." />
+    <meta property="og:image" content="https://arouzmarket.com/images/HeroSection.png" />
+    <meta property="og:url" content="https://arouzmarket.com" />
+    <meta property="og:type" content="website" />
+    <!-- Twitter Card tags -->
+    <meta name="twitter:card" content="summary_large_image" />
+    <meta name="twitter:title" content="AROUZ MARKET - Algeria's First Intelligent Auto Parts Ecosystem" />
+    <meta name="twitter:description" content="The first and only Algerian platform to integrate auto parts manufacturers, suppliers, merchants, consumers, and distribution services into a single ecosystem." />
+    <meta name="twitter:image" content="https://arouzmarket.com/images/HeroSection.png" />
+    <link rel="icon" href="/favicon.png" type="image/png" />
+    <link rel="shortcut icon" href="/favicon.png" type="image/png" />
+    <link rel="apple-touch-icon" href="/favicon.png" />
+    <link rel="manifest" href="/manifest.json" />
     <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap">
   </head>
 
diff --git a/package-lock.json b/package-lock.json
index 67feb5f..b8cf8a6 100644
--- a/package-lock.json
+++ b/package-lock.json
@@ -56,6 +56,7 @@
         "react-dom": "^18.3.1",
         "react-hook-form": "^7.53.0",
         "react-i18next": "^15.4.1",
+        "react-photo-view": "^1.2.7",
         "react-resizable-panels": "^2.1.3",
         "react-router-dom": "^6.26.2",
         "recharts": "^2.12.7",
@@ -79,6 +80,7 @@
         "globals": "^15.9.0",
         "lovable-tagger": "^1.1.7",
         "postcss": "^8.4.47",
+        "sharp": "^0.34.1",
         "tailwindcss": "^3.4.11",
         "typescript": "^5.5.3",
         "typescript-eslint": "^8.0.1",
@@ -159,6 +161,17 @@
         "node": ">=6.9.0"
       }
     },
+    "node_modules/@emnapi/runtime": {
+      "version": "1.4.3",
+      "resolved": "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.3.tgz",
+      "integrity": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==",
+      "dev": true,
+      "license": "MIT",
+      "optional": true,
+      "dependencies": {
+        "tslib": "^2.4.0"
+      }
+    },
     "node_modules/@esbuild/aix-ppc64": {
       "version": "0.21.5",
       "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz",
@@ -816,6 +829,403 @@
         "url": "https://github.com/sponsors/nzakas"
       }
     },
+    "node_modules/@img/sharp-darwin-arm64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.34.1.tgz",
+      "integrity": "sha512-pn44xgBtgpEbZsu+lWf2KNb6OAf70X68k+yk69Ic2Xz11zHR/w24/U49XT7AeRwJ0Px+mhALhU5LPci1Aymk7A==",
+      "cpu": [
+        "arm64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "darwin"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-darwin-arm64": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-darwin-x64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-darwin-x64/-/sharp-darwin-x64-0.34.1.tgz",
+      "integrity": "sha512-VfuYgG2r8BpYiOUN+BfYeFo69nP/MIwAtSJ7/Zpxc5QF3KS22z8Pvg3FkrSFJBPNQ7mmcUcYQFBmEQp7eu1F8Q==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "darwin"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-darwin-x64": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-libvips-darwin-arm64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.1.0.tgz",
+      "integrity": "sha512-HZ/JUmPwrJSoM4DIQPv/BfNh9yrOA8tlBbqbLz4JZ5uew2+o22Ik+tHQJcih7QJuSa0zo5coHTfD5J8inqj9DA==",
+      "cpu": [
+        "arm64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "darwin"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-darwin-x64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-x64/-/sharp-libvips-darwin-x64-1.1.0.tgz",
+      "integrity": "sha512-Xzc2ToEmHN+hfvsl9wja0RlnXEgpKNmftriQp6XzY/RaSfwD9th+MSh0WQKzUreLKKINb3afirxW7A0fz2YWuQ==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "darwin"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linux-arm": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm/-/sharp-libvips-linux-arm-1.1.0.tgz",
+      "integrity": "sha512-s8BAd0lwUIvYCJyRdFqvsj+BJIpDBSxs6ivrOPm/R7piTs5UIwY5OjXrP2bqXC9/moGsyRa37eYWYCOGVXxVrA==",
+      "cpu": [
+        "arm"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linux-arm64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm64/-/sharp-libvips-linux-arm64-1.1.0.tgz",
+      "integrity": "sha512-IVfGJa7gjChDET1dK9SekxFFdflarnUB8PwW8aGwEoF3oAsSDuNUTYS+SKDOyOJxQyDC1aPFMuRYLoDInyV9Ew==",
+      "cpu": [
+        "arm64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linux-ppc64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-ppc64/-/sharp-libvips-linux-ppc64-1.1.0.tgz",
+      "integrity": "sha512-tiXxFZFbhnkWE2LA8oQj7KYR+bWBkiV2nilRldT7bqoEZ4HiDOcePr9wVDAZPi/Id5fT1oY9iGnDq20cwUz8lQ==",
+      "cpu": [
+        "ppc64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linux-s390x": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-s390x/-/sharp-libvips-linux-s390x-1.1.0.tgz",
+      "integrity": "sha512-xukSwvhguw7COyzvmjydRb3x/09+21HykyapcZchiCUkTThEQEOMtBj9UhkaBRLuBrgLFzQ2wbxdeCCJW/jgJA==",
+      "cpu": [
+        "s390x"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linux-x64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-x64/-/sharp-libvips-linux-x64-1.1.0.tgz",
+      "integrity": "sha512-yRj2+reB8iMg9W5sULM3S74jVS7zqSzHG3Ol/twnAAkAhnGQnpjj6e4ayUz7V+FpKypwgs82xbRdYtchTTUB+Q==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linuxmusl-arm64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-arm64/-/sharp-libvips-linuxmusl-arm64-1.1.0.tgz",
+      "integrity": "sha512-jYZdG+whg0MDK+q2COKbYidaqW/WTz0cc1E+tMAusiDygrM4ypmSCjOJPmFTvHHJ8j/6cAGyeDWZOsK06tP33w==",
+      "cpu": [
+        "arm64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-libvips-linuxmusl-x64": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-x64/-/sharp-libvips-linuxmusl-x64-1.1.0.tgz",
+      "integrity": "sha512-wK7SBdwrAiycjXdkPnGCPLjYb9lD4l6Ze2gSdAGVZrEL05AOUJESWU2lhlC+Ffn5/G+VKuSm6zzbQSzFX/P65A==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-linux-arm": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm/-/sharp-linux-arm-0.34.1.tgz",
+      "integrity": "sha512-anKiszvACti2sGy9CirTlNyk7BjjZPiML1jt2ZkTdcvpLU1YH6CXwRAZCA2UmRXnhiIftXQ7+Oh62Ji25W72jA==",
+      "cpu": [
+        "arm"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-linux-arm": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-linux-arm64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm64/-/sharp-linux-arm64-0.34.1.tgz",
+      "integrity": "sha512-kX2c+vbvaXC6vly1RDf/IWNXxrlxLNpBVWkdpRq5Ka7OOKj6nr66etKy2IENf6FtOgklkg9ZdGpEu9kwdlcwOQ==",
+      "cpu": [
+        "arm64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-linux-arm64": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-linux-s390x": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.34.1.tgz",
+      "integrity": "sha512-7s0KX2tI9mZI2buRipKIw2X1ufdTeaRgwmRabt5bi9chYfhur+/C1OXg3TKg/eag1W+6CCWLVmSauV1owmRPxA==",
+      "cpu": [
+        "s390x"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-linux-s390x": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-linux-x64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-linux-x64/-/sharp-linux-x64-0.34.1.tgz",
+      "integrity": "sha512-wExv7SH9nmoBW3Wr2gvQopX1k8q2g5V5Iag8Zk6AVENsjwd+3adjwxtp3Dcu2QhOXr8W9NusBU6XcQUohBZ5MA==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-linux-x64": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-linuxmusl-arm64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.34.1.tgz",
+      "integrity": "sha512-DfvyxzHxw4WGdPiTF0SOHnm11Xv4aQexvqhRDAoD00MzHekAj9a/jADXeXYCDFH/DzYruwHbXU7uz+H+nWmSOQ==",
+      "cpu": [
+        "arm64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-linuxmusl-arm64": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-linuxmusl-x64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-x64/-/sharp-linuxmusl-x64-0.34.1.tgz",
+      "integrity": "sha512-pax/kTR407vNb9qaSIiWVnQplPcGU8LRIJpDT5o8PdAx5aAA7AS3X9PS8Isw1/WfqgQorPotjrZL3Pqh6C5EBg==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0",
+      "optional": true,
+      "os": [
+        "linux"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-libvips-linuxmusl-x64": "1.1.0"
+      }
+    },
+    "node_modules/@img/sharp-wasm32": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.34.1.tgz",
+      "integrity": "sha512-YDybQnYrLQfEpzGOQe7OKcyLUCML4YOXl428gOOzBgN6Gw0rv8dpsJ7PqTHxBnXnwXr8S1mYFSLSa727tpz0xg==",
+      "cpu": [
+        "wasm32"
+      ],
+      "dev": true,
+      "license": "Apache-2.0 AND LGPL-3.0-or-later AND MIT",
+      "optional": true,
+      "dependencies": {
+        "@emnapi/runtime": "^1.4.0"
+      },
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-win32-ia32": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.34.1.tgz",
+      "integrity": "sha512-WKf/NAZITnonBf3U1LfdjoMgNO5JYRSlhovhRhMxXVdvWYveM4kM3L8m35onYIdh75cOMCo1BexgVQcCDzyoWw==",
+      "cpu": [
+        "ia32"
+      ],
+      "dev": true,
+      "license": "Apache-2.0 AND LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "win32"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
+    "node_modules/@img/sharp-win32-x64": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/@img/sharp-win32-x64/-/sharp-win32-x64-0.34.1.tgz",
+      "integrity": "sha512-hw1iIAHpNE8q3uMIRCgGOeDoz9KtFNarFLQclLxr/LK1VBkj8nby18RjFvr6aP7USRYAjTZW6yisnBWMX571Tw==",
+      "cpu": [
+        "x64"
+      ],
+      "dev": true,
+      "license": "Apache-2.0 AND LGPL-3.0-or-later",
+      "optional": true,
+      "os": [
+        "win32"
+      ],
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      }
+    },
     "node_modules/@isaacs/cliui": {
       "version": "8.0.2",
       "resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz",
@@ -3994,6 +4404,20 @@
         }
       }
     },
+    "node_modules/color": {
+      "version": "4.2.3",
+      "resolved": "https://registry.npmjs.org/color/-/color-4.2.3.tgz",
+      "integrity": "sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==",
+      "dev": true,
+      "license": "MIT",
+      "dependencies": {
+        "color-convert": "^2.0.1",
+        "color-string": "^1.9.0"
+      },
+      "engines": {
+        "node": ">=12.5.0"
+      }
+    },
     "node_modules/color-convert": {
       "version": "2.0.1",
       "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",
@@ -4012,6 +4436,17 @@
       "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",
       "license": "MIT"
     },
+    "node_modules/color-string": {
+      "version": "1.9.1",
+      "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz",
+      "integrity": "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==",
+      "dev": true,
+      "license": "MIT",
+      "dependencies": {
+        "color-name": "^1.0.0",
+        "simple-swizzle": "^0.2.2"
+      }
+    },
     "node_modules/commander": {
       "version": "4.1.1",
       "resolved": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz",
@@ -4221,6 +4656,16 @@
       "dev": true,
       "license": "MIT"
     },
+    "node_modules/detect-libc": {
+      "version": "2.0.3",
+      "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.3.tgz",
+      "integrity": "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==",
+      "dev": true,
+      "license": "Apache-2.0",
+      "engines": {
+        "node": ">=8"
+      }
+    },
     "node_modules/detect-node-es": {
       "version": "1.1.0",
       "resolved": "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz",
@@ -4956,6 +5401,13 @@
         "loose-envify": "^1.0.0"
       }
     },
+    "node_modules/is-arrayish": {
+      "version": "0.3.2",
+      "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz",
+      "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==",
+      "dev": true,
+      "license": "MIT"
+    },
     "node_modules/is-binary-path": {
       "version": "2.1.0",
       "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz",
@@ -6231,6 +6683,16 @@
       "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==",
       "license": "MIT"
     },
+    "node_modules/react-photo-view": {
+      "version": "1.2.7",
+      "resolved": "https://registry.npmjs.org/react-photo-view/-/react-photo-view-1.2.7.tgz",
+      "integrity": "sha512-MfOWVPxuibncRLaycZUNxqYU8D9IA+rbGDDaq6GM8RIoGJal592hEJoRAyRSI7ZxyyJNJTLMUWWL3UIXHJJOpw==",
+      "license": "Apache-2.0",
+      "peerDependencies": {
+        "react": ">=16.8.0",
+        "react-dom": ">=16.8.0"
+      }
+    },
     "node_modules/react-remove-scroll": {
       "version": "2.6.0",
       "resolved": "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.0.tgz",
@@ -6539,9 +7001,9 @@
       }
     },
     "node_modules/semver": {
-      "version": "7.6.3",
-      "resolved": "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz",
-      "integrity": "sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==",
+      "version": "7.7.1",
+      "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz",
+      "integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==",
       "dev": true,
       "license": "ISC",
       "bin": {
@@ -6551,6 +7013,47 @@
         "node": ">=10"
       }
     },
+    "node_modules/sharp": {
+      "version": "0.34.1",
+      "resolved": "https://registry.npmjs.org/sharp/-/sharp-0.34.1.tgz",
+      "integrity": "sha512-1j0w61+eVxu7DawFJtnfYcvSv6qPFvfTaqzTQ2BLknVhHTwGS8sc63ZBF4rzkWMBVKybo4S5OBtDdZahh2A1xg==",
+      "dev": true,
+      "hasInstallScript": true,
+      "license": "Apache-2.0",
+      "dependencies": {
+        "color": "^4.2.3",
+        "detect-libc": "^2.0.3",
+        "semver": "^7.7.1"
+      },
+      "engines": {
+        "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
+      },
+      "funding": {
+        "url": "https://opencollective.com/libvips"
+      },
+      "optionalDependencies": {
+        "@img/sharp-darwin-arm64": "0.34.1",
+        "@img/sharp-darwin-x64": "0.34.1",
+        "@img/sharp-libvips-darwin-arm64": "1.1.0",
+        "@img/sharp-libvips-darwin-x64": "1.1.0",
+        "@img/sharp-libvips-linux-arm": "1.1.0",
+        "@img/sharp-libvips-linux-arm64": "1.1.0",
+        "@img/sharp-libvips-linux-ppc64": "1.1.0",
+        "@img/sharp-libvips-linux-s390x": "1.1.0",
+        "@img/sharp-libvips-linux-x64": "1.1.0",
+        "@img/sharp-libvips-linuxmusl-arm64": "1.1.0",
+        "@img/sharp-libvips-linuxmusl-x64": "1.1.0",
+        "@img/sharp-linux-arm": "0.34.1",
+        "@img/sharp-linux-arm64": "0.34.1",
+        "@img/sharp-linux-s390x": "0.34.1",
+        "@img/sharp-linux-x64": "0.34.1",
+        "@img/sharp-linuxmusl-arm64": "0.34.1",
+        "@img/sharp-linuxmusl-x64": "0.34.1",
+        "@img/sharp-wasm32": "0.34.1",
+        "@img/sharp-win32-ia32": "0.34.1",
+        "@img/sharp-win32-x64": "0.34.1"
+      }
+    },
     "node_modules/shebang-command": {
       "version": "2.0.0",
       "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz",
@@ -6584,6 +7087,16 @@
         "url": "https://github.com/sponsors/isaacs"
       }
     },
+    "node_modules/simple-swizzle": {
+      "version": "0.2.2",
+      "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz",
+      "integrity": "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==",
+      "dev": true,
+      "license": "MIT",
+      "dependencies": {
+        "is-arrayish": "^0.3.1"
+      }
+    },
     "node_modules/sonner": {
       "version": "1.5.0",
       "resolved": "https://registry.npmjs.org/sonner/-/sonner-1.5.0.tgz",
diff --git a/package.json b/package.json
index 5380258..ccc0ea3 100644
--- a/package.json
+++ b/package.json
@@ -59,6 +59,7 @@
     "react-dom": "^18.3.1",
     "react-hook-form": "^7.53.0",
     "react-i18next": "^15.4.1",
+    "react-photo-view": "^1.2.7",
     "react-resizable-panels": "^2.1.3",
     "react-router-dom": "^6.26.2",
     "recharts": "^2.12.7",
@@ -82,6 +83,7 @@
     "globals": "^15.9.0",
     "lovable-tagger": "^1.1.7",
     "postcss": "^8.4.47",
+    "sharp": "^0.34.1",
     "tailwindcss": "^3.4.11",
     "typescript": "^5.5.3",
     "typescript-eslint": "^8.0.1",
diff --git a/public/favicon.ico b/public/favicon.ico
deleted file mode 100644
index 32e0122..0000000
Binary files a/public/favicon.ico and /dev/null differ
diff --git a/public/favicon.png b/public/favicon.png
new file mode 100644
index 0000000..f31a5dc
Binary files /dev/null and b/public/favicon.png differ
diff --git a/public/manifest.json b/public/manifest.json
new file mode 100644
index 0000000..d90827f
--- /dev/null
+++ b/public/manifest.json
@@ -0,0 +1,19 @@
+{
+  "name": "AROUZ MARKET",
+  "short_name": "AROUZ",
+  "icons": [
+    {
+      "src": "/favicon.png",
+      "sizes": "192x192",
+      "type": "image/png"
+    },
+    {
+      "src": "/favicon.png",
+      "sizes": "512x512",
+      "type": "image/png"
+    }
+  ],
+  "theme_color": "#ff8000",
+  "background_color": "#ffffff",
+  "display": "standalone"
+}
diff --git a/public/og-image.png b/public/og-image.png
deleted file mode 100644
index 89e22ee..0000000
Binary files a/public/og-image.png and /dev/null differ
diff --git a/src/features/products/components/ProductsDataGrid.tsx b/src/features/products/components/ProductsDataGrid.tsx
index 194e335..8c7afa6 100644
--- a/src/features/products/components/ProductsDataGrid.tsx
+++ b/src/features/products/components/ProductsDataGrid.tsx
@@ -1,4 +1,4 @@
-import React, { useState, useEffect, useMemo } from 'react';
+import React, { useState, useEffect, useMemo, useCallback } from 'react';
 import {
   useReactTable,
   getCoreRowModel,
@@ -12,8 +12,10 @@ import {
   VisibilityState,
 } from '@tanstack/react-table';
 import { useVirtualizer } from '@tanstack/react-virtual';
-import { CategoryTableConfig } from '../types/product.types';
+import { CategoryTableConfig, ProductColumnDef } from '../types/product.types';
 import { CellRenderer } from './cells/CellRenderer';
+import { FilterPopover } from './filters/FilterPopover';
+import { FilterPresetsMenu } from './filters/FilterPresetsMenu';
 import { Button } from '@/components/ui/button';
 import { Input } from '@/components/ui/input';
 import {
@@ -22,14 +24,17 @@ import {
   ChevronsLeft,
   ChevronsRight,
   Search,
+  SlidersHorizontal,
 } from 'lucide-react';
 import { cn } from '@/lib/utils';
+import { Badge } from '@/components/ui/badge';
 
 interface ProductsDataGridProps {
   data: any[];
   tableConfig: CategoryTableConfig;
   isLoading?: boolean;
   onRowClick?: (row: any) => void;
+  onCellEdit?: (rowId: string, columnId: string, value: any) => void;
 }
 
 export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
@@ -37,6 +42,7 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
   tableConfig,
   isLoading = false,
   onRowClick,
+  onCellEdit,
 }) => {
   // Table state
   const [sorting, setSorting] = useState<SortingState>([]);
@@ -44,28 +50,65 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
   const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
   const [globalFilter, setGlobalFilter] = useState('');
   const [rowSelection, setRowSelection] = useState({});
-  
+  const [editingCell, setEditingCell] = useState<{rowId: string; columnId: string} | null>(null);
+
+  // Handle cell edit
+  const handleCellEdit = (rowId: string, columnId: string, value: any) => {
+    onCellEdit?.(rowId, columnId, value);
+    setEditingCell(null);
+  };
+
+
+
   // Create columns from table config
   const columns = useMemo<ColumnDef<any>[]>(() => {
     return tableConfig.columns.map(colDef => ({
       id: colDef.id,
       accessorKey: colDef.accessorKey,
-      header: colDef.header,
+      header: ({ column }) => {
+        return (
+          <div className="flex items-center gap-1 w-full">
+            <span className="font-medium text-sm whitespace-normal">{colDef.header}</span>
+            {colDef.enableFiltering && colDef.filterType && (
+              <FilterPopover
+                column={colDef}
+                value={column.getFilterValue()}
+                onChange={(value) => column.setFilterValue(value)}
+                onClear={() => column.setFilterValue(undefined)}
+              />
+            )}
+          </div>
+        );
+      },
       size: colDef.size,
-      minSize: colDef.minSize,
-      maxSize: colDef.maxSize,
       enableSorting: colDef.enableSorting,
       enableColumnFilter: colDef.enableFiltering,
-      cell: ({ getValue }) => (
-        <CellRenderer
-          column={colDef}
-          value={getValue()}
-          isEditable={false}
-        />
-      ),
+      cell: ({ row, getValue }) => {
+        const isEditing = editingCell?.rowId === row.id && editingCell?.columnId === colDef.id;
+        return (
+          <div
+            className={cn(
+              "w-full h-full",
+              colDef.editable && "cursor-pointer hover:bg-muted/30"
+            )}
+            onClick={() => {
+              if (colDef.editable && !isEditing) {
+                setEditingCell({ rowId: row.id, columnId: colDef.id });
+              }
+            }}
+          >
+            <CellRenderer
+              column={colDef}
+              value={getValue()}
+              onChange={(newValue) => handleCellEdit(row.id, colDef.id, newValue)}
+              isEditable={isEditing}
+            />
+          </div>
+        );
+      },
     }));
-  }, [tableConfig]);
-  
+  }, [tableConfig, editingCell, onCellEdit]);
+
   // Initialize table
   const table = useReactTable({
     data,
@@ -89,30 +132,32 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
     getPaginationRowModel: getPaginationRowModel(),
     debugTable: true,
   });
-  
+
   // Set default pagination
   useEffect(() => {
     table.setPageSize(50);
   }, [table]);
-  
+
   // Virtualization setup
   const { rows } = table.getRowModel();
   const parentRef = React.useRef<HTMLDivElement>(null);
-  
+
   const rowVirtualizer = useVirtualizer({
     count: rows.length,
     estimateSize: () => 48, // estimated row height
     getScrollElement: () => parentRef.current,
     overscan: 10,
   });
-  
+
   // Calculate total width of all columns
   const totalWidth = useMemo(() => {
     return table.getAllColumns().reduce((acc, column) => {
       return acc + (column.getSize() || 150);
     }, 0);
   }, [table.getAllColumns()]);
-  
+
+
+
   return (
     <div className="flex flex-col gap-4">
       {/* Search and filters */}
@@ -126,55 +171,83 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
             className="pl-8"
           />
         </div>
-        
+
         <div className="flex items-center gap-2">
-          {/* Additional controls can go here */}
+          {/* Active filters count */}
+          {table.getState().columnFilters.length > 0 && (
+            <Badge variant="secondary" className="font-normal">
+              <SlidersHorizontal className="h-3 w-3 mr-1" />
+              {table.getState().columnFilters.length} active {table.getState().columnFilters.length === 1 ? 'filter' : 'filters'}
+            </Badge>
+          )}
+
+          {/* Filter presets */}
+          <FilterPresetsMenu
+            columnFilters={table.getState().columnFilters}
+            onApplyPreset={(filters) => {
+              // Clear existing filters first
+              table.resetColumnFilters();
+              // Apply each filter from the preset
+              filters.forEach(filter => {
+                table.getColumn(filter.id)?.setFilterValue(filter.value);
+              });
+            }}
+          />
         </div>
       </div>
-      
+
       {/* Table */}
       <div className="rounded-md border">
         <div className="overflow-hidden">
-          {/* Table header */}
-          <div 
-            className="border-b bg-muted/50"
-            style={{ width: `${Math.max(totalWidth, 800)}px` }}
-          >
-            {table.getHeaderGroups().map((headerGroup) => (
-              <div
-                key={headerGroup.id}
-                className="flex"
-              >
-                {headerGroup.headers.map((header) => (
-                  <div
-                    key={header.id}
-                    className={cn(
-                      "flex items-center border-r p-2 last:border-r-0",
-                      header.column.getCanSort() && "cursor-pointer select-none"
-                    )}
-                    style={{ width: `${header.getSize()}px` }}
-                    onClick={header.column.getToggleSortingHandler()}
-                  >
-                    {flexRender(
-                      header.column.columnDef.header,
-                      header.getContext()
-                    )}
-                    {{
-                      asc: ' 🔼',
-                      desc: ' 🔽',
-                    }[header.column.getIsSorted() as string] ?? null}
-                  </div>
-                ))}
-              </div>
-            ))}
-          </div>
-          
-          {/* Table body with virtualization */}
+          {/* Main scrollable container */}
           <div
             ref={parentRef}
             className="overflow-auto"
             style={{ height: '600px', width: '100%' }}
           >
+            {/* Table header - now inside the scrollable container */}
+            <div
+              className="sticky top-0 z-10 border-b bg-muted/50"
+              style={{ width: `${Math.max(totalWidth, 800)}px` }}
+            >
+              {table.getHeaderGroups().map((headerGroup) => (
+                <div
+                  key={headerGroup.id}
+                  className="flex"
+                >
+                  {headerGroup.headers.map((header) => (
+                    <div
+                      key={header.id}
+                      className={cn(
+                        "flex items-center border-r p-3 last:border-r-0 relative overflow-visible",
+                        header.column.getCanSort() && "cursor-pointer select-none"
+                      )}
+                      style={{ width: `${header.getSize()}px` }}
+                      onClick={header.column.getToggleSortingHandler()}
+                    >
+                      <div className="flex items-center justify-between w-full overflow-visible">
+                        <div className="flex items-center pr-3 w-full overflow-visible">
+                          <div className="w-full overflow-visible whitespace-normal">
+                            {flexRender(
+                              header.column.columnDef.header,
+                              header.getContext()
+                            )}
+                          </div>
+                          <div className="flex-shrink-0 ml-1">
+                            {{
+                              asc: ' 🔼',
+                              desc: ' 🔽',
+                            }[header.column.getIsSorted() as string] ?? null}
+                          </div>
+                        </div>
+                      </div>
+                    </div>
+                  ))}
+                </div>
+              ))}
+            </div>
+
+            {/* Table body with virtualization */}
             {isLoading ? (
               <div className="flex items-center justify-center h-24">
                 <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
@@ -204,6 +277,7 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
                       style={{
                         height: `${virtualRow.size}px`,
                         transform: `translateY(${virtualRow.start}px)`,
+                        width: '100%'
                       }}
                       onClick={() => onRowClick?.(row.original)}
                     >
@@ -211,7 +285,7 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
                         <div
                           key={cell.id}
                           className="flex items-center border-r p-2 last:border-r-0"
-                          style={{ width: `${cell.column.getSize()}px` }}
+                          style={{ width: cell.column.columnDef.size ? `${cell.column.columnDef.size}px` : '150px' }}
                         >
                           {flexRender(
                             cell.column.columnDef.cell,
@@ -226,7 +300,7 @@ export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
             )}
           </div>
         </div>
-        
+
         {/* Pagination */}
         <div className="flex items-center justify-between border-t p-2">
           <div className="flex-1 text-sm text-muted-foreground">
diff --git a/src/features/products/components/cells/CellRenderer.tsx b/src/features/products/components/cells/CellRenderer.tsx
index d67c75d..7390cdc 100644
--- a/src/features/products/components/cells/CellRenderer.tsx
+++ b/src/features/products/components/cells/CellRenderer.tsx
@@ -4,6 +4,7 @@ import { ImageCell } from './ImageCell';
 import { MultiImageCell } from './MultiImageCell';
 import { StatusCell } from './StatusCell';
 import { PricingTiersCell } from './PricingTiersCell';
+import { SpecificationsCell } from './SpecificationsCell';
 import { format } from 'date-fns';
 
 interface CellRendererProps {
@@ -21,7 +22,7 @@ export const CellRenderer: React.FC<CellRendererProps> = ({
 }) => {
   // Only allow editing if the column is editable and isEditable is true
   const canEdit = column.editable && isEditable;
-  
+
   switch (column.cellType) {
     case 'image':
       return (
@@ -31,7 +32,7 @@ export const CellRenderer: React.FC<CellRendererProps> = ({
           isEditable={canEdit}
         />
       );
-      
+
     case 'multiImage':
       return (
         <MultiImageCell
@@ -40,7 +41,7 @@ export const CellRenderer: React.FC<CellRendererProps> = ({
           isEditable={canEdit}
         />
       );
-      
+
     case 'status':
       return (
         <StatusCell
@@ -49,7 +50,7 @@ export const CellRenderer: React.FC<CellRendererProps> = ({
           isEditable={canEdit}
         />
       );
-      
+
     case 'pricingTiers':
       return (
         <PricingTiersCell
@@ -58,7 +59,16 @@ export const CellRenderer: React.FC<CellRendererProps> = ({
           isEditable={canEdit}
         />
       );
-      
+
+    case 'specifications':
+      return (
+        <SpecificationsCell
+          value={value}
+          onChange={canEdit ? onChange : undefined}
+          isEditable={canEdit}
+        />
+      );
+
     case 'date':
       if (value instanceof Date) {
         return (
@@ -68,16 +78,16 @@ export const CellRenderer: React.FC<CellRendererProps> = ({
         );
       }
       return <div className="text-sm text-muted-foreground">No date</div>;
-      
+
     case 'number':
       return (
         <div className="text-sm text-right">
-          {typeof value === 'number' 
-            ? value.toLocaleString() 
+          {typeof value === 'number'
+            ? value.toLocaleString()
             : <span className="text-muted-foreground">-</span>}
         </div>
       );
-      
+
     case 'text':
     default:
       return (
diff --git a/src/features/products/components/cells/ImageCell.tsx b/src/features/products/components/cells/ImageCell.tsx
index 2bb86bf..2126610 100644
--- a/src/features/products/components/cells/ImageCell.tsx
+++ b/src/features/products/components/cells/ImageCell.tsx
@@ -1,13 +1,11 @@
-import React, { useState } from 'react';
-import { Eye, Upload } from 'lucide-react';
-import {
-  Dialog,
-  DialogContent,
-  DialogHeader,
-  DialogTitle,
-} from '@/components/ui/dialog';
-import { Button } from '@/components/ui/button';
+import React, { useState, useRef, useEffect } from 'react';
+import { Camera, X, Upload, Plus, Save } from 'lucide-react';
 import { cn } from '@/lib/utils';
+import { toast } from 'sonner';
+import { PhotoView } from 'react-photo-view';
+import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
+import { Button } from '@/components/ui/button';
+import 'react-photo-view/dist/react-photo-view.css';
 
 interface ImageCellProps {
   value: string | undefined;
@@ -20,102 +18,191 @@ export const ImageCell: React.FC<ImageCellProps> = ({
   onChange,
   isEditable = false,
 }) => {
-  const [isOpen, setIsOpen] = useState(false);
-  
-  // Placeholder image if no value is provided
   const imageUrl = value || '/placeholder.svg';
-  
-  const handleImageClick = () => {
-    if (value) {
-      setIsOpen(true);
-    } else if (isEditable) {
-      // Trigger file upload
-      document.getElementById('image-upload')?.click();
-    }
-  };
-  
+  const [isUploading, setIsUploading] = useState(false);
+  const [isDialogOpen, setIsDialogOpen] = useState(false);
+  const [tempImage, setTempImage] = useState<string | undefined>(value);
+  const fileInputRef = useRef<HTMLInputElement>(null);
+
+  // Reset tempImage when value changes from outside
+  useEffect(() => {
+    setTempImage(value);
+  }, [value]);
+
   const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
     const file = e.target.files?.[0];
-    if (file && onChange) {
-      // In a real app, you would upload the file to a server
-      // and get back a URL. For now, we'll create a local URL.
-      const localUrl = URL.createObjectURL(file);
-      onChange(localUrl);
+    if (file) {
+      if (!file.type.startsWith('image/')) {
+        toast.error('Please select an image file');
+        return;
+      }
+
+      if (file.size > 5 * 1024 * 1024) {
+        toast.error('Image size should be less than 5MB');
+        return;
+      }
+
+      setIsUploading(true);
+      const reader = new FileReader();
+
+      reader.onload = (event) => {
+        if (event.target?.result) {
+          const dataUrl = event.target.result as string;
+          setTempImage(dataUrl);
+          setIsUploading(false);
+          toast.success('Image uploaded successfully');
+
+          // Reset the file input so the same file can be selected again
+          if (fileInputRef.current) {
+            fileInputRef.current.value = '';
+          }
+        }
+      };
+
+      reader.onerror = () => {
+        setIsUploading(false);
+        toast.error('Failed to read image file');
+
+        // Reset the file input on error
+        if (fileInputRef.current) {
+          fileInputRef.current.value = '';
+        }
+      };
+
+      reader.readAsDataURL(file);
     }
   };
-  
+
+  const handleRemoveImage = () => {
+    setTempImage('');
+    toast.success('Image removed');
+  };
+
+  const handleSaveChanges = () => {
+    if (onChange && tempImage !== value) {
+      onChange(tempImage || '');
+      toast.success('Image saved successfully');
+    }
+    setIsDialogOpen(false);
+  };
+
+  const handleDialogClose = (open: boolean) => {
+    if (!open) {
+      // Reset to original value if dialog is closed without saving
+      setTempImage(value);
+    }
+    setIsDialogOpen(open);
+  };
+
+  // Image popup dialog for viewing and editing
+  const ImageDialog = () => (
+    <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
+      <DialogContent className="sm:max-w-md">
+        <DialogHeader>
+          <DialogTitle>Image</DialogTitle>
+        </DialogHeader>
+        <div className="flex items-center justify-center p-4">
+          <div className="relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden">
+            {tempImage ? (
+              <img
+                src={tempImage}
+                alt="Product"
+                className="max-w-full max-h-full object-contain"
+                onError={(e) => {
+                  e.currentTarget.src = '/placeholder.svg';
+                }}
+              />
+            ) : (
+              <div className="flex flex-col items-center justify-center text-muted-foreground">
+                <Camera className="w-12 h-12" />
+                <span className="text-sm mt-2">No image available</span>
+              </div>
+            )}
+          </div>
+        </div>
+        {isEditable && (
+          <DialogFooter className="flex sm:justify-between">
+            <div className="flex gap-2">
+              <Button
+                variant="outline"
+                size="sm"
+                onClick={() => fileInputRef.current?.click()}
+                disabled={isUploading}
+              >
+                <Upload className="mr-2 h-4 w-4" />
+                {tempImage ? 'Change' : 'Upload'}
+              </Button>
+              {tempImage && (
+                <Button
+                  variant="destructive"
+                  size="sm"
+                  onClick={handleRemoveImage}
+                >
+                  <X className="mr-2 h-4 w-4" />
+                  Remove
+                </Button>
+              )}
+            </div>
+            <div className="flex gap-2">
+              <Button variant="secondary" size="sm" onClick={() => handleDialogClose(false)}>
+                Cancel
+              </Button>
+              <Button variant="default" size="sm" onClick={handleSaveChanges}>
+                <Save className="mr-2 h-4 w-4" />
+                Save
+              </Button>
+            </div>
+          </DialogFooter>
+        )}
+      </DialogContent>
+    </Dialog>
+  );
+
   return (
     <>
-      <div 
+      <div
         className={cn(
-          "relative w-14 h-14 rounded-md overflow-hidden border border-border flex items-center justify-center group",
+          "relative w-12 h-12 rounded-md overflow-hidden border border-border flex items-center justify-center group",
           !value && "bg-muted",
-          isEditable && "cursor-pointer hover:bg-accent/50 transition-colors"
+          "cursor-pointer hover:bg-accent/50 transition-colors"
         )}
-        onClick={handleImageClick}
+        onClick={() => setIsDialogOpen(true)}
       >
-        {value ? (
-          <>
-            <img 
-              src={imageUrl} 
-              alt="Product" 
-              className="w-full h-full object-cover group-hover:scale-105 transition-transform"
+        {isUploading ? (
+          <div className="flex flex-col items-center justify-center text-muted-foreground">
+            <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent" />
+            <span className="text-[10px] mt-1">Uploading...</span>
+          </div>
+        ) : value ? (
+          <div className="w-full h-full flex items-center justify-center p-1">
+            <img
+              src={imageUrl}
+              alt="Product"
+              className="max-w-full max-h-full object-contain group-hover:scale-105 transition-transform"
+              onError={(e) => {
+                e.currentTarget.src = '/placeholder.svg';
+              }}
             />
-            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
-              <Eye className="w-5 h-5 text-white" />
-            </div>
-          </>
+          </div>
         ) : (
           <div className="flex flex-col items-center justify-center text-muted-foreground">
-            <Upload className="w-5 h-5" />
-            <span className="text-xs mt-1">Add</span>
+            <Camera className="w-5 h-5" />
+            <span className="text-[10px] mt-0.5">Add Image</span>
           </div>
         )}
       </div>
-      
+
       {isEditable && (
         <input
-          id="image-upload"
+          ref={fileInputRef}
           type="file"
           accept="image/*"
           className="hidden"
           onChange={handleImageUpload}
         />
       )}
-      
-      <Dialog open={isOpen} onOpenChange={setIsOpen}>
-        <DialogContent className="sm:max-w-md">
-          <DialogHeader>
-            <DialogTitle>Product Image</DialogTitle>
-          </DialogHeader>
-          <div className="flex items-center justify-center p-4">
-            <img 
-              src={imageUrl} 
-              alt="Product" 
-              className="max-w-full max-h-[70vh] object-contain"
-            />
-          </div>
-          {isEditable && (
-            <div className="flex justify-end gap-2">
-              <Button
-                variant="outline"
-                onClick={() => setIsOpen(false)}
-              >
-                Close
-              </Button>
-              <Button
-                variant="default"
-                onClick={() => {
-                  document.getElementById('image-upload')?.click();
-                  setIsOpen(false);
-                }}
-              >
-                Replace
-              </Button>
-            </div>
-          )}
-        </DialogContent>
-      </Dialog>
+
+      <ImageDialog />
     </>
   );
 };
diff --git a/src/features/products/components/cells/MultiImageCell.tsx b/src/features/products/components/cells/MultiImageCell.tsx
index df4b2ad..3e85c13 100644
--- a/src/features/products/components/cells/MultiImageCell.tsx
+++ b/src/features/products/components/cells/MultiImageCell.tsx
@@ -1,16 +1,11 @@
-
-import React, { useState } from 'react';
-import { Eye, Upload, Plus } from 'lucide-react';
-import {
-  Dialog,
-  DialogContent,
-  DialogHeader,
-  DialogTitle,
-  DialogDescription,
-} from '@/components/ui/dialog';
+import React, { useState, useRef, useEffect } from 'react';
+import { Plus, Images, X, Upload, ChevronLeft, ChevronRight, Save } from 'lucide-react';
+import { toast } from 'sonner';
 import { Button } from '@/components/ui/button';
 import { cn } from '@/lib/utils';
 import { ScrollArea } from '@/components/ui/scroll-area';
+import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
+import 'react-photo-view/dist/react-photo-view.css';
 
 interface MultiImageCellProps {
   value: string[] | undefined;
@@ -23,89 +18,319 @@ export const MultiImageCell: React.FC<MultiImageCellProps> = ({
   onChange,
   isEditable = false,
 }) => {
-  const [isOpen, setIsOpen] = useState(false);
+  const [isUploading, setIsUploading] = useState(false);
+  const [isDialogOpen, setIsDialogOpen] = useState(false);
   const [selectedImageIndex, setSelectedImageIndex] = useState(0);
-  
+  const [tempImages, setTempImages] = useState<string[]>(value?.length ? value : []);
+  const fileInputRef = useRef<HTMLInputElement>(null);
+
+  // Reset tempImages when value changes from outside
+  useEffect(() => {
+    setTempImages(value?.length ? value : []);
+  }, [value]);
+
   const images = value?.length ? value : [];
-  
-  const handleCellClick = () => {
-    if (images.length > 0) {
-      setIsOpen(true);
-      setSelectedImageIndex(0);
-    } else if (isEditable) {
-      // Trigger file upload
-      document.getElementById('multi-image-upload')?.click();
-    }
-  };
-  
+
   const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
     const files = e.target.files;
-    if (files && onChange) {
-      // In a real app, you would upload the files to a server
-      // and get back URLs. For now, we'll create local URLs.
-      const newImages = Array.from(files).map(file => URL.createObjectURL(file));
-      onChange([...images, ...newImages]);
+    if (files) {
+      // Validate files
+      const validFiles = Array.from(files).filter(file => {
+        if (!file.type.startsWith('image/')) {
+          toast.error(`File ${file.name} is not an image`);
+          return false;
+        }
+
+        if (file.size > 5 * 1024 * 1024) { // 5MB limit
+          toast.error(`File ${file.name} exceeds 5MB limit`);
+          return false;
+        }
+
+        return true;
+      });
+
+      if (validFiles.length === 0) return;
+
+      setIsUploading(true);
+
+      // Process files one by one using FileReader
+      const newImages: string[] = [];
+      let filesProcessed = 0;
+
+      validFiles.forEach(file => {
+        const reader = new FileReader();
+
+        reader.onload = (event) => {
+          if (event.target?.result) {
+            newImages.push(event.target.result as string);
+            filesProcessed++;
+
+            // When all files are processed, update state
+            if (filesProcessed === validFiles.length) {
+              setTempImages(prev => [...prev, ...newImages]);
+              setIsUploading(false);
+              toast.success(`${validFiles.length} image(s) uploaded successfully`);
+
+              // Reset the file input so the same files can be selected again
+              if (fileInputRef.current) {
+                fileInputRef.current.value = '';
+              }
+            }
+          }
+        };
+
+        reader.onerror = () => {
+          filesProcessed++;
+          toast.error(`Failed to read file: ${file.name}`);
+
+          // Even if some files fail, continue with the ones that succeeded
+          if (filesProcessed === validFiles.length && newImages.length > 0) {
+            setTempImages(prev => [...prev, ...newImages]);
+            setIsUploading(false);
+            toast.success(`${newImages.length} image(s) uploaded successfully`);
+
+            // Reset the file input
+            if (fileInputRef.current) {
+              fileInputRef.current.value = '';
+            }
+          } else if (filesProcessed === validFiles.length) {
+            setIsUploading(false);
+
+            // Reset the file input
+            if (fileInputRef.current) {
+              fileInputRef.current.value = '';
+            }
+          }
+        };
+
+        // Read the file as a data URL
+        reader.readAsDataURL(file);
+      });
     }
   };
-  
+
   const handleRemoveImage = (index: number) => {
-    if (onChange) {
-      const newImages = [...images];
-      newImages.splice(index, 1);
-      onChange(newImages);
-      
-      if (selectedImageIndex >= newImages.length) {
-        setSelectedImageIndex(Math.max(0, newImages.length - 1));
-      }
+    const newImages = [...tempImages];
+    newImages.splice(index, 1);
+    setTempImages(newImages);
+
+    if (selectedImageIndex >= newImages.length) {
+      setSelectedImageIndex(Math.max(0, newImages.length - 1));
     }
+
+    toast.success('Image removed');
   };
-  
-  return (
-    <>
-      <div 
-        className={cn(
-          "relative h-14 rounded-md overflow-hidden border border-border flex items-center group",
-          images.length === 0 && "bg-muted justify-center w-14",
-          images.length > 0 && "p-1 gap-1",
-          isEditable && "cursor-pointer hover:bg-accent/50 transition-colors"
-        )}
-        onClick={handleCellClick}
-      >
-        {images.length > 0 ? (
-          <>
-            <ScrollArea className="flex items-center h-full" orientation="horizontal">
-              <div className="flex gap-1 h-full">
-                {images.slice(0, 3).map((img, index) => (
-                  <div key={index} className="relative h-full aspect-square flex-shrink-0">
-                    <img 
-                      src={img} 
-                      alt={`Product ${index + 1}`} 
-                      className="h-full w-full object-cover rounded-sm"
+
+  const handleSaveChanges = () => {
+    if (onChange && JSON.stringify(tempImages) !== JSON.stringify(value)) {
+      onChange(tempImages);
+      toast.success('Images saved successfully');
+    }
+    setIsDialogOpen(false);
+  };
+
+  const handleDialogClose = (open: boolean) => {
+    if (!open) {
+      // Reset to original value if dialog is closed without saving
+      setTempImages(value?.length ? value : []);
+    }
+    setIsDialogOpen(open);
+  };
+
+  const navigateImages = (direction: 'prev' | 'next') => {
+    if (direction === 'prev') {
+      setSelectedImageIndex(prev => (prev > 0 ? prev - 1 : prev));
+    } else {
+      setSelectedImageIndex(prev => (prev < tempImages.length - 1 ? prev + 1 : prev));
+    }
+  };
+
+  // Image gallery dialog for viewing and editing multiple images
+  const ImageGalleryDialog = () => (
+    <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
+      <DialogContent className="sm:max-w-md">
+        <DialogHeader>
+          <DialogTitle>Images ({tempImages.length})</DialogTitle>
+        </DialogHeader>
+
+        {tempImages.length > 0 ? (
+          <div className="flex flex-col gap-4">
+            <div className="relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden">
+              <img
+                src={tempImages[selectedImageIndex]}
+                alt={`Product ${selectedImageIndex + 1}`}
+                className="max-w-full max-h-full object-contain"
+                onError={(e) => {
+                  e.currentTarget.src = '/placeholder.svg';
+                }}
+              />
+
+              {tempImages.length > 1 && (
+                <>
+                  <Button
+                    variant="ghost"
+                    size="icon"
+                    className="absolute left-2 bg-black/20 hover:bg-black/40 text-white rounded-full"
+                    onClick={() => navigateImages('prev')}
+                    disabled={selectedImageIndex === 0}
+                  >
+                    <ChevronLeft className="h-4 w-4" />
+                  </Button>
+                  <Button
+                    variant="ghost"
+                    size="icon"
+                    className="absolute right-2 bg-black/20 hover:bg-black/40 text-white rounded-full"
+                    onClick={() => navigateImages('next')}
+                    disabled={selectedImageIndex === tempImages.length - 1}
+                  >
+                    <ChevronRight className="h-4 w-4" />
+                  </Button>
+                </>
+              )}
+
+              {isEditable && (
+                <Button
+                  variant="destructive"
+                  size="icon"
+                  className="absolute top-2 right-2 h-7 w-7"
+                  onClick={() => handleRemoveImage(selectedImageIndex)}
+                >
+                  <X className="h-4 w-4" />
+                </Button>
+              )}
+            </div>
+
+            <ScrollArea className="w-full" orientation="horizontal">
+              <div className="flex gap-2 pb-2">
+                {tempImages.map((img, index) => (
+                  <div
+                    key={index}
+                    className={cn(
+                      "h-16 w-16 rounded-md overflow-hidden border cursor-pointer",
+                      selectedImageIndex === index ? "border-primary border-2" : "border-border"
+                    )}
+                    onClick={() => setSelectedImageIndex(index)}
+                  >
+                    <img
+                      src={img}
+                      alt={`Thumbnail ${index + 1}`}
+                      className="h-full w-full object-cover"
+                      onError={(e) => {
+                        e.currentTarget.src = '/placeholder.svg';
+                      }}
                     />
                   </div>
                 ))}
-                {images.length > 3 && (
-                  <div className="flex items-center justify-center h-full aspect-square bg-primary/10 rounded-sm text-primary font-medium text-xs">
-                    +{images.length - 3}
+
+                {isEditable && (
+                  <div
+                    className="h-16 w-16 rounded-md border border-dashed border-muted-foreground/30 flex items-center justify-center cursor-pointer hover:bg-muted/50"
+                    onClick={() => fileInputRef.current?.click()}
+                  >
+                    <Plus className="h-6 w-6 text-muted-foreground" />
                   </div>
                 )}
               </div>
             </ScrollArea>
-            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
-              <Eye className="w-5 h-5 text-white" />
+          </div>
+        ) : (
+          <div className="flex flex-col items-center justify-center p-8 gap-4">
+            <div className="flex flex-col items-center justify-center text-muted-foreground">
+              <Images className="w-16 h-16" />
+              <span className="text-sm mt-2">No images available</span>
+            </div>
+
+            {isEditable && (
+              <Button
+                variant="outline"
+                onClick={() => fileInputRef.current?.click()}
+                disabled={isUploading}
+              >
+                <Upload className="mr-2 h-4 w-4" />
+                Upload Images
+              </Button>
+            )}
+          </div>
+        )}
+
+        {isEditable && (
+          <DialogFooter className="flex sm:justify-between">
+            <div className="flex gap-2">
+              {tempImages.length > 0 && (
+                <Button
+                  variant="outline"
+                  size="sm"
+                  onClick={() => fileInputRef.current?.click()}
+                  disabled={isUploading}
+                >
+                  <Upload className="mr-2 h-4 w-4" />
+                  Add More
+                </Button>
+              )}
+            </div>
+            <div className="flex gap-2">
+              <Button variant="secondary" size="sm" onClick={() => handleDialogClose(false)}>
+                Cancel
+              </Button>
+              <Button variant="default" size="sm" onClick={handleSaveChanges}>
+                <Save className="mr-2 h-4 w-4" />
+                Save
+              </Button>
+            </div>
+          </DialogFooter>
+        )}
+      </DialogContent>
+    </Dialog>
+  );
+
+  return (
+    <>
+      <div
+        className={cn(
+          "relative h-12 rounded-md overflow-hidden border border-border flex items-center group",
+          images.length === 0 && "bg-muted justify-center w-12",
+          images.length > 0 && "p-1 gap-1 w-auto min-w-[48px] max-w-[120px]",
+          "cursor-pointer hover:bg-accent/50 transition-colors"
+        )}
+        style={{ minWidth: images.length === 0 ? '48px' : '80px' }}
+        onClick={() => setIsDialogOpen(true)}
+      >
+        {isUploading ? (
+          <div className="flex flex-col items-center justify-center w-full text-muted-foreground">
+            <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent" />
+            <span className="text-[10px] mt-1">Uploading...</span>
+          </div>
+        ) : images.length > 0 ? (
+          <ScrollArea className="flex items-center h-full w-full" orientation="horizontal">
+            <div className="flex gap-1 h-full">
+              {images.map((img, index) => (
+                <div key={index} className="relative h-full aspect-square flex-shrink-0 rounded-sm overflow-hidden">
+                  <div className="flex items-center justify-center h-full w-full">
+                    <img
+                      src={img}
+                      alt={`Product ${index + 1}`}
+                      className="max-h-full max-w-full object-contain"
+                      onError={(e) => {
+                        e.currentTarget.src = '/placeholder.svg';
+                        console.error('Image failed to load:', img);
+                      }}
+                    />
+                  </div>
+                </div>
+              ))}
             </div>
-          </>
+          </ScrollArea>
         ) : (
           <div className="flex flex-col items-center justify-center text-muted-foreground">
-            <Upload className="w-5 h-5" />
-            <span className="text-xs mt-1">Add</span>
+            <Images className="w-5 h-5" />
+            <span className="text-[10px] mt-0.5">Add Images</span>
           </div>
         )}
       </div>
-      
+
       {isEditable && (
         <input
-          id="multi-image-upload"
+          ref={fileInputRef}
           type="file"
           accept="image/*"
           multiple
@@ -113,96 +338,8 @@ export const MultiImageCell: React.FC<MultiImageCellProps> = ({
           onChange={handleImageUpload}
         />
       )}
-      
-      <Dialog open={isOpen} onOpenChange={setIsOpen}>
-        <DialogContent className="sm:max-w-xl">
-          <DialogHeader>
-            <DialogTitle>Product Images</DialogTitle>
-            <DialogDescription>
-              {images.length > 0 ? `Image ${selectedImageIndex + 1} of ${images.length}` : 'No images'}
-            </DialogDescription>
-          </DialogHeader>
-          
-          {images.length > 0 && (
-            <div className="flex flex-col gap-4">
-              <div className="flex items-center justify-center p-4 bg-muted/30 rounded-md">
-                <img 
-                  src={images[selectedImageIndex]} 
-                  alt={`Product ${selectedImageIndex + 1}`} 
-                  className="max-w-full max-h-[50vh] object-contain"
-                />
-              </div>
-              
-              <ScrollArea className="w-full" orientation="horizontal">
-                <div className="flex gap-2 pb-2">
-                  {images.map((img, index) => (
-                    <div 
-                      key={index}
-                      className={cn(
-                        "relative h-16 w-16 rounded-md overflow-hidden border-2 cursor-pointer",
-                        index === selectedImageIndex ? "border-primary" : "border-transparent"
-                      )}
-                      onClick={() => setSelectedImageIndex(index)}
-                    >
-                      <img 
-                        src={img} 
-                        alt={`Thumbnail ${index + 1}`} 
-                        className="h-full w-full object-cover"
-                      />
-                      {isEditable && (
-                        <button
-                          className="absolute top-0 right-0 bg-destructive text-destructive-foreground rounded-bl-md p-0.5"
-                          onClick={(e) => {
-                            e.stopPropagation();
-                            handleRemoveImage(index);
-                          }}
-                        >
-                          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
-                            <path d="M18 6 6 18"></path>
-                            <path d="m6 6 12 12"></path>
-                          </svg>
-                        </button>
-                      )}
-                    </div>
-                  ))}
-                  
-                  {isEditable && (
-                    <div 
-                      className="h-16 w-16 rounded-md border-2 border-dashed border-muted-foreground/30 flex items-center justify-center cursor-pointer hover:bg-muted/50"
-                      onClick={() => {
-                        document.getElementById('multi-image-upload')?.click();
-                      }}
-                    >
-                      <Plus className="h-6 w-6 text-muted-foreground" />
-                    </div>
-                  )}
-                </div>
-              </ScrollArea>
-            </div>
-          )}
-          
-          {isEditable && (
-            <div className="flex justify-end gap-2">
-              <Button
-                variant="outline"
-                onClick={() => setIsOpen(false)}
-              >
-                Close
-              </Button>
-              {images.length === 0 && (
-                <Button
-                  variant="default"
-                  onClick={() => {
-                    document.getElementById('multi-image-upload')?.click();
-                  }}
-                >
-                  Add Images
-                </Button>
-              )}
-            </div>
-          )}
-        </DialogContent>
-      </Dialog>
+
+      <ImageGalleryDialog />
     </>
   );
 };
diff --git a/src/features/products/utils/tableConfigs.ts b/src/features/products/utils/tableConfigs.ts
index 5592f96..5a0de7e 100644
--- a/src/features/products/utils/tableConfigs.ts
+++ b/src/features/products/utils/tableConfigs.ts
@@ -102,7 +102,8 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'primaryImage',
       enableSorting: false,
       enableFiltering: false,
-      size: 100,
+      size: 80,
+      minSize: 60,
       cellType: 'image',
       editable: true,
     },
@@ -112,7 +113,8 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'additionalImages',
       enableSorting: false,
       enableFiltering: false,
-      size: 150,
+      size: 120,
+      minSize: 80,
       cellType: 'multiImage',
       editable: true,
     },
@@ -189,7 +191,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'retailPrice',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 180,
       cellType: 'number',
       filterType: 'number',
       editable: true,
@@ -200,7 +202,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'width',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 180,
       cellType: 'number',
       filterType: 'number',
       editable: true,
@@ -211,7 +213,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'aspectRatio',
       enableSorting: true,
       enableFiltering: true,
-      size: 140,
+      size: 200,
       cellType: 'number',
       filterType: 'number',
       editable: true,
@@ -222,7 +224,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'rimDiameter',
       enableSorting: true,
       enableFiltering: true,
-      size: 160,
+      size: 220,
       cellType: 'number',
       filterType: 'number',
       editable: true,
@@ -233,7 +235,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'loadIndex',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 180,
       cellType: 'number',
       filterType: 'number',
       editable: true,
@@ -244,7 +246,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'speedRating',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 180,
       cellType: 'text',
       filterType: 'select',
       editable: true,
@@ -266,7 +268,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'season',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 160,
       cellType: 'text',
       filterType: 'select',
       editable: true,
@@ -282,7 +284,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'vehicleTypeCompatibility',
       enableSorting: false,
       enableFiltering: true,
-      size: 180,
+      size: 280,
       cellType: 'text',
       filterType: 'select',
       editable: true,
@@ -299,7 +301,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'treadLife',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 180,
       cellType: 'text',
       filterType: 'text',
       editable: true,
@@ -310,7 +312,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'tractionRating',
       enableSorting: true,
       enableFiltering: true,
-      size: 140,
+      size: 200,
       cellType: 'text',
       filterType: 'select',
       editable: true,
@@ -327,7 +329,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'temperatureRating',
       enableSorting: true,
       enableFiltering: true,
-      size: 160,
+      size: 200,
       cellType: 'text',
       filterType: 'select',
       editable: true,
@@ -343,7 +345,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'certifications',
       enableSorting: false,
       enableFiltering: true,
-      size: 150,
+      size: 180,
       cellType: 'text',
       filterType: 'text',
       editable: true,
@@ -354,7 +356,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'shippingOrigin',
       enableSorting: true,
       enableFiltering: true,
-      size: 140,
+      size: 180,
       cellType: 'text',
       filterType: 'text',
       editable: true,
@@ -365,7 +367,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'estimatedLeadTime',
       enableSorting: true,
       enableFiltering: true,
-      size: 160,
+      size: 200,
       cellType: 'text',
       filterType: 'text',
       editable: true,
@@ -376,7 +378,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'packagingDetails',
       enableSorting: false,
       enableFiltering: true,
-      size: 150,
+      size: 180,
       cellType: 'text',
       filterType: 'text',
       editable: true,
@@ -387,7 +389,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'inventoryUpdateDate',
       enableSorting: true,
       enableFiltering: true,
-      size: 160,
+      size: 200,
       cellType: 'date',
       filterType: 'date',
       editable: true,
@@ -398,7 +400,7 @@ export const tyresTableConfig: CategoryTableConfig = {
       accessorKey: 'status',
       enableSorting: true,
       enableFiltering: true,
-      size: 120,
+      size: 160,
       cellType: 'status',
       filterType: 'select',
       editable: true,
@@ -411,6 +413,16 @@ export const tyresTableConfig: CategoryTableConfig = {
         { label: 'Discontinued', value: 'discontinued' },
       ],
     },
+    {
+      id: 'specifications',
+      header: 'Specifications',
+      accessorKey: 'specifications',
+      enableSorting: false,
+      enableFiltering: false,
+      size: 200,
+      cellType: 'specifications',
+      editable: true,
+    },
     {
       id: 'adminNotes',
       header: 'Admin Notes',
diff --git a/src/index.css b/src/index.css
index 11c1969..a092c3b 100644
--- a/src/index.css
+++ b/src/index.css
@@ -235,6 +235,21 @@
     @apply rtl:mr-auto rtl:ml-0 ltr:ml-auto ltr:mr-0;
   }
 
+  /* Table column resizing styles */
+  .column-resize-handle {
+    @apply absolute right-0 top-0 h-full w-2 cursor-col-resize select-none touch-none flex items-center justify-center transition-colors;
+  }
+
+  .column-resize-handle:hover::before {
+    content: "";
+    @apply absolute h-full w-0.5 bg-primary;
+  }
+
+  .column-resize-handle::after {
+    content: "";
+    @apply absolute h-full w-0.5 bg-muted-foreground/30;
+  }
+
   /* Background patterns for public website */
   .bg-grid-pattern {
     background-size: 40px 40px;
diff --git a/src/pages/ProductsDataGridPage.tsx b/src/pages/ProductsDataGridPage.tsx
index 7ce039b..d4ade32 100644
--- a/src/pages/ProductsDataGridPage.tsx
+++ b/src/pages/ProductsDataGridPage.tsx
@@ -9,6 +9,8 @@ import { getTableConfigForCategory } from '@/features/products/utils/tableConfig
 import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
 import { Grid } from 'lucide-react';
 import { toast } from 'sonner';
+import { PhotoProvider } from 'react-photo-view';
+import 'react-photo-view/dist/react-photo-view.css';
 
 // Mock categories for now
 const categories = [
@@ -71,65 +73,74 @@ export default function ProductsDataGridPage() {
     toast.info(`Clicked on product: ${product.name}`);
   };
 
+  const handleCellEdit = (rowId: string, columnId: string, value: any) => {
+    // In a real app, this would update the data in your backend
+    toast.success(`Updated ${columnId} for product ID ${rowId}`);
+    console.log('Cell edit:', { rowId, columnId, value });
+  };
+
   return (
     <AdminLayout>
-      <div className="flex flex-col gap-6">
-        <div>
-          <h1 className="text-3xl font-bold tracking-tight">{t('productsDataGrid.productLibrary')}</h1>
-          <p className="text-muted-foreground mt-1">
-            {t('productsDataGrid.manageProductCatalog')}
-          </p>
+      <PhotoProvider maskOpacity={0.8}>
+        <div className="flex flex-col gap-6">
+          <div>
+            <h1 className="text-3xl font-bold tracking-tight">{t('productsDataGrid.productLibrary')}</h1>
+            <p className="text-muted-foreground mt-1">
+              {t('productsDataGrid.manageProductCatalog')}
+            </p>
+          </div>
+
+          <div className="overflow-hidden">
+            <Tabs
+              defaultValue="tyres"
+              value={activeCategory}
+              onValueChange={setActiveCategory}
+              className="w-full"
+            >
+              <div className="border-b border-[#e1e1e1] mb-2">
+                <TabsList className="bg-transparent h-auto p-0 w-full flex justify-start gap-4 overflow-x-auto">
+                  {categories.map(category => (
+                    <TabsTrigger
+                      key={category.id}
+                      value={category.id}
+                      className="data-[state=active]:border-b-2 data-[state=active]:border-electric-orange data-[state=active]:bg-transparent data-[state=active]:shadow-none px-5 py-3 h-12 rounded-t-md data-[state=active]:text-electric-orange transition-all"
+                    >
+                      <Grid className="w-4 h-4 mr-2" />
+                      <span className="font-medium">{category.name}</span>
+                    </TabsTrigger>
+                  ))}
+                </TabsList>
+              </div>
+            </Tabs>
+          </div>
+
+          <ProductsToolbar
+            tableConfig={tableConfig}
+            selectedRows={selectedProductIds.length}
+            onAddProduct={handleAddProduct}
+            onImport={handleImport}
+            onExport={handleExport}
+            onRefresh={refetch}
+            onScanBarcode={handleScanBarcode}
+            onDeleteSelected={handleDeleteSelected}
+          />
+
+          <ProductsDataGrid
+            data={products}
+            tableConfig={tableConfig}
+            isLoading={isLoading}
+            onRowClick={handleRowClick}
+            onCellEdit={handleCellEdit}
+          />
+
+          {/* Barcode Scanner */}
+          <BarcodeScanner
+            isOpen={isScannerOpen}
+            onClose={() => setIsScannerOpen(false)}
+            onScan={handleBarcodeScan}
+          />
         </div>
-
-        <div className="overflow-hidden">
-          <Tabs
-            defaultValue="tyres"
-            value={activeCategory}
-            onValueChange={setActiveCategory}
-            className="w-full"
-          >
-            <div className="border-b border-[#e1e1e1] mb-2">
-              <TabsList className="bg-transparent h-auto p-0 w-full flex justify-start gap-4 overflow-x-auto">
-                {categories.map(category => (
-                  <TabsTrigger
-                    key={category.id}
-                    value={category.id}
-                    className="data-[state=active]:border-b-2 data-[state=active]:border-electric-orange data-[state=active]:bg-transparent data-[state=active]:shadow-none px-5 py-3 h-12 rounded-t-md data-[state=active]:text-electric-orange transition-all"
-                  >
-                    <Grid className="w-4 h-4 mr-2" />
-                    <span className="font-medium">{category.name}</span>
-                  </TabsTrigger>
-                ))}
-              </TabsList>
-            </div>
-          </Tabs>
-        </div>
-
-        <ProductsToolbar
-          tableConfig={tableConfig}
-          selectedRows={selectedProductIds.length}
-          onAddProduct={handleAddProduct}
-          onImport={handleImport}
-          onExport={handleExport}
-          onRefresh={refetch}
-          onScanBarcode={handleScanBarcode}
-          onDeleteSelected={handleDeleteSelected}
-        />
-
-        <ProductsDataGrid
-          data={products}
-          tableConfig={tableConfig}
-          isLoading={isLoading}
-          onRowClick={handleRowClick}
-        />
-
-        {/* Barcode Scanner */}
-        <BarcodeScanner
-          isOpen={isScannerOpen}
-          onClose={() => setIsScannerOpen(false)}
-          onScan={handleBarcodeScan}
-        />
-      </div>
+      </PhotoProvider>
     </AdminLayout>
   );
 }
