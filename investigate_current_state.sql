-- COMP<PERSON><PERSON><PERSON>SIVE INVESTIGATION: Current Database State
-- Run this to understand EXACTLY what policies and structures exist
-- DO NOT MAKE ANY CHANGES - INVESTIGATION ONLY
-- Date: July 8, 2025

-- 1. SHOW ALL CURRENT RLS POLICIES ON ALL TABLES
SELECT 
  'CURRENT_RLS_POLICIES' as investigation_type,
  schemaname, 
  tablename, 
  policyname, 
  cmd as command_type,
  permissive,
  roles,
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 2. SHOW RLS STATUS FOR ALL TABLES
SELECT 
  'RLS_STATUS' as investigation_type,
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'products', 'orders', 'order_items', 'shipments', 'wholesale_pricing_tiers', 'tyre_specifications', 'vehicle_compatibility')
ORDER BY tablename;

-- 3. CHECK PROFILES TABLE STRUCTURE
SELECT 
  'PROFILES_TABLE_STRUCTURE' as investigation_type,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 4. SAMPLE PROFILES DATA (to understand what exists)
SELECT 
  'PROFILES_SAMPLE_DATA' as investigation_type,
  id,
  email,
  role,
  full_name,
  company_name,
  store_name,
  created_at
FROM profiles 
WHERE role IN ('admin', 'merchant', 'supplier')
LIMIT 5;

-- 5. CHECK CONSUMER PROFILES (to ensure consumer auth still works)
SELECT 
  'CONSUMER_PROFILES_SAMPLE' as investigation_type,
  id,
  phone,
  role,
  full_name,
  first_name,
  last_name,
  created_at
FROM profiles 
WHERE role = 'consumer'
LIMIT 5;

-- 6. CHECK AUTH.USERS TABLE (to understand admin authentication)
SELECT 
  'AUTH_USERS_SAMPLE' as investigation_type,
  id,
  email,
  created_at,
  email_confirmed_at,
  last_sign_in_at
FROM auth.users 
WHERE email LIKE '%@arouzmarket.com%' OR email LIKE '%hamza%'
LIMIT 5;

-- 7. CHECK PRODUCTS TABLE POLICIES AND STRUCTURE
SELECT 
  'PRODUCTS_POLICIES' as investigation_type,
  policyname, 
  cmd as command_type,
  qual as using_condition
FROM pg_policies 
WHERE tablename = 'products'
ORDER BY policyname;

-- 8. CHECK ORDERS TABLE POLICIES (to ensure order management works)
SELECT 
  'ORDERS_POLICIES' as investigation_type,
  policyname, 
  cmd as command_type,
  qual as using_condition
FROM pg_policies 
WHERE tablename = 'orders'
ORDER BY policyname;

-- 9. CHECK FOR ANY TRIGGERS ON PROFILES TABLE
SELECT 
  'PROFILES_TRIGGERS' as investigation_type,
  trigger_name,
  event_manipulation,
  action_timing,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'profiles'
ORDER BY trigger_name;

-- 10. CHECK FOR ANY FUNCTIONS RELATED TO PROFILES
SELECT 
  'PROFILE_FUNCTIONS' as investigation_type,
  routine_name,
  routine_type,
  routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND (routine_name LIKE '%profile%' OR routine_name LIKE '%user%')
ORDER BY routine_name;

-- 11. TEST SIMPLE QUERIES TO SEE WHAT WORKS/FAILS
-- Test 1: Can we access profiles directly?
SELECT 
  'TEST_PROFILES_ACCESS' as test_type,
  'SUCCESS' as result,
  count(*) as profile_count
FROM profiles;

-- Test 2: Can we access products?
SELECT 
  'TEST_PRODUCTS_ACCESS' as test_type,
  'SUCCESS' as result,
  count(*) as product_count
FROM products;

-- Test 3: Can we access orders?
SELECT 
  'TEST_ORDERS_ACCESS' as test_type,
  'SUCCESS' as result,
  count(*) as order_count
FROM orders;

-- 12. CHECK CURRENT DATABASE CONNECTIONS AND ROLES
SELECT 
  'CURRENT_CONNECTION_INFO' as investigation_type,
  current_user as current_user,
  session_user as session_user,
  current_database() as current_database,
  version() as postgres_version;

-- 13. FINAL SUMMARY
SELECT 
  'INVESTIGATION_COMPLETE' as status,
  'Check all results above to understand current state' as message,
  now() as timestamp;
