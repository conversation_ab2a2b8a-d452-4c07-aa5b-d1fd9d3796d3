-- FINAL FIX: Remove foreign key constraint for phone-only authentication
-- This allows profiles to exist without auth.users records

-- First, drop the foreign key constraint that's causing the issue
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_id_fkey;

-- Update the handle_phone_verification function to work without auth.users dependency
CREATE OR REPLACE FUNCTION handle_phone_verification(
  p_phone TEXT,
  p_verification_id TEXT
)
RETURNS JSON AS $$
DECLARE
  v_user_id UUID;
  v_result JSON;
  v_action TEXT;
BEGIN
  -- Check if user already exists
  SELECT id INTO v_user_id
  FROM profiles 
  WHERE phone = p_phone 
    AND role = 'consumer'
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- SIGNIN: User exists, update their info
    UPDATE profiles 
    SET 
      phone_verified = TRUE,
      phone_verification_method = 'sms_otp',
      phone_verified_at = NOW(),
      last_login = NOW(),
      updated_at = NOW()
    WHERE id = v_user_id;
    
    v_action := 'login';
    
  ELSE
    -- SIGNUP: Create new user
    v_user_id := gen_random_uuid();
    
    -- Insert new profile (no auth.users dependency)
    INSERT INTO profiles (
      id,
      phone,
      role,
      phone_verified,
      phone_verification_method,
      phone_verified_at,
      last_login,
      created_at,
      updated_at
    ) VALUES (
      v_user_id,
      p_phone,
      'consumer',
      TRUE,
      'sms_otp',
      NOW(),
      NOW(),
      NOW(),
      NOW()
    );
    
    v_action := 'signup';
  END IF;
  
  -- Mark OTP as verified
  UPDATE otp_verifications 
  SET 
    status = 'verified',
    verified_at = NOW()
  WHERE id = p_verification_id;
  
  -- Return result
  v_result := json_build_object(
    'user_id', v_user_id,
    'action', v_action,
    'phone', p_phone,
    'verified_at', NOW()
  );
  
  RETURN v_result;
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies to work without auth.users dependency
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;

-- Create new policies that work for phone authentication
CREATE POLICY "Phone users can view their own profile"
  ON profiles
  FOR SELECT
  USING (true); -- Allow all reads for now, we'll handle auth in the app

CREATE POLICY "Phone users can update their own profile"
  ON profiles
  FOR UPDATE
  USING (true); -- Allow all updates for now, we'll handle auth in the app

CREATE POLICY "Phone users can insert their own profile"
  ON profiles
  FOR INSERT
  WITH CHECK (true); -- Allow all inserts for now, we'll handle auth in the app

-- Service role can still manage everything
CREATE POLICY "Service role can manage all profiles"
  ON profiles
  USING (auth.role() = 'service_role');
