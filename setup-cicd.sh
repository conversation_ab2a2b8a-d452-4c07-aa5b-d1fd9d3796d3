#!/bin/bash

# 🚀 AROUZ MARKET CI/CD Pipeline Setup Script
# This script helps you set up the complete CI/CD pipeline quickly

set -e

echo "🚀 Setting up AROUZ MARKET CI/CD Pipeline..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_info "Checking dependencies..."
    
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_status "All dependencies are installed"
}

# Install project dependencies
install_dependencies() {
    print_info "Installing project dependencies..."
    npm install
    print_status "Dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    print_info "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_warning "Created .env file from template. Please update with your actual values."
    fi
    
    print_status "Environment files are ready"
}

# Setup GitHub CLI (optional)
setup_github_cli() {
    print_info "Checking GitHub CLI..."
    
    if command -v gh &> /dev/null; then
        if gh auth status &> /dev/null; then
            print_status "GitHub CLI is installed and authenticated"
        else
            print_warning "GitHub CLI is installed but not authenticated. Run 'gh auth login' to authenticate."
        fi
    else
        print_warning "GitHub CLI is not installed. Install it for easier workflow management: https://cli.github.com/"
    fi
}

# Setup Supabase CLI (optional)
setup_supabase_cli() {
    print_info "Checking Supabase CLI..."
    
    if command -v supabase &> /dev/null; then
        print_status "Supabase CLI is installed"
    else
        print_warning "Supabase CLI is not installed. Install it for database management: npm install -g supabase"
    fi
}

# Validate CI/CD files
validate_cicd_files() {
    print_info "Validating CI/CD configuration files..."
    
    required_files=(
        ".github/workflows/ci-cd.yml"
        ".github/workflows/rollback.yml"
        ".github/workflows/database-migration.yml"
        ".github/workflows/monitoring.yml"
        "vitest.config.ts"
        ".env.development"
        ".env.production"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_status "$file exists"
        else
            print_error "$file is missing"
            exit 1
        fi
    done
}

# Run initial tests
run_initial_tests() {
    print_info "Running initial tests..."
    
    # Lint check
    if npm run lint; then
        print_status "Linting passed"
    else
        print_warning "Linting issues found. Fix them before deploying."
    fi
    
    # Type check
    if npx tsc --noEmit; then
        print_status "TypeScript compilation check passed"
    else
        print_error "TypeScript compilation errors found"
        exit 1
    fi
    
    # Test build
    if npm run build:dev; then
        print_status "Development build successful"
    else
        print_error "Development build failed"
        exit 1
    fi
}

# Display setup summary
display_summary() {
    echo ""
    echo "🎉 CI/CD Pipeline Setup Complete!"
    echo "=================================="
    echo ""
    print_info "Next Steps:"
    echo "1. 🔐 Configure GitHub Secrets (see .github/workflows/secrets-template.md)"
    echo "2. 🗄️ Set up Supabase projects (dev + prod)"
    echo "3. 🌐 Configure Vercel project for production"
    echo "4. 🧪 Test the pipeline with a commit to develop branch"
    echo ""
    print_info "Quick Commands:"
    echo "• Deploy to dev:  git push origin develop"
    echo "• Deploy to prod: git push origin main"
    echo "• Run tests:      npm test"
    echo "• Check health:   gh workflow run monitoring.yml"
    echo ""
    print_info "Documentation:"
    echo "• CI/CD Guide:    docs/CI-CD-PIPELINE.md"
    echo "• Secrets Setup:  .github/workflows/secrets-template.md"
    echo ""
    print_status "Ready for exceptional deployments! 🚀"
}

# Main execution
main() {
    check_dependencies
    install_dependencies
    setup_environment
    setup_github_cli
    setup_supabase_cli
    validate_cicd_files
    run_initial_tests
    display_summary
}

# Run the setup
main "$@"
