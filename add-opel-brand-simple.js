import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read all the Opel model files
const opelAstraModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-astra-models.json');
const opelCorsaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-corsa-models.json');
const opelInsigniaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-insignia-models.json');
const opelMokkaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-mokka-models.json');
const opelVectraModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-vectra-models.json');
const opelZafiraModelsPath = path.join(__dirname, 'src', 'services', 'data', 'opel-zafira-models.json');

const opelAstraModels = JSON.parse(fs.readFileSync(opelAstraModelsPath, 'utf8'));
const opelCorsaModels = JSON.parse(fs.readFileSync(opelCorsaModelsPath, 'utf8'));
const opelInsigniaModels = JSON.parse(fs.readFileSync(opelInsigniaModelsPath, 'utf8'));
const opelMokkaModels = JSON.parse(fs.readFileSync(opelMokkaModelsPath, 'utf8'));
const opelVectraModels = JSON.parse(fs.readFileSync(opelVectraModelsPath, 'utf8'));
const opelZafiraModels = JSON.parse(fs.readFileSync(opelZafiraModelsPath, 'utf8'));

// Combine all Opel models
const opelModels = [
  ...opelAstraModels,
  ...opelCorsaModels,
  ...opelInsigniaModels,
  ...opelMokkaModels,
  ...opelVectraModels,
  ...opelZafiraModels
];

// Find the Opel brand in the cars.json file
const opelBrandIndex = carsJson.findIndex(brand => brand.brand === 'Opel');

if (opelBrandIndex !== -1) {
  // Update the Opel models
  carsJson[opelBrandIndex].models = opelModels;
  console.log(`Updated ${opelModels.length} Opel models in the cars.json file.`);
} else {
  // Add the Opel brand
  carsJson.push({
    brand: 'Opel',
    image_url: '/images/brands/opel.png',
    models: opelModels
  });
  console.log(`Added Opel brand with ${opelModels.length} models to the cars.json file.`);
}

// Write the updated cars.json file
fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

// Clean up the temporary files
fs.unlinkSync(opelAstraModelsPath);
fs.unlinkSync(opelCorsaModelsPath);
fs.unlinkSync(opelInsigniaModelsPath);
fs.unlinkSync(opelMokkaModelsPath);
fs.unlinkSync(opelVectraModelsPath);
fs.unlinkSync(opelZafiraModelsPath);

console.log('Temporary files cleaned up.');
