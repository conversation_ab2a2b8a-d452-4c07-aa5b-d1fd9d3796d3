import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function convertPngToIco() {
  try {
    // Read the PNG file
    const pngPath = path.join(__dirname, 'public', 'images', 'Favicon1.png');

    // Create resized versions for favicon (16x16, 32x32, 48x48)
    const sizes = [16, 32, 48];

    for (const size of sizes) {
      await sharp(pngPath)
        .resize(size, size)
        .toFile(path.join(__dirname, 'public', `favicon-${size}.png`));

      console.log(`Created favicon-${size}.png`);
    }

    // Copy the original PNG to the public root directory
    await sharp(pngPath)
      .toFile(path.join(__dirname, 'public', 'favicon.png'));

    console.log('Created favicon.png in public directory');

    console.log('Favicon conversion complete!');
  } catch (error) {
    console.error('Error converting favicon:', error);
  }
}

convertPngToIco();
