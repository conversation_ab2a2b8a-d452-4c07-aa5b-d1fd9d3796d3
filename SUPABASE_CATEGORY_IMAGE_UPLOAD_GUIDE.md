# AROUZ MARKET - Category & Subcategory Image Upload Guide

## 📋 OVERVIEW

This comprehensive guide covers uploading category and subcategory images to the Supabase `category-images` storage bucket for the AROUZ MARKET marketplace. These images will be displayed in the navigation system with the new larger container sizes.

## 🎯 CONTAINER SPECIFICATIONS

### **Categories (Rectangular Containers)**
- **Desktop**: 80x64px (w-20 h-16)
- **Mobile**: 64x48px (w-16 h-12)
- **Shape**: Rectangular with rounded corners
- **Image Display Area**: 70x48px (desktop), 40x32px (mobile)

### **Subcategories (Circular Containers)**
- **Desktop**: 72x72px (w-18 h-18)
- **Mobile**: 56x56px (w-14 h-14)
- **Shape**: Circular
- **Image Display Area**: 48x48px (desktop), 40x40px (mobile)

## 📁 FOLDER STRUCTURE

The `category-images` bucket should be organized as follows:

```
category-images/
├── category/
│   ├── all.png
│   ├── tyres.png
│   ├── brakes.png
│   ├── filters.png
│   ├── ignition.png
│   ├── exterior.png
│   ├── interior.png
│   ├── engine.png
│   ├── transmission.png
│   ├── suspension.png
│   ├── electrical.png
│   └── tools.png
└── subcategory/
    ├── wheel-covers.png
    ├── tire-bag-sets.png
    ├── wheel-chocks.png
    ├── lug-wrenches.png
    ├── snow-chains.png
    ├── car-jacks.png
    ├── air-compressors.png
    ├── tyre-repair-kits.png
    ├── wheel-bolts.png
    ├── track-widening-kits.png
    ├── brake-pads.png
    ├── brake-discs.png
    ├── brake-calipers.png
    ├── brake-fluid.png
    ├── brake-lines.png
    └── [additional subcategories...]
```

## 🏷️ FILE NAMING CONVENTIONS

### **Categories**
- Format: `{category-id}.png`
- Examples:
  - `all.png` - All Categories
  - `tyres.png` - Tyres & Related Products
  - `brakes.png` - Brake Parts & Systems
  - `filters.png` - Filters
  - `ignition.png` - Ignition
  - `exterior.png` - Exterior
  - `interior.png` - Interior
  - `engine.png` - Engine
  - `transmission.png` - Transmission
  - `suspension.png` - Suspension
  - `electrical.png` - Electrical
  - `tools.png` - Tools

### **Subcategories**
- Format: `{subcategory-name-kebab-case}.png`
- Examples:
  - `wheel-covers.png` - Wheel Covers
  - `tire-bag-sets.png` - Tire Bag Sets
  - `wheel-chocks.png` - Wheel Chocks
  - `lug-wrenches.png` - Lug Wrenches
  - `snow-chains.png` - Snow Chains
  - `car-jacks.png` - Car Jacks
  - `air-compressors.png` - Air Compressors
  - `tyre-repair-kits.png` - Tyre Repair Kits
  - `wheel-bolts.png` - Wheel Bolts
  - `track-widening-kits.png` - Track Widening Kits

## 🖼️ IMAGE SPECIFICATIONS

### **Recommended Dimensions**
- **Categories**: 160x128px (2x desktop size for crisp display)
- **Subcategories**: 144x144px (2x desktop size for crisp display)

### **File Requirements**
- **Format**: PNG (preferred) or JPG
- **File Size**: Maximum 500KB per image
- **Background**: Transparent PNG recommended
- **Color Mode**: RGB
- **Quality**: High resolution for crisp display

### **Design Guidelines**
- **Style**: Clean, minimalist icons
- **Colors**: Monochrome or brand colors (#fa7b00, #071c44)
- **Content**: Simple, recognizable symbols
- **Padding**: 10-15% internal padding from edges
- **Consistency**: Uniform style across all images

## 📤 UPLOAD PROCESS

### **Step 1: Access Supabase Dashboard**
1. Navigate to: `https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images`
2. Log in with your AROUZ MARKET project credentials
3. Select the `category-images` bucket

### **Step 2: Create Folder Structure**
1. Click "Create folder" button
2. Create `category` folder
3. Create `subcategory` folder
4. Verify both folders are visible in the bucket

### **Step 3: Upload Category Images**
1. Navigate to the `category` folder
2. Click "Upload files" button
3. Select all category images (12 total)
4. Ensure file names match the naming convention exactly
5. Click "Upload" to complete

### **Step 4: Upload Subcategory Images**
1. Navigate to the `subcategory` folder
2. Click "Upload files" button
3. Select all subcategory images (66 total)
4. Ensure file names match the naming convention exactly
5. Click "Upload" to complete

### **Step 5: Verify Upload**
1. Check that all files are visible in their respective folders
2. Verify file names are correct (no spaces, proper kebab-case)
3. Confirm file sizes are under 500KB
4. Test image URLs are accessible

## 🔗 URL STRUCTURE

Images will be accessible via the following URL pattern:

### **Categories**
```
https://irkwpzcskeqtasutqnxp.supabase.co/storage/v1/object/public/category-images/category/{category-id}.png
```

### **Subcategories**
```
https://irkwpzcskeqtasutqnxp.supabase.co/storage/v1/object/public/category-images/subcategory/{subcategory-name}.png
```

## ✅ VERIFICATION CHECKLIST

### **Before Upload**
- [ ] Images are properly sized (160x128px for categories, 144x144px for subcategories)
- [ ] File names follow exact naming conventions
- [ ] File sizes are under 500KB
- [ ] Images have transparent backgrounds (PNG)
- [ ] All required images are prepared (12 categories + 66 subcategories)

### **After Upload**
- [ ] All files appear in correct folders
- [ ] File names match exactly (case-sensitive)
- [ ] Images load correctly in browser when accessing URLs directly
- [ ] No broken image icons in the marketplace
- [ ] Images display properly on both desktop and mobile
- [ ] Loading performance is acceptable

## 🔧 TROUBLESHOOTING

### **Common Issues & Solutions**

#### **Images Not Displaying**
- **Cause**: Incorrect file names or folder structure
- **Solution**: Verify exact naming convention and folder placement

#### **Broken Image Icons**
- **Cause**: File path mismatch or missing files
- **Solution**: Check console for 404 errors and verify file existence

#### **Slow Loading**
- **Cause**: Large file sizes
- **Solution**: Optimize images to under 200KB while maintaining quality

#### **Blurry Images**
- **Cause**: Low resolution or incorrect dimensions
- **Solution**: Use 2x resolution images (160x128px for categories)

#### **Cache Issues**
- **Cause**: Browser caching old images
- **Solution**: Hard refresh (Ctrl+F5) or clear browser cache

### **Testing Image Display**
1. Navigate to marketplace: `http://localhost:8080/my-vehicle-parts`
2. Check all category images load properly
3. Select different categories to verify subcategory images
4. Test on mobile viewport (responsive design)
5. Verify hover effects and selection states work correctly

## 📱 MOBILE CONSIDERATIONS

- Images automatically scale down for mobile devices
- Circular subcategory images maintain aspect ratio
- Touch targets remain accessible
- Loading performance optimized for mobile networks

## 🎨 DESIGN CONSISTENCY

- Maintain consistent visual style across all images
- Use AROUZ MARKET brand colors when appropriate
- Ensure icons are easily recognizable at small sizes
- Test visibility against gray background containers

## 🔄 UPDATING IMAGES

To update existing images:
1. Upload new image with same filename
2. Supabase will automatically replace the old version
3. Clear browser cache to see changes immediately
4. Test in marketplace to verify update

This guide ensures all category and subcategory images are properly uploaded and displayed in the AROUZ MARKET navigation system with optimal quality and performance.
