// Verification script to check if database is properly set up
// Run this in browser console

async function verifyDatabaseSetup() {
  console.log('🔍 VERIFYING DATABASE SETUP FOR CONSUMER PRIVACY PROTECTION');
  console.log('===========================================================');
  
  try {
    const { supabase } = await import('/src/services/authService.ts');
    
    // 1. Check if original_full_name column exists
    console.log('\n1️⃣ CHECKING DATABASE COLUMNS:');
    
    // Try to select from profiles table to see available columns
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id, full_name, original_full_name, phone, role')
      .limit(1);
      
    if (testError) {
      if (testError.message.includes('original_full_name')) {
        console.log('❌ original_full_name column does NOT exist');
        console.log('🚨 CRITICAL: You need to run the manual migration SQL!');
        console.log('📋 Run the SQL in manual_migration_add_original_full_name.sql');
        return false;
      } else {
        console.log('❌ Other database error:', testError);
        return false;
      }
    } else {
      console.log('✅ original_full_name column exists');
    }

    // 2. Check if privacy protection function exists
    console.log('\n2️⃣ CHECKING PRIVACY PROTECTION FUNCTION:');
    try {
      const { data: funcResult, error: funcError } = await supabase
        .rpc('convert_to_privacy_protected_name', { full_name_input: 'Test User' });
        
      if (funcError) {
        console.log('❌ Privacy protection function does NOT exist');
        console.log('🚨 CRITICAL: You need to run the manual migration SQL!');
        return false;
      } else {
        console.log('✅ Privacy protection function exists');
        console.log('📋 Test result: "Test User" →', funcResult);
      }
    } catch (err) {
      console.log('❌ Privacy protection function error:', err);
      return false;
    }

    // 3. Test creating a consumer profile
    console.log('\n3️⃣ TESTING CONSUMER PROFILE CREATION:');
    const testPhone = '+************';
    const testName = 'Test Consumer';
    
    // First, clean up any existing test data
    await supabase
      .from('profiles')
      .delete()
      .eq('phone', testPhone);
      
    // Create test profile
    const { data: insertData, error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: crypto.randomUUID(),
        phone: testPhone,
        role: 'consumer',
        full_name: testName,
        original_full_name: testName
      })
      .select()
      .single();
      
    if (insertError) {
      console.log('❌ Error creating test profile:', insertError);
      return false;
    } else {
      console.log('✅ Test profile created successfully');
      
      // Clean up test data
      await supabase
        .from('profiles')
        .delete()
        .eq('phone', testPhone);
        
      console.log('✅ Test data cleaned up');
    }

    console.log('\n🎉 DATABASE SETUP IS COMPLETE AND WORKING!');
    return true;
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

// Run verification
verifyDatabaseSetup().then(success => {
  if (success) {
    console.log('\n✅ You can now test the consumer authentication flow!');
  } else {
    console.log('\n❌ Please fix the database setup before testing!');
  }
});
