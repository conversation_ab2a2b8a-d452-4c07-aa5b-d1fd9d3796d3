-- Check search_suggestions table structure
SELECT 
    'SEARCH_SUGGESTIONS STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'search_suggestions' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check search_analytics table structure  
SELECT 
    'SEARCH_ANALYTICS STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'search_analytics' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Sample data from search_suggestions
SELECT 'SEARCH_SUGGESTIONS SAMPLE' as section, * FROM search_suggestions LIMIT 2;

-- Sample data from search_analytics
SELECT 'SEARCH_ANALYTICS SAMPLE' as section, * FROM search_analytics LIMIT 2;
