#!/usr/bin/env node

/**
 * AROUZ MARKET - Database Migration Script
 * 
 * This script handles database migrations between environments:
 * - Export schema from production
 * - Import schema to UAT
 * - Sync schema changes between environments
 * - Preserve data integrity
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { createRequire } from 'module';
const require = createRequire(import.meta.url);

// Configuration
const config = {
  production: {
    name: 'Production',
    host: 'db.irkwpzcskeqtasutqnxp.supabase.co',
    port: 5432,
    database: 'postgres',
    // Password will be prompted or from environment
  },
  uat: {
    name: 'UAT+DEV',
    host: 'db.vpqwfmpohjaqlrlufaga.supabase.co',
    port: 5432,
    database: 'postgres',
    // Password will be prompted or from environment
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logStep(message) {
  log(`🔧 ${message}`, 'cyan');
}

// Ensure required directories exist
function ensureDirectories() {
  const dirs = ['scripts/migrations', 'scripts/backups'];
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logInfo(`Created directory: ${dir}`);
    }
  });
}

// Generate timestamp for file names
function getTimestamp() {
  return new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
}

// Build PostgreSQL connection string
function buildConnectionString(env, password) {
  const cfg = config[env];
  return `postgresql://postgres:${password}@${cfg.host}:${cfg.port}/${cfg.database}`;
}

// Export database schema
function exportSchema(env, password, outputFile) {
  logStep(`Exporting ${config[env].name} database schema...`);
  
  const connectionString = buildConnectionString(env, password);
  const command = `pg_dump "${connectionString}" --schema-only --no-owner --no-privileges --clean --if-exists > "${outputFile}"`;
  
  try {
    execSync(command, { stdio: 'inherit' });
    logSuccess(`Schema exported to: ${outputFile}`);
    return true;
  } catch (error) {
    logError(`Failed to export schema: ${error.message}`);
    return false;
  }
}

// Export database data
function exportData(env, password, outputFile, tables = []) {
  logStep(`Exporting ${config[env].name} database data...`);
  
  const connectionString = buildConnectionString(env, password);
  let command = `pg_dump "${connectionString}" --data-only --no-owner --no-privileges`;
  
  if (tables.length > 0) {
    const tableArgs = tables.map(table => `--table=${table}`).join(' ');
    command += ` ${tableArgs}`;
  }
  
  command += ` > "${outputFile}"`;
  
  try {
    execSync(command, { stdio: 'inherit' });
    logSuccess(`Data exported to: ${outputFile}`);
    return true;
  } catch (error) {
    logError(`Failed to export data: ${error.message}`);
    return false;
  }
}

// Import SQL file to database
function importSql(env, password, inputFile) {
  logStep(`Importing SQL to ${config[env].name} database...`);
  
  if (!fs.existsSync(inputFile)) {
    logError(`Input file not found: ${inputFile}`);
    return false;
  }
  
  const connectionString = buildConnectionString(env, password);
  const command = `psql "${connectionString}" < "${inputFile}"`;
  
  try {
    execSync(command, { stdio: 'inherit' });
    logSuccess(`SQL imported successfully`);
    return true;
  } catch (error) {
    logError(`Failed to import SQL: ${error.message}`);
    return false;
  }
}

// Validate database connection
function validateConnection(env, password) {
  logStep(`Validating ${config[env].name} database connection...`);
  
  const connectionString = buildConnectionString(env, password);
  const command = `psql "${connectionString}" -c "SELECT version();"`;
  
  try {
    execSync(command, { stdio: 'pipe' });
    logSuccess(`Connection to ${config[env].name} database successful`);
    return true;
  } catch (error) {
    logError(`Failed to connect to ${config[env].name} database`);
    return false;
  }
}

// Main migration functions
async function migrateSchemaToUAT() {
  log('\n🚀 AROUZ MARKET - Schema Migration to UAT', 'magenta');
  log('=' .repeat(50), 'magenta');
  
  ensureDirectories();
  
  // Get passwords
  const prodPassword = process.env.PROD_DB_PASSWORD || 
    execSync('read -s -p "Enter Production database password: " pwd && echo $pwd', 
      { encoding: 'utf8', stdio: ['inherit', 'pipe', 'inherit'] }).trim();
  
  const uatPassword = process.env.UAT_DB_PASSWORD || 
    execSync('read -s -p "Enter UAT database password: " pwd && echo $pwd', 
      { encoding: 'utf8', stdio: ['inherit', 'pipe', 'inherit'] }).trim();
  
  // Validate connections
  if (!validateConnection('production', prodPassword)) {
    logError('Cannot connect to production database');
    process.exit(1);
  }
  
  if (!validateConnection('uat', uatPassword)) {
    logError('Cannot connect to UAT database');
    process.exit(1);
  }
  
  // Export production schema
  const timestamp = getTimestamp();
  const schemaFile = `scripts/migrations/schema-${timestamp}.sql`;
  
  if (!exportSchema('production', prodPassword, schemaFile)) {
    logError('Schema export failed');
    process.exit(1);
  }
  
  // Import schema to UAT
  if (!importSql('uat', uatPassword, schemaFile)) {
    logError('Schema import to UAT failed');
    process.exit(1);
  }
  
  // Import initial UAT data
  const uatSetupFile = 'scripts/setup-uat-database.sql';
  if (fs.existsSync(uatSetupFile)) {
    logStep('Importing UAT-specific setup...');
    if (!importSql('uat', uatPassword, uatSetupFile)) {
      logWarning('UAT setup import failed, but schema migration completed');
    }
  }
  
  logSuccess('Schema migration to UAT completed successfully!');
  logInfo(`Schema backup saved: ${schemaFile}`);
}

async function syncSchemaChanges() {
  log('\n🔄 AROUZ MARKET - Schema Synchronization', 'magenta');
  log('=' .repeat(50), 'magenta');
  
  logWarning('This will sync schema changes from Production to UAT');
  logWarning('UAT data may be affected. Continue? (y/N)');
  
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    readline.question('', (answer) => {
      readline.close();
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        migrateSchemaToUAT().then(resolve);
      } else {
        logInfo('Schema synchronization cancelled');
        resolve();
      }
    });
  });
}

async function backupDatabase(env) {
  log(`\n💾 AROUZ MARKET - ${config[env].name} Database Backup`, 'magenta');
  log('=' .repeat(50), 'magenta');
  
  ensureDirectories();
  
  const password = process.env[`${env.toUpperCase()}_DB_PASSWORD`] || 
    execSync(`read -s -p "Enter ${config[env].name} database password: " pwd && echo $pwd`, 
      { encoding: 'utf8', stdio: ['inherit', 'pipe', 'inherit'] }).trim();
  
  if (!validateConnection(env, password)) {
    logError(`Cannot connect to ${config[env].name} database`);
    process.exit(1);
  }
  
  const timestamp = getTimestamp();
  const schemaFile = `scripts/backups/${env}-schema-${timestamp}.sql`;
  const dataFile = `scripts/backups/${env}-data-${timestamp}.sql`;
  
  // Export schema
  if (!exportSchema(env, password, schemaFile)) {
    logError('Schema backup failed');
    process.exit(1);
  }
  
  // Export data
  if (!exportData(env, password, dataFile)) {
    logError('Data backup failed');
    process.exit(1);
  }
  
  logSuccess(`${config[env].name} database backup completed!`);
  logInfo(`Schema backup: ${schemaFile}`);
  logInfo(`Data backup: ${dataFile}`);
}

// CLI interface
function showHelp() {
  console.log(`
🗄️  AROUZ MARKET - Database Migration Tool

Usage: node scripts/migrate-database.js <command>

Commands:
  migrate-to-uat    Migrate production schema to UAT database
  sync-schema       Sync schema changes from production to UAT
  backup-prod       Backup production database
  backup-uat        Backup UAT database
  help              Show this help message

Environment Variables:
  PROD_DB_PASSWORD  Production database password
  UAT_DB_PASSWORD   UAT database password

Examples:
  node scripts/migrate-database.js migrate-to-uat
  node scripts/migrate-database.js backup-prod
  PROD_DB_PASSWORD=xxx UAT_DB_PASSWORD=yyy node scripts/migrate-database.js sync-schema
`);
}

// Main execution
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'migrate-to-uat':
      await migrateSchemaToUAT();
      break;
    case 'sync-schema':
      await syncSchemaChanges();
      break;
    case 'backup-prod':
      await backupDatabase('production');
      break;
    case 'backup-uat':
      await backupDatabase('uat');
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      logError('Invalid command');
      showHelp();
      process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logError(`Migration failed: ${error.message}`);
    process.exit(1);
  });
}

export {
  migrateSchemaToUAT,
  syncSchemaChanges,
  backupDatabase,
  exportSchema,
  importSql
};
