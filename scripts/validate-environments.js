#!/usr/bin/env node

/**
 * AROUZ MARKET - Environment Validation Script
 * 
 * This script validates both UAT and Production environments to ensure:
 * - 100% functionality parity
 * - All features working correctly
 * - Performance meets standards
 * - Security configurations active
 * - External services operational
 */

import https from 'https';
import http from 'http';
import fs from 'fs';

// Environment configurations
const environments = {
  uat: {
    name: 'UAT',
    url: 'https://dev.arouzmarket.com',
    description: 'UAT Environment (dev.arouzmarket.com)'
  },
  production: {
    name: 'Production',
    url: 'https://arouzmarket.com',
    description: 'Production Environment (arouzmarket.com)'
  }
};

// Test configurations
const testConfig = {
  timeout: 10000,
  retries: 3,
  expectedLoadTime: 3000, // 3 seconds
  criticalPaths: [
    '/',
    '/partners',
    '/marketplace',
    '/basket',
    '/wishlist',
    '/my-orders',
    '/app/dashboard'
  ],
  securityHeaders: [
    'strict-transport-security',
    'content-security-policy',
    'x-content-type-options',
    'x-frame-options',
    'x-xss-protection'
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logStep(message) {
  log(`🔧 ${message}`, 'cyan');
}

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, {
      timeout: testConfig.timeout,
      ...options
    }, (res) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data,
          responseTime,
          url
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout for ${url}`));
    });
  });
}

// Test functions
async function testBasicConnectivity(env) {
  logStep(`Testing basic connectivity for ${env.name}...`);
  
  try {
    const response = await makeRequest(env.url);
    
    if (response.statusCode === 200) {
      logSuccess(`${env.name} is accessible (${response.responseTime}ms)`);
      return { success: true, responseTime: response.responseTime };
    } else {
      logError(`${env.name} returned status ${response.statusCode}`);
      return { success: false, error: `HTTP ${response.statusCode}` };
    }
  } catch (error) {
    logError(`${env.name} connectivity failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testSPARouting(env) {
  logStep(`Testing SPA routing for ${env.name}...`);
  
  const results = [];
  
  for (const path of testConfig.criticalPaths) {
    const url = `${env.url}${path}`;
    
    try {
      const response = await makeRequest(url);
      
      if (response.statusCode === 200) {
        // Check if it's serving the SPA (should contain React app)
        if (response.data.includes('id="root"') || response.data.includes('AROUZ MARKET')) {
          logSuccess(`${path} - SPA routing working`);
          results.push({ path, success: true, responseTime: response.responseTime });
        } else {
          logWarning(`${path} - Response doesn't look like SPA`);
          results.push({ path, success: false, error: 'Not SPA response' });
        }
      } else {
        logError(`${path} - HTTP ${response.statusCode}`);
        results.push({ path, success: false, error: `HTTP ${response.statusCode}` });
      }
    } catch (error) {
      logError(`${path} - ${error.message}`);
      results.push({ path, success: false, error: error.message });
    }
  }
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  if (successCount === totalCount) {
    logSuccess(`All ${totalCount} routes working correctly`);
  } else {
    logWarning(`${successCount}/${totalCount} routes working`);
  }
  
  return results;
}

async function testSecurityHeaders(env) {
  logStep(`Testing security headers for ${env.name}...`);
  
  try {
    const response = await makeRequest(env.url);
    const headers = response.headers;
    const results = [];
    
    for (const headerName of testConfig.securityHeaders) {
      const headerValue = headers[headerName];
      
      if (headerValue) {
        logSuccess(`${headerName}: ${headerValue.substring(0, 50)}...`);
        results.push({ header: headerName, present: true, value: headerValue });
      } else {
        logWarning(`Missing security header: ${headerName}`);
        results.push({ header: headerName, present: false });
      }
    }
    
    // Check HTTPS redirect
    if (env.url.startsWith('https:')) {
      const httpUrl = env.url.replace('https:', 'http:');
      try {
        const httpResponse = await makeRequest(httpUrl);
        if (httpResponse.statusCode >= 300 && httpResponse.statusCode < 400) {
          logSuccess('HTTPS redirect working');
          results.push({ header: 'https-redirect', present: true });
        } else {
          logWarning('HTTPS redirect not detected');
          results.push({ header: 'https-redirect', present: false });
        }
      } catch (error) {
        logInfo('HTTP version not accessible (good for security)');
        results.push({ header: 'https-redirect', present: true });
      }
    }
    
    return results;
  } catch (error) {
    logError(`Security headers test failed: ${error.message}`);
    return [];
  }
}

async function testPerformance(env) {
  logStep(`Testing performance for ${env.name}...`);
  
  const results = [];
  const iterations = 3;
  
  for (let i = 0; i < iterations; i++) {
    try {
      const response = await makeRequest(env.url);
      results.push(response.responseTime);
    } catch (error) {
      logWarning(`Performance test iteration ${i + 1} failed`);
    }
  }
  
  if (results.length > 0) {
    const avgTime = results.reduce((a, b) => a + b, 0) / results.length;
    const minTime = Math.min(...results);
    const maxTime = Math.max(...results);
    
    logInfo(`Average response time: ${avgTime.toFixed(0)}ms`);
    logInfo(`Min: ${minTime}ms, Max: ${maxTime}ms`);
    
    if (avgTime <= testConfig.expectedLoadTime) {
      logSuccess(`Performance meets standards (<${testConfig.expectedLoadTime}ms)`);
      return { success: true, avgTime, minTime, maxTime };
    } else {
      logWarning(`Performance below standards (>${testConfig.expectedLoadTime}ms)`);
      return { success: false, avgTime, minTime, maxTime };
    }
  } else {
    logError('All performance tests failed');
    return { success: false };
  }
}

async function testAssetLoading(env) {
  logStep(`Testing asset loading for ${env.name}...`);
  
  try {
    const response = await makeRequest(env.url);
    const html = response.data;
    
    // Extract asset URLs from HTML
    const assetRegex = /(href|src)="([^"]*\.(css|js|png|jpg|jpeg|svg|ico))"/g;
    const assets = [];
    let match;
    
    while ((match = assetRegex.exec(html)) !== null) {
      const assetUrl = match[2];
      if (assetUrl.startsWith('/')) {
        assets.push(`${env.url}${assetUrl}`);
      } else if (assetUrl.startsWith('http')) {
        assets.push(assetUrl);
      }
    }
    
    logInfo(`Found ${assets.length} assets to test`);
    
    const results = [];
    const sampleSize = Math.min(assets.length, 10); // Test up to 10 assets
    
    for (let i = 0; i < sampleSize; i++) {
      const assetUrl = assets[i];
      
      try {
        const assetResponse = await makeRequest(assetUrl);
        
        if (assetResponse.statusCode === 200) {
          results.push({ url: assetUrl, success: true });
        } else {
          results.push({ url: assetUrl, success: false, error: `HTTP ${assetResponse.statusCode}` });
          logWarning(`Asset failed: ${assetUrl}`);
        }
      } catch (error) {
        results.push({ url: assetUrl, success: false, error: error.message });
        logWarning(`Asset error: ${assetUrl} - ${error.message}`);
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    
    if (successCount === sampleSize) {
      logSuccess(`All ${sampleSize} tested assets loading correctly`);
    } else {
      logWarning(`${successCount}/${sampleSize} assets loading correctly`);
    }
    
    return results;
  } catch (error) {
    logError(`Asset loading test failed: ${error.message}`);
    return [];
  }
}

// Main validation function
async function validateEnvironment(envKey) {
  const env = environments[envKey];
  
  log(`\n🧪 Validating ${env.description}`, 'magenta');
  log('='.repeat(60), 'magenta');
  
  const results = {
    environment: envKey,
    name: env.name,
    url: env.url,
    timestamp: new Date().toISOString(),
    tests: {}
  };
  
  // Run all tests
  results.tests.connectivity = await testBasicConnectivity(env);
  results.tests.routing = await testSPARouting(env);
  results.tests.security = await testSecurityHeaders(env);
  results.tests.performance = await testPerformance(env);
  results.tests.assets = await testAssetLoading(env);
  
  // Calculate overall score
  const testResults = Object.values(results.tests);
  const successfulTests = testResults.filter(test => {
    if (Array.isArray(test)) {
      return test.every(item => item.success !== false);
    }
    return test.success !== false;
  }).length;
  
  const totalTests = testResults.length;
  const score = Math.round((successfulTests / totalTests) * 100);
  
  results.score = score;
  results.status = score >= 80 ? 'PASS' : score >= 60 ? 'WARNING' : 'FAIL';
  
  // Display summary
  log(`\n📊 ${env.name} Validation Summary:`, 'cyan');
  log(`Score: ${score}% (${successfulTests}/${totalTests} tests passed)`, 
    score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red');
  log(`Status: ${results.status}`, 
    results.status === 'PASS' ? 'green' : results.status === 'WARNING' ? 'yellow' : 'red');
  
  return results;
}

// CLI interface
async function main() {
  const command = process.argv[2];
  
  log('🚀 AROUZ MARKET - Environment Validation Tool', 'magenta');
  log('='.repeat(60), 'magenta');
  
  let results = [];
  
  switch (command) {
    case 'uat':
      results.push(await validateEnvironment('uat'));
      break;
    case 'production':
    case 'prod':
      results.push(await validateEnvironment('production'));
      break;
    case 'all':
    default:
      results.push(await validateEnvironment('uat'));
      results.push(await validateEnvironment('production'));
      break;
  }
  
  // Save results
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
  const reportFile = `scripts/validation-report-${timestamp}.json`;
  
  fs.writeFileSync(reportFile, JSON.stringify(results, null, 2));
  
  // Final summary
  log('\n🎯 Final Validation Summary:', 'magenta');
  log('='.repeat(60), 'magenta');
  
  for (const result of results) {
    const statusColor = result.status === 'PASS' ? 'green' : 
                       result.status === 'WARNING' ? 'yellow' : 'red';
    log(`${result.name}: ${result.score}% - ${result.status}`, statusColor);
  }
  
  const allPassed = results.every(r => r.status === 'PASS');
  
  if (allPassed) {
    logSuccess('\n🎉 All environments validated successfully!');
    logSuccess('Ready for production deployment!');
  } else {
    logWarning('\n⚠️  Some environments need attention');
    logInfo('Review the detailed results above');
  }
  
  logInfo(`\nDetailed report saved: ${reportFile}`);
}

// Show help
function showHelp() {
  console.log(`
🧪 AROUZ MARKET - Environment Validation Tool

Usage: node scripts/validate-environments.js [command]

Commands:
  uat          Validate UAT environment only
  production   Validate Production environment only
  all          Validate both environments (default)

The tool tests:
  ✅ Basic connectivity
  ✅ SPA routing functionality
  ✅ Security headers
  ✅ Performance metrics
  ✅ Asset loading

Examples:
  node scripts/validate-environments.js
  node scripts/validate-environments.js uat
  node scripts/validate-environments.js production
`);
}

// Run if called directly
if (require.main === module) {
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
  } else {
    main().catch(error => {
      logError(`Validation failed: ${error.message}`);
      process.exit(1);
    });
  }
}

module.exports = { validateEnvironment, testBasicConnectivity, testSPARouting };
