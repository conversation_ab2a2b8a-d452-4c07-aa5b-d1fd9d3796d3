-- =============================================================================
-- AROUZ MARKET - Production Database Cleanup Script
-- =============================================================================
-- This script cleans test/development data from the production database
-- while preserving the schema and essential data structures
-- 
-- ⚠️  WARNING: This will DELETE test data from production database
-- ⚠️  Only run this on irkwpzcskeqtasutqnxp.supabase.co for production cleanup
-- ⚠️  Make sure to backup before running this script

BEGIN;

-- =============================================================================
-- BACKUP VERIFICATION
-- =============================================================================
-- Verify this is the production database
DO $$
BEGIN
    -- Check if we're on the correct database
    IF current_database() != 'postgres' THEN
        RAISE EXCEPTION 'This script should only run on the production postgres database';
    END IF;
    
    -- Log the cleanup operation
    RAISE NOTICE 'Starting production database cleanup...';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'Timestamp: %', NOW();
END $$;

-- =============================================================================
-- DATA CLEANUP - REMOVE TEST DATA
-- =============================================================================

-- Clean test orders and related data
DELETE FROM order_items WHERE order_id IN (
    SELECT id FROM orders WHERE
    consumer_name ILIKE '%test%' OR
    consumer_name ILIKE '%demo%'
);

DELETE FROM orders WHERE
    consumer_name ILIKE '%test%' OR
    consumer_name ILIKE '%demo%' OR
    created_at < NOW() - INTERVAL '30 days'; -- Remove old test orders

-- Clean test products
DELETE FROM products WHERE 
    name ILIKE '%test%' OR 
    name ILIKE '%demo%' OR 
    name ILIKE '%sample%' OR
    description ILIKE '%test%' OR
    product_id LIKE 'TEST-%' OR
    product_id LIKE 'DEMO-%' OR
    status = 'draft' AND created_at < NOW() - INTERVAL '7 days'; -- Remove old drafts

-- Clean test user profiles (keep admin and verified accounts)
DELETE FROM user_profiles WHERE 
    (first_name ILIKE '%test%' OR 
     last_name ILIKE '%test%' OR 
     email ILIKE '%test%' OR
     email ILIKE '%demo%' OR
     company_name ILIKE '%test%') AND
    is_verified = FALSE AND
    role != 'admin';

-- Clean test phone authentication records
DELETE FROM phone_auth WHERE 
    phone LIKE '+213555%' OR -- Test phone numbers
    phone LIKE '+1234%' OR
    created_at < NOW() - INTERVAL '7 days'; -- Remove old OTP records

-- Clean test shipments (if shipments table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'shipments') THEN
        DELETE FROM shipments WHERE
            tracking_number ILIKE '%test%' OR
            status = 'pending' AND created_at < NOW() - INTERVAL '30 days';
    END IF;
END $$;

-- Clean test user sessions
DELETE FROM user_sessions WHERE 
    started_at < NOW() - INTERVAL '30 days' OR
    ended_at IS NOT NULL AND ended_at < NOW() - INTERVAL '7 days';

-- Clean old error logs (keep recent ones for monitoring)
DELETE FROM error_logs WHERE 
    created_at < NOW() - INTERVAL '30 days' OR
    (severity = 'low' AND created_at < NOW() - INTERVAL '7 days');

-- =============================================================================
-- RESET SEQUENCES AND COUNTERS
-- =============================================================================

-- Reset any auto-incrementing sequences if needed
-- (Most tables use UUIDs, but some might have sequences)

-- =============================================================================
-- VERIFY ESSENTIAL DATA REMAINS
-- =============================================================================

-- Verify categories exist
DO $$
DECLARE
    category_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO category_count FROM categories;
    IF category_count = 0 THEN
        RAISE EXCEPTION 'ERROR: All categories were deleted! Rolling back...';
    END IF;
    RAISE NOTICE 'Categories remaining: %', category_count;
END $$;

-- Verify Algeria locations exist
DO $$
DECLARE
    location_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO location_count FROM algeria_locations;
    IF location_count = 0 THEN
        RAISE EXCEPTION 'ERROR: All locations were deleted! Rolling back...';
    END IF;
    RAISE NOTICE 'Algeria locations remaining: %', location_count;
END $$;

-- Verify shipping companies exist
DO $$
DECLARE
    shipping_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO shipping_count FROM shipping_companies;
    IF shipping_count = 0 THEN
        RAISE EXCEPTION 'ERROR: All shipping companies were deleted! Rolling back...';
    END IF;
    RAISE NOTICE 'Shipping companies remaining: %', shipping_count;
END $$;

-- =============================================================================
-- PRODUCTION DATA SUMMARY
-- =============================================================================

-- Show remaining data counts
DO $$
DECLARE
    user_count INTEGER;
    product_count INTEGER;
    order_count INTEGER;
    category_count INTEGER;
    location_count INTEGER;
    shipping_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM user_profiles;
    SELECT COUNT(*) INTO product_count FROM products;
    SELECT COUNT(*) INTO order_count FROM orders;
    SELECT COUNT(*) INTO category_count FROM categories;
    SELECT COUNT(*) INTO location_count FROM algeria_locations;
    SELECT COUNT(*) INTO shipping_count FROM shipping_companies;
    
    RAISE NOTICE '=== PRODUCTION DATABASE CLEANUP SUMMARY ===';
    RAISE NOTICE 'User Profiles: %', user_count;
    RAISE NOTICE 'Products: %', product_count;
    RAISE NOTICE 'Orders: %', order_count;
    RAISE NOTICE 'Categories: %', category_count;
    RAISE NOTICE 'Algeria Locations: %', location_count;
    RAISE NOTICE 'Shipping Companies: %', shipping_count;
    RAISE NOTICE '============================================';
END $$;

-- =============================================================================
-- OPTIMIZE DATABASE
-- =============================================================================

-- Vacuum and analyze tables for optimal performance
VACUUM ANALYZE user_profiles;
VACUUM ANALYZE products;
VACUUM ANALYZE orders;
VACUUM ANALYZE order_items;
VACUUM ANALYZE categories;
VACUUM ANALYZE algeria_locations;
VACUUM ANALYZE shipping_companies;
VACUUM ANALYZE shipments;
VACUUM ANALYZE user_sessions;
VACUUM ANALYZE error_logs;

-- =============================================================================
-- FINAL VERIFICATION
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Production database cleanup completed successfully!';
    RAISE NOTICE '✅ Database is ready for production deployment';
    RAISE NOTICE '✅ All essential data structures preserved';
    RAISE NOTICE '✅ Test data removed';
    RAISE NOTICE '✅ Database optimized';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PRODUCTION DATABASE IS READY!';
END $$;

COMMIT;
