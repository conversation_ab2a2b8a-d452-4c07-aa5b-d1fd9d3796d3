-- =============================================================================
-- AROUZ MARKET - UAT Database Setup Script
-- =============================================================================
-- This script sets up the UAT database with identical schema to production
-- Run this script on the NEW UAT Supabase project

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================================================
-- USERS AND AUTHENTICATION TABLES
-- =============================================================================

-- User profiles table (identical to production)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    phone TEXT UNIQUE,
    first_name TEXT,
    last_name TEXT,
    role TEXT NOT NULL CHECK (role IN ('supplier', 'merchant', 'consumer', 'distribution', 'admin', 'shipping_company')),
    company_name TEXT,
    business_license TEXT,
    tax_id TEXT,
    address JSONB,
    profile_image_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Phone authentication table
CREATE TABLE IF NOT EXISTS phone_auth (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone TEXT UNIQUE NOT NULL,
    otp_code TEXT,
    otp_expires_at TIMESTAMP WITH TIME ZONE,
    is_verified BOOLEAN DEFAULT FALSE,
    attempts INTEGER DEFAULT 0,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- PRODUCT MANAGEMENT TABLES
-- =============================================================================

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    image_url TEXT,
    parent_id UUID REFERENCES categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table (unified for all categories)
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id TEXT UNIQUE NOT NULL,
    supplier_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id),
    name TEXT NOT NULL,
    description TEXT,
    brand TEXT,
    model TEXT,
    year INTEGER,
    price DECIMAL(10,2),
    currency TEXT DEFAULT 'DZD',
    stock_quantity INTEGER DEFAULT 0,
    min_order_quantity INTEGER DEFAULT 1,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'out_of_stock', 'discontinued')),
    images JSONB DEFAULT '[]',
    specifications JSONB DEFAULT '{}',
    compatibility JSONB DEFAULT '{}',
    tags TEXT[],
    weight DECIMAL(8,2),
    dimensions JSONB,
    warranty_period INTEGER,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ORDER MANAGEMENT TABLES
-- =============================================================================

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number TEXT UNIQUE NOT NULL,
    consumer_id UUID REFERENCES user_profiles(id),
    consumer_phone TEXT,
    consumer_name TEXT,
    total_amount DECIMAL(12,2) NOT NULL,
    currency TEXT DEFAULT 'DZD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
    delivery_address JSONB NOT NULL,
    delivery_instructions TEXT,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    supplier_id UUID REFERENCES user_profiles(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- SHIPPING AND LOGISTICS TABLES
-- =============================================================================

-- Shipping companies table
CREATE TABLE IF NOT EXISTS shipping_companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_name TEXT NOT NULL,
    contact_email TEXT,
    contact_phone TEXT,
    coverage_areas JSONB DEFAULT '[]',
    service_types JSONB DEFAULT '[]',
    commission_rate DECIMAL(5,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shipments table
CREATE TABLE IF NOT EXISTS shipments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id),
    shipping_company_id UUID REFERENCES shipping_companies(id),
    tracking_number TEXT UNIQUE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'picked_up', 'in_transit', 'delivered', 'failed')),
    pickup_address JSONB,
    delivery_address JSONB,
    estimated_delivery TIMESTAMP WITH TIME ZONE,
    actual_delivery TIMESTAMP WITH TIME ZONE,
    shipping_cost DECIMAL(10,2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- LOCATION DATA TABLES
-- =============================================================================

-- Algeria administrative divisions
CREATE TABLE IF NOT EXISTS algeria_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    name_ar TEXT,
    type TEXT NOT NULL CHECK (type IN ('wilaya', 'daira', 'commune')),
    code TEXT,
    parent_id UUID REFERENCES algeria_locations(id),
    coordinates JSONB,
    postal_codes TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ANALYTICS AND MONITORING TABLES
-- =============================================================================

-- User sessions for analytics
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id TEXT UNIQUE NOT NULL,
    user_id UUID REFERENCES user_profiles(id),
    ip_address INET,
    user_agent TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    page_views INTEGER DEFAULT 0,
    actions JSONB DEFAULT '[]'
);

-- Error logs
CREATE TABLE IF NOT EXISTS error_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id TEXT,
    user_id UUID REFERENCES user_profiles(id),
    error_type TEXT NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    url TEXT,
    user_agent TEXT,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_phone ON user_profiles(phone);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);

-- Products indexes
CREATE INDEX IF NOT EXISTS idx_products_product_id ON products(product_id);
CREATE INDEX IF NOT EXISTS idx_products_supplier_id ON products(supplier_id);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);

-- Orders indexes
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_consumer_id ON orders(consumer_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

-- Order items indexes
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_supplier_id ON order_items(supplier_id);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE phone_auth ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipping_companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
ALTER TABLE algeria_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Products policies
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (status = 'active');

CREATE POLICY "Suppliers can manage their own products" ON products
    FOR ALL USING (auth.uid() IN (SELECT user_id FROM user_profiles WHERE id = supplier_id));

-- Orders policies
CREATE POLICY "Users can view their own orders" ON orders
    FOR SELECT USING (
        auth.uid() IN (SELECT user_id FROM user_profiles WHERE id = consumer_id)
        OR auth.uid() IN (
            SELECT up.user_id FROM user_profiles up
            JOIN order_items oi ON up.id = oi.supplier_id
            WHERE oi.order_id = orders.id
        )
    );

-- Categories and locations are public
CREATE POLICY "Categories are publicly readable" ON categories
    FOR SELECT USING (true);

CREATE POLICY "Algeria locations are publicly readable" ON algeria_locations
    FOR SELECT USING (true);

-- =============================================================================
-- FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all relevant tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INITIAL DATA FOR UAT TESTING
-- =============================================================================

-- Insert test categories
INSERT INTO categories (name, slug, description) VALUES
('Tyres & Related Products', 'tyres', 'Tyres and related automotive products'),
('Brake System', 'brake-system', 'Brake pads, discs, and related components'),
('Engine Parts', 'engine-parts', 'Engine components and accessories'),
('Electrical System', 'electrical-system', 'Electrical components and wiring'),
('Suspension System', 'suspension-system', 'Suspension components and parts')
ON CONFLICT (slug) DO NOTHING;

-- Insert test Algeria locations (sample)
INSERT INTO algeria_locations (name, name_ar, type, code) VALUES
('Alger', 'الجزائر', 'wilaya', '16'),
('Oran', 'وهران', 'wilaya', '31'),
('Constantine', 'قسنطينة', 'wilaya', '25'),
('Annaba', 'عنابة', 'wilaya', '23'),
('Blida', 'البليدة', 'wilaya', '09')
ON CONFLICT DO NOTHING;

-- Insert test shipping companies
INSERT INTO shipping_companies (company_name, contact_email, contact_phone, coverage_areas, service_types) VALUES
('Express Delivery DZ', '<EMAIL>', '+213555000001', '["Alger", "Oran", "Constantine"]', '["standard", "express"]'),
('Fast Logistics', '<EMAIL>', '+213555000002', '["Alger", "Blida", "Boumerdes"]', '["standard", "same_day"]'),
('National Transport', '<EMAIL>', '+213555000003', '["All_Wilayas"]', '["standard", "express", "freight"]')
ON CONFLICT DO NOTHING;

COMMIT;
