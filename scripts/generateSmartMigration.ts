/**
 * Generate Smart Migration SQL that preserves original IDs where possible
 * Only adds category prefixes to actual duplicate subcategories
 */

import { CATEGORIES } from '../src/data/categoryData.js';
import fs from 'fs';
import path from 'path';

function generateSmartMigrationSQL(): string {
  // First, identify which subcategories are duplicates
  const subcategoryMap = new Map<string, { id: string; categoryId: string; name: string; displayName: string }[]>();
  
  CATEGORIES.forEach(category => {
    if (category.subcategories && category.subcategories.length > 0) {
      category.subcategories.forEach(sub => {
        if (!subcategoryMap.has(sub.id)) {
          subcategoryMap.set(sub.id, []);
        }
        subcategoryMap.get(sub.id)!.push({
          id: sub.id,
          categoryId: sub.categoryId,
          name: sub.name,
          displayName: sub.displayName
        });
      });
    }
  });

  // Identify which IDs are duplicates
  const duplicateIds = new Set<string>();
  subcategoryMap.forEach((entries, subcategoryId) => {
    if (entries.length > 1) {
      duplicateIds.add(subcategoryId);
    }
  });

  console.log(`Found ${duplicateIds.size} duplicate subcategory IDs that need smart handling...`);

  let sql = `-- =====================================================
-- AROUZ MARKET - Smart Categories & Subcategories Migration
-- Generated: ${new Date().toISOString()}
-- Purpose: Sync Supabase with localStorage data (1000000000% accuracy)
-- Strategy: Preserve original IDs, only prefix actual duplicates
-- =====================================================

-- Clear existing data to start fresh
DELETE FROM subcategories;
DELETE FROM categories;

-- Insert ALL categories (10 total)
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES\n`;

  // Generate categories
  const categoryValues = CATEGORIES.map((cat, index) => {
    const idPrefix = cat.id === 'tyres' ? 'TYR' : 'PROD';
    return `  ('${cat.id}', '${cat.name.replace(/'/g, "''")}', '${cat.displayName.replace(/'/g, "''")}', '${cat.description?.replace(/'/g, "''") || ''}', '${idPrefix}', ${index})`;
  }).join(',\n');

  sql += categoryValues + ';\n\n';

  // Generate subcategories with smart ID handling
  sql += '-- Insert ALL subcategories (459 total) - smart duplicate handling\n';
  sql += 'INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES\n';

  const allSubcategories: string[] = [];
  
  CATEGORIES.forEach(category => {
    if (category.subcategories && category.subcategories.length > 0) {
      category.subcategories.forEach((sub, index) => {
        let uniqueId = sub.id;
        
        // Only add prefix if this ID is a duplicate AND it's not the first occurrence
        if (duplicateIds.has(sub.id)) {
          const allOccurrences = subcategoryMap.get(sub.id) || [];
          const isFirstOccurrence = allOccurrences[0].categoryId === category.id;
          
          if (!isFirstOccurrence) {
            // This is a duplicate, add category prefix
            uniqueId = `${category.id}-${sub.id}`;
          }
          // If it's the first occurrence, keep the original ID
        }
        
        const value = `  ('${uniqueId}', '${sub.name.replace(/'/g, "''")}', '${sub.displayName.replace(/'/g, "''")}', '${sub.categoryId}', ${index + 1})`;
        allSubcategories.push(value);
      });
    }
  });

  sql += allSubcategories.join(',\n') + ';\n\n';

  sql += `-- Verification queries
SELECT 'Smart migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT category_id, COUNT(*) as subcategory_count FROM subcategories GROUP BY category_id ORDER BY category_id;

-- Show which IDs got prefixed (duplicates only)
SELECT id, name, category_id, 
  CASE 
    WHEN id LIKE '%-%' THEN 'Prefixed (was duplicate)'
    ELSE 'Original ID preserved'
  END as id_status
FROM subcategories 
WHERE id IN (
  SELECT DISTINCT CASE 
    WHEN id LIKE '%-%' THEN SUBSTRING(id FROM POSITION('-' IN id) + 1)
    ELSE id
  END
  FROM subcategories
  GROUP BY CASE 
    WHEN id LIKE '%-%' THEN SUBSTRING(id FROM POSITION('-' IN id) + 1)
    ELSE id
  END
  HAVING COUNT(*) > 1
)
ORDER BY category_id, id;`;

  return sql;
}

// Generate the smart migration
const migrationSQL = generateSmartMigrationSQL();

// Write to file
const outputPath = path.join(process.cwd(), 'supabase/migrations/20241220120012_smart_categories_migration.sql');
fs.writeFileSync(outputPath, migrationSQL, 'utf8');

console.log('✅ Smart migration SQL generated successfully!');
console.log(`📁 File saved to: ${outputPath}`);
console.log(`📊 Total categories: ${CATEGORIES.length}`);
console.log(`📊 Total subcategories: ${CATEGORIES.reduce((total, cat) => total + (cat.subcategories?.length || 0), 0)}`);
