// =============================================================================
// AROUZ MARKET - Production Authentication Test Script
// =============================================================================
// This script tests the backend-frontend connection in production
// Run this in browser console on https://arouzmarket.com

console.log('🔍 AROUZ MARKET - Production Auth Test');
console.log('=====================================');

// Test 1: Check environment variables
console.log('📋 ENVIRONMENT CHECK:');
console.log('VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('VITE_USE_SUPABASE:', import.meta.env.VITE_USE_SUPABASE);
console.log('Current URL:', window.location.origin);

// Test 2: Check Supabase connection
async function testSupabaseConnection() {
    try {
        console.log('\n🔗 SUPABASE CONNECTION TEST:');
        
        // Import Supabase client
        const { createClient } = await import('@supabase/supabase-js');
        
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
        const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
            console.error('❌ Missing Supabase credentials');
            return false;
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test database connection
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        
        if (error) {
            console.error('❌ Database connection failed:', error.message);
            return false;
        }
        
        console.log('✅ Database connection successful');
        return true;
        
    } catch (error) {
        console.error('❌ Supabase connection error:', error);
        return false;
    }
}

// Test 3: Check auth configuration
async function testAuthConfig() {
    try {
        console.log('\n🔐 AUTH CONFIGURATION TEST:');
        
        const { createClient } = await import('@supabase/supabase-js');
        const supabase = createClient(
            import.meta.env.VITE_SUPABASE_URL,
            import.meta.env.VITE_SUPABASE_ANON_KEY
        );
        
        // Get current session
        const { data: session } = await supabase.auth.getSession();
        console.log('Current session:', session.session ? 'Active' : 'None');
        
        // Test auth settings by attempting to get user
        const { data: user } = await supabase.auth.getUser();
        console.log('Current user:', user.user ? user.user.email : 'None');
        
        console.log('✅ Auth configuration accessible');
        return true;
        
    } catch (error) {
        console.error('❌ Auth configuration error:', error);
        return false;
    }
}

// Test 4: Simulate registration test
async function testRegistrationFlow() {
    console.log('\n📝 REGISTRATION FLOW TEST:');
    console.log('Testing email redirect configuration...');
    
    const expectedRedirect = `${window.location.origin}/auth/callback`;
    console.log('Expected redirect URL:', expectedRedirect);
    
    // Check if the redirect URL is properly configured
    if (window.location.origin === 'https://arouzmarket.com') {
        console.log('✅ Production domain confirmed');
        console.log('✅ Redirect URL should be: https://arouzmarket.com/auth/callback');
    } else {
        console.log('⚠️ Not on production domain:', window.location.origin);
    }
    
    return true;
}

// Run all tests
async function runAllTests() {
    console.log('\n🚀 STARTING PRODUCTION TESTS...\n');
    
    const results = {
        supabase: await testSupabaseConnection(),
        auth: await testAuthConfig(),
        registration: await testRegistrationFlow()
    };
    
    console.log('\n📊 TEST RESULTS:');
    console.log('================');
    console.log('Supabase Connection:', results.supabase ? '✅ PASS' : '❌ FAIL');
    console.log('Auth Configuration:', results.auth ? '✅ PASS' : '❌ FAIL');
    console.log('Registration Flow:', results.registration ? '✅ PASS' : '❌ FAIL');
    
    const allPassed = Object.values(results).every(result => result);
    
    console.log('\n🎯 OVERALL STATUS:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
    
    if (!allPassed) {
        console.log('\n🔧 NEXT STEPS:');
        if (!results.supabase) {
            console.log('- Check Supabase URL and API key configuration');
        }
        if (!results.auth) {
            console.log('- Verify Supabase auth settings in dashboard');
        }
        console.log('- Configure Site URL and Redirect URLs in Supabase dashboard');
        console.log('- Enable email confirmation in Supabase Auth settings');
    }
    
    return results;
}

// Auto-run tests
runAllTests().catch(console.error);

// Export for manual testing
window.arouzMarketTests = {
    runAllTests,
    testSupabaseConnection,
    testAuthConfig,
    testRegistrationFlow
};
