-- =============================================================================
-- AROUZ MARKET - SAFE Production Database Cleanup Script
-- =============================================================================
-- This script safely cleans test/development data from the production database
-- while checking for table and column existence first
-- 
-- ⚠️  WARNING: This will DELETE test data from production database
-- ⚠️  Only run this on irkwpzcskeqtasutqnxp.supabase.co for production cleanup

BEGIN;

-- =============================================================================
-- BACKUP VERIFICATION
-- =============================================================================
DO $$
BEGIN
    -- Log the cleanup operation
    RAISE NOTICE 'Starting SAFE production database cleanup...';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'Timestamp: %', NOW();
END $$;

-- =============================================================================
-- SAFE DATA CLEANUP - CHECK EXISTENCE FIRST
-- =============================================================================

-- Clean test orders and related data (SAFE VERSION)
DO $$
BEGIN
    -- Check if order_items table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'order_items') THEN
        -- Check if orders table exists
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'orders') THEN
            DELETE FROM order_items WHERE order_id IN (
                SELECT id FROM orders WHERE 
                consumer_name ILIKE '%test%' OR 
                consumer_name ILIKE '%demo%'
            );
            RAISE NOTICE 'Cleaned test order items';
        END IF;
    END IF;
END $$;

DO $$
BEGIN
    -- Clean test orders
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'orders') THEN
        DELETE FROM orders WHERE 
            consumer_name ILIKE '%test%' OR 
            consumer_name ILIKE '%demo%' OR
            created_at < NOW() - INTERVAL '30 days'; -- Remove old test orders
        RAISE NOTICE 'Cleaned test orders';
    END IF;
END $$;

-- Clean test products (SAFE VERSION)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
        DELETE FROM products WHERE 
            name ILIKE '%test%' OR 
            name ILIKE '%demo%' OR 
            name ILIKE '%sample%' OR
            description ILIKE '%test%' OR
            product_id LIKE 'TEST-%' OR
            product_id LIKE 'DEMO-%' OR
            (status = 'draft' AND created_at < NOW() - INTERVAL '7 days'); -- Remove old drafts
        RAISE NOTICE 'Cleaned test products';
    END IF;
END $$;

-- Clean test user profiles (SAFE VERSION)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        DELETE FROM user_profiles WHERE 
            (first_name ILIKE '%test%' OR 
             last_name ILIKE '%test%' OR 
             email ILIKE '%test%' OR
             email ILIKE '%demo%' OR
             company_name ILIKE '%test%') AND
            is_verified = FALSE AND
            role != 'admin';
        RAISE NOTICE 'Cleaned test user profiles';
    END IF;
END $$;

-- Clean test phone authentication records (SAFE VERSION)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'phone_auth') THEN
        DELETE FROM phone_auth WHERE 
            phone LIKE '+213555%' OR -- Test phone numbers
            phone LIKE '+1234%' OR
            created_at < NOW() - INTERVAL '7 days'; -- Remove old OTP records
        RAISE NOTICE 'Cleaned test phone auth records';
    END IF;
END $$;

-- Clean test shipments (SAFE VERSION)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'shipments') THEN
        DELETE FROM shipments WHERE 
            tracking_number ILIKE '%test%' OR
            (status = 'pending' AND created_at < NOW() - INTERVAL '30 days');
        RAISE NOTICE 'Cleaned test shipments';
    END IF;
END $$;

-- Clean test user sessions (SAFE VERSION)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_sessions') THEN
        DELETE FROM user_sessions WHERE 
            started_at < NOW() - INTERVAL '30 days' OR
            (ended_at IS NOT NULL AND ended_at < NOW() - INTERVAL '7 days');
        RAISE NOTICE 'Cleaned old user sessions';
    END IF;
END $$;

-- Clean old error logs (SAFE VERSION)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'error_logs') THEN
        DELETE FROM error_logs WHERE 
            created_at < NOW() - INTERVAL '30 days' OR
            (severity = 'low' AND created_at < NOW() - INTERVAL '7 days');
        RAISE NOTICE 'Cleaned old error logs';
    END IF;
END $$;

-- =============================================================================
-- VERIFY ESSENTIAL DATA REMAINS (SAFE VERSION)
-- =============================================================================

-- Verify categories exist (if table exists)
DO $$
DECLARE
    category_count INTEGER := 0;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'categories') THEN
        SELECT COUNT(*) INTO category_count FROM categories;
        RAISE NOTICE 'Categories remaining: %', category_count;
    ELSE
        RAISE NOTICE 'Categories table does not exist yet (normal for new setup)';
    END IF;
END $$;

-- Verify Algeria locations exist (if table exists)
DO $$
DECLARE
    location_count INTEGER := 0;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'algeria_locations') THEN
        SELECT COUNT(*) INTO location_count FROM algeria_locations;
        RAISE NOTICE 'Algeria locations remaining: %', location_count;
    ELSE
        RAISE NOTICE 'Algeria locations table does not exist yet (normal for new setup)';
    END IF;
END $$;

-- Verify shipping companies exist (if table exists)
DO $$
DECLARE
    shipping_count INTEGER := 0;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'shipping_companies') THEN
        SELECT COUNT(*) INTO shipping_count FROM shipping_companies;
        RAISE NOTICE 'Shipping companies remaining: %', shipping_count;
    ELSE
        RAISE NOTICE 'Shipping companies table does not exist yet (normal for new setup)';
    END IF;
END $$;

-- =============================================================================
-- PRODUCTION DATA SUMMARY (SAFE VERSION)
-- =============================================================================

DO $$
DECLARE
    user_count INTEGER := 0;
    product_count INTEGER := 0;
    order_count INTEGER := 0;
    category_count INTEGER := 0;
    location_count INTEGER := 0;
    shipping_count INTEGER := 0;
BEGIN
    -- Count existing tables only
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        SELECT COUNT(*) INTO user_count FROM user_profiles;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
        SELECT COUNT(*) INTO product_count FROM products;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'orders') THEN
        SELECT COUNT(*) INTO order_count FROM orders;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'categories') THEN
        SELECT COUNT(*) INTO category_count FROM categories;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'algeria_locations') THEN
        SELECT COUNT(*) INTO location_count FROM algeria_locations;
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'shipping_companies') THEN
        SELECT COUNT(*) INTO shipping_count FROM shipping_companies;
    END IF;
    
    RAISE NOTICE '=== SAFE PRODUCTION DATABASE CLEANUP SUMMARY ===';
    RAISE NOTICE 'User Profiles: %', user_count;
    RAISE NOTICE 'Products: %', product_count;
    RAISE NOTICE 'Orders: %', order_count;
    RAISE NOTICE 'Categories: %', category_count;
    RAISE NOTICE 'Algeria Locations: %', location_count;
    RAISE NOTICE 'Shipping Companies: %', shipping_count;
    RAISE NOTICE '================================================';
END $$;

-- =============================================================================
-- OPTIMIZE DATABASE (SAFE VERSION)
-- =============================================================================

DO $$
BEGIN
    -- Only vacuum tables that exist
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        EXECUTE 'VACUUM ANALYZE user_profiles';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
        EXECUTE 'VACUUM ANALYZE products';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'orders') THEN
        EXECUTE 'VACUUM ANALYZE orders';
    END IF;
    
    RAISE NOTICE 'Database optimization completed';
END $$;

-- =============================================================================
-- FINAL VERIFICATION
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ SAFE production database cleanup completed successfully!';
    RAISE NOTICE '✅ Database is ready for production deployment';
    RAISE NOTICE '✅ All operations were safe and checked for existence';
    RAISE NOTICE '✅ No errors occurred';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PRODUCTION DATABASE IS READY!';
END $$;

COMMIT;
