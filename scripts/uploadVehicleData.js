// Script to upload vehicle data to Supabase
// This script would be run manually or as part of a build process

import { storeVehicleData, initializeVehicleDataTables } from '../src/services/supabaseService.js';
import carsData from '../src/services/data/cars.json';

/**
 * Upload vehicle data to Supabase
 */
const uploadVehicleData = async () => {
  try {
    console.log('Initializing vehicle data tables...');
    const initialized = await initializeVehicleDataTables();
    
    if (!initialized) {
      console.error('Failed to initialize vehicle data tables. Please check your Supabase setup.');
      return;
    }
    
    console.log('Uploading car data...');
    const carsResult = await storeVehicleData('car', carsData);
    
    if (carsResult.success) {
      console.log('Successfully uploaded car data to Supabase.');
    } else {
      console.error('Failed to upload car data:', carsResult.error);
    }
    
    // Add similar code for motorcycles and trucks when those JSON files are created
    
    console.log('Vehicle data upload complete.');
  } catch (error) {
    console.error('Error uploading vehicle data:', error);
  }
};

// Run the upload function
uploadVehicleData();
