-- =============================================================================
-- AROUZ MARKET - COMPLETE DATABASE DISCOVERY SCRIPT
-- =============================================================================
-- This script discovers EVERY SINGLE TABLE and COLUMN in your database
-- So we can create a 100000000000000% accurate cleanup plan
-- =============================================================================

-- =============================================================================
-- 1. DISCOVER ALL TABLES IN DATABASE
-- =============================================================================
SELECT 
    '🗂️ TABLE DISCOVERY' as section,
    table_name,
    table_type,
    CASE 
        WHEN table_type = 'BASE TABLE' THEN '📊 Data Table'
        WHEN table_type = 'VIEW' THEN '👁️ View'
        ELSE '❓ Other'
    END as table_description
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- =============================================================================
-- 2. GET COMPLETE STRUCTURE OF EVERY TABLE
-- =============================================================================

-- Products table structure (if exists)
SELECT 
    '📦 PRODUCTS TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'products' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Orders table structure (if exists)
SELECT 
    '🛒 ORDERS TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'orders' AND table_schema = 'public'
ORDER BY ordinal_position;

-- User profiles table structure (if exists)
SELECT 
    '👤 USER_PROFILES TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Order items table structure (if exists)
SELECT 
    '📋 ORDER_ITEMS TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'order_items' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Phone auth table structure (if exists)
SELECT 
    '📱 PHONE_AUTH TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'phone_auth' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Shipments table structure (if exists)
SELECT 
    '🚚 SHIPMENTS TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'shipments' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Categories table structure (if exists)
SELECT 
    '🏷️ CATEGORIES TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'categories' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Shipping companies table structure (if exists)
SELECT 
    '🏢 SHIPPING_COMPANIES TABLE STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'shipping_companies' AND table_schema = 'public'
ORDER BY ordinal_position;

-- =============================================================================
-- 3. DISCOVER ALL OTHER TABLES DYNAMICALLY
-- =============================================================================
DO $$
DECLARE
    table_record RECORD;
    column_record RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 DISCOVERING ALL OTHER TABLES...';
    RAISE NOTICE '=====================================';
    
    -- Loop through all tables not already checked
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name NOT IN ('products', 'orders', 'user_profiles', 'order_items', 'phone_auth', 'shipments', 'categories', 'shipping_companies')
        ORDER BY table_name
    LOOP
        RAISE NOTICE '';
        RAISE NOTICE '📋 TABLE: %', UPPER(table_record.table_name);
        RAISE NOTICE '----------------------------------------';
        
        -- Show columns for this table
        FOR column_record IN 
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = table_record.table_name AND table_schema = 'public'
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE '   📄 % (%) - Nullable: %', 
                column_record.column_name, 
                column_record.data_type,
                column_record.is_nullable;
        END LOOP;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ DATABASE DISCOVERY COMPLETED!';
END $$;

-- =============================================================================
-- 4. COUNT RECORDS IN EACH TABLE
-- =============================================================================
DO $$
DECLARE
    table_record RECORD;
    record_count INTEGER;
    sql_query TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📊 RECORD COUNTS FOR ALL TABLES';
    RAISE NOTICE '================================';
    
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        ORDER BY table_name
    LOOP
        BEGIN
            sql_query := 'SELECT COUNT(*) FROM ' || quote_ident(table_record.table_name);
            EXECUTE sql_query INTO record_count;
            RAISE NOTICE '📋 %: % records', table_record.table_name, record_count;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '❌ %: Access denied or error', table_record.table_name;
        END;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ RECORD COUNT ANALYSIS COMPLETED!';
END $$;

-- =============================================================================
-- 5. SAMPLE DATA FROM KEY TABLES (FIRST 2 RECORDS ONLY)
-- =============================================================================

-- Sample products data (if accessible)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
        RAISE NOTICE '';
        RAISE NOTICE '📦 SAMPLE PRODUCTS DATA (First 2 records):';
        RAISE NOTICE '==========================================';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Cannot access products table';
END $$;

-- Show first 2 products
SELECT '📦 PRODUCTS SAMPLE' as section, * FROM products LIMIT 2;

-- Sample orders data (if accessible)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'orders') THEN
        RAISE NOTICE '';
        RAISE NOTICE '🛒 SAMPLE ORDERS DATA (First 2 records):';
        RAISE NOTICE '========================================';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Cannot access orders table';
END $$;

-- Show first 2 orders
SELECT '🛒 ORDERS SAMPLE' as section, * FROM orders LIMIT 2;
