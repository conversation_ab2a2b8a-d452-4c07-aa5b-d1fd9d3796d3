/**
 * Generate Fixed Migration SQL with Unique Subcategory IDs
 * This script fixes duplicate subcategory IDs by adding category prefixes
 */

import { CATEGORIES } from '../src/data/categoryData.js';
import fs from 'fs';
import path from 'path';

function generateFixedMigrationSQL(): string {
  // First, identify duplicates
  const subcategoryMap = new Map<string, string[]>();
  
  CATEGORIES.forEach(category => {
    if (category.subcategories && category.subcategories.length > 0) {
      category.subcategories.forEach(sub => {
        if (!subcategoryMap.has(sub.id)) {
          subcategoryMap.set(sub.id, []);
        }
        subcategoryMap.get(sub.id)!.push(category.id);
      });
    }
  });

  const duplicateIds = new Set<string>();
  subcategoryMap.forEach((categories, subcategoryId) => {
    if (categories.length > 1) {
      duplicateIds.add(subcategoryId);
    }
  });

  console.log(`Found ${duplicateIds.size} duplicate subcategory IDs that need fixing...`);

  let sql = `-- =====================================================
-- AROUZ MARKET - Complete Categories & Subcategories Migration (FIXED)
-- Generated: ${new Date().toISOString()}
-- Purpose: Sync Supabase with localStorage data (1000000000% accuracy)
-- Fixed: All subcategory IDs are now unique with category prefixes for duplicates
-- =====================================================

-- Clear existing data to start fresh
DELETE FROM subcategories;
DELETE FROM categories;

-- Insert ALL categories (10 total)
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES\n`;

  // Generate categories
  const categoryValues = CATEGORIES.map((cat, index) => {
    const idPrefix = cat.id === 'tyres' ? 'TYR' : 'PROD';
    return `  ('${cat.id}', '${cat.name.replace(/'/g, "''")}', '${cat.displayName.replace(/'/g, "''")}', '${cat.description?.replace(/'/g, "''") || ''}', '${idPrefix}', ${index})`;
  }).join(',\n');

  sql += categoryValues + ';\n\n';

  // Generate subcategories with unique IDs
  sql += '-- Insert ALL subcategories (459 total) - with unique IDs for duplicates\n';
  sql += 'INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES\n';

  const allSubcategories: string[] = [];
  
  CATEGORIES.forEach(category => {
    if (category.subcategories && category.subcategories.length > 0) {
      category.subcategories.forEach((sub, index) => {
        // Make ID unique if it's a duplicate
        let uniqueId = sub.id;
        if (duplicateIds.has(sub.id)) {
          uniqueId = `${category.id}-${sub.id}`;
        }
        
        const value = `  ('${uniqueId}', '${sub.name.replace(/'/g, "''")}', '${sub.displayName.replace(/'/g, "''")}', '${sub.categoryId}', ${index + 1})`;
        allSubcategories.push(value);
      });
    }
  });

  sql += allSubcategories.join(',\n') + ';\n\n';

  sql += `-- Verification queries
SELECT 'Migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT category_id, COUNT(*) as subcategory_count FROM subcategories GROUP BY category_id ORDER BY category_id;

-- Show sample of fixed duplicate IDs
SELECT id, name, category_id FROM subcategories WHERE id LIKE '%-%' ORDER BY category_id, id LIMIT 20;`;

  return sql;
}

// Generate the fixed migration
const migrationSQL = generateFixedMigrationSQL();

// Write to file
const outputPath = path.join(process.cwd(), 'supabase/migrations/20241220120011_complete_categories_migration_fixed.sql');
fs.writeFileSync(outputPath, migrationSQL, 'utf8');

console.log('✅ Fixed migration SQL generated successfully!');
console.log(`📁 File saved to: ${outputPath}`);
console.log(`📊 Total categories: ${CATEGORIES.length}`);
console.log(`📊 Total subcategories: ${CATEGORIES.reduce((total, cat) => total + (cat.subcategories?.length || 0), 0)}`);
