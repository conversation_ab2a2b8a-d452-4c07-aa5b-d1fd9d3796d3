/**
 * Find Duplicate Subcategory IDs in localStorage Data
 * This script identifies all duplicate subcategory IDs across categories
 */

import { CATEGORIES } from '../src/data/categoryData.js';

function findDuplicateSubcategories() {
  const subcategoryMap = new Map<string, string[]>();
  
  // Collect all subcategories with their categories
  CATEGORIES.forEach(category => {
    if (category.subcategories && category.subcategories.length > 0) {
      category.subcategories.forEach(sub => {
        if (!subcategoryMap.has(sub.id)) {
          subcategoryMap.set(sub.id, []);
        }
        subcategoryMap.get(sub.id)!.push(category.id);
      });
    }
  });

  // Find duplicates
  const duplicates: { id: string; categories: string[] }[] = [];
  
  subcategoryMap.forEach((categories, subcategoryId) => {
    if (categories.length > 1) {
      duplicates.push({ id: subcategoryId, categories });
    }
  });

  console.log('🔍 DUPLICATE SUBCATEGORY IDs FOUND:');
  console.log('=====================================');
  
  if (duplicates.length === 0) {
    console.log('✅ No duplicates found!');
  } else {
    duplicates.forEach(dup => {
      console.log(`❌ "${dup.id}" appears in categories: ${dup.categories.join(', ')}`);
    });
    
    console.log(`\n📊 Total duplicates: ${duplicates.length}`);
  }

  return duplicates;
}

// Run the check
findDuplicateSubcategories();
