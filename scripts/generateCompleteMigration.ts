/**
 * Generate Complete Migration SQL from localStorage Category Data
 * This script generates the complete SQL migration to sync Supabase with localStorage
 */

import { CATEGORIES } from '../src/data/categoryData.js';
import fs from 'fs';
import path from 'path';

function generateCompleteMigrationSQL(): string {
  let sql = `-- =====================================================
-- AROUZ MARKET - Complete Categories & Subcategories Migration
-- Generated: ${new Date().toISOString()}
-- Purpose: Sync Supabase with localStorage data (1000000000% accuracy)
-- =====================================================

-- Clear existing data to start fresh
DELETE FROM subcategories;
DELETE FROM categories;

-- Insert ALL categories (10 total)
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES\n`;

  // Generate categories
  const categoryValues = CATEGORIES.map((cat, index) => {
    const idPrefix = cat.id === 'tyres' ? 'TYR' : 'PROD';
    return `  ('${cat.id}', '${cat.name.replace(/'/g, "''")}', '${cat.displayName.replace(/'/g, "''")}', '${cat.description?.replace(/'/g, "''") || ''}', '${idPrefix}', ${index})`;
  }).join(',\n');

  sql += categoryValues + ';\n\n';

  // Generate subcategories
  sql += '-- Insert ALL subcategories (463 total)\n';
  sql += 'INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES\n';

  const allSubcategories: string[] = [];
  
  CATEGORIES.forEach(category => {
    if (category.subcategories && category.subcategories.length > 0) {
      category.subcategories.forEach((sub, index) => {
        const value = `  ('${sub.id}', '${sub.name.replace(/'/g, "''")}', '${sub.displayName.replace(/'/g, "''")}', '${sub.categoryId}', ${index + 1})`;
        allSubcategories.push(value);
      });
    }
  });

  sql += allSubcategories.join(',\n') + ';\n\n';

  sql += `-- Verification queries
SELECT 'Migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT category_id, COUNT(*) as subcategory_count FROM subcategories GROUP BY category_id ORDER BY category_id;`;

  return sql;
}

// Generate the migration
const migrationSQL = generateCompleteMigrationSQL();

// Write to file
const outputPath = path.join(process.cwd(), 'supabase/migrations/20241220120010_complete_categories_migration.sql');
fs.writeFileSync(outputPath, migrationSQL, 'utf8');

console.log('✅ Complete migration SQL generated successfully!');
console.log(`📁 File saved to: ${outputPath}`);
console.log(`📊 Total categories: ${CATEGORIES.length}`);
console.log(`📊 Total subcategories: ${CATEGORIES.reduce((total, cat) => total + (cat.subcategories?.length || 0), 0)}`);
