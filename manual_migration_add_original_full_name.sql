-- Manual Migration: Add original_full_name column to profiles table
-- Run this in Supabase SQL Editor if the migration file fails

-- Step 1: Add original_full_name column if it doesn't exist
DO $$
BEGIN
  -- Check if original_full_name column exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'profiles'
    AND column_name = 'original_full_name'
  ) THEN
    ALTER TABLE profiles ADD COLUMN original_full_name TEXT;
    RAISE NOTICE 'Added original_full_name column to profiles table';
  ELSE
    RAISE NOTICE 'original_full_name column already exists';
  END IF;
END $$;

-- Step 2: Add comments to explain the privacy protection system
COMMENT ON COLUMN profiles.full_name IS 'Privacy-protected name visible to suppliers/merchants/shipping (e.g., "Yousef P")';
COMMENT ON COLUMN profiles.original_full_name IS 'Original full name for internal use only (e.g., "Yousef Palorino")';

-- Step 3: Create index for better performance on name searches
CREATE INDEX IF NOT EXISTS idx_profiles_original_full_name ON profiles(original_full_name);
CREATE INDEX IF NOT EXISTS idx_profiles_full_name ON profiles(full_name);

-- Step 4: Function to convert full name to privacy-protected format
CREATE OR REPLACE FUNCTION convert_to_privacy_protected_name(full_name_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
  name_parts TEXT[];
  first_name TEXT;
  last_initial TEXT;
BEGIN
  -- Handle null or empty input
  IF full_name_input IS NULL OR TRIM(full_name_input) = '' THEN
    RETURN 'Consumer';
  END IF;

  -- Split name into parts
  name_parts := string_to_array(TRIM(full_name_input), ' ');

  -- If only one name part, return as is
  IF array_length(name_parts, 1) = 1 THEN
    RETURN name_parts[1];
  END IF;

  -- Get first name and last initial
  first_name := name_parts[1];
  last_initial := UPPER(SUBSTRING(name_parts[array_length(name_parts, 1)] FROM 1 FOR 1));

  -- Return privacy-protected format
  RETURN first_name || ' ' || last_initial;
END;
$$;

-- Step 5: Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION convert_to_privacy_protected_name(TEXT) TO authenticated;

-- Step 6: Create trigger to automatically handle new consumer registrations
CREATE OR REPLACE FUNCTION handle_consumer_privacy_protection()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process consumer role updates
  IF NEW.role = 'consumer' AND NEW.full_name IS NOT NULL AND NEW.full_name != '' THEN
    -- If original_full_name is not set, this is a new registration
    IF NEW.original_full_name IS NULL OR NEW.original_full_name = '' THEN
      -- Store original name and convert full_name to privacy-protected format
      NEW.original_full_name := NEW.full_name;
      NEW.full_name := convert_to_privacy_protected_name(NEW.full_name);
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new consumer registrations
DROP TRIGGER IF EXISTS trigger_consumer_privacy_protection ON profiles;
CREATE TRIGGER trigger_consumer_privacy_protection
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION handle_consumer_privacy_protection();

-- Step 7: Update existing consumer profiles to have privacy-protected names
DO $$
DECLARE
  consumer_record RECORD;
  protected_name TEXT;
BEGIN
  -- Process all consumer profiles that have full_name but no original_full_name
  FOR consumer_record IN
    SELECT id, full_name
    FROM profiles
    WHERE role = 'consumer'
    AND full_name IS NOT NULL
    AND full_name != ''
    AND (original_full_name IS NULL OR original_full_name = '')
  LOOP
    -- Convert to privacy-protected format
    protected_name := convert_to_privacy_protected_name(consumer_record.full_name);

    -- Update the profile
    UPDATE profiles
    SET
      original_full_name = consumer_record.full_name,  -- Store original
      full_name = protected_name,                      -- Store privacy-protected
      updated_at = NOW()
    WHERE id = consumer_record.id;

    RAISE NOTICE 'Updated consumer profile: % -> %', consumer_record.full_name, protected_name;
  END LOOP;
END $$;

-- Step 8: Verification query
SELECT 
  'Consumer Profiles with Privacy Protection' as check_type,
  COUNT(*) as total_consumers,
  COUNT(CASE WHEN original_full_name IS NOT NULL THEN 1 END) as with_original_name
FROM profiles 
WHERE role = 'consumer';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Consumer Privacy Protection System installed successfully!';
  RAISE NOTICE 'Privacy-protected names will be visible to suppliers/merchants/shipping companies';
  RAISE NOTICE 'Original full names are stored securely for internal use only';
END $$;
