-- Direct SQL to fix profile creation RLS issues
-- Run this directly in Supabase SQL Editor

-- 1. Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can do anything" ON profiles;
DROP POLICY IF EXISTS "Postgres role can do anything" ON profiles;
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_insert_own_profile" ON profiles;

-- 2. Create comprehensive RLS policies

-- Allow service role to do anything (needed for triggers and admin operations)
CREATE POLICY "service_role_all_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Allow authenticated users to view their own profile
CREATE POLICY "users_select_own_profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Allow authenticated users to update their own profile
CREATE POLICY "users_update_own_profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- CRITICAL: Allow authenticated users to insert their own profile
CREATE POLICY "users_insert_own_profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 3. Ensure the handle_new_user function is properly configured
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Extract role from the user metadata
  user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');
  
  -- Insert the new profile with comprehensive data from metadata
  INSERT INTO public.profiles (
    id,
    email,
    phone,
    role,
    full_name,
    first_name,
    last_name,
    company_name,
    store_name,
    store_address,
    email_verified,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.email,
    -- Format phone number if it exists
    CASE
      WHEN new.phone IS NOT NULL AND new.phone != '' THEN
        CASE
          WHEN new.phone LIKE '+213%' THEN new.phone
          WHEN new.phone LIKE '0%' THEN '+213' || SUBSTRING(new.phone FROM 2)
          ELSE '+213' || new.phone
        END
      ELSE NULL
    END,
    user_role,
    COALESCE(new.raw_user_meta_data->>'fullName', ''),
    COALESCE(new.raw_user_meta_data->>'firstName', ''),
    COALESCE(new.raw_user_meta_data->>'lastName', ''),
    CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
    false,
    now(),
    now()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    full_name = EXCLUDED.full_name,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    company_name = EXCLUDED.company_name,
    store_name = EXCLUDED.store_name,
    store_address = EXCLUDED.store_address,
    updated_at = now();

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 5. Grant necessary permissions
GRANT ALL ON profiles TO postgres;
GRANT ALL ON profiles TO service_role;
GRANT SELECT, UPDATE, INSERT ON profiles TO authenticated;
GRANT SELECT ON profiles TO anon;
