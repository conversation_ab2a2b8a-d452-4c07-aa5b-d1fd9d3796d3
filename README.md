# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/6c5a3a86-cf38-4249-b9e5-f1d5007c8d36

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/6c5a3a86-cf38-4249-b9e5-f1d5007c8d36) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with .

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

### Deploying to arouzmarket.com

This project includes a comprehensive deployment guide for deploying to arouzmarket.com while keeping product features hidden during development.

Please refer to the `DEPLOYMENT_GUIDE.md` file for detailed instructions on how to deploy the public website to your domain.

### Quick Deployment

For a quick deployment, you can run the included deployment script:

```sh
chmod +x deploy.sh
./deploy.sh
```

This will create a production build in the `dist` directory that you can upload to your hosting provider.

### Deployment Platforms

This project includes configuration files for easy deployment to:

- **Netlify**: Use the `netlify.toml` configuration file
- **Vercel**: Use the `vercel.json` configuration file
- **Traditional hosting**: Upload the contents of the `dist` directory to your web hosting provider
