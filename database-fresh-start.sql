-- 🗄️ AROUZ MARKET Database Fresh Start
-- This script clears all user data while preserving table structures
-- Use this to start with fresh data for production deployment

-- ⚠️ WARNING: This will delete ALL user data!
-- Only run this if you want to start with completely fresh data

BEGIN;

-- Clear user data tables (preserve structure)
TRUNCATE TABLE IF EXISTS profiles CASCADE;
TRUNCATE TABLE IF EXISTS products CASCADE;
TRUNCATE TABLE IF EXISTS orders CASCADE;
TRUNCATE TABLE IF EXISTS order_items CASCADE;
TRUNCATE TABLE IF EXISTS reviews CASCADE;
TRUNCATE TABLE IF EXISTS favorites CASCADE;
TRUNCATE TABLE IF EXISTS cart_items CASCADE;
TRUNCATE TABLE IF EXISTS shipping_addresses CASCADE;
TRUNCATE TABLE IF EXISTS phone_verifications CASCADE;
TRUNCATE TABLE IF EXISTS audit_logs CASCADE;

-- Clear authentication data
DELETE FROM auth.users WHERE email NOT LIKE '%@arouz%';

-- Reset sequences (optional - for clean IDs)
-- ALTER SEQUENCE IF EXISTS products_id_seq RESTART WITH 1;
-- ALTER SEQUENCE IF EXISTS orders_id_seq RESTART WITH 1;

-- Keep system/reference data intact:
-- - categories (product categories)
-- - shipping_companies (shipping providers)
-- - vehicle_data (car models, etc.)
-- - location data (wilayas, communes, etc.)

COMMIT;

-- Verification queries
SELECT 'profiles' as table_name, COUNT(*) as record_count FROM profiles
UNION ALL
SELECT 'products', COUNT(*) FROM products
UNION ALL
SELECT 'orders', COUNT(*) FROM orders
UNION ALL
SELECT 'auth.users', COUNT(*) FROM auth.users;

-- Success message
SELECT '✅ Database cleaned successfully! Ready for fresh production data.' as status;
