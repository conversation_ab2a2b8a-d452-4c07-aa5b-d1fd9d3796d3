import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Dacia engine updates file
const daciaEnginesUpdatesPath = path.join(__dirname, 'src', 'services', 'data', 'dacia-engines-updates.json');
const daciaEnginesUpdates = JSON.parse(fs.readFileSync(daciaEnginesUpdatesPath, 'utf8'));

// Find the Dacia brand in the cars.json file
const daciaBrandIndex = carsJson.findIndex(brand => brand.brand === 'Dacia');

if (daciaBrandIndex !== -1) {
  // Create a map of models and generations for easier lookup and updating
  const modelMap = {};
  
  daciaEnginesUpdates.forEach(model => {
    modelMap[model.model] = {};
    model.generations.forEach(gen => {
      modelMap[model.model][gen.code] = gen;
    });
  });

  // Update each model in the Dacia brand
  carsJson[daciaBrandIndex].models.forEach((model, modelIndex) => {
    if (modelMap[model.model]) {
      model.generations.forEach((gen, genIndex) => {
        if (modelMap[model.model][gen.code]) {
          // Update the engines for this generation
          carsJson[daciaBrandIndex].models[modelIndex].generations[genIndex].engines = 
            modelMap[model.model][gen.code].engines;
        }
      });
    }
  });

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Updated engine data for Dacia models in the cars.json file.`);
} else {
  console.error('Dacia brand not found in the cars.json file.');
}

// Clean up the temporary file
fs.unlinkSync(daciaEnginesUpdatesPath);

console.log('Temporary file cleaned up.');
