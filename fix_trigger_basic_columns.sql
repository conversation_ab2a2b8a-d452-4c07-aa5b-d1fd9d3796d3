-- Fix the trigger function to use all available columns from the profiles table
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Extract role from the user metadata
  user_role := COALESCE(new.raw_user_meta_data->>'role', 'consumer');

  -- Insert the new profile with all available columns
  INSERT INTO public.profiles (
    id,
    email,
    phone,
    role,
    full_name,
    first_name,
    last_name,
    company_name,
    store_name,
    store_address,
    email_verified,
    phone_verified,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    new.email,
    -- Format phone number if it exists
    CASE
      WHEN new.phone IS NOT NULL AND new.phone != '' THEN
        CASE
          WHEN new.phone LIKE '+213%' THEN new.phone
          WHEN new.phone LIKE '0%' THEN '+213' || SUBSTRING(new.phone FROM 2)
          ELSE '+213' || new.phone
        END
      ELSE NULL
    END,
    user_role,
    COALESCE(new.raw_user_meta_data->>'fullName', ''),
    COALESCE(new.raw_user_meta_data->>'firstName', ''),
    COALESCE(new.raw_user_meta_data->>'lastName', ''),
    CASE WHEN user_role = 'supplier' THEN new.raw_user_meta_data->>'companyName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeName' ELSE NULL END,
    CASE WHEN user_role = 'merchant' THEN new.raw_user_meta_data->>'storeAddress' ELSE NULL END,
    false,
    false,
    now(),
    now()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role,
    full_name = EXCLUDED.full_name,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    company_name = EXCLUDED.company_name,
    store_name = EXCLUDED.store_name,
    store_address = EXCLUDED.store_address,
    updated_at = now();

  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
