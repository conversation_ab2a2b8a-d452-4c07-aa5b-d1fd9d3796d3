#!/bin/bash

# Production deployment script for AROUZ MARKET public website

echo "Starting deployment process for arouzmarket.com..."

# Step 1: Install dependencies
echo "Installing dependencies..."
npm install

# Step 2: Build the production version
echo "Building production version..."
npm run build

# Step 3: Output success message with next steps
echo "✅ Build completed successfully!"
echo "The production build is now available in the 'dist' directory."
echo ""
echo "Next steps for deployment to arouzmarket.com:"
echo "1. Upload the contents of the 'dist' directory to your web hosting provider"
echo "2. Configure your domain DNS settings to point to your hosting provider"
echo "3. Set up HTTPS for secure connections"
echo ""
echo "For more detailed instructions, refer to the deployment guide."
