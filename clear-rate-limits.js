// Clear all rate limiting data from localStorage
console.log('🧹 Clearing all rate limiting data...');

// Clear login attempts
localStorage.removeItem('loginAttempts');
localStorage.removeItem('lastLoginAttempt');

// Clear rate limit records
const keys = Object.keys(localStorage);
keys.forEach(key => {
  if (key.includes('rateLimit') || 
      key.includes('LOGIN_IP') || 
      key.includes('LOGIN_USER') || 
      key.includes('API_IP') || 
      key.includes('ADMIN_ACTION') ||
      key.includes('attempts') ||
      key.includes('blocked')) {
    localStorage.removeItem(key);
    console.log('Removed:', key);
  }
});

console.log('✅ All rate limiting data cleared!');
console.log('🚀 You can now make unlimited login attempts!');
