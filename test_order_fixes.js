// Test script to verify order confirmation fixes
// Run this in the browser console on the checkout page

console.log('🧪 TESTING ORDER CONFIRMATION FIXES');

// Test 1: Check if shipping origins match order summary
function testShippingOriginsMatch() {
  console.log('\n1️⃣ TESTING SHIPPING ORIGINS MATCH:');
  
  // Get cart items from localStorage
  const cartData = localStorage.getItem('arouz_cart');
  if (!cartData) {
    console.log('❌ No cart items found');
    return false;
  }
  
  const cartItems = JSON.parse(cartData);
  console.log('📦 Cart items count:', cartItems.length);
  
  // Check if shipping origins section shows same items as order summary
  const shippingOriginItems = document.querySelectorAll('[data-testid="shipping-origin-item"]');
  const orderSummaryItems = document.querySelectorAll('[data-testid="order-summary-item"]');
  
  console.log('🏢 Shipping origins items found:', shippingOriginItems.length);
  console.log('📋 Order summary items found:', orderSummaryItems.length);
  
  if (shippingOriginItems.length === orderSummaryItems.length && shippingOriginItems.length === cartItems.length) {
    console.log('✅ Shipping origins match order summary!');
    return true;
  } else {
    console.log('❌ Shipping origins do NOT match order summary');
    console.log('Expected:', cartItems.length, 'Got shipping origins:', shippingOriginItems.length, 'Got order summary:', orderSummaryItems.length);
    return false;
  }
}

// Test 2: Check if order success page has all sections
function testOrderSuccessPageSections() {
  console.log('\n2️⃣ TESTING ORDER SUCCESS PAGE SECTIONS:');
  
  // This test should be run on the order success page
  const currentUrl = window.location.href;
  if (!currentUrl.includes('order-success')) {
    console.log('⚠️ This test should be run on the order success page');
    return false;
  }
  
  const requiredSections = [
    'Order Details',
    'Customer Information', 
    'Delivery Information',
    'Shipping Origins',
    'Order Items',
    'What Happens Next?'
  ];
  
  let foundSections = 0;
  requiredSections.forEach(section => {
    const sectionElement = document.querySelector(`h3:contains("${section}"), h2:contains("${section}")`);
    if (sectionElement) {
      console.log('✅ Found section:', section);
      foundSections++;
    } else {
      console.log('❌ Missing section:', section);
    }
  });
  
  if (foundSections === requiredSections.length) {
    console.log('✅ All required sections found on order success page!');
    return true;
  } else {
    console.log('❌ Missing sections on order success page');
    return false;
  }
}

// Test 3: Check translation keys
function testTranslationKeys() {
  console.log('\n3️⃣ TESTING TRANSLATION KEYS:');
  
  const requiredKeys = [
    'orderSuccess.customerInfo',
    'orderSuccess.name', 
    'orderSuccess.phone',
    'orderSuccess.shippingOrigins'
  ];
  
  // Check if i18n is available
  if (typeof window.i18n === 'undefined') {
    console.log('⚠️ i18n not available in global scope');
    return false;
  }
  
  let foundKeys = 0;
  requiredKeys.forEach(key => {
    const translation = window.i18n.t(key);
    if (translation && translation !== key) {
      console.log('✅ Found translation for:', key, '=', translation);
      foundKeys++;
    } else {
      console.log('❌ Missing translation for:', key);
    }
  });
  
  if (foundKeys === requiredKeys.length) {
    console.log('✅ All translation keys found!');
    return true;
  } else {
    console.log('❌ Missing translation keys');
    return false;
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 RUNNING ALL ORDER CONFIRMATION TESTS\n');
  
  const test1 = testShippingOriginsMatch();
  const test2 = testOrderSuccessPageSections();
  const test3 = testTranslationKeys();
  
  console.log('\n📊 TEST RESULTS:');
  console.log('Shipping Origins Match:', test1 ? '✅ PASS' : '❌ FAIL');
  console.log('Order Success Sections:', test2 ? '✅ PASS' : '❌ FAIL');
  console.log('Translation Keys:', test3 ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = test1 && test2 && test3;
  console.log('\n🎯 OVERALL RESULT:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  return allPassed;
}

// Export functions for manual testing
window.testOrderFixes = {
  runAllTests,
  testShippingOriginsMatch,
  testOrderSuccessPageSections,
  testTranslationKeys
};

console.log('✅ Test functions loaded! Run window.testOrderFixes.runAllTests() to test all fixes.');
