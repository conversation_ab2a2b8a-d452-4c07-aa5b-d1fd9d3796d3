import json
import sys

try:
    with open('src/services/data/cars.json', 'r') as f:
        content = f.read()
    
    # Try to parse the JSON
    try:
        json.loads(content)
        print("JSON is already valid")
    except json.JSONDecodeError as e:
        print(f"JSON is invalid: {e}")
        
        # Get the problematic line
        lines = content.split('\n')
        line_num = e.lineno - 1
        col_num = e.colno - 1
        
        # Print the problematic line and the surrounding lines
        start_line = max(0, line_num - 2)
        end_line = min(len(lines), line_num + 3)
        
        print(f"\nProblematic area (lines {start_line+1}-{end_line}):")
        for i in range(start_line, end_line):
            prefix = ">>> " if i == line_num else "    "
            print(f"{prefix}{i+1}: {lines[i]}")
        
        # Fix the specific issue at line 20783
        if line_num == 20782:  # 0-based index for line 20783
            # Replace the problematic line
            lines[line_num] = '              ],'
            
            # Write the fixed content back to the file
            with open('src/services/data/cars.json', 'w') as f:
                f.write('\n'.join(lines))
            
            print("\nFixed the JSON file")
        else:
            print("\nCouldn't fix the JSON file automatically")
        
except Exception as e:
    print(f"Error: {e}")
