// Quick verification script to confirm production database connection
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyConnection() {
  try {
    console.log('🔍 Verifying connection to production database...');
    console.log('📍 Database URL:', supabaseUrl);
    
    // Test basic connection
    const { data: tables, error: tablesError } = await supabase
      .from('profiles')
      .select('count', { count: 'exact', head: true });
    
    if (tablesError) {
      console.error('❌ Connection failed:', tablesError.message);
      return false;
    }
    
    console.log('✅ Successfully connected to production database!');
    console.log(`📊 Total profiles in production: ${tables?.length || 'Unknown'}`);
    
    // Test products table
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('count', { count: 'exact', head: true });
    
    if (!productsError) {
      console.log(`📦 Total products in production: ${products?.length || 'Unknown'}`);
    }
    
    // Test orders table
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('count', { count: 'exact', head: true });
    
    if (!ordersError) {
      console.log(`🛒 Total orders in production: ${orders?.length || 'Unknown'}`);
    }
    
    console.log('🎉 Production database connection verified successfully!');
    console.log('🚀 You can now test signup with real production data');
    
    return true;
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

verifyConnection();
