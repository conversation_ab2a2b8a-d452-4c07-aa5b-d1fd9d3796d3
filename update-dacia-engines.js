import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Dacia engine files
const daciaEngines1Path = path.join(__dirname, 'src', 'services', 'data', 'dacia-engines-1.json');
const daciaEngines2Path = path.join(__dirname, 'src', 'services', 'data', 'dacia-engines-2.json');
const daciaEngines3Path = path.join(__dirname, 'src', 'services', 'data', 'dacia-engines-3.json');

const daciaEngines1 = JSON.parse(fs.readFileSync(daciaEngines1Path, 'utf8'));
const daciaEngines2 = JSON.parse(fs.readFileSync(daciaEngines2Path, 'utf8'));
const daciaEngines3 = JSON.parse(fs.readFileSync(daciaEngines3Path, 'utf8'));

// Combine all Dacia engines
const allDaciaEngines = [
  ...daciaEngines1,
  ...daciaEngines2,
  ...daciaEngines3
];

// Find the Dacia brand in the cars.json file
const daciaBrandIndex = carsJson.findIndex(brand => brand.brand === 'Dacia');

if (daciaBrandIndex !== -1) {
  // Create a map of models for easier lookup
  const modelMap = {};
  allDaciaEngines.forEach(model => {
    modelMap[model.model] = model;
  });

  // Update each model in the Dacia brand
  carsJson[daciaBrandIndex].models.forEach((model, index) => {
    if (modelMap[model.model]) {
      carsJson[daciaBrandIndex].models[index] = modelMap[model.model];
    }
  });

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Updated engine data for ${allDaciaEngines.length} Dacia models in the cars.json file.`);
} else {
  console.error('Dacia brand not found in the cars.json file.');
}

// Clean up the temporary files
fs.unlinkSync(daciaEngines1Path);
fs.unlinkSync(daciaEngines2Path);
fs.unlinkSync(daciaEngines3Path);

console.log('Temporary files cleaned up.');
