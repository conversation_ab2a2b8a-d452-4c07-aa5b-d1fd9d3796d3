import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Chery models file
const cheryModelsPath = path.join(__dirname, 'src', 'services', 'data', 'chery-models.json');
const cheryModels = JSON.parse(fs.readFileSync(cheryModelsPath, 'utf8'));

// Find the Chery brand in the cars.json file
const cheryBrandIndex = carsJson.findIndex(brand => brand.brand === 'Chery');

if (cheryBrandIndex !== -1) {
  // Update the Chery models
  carsJson[cheryBrandIndex].models = cheryModels;

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Added ${cheryModels.length} Chery models to the cars.json file.`);
} else {
  console.error('Chery brand not found in the cars.json file.');
}

// Clean up the temporary file
fs.unlinkSync(cheryModelsPath);

console.log('Temporary file cleaned up.');
