name: 🔄 Emergency Rollback

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
        - dev
        - prod
      rollback_to:
        description: 'Git commit SHA or tag to rollback to'
        required: true
        type: string
      reason:
        description: 'Reason for rollback'
        required: true
        type: string

env:
  NODE_VERSION: '18'

jobs:
  validate-rollback:
    name: 🔍 Validate Rollback Request
    runs-on: ubuntu-latest
    timeout-minutes: 2
    outputs:
      is_valid: ${{ steps.validate.outputs.is_valid }}
      commit_exists: ${{ steps.validate.outputs.commit_exists }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔍 Validate commit exists
        id: validate
        run: |
          if git cat-file -e ${{ inputs.rollback_to }}^{commit}; then
            echo "is_valid=true" >> $GITHUB_OUTPUT
            echo "commit_exists=true" >> $GITHUB_OUTPUT
            echo "✅ Commit ${{ inputs.rollback_to }} exists"
          else
            echo "is_valid=false" >> $GITHUB_OUTPUT
            echo "commit_exists=false" >> $GITHUB_OUTPUT
            echo "❌ Commit ${{ inputs.rollback_to }} does not exist"
            exit 1
          fi

  rollback-dev:
    name: 🔄 Rollback Development
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: inputs.environment == 'dev' && needs.validate-rollback.outputs.is_valid == 'true'
    needs: [validate-rollback]
    environment:
      name: development
      url: https://hamzaarou.github.io/parts-library-central
    steps:
      - name: 📥 Checkout rollback commit
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.rollback_to }}
          
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: ⚡ Install dependencies
        run: npm ci --prefer-offline --no-audit
        
      - name: 🏗️ Build rollback version
        run: npm run build:dev
        env:
          VITE_APP_ENV: development
          VITE_APP_URL: https://hamzaarou.github.io/parts-library-central
          VITE_ENABLE_PRODUCT_FEATURES: true
          VITE_SUPABASE_URL: ${{ secrets.SUPABASE_DEV_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_DEV_ANON_KEY }}
          VITE_USE_SUPABASE: true
          VITE_GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          VITE_DEXATEL_API_KEY: ${{ secrets.DEXATEL_API_KEY }}
          
      - name: 🚀 Deploy rollback to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
          cname: dev.arouzmarket.com
          commit_message: "🔄 Emergency rollback to ${{ inputs.rollback_to }} - ${{ inputs.reason }}"
          
      - name: 📧 Notify rollback completion
        run: |
          echo "🔄 Development rollback completed!"
          echo "📍 Rolled back to: ${{ inputs.rollback_to }}"
          echo "📝 Reason: ${{ inputs.reason }}"
          echo "🌐 URL: https://hamzaarou.github.io/parts-library-central"

  rollback-prod:
    name: 🔄 Rollback Production
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: inputs.environment == 'prod' && needs.validate-rollback.outputs.is_valid == 'true'
    needs: [validate-rollback]
    environment:
      name: production
      url: https://arouzmarket.com
    steps:
      - name: 📥 Checkout rollback commit
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.rollback_to }}
          
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: ⚡ Install dependencies
        run: npm ci --prefer-offline --no-audit
        
      - name: 🏗️ Build rollback version
        run: npm run build:prod
        env:
          VITE_APP_ENV: production
          VITE_APP_URL: https://arouzmarket.com
          VITE_ENABLE_PRODUCT_FEATURES: false
          VITE_SUPABASE_URL: ${{ secrets.SUPABASE_PROD_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_PROD_ANON_KEY }}
          VITE_USE_SUPABASE: true
          VITE_GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          VITE_DEXATEL_API_KEY: ${{ secrets.DEXATEL_API_KEY }}
          
      - name: 🚀 Deploy rollback to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./dist
          vercel-args: '--prod'
          
      - name: 📧 Notify rollback completion
        run: |
          echo "🔄 Production rollback completed!"
          echo "📍 Rolled back to: ${{ inputs.rollback_to }}"
          echo "📝 Reason: ${{ inputs.reason }}"
          echo "🌐 URL: https://arouzmarket.com"

  post-rollback-verification:
    name: ✅ Verify Rollback
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: always()
    needs: [rollback-dev, rollback-prod]
    steps:
      - name: 🔍 Health check after rollback
        run: |
          if [ "${{ inputs.environment }}" == "dev" ]; then
            curl -f https://hamzaarou.github.io/parts-library-central || exit 1
            echo "✅ Development rollback verified"
          elif [ "${{ inputs.environment }}" == "prod" ]; then
            curl -f https://arouzmarket.com || exit 1
            echo "✅ Production rollback verified"
          fi
          
      - name: 📝 Create rollback issue
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🔄 Emergency Rollback - ${{ inputs.environment }}`,
              body: `## Emergency Rollback Executed
              
              **Environment:** ${{ inputs.environment }}
              **Rolled back to:** ${{ inputs.rollback_to }}
              **Reason:** ${{ inputs.reason }}
              **Executed by:** @${{ github.actor }}
              **Timestamp:** ${{ github.event.head_commit.timestamp }}
              
              ## Next Steps
              - [ ] Investigate the issue that caused the rollback
              - [ ] Fix the problem in a new branch
              - [ ] Test thoroughly before redeployment
              - [ ] Update this issue with resolution details
              
              ## Verification
              - [ ] Site is accessible and functional
              - [ ] Critical features are working
              - [ ] No console errors
              `,
              labels: ['rollback', 'urgent', '${{ inputs.environment }}']
            })
