# 🔐 GitHub Secrets Configuration Template

## Required Secrets for CI/CD Pipeline

To enable the full CI/CD pipeline, you need to configure the following secrets in your GitHub repository:

**Go to: Repository Settings → Secrets and variables → Actions → New repository secret**

### 🗄️ Supabase Secrets

#### Single Supabase Project (Free Tier Optimized)
```
SUPABASE_ANON_KEY=your-supabase-anon-key
```

**Note**: We use the same Supabase project (irkwpzcskeqtasutqnxp.supabase.co) for both dev and prod to stay within free tier limits. Data isolation is handled via RLS policies.

### 🌐 External Services
```
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
DEXATEL_API_KEY=your-dexatel-api-key
```

### 🚀 Deployment Secrets

#### Octenium Hosting (for Production)
```
OCTENIUM_FTP_HOST=ftp.arouzmarket.com
OCTENIUM_FTP_USERNAME=your-octenium-username
OCTENIUM_FTP_PASSWORD=your-octenium-password
```

#### GitHub Pages (for Development)
```
# GITHUB_TOKEN is automatically provided by GitHub Actions
# No additional configuration needed for GitHub Pages
```

### 📧 Notification Secrets (Optional)
```
SLACK_WEBHOOK_URL=your-slack-webhook-url
DISCORD_WEBHOOK_URL=your-discord-webhook-url
```

## 🔧 How to Get These Values

### Supabase
1. Go to your Supabase dashboard (https://app.supabase.com)
2. Select your project: irkwpzcskeqtasutqnxp
3. Go to Settings → API
4. Copy the anon/public key

### Octenium Hosting
1. Log into your Octenium control panel (panel.octenium.com)
2. Go to File Manager or FTP Accounts
3. Find your FTP credentials:
   - Host: Usually ftp.arouzmarket.com or ftp.octenium.com
   - Username: Your hosting username
   - Password: Your hosting password

### Google Maps
1. Go to Google Cloud Console
2. Enable Maps JavaScript API
3. Create credentials → API Key

### Dexatel
1. Go to Dexatel dashboard
2. Go to API section
3. Copy your API key

## 🚨 Security Best Practices

1. **Never commit secrets to your repository**
2. **Use different keys for dev/prod environments**
3. **Regularly rotate your API keys**
4. **Monitor secret usage in GitHub Actions logs**
5. **Use environment-specific Supabase projects**

## ✅ Verification

After setting up secrets, you can verify them by:
1. Triggering a workflow run
2. Checking the Actions tab for any secret-related errors
3. Ensuring deployments complete successfully
