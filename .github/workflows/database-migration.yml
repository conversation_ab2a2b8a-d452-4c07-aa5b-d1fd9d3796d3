name: 🗄️ Database Migration Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'supabase/migrations/**'
      - 'supabase/seed.sql'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to migrate'
        required: true
        type: choice
        options:
        - dev
        - prod
        - both
      migration_type:
        description: 'Type of migration'
        required: true
        type: choice
        options:
        - schema
        - data
        - functions
        - all
      dry_run:
        description: 'Perform dry run (validate only)'
        required: false
        default: false
        type: boolean

env:
  SUPABASE_CLI_VERSION: '1.127.4'

jobs:
  # ============================================================================
  # MIGRATION VALIDATION
  # ============================================================================
  
  validate-migrations:
    name: 🔍 Validate Migrations
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      has_migrations: ${{ steps.check.outputs.has_migrations }}
      migration_files: ${{ steps.check.outputs.migration_files }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: 🔧 Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: ${{ env.SUPABASE_CLI_VERSION }}
          
      - name: 🔍 Check for migration changes
        id: check
        run: |
          if [ -d "supabase/migrations" ] && [ "$(ls -A supabase/migrations)" ]; then
            echo "has_migrations=true" >> $GITHUB_OUTPUT
            echo "migration_files=$(ls supabase/migrations/*.sql | tr '\n' ',' | sed 's/,$//')" >> $GITHUB_OUTPUT
            echo "✅ Found migration files"
          else
            echo "has_migrations=false" >> $GITHUB_OUTPUT
            echo "⚠️ No migration files found"
          fi
          
      - name: 🧪 Validate migration syntax
        if: steps.check.outputs.has_migrations == 'true'
        run: |
          for file in supabase/migrations/*.sql; do
            echo "Validating $file..."
            # Basic SQL syntax validation
            if ! grep -q ";" "$file"; then
              echo "❌ Missing semicolon in $file"
              exit 1
            fi
            echo "✅ $file syntax is valid"
          done
          
      - name: 📊 Migration summary
        if: steps.check.outputs.has_migrations == 'true'
        run: |
          echo "## Migration Summary" >> $GITHUB_STEP_SUMMARY
          echo "| File | Size | Type |" >> $GITHUB_STEP_SUMMARY
          echo "|------|------|------|" >> $GITHUB_STEP_SUMMARY
          for file in supabase/migrations/*.sql; do
            size=$(wc -c < "$file")
            type="Schema"
            if grep -q "INSERT\|UPDATE\|DELETE" "$file"; then
              type="Data"
            fi
            if grep -q "CREATE FUNCTION\|CREATE OR REPLACE FUNCTION" "$file"; then
              type="Function"
            fi
            echo "| $(basename $file) | ${size} bytes | ${type} |" >> $GITHUB_STEP_SUMMARY
          done

  # ============================================================================
  # DEVELOPMENT MIGRATION
  # ============================================================================
  
  migrate-dev:
    name: 🗄️ Migrate Development DB
    runs-on: ubuntu-latest
    timeout-minutes: 15
    if: (github.ref == 'refs/heads/develop' || inputs.environment == 'dev' || inputs.environment == 'both') && needs.validate-migrations.outputs.has_migrations == 'true'
    needs: [validate-migrations]
    environment:
      name: development-db
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: ${{ env.SUPABASE_CLI_VERSION }}
          
      - name: 🔐 Login to Supabase
        run: |
          echo "${{ secrets.SUPABASE_ACCESS_TOKEN }}" | supabase auth login --token
          
      - name: 🔗 Link to development project
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_DEV_PROJECT_ID }}
          
      - name: 🧪 Dry run migration (if requested)
        if: inputs.dry_run == true
        run: |
          echo "🧪 Performing dry run migration..."
          supabase db diff --schema public --use-migra
          
      - name: 🗄️ Run migrations
        if: inputs.dry_run != true
        run: |
          echo "🗄️ Applying migrations to development database..."
          supabase db push --include-all
          
      - name: 🔧 Deploy Edge Functions
        if: inputs.migration_type == 'functions' || inputs.migration_type == 'all'
        run: |
          echo "🔧 Deploying Edge Functions..."
          if [ -d "supabase/functions" ]; then
            supabase functions deploy --no-verify-jwt
          fi
          
      - name: 📊 Migration status
        run: |
          echo "✅ Development database migration completed"
          supabase db remote commit

  # ============================================================================
  # PRODUCTION MIGRATION
  # ============================================================================
  
  migrate-prod:
    name: 🗄️ Migrate Production DB
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: (github.ref == 'refs/heads/main' || inputs.environment == 'prod' || inputs.environment == 'both') && needs.validate-migrations.outputs.has_migrations == 'true'
    needs: [validate-migrations, migrate-dev]
    environment:
      name: production-db
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: ${{ env.SUPABASE_CLI_VERSION }}
          
      - name: 🔐 Login to Supabase
        run: |
          echo "${{ secrets.SUPABASE_ACCESS_TOKEN }}" | supabase auth login --token
          
      - name: 🔗 Link to production project
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_PROD_PROJECT_ID }}
          
      - name: 💾 Backup production database
        run: |
          echo "💾 Creating backup of production database..."
          timestamp=$(date +%Y%m%d_%H%M%S)
          supabase db dump --data-only > "backup_${timestamp}.sql"
          
      - name: 🧪 Dry run migration (if requested)
        if: inputs.dry_run == true
        run: |
          echo "🧪 Performing dry run migration..."
          supabase db diff --schema public --use-migra
          
      - name: 🗄️ Run migrations
        if: inputs.dry_run != true
        run: |
          echo "🗄️ Applying migrations to production database..."
          supabase db push --include-all
          
      - name: 🔧 Deploy Edge Functions
        if: inputs.migration_type == 'functions' || inputs.migration_type == 'all'
        run: |
          echo "🔧 Deploying Edge Functions to production..."
          if [ -d "supabase/functions" ]; then
            supabase functions deploy --no-verify-jwt
          fi
          
      - name: 📊 Migration status
        run: |
          echo "✅ Production database migration completed"
          supabase db remote commit

  # ============================================================================
  # POST-MIGRATION VERIFICATION
  # ============================================================================
  
  verify-migrations:
    name: ✅ Verify Migrations
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: always() && needs.validate-migrations.outputs.has_migrations == 'true'
    needs: [migrate-dev, migrate-prod]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 🔧 Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: ${{ env.SUPABASE_CLI_VERSION }}
          
      - name: 🔐 Login to Supabase
        run: |
          echo "${{ secrets.SUPABASE_ACCESS_TOKEN }}" | supabase auth login --token
          
      - name: 🔍 Verify development database
        if: needs.migrate-dev.result == 'success'
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_DEV_PROJECT_ID }}
          echo "🔍 Verifying development database schema..."
          supabase db diff --schema public
          
      - name: 🔍 Verify production database
        if: needs.migrate-prod.result == 'success'
        run: |
          supabase link --project-ref ${{ secrets.SUPABASE_PROD_PROJECT_ID }}
          echo "🔍 Verifying production database schema..."
          supabase db diff --schema public
          
      - name: 📝 Create migration report
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = `## 🗄️ Database Migration Report
            
            **Trigger:** ${{ github.event_name }}
            **Branch:** ${{ github.ref_name }}
            **Commit:** ${{ github.sha }}
            **Actor:** @${{ github.actor }}
            
            ### Migration Results
            - **Development:** ${{ needs.migrate-dev.result || 'skipped' }}
            - **Production:** ${{ needs.migrate-prod.result || 'skipped' }}
            
            ### Files Processed
            ${{ needs.validate-migrations.outputs.migration_files }}
            
            ### Next Steps
            - [ ] Verify application functionality
            - [ ] Monitor database performance
            - [ ] Update documentation if needed
            `;
            
            console.log(report);
            fs.writeFileSync('migration-report.md', report);
