name: 📊 Monitoring & Health Checks

on:
  # DISABLED: Automatic monitoring to prevent email spam
  # schedule:
  #   # Run every 15 minutes during business hours (9 AM - 6 PM UTC)
  #   - cron: '*/15 9-18 * * 1-5'
  #   # Run every hour outside business hours
  #   - cron: '0 * * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to monitor'
        required: true
        type: choice
        options:
        - dev
        - prod
        - both
      check_type:
        description: 'Type of health check'
        required: true
        type: choice
        options:
        - basic
        - comprehensive
        - performance

env:
  TIMEOUT_SECONDS: 30
  MAX_RESPONSE_TIME: 3000

jobs:
  # ============================================================================
  # BASIC HEALTH CHECKS
  # ============================================================================
  
  health-check-dev:
    name: 🔍 Development Health Check
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: inputs.environment == 'dev' || inputs.environment == 'both' || github.event_name == 'schedule'
    steps:
      - name: 🌐 Check development site availability
        id: dev-check
        run: |
          echo "🔍 Checking development site..."
          start_time=$(date +%s%3N)
          
          if curl -f -s -m ${{ env.TIMEOUT_SECONDS }} https://hamzaarou.github.io/parts-library-central > /dev/null; then
            end_time=$(date +%s%3N)
            response_time=$((end_time - start_time))
            echo "status=healthy" >> $GITHUB_OUTPUT
            echo "response_time=${response_time}" >> $GITHUB_OUTPUT
            echo "✅ Development site is healthy (${response_time}ms)"
          else
            echo "status=unhealthy" >> $GITHUB_OUTPUT
            echo "❌ Development site is not responding"
            exit 1
          fi
          
      - name: 🔍 Check critical pages
        if: steps.dev-check.outputs.status == 'healthy'
        run: |
          pages=("/" "/about" "/contact")
          base_url="https://hamzaarou.github.io/parts-library-central"
          
          for page in "${pages[@]}"; do
            echo "Checking ${base_url}${page}..."
            if curl -f -s -m ${{ env.TIMEOUT_SECONDS }} "${base_url}${page}" > /dev/null; then
              echo "✅ ${page} is accessible"
            else
              echo "❌ ${page} is not accessible"
              exit 1
            fi
          done

  health-check-prod:
    name: 🔍 Production Health Check
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: inputs.environment == 'prod' || inputs.environment == 'both' || github.event_name == 'schedule'
    steps:
      - name: 🌐 Check production site availability
        id: prod-check
        run: |
          echo "🔍 Checking production site..."
          start_time=$(date +%s%3N)
          
          if curl -f -s -m ${{ env.TIMEOUT_SECONDS }} https://arouzmarket.com > /dev/null; then
            end_time=$(date +%s%3N)
            response_time=$((end_time - start_time))
            echo "status=healthy" >> $GITHUB_OUTPUT
            echo "response_time=${response_time}" >> $GITHUB_OUTPUT
            echo "✅ Production site is healthy (${response_time}ms)"
            
            # Check if response time is acceptable
            if [ $response_time -gt ${{ env.MAX_RESPONSE_TIME }} ]; then
              echo "⚠️ Response time is high: ${response_time}ms"
              echo "performance_warning=true" >> $GITHUB_OUTPUT
            fi
          else
            echo "status=unhealthy" >> $GITHUB_OUTPUT
            echo "❌ Production site is not responding"
            exit 1
          fi
          
      - name: 🔍 Check critical pages
        if: steps.prod-check.outputs.status == 'healthy'
        run: |
          pages=("/" "/about" "/contact" "/marketplace")
          base_url="https://arouzmarket.com"
          
          for page in "${pages[@]}"; do
            echo "Checking ${base_url}${page}..."
            if curl -f -s -m ${{ env.TIMEOUT_SECONDS }} "${base_url}${page}" > /dev/null; then
              echo "✅ ${page} is accessible"
            else
              echo "❌ ${page} is not accessible"
              exit 1
            fi
          done
          
      - name: 🔍 Check SSL certificate
        run: |
          echo "🔍 Checking SSL certificate..."
          expiry_date=$(echo | openssl s_client -servername arouzmarket.com -connect arouzmarket.com:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
          expiry_timestamp=$(date -d "$expiry_date" +%s)
          current_timestamp=$(date +%s)
          days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
          
          echo "SSL certificate expires in $days_until_expiry days"
          
          if [ $days_until_expiry -lt 30 ]; then
            echo "⚠️ SSL certificate expires soon: $days_until_expiry days"
            echo "ssl_warning=true" >> $GITHUB_OUTPUT
          else
            echo "✅ SSL certificate is valid"
          fi

  # ============================================================================
  # PERFORMANCE MONITORING
  # ============================================================================
  
  performance-check:
    name: ⚡ Performance Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: inputs.check_type == 'performance' || inputs.check_type == 'comprehensive'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: ⚡ Install Lighthouse CI
        run: npm install -g @lhci/cli
        
      - name: 🔍 Run Lighthouse audit
        run: |
          echo "🔍 Running Lighthouse performance audit..."
          lhci autorun --collect.url=https://arouzmarket.com --collect.numberOfRuns=3
          
      - name: 📊 Upload Lighthouse results
        uses: actions/upload-artifact@v4
        with:
          name: lighthouse-results
          path: .lighthouseci/

  # ============================================================================
  # DATABASE MONITORING
  # ============================================================================
  
  database-check:
    name: 🗄️ Database Health Check
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: inputs.check_type == 'comprehensive' || github.event_name == 'schedule'
    steps:
      - name: 🔍 Check Supabase API health
        run: |
          echo "🔍 Checking Supabase API health..."
          
          # Check development database
          if curl -f -s -m ${{ env.TIMEOUT_SECONDS }} "${{ secrets.SUPABASE_DEV_URL }}/rest/v1/" \
               -H "apikey: ${{ secrets.SUPABASE_DEV_ANON_KEY }}" > /dev/null; then
            echo "✅ Development database is healthy"
          else
            echo "❌ Development database is not responding"
            exit 1
          fi
          
          # Check production database
          if curl -f -s -m ${{ env.TIMEOUT_SECONDS }} "${{ secrets.SUPABASE_PROD_URL }}/rest/v1/" \
               -H "apikey: ${{ secrets.SUPABASE_PROD_ANON_KEY }}" > /dev/null; then
            echo "✅ Production database is healthy"
          else
            echo "❌ Production database is not responding"
            exit 1
          fi

  # ============================================================================
  # ALERTING
  # ============================================================================
  
  send-alerts:
    name: 🚨 Send Alerts
    runs-on: ubuntu-latest
    timeout-minutes: 2
    if: failure()
    needs: [health-check-dev, health-check-prod, performance-check, database-check]
    steps:
      - name: 📧 Create incident issue
        uses: actions/github-script@v7
        with:
          script: |
            const now = new Date().toISOString();
            const runUrl = `https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}`;
            
            const title = `🚨 AROUZ MARKET Health Check Failed - ${now}`;
            const body = `## 🚨 Health Check Incident
            
            **Time:** ${now}
            **Trigger:** ${{ github.event_name }}
            **Run:** [View Details](${runUrl})
            
            ### Failed Checks
            - **Development:** ${{ needs.health-check-dev.result }}
            - **Production:** ${{ needs.health-check-prod.result }}
            - **Performance:** ${{ needs.performance-check.result }}
            - **Database:** ${{ needs.database-check.result }}
            
            ### Immediate Actions Required
            - [ ] Investigate the root cause
            - [ ] Check server status and logs
            - [ ] Verify DNS and SSL configuration
            - [ ] Test database connectivity
            - [ ] Monitor for recovery
            
            ### Escalation
            If this issue persists for more than 15 minutes, escalate to the development team.
            
            ---
            *This issue was automatically created by the monitoring system.*
            `;
            
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['incident', 'monitoring', 'urgent']
            });

  # ============================================================================
  # RECOVERY VERIFICATION
  # ============================================================================
  
  verify-recovery:
    name: ✅ Verify Recovery
    runs-on: ubuntu-latest
    timeout-minutes: 5
    if: success()
    needs: [health-check-dev, health-check-prod]
    steps:
      - name: 🔍 Close resolved incidents
        uses: actions/github-script@v7
        with:
          script: |
            // Find open incident issues
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: 'incident,monitoring',
              state: 'open'
            });
            
            // Close resolved incidents
            for (const issue of issues.data) {
              if (issue.title.includes('Health Check Failed')) {
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  body: `✅ **RESOLVED** - Health checks are now passing.\n\nRecovery verified at: ${new Date().toISOString()}`
                });
                
                await github.rest.issues.update({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  state: 'closed',
                  labels: [...issue.labels.map(l => l.name), 'resolved']
                });
              }
            }
            
      - name: 📊 Generate health report
        run: |
          echo "## 📊 Health Check Summary" >> $GITHUB_STEP_SUMMARY
          echo "**Time:** $(date -u)" >> $GITHUB_STEP_SUMMARY
          echo "**Status:** ✅ All systems operational" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Response Times" >> $GITHUB_STEP_SUMMARY
          echo "- **Development:** ${{ needs.health-check-dev.outputs.response_time || 'N/A' }}ms" >> $GITHUB_STEP_SUMMARY
          echo "- **Production:** ${{ needs.health-check-prod.outputs.response_time || 'N/A' }}ms" >> $GITHUB_STEP_SUMMARY
