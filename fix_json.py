import json
import sys

try:
    with open('src/services/data/cars.json', 'r') as f:
        content = f.read()
    
    # Fix the specific issue at line 20783
    lines = content.split('\n')
    if len(lines) >= 20783:
        # Replace the problematic line
        lines[20782] = '              ],'
        
        # Write the fixed content back to the file
        with open('src/services/data/cars.json', 'w') as f:
            f.write('\n'.join(lines))
        
        print("Fixed the JSON file")
    else:
        print("File doesn't have enough lines")
        
except Exception as e:
    print(f"Error: {e}")
