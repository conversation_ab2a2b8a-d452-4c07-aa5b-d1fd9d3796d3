/**
 * Phone Authentication API Endpoints
 * 
 * Secure API endpoints for phone-only authentication
 * Handles OTP sending, verification, and user management
 */

import { supabase } from '@/services/authService';
import { sendOTP, verifyOTP, validateAlgerianPhone } from '@/services/dexatelService';

// Types
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

interface SendOTPRequest {
  phone: string;
}

interface VerifyOTPRequest {
  sessionId: string;
  code: string;
}

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map<string, { attempts: number; windowStart: number; blocked: boolean }>();

/**
 * Rate limiting middleware - DISABLED: Always allow unlimited attempts
 */
function checkRateLimit(identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): { allowed: boolean; remaining: number; resetTime?: number } {
  // DISABLED: Rate limiting completely removed - always allow unlimited attempts
  return { allowed: true, remaining: 999 };
}

/**
 * Validate request data
 */
function validateSendOTPRequest(data: any): data is SendOTPRequest {
  return data && typeof data.phone === 'string' && data.phone.trim().length > 0;
}

function validateVerifyOTPRequest(data: any): data is VerifyOTPRequest {
  return (
    data &&
    typeof data.sessionId === 'string' &&
    typeof data.code === 'string' &&
    data.sessionId.trim().length > 0 &&
    /^\d{6}$/.test(data.code)
  );
}

/**
 * Send OTP endpoint
 */
export async function sendOTPEndpoint(request: Request): Promise<Response> {
  try {
    // Parse request
    const data = await request.json();
    
    // Validate request data
    if (!validateSendOTPRequest(data)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid request data',
        code: 'INVALID_REQUEST'
      } as APIResponse), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate phone number
    const validation = validateAlgerianPhone(data.phone);
    if (!validation.isValid) {
      return new Response(JSON.stringify({
        success: false,
        error: validation.error || 'Invalid phone number format',
        code: 'INVALID_PHONE'
      } as APIResponse), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const formattedPhone = validation.formatted;
    
    // Check rate limiting
    const rateLimit = checkRateLimit(`phone:${formattedPhone}`, 3, 60 * 60 * 1000); // 3 attempts per hour
    if (!rateLimit.allowed) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Too many attempts. Please try again later.',
        code: 'RATE_LIMITED',
        data: {
          resetTime: rateLimit.resetTime ? new Date(rateLimit.resetTime).toISOString() : undefined
        }
      } as APIResponse), {
        status: 429,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Send OTP
    const result = await sendOTP(formattedPhone);
    
    if (!result.success) {
      return new Response(JSON.stringify({
        success: false,
        error: result.error,
        code: 'OTP_SEND_FAILED'
      } as APIResponse), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return success (don't expose verification ID)
    return new Response(JSON.stringify({
      success: true,
      data: {
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2)}`,
        expiresAt: result.data?.expires_at,
        remainingAttempts: rateLimit.remaining
      }
    } as APIResponse), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error in sendOTPEndpoint:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    } as APIResponse), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Verify OTP endpoint
 */
export async function verifyOTPEndpoint(request: Request): Promise<Response> {
  try {
    // Parse request
    const data = await request.json();
    
    // Validate request data
    if (!validateVerifyOTPRequest(data)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid request data',
        code: 'INVALID_REQUEST'
      } as APIResponse), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check rate limiting for verification attempts
    const rateLimit = checkRateLimit(`verify:${data.sessionId}`, 5, 5 * 60 * 1000); // 5 attempts per 5 minutes
    if (!rateLimit.allowed) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Too many verification attempts. Please request a new code.',
        code: 'RATE_LIMITED'
      } as APIResponse), {
        status: 429,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify OTP
    const result = await verifyOTP(data.sessionId, data.code);
    
    if (!result.success) {
      return new Response(JSON.stringify({
        success: false,
        error: result.error,
        code: 'VERIFICATION_FAILED',
        data: {
          remainingAttempts: rateLimit.remaining
        }
      } as APIResponse), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return success with user data
    return new Response(JSON.stringify({
      success: true,
      data: {
        user: result.data,
        action: 'login' // or 'signup' based on whether user existed
      }
    } as APIResponse), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error in verifyOTPEndpoint:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    } as APIResponse), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Check phone existence endpoint
 */
export async function checkPhoneEndpoint(request: Request): Promise<Response> {
  try {
    const url = new URL(request.url);
    const phone = url.searchParams.get('phone');
    
    if (!phone) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Phone number is required',
        code: 'MISSING_PHONE'
      } as APIResponse), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate phone number
    const validation = validateAlgerianPhone(phone);
    if (!validation.isValid) {
      return new Response(JSON.stringify({
        success: false,
        error: validation.error || 'Invalid phone number format',
        code: 'INVALID_PHONE'
      } as APIResponse), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check if phone exists
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .eq('phone', validation.formatted)
      .limit(1);
    
    if (error) {
      console.error('Error checking phone existence:', error);
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to check phone number',
        code: 'DATABASE_ERROR'
      } as APIResponse), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify({
      success: true,
      data: {
        exists: data && data.length > 0,
        phone: validation.formatted
      }
    } as APIResponse), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error in checkPhoneEndpoint:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    } as APIResponse), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Health check endpoint
 */
export async function healthCheckEndpoint(): Promise<Response> {
  try {
    // Check Dexatel API key
    const hasApiKey = !!import.meta.env.VITE_DEXATEL_API_KEY;
    
    // Check Supabase connection
    const { error } = await supabase.from('profiles').select('id').limit(1);
    const supabaseHealthy = !error;
    
    return new Response(JSON.stringify({
      success: true,
      data: {
        status: 'healthy',
        services: {
          dexatel: hasApiKey ? 'configured' : 'not_configured',
          supabase: supabaseHealthy ? 'healthy' : 'error'
        },
        timestamp: new Date().toISOString()
      }
    } as APIResponse), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error in healthCheckEndpoint:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Health check failed',
      code: 'HEALTH_CHECK_FAILED'
    } as APIResponse), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
