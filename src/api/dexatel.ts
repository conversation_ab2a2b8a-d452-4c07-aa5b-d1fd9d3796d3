/**
 * Dexatel API Proxy
 * 
 * Server-side proxy to handle Dexatel API calls
 * Solves CORS issues by making API calls from the server
 */

// This would typically be in a backend API route
// For now, we'll create a workaround using a different approach

export interface DexatelProxyRequest {
  action: 'send' | 'verify';
  phone?: string;
  verificationId?: string;
  code?: string;
}

export interface DexatelProxyResponse {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Proxy function to handle Dexatel API calls
 * This should be moved to a proper backend API route
 */
export async function callDexatelAPI(request: DexatelProxyRequest): Promise<DexatelProxyResponse> {
  const apiKey = import.meta.env.VITE_DEXATEL_API_KEY;
  
  if (!apiKey) {
    return {
      success: false,
      error: 'Dexatel API key not configured'
    };
  }

  try {
    if (request.action === 'send') {
      // Send OTP
      const response = await fetch('https://api.dexatel.com/v1/verifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Dexatel-Key': api<PERSON>ey,
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          to: request.phone,
          channel: 'sms',
          template: 'Your AROUZ MARKET verification code is: {{code}}. Valid for 5 minutes.'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: `Dexatel API error: ${response.status} - ${errorText}`
        };
      }

      const data = await response.json();
      return {
        success: true,
        data
      };

    } else if (request.action === 'verify') {
      // Verify OTP
      const response = await fetch('https://api.dexatel.com/v1/verifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Dexatel-Key': apiKey,
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          verification_id: request.verificationId,
          code: request.code
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: `Dexatel API error: ${response.status} - ${errorText}`
        };
      }

      const data = await response.json();
      return {
        success: true,
        data
      };
    }

    return {
      success: false,
      error: 'Invalid action'
    };

  } catch (error: any) {
    return {
      success: false,
      error: `Network error: ${error.message}`
    };
  }
}
