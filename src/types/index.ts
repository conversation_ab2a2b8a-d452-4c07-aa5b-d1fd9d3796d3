
export interface Product {
  id: string;
  name: string;
  description: string;
  sku: string;
  partArticleNumber?: string;
  category: string;
  subcategory?: string;
  price: number;
  stockQuantity: number;
  images: string[];
  primaryImage?: string;
  additionalImages?: string[];
  specifications: Record<string, string>;
  manufacturer: string;
  manufacturerLogo?: string;
  supplierName?: string;
  shippingOrigin?: string; // CRITICAL: Supplier location (wilaya) for shipping origins
  compatibleVehicles?: string[];
  status: 'draft' | 'published' | 'out_of_stock';
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  descriptionAndSpecifications?: string;
  parentId?: string;
  image?: string;
  slug: string;
  productCount?: number;
  isDefault?: boolean;
}

export interface DashboardStats {
  totalProducts: number;
  pendingApprovals: number;
  outOfStock: number;
  recentlyViewed: number;
}
