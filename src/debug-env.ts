// Debug environment variables to ensure production database connection
console.log('🔍 ENVIRONMENT DEBUG INFORMATION');
console.log('================================');
console.log('📍 VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('🔑 VITE_SUPABASE_ANON_KEY (first 20 chars):', import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...');
console.log('🚀 VITE_USE_SUPABASE:', import.meta.env.VITE_USE_SUPABASE);
console.log('🏗️ VITE_USE_SUPABASE_BACKEND:', import.meta.env.VITE_USE_SUPABASE_BACKEND);
console.log('🌍 VITE_APP_ENV:', import.meta.env.VITE_APP_ENV);
console.log('🔧 NODE_ENV:', import.meta.env.NODE_ENV);
console.log('🏠 DEV mode:', import.meta.env.DEV);
console.log('🏭 PROD mode:', import.meta.env.PROD);

// Check if we're using production database
const isProductionDB = import.meta.env.VITE_SUPABASE_URL?.includes('irkwpzcskeqtasutqnxp');
console.log('🎯 Using Production Database:', isProductionDB ? '✅ YES' : '❌ NO');

if (!isProductionDB) {
  console.error('🚨 WARNING: NOT CONNECTED TO PRODUCTION DATABASE!');
  console.error('🚨 Current URL:', import.meta.env.VITE_SUPABASE_URL);
  console.error('🚨 Expected: https://irkwpzcskeqtasutqnxp.supabase.co');
}

export const debugInfo = {
  supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
  isProductionDB,
  useSupabase: import.meta.env.VITE_USE_SUPABASE,
  appEnv: import.meta.env.VITE_APP_ENV
};
