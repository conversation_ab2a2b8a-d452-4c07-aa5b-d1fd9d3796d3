{"general": {"appName": "AROUZ MARKET", "dashboard": "Dashboard", "welcome": "Welcome back, {{name}}. Here's what's happening with your products."}, "navigation": {"dashboard": "Dashboard", "products": "Products", "productsTable": "Product Data Grid", "allProducts": "All Products", "addProduct": "Add Product", "orders": "Orders", "shipments": "Shipments", "categories": "Categories", "customers": "Customers", "compatibilityHub": "Compatibility Hub", "settings": "Settings", "logout": "Logout"}, "dashboard": {"totalProducts": "Total Products", "pendingApprovals": "Pending Approvals", "outOfStock": "Out of Stock", "recentlyViewed": "Recently Viewed", "recentProducts": "Recent Products", "latestAddedProducts": "Latest Added Products", "viewAll": "View All", "productPerformance": "Product Performance", "topViewedProducts": "Top Viewed Products This Month"}, "products": {"addProduct": "Add Product", "editProduct": "Edit Product", "id": "Product ID", "name": "Product Name", "enterName": "Enter product name", "image": "Image", "primaryImage": "Primary Product Image", "additionalImages": "Additional Product Images", "sku": "SKU", "enterSku": "Enter SKU", "barcodeEan": "Part Article Number", "enterBarcode": "Enter part article number", "category": "Category", "subcategory": "Subcategory", "selectSubcategory": "Select subcategory", "manufacturer": "Brand / Manufacturer", "enterManufacturer": "Enter manufacturer", "supplierName": "Supplier Name", "enterSupplier": "Enter supplier", "description": "Description and Specifications", "enterDescription": "Enter product description and specifications", "basicInfo": "Basic Info", "images": "Images", "dragAndDrop": "Drag & drop image or click to upload", "uploadImage": "Upload Image", "addImage": "Add Image", "export": "Export", "exportProducts": "Export Products", "exportSuccess": "Successfully exported {{count}} products", "importProducts": "Import Products", "importNotImplemented": "Import functionality coming soon", "duplicateNotImplemented": "Duplicate functionality coming soon", "featureNotImplemented": "Feature functionality coming soon", "search": "Search products...", "allCategories": "All Categories", "status": "Status", "allStatus": "All Status", "allStatuses": "All Statuses", "moreFilters": "More Filters", "advancedFilters": "Advanced Filters", "engineParts": "Engine Parts", "brakeSystem": "Brake System", "suspension": "Suspension", "electrical": "Electrical System", "bodyParts": "Body Parts", "tyres": "<PERSON><PERSON>", "products": "Products", "manageProductCatalog": "Manage your product catalog", "gridView": "Grid view", "listView": "List view", "goToDataGrid": "Go to Product Data Grid", "refreshProducts": "Refresh Products", "published": "Published", "active": "Active", "draft": "Draft", "outOfStock": "Out of Stock", "pendingApproval": "Pending Approval", "discontinued": "Discontinued", "lowStock": "Low Stock", "edit": "Edit", "duplicate": "Duplicate", "delete": "Delete", "feature": "Feature", "stock": "Stock", "price": "Price", "priceRange": "Price Range", "inStockOnly": "In Stock Only", "allManufacturers": "All Manufacturers", "savePreset": "Save Preset", "saveFilterPreset": "Save Filter Preset", "saveFilterPresetDescription": "Save your current filter settings for future use", "presetName": "Preset Name", "resetFilters": "Reset Filters", "clearAll": "Clear All", "activeFilters": "Active Filters", "barcodeRequired": "Barcode is required", "barcodeExists": "Barcode already exists", "productCreated": "Product created successfully", "productUpdated": "Product updated successfully", "untitled": "Untitled Product", "actions": "Actions", "markAsActive": "<PERSON> as Active", "markAsDraft": "<PERSON> as Draft", "markAsOutOfStock": "<PERSON> as Out of Stock", "markAsDiscontinued": "<PERSON> as Discontinued", "setStatus": "Set Status", "view": "View", "deleteSuccess": "Product deleted successfully", "deleteError": "Failed to delete product", "bulkDeleteSuccess": "{{count}} products deleted successfully", "bulkDeleteError": "Failed to delete products", "statusUpdateSuccess": "Status updated for {{count}} products", "statusUpdateError": "Failed to update product status", "updateSuccess": "Product updated successfully", "updateError": "Failed to update product", "units": "units", "duplicateSuccess": "Product duplicated successfully", "comingSoon": "Coming soon", "noProductsFound": "No products found", "tryAdjustingFilters": "Try adjusting your search or filter criteria", "loading": "Loading products...", "loadingDescription": "Please wait while we load your products", "enterBarcodeToAdd": "Enter a barcode to add a new product", "scanBarcode": "Scan Barcode", "barcodeScanned": "Barcode scanned successfully", "columns": "Columns", "toggleColumns": "Toggle Columns", "import": "Import", "selectedItems": "{{count}} items selected", "confirmDelete": "Confirm Delete", "deleteConfirmation": "Are you sure you want to delete this product? This action cannot be undone.", "deleteSelectedConfirmation": "Are you sure you want to delete {{count}} selected products? This action cannot be undone.", "shareByEmail": "Share by Email", "shareProductsByEmail": "Share Products by Email", "shareProductsDescription": "Share {{count}} selected products with your client via email", "recipientEmail": "Recipient Email", "emailSubject": "Email Subject", "messageToClient": "Here are some products I thought you might be interested in...", "includeOptions": "Include Options", "includeImages": "Include Images", "includePrices": "Include Prices", "includeDescriptions": "Include Descriptions", "preview": "Preview", "copyContent": "Copy Content", "copied": "Copied!", "sendEmail": "Send Email", "emailRequired": "Email address is required", "emailClientOpened": "Email client opened", "copiedToClipboard": "Copied to clipboard", "failedToCopy": "Failed to copy to clipboard", "selectedProducts": "Selected Products", "emailFooter": "Thank you for your interest in our products. Please contact us for more information.", "checkOutTheseProducts": "Check out these products from AROUZ MARKET"}, "categories": {"tyres": "<PERSON><PERSON>", "brakes": "<PERSON>rakes"}, "productsDataGrid": {"title": "Product Library", "productLibrary": "Product Library", "manageProductCatalog": "Manage your product catalog with advanced filtering and bulk actions", "description": "Manage your product catalog with advanced filtering and bulk actions", "selectAll": "Select All", "columns": "Columns", "import": "Import", "export": "Export", "search": "Search products...", "allCategories": "All Categories", "allStatus": "All Status", "productName": "Product Name", "category": "Category", "price": "Price", "stock": "Stock", "status": "Status", "updatedAt": "Updated At"}, "actions": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "view": "View", "clearSelection": "Clear Selection", "confirm": "Confirm", "remove": "Remove", "close": "Close"}, "language": {"arabic": "العربية", "english": "English", "french": "Français", "selectLanguage": "Select Language"}, "roles": {"manufacturer": "Supplier & Manufacturer", "merchant": "Merchant Retailer", "consumer": "Consumer", "distribution": "Distribution", "supplierAndManufacturer": "Supplier & Manufacturer", "merchantRetailer": "Merchant Retailer", "user": "User", "manufacturerDescription": "Manage products, inventory and wholesale pricing for manufacturers and suppliers", "merchantDescription": "Sell products directly to consumers with retail pricing and store management", "consumerDescription": "Browse and purchase products", "distributionDescription": "Manage shipping and logistics"}, "checkout": {"title": "Checkout", "secureCheckout": "Secure Checkout", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shipping": "Shipping", "arouzFee": "AROUZ Fee", "total": "Total", "calculatedAtCheckout": "Calculated at checkout", "cashOnDelivery": "Cash on Delivery", "storePickup": "Store Pickup & Pay", "orderCreated": "Order Created Successfully", "orderNumber": "Order Number", "orderFailed": "Order Creation Failed", "orderFailedDescription": "There was an error creating your order. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again.", "authRequired": "Authentication Required", "pleaseSignIn": "Please sign in to continue with checkout", "pleaseSignInFirst": "Please sign in first to proceed", "signInToContinue": "Sign in to continue with checkout", "completeAuthentication": "Please complete phone verification to proceed", "deliveryRequired": "Delivery Address Required", "selectDeliveryLocation": "Please select a delivery location to continue", "paymentRequired": "Payment Method Required", "selectPaymentMethod": "Please select a payment method to continue", "step1": {"title": "Phone Verification", "description": "Verify your phone number to continue with checkout", "authRequired": "Authentication Required", "authDescription": "Please verify your phone number to proceed with your order", "verifyPhone": "Verify Phone Number", "securityFeatures": "Security Features", "feature1": "Secure SMS verification", "feature2": "No password required", "feature3": "Quick and easy process", "authSuccess": "Authentication Successful", "welcomeBack": "Welcome back! You can now proceed with your order.", "consumer": "Consumer", "verified": "Verified", "proceeding": "Proceeding to next step..."}, "step2": {"title": "Delivery Address", "description": "Confirm your delivery location using Google Maps", "selectLocation": "Select Location on Map", "locationSelected": "Location selected successfully", "deliveryAddress": "Delivery Address", "addressPlaceholder": "Enter your complete delivery address...", "wilaya": "<PERSON><PERSON><PERSON>", "wilayaPlaceholder": "Select your wilaya", "googleMapsUrl": "Google Maps Confirmation", "confirmed": "Confirmed", "viewOnMaps": "View on Maps", "specialInstructions": "Special Instructions", "instructionsPlaceholder": "Any special delivery instructions...", "instructionsHelp": "Optional: Provide any special instructions for delivery", "deliverySummary": "Delivery Summary", "instructions": "Instructions", "validating": "Validating address...", "errors": {"addressRequired": "Delivery address is required", "wilayaRequired": "Wilaya is required", "coordinatesRequired": "Please select a location on the map", "addressTooShort": "Please provide a more detailed address", "validationFailed": "Address validation failed"}}, "step3": {"title": "Payment Method", "description": "Choose how you'd like to pay for your order", "codNote": "Cash Payment Required", "codDescription": "Have exact change ready for the delivery driver", "pickupNote": "Store Location", "pickupDescription": "You'll receive pickup details after order confirmation", "securityTitle": "Secure Payment", "security1": "All transactions are secure and encrypted", "security2": "No payment information stored", "security3": "Full refund policy available", "selectedMethod": "Selected Payment Method", "comingSoon": "Coming Soon", "futurePayments": "Online payment options will be available soon"}, "step4": {"title": "Order Confirmation", "description": "Review your order details before confirming", "customerInfo": "Customer Information", "name": "Name", "phone": "Phone", "consumer": "Consumer", "deliveryInfo": "Delivery Information", "wilaya": "<PERSON><PERSON><PERSON>", "address": "Address", "instructions": "Special Instructions", "viewOnMaps": "View on Maps", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery", "express24h": "Express 24h", "storePickup": "Store Pickup & Pay", "noDeliveryFee": "No Delivery Fee", "shippingOrigins": "Shipping Origins", "estimatedLocation": "Location TBD", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shipping": "Shipping", "arouzFee": "AROUZ Fee", "total": "Total", "termsText": "I agree to the", "termsLink": "Terms & Conditions", "and": "and", "privacyLink": "Privacy Policy", "warningTitle": "Important Notice", "warningText": "Please ensure all information is correct before confirming your order.", "processing": "Processing...", "confirmOrder": "Confirm Order"}}, "orderSuccess": {"title": "Order Confirmed!", "description": "Thank you for your order. We'll process it shortly.", "orderNotFound": "Order Not Found", "orderDetails": "Order Details", "orderNumber": "Order Number", "orderDate": "Order Date", "status": "Status", "paymentMethod": "Payment Method", "customerInfo": "Customer Information", "name": "Name", "phone": "Phone", "deliveryInfo": "Delivery Information", "deliveryAddress": "Delivery Address", "wilaya": "<PERSON><PERSON><PERSON>", "specialInstructions": "Special Instructions", "viewOnMaps": "View on Maps", "shippingOrigins": "Shipping Origins", "orderItems": "Order Items", "quantity": "Quantity", "supplier": "Supplier", "subtotal": "Subtotal", "shipping": "Shipping", "arouzFee": "AROUZ Fee", "total": "Total", "nextSteps": "What Happens Next?", "step1": "We'll confirm your order and assign it to a shipping company", "step2": "You'll receive tracking information via SMS", "step3": "Your order will be delivered within 24-48 hours", "backToHome": "Back to Home", "viewOrders": "View My Orders"}, "orderStatus": {"pending": "Pending", "confirmed": "Confirmed", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled"}, "auth": {"loginOrSignUp": "Login or Sign up", "welcomeToArouz": "Welcome to AROUZ MARKET", "countryRegion": "Country/Region", "phoneNumber": "Phone number", "phoneFormat": "Enter your 9-digit Algerian mobile number (e.g., XX XXX XX XX)", "privacyNotice": "We'll call or text you to confirm your number. Standard message and data rates apply.", "privacyPolicy": "Privacy Policy", "continue": "Continue", "or": "or", "continueWithGoogle": "Continue with Google", "continueWithApple": "Continue with Apple", "continueWithEmail": "Continue with email", "continueWithFacebook": "Continue with Facebook", "becomeSupplier": "Become a Supplier", "joinAsMerchant": "Join as <PERSON>", "sellWithUs": "If you want to sell your products with us:", "supplierTooltip": "Sign up to sell products in bulk to retailers.", "merchantTooltip": "Register to sell products directly to consumers.", "supplierSignUp": "Supplier & Manufacturer Sign Up", "merchantSignUp": "Merchant Retailer Sign Up", "supplierLogin": "Supplier & Manufacturer Login", "merchantLogin": "Merchant Retailer Login", "companyName": "Company Name", "enterCompanyName": "Enter your company name", "taxId": "Tax ID", "enterTaxId": "Enter your tax ID", "storeName": "Store Name", "enterStoreName": "Enter your store name", "storeAddress": "Store Address", "enterStoreAddress": "Enter your store address", "signUp": "Sign Up", "login": "<PERSON><PERSON>", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "otpCode": "Verification Code", "otpDescription": "Enter the verification code sent to your phone", "verify": "Verify", "verifying": "Verifying...", "processing": "Processing...", "verifyOtp": "Verify Your Phone", "didntReceiveCode": "Didn't receive a code?", "tryAgain": "Try again", "registrationSuccess": "Registration successful!", "loginSuccess": "Login successful!", "verificationSuccess": "Verification successful!", "registrationFailed": "Registration failed", "loginFailed": "<PERSON><PERSON> failed", "verificationFailed": "Verification failed", "verificationEmailSent": "Verification email sent", "checkYourEmail": "Check Your Email", "confirmEmailInstructions": "Please check your email and click the confirmation link to activate your account", "emailVerificationRequired": "Important: Email Verification Required", "emailSentTo": "We've sent a confirmation email to", "verificationLinkInstructions": "Please check your inbox and click the verification link to activate your account", "whatToDoNext": "What to do next:", "checkInbox": "Check your email inbox (and spam folder)", "openEmail": "Open the email from AROUZ MARKET", "clickVerificationLink": "Click the verification link to activate your account", "loginAfterVerification": "Once verified, you can log in to access your account", "gotIt": "Got it", "verifyingAccount": "Verifying your account", "pleaseWait": "Please wait while we verify your account", "accountVerified": "Account verified successfully!", "redirectingToDashboard": "You'll be redirected to your dashboard in a moment", "userNotFound": "User not found", "profileNotFound": "Profile not found", "unexpectedError": "An unexpected error occurred. Please try again.", "returnToPartners": "Return to Partners", "alreadyVerified": "This account has already been verified. Please login instead.", "linkExpired": "The verification link has expired or is invalid. Please request a new one.", "emailAlreadyRegistered": "This email is already registered. Please use the login option instead.", "supplierPartner": "Supplier & Manufacturer", "merchantPartner": "Merchant Retailer", "supplierDescription": "Join as a Supplier & Manufacturer to sell your products in bulk to retailers across Algeria. Access powerful B2B wholesale tools and expand your distribution network.", "merchantDescription": "Join as a Merchant Retailer to sell products directly to consumers. Create your online store and reach customers throughout Algeria with our B2C marketplace.", "confirmLogout": "Confirm <PERSON>ut", "logoutConfirmationMessage": "Are you sure you want to log out? You will need to sign in again to access your account.", "confirmLogoutAction": "Yes, Log Out", "loggingOut": "Logging out...", "logoutSuccess": "Logged Out Successfully", "logoutSuccessDescription": "You have been logged out successfully.", "logoutFailed": "Logout Failed", "logoutFailedDescription": "There was a problem logging you out. Please try again.", "loginRequired": "<PERSON><PERSON> Required", "loginRequiredDescription": "Please log in to add items to your wishlist", "logout": "Logout", "loggedOut": "Logged Out", "loggedOutDescription": "You have been successfully logged out", "alreadyAuthenticatedAsSupplier": "You are already signed in as a Supplier & Manufacturer. You can continue to the dashboard or close this dialog.", "alreadyAuthenticatedAsMerchant": "You are already signed in as a Merchant Retailer. You can continue to the dashboard or close this dialog.", "supplierAccountStatus": "Supplier & Manufacturer Account", "merchantAccountStatus": "Merchant Retailer Account", "active": "Active", "inactive": "Not Active", "signInToAccount": "Sign in to your account", "createAccount": "Create an account", "continueToAccount": "Continue to your account", "logoutFromSupplierAccount": "<PERSON><PERSON><PERSON> from Supplier Account", "logoutFromMerchantAccount": "<PERSON><PERSON><PERSON> from Merchant Account", "logoutFromSupplier": "<PERSON><PERSON><PERSON> from Supplier", "logoutFromMerchant": "<PERSON><PERSON><PERSON> from Merchant", "logoutRoleSpecificMessage": "You will remain logged in to your other account. You can switch between accounts from the Partners page.", "roleConflictTitle": "Role Conflict Detected", "roleConflictDescription": "You cannot be logged in as both a Supplier & Manufacturer and a Merchant Retailer at the same time. Please log out from your current role to continue.", "logoutAndContinue": "Logout and Continue", "roleLogoutSuccess": "Role Logout Successful", "roleLogoutSuccessDescription": "You have been logged out from your previous role.", "emailRoleMismatchTitle": "Account Role Mismatch", "emailRoleMismatchSupplier": "This email is registered as a Supplier & Manufacturer account. Please use the Supplier & Manufacturer login section.", "emailRoleMismatchMerchant": "This email is registered as a Merchant Retailer account. Please use the Merchant Retailer login section.", "sessionConflictTitle": "Session Conflict Detected", "sessionConflictSupplierActive": "You are currently logged in as a Supplier & Manufacturer. Please logout from your Supplier account before logging in as a Merchant Retailer.", "sessionConflictMerchantActive": "You are currently logged in as a Merchant Retailer. Please logout from your Merchant account before logging in as a Supplier & Manufacturer.", "logoutFromCurrentRole": "<PERSON><PERSON><PERSON> from Current Role", "switchToCorrectLogin": "Switch to Correct Login", "stayLoggedIn": "Stay Logged In", "authenticationBlocked": "Authentication Blocked", "authenticationBlockedDescription": "Please resolve the role conflict to continue.", "password": "Password", "enterPassword": "Enter your password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "forgotPasswordDescription": "Enter your email address and we'll send you a link to reset your password", "sendResetLink": "Send Reset Link", "resetPassword": "Reset Password", "resetPasswordDescription": "Enter your new password below", "enterNewPassword": "Enter new password", "confirmNewPassword": "Confirm new password", "passwordResetSuccess": "Password Reset Successful", "passwordResetSuccessDescription": "Your password has been reset successfully. You can now log in with your new password.", "resetPasswordFailed": "Password Reset Failed", "resetPasswordEmailSent": "Password Reset Email <PERSON>", "resetPasswordEmailSentDescription": "Check your email for a link to reset your password. The link will expire in 1 hour.", "backToLogin": "Back to Login", "withPassword": "With Password", "withPhone": "With Phone", "welcomeBack": "Welcome back!", "optionalPhoneVerification": "Phone Verification", "requiredPhoneVerification": "Phone Verification (Required)", "fullName": "Full Name", "enterFullName": "Enter your full name"}, "marketplace": {"navigation": "Navigation", "myVehicleParts": "Parts For My Vehicle", "wholesaleOffers": "Wholesale Offers", "wholesaleOffersDescription": "Discover bulk offers and special deals from suppliers and manufacturers", "noWholesaleProductsFound": "No wholesale products found", "tryDifferentFiltersWholesale": "Try adjusting your filters or browse different categories to find wholesale offers", "partners": "Partners", "account": "Account", "cart": "<PERSON><PERSON>", "item": "item", "items": "items", "close": "Close", "tyres": "<PERSON><PERSON>", "allOtherParts": "Parts Search Engine", "comingSoon": "Coming Soon", "underDevelopment": "This feature is under development and will be available soon!", "myWishlist": "My Wishlist", "myReviews": "My Reviews", "messages": "Messages", "languages": "Languages", "helpCenter": "Help Centre", "tyreWidth": "<PERSON><PERSON>", "aspectRatio": "Aspect Ratio", "rimDiameter": "<PERSON><PERSON>", "season": "Season", "selectWidth": "Select Width", "selectRatio": "Select Ratio", "selectDiameter": "Select Diameter", "selectSeason": "Select Season", "summer": "Summer", "winter": "Winter", "allSeason": "All Season", "deliverTo": "Deliver To", "selectLocation": "Select Location", "chooseYourLocation": "Choose Your Location", "enterDeliveryLocation": "Enter Delivery Location", "enterLocation": "Enter your location", "currentLocation": "Current Location", "searchLocation": "Search for a location", "locationPermissionDenied": "Location access denied. Please enable location services.", "locationUnavailable": "Location information is unavailable.", "locationTimeout": "Location request timed out.", "gettingLocation": "Getting your location...", "selectOnMap": "Select on Map", "dragToAdjust": "Drag marker to adjust location", "searchTyres": "Search Tyres", "searchResults": "Search Results", "showing": "Showing", "results": "results", "filters": "Filters", "productFilters": "Product Filters", "filtersActive": "filters active", "resetAll": "Reset All", "priceRange": "Price Range", "brands": "Brands", "brand": "Brand", "stockStatus": "Stock Status", "sortBy": "Sort by", "cartEmpty": "Your cart is empty", "addItemsToCart": "Add items to your cart to see them here", "continueShopping": "Continue Shopping", "quantity": "Quantity", "removeItem": "Remove Item", "subtotal": "Subtotal", "checkout": "Checkout", "addToCart": "Add to Cart", "addToWishlist": "Add to Wishlist", "removeFromWishlist": "Remove from Wishlist", "addedToWishlist": "Added to Wishlist", "removedFromWishlist": "Removed from Wishlist", "addedToWishlistDescription": "Product saved to your wishlist", "removedFromWishlistDescription": "Product removed from your wishlist", "wishlistError": "Wishlist Error", "wishlistErrorDescription": "Failed to update wishlist. Please try again.", "wishlistEmpty": "Your wishlist is empty", "wishlistEmptyDescription": "Save items you love to your wishlist and shop them later", "addedToCart": "added to cart", "writeReview": "Write Review", "customerReviews": "Customer Reviews", "noReviewsYet": "No Reviews Yet", "beFirstToReview": "Be the first to share your experience with this product!", "writeFirstReview": "Write First Review", "reviewSubmitted": "Review Submitted", "reviewSubmittedDescription": "Thank you for your review! It will be published shortly.", "outOfStock": "Out of Stock", "inStock": "In Stock", "piece": "piece", "useFiltersToRefine": "Use filters to find specific products", "allCategories": "All Categories", "allCategoriesDesc": "Browse all available products", "tyresDesc": "Find the perfect tyres for your vehicle", "brakePartsDesc": "Quality brake components and parts", "heroTitle": "Find the Perfect Parts for Your Vehicle", "heroSubtitle": "Browse our extensive catalog of auto parts and accessories", "findPartsForVehicle": "Find Parts for My Vehicle", "browseWholesale": "Browse Wholesale Offers", "mainCategories": "Main Categories", "vehiclePartsDescription": "Find parts specifically designed for your vehicle with our intelligent matching system", "findParts": "Find Parts", "wholesaleDescription": "Explore bulk offers and special deals for merchants and retailers", "browseOffers": "Browse Offers", "whyChooseUs": "Why Choose AROUZ MARKET", "whyChooseUsDescription": "We offer a seamless shopping experience with quality products and exceptional service", "easySearch": "Easy Search", "easySearchDescription": "Find the right parts for your vehicle in just a few clicks", "fastDelivery": "Fast Delivery", "fastDeliveryDescription": "Get your parts delivered quickly to your location", "qualityGuarantee": "Quality Guarantee", "qualityGuaranteeDescription": "All our products meet or exceed OEM specifications", "support": "24/7 Support", "supportDescription": "Our customer service team is always ready to help", "readyToShop": "Ready to Shop?", "readyToShopDescription": "Start browsing our extensive catalog of auto parts and accessories", "startShopping": "Start Shopping", "products": "products", "popularity": "Popularity", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "minimumOrderQuantity": "Minimum Order Quantity", "highestRated": "Highest Rated", "gridView": "Grid View", "listView": "List View", "viewMode": "View Mode", "remove": "Remove", "clearFilters": "Clear Filters", "selectBrand": "Select Brand", "selectModel": "Select Model", "selectEngine": "Select Engine", "searchBrands": "Search brands", "searchModels": "Search models", "searchEngines": "Search engines", "searchForBrand": "Search for a brand", "searchForModel": "Search for a model", "searchForEngine": "Search for an engine", "noBrandsFound": "No brands found", "noModelsFound": "No models found", "noEnginesFound": "No engines found", "noBrandSelected": "Select a brand", "noModelSelected": "Select a model", "noEngineSelected": "Select an engine", "searchParts": "Search Parts", "searchText": "Search Text", "searchTyresPlaceholder": "Search for tyres...", "searchPartsPlaceholder": "Search for parts...", "searchAllProducts": "Search for products...", "setTyreFilters": "<PERSON> Tyre Filters", "setVehicleFilters": "Set Vehicle Filters", "tyreSelectorTitle": "Select Tyre Specifications", "vehicleSelectorTitle": "Select Your Vehicle", "startYourSearch": "Start your search", "search": "Search", "appliedFilters": "Applied Filters", "noFiltersApplied": "No filters applied", "applyFilters": "Apply Filters", "profile": "Profile", "tyreParametersGuide": "Tyre Parameters Guide", "learnMore": "Learn More", "tyreParametersExplained": "Understanding Tyre Specifications", "tyreParametersDescription": "Tyre specifications are standardized codes that tell you the exact dimensions and capabilities of a tyre.", "tyreWidthDescription": "Width of the tyre in millimeters", "aspectRatioDescription": "Height as a percentage of width", "constructionTypeDescription": "Radial construction type", "rimDiameterDescription": "Diameter of the wheel rim in inches", "loadIndexDescription": "Maximum load capacity rating", "speedRatingDescription": "Maximum speed rating", "tyreParametersHelp": "Use this guide to help you select the right tyre specifications for your vehicle."}, "settings": {"title": "Settings", "description": "Manage your account settings and preferences", "manageYourAccount": "Manage your account", "profile": "Profile", "security": "Security", "notifications": "Notifications", "preferences": "Preferences", "profileInformation": "Profile Information", "profileInformationDescription": "Update your personal information and how others see you on the platform", "securitySettings": "Security Settings", "securitySettingsDescription": "Manage your password and account security preferences", "notificationSettings": "Notification Settings", "notificationSettingsDescription": "Control which notifications you receive and how", "accountPreferences": "Account Preferences", "accountPreferencesDescription": "Customize your account experience and regional settings", "fullName": "Full Name", "companyName": "Company Name", "storeName": "Store Name", "storeAddress": "Store Address", "email": "Email", "phone": "Phone", "uploadPhoto": "Upload Photo", "photoRequirements": "JPG, GIF or PNG. Max size 1MB. Square aspect ratio recommended.", "emailChangeDescription": "To change your email address, please contact support", "phoneChangeDescription": "To change your phone number, please contact support", "saveChanges": "Save Changes", "saving": "Saving...", "profileSaved": "Profile Saved", "profileSavedDescription": "Your profile information has been updated successfully", "errorLoadingProfile": "Error Loading Profile", "errorSavingProfile": "Error Saving Profile", "unexpectedError": "An unexpected error occurred. Please try again.", "changePassword": "Change Password", "changePasswordDescription": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "updatePassword": "Update Password", "changing": "Changing...", "passwordChanged": "Password Changed", "passwordChangedDescription": "Your password has been updated successfully", "passwordChangeFailed": "Password Change Failed", "passwordMismatch": "Passwords Don't Match", "passwordMismatchDescription": "The passwords you entered don't match", "passwordTooWeak": "Password Too Weak", "passwordRequirements": "Password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters", "twoFactorAuthentication": "Two-Factor Authentication", "twoFactorAuthenticationDescription": "Add an extra layer of security to your account", "twoFactorEnabled": "Two-Factor Authentication Enabled", "twoFactorEnabledDescription": "Your account is now protected with two-factor authentication", "twoFactorDisabled": "Two-Factor Authentication Disabled", "twoFactorDisabledDescription": "Two-factor authentication has been disabled for your account", "emailVerification": "Email Verification", "emailVerificationDescription": "Verify your email address to ensure account security", "verifyEmail": "<PERSON><PERSON><PERSON>", "verificationEmailSent": "Verification Email <PERSON>", "verificationEmailSentDescription": "Please check your email for a verification link", "verificationEmailFailed": "Verification Email Failed", "accountActivity": "Account Activity", "accountActivityDescription": "Review recent activity on your account", "securityTip": "Security Tip", "securityTipDescription": "Never share your password or verification codes with anyone, including AROUZ MARKET staff", "orderUpdates": "Order Updates", "orderUpdatesDescription": "Get notified about order status changes and shipping updates", "priceAlerts": "Price Alerts", "priceAlertsDescription": "Receive notifications when prices change for products you're watching", "inventoryAlerts": "Inventory Alerts", "inventoryAlertsDescription": "Get notified when inventory levels are low or products are back in stock", "messagesDescription": "Receive notifications for new messages and inquiries", "push": "<PERSON><PERSON>", "sms": "SMS", "notificationSettingsSaved": "Notification Settings Saved", "notificationSettingsSavedDescription": "Your notification preferences have been updated successfully", "language": "Language", "languageDescription": "Choose your preferred language for the platform", "selectLanguage": "Select Language", "timezone": "Timezone", "timezoneDescription": "Set your local timezone for accurate time displays", "selectTimezone": "Select Timezone", "dateFormat": "Date Format", "dateFormatDescription": "Choose how dates are displayed throughout the platform", "theme": "Theme", "themeDescription": "Choose between light, dark, or system theme", "lightTheme": "Light", "darkTheme": "Dark", "systemTheme": "System Default", "autoTranslate": "Auto-Translate Content", "autoTranslateDescription": "Automatically translate content to your preferred language when available", "preferencesSaved": "Preferences Saved", "preferencesSavedDescription": "Your account preferences have been updated successfully", "yourAccount": "Your Account", "lastLogin": "Last login", "accountSettings": "Account <PERSON><PERSON>"}, "public": {"home": "Home", "about": "About", "features": "Features", "roadmap": "Roadmap", "contact": "Contact", "joinWaitlist": "Join <PERSON>", "footerTagline": "Algeria's First Intelligent Auto Parts Ecosystem connecting manufacturers, suppliers, merchants, and consumers.", "allRightsReserved": "All Rights Reserved", "navigation": "Navigation", "address": "Address", "prelaunchAnnouncement": "Pre-Launch Announcement", "heroTitle": "Algeria's First Intelligent Auto Parts Ecosystem", "heroSubtitle": "Connecting manufacturers, suppliers, merchants, and consumers in one powerful digital platform.", "getUpdates": "Get Launch Updates", "explorePlatform": "Explore the Platform", "statAlgerian": "Algerian-built", "statCategories": "Product Categories", "statAIPowered": "AI-powered Matching", "statRealTime": "Real-time Inventory", "statYes": "Yes", "statSync": "Sync", "platformPreview": "Platform Preview", "platformPreviewDescription": "Screenshots and demo coming soon", "coreFeatures": "Core Features", "coreFeaturesDescription": "AROUZ MARKET is built on four powerful pillars that create a complete ecosystem for the auto parts industry.", "featureInventoryTitle": "Smart Inventory Data Grid", "featureInventoryDescription": "AI-powered inventory management with real-time analytics and alerts.", "featureMatchingTitle": "AI Part Matching", "featureMatchingDescription": "Intelligent matching of parts to vehicles with compatibility verification.", "featurePOSTitle": "POS Integration", "featurePOSDescription": "Seamless point-of-sale integration with barcode scanning and receipt printing.", "featureMobileTitle": "Mobile Marketplace", "featureMobileDescription": "Consumer-facing mobile app for finding and purchasing auto parts.", "exploreAllFeatures": "Explore All Features", "builtFor": "Built for Manufacturers, Merchants, and Garages", "builtForDescription": "Our platform serves the entire auto parts ecosystem with specialized tools for each stakeholder.", "manufacturers": "Manufacturers & Suppliers", "manufacturersDescription": "Manage inventory, track sales, and connect with merchants across Algeria.", "merchants": "Merchants & Retailers", "merchantsDescription": "Access a wide range of products, manage orders, and streamline operations.", "garages": "Garages & Mechanics", "garagesDescription": "Find the right parts for any vehicle with our intelligent matching system.", "launchingQ3": "Launching Q3 2025", "launchingDescription": "Be among the first to experience AROUZ MARKET when we launch.", "joinPreLaunch": "Join Our Pre-Launch List", "joinOurWaitlist": "Join Our Waitlist", "waitlistPageDescription": "Be the first to know when AROUZ MARKET launches and get early access to our platform.", "waitlistDescription": "Sign up to receive updates about our launch and be first in line when we go live.", "fullName": "Full Name", "enterName": "Enter your name", "email": "Email Address", "enterEmail": "Enter your email", "iAm": "I am a:", "supplier": "Supplier / Manufacturer", "merchant": "Merchant / Retailer", "consumer": "Consumer", "other": "Other", "submitting": "Submitting...", "pleaseCompleteForm": "Please complete all required fields", "waitlistSuccess": "Thank you for joining our waitlist!", "waitlistError": "There was an error submitting your request. Please try again.", "aboutTitle": "About AROUZ MARKET", "aboutSubtitle": "Building the future of auto parts distribution in Algeria", "ourVision": "Our Vision", "visionTitle": "Solving fragmented distribution with one unified ecosystem", "visionDescription": "We envision a future where the Algerian auto parts industry operates on a single, intelligent platform that connects all stakeholders and eliminates inefficiencies.", "ourMission": "Our Mission", "missionTitle": "Empower Algerian industry and reduce import dependency", "missionDescription": "Our mission is to digitize operations across the auto parts supply chain, empower local manufacturers, and create a more efficient marketplace for all participants.", "differentiatorTitle": "We're not building software — we're building infrastructure", "differentiatorDescription": "AROUZ MARKET is more than just an app or a website. We're creating the digital infrastructure that will power the future of Algeria's auto parts industry.", "meetTheFounders": "Meet the Founders", "foundersDescription": "The team behind AROUZ MARKET combines expertise in robotics, computer science, and deep industry knowledge.", "roboticsEngineer": "Robotics Engineer (UCL)", "computerScientist": "Computer Scientist (University of Surrey)", "hamzaBio": "<PERSON><PERSON> brings expertise in robotics and automation to AROUZ MARKET, with a focus on creating intelligent systems that solve real-world problems.", "ahmedBio": "<PERSON>'s background in computer science and software engineering drives the technical vision behind AROUZ MARKET's innovative platform.", "algerianTeam": "We are proudly building a 100% Algerian product team.", "featuresTitle": "Platform Features", "featuresSubtitle": "Discover what makes AROUZ MARKET a game-changing platform for the auto parts industry", "featureHighlight": "Feature Highlight", "inventoryDataGridTitle": "Inventory Data Grid", "inventoryDataGridDescription": "Our intelligent inventory management system helps suppliers and merchants track products, manage stock levels, and gain valuable insights.", "aiPoweredAlerts": "AI-Power<PERSON>", "aiPoweredAlertsDescription": "Get notified about low stock, pricing opportunities, and market trends.", "advancedAnalytics": "Advanced Analytics", "advancedAnalyticsDescription": "Visualize sales data, inventory turnover, and performance metrics.", "csvImportExport": "CSV Import/Export", "csvImportExportDescription": "Easily import and export product data for seamless integration.", "inventoryPreview": "Inventory Data Grid Preview", "inventoryPreviewDescription": "Visual preview coming soon", "partMatchingTitle": "Part Matching Engine", "partMatchingDescription": "Our AI-powered matching engine ensures customers find the right parts for their specific vehicles, reducing returns and improving satisfaction.", "carModelSearch": "Car Model Search", "carModelSearchDescription": "Find parts by vehicle make, model, and year with precision.", "compatibilityCheck": "Compatibility Check", "compatibilityCheckDescription": "Verify part compatibility before purchase to reduce returns.", "qualityAssurance": "Quality Assurance", "qualityAssuranceDescription": "Ensure parts meet OEM specifications and quality standards.", "matchingPreview": "Part Matching Preview", "matchingPreviewDescription": "Visual preview coming soon", "moreFeatures": "More Powerful Features", "moreFeaturesDescription": "AROUZ MARKET offers a comprehensive suite of tools designed for the auto parts industry.", "merchantPanelTitle": "Merchant Panel", "merchantPanelDescription": "Complete control over product listings, pricing, stock levels, and returns.", "posIntegrationTitle": "POS Hardware Integration", "posIntegrationDescription": "Connect barcode scanners, cash drawers, and receipt printers for seamless operation.", "mobileAppTitle": "Mobile App", "mobileAppDescription": "Search for parts, check availability, and place orders from anywhere.", "receiptPrintingTitle": "Receipt Printing", "receiptPrintingDescription": "Generate professional receipts and invoices for customers.", "paymentProcessingTitle": "Payment Processing", "paymentProcessingDescription": "Secure payment processing with multiple payment options.", "deliveryTrackingTitle": "Delivery Tracking", "deliveryTrackingDescription": "Real-time tracking of orders and deliveries for merchants and customers.", "readyToJoin": "Ready to join AROUZ MARKET?", "readyToJoinDescription": "Sign up for our waitlist to be the first to know when we launch.", "roadmapTitle": "Launch Timeline", "roadmapSubtitle": "Our journey to revolutionizing Algeria's auto parts industry", "roadmapQ1Title": "Beta Testing with Suppliers", "roadmapQ1Description": "Initial platform testing with select suppliers to refine the inventory management system and data grid.", "roadmapQ2Title": "POS + Inventory Finalization", "roadmapQ2Description": "Completion of point-of-sale integration and finalization of the inventory management system.", "roadmapQ3Title": "Mobile Marketplace Launch", "roadmapQ3Description": "Public launch of the mobile marketplace app for consumers and merchants.", "roadmapQ4Title": "AI Matching Engine Release", "roadmapQ4Description": "Release of our advanced AI part matching engine for precise vehicle compatibility.", "current": "Current", "beyondRoadmap": "Beyond the Roadmap", "beyondRoadmapDescription": "Our vision extends far beyond our initial launch. Here's a glimpse of what's coming after our core platform is established.", "futureVision1Title": "National Distribution Network", "futureVision1Description": "Building a nationwide logistics network specifically optimized for auto parts delivery.", "futureVision2Title": "Manufacturing Insights", "futureVision2Description": "Advanced analytics platform for manufacturers to identify market gaps and opportunities.", "futureVision3Title": "International Expansion", "futureVision3Description": "Expanding the AROUZ MARKET ecosystem to neighboring countries in North Africa.", "bePartOfFuture": "Be part of the future", "bePartOfFutureDescription": "Join our waitlist today to be part of Algeria's auto parts revolution.", "joinWaitlistToday": "Join the waitlist today", "contactTitle": "Contact Us", "contactSubtitle": "Get in touch with the AROUZ MARKET team", "getInTouch": "Get in Touch", "getInTouchDescription": "Have questions about AROUZ MARKET? We're here to help. Reach out to our team using the contact information below or send us a message using the form.", "phone": "Phone", "enterPhone": "Enter your phone number", "message": "Message", "enterMessage": "Enter your message", "sendMessage": "Send Message", "sending": "Sending...", "messageSent": "Your message has been sent successfully!", "messageError": "There was an error sending your message. Please try again.", "businessHours": "Business Hours", "businessHoursDetails": "Sunday - Thursday: 9:00 AM - 5:00 PM", "mapPlaceholder": "Our Location", "mapPlaceholderDescription": "Interactive map coming soon"}, "validation": {"ratingRequired": "Rating Required", "ratingRequiredDescription": "Please select a star rating for your review", "reviewContentRequired": "Review Content Required", "reviewContentRequiredDescription": "Please provide either a title or review text", "reviewSubmissionFailed": "Failed to Submit Review", "reviewSubmissionFailedDescription": "Please try again"}, "footer": {"trackYourOrder": "Track Your Order", "shop": "SHOP", "products": "PRODUCTS", "help": "HELP", "aboutUs": "ABOUT US", "services": "SERVICES", "basket": "Basket", "tyres": "<PERSON><PERSON>", "oilsFluids": "Oils & Fluids", "batteries": "Batteries", "filters": "Filters", "trackMyOrder": "Track My Order", "contactUs": "Contact Us", "myAccount": "My Account", "faq": "FAQ", "aboutArouz": "About AROUZ", "careers": "Careers", "investorRelations": "Investor Relations", "corporateResponsibility": "Corporate Responsibility", "deliveryInfo": "Delivery Information", "returnPolicy": "Return Policy", "warranty": "Warranty", "installationServices": "Installation Services", "followUs": "Follow Us", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "sitemap": "Sitemap", "poweredBy": "Powered by"}}