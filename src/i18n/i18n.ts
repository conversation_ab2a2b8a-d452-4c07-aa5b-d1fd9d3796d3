
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import arTranslation from './locales/ar/translation.json';
import enTranslation from './locales/en/translation.json';
import frTranslation from './locales/fr/translation.json';

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      ar: {
        translation: arTranslation,
      },
      en: {
        translation: enTranslation,
      },
      fr: {
        translation: frTranslation,
      },
    },
    lng: localStorage.getItem('i18nextLng') || 'en', // Use stored language or default to English
    fallbackLng: 'en', // Fallback to English
    interpolation: {
      escapeValue: false, // React already escapes by default
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    returnNull: false, // Return empty string instead of null for missing translations
    returnEmptyString: false, // Return key instead of empty string
    parseMissingKeyHandler: (key) => {
      console.warn(`Missing translation key: ${key}`);
      return key.split('.').pop() || key; // Return the last part of the key as fallback
    },
  });

// Function to set document direction based on language
export const setDocumentDirection = (language: string) => {
  const isRTL = language === 'ar';
  document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
  document.documentElement.lang = language;
  
  // Update RTL/LTR classes with a cleaner approach
  if (isRTL) {
    document.documentElement.classList.add('rtl');
    document.documentElement.classList.remove('ltr');
    // Force text alignment to ensure consistency
    document.documentElement.style.textAlign = 'right';
  } else {
    document.documentElement.classList.remove('rtl');
    document.documentElement.classList.add('ltr');
    // Reset text alignment
    document.documentElement.style.textAlign = '';
  }
  
  // Force text isolation to prevent incorrect text joining
  const buttons = document.querySelectorAll('button');
  buttons.forEach(button => {
    button.style.unicodeBidi = 'isolate';
  });
};

// Set initial direction
setDocumentDirection(i18n.language);

// Listen for language changes and update direction
i18n.on('languageChanged', (lng) => {
  setDocumentDirection(lng);
});

export default i18n;
