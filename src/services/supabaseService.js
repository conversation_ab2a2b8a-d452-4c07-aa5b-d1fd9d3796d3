// Supabase service for securely storing and retrieving vehicle data
// This service will be used to interact with Supabase for vehicle data

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client (these would be environment variables in production)
// For now, we'll use placeholder values
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

// Check if we have valid credentials before creating the client
const hasValidCredentials = supabaseUrl && supabaseAnonKey && supabaseUrl.includes('supabase.co');

// Create Supabase client only if we have valid credentials
const supabase = hasValidCredentials ? createClient(supabaseUrl, supabaseAnonKey) : null;

/**
 * Store vehicle data in Supabase
 * @param {string} vehicleType - The type of vehicle (car, motorcycle, truck)
 * @param {Array} vehicleData - The vehicle data to store
 * @returns {Promise<Object>} - Result of the operation
 */
export const storeVehicleData = async (vehicleType, vehicleData) => {
  try {
    // Check if Supabase is available
    if (!supabase) {
      console.warn('Supabase client not initialized. Cannot store vehicle data.');
      return { success: false, error: 'Supabase client not initialized' };
    }

    // Store the data in the vehicle_data table
    const { data, error } = await supabase
      .from('vehicle_data')
      .upsert({
        type: vehicleType,
        data: vehicleData,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'type'
      });

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error storing vehicle data:', error);
    return { success: false, error };
  }
};

/**
 * Retrieve vehicle data from Supabase
 * @param {string} vehicleType - The type of vehicle (car, motorcycle, truck)
 * @returns {Promise<Array>} - The vehicle data
 */
export const getVehicleData = async (vehicleType) => {
  try {
    // Check if Supabase is available
    if (!supabase) {
      console.warn('Supabase client not initialized. Using local data instead.');
      // Import the local data dynamically to avoid circular dependencies
      const { default: carsData } = await import('./data/cars.json');
      return vehicleType === 'car' ? carsData : [];
    }

    // Get the data from the vehicle_data table
    const { data, error } = await supabase
      .from('vehicle_data')
      .select('data')
      .eq('type', vehicleType)
      .single();

    if (error) {
      // If there's an error (like the table doesn't exist), fall back to local data
      console.warn('Error fetching from Supabase. Using local data instead:', error.message);
      const { default: carsData } = await import('./data/cars.json');
      return vehicleType === 'car' ? carsData : [];
    }

    return data?.data || [];
  } catch (error) {
    console.error('Error retrieving vehicle data:', error);
    // Fall back to local data
    try {
      const { default: carsData } = await import('./data/cars.json');
      return vehicleType === 'car' ? carsData : [];
    } catch (importError) {
      console.error('Error importing local data:', importError);
      return [];
    }
  }
};

/**
 * Initialize the vehicle data tables in Supabase
 * This would typically be run once during setup
 * @returns {Promise<boolean>} - Success status
 */
export const initializeVehicleDataTables = async () => {
  try {
    // Check if Supabase is available
    if (!supabase) {
      console.warn('Supabase client not initialized. Cannot initialize tables.');
      return false;
    }

    // Check if the table exists
    const { error: checkError } = await supabase
      .from('vehicle_data')
      .select('id')
      .limit(1);

    // If the table doesn't exist, we'll get an error
    if (checkError) {
      // Create the table using SQL (requires admin privileges)
      // In a real implementation, this would be done through migrations
      console.error('Table does not exist. Please create it manually or use migrations.');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error initializing vehicle data tables:', error);
    return false;
  }
};

export default supabase;
