import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types
export interface AdminOrderWithItems {
  id: string;
  order_number: string;
  consumer_name: string;
  consumer_phone: string;
  status: string;
  total_amount: number;
  delivery_wilaya: string;
  delivery_address: string;
  google_maps_url: string;
  created_at: string;
  confirmed_at: string;
  order_items: AdminOrderItem[];
  has_shipment: boolean;
  assigned_company?: string;
  shipment_id?: string;
}

export interface AdminOrderItem {
  id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  supplier_name: string;
  supplier_phone: string;
  supplier_city: string;
  supplier_wilaya: string;
  marketplace_section: string;
}

export interface ShippingCompany {
  id: string;
  company_name: string;
  coverage_areas: string[];
  service_types: string[];
  is_active: boolean;
  contact_phone?: string;
  contact_email?: string;
}

export interface AdminOrderStats {
  total_orders: number;
  pending_orders: number;
  confirmed_orders: number;
  shipped_orders: number;
  delivered_orders: number;
  unassigned_orders: number;
  total_revenue: number;
  this_month_revenue: number;
}

// Get all orders for admin management
export const getAdminOrders = async (filters?: {
  status?: string;
  wilaya?: string;
  search?: string;
  limit?: number;
  offset?: number;
}): Promise<{ success: boolean; orders?: AdminOrderWithItems[]; error?: string; count?: number }> => {
  try {
    console.log('📊 [ADMIN_ORDER_SERVICE] Fetching orders for admin...', filters);

    // Build query for orders with items
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          product_name,
          quantity,
          unit_price,
          total_price,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section
        ),
        shipments (
          id,
          shipping_company_id,
          company_name,
          status
        )
      `);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.wilaya) {
      query = query.eq('delivery_wilaya', filters.wilaya);
    }

    if (filters?.search) {
      query = query.or(`order_number.ilike.%${filters.search}%,consumer_name.ilike.%${filters.search}%,consumer_phone.ilike.%${filters.search}%`);
    }

    // Apply pagination
    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
    }

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: orders, error, count } = await query;

    if (error) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Database error:', error);
      return { success: false, error: 'Failed to fetch orders' };
    }

    // Process orders to include shipment information
    const processedOrders: AdminOrderWithItems[] = (orders || []).map(order => ({
      ...order,
      order_items: order.order_items || [],
      has_shipment: order.shipments && order.shipments.length > 0,
      assigned_company: order.shipments?.[0]?.company_name,
      shipment_id: order.shipments?.[0]?.id
    }));

    console.log('✅ [ADMIN_ORDER_SERVICE] Successfully fetched orders:', processedOrders.length);
    return { success: true, orders: processedOrders, count: count || 0 };

  } catch (error) {
    console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching orders:', error);
    return { success: false, error: 'Failed to fetch orders' };
  }
};

// Get shipping companies
export const getShippingCompanies = async (): Promise<{ success: boolean; companies?: ShippingCompany[]; error?: string }> => {
  try {
    console.log('🚚 [ADMIN_ORDER_SERVICE] Fetching shipping companies...');

    const { data: companies, error } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('is_active', true)
      .order('company_name');

    if (error) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching shipping companies:', error);
      return { success: false, error: 'Failed to fetch shipping companies' };
    }

    console.log('✅ [ADMIN_ORDER_SERVICE] Successfully fetched shipping companies:', companies?.length);
    return { success: true, companies: companies || [] };

  } catch (error) {
    console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching shipping companies:', error);
    return { success: false, error: 'Failed to fetch shipping companies' };
  }
};

// Assign order to shipping company
export const assignOrderToShipping = async (
  orderId: string, 
  shippingCompanyId: string
): Promise<{ success: boolean; shipment?: any; error?: string }> => {
  try {
    console.log('🚚 [ADMIN_ORDER_SERVICE] Assigning order to shipping company...', { orderId, shippingCompanyId });

    // First, get the order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya
        )
      `)
      .eq('id', orderId)
      .single();

    if (orderError || !order) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching order:', orderError);
      return { success: false, error: 'Order not found' };
    }

    // Get shipping company details
    const { data: shippingCompany, error: companyError } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('id', shippingCompanyId)
      .single();

    if (companyError || !shippingCompany) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching shipping company:', companyError);
      return { success: false, error: 'Shipping company not found' };
    }

    // Check if order already has a shipment
    const { data: existingShipment } = await supabase
      .from('shipments')
      .select('id')
      .eq('order_id', orderId)
      .single();

    if (existingShipment) {
      return { success: false, error: 'Order already has a shipment assigned' };
    }

    // Prepare supplier contact information
    const supplierContactInfo = {
      suppliers: order.order_items.map((item: any) => ({
        name: item.supplier_name,
        phone: item.supplier_phone,
        city: item.supplier_city,
        wilaya: item.supplier_wilaya
      }))
    };

    // Create shipment record
    const { data: shipment, error: shipmentError } = await supabase
      .from('shipments')
      .insert({
        order_id: orderId,
        shipping_company_id: shippingCompanyId,
        company_name: shippingCompany.company_name,
        status: 'assigned',
        assigned_by: 'arouz_market',
        delivery_cost: 500.00, // Default delivery cost - can be updated later
        estimated_delivery_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 days from now
        supplier_contact_info: supplierContactInfo,
        assigned_at: new Date().toISOString()
      })
      .select()
      .single();

    if (shipmentError) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Error creating shipment:', shipmentError);
      return { success: false, error: 'Failed to create shipment record' };
    }

    console.log('✅ [ADMIN_ORDER_SERVICE] Successfully assigned order to shipping company');
    return { success: true, shipment };

  } catch (error) {
    console.error('❌ [ADMIN_ORDER_SERVICE] Error assigning order:', error);
    return { success: false, error: 'Failed to assign order to shipping company' };
  }
};

// Get admin dashboard statistics
export const getAdminOrderStats = async (): Promise<{ success: boolean; stats?: AdminOrderStats; error?: string }> => {
  try {
    console.log('📊 [ADMIN_ORDER_SERVICE] Fetching admin statistics...');

    // Use the database function for better performance
    const { data, error } = await supabase.rpc('get_admin_dashboard_stats');

    if (error) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching admin stats:', error);
      return { success: false, error: 'Failed to fetch statistics' };
    }

    const stats: AdminOrderStats = {
      total_orders: data.total_orders || 0,
      pending_orders: data.pending_orders || 0,
      confirmed_orders: data.confirmed_orders || 0,
      shipped_orders: data.shipped_orders || 0,
      delivered_orders: data.delivered_orders || 0,
      unassigned_orders: data.unassigned_orders || 0,
      total_revenue: data.total_revenue || 0,
      this_month_revenue: data.this_month_revenue || 0
    };

    console.log('✅ [ADMIN_ORDER_SERVICE] Successfully fetched statistics');
    return { success: true, stats };

  } catch (error) {
    console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching statistics:', error);
    return { success: false, error: 'Failed to fetch statistics' };
  }
};

// Get orders by shipping company (for shipping portal)
export const getOrdersByShippingCompany = async (
  shippingCompanyId: string
): Promise<{ success: boolean; orders?: any[]; error?: string }> => {
  try {
    console.log('🚚 [ADMIN_ORDER_SERVICE] Fetching orders for shipping company:', shippingCompanyId);

    const { data: shipments, error } = await supabase
      .from('shipments')
      .select(`
        *,
        orders (
          *,
          order_items (
            id,
            product_name,
            quantity,
            unit_price,
            total_price,
            supplier_name,
            supplier_phone,
            supplier_city,
            supplier_wilaya
          )
        )
      `)
      .eq('shipping_company_id', shippingCompanyId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching shipping orders:', error);
      return { success: false, error: 'Failed to fetch orders' };
    }

    console.log('✅ [ADMIN_ORDER_SERVICE] Successfully fetched shipping orders:', shipments?.length);
    return { success: true, orders: shipments || [] };

  } catch (error) {
    console.error('❌ [ADMIN_ORDER_SERVICE] Error fetching shipping orders:', error);
    return { success: false, error: 'Failed to fetch orders' };
  }
};
