/**
 * Wishlist Service - Consumer Wishlist Management
 * 
 * Handles all consumer wishlist operations with phone-based authentication
 * 🎯 CRITICAL: Preserves existing authentication system - integrates with phone auth
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types
export interface WishlistItem {
  id: string;
  consumer_phone: string;
  product_id: string;
  product_name: string;
  product_image?: string;
  product_price?: number;
  product_manufacturer?: string;
  added_at: string;
  notes?: string;
  priority: number;
}

export interface AddToWishlistData {
  product_id: string;
  product_name: string;
  product_image?: string;
  product_price?: number;
  product_manufacturer?: string;
  notes?: string;
  priority?: number;
}

/**
 * Get current consumer's phone from authentication
 */
async function getCurrentConsumerPhone(): Promise<string | null> {
  try {
    // Check for phone authentication session first
    const phoneSession = localStorage.getItem('phone_auth_session');
    if (phoneSession) {
      const session = JSON.parse(phoneSession);
      return session.user?.phone || session.profile?.phone || null;
    }

    // Fallback to regular auth if needed
    const { data: { user } } = await supabase.auth.getUser();
    if (user?.phone) {
      return user.phone;
    }

    return null;
  } catch (error) {
    console.error('Error getting consumer phone:', error);
    return null;
  }
}

/**
 * Add product to wishlist
 */
export async function addToWishlist(productData: AddToWishlistData): Promise<{ success: boolean; item?: WishlistItem; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { data, error } = await supabase
      .from('consumer_wishlists')
      .insert({
        consumer_phone: phone,
        product_id: productData.product_id,
        product_name: productData.product_name,
        product_image: productData.product_image,
        product_price: productData.product_price,
        product_manufacturer: productData.product_manufacturer,
        notes: productData.notes,
        priority: productData.priority || 1,
      })
      .select()
      .single();

    if (error) {
      // Handle duplicate entry error gracefully
      if (error.code === '23505') {
        return { success: false, error: 'Product is already in your wishlist' };
      }
      console.error('Error adding to wishlist:', error);
      return { success: false, error: 'Failed to add to wishlist' };
    }

    return { success: true, item: data };
  } catch (error) {
    console.error('Error in addToWishlist:', error);
    return { success: false, error: 'Failed to add to wishlist' };
  }
}

/**
 * Remove product from wishlist
 */
export async function removeFromWishlist(productId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { error } = await supabase
      .from('consumer_wishlists')
      .delete()
      .eq('consumer_phone', phone)
      .eq('product_id', productId);

    if (error) {
      console.error('Error removing from wishlist:', error);
      return { success: false, error: 'Failed to remove from wishlist' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in removeFromWishlist:', error);
    return { success: false, error: 'Failed to remove from wishlist' };
  }
}

/**
 * Check if product is in wishlist
 */
export async function isProductInWishlist(productId: string): Promise<{ success: boolean; isInWishlist?: boolean; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: true, isInWishlist: false };
    }

    const { data, error } = await supabase
      .from('consumer_wishlists')
      .select('id')
      .eq('consumer_phone', phone)
      .eq('product_id', productId)
      .limit(1);

    if (error) {
      console.error('Error checking wishlist status:', error);
      return { success: false, error: 'Failed to check wishlist status' };
    }

    return { success: true, isInWishlist: (data && data.length > 0) };
  } catch (error) {
    console.error('Error in isProductInWishlist:', error);
    return { success: false, error: 'Failed to check wishlist status' };
  }
}

/**
 * Get consumer's wishlist
 */
export async function getConsumerWishlist(): Promise<{ success: boolean; items?: WishlistItem[]; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { data, error } = await supabase
      .from('consumer_wishlists')
      .select('*')
      .eq('consumer_phone', phone)
      .order('added_at', { ascending: false });

    if (error) {
      console.error('Error fetching wishlist:', error);
      return { success: false, error: 'Failed to fetch wishlist' };
    }

    return { success: true, items: data || [] };
  } catch (error) {
    console.error('Error in getConsumerWishlist:', error);
    return { success: false, error: 'Failed to fetch wishlist' };
  }
}

/**
 * Get wishlist count for consumer
 */
export async function getWishlistCount(): Promise<{ success: boolean; count?: number; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: true, count: 0 };
    }

    const { count, error } = await supabase
      .from('consumer_wishlists')
      .select('*', { count: 'exact', head: true })
      .eq('consumer_phone', phone);

    if (error) {
      console.error('Error getting wishlist count:', error);
      return { success: false, error: 'Failed to get wishlist count' };
    }

    return { success: true, count: count || 0 };
  } catch (error) {
    console.error('Error in getWishlistCount:', error);
    return { success: false, error: 'Failed to get wishlist count' };
  }
}

/**
 * Update wishlist item
 */
export async function updateWishlistItem(itemId: string, updateData: Partial<AddToWishlistData>): Promise<{ success: boolean; item?: WishlistItem; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { data, error } = await supabase
      .from('consumer_wishlists')
      .update(updateData)
      .eq('id', itemId)
      .eq('consumer_phone', phone) // Ensure user can only update their own items
      .select()
      .single();

    if (error) {
      console.error('Error updating wishlist item:', error);
      return { success: false, error: 'Failed to update wishlist item' };
    }

    return { success: true, item: data };
  } catch (error) {
    console.error('Error in updateWishlistItem:', error);
    return { success: false, error: 'Failed to update wishlist item' };
  }
}

/**
 * Clear entire wishlist
 */
export async function clearWishlist(): Promise<{ success: boolean; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { error } = await supabase
      .from('consumer_wishlists')
      .delete()
      .eq('consumer_phone', phone);

    if (error) {
      console.error('Error clearing wishlist:', error);
      return { success: false, error: 'Failed to clear wishlist' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in clearWishlist:', error);
    return { success: false, error: 'Failed to clear wishlist' };
  }
}

/**
 * Toggle product in wishlist (add if not present, remove if present)
 */
export async function toggleWishlist(productData: AddToWishlistData): Promise<{ success: boolean; action?: 'added' | 'removed'; error?: string }> {
  try {
    const { success: checkSuccess, isInWishlist } = await isProductInWishlist(productData.product_id);
    
    if (!checkSuccess) {
      return { success: false, error: 'Failed to check wishlist status' };
    }

    if (isInWishlist) {
      const result = await removeFromWishlist(productData.product_id);
      return { ...result, action: 'removed' };
    } else {
      const result = await addToWishlist(productData);
      return { success: result.success, action: 'added', error: result.error };
    }
  } catch (error) {
    console.error('Error in toggleWishlist:', error);
    return { success: false, error: 'Failed to toggle wishlist' };
  }
}
