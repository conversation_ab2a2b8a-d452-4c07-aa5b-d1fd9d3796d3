/**
 * SMS AUTHENTICATION DEBUGGER
 *
 * Comprehensive debugging tool to test SMS authentication via Supabase Edge Functions
 * Tests both Dexatel integration and Supabase function deployment
 */

export interface DebugResult {
  step: string;
  success: boolean;
  message: string;
  data?: any;
  timestamp: string;
}

export class DexatelDebugger {
  private results: DebugResult[] = [];
  private apiKey: string;
  private supabaseUrl: string;
  private supabaseAnonKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_DEXATEL_API_KEY || '';
    this.supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
    this.supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
  }

  private addResult(step: string, success: boolean, message: string, data?: any) {
    const result: DebugResult = {
      step,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    console.log(`[${step}] ${success ? '✅' : '❌'} ${message}`, data || '');
  }

  async runComprehensiveTest(): Promise<DebugResult[]> {
    this.results = [];

    // Step 1: Environment Check
    this.addResult(
      'Environment Check',
      !!(this.apiKey && this.supabaseUrl && this.supabaseAnonKey),
      this.getEnvironmentMessage(),
      {
        hasApiKey: !!this.apiKey,
        hasSupabaseUrl: !!this.supabaseUrl,
        hasSupabaseAnonKey: !!this.supabaseAnonKey,
        keyLength: this.apiKey.length
      }
    );

    if (!this.supabaseUrl || !this.supabaseAnonKey) {
      this.addResult('CRITICAL ERROR', false, 'Cannot proceed without Supabase configuration', {});
      return this.results;
    }

    // Step 2: Supabase Edge Function Test
    await this.testSupabaseEdgeFunction();

    // Step 3: SMS OTP Send Test
    await this.testSMSOTPSend();

    // Step 4: OTP Verification Test
    await this.testOTPVerification();

    // Step 5: Different Phone Formats Test
    await this.testPhoneFormats();

    return this.results;
  }

  private getEnvironmentMessage(): string {
    const missing = [];
    if (!this.apiKey) missing.push('Dexatel API Key');
    if (!this.supabaseUrl) missing.push('Supabase URL');
    if (!this.supabaseAnonKey) missing.push('Supabase Anon Key');

    if (missing.length === 0) {
      return `All environment variables configured correctly`;
    } else {
      return `Missing: ${missing.join(', ')}`;
    }
  }

  private async testSupabaseEdgeFunction() {
    try {
      // Test basic connectivity to Supabase Edge Functions
      const response = await fetch(`${this.supabaseUrl}/functions/v1/send-sms-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.supabaseAnonKey}`,
          'apikey': this.supabaseAnonKey
        },
        body: JSON.stringify({
          phone: '+213555123456',
          test: true // Test mode
        })
      });

      const responseText = await response.text();
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      this.addResult(
        'Supabase Edge Function',
        response.ok,
        `Edge Function ${response.ok ? 'accessible' : 'failed'} (Status: ${response.status})`,
        {
          status: response.status,
          response: responseData,
          headers: Object.fromEntries(response.headers.entries())
        }
      );
    } catch (error: any) {
      this.addResult(
        'Supabase Edge Function',
        false,
        `Edge Function test failed: ${error.message}`,
        { error: error.toString() }
      );
    }
  }

  private async testSMSOTPSend() {
    try {
      const payload = {
        phone: '+213555123456'
      };

      const response = await fetch(`${this.supabaseUrl}/functions/v1/send-sms-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.supabaseAnonKey}`,
          'apikey': this.supabaseAnonKey
        },
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      this.addResult(
        'SMS OTP Send Test',
        response.ok,
        `SMS OTP ${response.ok ? 'sent successfully' : 'failed to send'} (Status: ${response.status})`,
        {
          payload,
          status: response.status,
          response: responseData,
          headers: Object.fromEntries(response.headers.entries())
        }
      );
    } catch (error: any) {
      this.addResult(
        'SMS OTP Send Test',
        false,
        `SMS OTP send test failed: ${error.message}`,
        { error: error.toString() }
      );
    }
  }

  private async testOTPVerification() {
    try {
      const payload = {
        phone: '+213555123456',
        otp: '123456' // Test OTP
      };

      const response = await fetch(`${this.supabaseUrl}/functions/v1/verify-sms-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.supabaseAnonKey}`,
          'apikey': this.supabaseAnonKey
        },
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      this.addResult(
        'OTP Verification Test',
        response.status === 200 || response.status === 400, // 400 is expected for invalid OTP
        `OTP Verification ${response.ok ? 'working' : 'endpoint accessible'} (Status: ${response.status})`,
        {
          payload,
          status: response.status,
          response: responseData,
          headers: Object.fromEntries(response.headers.entries())
        }
      );
    } catch (error: any) {
      this.addResult(
        'OTP Verification Test',
        false,
        `OTP verification test failed: ${error.message}`,
        { error: error.toString() }
      );
    }
  }

  private async testPhoneFormats() {
    const phoneFormats = [
      '+213555123456',
      '+213 555 123 456',
      '213555123456',
      '0555123456'
    ];

    for (const phone of phoneFormats) {
      try {
        const payload = {
          phone: phone
        };

        const response = await fetch(`${this.supabaseUrl}/functions/v1/send-sms-otp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.supabaseAnonKey}`,
            'apikey': this.supabaseAnonKey
          },
          body: JSON.stringify(payload)
        });

        const responseText = await response.text();
        let responseData;
        try {
          responseData = JSON.parse(responseText);
        } catch {
          responseData = responseText;
        }

        this.addResult(
          `Phone Format Test (${phone})`,
          response.ok,
          `Phone format ${phone} ${response.ok ? 'accepted' : 'rejected'} (Status: ${response.status})`,
          {
            phone,
            payload,
            status: response.status,
            response: responseData
          }
        );
      } catch (error: any) {
        this.addResult(
          `Phone Format Test (${phone})`,
          false,
          `Phone format test failed: ${error.message}`,
          { phone, error: error.toString() }
        );
      }
    }
  }

  getResults(): DebugResult[] {
    return this.results;
  }

  getFailures(): DebugResult[] {
    return this.results.filter(r => !r.success);
  }

  getSummary(): { total: number; passed: number; failed: number; criticalIssues: string[] } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.success).length;
    const failed = total - passed;

    const criticalIssues: string[] = [];

    // Check for critical issues
    if (!this.supabaseUrl || !this.supabaseAnonKey) {
      criticalIssues.push('Supabase configuration missing');
    }

    if (!this.apiKey) {
      criticalIssues.push('Dexatel API Key not configured');
    }

    const edgeFunctionFailure = this.results.find(r => r.step === 'Supabase Edge Function' && !r.success);
    if (edgeFunctionFailure) {
      criticalIssues.push('Supabase Edge Function not accessible');
    }

    const smsFailure = this.results.find(r => r.step === 'SMS OTP Send Test' && !r.success);
    if (smsFailure) {
      criticalIssues.push('SMS OTP sending failed');
    }

    return { total, passed, failed, criticalIssues };
  }
}

// Export singleton instance
export const dexatelDebugger = new DexatelDebugger();
