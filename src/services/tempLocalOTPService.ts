/**
 * Temporary Local OTP Service
 * 
 * This is a temporary solution to test OTP verification locally
 * while the Edge Functions are being deployed.
 * 
 * IMPORTANT: This should be replaced with proper Edge Function calls
 * once Docker is set up and functions are deployed.
 */

const DEXATEL_API_KEY = '4595fd59fb0655b92b79c6fa00fb5d36';

// Temporary in-memory storage for OTP codes (for testing only)
const tempOTPStorage = new Map<string, {
  phone: string;
  code: string;
  expiresAt: Date;
  messageId: string;
}>();

/**
 * Send OTP via Dexatel messaging API (temporary implementation)
 */
export async function tempSendOTP(phone: string): Promise<{
  success: boolean;
  data?: { id: string; expires_at: string };
  error?: string;
}> {
  try {
    console.log('🔐 [TEMP] Sending OTP to:', phone);
    
    // Generate 6-digit OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    console.log('🔢 [TEMP] Generated OTP:', otpCode);
    
    // Send via Dexatel messaging API
    const response = await fetch('https://api.dexatel.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Dexatel-Key': DEXATEL_API_KEY,
      },
      body: JSON.stringify({
        data: {
          from: "AROUZ",
          to: phone,
          text: `Your AROUZ MARKET verification code is: ${otpCode}. Valid for 5 minutes.`
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [TEMP] Dexatel error:', response.status, errorText);
      return {
        success: false,
        error: `SMS service error: ${response.status}`
      };
    }

    const data = await response.json();
    const messageId = data.data?.id || `temp_${Date.now()}`;
    
    // Store in temporary storage
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    tempOTPStorage.set(messageId, {
      phone,
      code: otpCode,
      expiresAt,
      messageId
    });
    
    console.log('✅ [TEMP] OTP sent and stored:', { messageId, expiresAt });
    
    return {
      success: true,
      data: {
        id: messageId,
        expires_at: expiresAt.toISOString()
      }
    };
    
  } catch (error) {
    console.error('❌ [TEMP] Error sending OTP:', error);
    return {
      success: false,
      error: 'Failed to send verification code'
    };
  }
}

/**
 * Verify OTP code (temporary implementation)
 */
export async function tempVerifyOTP(verificationId: string, code: string): Promise<{
  success: boolean;
  data?: { id: string; status: string; verified_at: string };
  error?: string;
}> {
  try {
    console.log('🔐 [TEMP] Verifying OTP:', { verificationId, code: '******' });
    
    // Get from temporary storage
    const otpData = tempOTPStorage.get(verificationId);
    
    if (!otpData) {
      console.error('❌ [TEMP] OTP not found:', verificationId);
      return {
        success: false,
        error: 'Invalid or expired verification code'
      };
    }
    
    // Check expiration
    const now = new Date();
    if (now > otpData.expiresAt) {
      console.error('❌ [TEMP] OTP expired:', { now, expiresAt: otpData.expiresAt });
      tempOTPStorage.delete(verificationId);
      return {
        success: false,
        error: 'Verification code has expired'
      };
    }
    
    // Check code
    if (otpData.code !== code) {
      console.error('❌ [TEMP] Invalid OTP code');
      return {
        success: false,
        error: 'Invalid verification code'
      };
    }
    
    // Success! Remove from storage
    tempOTPStorage.delete(verificationId);
    
    console.log('✅ [TEMP] OTP verified successfully');
    
    return {
      success: true,
      data: {
        id: verificationId,
        status: 'verified',
        verified_at: new Date().toISOString()
      }
    };
    
  } catch (error) {
    console.error('❌ [TEMP] Error verifying OTP:', error);
    return {
      success: false,
      error: 'Failed to verify code'
    };
  }
}

/**
 * Clean up expired OTPs (temporary implementation)
 */
export function tempCleanupExpiredOTPs(): void {
  const now = new Date();
  for (const [id, data] of tempOTPStorage.entries()) {
    if (now > data.expiresAt) {
      tempOTPStorage.delete(id);
    }
  }
}

// Clean up every minute
setInterval(tempCleanupExpiredOTPs, 60 * 1000);
