/**
 * Dexatel SMS OTP Service
 * 
 * Production-ready SMS OTP service using Dexatel API
 * Implements secure OTP generation, sending, and verification
 * with comprehensive error handling and rate limiting
 */

import { supabase } from '@/services/authService';

// Types based on official Dexatel API documentation
export interface DexatelVerificationRequest {
  to: string;
  channel: 'sms';
  template?: string;
  locale?: string;
  sender?: string;
}

export interface DexatelVerificationResponse {
  id: string;
  to: string;
  channel: string;
  status: string;
  created_at: string;
  expires_at?: string;
}

export interface DexatelVerifyRequest {
  verification_id: string;
  code: string;
}

export interface DexatelVerifyResponse {
  id: string;
  status: string;
  verified_at?: string;
}

export interface DexatelError {
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

// Constants
const DEXATEL_BASE_URL = 'https://api.dexatel.com/v1';
const OTP_EXPIRY_MINUTES = 5;
const MAX_ATTEMPTS_PER_PHONE = 3;
const RATE_LIMIT_WINDOW_MINUTES = 60;

// Rate limiting storage (in production, use Redis or database)
const rateLimitStore = new Map<string, { attempts: number; windowStart: number }>();

/**
 * Validate Algerian phone number format
 */
export function validateAlgerianPhone(phone: string): { isValid: boolean; formatted: string; error?: string } {
  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '');
  
  // Check if it starts with 213 (Algeria country code)
  if (digitsOnly.startsWith('213')) {
    // Should be 213 + 9 digits = 12 digits total
    if (digitsOnly.length === 12) {
      const formatted = `+${digitsOnly}`;
      return { isValid: true, formatted };
    }
  }
  
  // Check if it starts with 0 (local format)
  if (digitsOnly.startsWith('0')) {
    // Should be 0 + 9 digits = 10 digits total
    if (digitsOnly.length === 10) {
      // Convert to international format: +213 + remove leading 0
      const formatted = `+213${digitsOnly.substring(1)}`;
      return { isValid: true, formatted };
    }
  }
  
  // Check if it's just 9 digits (without country code or leading 0)
  if (digitsOnly.length === 9) {
    const formatted = `+213${digitsOnly}`;
    return { isValid: true, formatted };
  }
  
  return {
    isValid: false,
    formatted: phone,
    error: 'Invalid Algerian phone number. Must be in format +213XXXXXXXXX or 0XXXXXXXXX'
  };
}

/**
 * Check rate limiting for phone number
 */
function checkRateLimit(phone: string): { allowed: boolean; remainingAttempts: number; resetTime?: number } {
  const now = Date.now();
  const windowMs = RATE_LIMIT_WINDOW_MINUTES * 60 * 1000;
  
  const record = rateLimitStore.get(phone);
  
  if (!record) {
    // First attempt
    rateLimitStore.set(phone, { attempts: 1, windowStart: now });
    return { allowed: true, remainingAttempts: MAX_ATTEMPTS_PER_PHONE - 1 };
  }
  
  // Check if window has expired
  if (now - record.windowStart > windowMs) {
    // Reset window
    rateLimitStore.set(phone, { attempts: 1, windowStart: now });
    return { allowed: true, remainingAttempts: MAX_ATTEMPTS_PER_PHONE - 1 };
  }
  
  // Check if limit exceeded
  if (record.attempts >= MAX_ATTEMPTS_PER_PHONE) {
    const resetTime = record.windowStart + windowMs;
    return { allowed: false, remainingAttempts: 0, resetTime };
  }
  
  // Increment attempts
  record.attempts++;
  rateLimitStore.set(phone, record);
  
  return { allowed: true, remainingAttempts: MAX_ATTEMPTS_PER_PHONE - record.attempts };
}

/**
 * Store OTP verification record in database
 */
async function storeOTPRecord(id: string, phone: string, expiresAt: Date): Promise<void> {
  const { error } = await supabase
    .from('otp_verifications')
    .insert({
      id,
      phone,
      status: 'pending',
      expires_at: expiresAt.toISOString(),
      created_at: new Date().toISOString()
    });
    
  if (error) {
    console.error('Error storing OTP record:', error);
    throw new Error('Failed to store OTP verification record');
  }
}

/**
 * Update OTP verification status
 */
async function updateOTPStatus(id: string, status: 'verified' | 'failed' | 'expired', verifiedAt?: Date): Promise<void> {
  const updateData: any = { status };
  if (verifiedAt) {
    updateData.verified_at = verifiedAt.toISOString();
  }
  
  const { error } = await supabase
    .from('otp_verifications')
    .update(updateData)
    .eq('id', id);
    
  if (error) {
    console.error('Error updating OTP status:', error);
    throw new Error('Failed to update OTP verification status');
  }
}

/**
 * Send OTP via Supabase Edge Function (CORS-Safe Implementation)
 */
export async function sendOTP(phone: string): Promise<{ success: boolean; data?: DexatelVerificationResponse; error?: string; rateLimitInfo?: any }> {
  try {
    // Validate phone number
    const validation = validateAlgerianPhone(phone);
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    const formattedPhone = validation.formatted;

    // Check rate limiting
    const rateLimit = checkRateLimit(formattedPhone);
    if (!rateLimit.allowed) {
      const resetTime = new Date(rateLimit.resetTime!);
      return {
        success: false,
        error: `Too many attempts. Try again after ${resetTime.toLocaleTimeString()}`,
        rateLimitInfo: {
          remainingAttempts: 0,
          resetTime: resetTime.toISOString()
        }
      };
    }

    // Use Supabase Edge Function for OTP sending
    console.log('🔐 Sending OTP via Supabase Edge Function:', { to: formattedPhone });

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Supabase configuration missing');
      return { success: false, error: 'SMS service not configured' };
    }

    const response = await fetch(`${supabaseUrl}/functions/v1/send-sms-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'apikey': supabaseAnonKey
      },
      body: JSON.stringify({
        phone: formattedPhone
      })
    });

    console.log('📡 Supabase Edge Function response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Supabase Edge Function error:', errorText);

      let errorMessage = 'Failed to send SMS';
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.error || errorMessage;
      } catch (e) {
        errorMessage = `HTTP ${response.status}: ${errorText}`;
      }

      return {
        success: false,
        error: errorMessage,
        rateLimitInfo: {
          remainingAttempts: rateLimit.remainingAttempts,
          resetTime: rateLimit.resetTime
        }
      };
    }

    const result = await response.json();
    console.log('✅ SMS OTP sent successfully:', result);

    if (result.success && result.data) {
      // Rate limiting is already handled by checkRateLimit function above

      return {
        success: true,
        data: result.data,
        rateLimitInfo: {
          remainingAttempts: rateLimit.remainingAttempts - 1,
          resetTime: rateLimit.resetTime
        }
      };
    } else {
      return {
        success: false,
        error: result.error || 'Failed to send SMS',
        rateLimitInfo: {
          remainingAttempts: rateLimit.remainingAttempts,
          resetTime: rateLimit.resetTime
        }
      };
    }

    // OLD EDGE FUNCTION CODE REMOVED - USING TEMPORARY LOCAL SERVICE

  } catch (error) {
    console.error('❌ Error sending OTP:', error);
    return {
      success: false,
      error: 'Network error. Please check your connection and try again.'
    };
  }
}

/**
 * Verify OTP code via Supabase Edge Function (CORS-Safe Implementation)
 */
export async function verifyOTP(verificationId: string, code: string): Promise<{ success: boolean; data?: DexatelVerifyResponse; error?: string }> {
  try {
    // Validate inputs
    if (!verificationId || !code) {
      console.error('❌ Missing required parameters:', { verificationId: !!verificationId, code: !!code });
      return { success: false, error: 'Verification ID and code are required' };
    }

    // Validate code format
    if (!/^\d{6}$/.test(code)) {
      return { success: false, error: 'Invalid code format. Please enter a 6-digit code.' };
    }

    console.log('🔐 Verifying OTP via Supabase Edge Function:', {
      verificationId,
      code: '******',
      verificationIdType: typeof verificationId,
      codeType: typeof code,
      verificationIdLength: verificationId?.length || 0,
      codeLength: code?.length || 0
    });

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Supabase configuration missing');
      return { success: false, error: 'SMS service not configured' };
    }

    const requestPayload = {
      verificationId: verificationId,
      code: code
    };

    console.log('📤 Sending verification request:', {
      ...requestPayload,
      code: '******'
    });

    const response = await fetch(`${supabaseUrl}/functions/v1/verify-sms-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'apikey': supabaseAnonKey
      },
      body: JSON.stringify(requestPayload)
    });

    console.log('📡 Supabase Edge Function verify response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Supabase Edge Function verify error:', errorText);

      let errorMessage = 'Invalid verification code';
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.error || errorMessage;
      } catch (e) {
        errorMessage = `HTTP ${response.status}: ${errorText}`;
      }

      return {
        success: false,
        error: errorMessage
      };
    }

    const result = await response.json();
    console.log('✅ SMS OTP verification response:', result);

    if (result.success && result.data) {
      console.log('🎉 OTP verified successfully:', { id: verificationId });
      return { success: true, data: result.data };
    } else {
      return {
        success: false,
        error: result.error || 'Invalid verification code'
      };
    }

    // OLD EDGE FUNCTION CODE REMOVED - USING TEMPORARY LOCAL SERVICE

  } catch (error) {
    console.error('❌ Error verifying OTP:', error);
    return {
      success: false,
      error: 'Network error. Please check your connection and try again.'
    };
  }
}

/**
 * Clean up expired OTP records (should be called periodically)
 */
export async function cleanupExpiredOTPs(): Promise<void> {
  try {
    const { error } = await supabase
      .from('otp_verifications')
      .update({ status: 'expired' })
      .lt('expires_at', new Date().toISOString())
      .eq('status', 'pending');
      
    if (error) {
      console.error('Error cleaning up expired OTPs:', error);
    }
  } catch (error) {
    console.error('Error in cleanup process:', error);
  }
}
