/**
 * Reviews Service - Consumer Reviews Management
 * 
 * Handles all consumer review operations with phone-based authentication
 * 🎯 CRITICAL: Preserves existing authentication system - integrates with phone auth
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types
export interface ConsumerReview {
  id: string;
  consumer_phone: string;
  consumer_name?: string;
  product_id: string;
  product_name: string;
  rating: number;
  title?: string;
  review_text?: string;
  is_verified_purchase: boolean;
  is_approved: boolean;
  helpful_count: number;
  created_at: string;
  updated_at: string;
}

export interface CreateReviewData {
  product_id: string;
  product_name: string;
  rating: number;
  title?: string;
  review_text?: string;
}

export interface ReviewStats {
  total_reviews: number;
  average_rating: number;
  rating_distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

/**
 * Get current consumer's phone from authentication
 */
async function getCurrentConsumerPhone(): Promise<string | null> {
  try {
    // Check for phone authentication session first
    const phoneSession = localStorage.getItem('phone_auth_session');
    if (phoneSession) {
      const session = JSON.parse(phoneSession);
      return session.user?.phone || session.profile?.phone || null;
    }

    // Fallback to regular auth if needed
    const { data: { user } } = await supabase.auth.getUser();
    if (user?.phone) {
      return user.phone;
    }

    return null;
  } catch (error) {
    console.error('Error getting consumer phone:', error);
    return null;
  }
}

/**
 * Get current consumer's name from profile
 */
async function getCurrentConsumerName(): Promise<string | null> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) return null;

    const { data: profile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('phone', phone)
      .eq('role', 'consumer')
      .single();

    return profile?.full_name || null;
  } catch (error) {
    console.error('Error getting consumer name:', error);
    return null;
  }
}

/**
 * Create a new review
 */
export async function createReview(reviewData: CreateReviewData): Promise<{ success: boolean; review?: ConsumerReview; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    // Check if consumer has already reviewed this product
    const existingReviewCheck = await hasConsumerReviewedProduct(reviewData.product_id);
    if (existingReviewCheck.success && existingReviewCheck.hasReviewed) {
      return { success: false, error: 'You have already reviewed this product' };
    }

    const consumerName = await getCurrentConsumerName();

    const { data, error } = await supabase
      .from('consumer_reviews')
      .insert({
        consumer_phone: phone,
        consumer_name: consumerName,
        product_id: reviewData.product_id,
        product_name: reviewData.product_name,
        rating: reviewData.rating,
        title: reviewData.title,
        review_text: reviewData.review_text,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating review:', error);
      // Check if it's a duplicate key error (in case of race condition)
      if (error.code === '23505') {
        return { success: false, error: 'You have already reviewed this product' };
      }
      return { success: false, error: 'Failed to create review' };
    }

    return { success: true, review: data };
  } catch (error) {
    console.error('Error in createReview:', error);
    return { success: false, error: 'Failed to create review' };
  }
}

/**
 * Get reviews for a product
 */
export async function getProductReviews(productId: string): Promise<{ success: boolean; reviews?: ConsumerReview[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('consumer_reviews')
      .select('*')
      .eq('product_id', productId)
      .eq('is_approved', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching product reviews:', error);
      return { success: false, error: 'Failed to fetch reviews' };
    }

    return { success: true, reviews: data || [] };
  } catch (error) {
    console.error('Error in getProductReviews:', error);
    return { success: false, error: 'Failed to fetch reviews' };
  }
}

/**
 * Get review statistics for a product
 */
export async function getProductReviewStats(productId: string): Promise<{ success: boolean; stats?: ReviewStats; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('consumer_reviews')
      .select('rating')
      .eq('product_id', productId)
      .eq('is_approved', true);

    if (error) {
      console.error('Error fetching review stats:', error);
      return { success: false, error: 'Failed to fetch review statistics' };
    }

    const reviews = data || [];
    const totalReviews = reviews.length;

    if (totalReviews === 0) {
      return {
        success: true,
        stats: {
          total_reviews: 0,
          average_rating: 0,
          rating_distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        }
      };
    }

    // Calculate average rating
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / totalReviews;

    // Calculate rating distribution
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach(review => {
      distribution[review.rating as keyof typeof distribution]++;
    });

    return {
      success: true,
      stats: {
        total_reviews: totalReviews,
        average_rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        rating_distribution: distribution
      }
    };
  } catch (error) {
    console.error('Error in getProductReviewStats:', error);
    return { success: false, error: 'Failed to fetch review statistics' };
  }
}

/**
 * Get current consumer's reviews
 */
export async function getConsumerReviews(): Promise<{ success: boolean; reviews?: ConsumerReview[]; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { data, error } = await supabase
      .from('consumer_reviews')
      .select('*')
      .eq('consumer_phone', phone)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching consumer reviews:', error);
      return { success: false, error: 'Failed to fetch your reviews' };
    }

    return { success: true, reviews: data || [] };
  } catch (error) {
    console.error('Error in getConsumerReviews:', error);
    return { success: false, error: 'Failed to fetch your reviews' };
  }
}

/**
 * Check if consumer has reviewed a product
 */
export async function hasConsumerReviewedProduct(productId: string): Promise<{ success: boolean; hasReviewed?: boolean; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: true, hasReviewed: false };
    }

    const { data, error } = await supabase
      .from('consumer_reviews')
      .select('id')
      .eq('consumer_phone', phone)
      .eq('product_id', productId)
      .limit(1);

    if (error) {
      console.error('Error checking review status:', error);
      return { success: false, error: 'Failed to check review status' };
    }

    return { success: true, hasReviewed: (data && data.length > 0) };
  } catch (error) {
    console.error('Error in hasConsumerReviewedProduct:', error);
    return { success: false, error: 'Failed to check review status' };
  }
}

/**
 * Update a review
 */
export async function updateReview(reviewId: string, updateData: Partial<CreateReviewData>): Promise<{ success: boolean; review?: ConsumerReview; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { data, error } = await supabase
      .from('consumer_reviews')
      .update(updateData)
      .eq('id', reviewId)
      .eq('consumer_phone', phone) // Ensure user can only update their own reviews
      .select()
      .single();

    if (error) {
      console.error('Error updating review:', error);
      return { success: false, error: 'Failed to update review' };
    }

    return { success: true, review: data };
  } catch (error) {
    console.error('Error in updateReview:', error);
    return { success: false, error: 'Failed to update review' };
  }
}

/**
 * Delete a review
 */
export async function deleteReview(reviewId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const phone = await getCurrentConsumerPhone();
    if (!phone) {
      return { success: false, error: 'Authentication required' };
    }

    const { error } = await supabase
      .from('consumer_reviews')
      .delete()
      .eq('id', reviewId)
      .eq('consumer_phone', phone); // Ensure user can only delete their own reviews

    if (error) {
      console.error('Error deleting review:', error);
      return { success: false, error: 'Failed to delete review' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in deleteReview:', error);
    return { success: false, error: 'Failed to delete review' };
  }
}
