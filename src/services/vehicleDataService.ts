import { cars } from '@/data/compatibility/cars';

// Function to fetch car brands
export const fetchCarBrands = async () => {
  try {
    // In a real application, this would be an API call
    // For now, we'll use the local data
    const brands = Object.keys(cars)
      .sort((a, b) => a.localeCompare(b)) // Sort alphabetically
      .map(brandName => ({
        name: brandName,
        image_url: `/images/car-brands/${brandName.toLowerCase().replace(/\s+/g, '-')}.png`
      }));

    // Log to ensure all brands are fetched
    console.log(`Fetched ${brands.length} car brands`);

    return brands;
  } catch (error) {
    console.error('Error fetching car brands:', error);
    return []; // Return empty array instead of throwing to prevent UI errors
  }
};

// Function to fetch car models for a specific brand
export const fetchCarModels = async (brand: string) => {
  try {
    // In a real application, this would be an API call
    // For now, we'll use the local data
    if (!cars[brand]) {
      console.warn(`No models found for brand: ${brand}`);
      return [];
    }

    const models = cars[brand].models
      .sort((a, b) => a.name.localeCompare(b.name)) // Sort alphabetically by model name
      .map(model => ({
        model: model.name,
        generation: model.generation || '',
        dateRange: model.productionYears || '',
        bodyType: model.bodyType || '',
        fullName: `${model.name} ${model.generation || ''}`.trim(),
        uniqueId: `${model.name}-${model.generation || 'base'}`.toLowerCase().replace(/\s+/g, '-')
      }));

    // Log to ensure all models are fetched
    console.log(`Fetched ${models.length} models for brand: ${brand}`);

    return models;
  } catch (error) {
    console.error('Error fetching car models:', error);
    return []; // Return empty array instead of throwing to prevent UI errors
  }
};

// Helper function to parse uniqueId
export const parseUniqueId = (uniqueId: string) => {
  const parts = uniqueId.split('-');
  const generationIndex = parts.length > 1 ? parts.length - 1 : -1;

  let model = '';
  let generationCode = '';

  if (generationIndex > 0) {
    model = parts.slice(0, generationIndex).join(' ');
    generationCode = parts[generationIndex];

    // If generationCode is 'base', it means no specific generation
    if (generationCode === 'base') {
      generationCode = '';
    }
  } else {
    model = parts.join(' ');
  }

  return { model, generationCode };
};

// Function to fetch car engines for a specific brand and model
export const fetchCarEngines = async (brand: string, model: string, generation?: string) => {
  try {
    // In a real application, this would be an API call
    // For now, we'll use the local data
    if (!cars[brand]) {
      console.warn(`No data found for brand: ${brand}`);
      return [];
    }

    const modelData = cars[brand].models.find(m =>
      m.name.toLowerCase() === model.toLowerCase() &&
      (generation ? (m.generation || '').toLowerCase() === generation.toLowerCase() : true)
    );

    if (!modelData) {
      console.warn(`No model found for ${brand} ${model} ${generation || ''}`);
      return [];
    }

    if (!modelData.engines || modelData.engines.length === 0) {
      console.warn(`No engines found for ${brand} ${model} ${generation || ''}`);
      return [];
    }

    const engines = modelData.engines
      .sort((a, b) => {
        // Sort by fuel type first, then by power
        if (a.fuel_type !== b.fuel_type) {
          return (a.fuel_type || '').localeCompare(b.fuel_type || '');
        }
        return (b.power_kW || 0) - (a.power_kW || 0); // Higher power first
      })
      .map(engine => ({
        code: engine.code || '',
        fuel_type: engine.fuel_type || '',
        power_kW: engine.power_kW || 0,
        power_HP: engine.power_HP || 0,
        fullName: engine.fullName || `${engine.code || ''} (${engine.power_kW || 0} kW / ${engine.power_HP || 0} HP)`,
        productionYears: engine.productionYears || ''
      }));

    // Log to ensure all engines are fetched
    console.log(`Fetched ${engines.length} engines for ${brand} ${model} ${generation || ''}`);

    return engines;
  } catch (error) {
    console.error('Error fetching car engines:', error);
    return []; // Return empty array instead of throwing to prevent UI errors
  }
};

// Function to get a specific vehicle by identifiers
export const getVehicleByIdentifiers = async (brand: string, modelUniqueId: string, engineFullName: string) => {
  try {
    // Parse the uniqueId to get model and generation information
    const { model, generationCode } = parseUniqueId(modelUniqueId);

    // In a real application, this would be an API call
    // For now, we'll use the local data
    if (!cars[brand]) {
      console.warn(`No data found for brand: ${brand}`);
      return null;
    }

    const modelData = cars[brand].models.find(m =>
      m.name.toLowerCase() === model.toLowerCase() &&
      (generationCode ? (m.generation || '').toLowerCase() === generationCode.toLowerCase() : true)
    );

    if (!modelData) {
      console.warn(`No model found for ${brand} ${model} ${generationCode || ''}`);
      return null;
    }

    if (!modelData.engines || modelData.engines.length === 0) {
      console.warn(`No engines found for ${brand} ${model} ${generationCode || ''}`);
      return null;
    }

    const engineData = modelData.engines.find(e => e.fullName === engineFullName);

    if (!engineData) {
      console.warn(`No engine found with name: ${engineFullName}`);

      // If no exact match, try to find a similar engine
      const similarEngine = modelData.engines.find(e =>
        e.fullName && engineFullName &&
        (e.fullName.includes(engineFullName) || engineFullName.includes(e.fullName))
      );

      if (!similarEngine) {
        return null;
      }

      console.log(`Found similar engine: ${similarEngine.fullName}`);

      // Create a unique ID for this vehicle
      const vehicleId = `${brand}-${model}-${generationCode}-${similarEngine.code || 'unknown'}`.toLowerCase().replace(/\s+/g, '-');

      return {
        id: vehicleId,
        type: 'car',
        brand,
        model,
        generation: generationCode,
        engineType: similarEngine.fuel_type,
        engineDetails: {
          code: similarEngine.code,
          fuel_type: similarEngine.fuel_type,
          power_kW: similarEngine.power_kW,
          power_HP: similarEngine.power_HP,
          fullName: similarEngine.fullName
        },
        displayName: `${brand} ${model} ${generationCode || ''} ${similarEngine.fullName}`
      };
    }

    // Create a unique ID for this vehicle
    const vehicleId = `${brand}-${model}-${generationCode}-${engineData.code || 'unknown'}`.toLowerCase().replace(/\s+/g, '-');

    // Log successful vehicle creation
    console.log(`Created vehicle: ${brand} ${model} ${generationCode || ''} ${engineData.fullName}`);

    return {
      id: vehicleId,
      type: 'car',
      brand,
      model,
      generation: generationCode,
      engineType: engineData.fuel_type,
      engineDetails: {
        code: engineData.code,
        fuel_type: engineData.fuel_type,
        power_kW: engineData.power_kW,
        power_HP: engineData.power_HP,
        fullName: engineData.fullName
      },
      displayName: `${brand} ${model} ${generationCode || ''} ${engineData.fullName}`
    };
  } catch (error) {
    console.error('Error getting vehicle by identifiers:', error);
    return null; // Return null instead of throwing to prevent UI errors
  }
};
