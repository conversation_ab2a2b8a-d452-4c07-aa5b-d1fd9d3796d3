import { supabase } from './authService';

export interface ArouzFee {
  id: string;
  fee_type: 'marketplace_commission' | 'service_fee' | 'processing_fee';
  calculation_method: 'percentage' | 'fixed' | 'tiered';
  percentage_rate: number;
  fixed_amount: number;
  tier_structure: any;
  applies_to_categories: string[];
  applies_to_sections: string[];
  minimum_order_value: number;
  maximum_order_value: number | null;
  is_active: boolean;
  description: string;
}

export interface FeeCalculation {
  fee_type: string;
  fee_name: string;
  calculation_method: string;
  base_amount: number;
  rate: number;
  calculated_amount: number;
  description: string;
}

export interface ArouzFeeBreakdown {
  subtotal: number;
  shipping_cost: number;
  fees: FeeCalculation[];
  total_fees: number;
  grand_total: number;
  fee_summary: {
    marketplace_commission: number;
    service_fee: number;
    processing_fee: number;
  };
}

// Get all active AROUZ fees
export async function getActiveArouzFees(): Promise<ArouzFee[]> {
  try {
    console.log('💰 Fetching active AROUZ fees...');
    
    const { data, error } = await supabase
      .from('arouz_fees')
      .select('*')
      .eq('is_active', true)
      .order('fee_type, minimum_order_value');

    if (error) {
      console.error('❌ Error fetching AROUZ fees:', error);
      throw error;
    }

    console.log('✅ AROUZ fees fetched:', data?.length || 0);
    return data || [];
  } catch (error) {
    console.error('❌ Exception in getActiveArouzFees:', error);
    throw error;
  }
}

// Calculate AROUZ fees for an order
export async function calculateArouzFees(
  subtotal: number,
  shippingCost: number,
  marketplaceSection: 'retail' | 'wholesale',
  categories: string[] = []
): Promise<ArouzFeeBreakdown> {
  try {
    console.log('🧮 Calculating AROUZ fees:', {
      subtotal,
      shippingCost,
      marketplaceSection,
      categories
    });

    const fees = await getActiveArouzFees();
    const calculations: FeeCalculation[] = [];
    let totalFees = 0;

    const feeSummary = {
      marketplace_commission: 0,
      service_fee: 0,
      processing_fee: 0
    };

    for (const fee of fees) {
      // Check if fee applies to this order
      if (!doesFeeApply(fee, subtotal, marketplaceSection, categories)) {
        continue;
      }

      const calculation = calculateIndividualFee(fee, subtotal);
      calculations.push(calculation);
      totalFees += calculation.calculated_amount;

      // Update fee summary
      if (fee.fee_type === 'marketplace_commission') {
        feeSummary.marketplace_commission += calculation.calculated_amount;
      } else if (fee.fee_type === 'service_fee') {
        feeSummary.service_fee += calculation.calculated_amount;
      } else if (fee.fee_type === 'processing_fee') {
        feeSummary.processing_fee += calculation.calculated_amount;
      }
    }

    const grandTotal = subtotal + shippingCost + totalFees;

    const breakdown: ArouzFeeBreakdown = {
      subtotal,
      shipping_cost: shippingCost,
      fees: calculations,
      total_fees: totalFees,
      grand_total: grandTotal,
      fee_summary: feeSummary
    };

    console.log('✅ AROUZ fee calculation completed:', breakdown);
    return breakdown;
  } catch (error) {
    console.error('❌ Exception in calculateArouzFees:', error);
    // Return basic breakdown with no fees on error
    return {
      subtotal,
      shipping_cost: shippingCost,
      fees: [],
      total_fees: 0,
      grand_total: subtotal + shippingCost,
      fee_summary: {
        marketplace_commission: 0,
        service_fee: 0,
        processing_fee: 0
      }
    };
  }
}

// Check if a fee applies to the current order
function doesFeeApply(
  fee: ArouzFee,
  orderValue: number,
  marketplaceSection: 'retail' | 'wholesale',
  categories: string[]
): boolean {
  // Check minimum order value
  if (orderValue < fee.minimum_order_value) {
    return false;
  }

  // Check maximum order value
  if (fee.maximum_order_value && orderValue > fee.maximum_order_value) {
    return false;
  }

  // Check marketplace section
  if (fee.applies_to_sections.length > 0 && !fee.applies_to_sections.includes(marketplaceSection)) {
    return false;
  }

  // Check categories (if specified)
  if (fee.applies_to_categories.length > 0) {
    const hasMatchingCategory = categories.some(category => 
      fee.applies_to_categories.includes(category)
    );
    if (!hasMatchingCategory) {
      return false;
    }
  }

  return true;
}

// Calculate individual fee amount
function calculateIndividualFee(fee: ArouzFee, orderValue: number): FeeCalculation {
  let calculatedAmount = 0;
  let rate = 0;

  switch (fee.calculation_method) {
    case 'percentage':
      rate = fee.percentage_rate * 100; // Convert to percentage for display
      calculatedAmount = orderValue * fee.percentage_rate;
      break;
    
    case 'fixed':
      calculatedAmount = fee.fixed_amount;
      break;
    
    case 'tiered':
      // TODO: Implement tiered calculation if needed
      calculatedAmount = fee.fixed_amount;
      break;
    
    default:
      calculatedAmount = 0;
  }

  return {
    fee_type: fee.fee_type,
    fee_name: getFeeDisplayName(fee.fee_type),
    calculation_method: fee.calculation_method,
    base_amount: orderValue,
    rate,
    calculated_amount: Math.round(calculatedAmount * 100) / 100, // Round to 2 decimal places
    description: fee.description
  };
}

// Get user-friendly fee names
function getFeeDisplayName(feeType: string): string {
  switch (feeType) {
    case 'marketplace_commission':
      return 'AROUZ Commission';
    case 'service_fee':
      return 'Service Fee';
    case 'processing_fee':
      return 'Processing Fee';
    default:
      return 'AROUZ Fee';
  }
}

// Calculate fees for checkout display
export async function calculateCheckoutFees(
  subtotal: number,
  shippingCost: number,
  cartItems: any[]
): Promise<ArouzFeeBreakdown> {
  try {
    // Determine marketplace section based on cart items
    const marketplaceSection = determineMarketplaceSection(cartItems);
    
    // Extract categories from cart items
    const categories = extractCategories(cartItems);
    
    return await calculateArouzFees(subtotal, shippingCost, marketplaceSection, categories);
  } catch (error) {
    console.error('❌ Exception in calculateCheckoutFees:', error);
    return {
      subtotal,
      shipping_cost: shippingCost,
      fees: [],
      total_fees: 0,
      grand_total: subtotal + shippingCost,
      fee_summary: {
        marketplace_commission: 0,
        service_fee: 0,
        processing_fee: 0
      }
    };
  }
}

// Determine marketplace section from cart items
function determineMarketplaceSection(cartItems: any[]): 'retail' | 'wholesale' {
  // If any item is wholesale, consider the entire order wholesale
  const hasWholesale = cartItems.some(item => 
    item.marketplace_section === 'wholesale' || 
    item.section === 'wholesale'
  );
  
  return hasWholesale ? 'wholesale' : 'retail';
}

// Extract categories from cart items
function extractCategories(cartItems: any[]): string[] {
  const categories = new Set<string>();
  
  cartItems.forEach(item => {
    if (item.category) {
      categories.add(item.category);
    }
    if (item.subcategory) {
      categories.add(item.subcategory);
    }
  });
  
  return Array.from(categories);
}
