// This service handles the new vehicle data structure
// It provides functions to fetch and filter vehicle data from the backend

// Import the getVehicleData function from supabaseService
import { getVehicleData } from './supabaseService';

// We'll load the local data dynamically to avoid issues with imports
let carsData = [];

// Load the local data
const loadLocalData = async () => {
  try {
    const { default: data } = await import('./data/cars.json');

    // Validate the data structure
    if (!data || !Array.isArray(data) || data.length === 0) {
      console.error('Invalid or empty car data structure');
      return [];
    }

    // Log the number of brands and total models for debugging
    let totalModels = 0;
    let totalGenerations = 0;
    let totalEngineGroups = 0;
    let totalEngineVariants = 0;
    let allEngineVariants = new Set(); // Track all unique engine variants

    data.forEach(brand => {
      if (brand.models && Array.isArray(brand.models)) {
        totalModels += brand.models.length;

        brand.models.forEach(model => {
          if (model.generations && Array.isArray(model.generations)) {
            totalGenerations += model.generations.length;

            model.generations.forEach(generation => {
              if (generation.engines && Array.isArray(generation.engines)) {
                totalEngineGroups += generation.engines.length;

                generation.engines.forEach(engineGroup => {
                  if (engineGroup.variants && Array.isArray(engineGroup.variants)) {
                    totalEngineVariants += engineGroup.variants.length;

                    // Add each variant to the set of all variants
                    engineGroup.variants.forEach(variant => {
                      allEngineVariants.add(variant);
                    });
                  }
                });
              }
            });
          }
        });
      }
    });

    console.log(`Loaded car data: ${data.length} brands, ${totalModels} models, ${totalGenerations} generations, ${totalEngineGroups} engine groups, ${totalEngineVariants} engine variants`);
    console.log(`Total unique engine variants: ${allEngineVariants.size}`);

    // Log a sample of engine variants to verify they're being loaded correctly
    const sampleVariants = Array.from(allEngineVariants).slice(0, 5);
    console.log('Sample engine variants:', sampleVariants);

    carsData = data;
    return data;
  } catch (error) {
    console.error('Error loading local car data:', error);
    return [];
  }
};

/**
 * Count the total number of variants in the cars.json file
 * This function performs a direct count of all variants without any filtering or processing
 * @returns {Promise<Object>} Object with counts of brands, models, generations, engine groups, and variants
 */
export const countTotalVariants = async () => {
  try {
    const { default: data } = await import('./data/cars.json');

    if (!data || !Array.isArray(data)) {
      console.error('Invalid data format');
      return {
        brands: 0,
        models: 0,
        generations: 0,
        engineGroups: 0,
        variants: 0,
        uniqueVariants: 0,
        allVariants: []
      };
    }

    let brands = data.length;
    let models = 0;
    let generations = 0;
    let engineGroups = 0;
    let variants = 0;
    let allVariants = [];

    // Track Mercedes-Benz variants separately to ensure we don't miss any
    let mercedesBenzVariants = [];

    // Process each brand
    data.forEach(brand => {
      if (!brand.models || !Array.isArray(brand.models)) return;

      models += brand.models.length;

      // Process each model
      brand.models.forEach(model => {
        if (!model.generations || !Array.isArray(model.generations)) return;

        generations += model.generations.length;

        // Process each generation
        model.generations.forEach(generation => {
          if (!generation.engines || !Array.isArray(generation.engines)) return;

          engineGroups += generation.engines.length;

          // Process each engine group
          generation.engines.forEach(engineGroup => {
            if (!engineGroup.variants || !Array.isArray(engineGroup.variants)) return;

            variants += engineGroup.variants.length;

            // Add each variant to the list
            engineGroup.variants.forEach(variant => {
              try {
                // Extract power values for better uniqueness checking
                const powerMatch = variant.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
                const powerKW = powerMatch ? parseInt(powerMatch[1]) : 0;
                const powerHP = powerMatch ? parseInt(powerMatch[2]) : 0;

                // Extract engine code for better uniqueness checking
                let engineCode = '';
                let engineName = '';

                // Extract engine name - everything before the first parenthesis
                const engineNameMatch = variant.match(/^(.*?)\s*\(/);
                engineName = engineNameMatch ? engineNameMatch[1].trim() : '';

                // Extract engine code
                const codeMatch = variant.match(/\(([^)]+)\)/);
                if (codeMatch) {
                  const isPowerValue = codeMatch[1].includes('kW') || codeMatch[1].includes('HP');
                  if (isPowerValue) {
                    // For variants without explicit engine codes, use the first part of the variant string
                    const parts = variant.split(' ');
                    engineCode = parts[0];
                    if (parts.length > 1 && !isNaN(parseFloat(parts[0]))) {
                      // If the first part is a number (like "1.6"), include the second part if it exists
                      engineCode = parts.length > 1 ? `${parts[0]} ${parts[1]}` : parts[0];
                    }
                  } else {
                    engineCode = codeMatch[1];
                  }
                } else if (engineName) {
                  // If no parentheses but we have an engine name, use that
                  engineCode = engineName;
                } else {
                  // Fallback to the first part of the variant string
                  engineCode = variant.split(' ')[0];
                }

                // Extract date range - usually the last parenthesized part
                const dateRangeMatch = variant.match(/\(([^)]+)\)$/);
                const dateRange = dateRangeMatch ? dateRangeMatch[1] : '';

                // Special handling for Mercedes-Benz variants
                if (brand.brand === 'Mercedes-Benz') {
                  // For Mercedes-Benz, use the model designation (e.g., "A 180", "C 200") as the engine code
                  const parts = variant.split(' ');
                  if (parts.length >= 2) {
                    engineCode = parts[0] + ' ' + parts[1];
                  } else {
                    engineCode = parts[0];
                  }

                  mercedesBenzVariants.push({
                    brand: brand.brand,
                    model: model.model,
                    generation: generation.code,
                    bodyType: generation.body_type,
                    fuelType: engineGroup.fuel_type,
                    variant: variant,
                    powerKW: powerKW,
                    powerHP: powerHP,
                    engineCode: engineCode,
                    engineName: engineName,
                    dateRange: dateRange
                  });
                }

                allVariants.push({
                  brand: brand.brand,
                  model: model.model,
                  generation: generation.code,
                  bodyType: generation.body_type,
                  fuelType: engineGroup.fuel_type,
                  variant: variant,
                  powerKW: powerKW,
                  powerHP: powerHP,
                  engineCode: engineCode,
                  engineName: engineName,
                  dateRange: dateRange
                });
              } catch (error) {
                console.error(`Error processing variant: "${variant}"`, error);
                // Still add the variant with basic information to ensure it's included
                allVariants.push({
                  brand: brand.brand,
                  model: model.model,
                  generation: generation.code,
                  bodyType: generation.body_type,
                  fuelType: engineGroup.fuel_type,
                  variant: variant,
                  powerKW: 0,
                  powerHP: 0,
                  engineCode: 'Unknown'
                });
              }
            });
          });
        });
      });
    });

    // Count unique variants - create a more comprehensive uniqueness check
    // Create a set of unique variant keys that include brand, model, generation, and variant
    const uniqueVariantKeys = new Set();
    const uniqueVariantObjects = []; // Store unique variant objects for debugging

    // Special handling for Mercedes-Benz variants to ensure we don't miss any
    const mercedesBenzUniqueKeys = new Set();

    // Create a map to track variants by their full string representation
    const variantMap = new Map();

    allVariants.forEach(v => {
      // Create a comprehensive key that includes all relevant details
      const variantKey = `${v.brand}-${v.model}-${v.generation}-${v.variant}`;

      // Check if we've already seen this exact variant
      if (!uniqueVariantKeys.has(variantKey)) {
        uniqueVariantKeys.add(variantKey);
        uniqueVariantObjects.push(v);

        // Add to the variant map
        if (!variantMap.has(v.variant)) {
          variantMap.set(v.variant, []);
        }
        variantMap.get(v.variant).push(v);
      }

      // For Mercedes-Benz, create a more sophisticated uniqueness key
      if (v.brand === 'Mercedes-Benz') {
        // Create a key that includes model designation and power values
        const modelDesignation = v.variant.split(' ')[0] + ' ' + (v.variant.split(' ')[1] || '');
        const mercedesKey = `${v.brand}-${v.model}-${v.generation}-${modelDesignation.trim()}-${v.powerKW}-${v.powerHP}`;
        mercedesBenzUniqueKeys.add(mercedesKey);
      }
    });

    // Count unique variant strings regardless of which model/generation they belong to
    const uniqueVariantStrings = new Set(allVariants.map(v => v.variant));

    // Count Mercedes-Benz variants separately
    const mercedesBenzVariantStrings = new Set(mercedesBenzVariants.map(v => v.variant));

    console.log(`Found ${uniqueVariantKeys.size} unique vehicle-engine combinations and ${uniqueVariantStrings.size} unique engine variant strings`);
    console.log(`Found ${mercedesBenzVariants.length} Mercedes-Benz variants with ${mercedesBenzUniqueKeys.size} unique combinations and ${mercedesBenzVariantStrings.size} unique variant strings`);

    // Check for variants that appear in multiple models/generations
    let sharedVariantCount = 0;
    variantMap.forEach((variants, variantString) => {
      if (variants.length > 1) {
        sharedVariantCount++;
        // Log the first few shared variants for debugging
        if (sharedVariantCount <= 5) {
          console.log(`Variant "${variantString}" appears in ${variants.length} different models/generations:`);
          variants.slice(0, 3).forEach(v => {
            console.log(`  - ${v.brand} ${v.model} ${v.generation} (${v.bodyType})`);
          });
          if (variants.length > 3) {
            console.log(`  - ... and ${variants.length - 3} more`);
          }
        }
      }
    });

    if (sharedVariantCount > 0) {
      console.log(`Found ${sharedVariantCount} variant strings that appear in multiple models/generations`);
    }

    // Use the more comprehensive uniqueness check
    const uniqueVariants = uniqueVariantKeys.size;

    return {
      brands,
      models,
      generations,
      engineGroups,
      variants,
      uniqueVariants,
      allVariants
    };
  } catch (error) {
    console.error('Error counting variants:', error);
    return {
      brands: 0,
      models: 0,
      generations: 0,
      engineGroups: 0,
      variants: 0,
      uniqueVariants: 0,
      allVariants: []
    };
  }
};

// Initialize the local data - make sure it's loaded before the app starts using it
(async () => {
  await loadLocalData();

  // Count total variants for verification
  const counts = await countTotalVariants();
  console.log('TOTAL COUNTS FROM DIRECT FILE SCAN:');
  console.log(`Brands: ${counts.brands}`);
  console.log(`Models: ${counts.models}`);
  console.log(`Generations: ${counts.generations}`);
  console.log(`Engine Groups: ${counts.engineGroups}`);
  console.log(`Total Variants: ${counts.variants}`);
  console.log(`Unique Variants: ${counts.uniqueVariants}`);
})();

// Flag to determine whether to use local data or fetch from Supabase
const USE_SUPABASE = import.meta.env.VITE_USE_SUPABASE === 'true';

/**
 * Fetch all car brands
 * @returns {Promise<Array>} - Array of brand objects with name and image_url
 */
export const fetchCarBrands = async () => {
  try {
    let data;

    if (USE_SUPABASE) {
      // Fetch from Supabase
      data = await getVehicleData('car');
    } else {
      // Use local data - ensure it's loaded
      if (carsData.length === 0) {
        data = await loadLocalData();
      } else {
        data = carsData;
      }
    }

    // If we still don't have data, return an empty array
    if (!data || !Array.isArray(data)) {
      console.warn('No car data available');
      return [];
    }

    return data.map(brand => ({
      name: brand.brand,
      image_url: brand.image_url
    }));
  } catch (error) {
    console.error('Error fetching car brands:', error);
    return [];
  }
};

/**
 * Fetch all models for a specific brand
 * @param {string} brandName - The name of the brand
 * @returns {Promise<Array>} - Array of model objects with generation information
 */
export const fetchCarModels = async (brandName) => {
  try {
    let data;

    if (USE_SUPABASE) {
      // Fetch from Supabase
      data = await getVehicleData('car');
    } else {
      // Use local data - ensure it's loaded
      if (carsData.length === 0) {
        data = await loadLocalData();
      } else {
        data = carsData;
      }
    }

    // If we still don't have data, return an empty array
    if (!data || !Array.isArray(data)) {
      console.warn('No car data available');
      return [];
    }

    // Find the brand in our data
    const brand = data.find(b => b.brand === brandName);
    if (!brand) {
      console.error(`Brand not found: ${brandName}`);
      return [];
    }

    console.log(`Found brand: ${brandName} with ${brand.models.length} models`);

    // For each model, create entries for each generation
    const modelGenerations = [];

    brand.models.forEach(model => {
      // Log the model being processed
      console.log(`Processing model: ${model.model} with ${model.generations ? model.generations.length : 0} generations`);

      if (!model.generations || model.generations.length === 0) {
        console.warn(`No generations found for model: ${model.model}`);
        // Even if there are no generations, add a placeholder entry to ensure the model appears in the dropdown
        modelGenerations.push({
          model: model.model,
          generation: 'All',
          bodyType: 'All',
          dateRange: '',
          fullName: `${model.model} (All)`,
          uniqueId: `${model.model}-All-All-All`
        });
        return;
      }

      model.generations.forEach(generation => {
        // Log the generation being processed
        console.log(`Processing generation: ${generation.code} - ${generation.body_type}`);

        // Create a standardized uniqueId that works for all models
        // Format: brand|model|generation|bodyType|startDate
        // Using | as separator instead of - to avoid issues with hyphens in model names or generation codes
        const uniqueId = `${brandName}|${model.model}|${generation.code}|${generation.body_type}|${generation.start}`;

        console.log(`Created standardized uniqueId: ${uniqueId}`);

        modelGenerations.push({
          model: model.model,
          generation: generation.code,
          bodyType: generation.body_type,
          dateRange: `${generation.start}–${generation.end}`,
          fullName: `${model.model} (${generation.code}) ${generation.body_type} ${generation.start}–${generation.end === '...' ? 'Present' : generation.end}`,
          uniqueId: uniqueId,
          // Add brand information to make filtering easier
          brand: brandName
        });
      });
    });

    // Log the total number of model generations found
    console.log(`Found ${modelGenerations.length} total model generations for ${brandName}`);

    // Log all model names to check if they're all being processed
    console.log(`All models for ${brandName}:`, brand.models.map(m => m.model).join(', '));

    // Log all model generations to check what's being returned
    console.log(`Returning model generations:`, modelGenerations.map(mg => `${mg.model} (${mg.generation})`).slice(0, 10).join(', ') + (modelGenerations.length > 10 ? '...' : ''));

    return modelGenerations;
  } catch (error) {
    console.error('Error fetching car models:', error);
    return [];
  }
};

/**
 * Fetch all engines for a specific model and generation
 * @param {string} brandName - The name of the brand
 * @param {string} modelName - The name of the model
 * @param {string} generationCode - The generation code
 * @returns {Promise<Array>} - Array of engine objects
 */
export const fetchCarEngines = async (brandName, modelName, generationCode) => {
  try {
    console.log('Fetching engines for:', { brandName, modelName, generationCode });
    let data;

    if (USE_SUPABASE) {
      // Fetch from Supabase
      data = await getVehicleData('car');
    } else {
      // Use local data - ensure it's loaded
      if (carsData.length === 0) {
        data = await loadLocalData();
      } else {
        data = carsData;
      }
    }

    // If we still don't have data, return an empty array
    if (!data || !Array.isArray(data)) {
      console.warn('No car data available');
      return [];
    }

    // Find the brand in our data
    const brand = data.find(b => b.brand === brandName);
    if (!brand) {
      console.warn('Brand not found:', brandName);
      return [];
    }

    // Special logging for Mercedes-Benz A-Class
    if (brandName === 'Mercedes-Benz' && modelName === 'A-Class') {
      console.log('Processing Mercedes-Benz A-Class model');
      console.log('Available models for Mercedes-Benz:', brand.models.map(m => m.model).join(', '));
    }

    // Find the model - case insensitive search
    const model = brand.models.find(m =>
      m.model.toLowerCase() === modelName.toLowerCase()
    );

    if (!model) {
      console.warn(`Model not found: "${modelName}" in brand "${brandName}"`);
      console.log('Available models:', brand.models.map(m => m.model).join(', '));
      return [];
    }

    // Special logging for Mercedes-Benz A-Class
    if (brandName === 'Mercedes-Benz' && modelName === 'A-Class') {
      console.log('Found Mercedes-Benz A-Class model:', model);
      console.log('Available generations:', model.generations.map(g => g.code).join(', '));
    }

    // Find the generation - case insensitive search and handle 'All' special case
    let generation;

    // Log available generations for debugging
    console.log('Available generations for model:', model.model);
    model.generations.forEach(g => {
      console.log(`- ${g.code} (${g.start}–${g.end}): ${g.body_type}`);
      if (g.engines) {
        console.log(`  Engine groups: ${g.engines.length}`);
        g.engines.forEach((eg, i) => {
          console.log(`  - Group ${i+1}: ${eg.fuel_type} (${eg.variants ? eg.variants.length : 0} variants)`);
          if (eg.variants && eg.variants.length > 0) {
            console.log(`    Sample variant: ${eg.variants[0]}`);
          }
        });
      }
    });

    if (generationCode === 'All' || generationCode === 'all') {
      // If 'All' is specified, use the first generation or create a dummy one
      generation = model.generations && model.generations.length > 0
        ? model.generations[0]
        : { code: 'All', engines: [] };

      console.log('Using first generation for "All" code:', generation.code);
    } else {
      // First try exact match (case insensitive)
      generation = model.generations.find(g =>
        g.code.toLowerCase() === generationCode.toLowerCase()
      );

      // If exact match fails, try fuzzy matching
      if (!generation) {
        console.log(`Exact generation match not found for "${generationCode}", trying fuzzy matching`);

        // Try to find a generation where the code contains the requested code or vice versa
        generation = model.generations.find(g =>
          g.code.toLowerCase().includes(generationCode.toLowerCase()) ||
          generationCode.toLowerCase().includes(g.code.toLowerCase())
        );

        if (generation) {
          console.log(`Found fuzzy match by substring: ${generation.code}`);
        } else {
          // Try matching by numbers/codes in the generation code
          const numbersInRequested = generationCode.match(/\d+|[A-Z]\d+/g) || [];

          if (numbersInRequested.length > 0) {
            generation = model.generations.find(g => {
              const numbersInGeneration = g.code.match(/\d+|[A-Z]\d+/g) || [];
              return numbersInRequested.some(num => numbersInGeneration.includes(num));
            });

            if (generation) {
              console.log(`Found fuzzy match by numbers/codes: ${generation.code}`);
            }
          }

          // If still not found, use the newest generation as a fallback
          if (!generation && model.generations && model.generations.length > 0) {
            generation = model.generations[0]; // Assuming the newest generation is first
            console.log(`Using newest generation as fallback: ${generation.code}`);
          }
        }
      }
    }

    if (!generation) {
      console.warn(`Generation not found: "${generationCode}" for model "${modelName}"`);
      console.log('Available generations:', model.generations.map(g => g.code).join(', '));
      return [];
    }

    console.log('Found generation:', generation);

    // Format the engines for display
    const engineOptions = [];

    // Handle the new engine data format (array of fuel types with variants)
    if (generation.engines && Array.isArray(generation.engines)) {
      console.log(`Processing ${generation.engines.length} engine groups for ${modelName} (${generationCode})`);

      // Special logging for Mercedes-Benz A-Class
      if (brandName === 'Mercedes-Benz' && modelName === 'A-Class') {
        console.log('Mercedes-Benz A-Class engine groups details:');
        generation.engines.forEach((eg, i) => {
          console.log(`- Group ${i+1}: ${eg.fuel_type} (${eg.variants ? eg.variants.length : 0} variants)`);
          if (eg.variants && eg.variants.length > 0) {
            console.log(`  First few variants:`);
            eg.variants.slice(0, 3).forEach(v => console.log(`  - ${v}`));
          }
        });
      }

      // Process each fuel type group
      generation.engines.forEach((fuelTypeGroup, fuelGroupIndex) => {
        if (fuelTypeGroup.fuel_type && Array.isArray(fuelTypeGroup.variants)) {
          console.log(`Processing ${fuelTypeGroup.variants.length} ${fuelTypeGroup.fuel_type} engine variants (group ${fuelGroupIndex + 1}/${generation.engines.length})`);

          // Process each variant in the fuel type group
          fuelTypeGroup.variants.forEach((variant, variantIndex) => {
            try {
              // Extract engine code from the variant string
              // Format examples:
              // "1.4 Fire (843A1000) (70 kW / 95 HP) (10.2015–...)"
              // "1.2 (115 A.000) (44 kW / 60 HP) (04.1966–06.1974)"
              // "E 200 (135 kW / 184 HP) (03.2016–03.2023)" - Mercedes-Benz format without engine code
              // "A 180 (100 kW / 136 HP) (05.2018–...)" - Mercedes-Benz A-Class format

              let engineCode = '';

              // Special handling for Mercedes-Benz models which often don't have explicit engine codes
              if (brandName === 'Mercedes-Benz') {
                // For Mercedes-Benz, use the model designation (e.g., "A 180", "C 200") as the engine code
                const parts = variant.split(' ');
                if (parts.length >= 2) {
                  engineCode = parts[0] + ' ' + parts[1];
                } else {
                  engineCode = parts[0];
                }
              } else {
                // For other brands, try to extract the engine code from parentheses
                const codeMatch = variant.match(/\(([^)]+)\)/);

                if (codeMatch) {
                  // Check if the first parenthesized part is a power value (kW / HP)
                  const isPowerValue = codeMatch[1].includes('kW') || codeMatch[1].includes('HP');

                  if (isPowerValue) {
                    // If it's a power value, use the first part of the variant string
                    const modelDesignation = variant.split(' ')[0];
                    engineCode = modelDesignation.trim();
                  } else {
                    // For variants with explicit engine codes, use the first parenthesized part
                    engineCode = codeMatch[1];
                  }
                }
              }

              // Extract power values - look for the pattern (XX kW / YY HP)
              const powerMatch = variant.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
              const powerKW = powerMatch ? parseInt(powerMatch[1]) : 0;
              const powerHP = powerMatch ? parseInt(powerMatch[2]) : 0;

              // Extract date range - usually the last parenthesized part
              const dateRangeMatch = variant.match(/\(([^)]+)\)$/);
              const dateRange = dateRangeMatch ? dateRangeMatch[1] : '';

              // Extract engine name - everything before the first parenthesis
              const engineNameMatch = variant.match(/^(.*?)\s*\(/);
              const engineName = engineNameMatch ? engineNameMatch[1].trim() : '';

              // Add the engine to the options with all extracted details
              engineOptions.push({
                code: engineCode,
                fuel_type: fuelTypeGroup.fuel_type,
                fullName: variant,
                power_kW: powerKW,
                power_HP: powerHP,
                dateRange: dateRange,
                engineName: engineName,
                // Add unique ID to ensure each variant is distinct
                uniqueId: `${brandName}-${modelName}-${generationCode}-${fuelTypeGroup.fuel_type}-${engineCode}-${powerKW}-${powerHP}`.toLowerCase().replace(/\s+/g, '-')
              });

              console.log(`Added engine variant ${variantIndex + 1}/${fuelTypeGroup.variants.length}: ${variant}`);
            } catch (variantError) {
              console.error(`Error processing engine variant: "${variant}"`, variantError);
              // Still add the variant with basic information to ensure it's included
              engineOptions.push({
                code: 'Unknown',
                fuel_type: fuelTypeGroup.fuel_type,
                fullName: variant,
                power_kW: 0,
                power_HP: 0,
                uniqueId: `${brandName}-${modelName}-${generationCode}-${fuelTypeGroup.fuel_type}-unknown-${variantIndex}`.toLowerCase().replace(/\s+/g, '-')
              });
            }
          });
        } else {
          console.warn(`Invalid engine group format for ${fuelTypeGroup.fuel_type || 'unknown fuel type'} (group ${fuelGroupIndex + 1}/${generation.engines.length})`);
        }
      });
    } else {
      console.warn(`No engines found for ${modelName} (${generationCode})`);
    }

    // If we're looking for 'All' generations and there are other generations, include their engines too
    if ((generationCode === 'All' || generationCode === 'all') && model.generations && model.generations.length > 0) {
      console.log(`Including engines from all ${model.generations.length} generations for model "${modelName}"`);

      // Process each generation - don't skip any generations to ensure we get all variants
      for (const gen of model.generations) {
        // Don't skip the generation we already processed to avoid duplicates
        if (gen.code === generation.code && generationCode !== 'All' && generationCode !== 'all') {
          console.log(`Skipping already processed generation "${gen.code}" to avoid duplicates`);
          continue;
        }

        if (gen.engines && Array.isArray(gen.engines)) {
          console.log(`Processing ${gen.engines.length} additional engine groups from generation "${gen.code}"`);

          // Process each fuel type group in this generation
          gen.engines.forEach((fuelTypeGroup, fuelGroupIndex) => {
            if (fuelTypeGroup.fuel_type && Array.isArray(fuelTypeGroup.variants)) {
              console.log(`Processing ${fuelTypeGroup.variants.length} ${fuelTypeGroup.fuel_type} engine variants from generation "${gen.code}"`);

              // Process each variant
              fuelTypeGroup.variants.forEach((variant, variantIndex) => {
                try {
                  let engineCode = '';

                  // Special handling for Mercedes-Benz models which often don't have explicit engine codes
                  if (brandName === 'Mercedes-Benz') {
                    // For Mercedes-Benz, use the model designation (e.g., "A 180", "C 200") as the engine code
                    const parts = variant.split(' ');
                    if (parts.length >= 2) {
                      engineCode = parts[0] + ' ' + parts[1];
                    } else {
                      engineCode = parts[0];
                    }
                  } else {
                    // For other brands, try to extract the engine code from parentheses
                    const codeMatch = variant.match(/\(([^)]+)\)/);

                    if (codeMatch) {
                      // Check if the first parenthesized part is a power value (kW / HP)
                      const isPowerValue = codeMatch[1].includes('kW') || codeMatch[1].includes('HP');

                      if (isPowerValue) {
                        // If it's a power value, use the first part of the variant string
                        const modelDesignation = variant.split(' ')[0];
                        engineCode = modelDesignation.trim();
                      } else {
                        // For variants with explicit engine codes, use the first parenthesized part
                        engineCode = codeMatch[1];
                      }
                    }
                  }

                  const powerMatch = variant.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
                  const powerKW = powerMatch ? parseInt(powerMatch[1]) : 0;
                  const powerHP = powerMatch ? parseInt(powerMatch[2]) : 0;

                  const uniqueId = `${brandName}-${modelName}-${gen.code}-${fuelTypeGroup.fuel_type}-${engineCode}-${powerKW}-${powerHP}`.toLowerCase().replace(/\s+/g, '-');

                  // Check if this engine variant is already in the options (avoid duplicates)
                  // We need to be careful not to filter out variants that appear similar but have different details
                  // Create a more comprehensive uniqueness check that considers all relevant details
                  const isDuplicate = engineOptions.some(e => {
                    // First check if the full variant string matches exactly
                    if (e.fullName === variant) return true;

                    // If not, check if this is a Mercedes-Benz style variant with the same model designation and power values
                    // This handles cases where the same engine might be listed with slightly different formatting
                    if (powerMatch && e.power_kW === powerKW && e.power_HP === powerHP) {
                      // For Mercedes-Benz style variants (A 180, C 200, etc.), check if the model designation is the same
                      const thisModelDesignation = variant.split(' ')[0] + ' ' + (variant.split(' ')[1] || '');
                      const existingModelDesignation = e.fullName.split(' ')[0] + ' ' + (e.fullName.split(' ')[1] || '');

                      if (thisModelDesignation.trim() === existingModelDesignation.trim()) {
                        return true;
                      }
                    }

                    return false;
                  });

                  if (!isDuplicate) {
                    engineOptions.push({
                      code: engineCode,
                      fuel_type: fuelTypeGroup.fuel_type,
                      fullName: variant,
                      power_kW: powerKW,
                      power_HP: powerHP,
                      uniqueId: uniqueId,
                      generation: gen.code // Add generation info to distinguish
                    });

                    console.log(`Added additional engine variant from generation "${gen.code}": ${variant}`);
                  } else {
                    console.log(`Skipped duplicate engine variant from generation "${gen.code}": ${variant}`);
                  }
                } catch (variantError) {
                  console.error(`Error processing additional engine variant from generation "${gen.code}": "${variant}"`, variantError);
                  // Still add it with basic info
                  engineOptions.push({
                    code: 'Unknown',
                    fuel_type: fuelTypeGroup.fuel_type,
                    fullName: variant,
                    power_kW: 0,
                    power_HP: 0,
                    uniqueId: `${brandName}-${modelName}-${gen.code}-${fuelTypeGroup.fuel_type}-unknown-${variantIndex}`.toLowerCase().replace(/\s+/g, '-'),
                    generation: gen.code
                  });
                }
              });
            }
          });
        }
      }
    }

    // Sort engines by fuel type and then by power (higher power first)
    engineOptions.sort((a, b) => {
      // First sort by fuel type
      const fuelTypeCompare = (a.fuel_type || '').localeCompare(b.fuel_type || '');
      if (fuelTypeCompare !== 0) return fuelTypeCompare;

      // Then sort by power (higher power first)
      return (b.power_kW || 0) - (a.power_kW || 0);
    });

    // Log detailed information about the engine variants we're returning
    console.log(`Returning ${engineOptions.length} total engines for ${brandName} ${modelName} (${generationCode})`);

    // Create a map of fuel types to count variants by fuel type
    const fuelTypeCounts = {};
    engineOptions.forEach(engine => {
      const fuelType = engine.fuel_type || 'Unknown';
      fuelTypeCounts[fuelType] = (fuelTypeCounts[fuelType] || 0) + 1;
    });

    // Log the counts by fuel type
    console.log('Engine variants by fuel type:', fuelTypeCounts);

    // Create a map to track unique variant strings
    const uniqueVariantStrings = new Set();
    engineOptions.forEach(engine => {
      uniqueVariantStrings.add(engine.fullName);
    });
    console.log(`Total unique variant strings: ${uniqueVariantStrings.size}`);

    // Log a sample of variants for verification
    if (engineOptions.length > 0) {
      console.log('Sample engine variants:');
      const sampleSize = Math.min(5, engineOptions.length);
      for (let i = 0; i < sampleSize; i++) {
        console.log(`  ${i+1}. ${engineOptions[i].fullName} (${engineOptions[i].fuel_type})`);
      }

      // If there are many variants, also log some from the end
      if (engineOptions.length > 10) {
        console.log('  ...');
        for (let i = Math.max(sampleSize, engineOptions.length - 3); i < engineOptions.length; i++) {
          console.log(`  ${i+1}. ${engineOptions[i].fullName} (${engineOptions[i].fuel_type})`);
        }
      }

      // Log all variants for debugging if there are fewer than 20
      if (engineOptions.length < 20) {
        console.log('All engine variants:');
        engineOptions.forEach((engine, index) => {
          console.log(`  ${index+1}. ${engine.fullName} (${engine.fuel_type})`);
        });
      }
    }

    return engineOptions;
  } catch (error) {
    console.error('Error fetching car engines:', error);
    return [];
  }
};

/**
 * Parse a uniqueId to extract brand, model, generation code, body type, and start date
 * @param {string} uniqueId - The unique ID in either new format (brand|model|generation|bodyType|startDate)
 *                           or legacy format (model-generation-bodyType-startDate)
 * @returns {Object} - Object with brand, model, generationCode, bodyType, and startDate
 */
export const parseUniqueId = (uniqueId) => {
  try {
    console.log('Parsing uniqueId:', uniqueId);

    if (!uniqueId) {
      console.error('Empty uniqueId provided');
      return { brand: '', model: '', generationCode: '', bodyType: '', startDate: '' };
    }

    // Check if this is the new format with | as separator
    if (uniqueId.includes('|')) {
      const parts = uniqueId.split('|');

      if (parts.length >= 5) {
        // New format: brand|model|generation|bodyType|startDate
        const [brand, model, generationCode, bodyType, startDate] = parts;

        const result = {
          brand,
          model,
          generationCode,
          bodyType,
          startDate
        };

        console.log('Parsed uniqueId (new format) result:', result);
        return result;
      } else {
        console.warn(`Invalid new format uniqueId (not enough parts): "${uniqueId}"`);
        // Try to extract what we can from the new format
        return {
          brand: parts[0] || '',
          model: parts[1] || '',
          generationCode: parts[2] || '',
          bodyType: parts[3] || '',
          startDate: parts[4] || ''
        };
      }
    }

    // Legacy format handling (for backward compatibility)

    // Special handling for Mercedes-Benz A-Class W177/V177 (legacy format)
    if (uniqueId.includes('A-Class') && uniqueId.includes('W177/V177')) {
      console.log('Special handling for Mercedes-Benz A-Class W177/V177 (legacy format)');
      return {
        brand: 'Mercedes-Benz',
        model: 'A-Class',
        generationCode: 'A-Class W177/V177',
        bodyType: 'Hatchback/Sedan',
        startDate: '05.2018'
      };
    }

    const parts = uniqueId.split('-');

    // Handle special case for 'All' generations (legacy format)
    if (parts.length === 2 && parts[1].toLowerCase() === 'all') {
      return {
        brand: '',
        model: parts[0],
        generationCode: 'All',
        bodyType: '',
        startDate: ''
      };
    }

    // Handle special case for uniqueIds with format "model-generation-All-All" (legacy format)
    if (parts.length === 4 && parts[2].toLowerCase() === 'all' && parts[3].toLowerCase() === 'all') {
      return {
        brand: '',
        model: parts[0],
        generationCode: parts[1],
        bodyType: '',
        startDate: ''
      };
    }

    if (parts.length < 4) {
      console.warn(`Invalid legacy format uniqueId (not enough parts): "${uniqueId}"`);

      // Special handling for Mercedes-Benz models which might have a different format
      if (parts.length >= 2 && parts[0].includes('Class')) {
        console.log('Detected Mercedes-Benz model with non-standard format (legacy)');
        return {
          brand: 'Mercedes-Benz',
          model: parts[0],
          generationCode: parts[1] || '',
          bodyType: parts[2] || '',
          startDate: parts[3] || ''
        };
      }

      // Try to extract what we can from legacy format
      return {
        brand: '',
        model: parts[0] || '',
        generationCode: parts[1] || '',
        bodyType: parts[2] || '',
        startDate: ''
      };
    }

    // Legacy format parsing
    // The last part is the start date
    const startDate = parts.pop();

    // The second-to-last part is the body type (might contain hyphens)
    const bodyType = parts.slice(2).join('-');

    // The second part is the generation code
    const generationCode = parts[1];

    // The first part is the model
    const model = parts[0];

    const result = {
      brand: '',  // Brand is unknown in legacy format
      model,
      generationCode,
      bodyType,
      startDate
    };

    console.log('Parsed uniqueId (legacy format) result:', result);
    return result;
  } catch (error) {
    console.error('Error parsing uniqueId:', error, uniqueId);
    return { brand: '', model: '', generationCode: '', bodyType: '', startDate: '' };
  }
};

/**
 * Get a vehicle by its identifiers
 * @param {string} brandName - The name of the brand
 * @param {string} uniqueId - The unique ID for the model-generation-bodyType combination
 * @param {string} engineString - The engine string
 * @returns {Promise<Object|null>} - Vehicle object or null if not found
 */
export const getVehicleByIdentifiers = async (brandName, uniqueId, engineString) => {
  try {
    const { brand: parsedBrand, model, generationCode, bodyType, startDate } = parseUniqueId(uniqueId);

    // Use the parsed brand if available, otherwise use the provided brandName
    const brandToUse = parsedBrand || brandName;

    console.log('Parsed uniqueId:', { brand: brandToUse, model, generationCode, bodyType, startDate });
    console.log('Looking for engine:', engineString);

    let data;

    if (USE_SUPABASE) {
      // Fetch from Supabase
      data = await getVehicleData('car');
    } else {
      // Use local data - ensure it's loaded
      if (carsData.length === 0) {
        data = await loadLocalData();
      } else {
        data = carsData;
      }
    }

    // If we still don't have data, return null
    if (!data || !Array.isArray(data)) {
      console.warn('No car data available');
      return null;
    }

    // Find the brand
    const brand = data.find(b => b.brand === brandToUse);
    if (!brand) {
      console.warn('Brand not found:', brandToUse);
      return null;
    }

    // Find the model
    const modelObj = brand.models.find(m => m.model === model);
    if (!modelObj) {
      console.warn('Model not found:', model);
      return null;
    }

    // Find the generation with matching code and body type
    const generation = modelObj.generations.find(g =>
      g.code === generationCode &&
      g.body_type === bodyType &&
      g.start === startDate
    );
    if (!generation) {
      console.warn('Generation not found:', { generationCode, bodyType, startDate });
      return null;
    }

    console.log('Found generation:', generation);

    // We need to find the exact engine variant that matches the engineString
    let matchedVariant = null;
    let matchedFuelType = '';

    // First, try to find an exact match for the full engine string
    for (const engineGroup of generation.engines) {
      if (Array.isArray(engineGroup.variants)) {
        const exactMatch = engineGroup.variants.find(variant => variant === engineString);
        if (exactMatch) {
          matchedVariant = exactMatch;
          matchedFuelType = engineGroup.fuel_type;
          console.log('Found exact match for engine:', matchedVariant);
          break;
        }
      }
    }

    // If no exact match, try to match by the full engine string
    if (!matchedVariant) {
      for (const engineGroup of generation.engines) {
        if (Array.isArray(engineGroup.variants)) {
          for (const variant of engineGroup.variants) {
            if (variant.includes(engineString) || engineString.includes(variant)) {
              matchedVariant = variant;
              matchedFuelType = engineGroup.fuel_type;
              console.log('Found partial match for engine:', matchedVariant);
              break;
            }
          }
          if (matchedVariant) break;
        }
      }
    }

    // If still no match, try to extract the engine code and find a match
    if (!matchedVariant) {
      // Format examples:
      // "1.4 Fire (843A1000) (70 kW / 95 HP) (10.2015–...)"
      // "E 200 (135 kW / 184 HP) (03.2016–03.2023)" - Mercedes-Benz format without engine code

      let engineCode = '';

      // Special handling for Mercedes-Benz models which often don't have explicit engine codes
      if (brandName === 'Mercedes-Benz') {
        // For Mercedes-Benz, use the model designation (e.g., "A 180", "C 200") as the engine code
        const parts = engineString.split(' ');
        if (parts.length >= 2) {
          engineCode = parts[0] + ' ' + parts[1];
        } else {
          engineCode = parts[0];
        }
      } else {
        // For other brands, try to extract the engine code from parentheses
        const engineCodeMatch = engineString.match(/\(([^)]+)\)/);

        if (engineCodeMatch) {
          // Check if the first parenthesized part is a power value (kW / HP)
          const isPowerValue = engineCodeMatch[1].includes('kW') || engineCodeMatch[1].includes('HP');

          if (isPowerValue) {
            // If it's a power value, use the first part of the variant string
            const modelDesignation = engineString.split(' ')[0];
            engineCode = modelDesignation.trim();
          } else {
            // For variants with explicit engine codes, use the first parenthesized part
            engineCode = engineCodeMatch[1];
          }
        }
      }

      console.log('Extracted engine code:', engineCode);

      if (engineCode) {
        // Look for a variant that contains this engine code
        for (const engineGroup of generation.engines) {
          if (Array.isArray(engineGroup.variants)) {
            for (const variant of engineGroup.variants) {
              // Check if the variant contains the engine code in parentheses
              if (variant.includes(engineCode)) {
                matchedVariant = variant;
                matchedFuelType = engineGroup.fuel_type;
                console.log('Found match by engine code:', matchedVariant);
                break;
              }
            }
            if (matchedVariant) break;
          }
        }
      }
    }

    // If we still don't have a match, try matching by power values
    if (!matchedVariant) {
      const powerMatch = engineString.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
      if (powerMatch) {
        const powerKW = parseInt(powerMatch[1]);
        const powerHP = parseInt(powerMatch[2]);

        console.log('Extracted power values:', { powerKW, powerHP });

        for (const engineGroup of generation.engines) {
          if (Array.isArray(engineGroup.variants)) {
            for (const variant of engineGroup.variants) {
              const variantPowerMatch = variant.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
              if (variantPowerMatch) {
                const variantKW = parseInt(variantPowerMatch[1]);
                const variantHP = parseInt(variantPowerMatch[2]);

                if (variantKW === powerKW && variantHP === powerHP) {
                  matchedVariant = variant;
                  matchedFuelType = engineGroup.fuel_type;
                  console.log('Found match by power values:', matchedVariant);
                  break;
                }
              }
            }
            if (matchedVariant) break;
          }
        }
      }
    }

    // If we still don't have a match, return null
    if (!matchedVariant) {
      console.warn('No matching engine variant found for:', engineString);
      return null;
    }

    // Extract engine code from the matched variant
    let engineCode = '';

    // Special handling for Mercedes-Benz models which often don't have explicit engine codes
    if (brandName === 'Mercedes-Benz') {
      // For Mercedes-Benz, use the model designation (e.g., "A 180", "C 200") as the engine code
      const parts = matchedVariant.split(' ');
      if (parts.length >= 2) {
        engineCode = parts[0] + ' ' + parts[1];
      } else {
        engineCode = parts[0];
      }
    } else {
      // For other brands, try to extract the engine code from parentheses
      const codeMatch = matchedVariant.match(/\(([^)]+)\)/);

      if (codeMatch) {
        // Check if the first parenthesized part is a power value (kW / HP)
        const isPowerValue = codeMatch[1].includes('kW') || codeMatch[1].includes('HP');

        if (isPowerValue) {
          // If it's a power value, use the first part of the variant string
          const modelDesignation = matchedVariant.split(' ')[0];
          engineCode = modelDesignation.trim();
        } else {
          // For variants with explicit engine codes, use the first parenthesized part
          engineCode = codeMatch[1];
        }
      }
    }

    // Extract power values from the matched variant
    const powerMatch = matchedVariant.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
    const powerKW = powerMatch ? parseInt(powerMatch[1]) : 0;
    const powerHP = powerMatch ? parseInt(powerMatch[2]) : 0;

    // Create a vehicle object with all the information
    const vehicle = {
      id: `${brandToUse}-${model}-${generationCode}-${bodyType}-${engineCode}`.replace(/\s+/g, '-').toLowerCase(),
      type: 'car',
      brand: brandToUse,
      model: model,
      generation: generationCode,
      bodyType: bodyType,
      engineType: engineCode,
      engineDetails: {
        code: engineCode,
        fuel_type: matchedFuelType,
        power_kW: powerKW,
        power_HP: powerHP,
        fullName: matchedVariant
      },
      yearFrom: generation.start,
      yearTo: generation.end,
      // These would be fetched from your product database in a real implementation
      myProductCount: Math.floor(Math.random() * 30) + 5,
      allProductCount: Math.floor(Math.random() * 50) + 20
    };

    console.log('Created vehicle object:', vehicle);
    return vehicle;
  } catch (error) {
    console.error('Error getting vehicle by identifiers:', error);
    return null;
  }
};

/**
 * Fetch all vehicles
 * @param {string} brandName - Optional brand name to filter by
 * @param {string} modelUniqueId - Optional model uniqueId to filter by
 * @returns {Promise<Array>} - Array of vehicle objects
 */
export const fetchAllVehicles = async (brandName = null, modelUniqueId = null) => {
  // If no filters are provided, use the direct variant counting method to ensure all variants are included
  if (!brandName && !modelUniqueId) {
    try {
      console.log('Using direct variant counting method to ensure all variants are included');
      const counts = await countTotalVariants();
      const allVariants = counts.allVariants;

      console.log(`Processing ${allVariants.length} variants from direct file scan`);

      // Convert all variants to vehicle objects
      const vehicles = allVariants.map(variantInfo => {
        try {
          // Extract engine details from the variant string
          const variant = variantInfo.variant;

          // Use the engineCode and engineName from the variant info if available
          let engineCode = variantInfo.engineCode || '';
          let engineName = variantInfo.engineName || '';

          // If engineCode is not available, extract it
          if (!engineCode) {
            // Check if the variant has an engine code in parentheses
            const codeMatch = variant.match(/\(([^)]+)\)/);

            if (codeMatch) {
              // Check if the first parenthesized part is a power value (kW / HP)
              const isPowerValue = codeMatch[1].includes('kW') || codeMatch[1].includes('HP');

              if (isPowerValue) {
                // For variants without explicit engine codes, use the first part of the variant string
                const parts = variant.split(' ');
                engineCode = parts[0];
                if (parts.length > 1 && !isNaN(parseFloat(parts[0]))) {
                  // If the first part is a number (like "1.6"), include the second part if it exists
                  engineCode = parts.length > 1 ? `${parts[0]} ${parts[1]}` : parts[0];
                }
              } else {
                // For variants with explicit engine codes, use the first parenthesized part
                engineCode = codeMatch[1];
              }
            }
          }

          // Special handling for Mercedes-Benz variants
          if (variantInfo.brand === 'Mercedes-Benz') {
            // For Mercedes-Benz, use the model designation (e.g., "A 180", "C 200") as the engine code
            const parts = variant.split(' ');
            if (parts.length >= 2) {
              engineCode = parts[0] + ' ' + parts[1];
            } else {
              engineCode = parts[0];
            }
          }

          // Extract power values - look for the pattern (XX kW / YY HP)
          const powerKW = variantInfo.powerKW || 0;
          const powerHP = variantInfo.powerHP || 0;

          // Extract date range - use the one from variantInfo or extract it
          let dateRange = variantInfo.dateRange || '';
          if (!dateRange) {
            const dateRangeMatch = variant.match(/\(([^)]+)\)$/);
            dateRange = dateRangeMatch ? dateRangeMatch[1] : '';
          }

          // Extract engine name if not already available
          if (!engineName) {
            const engineNameMatch = variant.match(/^(.*?)\s*\(/);
            engineName = engineNameMatch ? engineNameMatch[1].trim() : '';
          }

          // Create a truly unique ID for this vehicle that includes all relevant details
          const variantHash = variant.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
          const vehicleId = `${variantInfo.brand}-${variantInfo.model}-${variantInfo.generation}-${variantInfo.bodyType}-${engineCode}-${powerKW}-${powerHP}-${variantHash}`.replace(/\s+/g, '-').toLowerCase();

          // Create the vehicle object with all available information
          return {
            id: vehicleId,
            type: 'car',
            brand: variantInfo.brand,
            model: variantInfo.model,
            generation: variantInfo.generation,
            bodyType: variantInfo.bodyType,
            engineType: engineCode,
            engineDetails: {
              code: engineCode,
              fuel_type: variantInfo.fuelType,
              power_kW: powerKW,
              power_HP: powerHP,
              fullName: variant,
              engineName: engineName,
              dateRange: dateRange
            },
            displayName: `${variantInfo.brand} ${variantInfo.model} ${variantInfo.generation} ${engineName} (${powerKW} kW / ${powerHP} HP)`,
            // These would be fetched from your product database in a real implementation
            myProductCount: Math.floor(Math.random() * 30) + 5,
            allProductCount: Math.floor(Math.random() * 50) + 20
          };
        } catch (error) {
          console.error(`Error processing variant: "${variantInfo.variant}"`, error);
          // Create a fallback vehicle object with basic information to ensure it's included
          const variantHash = variantInfo.variant.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
          const vehicleId = `${variantInfo.brand}-${variantInfo.model}-${variantInfo.generation}-${variantInfo.bodyType}-unknown-${variantHash}`.replace(/\s+/g, '-').toLowerCase();

          return {
            id: vehicleId,
            type: 'car',
            brand: variantInfo.brand,
            model: variantInfo.model,
            generation: variantInfo.generation,
            bodyType: variantInfo.bodyType,
            engineType: 'Unknown',
            engineDetails: {
              code: 'Unknown',
              fuel_type: variantInfo.fuelType || 'Unknown',
              power_kW: 0,
              power_HP: 0,
              fullName: variantInfo.variant
            },
            displayName: `${variantInfo.brand} ${variantInfo.model} ${variantInfo.generation} ${variantInfo.variant}`,
            myProductCount: Math.floor(Math.random() * 30) + 5,
            allProductCount: Math.floor(Math.random() * 50) + 20
          };
        }
      });

      // Log statistics about the vehicles created
      console.log(`Created ${vehicles.length} vehicle objects using direct method`);

      // Count vehicles by brand for verification
      const brandCounts = {};
      vehicles.forEach(vehicle => {
        const brand = vehicle.brand;
        brandCounts[brand] = (brandCounts[brand] || 0) + 1;
      });

      console.log('Vehicles by brand (top 5):');
      Object.entries(brandCounts)
        .sort((a, b) => b[1] - a[1]) // Sort by count (descending)
        .slice(0, 5) // Take top 5
        .forEach(([brand, count]) => {
          console.log(`  ${brand}: ${count} vehicles`);
        });

      return vehicles;
    } catch (error) {
      console.error('Error using direct variant counting method:', error);
      console.log('Falling back to standard method');
      // Fall back to the standard method if there's an error
    }
  }
  try {
    console.log('Fetching all vehicles with filters:', { brandName, modelUniqueId });
    let data;

    if (USE_SUPABASE) {
      // Fetch from Supabase
      data = await getVehicleData('car');
    } else {
      // Use local data - ensure it's loaded
      if (carsData.length === 0) {
        data = await loadLocalData();
      } else {
        data = carsData;
      }
    }

    // If we still don't have data, return an empty array
    if (!data || !Array.isArray(data)) {
      console.warn('No car data available');
      return [];
    }

    const vehicles = [];
    let selectedModelInfo = null;

    // If modelUniqueId is provided, parse it to get model details
    if (modelUniqueId) {
      selectedModelInfo = parseUniqueId(modelUniqueId);
      console.log('fetchAllVehicles: Parsed model uniqueId:', selectedModelInfo);

      // If brand is not provided in the uniqueId but we have it as a parameter, add it
      if (!selectedModelInfo.brand && brandName) {
        selectedModelInfo.brand = brandName;
        console.log('fetchAllVehicles: Added brand to selectedModelInfo:', selectedModelInfo);
      }
    }

    // Filter by brand if provided
    const brandsToProcess = brandName
      ? [data.find(b => b.brand === brandName)].filter(Boolean)
      : data;

    if (brandName && brandsToProcess.length === 0) {
      console.warn(`Brand not found: "${brandName}"`);
      return [];
    }

    console.log(`Processing ${brandsToProcess.length} brands`);

    // Track statistics for debugging
    let totalModels = 0;
    let totalGenerations = 0;
    let totalEngineGroups = 0;
    let totalEngineVariants = 0;
    let totalVehicles = 0;

    for (const brand of brandsToProcess) {
      console.log(`Processing brand: ${brand.brand} with ${brand.models.length} models`);
      totalModels += brand.models.length;

      for (const model of brand.models) {
        // Skip if we're filtering by model and this isn't the one
        if (selectedModelInfo && model.model.toLowerCase() !== selectedModelInfo.model.toLowerCase()) {
          continue;
        }

        console.log(`Processing model: ${model.model} with ${model.generations ? model.generations.length : 0} generations`);

        if (!model.generations || model.generations.length === 0) {
          console.warn(`No generations found for model: ${model.model}`);
          continue;
        }

        totalGenerations += model.generations.length;

        for (const generation of model.generations) {
          // Skip if we're filtering by generation and this isn't the one
          if (selectedModelInfo && selectedModelInfo.generationCode) {
            // First try exact match (case insensitive)
            if (generation.code.toLowerCase() === selectedModelInfo.generationCode.toLowerCase()) {
              console.log(`fetchAllVehicles: Found exact match for generation: ${generation.code}`);
            }
            // If exact match fails, try fuzzy matching for all models
            else {
              // Check if the generation code contains the selected generation code or vice versa
              const generationCodeMatches =
                generation.code.toLowerCase().includes(selectedModelInfo.generationCode.toLowerCase()) ||
                selectedModelInfo.generationCode.toLowerCase().includes(generation.code.toLowerCase());

              // Check if the generation code contains any numbers or codes from the selected generation
              const numbersInSelected = selectedModelInfo.generationCode.match(/\d+|[A-Z]\d+/g) || [];
              const numbersInGeneration = generation.code.match(/\d+|[A-Z]\d+/g) || [];

              const numberMatches = numbersInSelected.some(num =>
                numbersInGeneration.includes(num)
              );

              if (!generationCodeMatches && !numberMatches) {
                console.log(`fetchAllVehicles: Skipping generation ${generation.code} - no match with ${selectedModelInfo.generationCode}`);
                continue;
              } else {
                console.log(`fetchAllVehicles: Found fuzzy match for generation: ${generation.code} matches ${selectedModelInfo.generationCode}`);
              }
            }
          }

          // Skip if we're filtering by body type and this isn't the one
          if (selectedModelInfo &&
              selectedModelInfo.bodyType &&
              generation.body_type.toLowerCase() !== selectedModelInfo.bodyType.toLowerCase()) {
            continue;
          }

          // Skip if we're filtering by start date and this isn't the one
          if (selectedModelInfo &&
              selectedModelInfo.startDate &&
              generation.start !== selectedModelInfo.startDate) {
            continue;
          }

          console.log(`Processing generation: ${generation.code} (${generation.body_type}) with ${generation.engines ? generation.engines.length : 0} engine groups`);

          // Create a base vehicle object without specific engine details
          const baseVehicle = {
            id: `${brand.brand}-${model.model}-${generation.code}-${generation.body_type}`.replace(/\s+/g, '-').toLowerCase(),
            type: 'car',
            brand: brand.brand,
            model: model.model,
            generation: generation.code,
            bodyType: generation.body_type,
            yearFrom: generation.start,
            yearTo: generation.end,
            // These would be fetched from your product database in a real implementation
            myProductCount: Math.floor(Math.random() * 30) + 5,
            allProductCount: Math.floor(Math.random() * 50) + 20,
            displayName: `${brand.brand} ${model.model} ${generation.code} ${generation.body_type} (${generation.start}–${generation.end === '...' ? 'Present' : generation.end})`
          };

          // If there are engines, create a vehicle for each engine
          if (generation.engines && generation.engines.length > 0) {
            totalEngineGroups += generation.engines.length;
            let hasAddedEngine = false;

            for (const engineGroup of generation.engines) {
              if (engineGroup.fuel_type && Array.isArray(engineGroup.variants)) {
                console.log(`Processing ${engineGroup.variants.length} ${engineGroup.fuel_type} engine variants`);
                totalEngineVariants += engineGroup.variants.length;

                for (const variant of engineGroup.variants) {
                  try {
                    // Extract engine details
                    let engineCode = '';

                    // Check if the variant has an engine code in parentheses
                    const codeMatch = variant.match(/\(([^)]+)\)/);

                    if (codeMatch) {
                      // Check if the first parenthesized part is a power value (kW / HP)
                      const isPowerValue = codeMatch[1].includes('kW') || codeMatch[1].includes('HP');

                      if (isPowerValue) {
                        // For Mercedes-Benz variants without explicit engine codes, use the model designation
                        const modelDesignation = variant.split(' ')[0] + ' ' + variant.split(' ')[1];
                        engineCode = modelDesignation.trim();
                      } else {
                        // For variants with explicit engine codes, use the first parenthesized part
                        engineCode = codeMatch[1];
                      }
                    }

                    const powerMatch = variant.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
                    const powerKW = powerMatch ? parseInt(powerMatch[1]) : 0;
                    const powerHP = powerMatch ? parseInt(powerMatch[2]) : 0;

                    // Extract date range - usually the last parenthesized part
                    const dateRangeMatch = variant.match(/\(([^)]+)\)$/);
                    const dateRange = dateRangeMatch ? dateRangeMatch[1] : '';

                    // Extract engine name - everything before the first parenthesis
                    const engineNameMatch = variant.match(/^(.*?)\s*\(/);
                    const engineName = engineNameMatch ? engineNameMatch[1].trim() : '';

                    // Create a truly unique ID for this vehicle with engine
                    // Include the full variant string to ensure uniqueness
                    const variantHash = variant.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
                    const vehicleId = `${baseVehicle.id}-${engineCode}-${powerKW}-${powerHP}-${variantHash}`.replace(/\s+/g, '-').toLowerCase();

                    // Create a vehicle with this engine
                    const vehicle = {
                      ...baseVehicle,
                      id: vehicleId,
                      engineType: engineCode,
                      engineDetails: {
                        code: engineCode,
                        fuel_type: engineGroup.fuel_type,
                        power_kW: powerKW,
                        power_HP: powerHP,
                        fullName: variant,
                        engineName: engineName,
                        dateRange: dateRange
                      },
                      // Update display name to include engine details
                      displayName: `${brand.brand} ${model.model} ${generation.code} ${engineName} (${powerKW} kW / ${powerHP} HP)`
                    };

                    vehicles.push(vehicle);
                    totalVehicles++;
                    hasAddedEngine = true;
                  } catch (variantError) {
                    console.error(`Error processing engine variant: "${variant}"`, variantError);
                    // Still add a vehicle with this variant to ensure it's included
                    // Create a unique ID based on the variant string
                    const variantHash = variant.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
                    const vehicleId = `${baseVehicle.id}-unknown-${variantHash}`.replace(/\s+/g, '-').toLowerCase();
                    vehicles.push({
                      ...baseVehicle,
                      id: vehicleId,
                      engineType: 'Unknown',
                      engineDetails: {
                        code: 'Unknown',
                        fuel_type: engineGroup.fuel_type,
                        power_kW: 0,
                        power_HP: 0,
                        fullName: variant
                      },
                      displayName: `${brand.brand} ${model.model} ${generation.code} ${variant}`
                    });
                    totalVehicles++;
                    hasAddedEngine = true;
                  }
                }
              } else {
                console.warn(`Invalid engine group format for ${engineGroup.fuel_type || 'unknown fuel type'}`);
              }
            }

            // If no engines were added, add the base vehicle with generic engine details
            if (!hasAddedEngine) {
              vehicles.push({
                ...baseVehicle,
                engineDetails: {
                  code: 'Unknown',
                  fuel_type: 'Unknown',
                  fullName: 'Engine details not available'
                }
              });
              totalVehicles++;
            }
          } else {
            // If no engines, add the base vehicle with generic engine details
            console.warn(`No engines found for ${model.model} (${generation.code})`);
            vehicles.push({
              ...baseVehicle,
              engineDetails: {
                code: 'Unknown',
                fuel_type: 'Unknown',
                fullName: 'Engine details not available'
              }
            });
            totalVehicles++;
          }
        }
      }
    }

    // Log statistics
    console.log(`Processed ${totalModels} models, ${totalGenerations} generations, ${totalEngineGroups} engine groups, ${totalEngineVariants} engine variants`);
    console.log(`Created ${vehicles.length} vehicle objects (before deduplication)`);

    // Ensure unique vehicles by checking both ID and engine variant
    const uniqueVehicles = [];
    const vehicleIds = new Set();
    const engineVariants = new Set();
    const skippedVariants = new Map(); // Track variants that are skipped for debugging

    // Create a map to track variants by their unique key for better debugging
    const variantMap = new Map();

    for (const vehicle of vehicles) {
      // Extract engine details
      const engineVariant = vehicle.engineDetails?.fullName || '';
      const powerKW = vehicle.engineDetails?.power_kW || 0;
      const powerHP = vehicle.engineDetails?.power_HP || 0;
      const engineCode = vehicle.engineDetails?.code || '';
      const fuelType = vehicle.engineDetails?.fuel_type || '';

      // Create a more comprehensive unique key that includes all relevant details
      // This ensures we don't miss any subtle differences between variants
      // Include fuel type and power values to distinguish between similar variants
      const uniqueKey = `${vehicle.brand}|${vehicle.model}|${vehicle.generation}|${vehicle.bodyType}|${engineVariant}|${fuelType}|${powerKW}|${powerHP}`;

      // Check if this is a duplicate vehicle
      let isDuplicate = vehicleIds.has(uniqueKey);

      // For Mercedes-Benz style variants, we need a more sophisticated check
      if (!isDuplicate && vehicle.brand === 'Mercedes-Benz' && engineVariant) {
        // Check if we already have a vehicle with the same model designation and power values
        const existingVehicle = uniqueVehicles.find(v => {
          // Must be same model, generation, and body type
          if (v.brand !== vehicle.brand || v.model !== vehicle.model ||
              v.generation !== vehicle.generation || v.bodyType !== vehicle.bodyType) {
            return false;
          }

          // Check if power values and fuel type match
          if (v.engineDetails?.power_kW === powerKW &&
              v.engineDetails?.power_HP === powerHP &&
              v.engineDetails?.fuel_type === fuelType) {

            // For Mercedes-Benz style variants (A 180, C 200, etc.), check if the model designation is the same
            const thisModelDesignation = engineVariant.split(' ')[0] + ' ' + (engineVariant.split(' ')[1] || '');
            const existingModelDesignation = (v.engineDetails?.fullName || '').split(' ')[0] + ' ' +
                                            ((v.engineDetails?.fullName || '').split(' ')[1] || '');

            if (thisModelDesignation.trim() === existingModelDesignation.trim()) {
              return true;
            }
          }

          return false;
        });

        isDuplicate = !!existingVehicle;
      }

      if (!isDuplicate) {
        vehicleIds.add(uniqueKey);
        variantMap.set(uniqueKey, vehicle);

        // Also track unique engine variants
        if (engineVariant) {
          engineVariants.add(engineVariant);
        }

        uniqueVehicles.push(vehicle);
      } else {
        // Track skipped variants for debugging
        if (!skippedVariants.has(uniqueKey)) {
          skippedVariants.set(uniqueKey, []);
        }
        skippedVariants.get(uniqueKey).push(vehicle);
      }
    }

    // Log information about skipped variants
    if (skippedVariants.size > 0) {
      console.log(`Skipped ${skippedVariants.size} unique vehicle variants during deduplication`);

      // Count total skipped variants
      let totalSkipped = 0;
      skippedVariants.forEach(list => {
        totalSkipped += list.length;
      });
      console.log(`Total skipped variants: ${totalSkipped} (including duplicates of the same key)`);

      // Log details about the first few skipped variants for debugging
      let count = 0;
      for (const [key, skippedList] of skippedVariants.entries()) {
        if (count < 3) { // Only log the first 3 for brevity
          const originalVehicle = variantMap.get(key);
          const skippedVehicle = skippedList[0];

          if (originalVehicle && skippedVehicle) {
            console.log(`Skipped duplicate: ${skippedVehicle.brand} ${skippedVehicle.model} ${skippedVehicle.generation} - ${skippedVehicle.engineDetails?.fullName}`);
            console.log(`  Original: ${originalVehicle.engineDetails?.fullName} (${originalVehicle.engineDetails?.fuel_type})`);
            console.log(`  Skipped: ${skippedVehicle.engineDetails?.fullName} (${skippedVehicle.engineDetails?.fuel_type})`);

            if (skippedList.length > 1) {
              console.log(`  ... and ${skippedList.length - 1} more duplicates of this variant`);
            }
          }

          count++;
        }
      }
    }

    console.log(`Returning ${uniqueVehicles.length} unique vehicles with ${engineVariants.size} unique engine variants (after deduplication)`);

    // Log a breakdown of vehicles by brand for verification
    const brandCounts = {};
    uniqueVehicles.forEach(vehicle => {
      const brand = vehicle.brand;
      brandCounts[brand] = (brandCounts[brand] || 0) + 1;
    });

    console.log('Vehicles by brand:');
    Object.entries(brandCounts)
      .sort((a, b) => b[1] - a[1]) // Sort by count (descending)
      .forEach(([brand, count]) => {
        console.log(`  ${brand}: ${count} vehicles`);
      });

    // Log a sample of vehicles for verification
    if (uniqueVehicles.length > 0) {
      console.log('Sample vehicles:');
      const sampleSize = Math.min(3, uniqueVehicles.length);
      for (let i = 0; i < sampleSize; i++) {
        const vehicle = uniqueVehicles[i];
        console.log(`  ${i+1}. ${vehicle.brand} ${vehicle.model} ${vehicle.generation} - Engine: ${vehicle.engineDetails?.fullName || 'Unknown'}`);
      }
    }

    return uniqueVehicles;
  } catch (error) {
    console.error('Error fetching all vehicles:', error);
    return [];
  }
};

/**
 * Securely store the vehicle data in Supabase
 * This is a placeholder function that would be implemented when Supabase is set up
 * @param {Array} vehicleData - The vehicle data to store
 * @returns {Promise<boolean>} - Success status
 */
export const storeVehicleDataInSupabase = async (vehicleData) => {
  try {
    // This would be implemented to store the data in Supabase
    // For now, it's just a placeholder
    console.log('Would store vehicle data in Supabase:', vehicleData.length, 'brands');
    return true;
  } catch (error) {
    console.error('Error storing vehicle data in Supabase:', error);
    return false;
  }
};
