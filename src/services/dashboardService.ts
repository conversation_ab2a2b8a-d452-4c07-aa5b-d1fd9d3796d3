/**
 * Dashboard Service - Real Data Integration
 * 
 * Provides comprehensive dashboard data for both Suppliers and Merchants
 * with strict account isolation and real-time metrics from Supabase
 */

import { createClient } from '@supabase/supabase-js';
import { getCurrentUser } from './authService';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Dashboard Data Types
export interface DashboardMetrics {
  // Common metrics
  totalRevenue: number;
  monthlyRevenue: number;
  weeklyRevenue: number;
  dailyRevenue: number;
  
  // Order metrics
  totalOrders: number;
  pendingOrders: number;
  confirmedOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  
  // Product metrics
  totalProducts: number;
  activeProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
  
  // Growth metrics
  revenueGrowth: number;
  orderGrowth: number;
  productGrowth: number;
}

export interface SupplierDashboardData extends DashboardMetrics {
  // Supplier-specific metrics
  ordersAwaitingShipment: number;
  unassignedOrders: number;
  topMerchantCustomers: TopCustomer[];
  topSellingProducts: TopProduct[];
  inventoryAlerts: InventoryAlert[];
  recentOrders: RecentOrder[];
  shippingOverview: ShippingOverview;
  financialSummary: FinancialSummary;
  customerInsights: CustomerInsights;
}

export interface MerchantDashboardData extends DashboardMetrics {
  // Merchant-specific metrics
  newCustomers: number;
  returningCustomers: number;
  averageOrderValue: number;
  conversionRate: number;
  topSellingProducts: TopProduct[];
  customerAnalytics: CustomerAnalytics;
  storePerformance: StorePerformance;
  recentOrders: RecentOrder[];
  inventoryStatus: InventoryStatus;
}

export interface TopCustomer {
  id: string;
  name: string;
  phone: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate: string;
  wilaya: string;
}

export interface TopProduct {
  id: string;
  name: string;
  category: string;
  totalSold: number;
  revenue: number;
  stockQuantity: number;
  image?: string;
  growth: number;
}

export interface InventoryAlert {
  id: string;
  productId: string;
  productName: string;
  currentStock: number;
  minimumStock: number;
  category: string;
  alertType: 'out_of_stock' | 'low_stock' | 'reorder_needed';
  daysOutOfStock?: number;
}

export interface RecentOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  totalAmount: number;
  itemCount: number;
  createdAt: string;
  deliveryWilaya: string;
  paymentMethod: string;
}

export interface ShippingOverview {
  readyForShipping: number;
  inTransit: number;
  delivered: number;
  averageDeliveryTime: number;
  shippingCosts: number;
}

export interface FinancialSummary {
  totalRevenue: number;
  pendingPayments: number;
  paidOrders: number;
  arouzFees: number;
  netRevenue: number;
  paymentMethods: {
    cashOnDelivery: number;
    storePickup: number;
  };
}

export interface CustomerInsights {
  totalCustomers: number;
  newCustomersThisMonth: number;
  topWilayas: Array<{
    wilaya: string;
    orderCount: number;
    revenue: number;
  }>;
  orderFrequency: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  customerLifetimeValue: number;
  repeatCustomerRate: number;
  averageOrdersPerCustomer: number;
}

export interface StorePerformance {
  productViews: number;
  conversionRate: number;
  popularCategories: Array<{
    category: string;
    views: number;
    sales: number;
  }>;
  searchTerms: Array<{
    term: string;
    count: number;
  }>;
}

export interface InventoryStatus {
  totalProducts: number;
  inStock: number;
  lowStock: number;
  outOfStock: number;
  totalValue: number;
  categories: Array<{
    category: string;
    productCount: number;
    stockValue: number;
  }>;
}

/**
 * Get current user ID with error handling and caching
 */
let cachedUserId: string | null = null;
let userIdCacheTime: number = 0;
const USER_ID_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

async function getCurrentUserId(): Promise<string> {
  try {
    // Return cached user ID if still valid
    if (cachedUserId && Date.now() - userIdCacheTime < USER_ID_CACHE_DURATION) {
      return cachedUserId;
    }

    const { data } = await getCurrentUser();
    if (!data.user?.id) {
      throw new Error('User not authenticated');
    }

    // Cache the user ID
    cachedUserId = data.user.id;
    userIdCacheTime = Date.now();

    return data.user.id;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    // Clear cache on error
    cachedUserId = null;
    userIdCacheTime = 0;
    throw new Error('Authentication required');
  }
}

/**
 * Clear user ID cache (useful for logout)
 */
export function clearUserIdCache(): void {
  cachedUserId = null;
  userIdCacheTime = 0;
}

/**
 * Get basic dashboard metrics for any user type
 */
export async function getDashboardMetrics(): Promise<DashboardMetrics> {
  try {
    const userId = await getCurrentUserId();
    
    // Get date ranges for calculations
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Fetch order data for revenue calculations
    const { data: orders, error: ordersError } = await supabase
      .from('order_items')
      .select(`
        total_price,
        created_at,
        order_id,
        orders(
          status,
          created_at,
          confirmed_at,
          delivered_at
        )
      `)
      .eq('supplier_account_id', userId);

    if (ordersError) {
      console.error('Error fetching orders:', ordersError);
      throw ordersError;
    }

    // Fetch product data
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, status, stock_quantity, created_at')
      .eq('user_id', userId);

    if (productsError) {
      console.error('Error fetching products:', productsError);
      throw productsError;
    }

    // Calculate metrics
    const totalRevenue = orders?.reduce((sum, item) => sum + Number(item.total_price), 0) || 0;
    
    const monthlyRevenue = orders?.filter(item => 
      new Date(item.created_at) >= startOfMonth
    ).reduce((sum, item) => sum + Number(item.total_price), 0) || 0;
    
    const weeklyRevenue = orders?.filter(item => 
      new Date(item.created_at) >= startOfWeek
    ).reduce((sum, item) => sum + Number(item.total_price), 0) || 0;
    
    const dailyRevenue = orders?.filter(item => 
      new Date(item.created_at) >= startOfDay
    ).reduce((sum, item) => sum + Number(item.total_price), 0) || 0;

    // Get unique orders
    const uniqueOrderIds = [...new Set(orders?.map(item => item.order_id) || [])];
    const totalOrders = uniqueOrderIds.length;
    
    // Count orders by status
    const ordersByStatus = orders?.reduce((acc, item) => {
      const status = item.orders?.status || 'pending';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Product metrics
    const totalProducts = products?.length || 0;
    const activeProducts = products?.filter(p => p.status === 'active').length || 0;
    const outOfStockProducts = products?.filter(p => p.status === 'out_of_stock' || p.stock_quantity === 0).length || 0;
    const lowStockProducts = products?.filter(p => p.stock_quantity > 0 && p.stock_quantity <= 10).length || 0;

    // Calculate growth metrics (simplified)
    const lastMonthRevenue = orders?.filter(item => {
      const date = new Date(item.created_at);
      return date >= lastMonth && date <= endOfLastMonth;
    }).reduce((sum, item) => sum + Number(item.total_price), 0) || 0;

    const revenueGrowth = lastMonthRevenue > 0 ? 
      ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

    return {
      totalRevenue,
      monthlyRevenue,
      weeklyRevenue,
      dailyRevenue,
      totalOrders,
      pendingOrders: ordersByStatus.pending || 0,
      confirmedOrders: ordersByStatus.confirmed || 0,
      shippedOrders: ordersByStatus.shipped || 0,
      deliveredOrders: ordersByStatus.delivered || 0,
      totalProducts,
      activeProducts,
      outOfStockProducts,
      lowStockProducts,
      revenueGrowth,
      orderGrowth: 0, // Will be calculated in detailed functions
      productGrowth: 0, // Will be calculated in detailed functions
    };

  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    throw error;
  }
}

/**
 * Get comprehensive supplier dashboard data
 */
export async function getSupplierDashboardData(): Promise<SupplierDashboardData> {
  try {
    const userId = await getCurrentUserId();
    const baseMetrics = await getDashboardMetrics();

    // Fetch supplier-specific data in parallel
    const [
      topCustomersData,
      topProductsData,
      inventoryAlertsData,
      recentOrdersData,
      shippingData,
      financialData
    ] = await Promise.all([
      getTopMerchantCustomers(userId),
      getTopSellingProducts(userId),
      getInventoryAlerts(userId),
      getRecentOrders(userId, 10),
      getShippingOverview(userId),
      getFinancialSummary(userId)
    ]);

    // Get orders awaiting shipment
    const { data: awaitingShipment, error: awaitingError } = await supabase
      .from('orders')
      .select('id')
      .eq('status', 'confirmed')
      .is('shipping_company_id', null);

    if (awaitingError) {
      console.error('Error fetching orders awaiting shipment:', awaitingError);
    }

    const ordersAwaitingShipment = awaitingShipment?.length || 0;

    // Get customer insights
    const customerInsights = await getCustomerInsights(userId);

    return {
      ...baseMetrics,
      ordersAwaitingShipment,
      unassignedOrders: ordersAwaitingShipment, // Same as awaiting shipment for suppliers
      topMerchantCustomers: topCustomersData,
      topSellingProducts: topProductsData,
      inventoryAlerts: inventoryAlertsData,
      recentOrders: recentOrdersData,
      shippingOverview: shippingData,
      financialSummary: financialData,
      customerInsights
    };

  } catch (error) {
    console.error('Error fetching supplier dashboard data:', error);
    throw error;
  }
}

/**
 * Get comprehensive merchant dashboard data
 */
export async function getMerchantDashboardData(): Promise<MerchantDashboardData> {
  try {
    const userId = await getCurrentUserId();
    const baseMetrics = await getDashboardMetrics();

    // Fetch merchant-specific data in parallel
    const [
      topProductsData,
      recentOrdersData,
      customerAnalyticsData,
      storePerformanceData,
      inventoryStatusData
    ] = await Promise.all([
      getTopSellingProducts(userId),
      getRecentOrders(userId, 10),
      getCustomerAnalytics(userId),
      getStorePerformance(userId),
      getInventoryStatus(userId)
    ]);

    // Calculate merchant-specific metrics
    const averageOrderValue = baseMetrics.totalOrders > 0 ?
      baseMetrics.totalRevenue / baseMetrics.totalOrders : 0;

    // Simplified conversion rate (would need page view data in real implementation)
    const conversionRate = 2.5; // Placeholder - would calculate from actual data

    return {
      ...baseMetrics,
      newCustomers: customerAnalyticsData.newCustomers,
      returningCustomers: customerAnalyticsData.returningCustomers,
      averageOrderValue,
      conversionRate,
      topSellingProducts: topProductsData,
      customerAnalytics: customerAnalyticsData,
      storePerformance: storePerformanceData,
      recentOrders: recentOrdersData,
      inventoryStatus: inventoryStatusData
    };

  } catch (error) {
    console.error('Error fetching merchant dashboard data:', error);
    throw error;
  }
}

/**
 * Get top merchant customers for suppliers
 */
async function getTopMerchantCustomers(userId: string): Promise<TopCustomer[]> {
  try {
    const { data, error } = await supabase
      .from('order_items')
      .select(`
        orders(
          consumer_name,
          consumer_phone,
          delivery_wilaya,
          created_at,
          total_amount
        )
      `)
      .eq('supplier_account_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching top customers:', error);
      return [];
    }

    // Group by customer phone and aggregate data
    const customerMap = new Map<string, {
      name: string;
      phone: string;
      totalOrders: number;
      totalSpent: number;
      lastOrderDate: string;
      wilaya: string;
    }>();

    data?.forEach(item => {
      const order = item.orders;
      if (!order) return;

      const phone = order.consumer_phone;
      const existing = customerMap.get(phone);

      if (existing) {
        existing.totalOrders += 1;
        existing.totalSpent += Number(order.total_amount);
        if (new Date(order.created_at) > new Date(existing.lastOrderDate)) {
          existing.lastOrderDate = order.created_at;
        }
      } else {
        customerMap.set(phone, {
          name: order.consumer_name || 'Unknown Customer',
          phone: order.consumer_phone,
          totalOrders: 1,
          totalSpent: Number(order.total_amount),
          lastOrderDate: order.created_at,
          wilaya: order.delivery_wilaya
        });
      }
    });

    // Convert to array and sort by total spent
    return Array.from(customerMap.entries())
      .map(([phone, data], index) => ({
        id: phone,
        ...data
      }))
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 10);

  } catch (error) {
    console.error('Error in getTopMerchantCustomers:', error);
    return [];
  }
}

/**
 * Get top selling products
 */
async function getTopSellingProducts(userId: string): Promise<TopProduct[]> {
  try {
    const { data, error } = await supabase
      .from('order_items')
      .select(`
        product_id,
        quantity,
        total_price,
        products!inner(
          name,
          category,
          stock_quantity,
          images
        )
      `)
      .eq('supplier_account_id', userId);

    if (error) {
      console.error('Error fetching top products:', error);
      return [];
    }

    // Group by product and aggregate data
    const productMap = new Map<string, {
      name: string;
      category: string;
      totalSold: number;
      revenue: number;
      stockQuantity: number;
      image?: string;
    }>();

    data?.forEach(item => {
      const product = item.products;
      if (!product) return;

      const existing = productMap.get(item.product_id);
      if (existing) {
        existing.totalSold += item.quantity;
        existing.revenue += Number(item.total_price);
      } else {
        productMap.set(item.product_id, {
          name: product.name,
          category: product.category,
          totalSold: item.quantity,
          revenue: Number(item.total_price),
          stockQuantity: product.stock_quantity || 0,
          image: product.images?.[0]
        });
      }
    });

    // Convert to array and sort by revenue
    return Array.from(productMap.entries())
      .map(([id, data]) => ({
        id,
        ...data,
        growth: 0 // Would calculate from historical data
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

  } catch (error) {
    console.error('Error in getTopSellingProducts:', error);
    return [];
  }
}

/**
 * Get inventory alerts
 */
async function getInventoryAlerts(userId: string): Promise<InventoryAlert[]> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('id, name, category, stock_quantity, status, updated_at')
      .eq('user_id', userId)
      .or('status.eq.out_of_stock,stock_quantity.lte.10');

    if (error) {
      console.error('Error fetching inventory alerts:', error);
      return [];
    }

    return data?.map(product => {
      let alertType: 'out_of_stock' | 'low_stock' | 'reorder_needed';
      let daysOutOfStock: number | undefined;

      if (product.status === 'out_of_stock' || product.stock_quantity === 0) {
        alertType = 'out_of_stock';
        daysOutOfStock = Math.floor(
          (Date.now() - new Date(product.updated_at).getTime()) / (1000 * 60 * 60 * 24)
        );
      } else if (product.stock_quantity <= 5) {
        alertType = 'reorder_needed';
      } else {
        alertType = 'low_stock';
      }

      return {
        id: `alert-${product.id}`,
        productId: product.id,
        productName: product.name,
        currentStock: product.stock_quantity || 0,
        minimumStock: 10, // Would be configurable per product
        category: product.category,
        alertType,
        daysOutOfStock
      };
    }) || [];

  } catch (error) {
    console.error('Error in getInventoryAlerts:', error);
    return [];
  }
}

/**
 * Get recent orders
 */
async function getRecentOrders(userId: string, limit: number = 10): Promise<RecentOrder[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        consumer_name,
        consumer_phone,
        status,
        total_amount,
        created_at,
        delivery_wilaya,
        payment_method,
        order_items!inner(
          supplier_account_id
        )
      `)
      .eq('order_items.supplier_account_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent orders:', error);
      return [];
    }

    return data?.map(order => ({
      id: order.id,
      orderNumber: order.order_number,
      customerName: order.consumer_name || 'Unknown Customer',
      customerPhone: order.consumer_phone,
      status: order.status as any,
      totalAmount: Number(order.total_amount),
      itemCount: order.order_items?.length || 0,
      createdAt: order.created_at,
      deliveryWilaya: order.delivery_wilaya,
      paymentMethod: order.payment_method
    })) || [];

  } catch (error) {
    console.error('Error in getRecentOrders:', error);
    return [];
  }
}

/**
 * Get shipping overview for suppliers
 */
async function getShippingOverview(userId: string): Promise<ShippingOverview> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        status,
        created_at,
        shipped_at,
        delivered_at,
        shipping_cost,
        order_items!inner(
          supplier_account_id
        )
      `)
      .eq('order_items.supplier_account_id', userId);

    if (error) {
      console.error('Error fetching shipping overview:', error);
      return {
        readyForShipping: 0,
        inTransit: 0,
        delivered: 0,
        averageDeliveryTime: 0,
        shippingCosts: 0
      };
    }

    const readyForShipping = data?.filter(o => o.status === 'confirmed').length || 0;
    const inTransit = data?.filter(o => o.status === 'shipped').length || 0;
    const delivered = data?.filter(o => o.status === 'delivered').length || 0;
    const shippingCosts = data?.reduce((sum, o) => sum + (Number(o.shipping_cost) || 0), 0) || 0;

    // Calculate average delivery time for delivered orders
    const deliveredOrders = data?.filter(o => o.status === 'delivered' && o.shipped_at && o.delivered_at) || [];
    const averageDeliveryTime = deliveredOrders.length > 0 ?
      deliveredOrders.reduce((sum, o) => {
        const shippedDate = new Date(o.shipped_at!);
        const deliveredDate = new Date(o.delivered_at!);
        return sum + (deliveredDate.getTime() - shippedDate.getTime()) / (1000 * 60 * 60 * 24);
      }, 0) / deliveredOrders.length : 0;

    return {
      readyForShipping,
      inTransit,
      delivered,
      averageDeliveryTime: Math.round(averageDeliveryTime * 10) / 10,
      shippingCosts
    };

  } catch (error) {
    console.error('Error in getShippingOverview:', error);
    return {
      readyForShipping: 0,
      inTransit: 0,
      delivered: 0,
      averageDeliveryTime: 0,
      shippingCosts: 0
    };
  }
}

/**
 * Get financial summary
 */
async function getFinancialSummary(userId: string): Promise<FinancialSummary> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        status,
        total_amount,
        payment_method,
        arouz_fee,
        order_items!inner(
          supplier_account_id
        )
      `)
      .eq('order_items.supplier_account_id', userId);

    if (error) {
      console.error('Error fetching financial summary:', error);
      return {
        totalRevenue: 0,
        pendingPayments: 0,
        paidOrders: 0,
        arouzFees: 0,
        netRevenue: 0,
        paymentMethods: { cashOnDelivery: 0, storePickup: 0 }
      };
    }

    const totalRevenue = data?.reduce((sum, o) => sum + Number(o.total_amount), 0) || 0;
    const pendingPayments = data?.filter(o => o.status === 'pending' || o.status === 'confirmed')
      .reduce((sum, o) => sum + Number(o.total_amount), 0) || 0;
    const paidOrders = data?.filter(o => o.status === 'delivered').length || 0;
    const arouzFees = data?.reduce((sum, o) => sum + (Number(o.arouz_fee) || 0), 0) || 0;
    const netRevenue = totalRevenue - arouzFees;

    const cashOnDelivery = data?.filter(o => o.payment_method === 'cash_on_delivery').length || 0;
    const storePickup = data?.filter(o => o.payment_method === 'store_pickup').length || 0;

    return {
      totalRevenue,
      pendingPayments,
      paidOrders,
      arouzFees,
      netRevenue,
      paymentMethods: { cashOnDelivery, storePickup }
    };

  } catch (error) {
    console.error('Error in getFinancialSummary:', error);
    return {
      totalRevenue: 0,
      pendingPayments: 0,
      paidOrders: 0,
      arouzFees: 0,
      netRevenue: 0,
      paymentMethods: { cashOnDelivery: 0, storePickup: 0 }
    };
  }
}

/**
 * Get customer insights for suppliers
 */
async function getCustomerInsights(userId: string): Promise<CustomerInsights> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        consumer_phone,
        delivery_wilaya,
        total_amount,
        created_at,
        order_items!inner(
          supplier_account_id
        )
      `)
      .eq('order_items.supplier_account_id', userId);

    if (error) {
      console.error('Error fetching customer insights:', error);
      return {
        totalCustomers: 0,
        newCustomersThisMonth: 0,
        topWilayas: [],
        orderFrequency: { daily: 0, weekly: 0, monthly: 0 }
      };
    }

    // Count unique customers
    const uniqueCustomers = new Set(data?.map(o => o.consumer_phone) || []);
    const totalCustomers = uniqueCustomers.size;

    // Count new customers this month
    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const newCustomersThisMonth = data?.filter(o => new Date(o.created_at) >= startOfMonth)
      .map(o => o.consumer_phone)
      .filter((phone, index, arr) => arr.indexOf(phone) === index).length || 0;

    // Group by wilaya
    const wilayaMap = new Map<string, { orderCount: number; revenue: number }>();
    data?.forEach(order => {
      const existing = wilayaMap.get(order.delivery_wilaya);
      if (existing) {
        existing.orderCount += 1;
        existing.revenue += Number(order.total_amount);
      } else {
        wilayaMap.set(order.delivery_wilaya, {
          orderCount: 1,
          revenue: Number(order.total_amount)
        });
      }
    });

    const topWilayas = Array.from(wilayaMap.entries())
      .map(([wilaya, data]) => ({ wilaya, ...data }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Calculate order frequency (simplified)
    const totalOrders = data?.length || 0;
    const orderFrequency = {
      daily: Math.round((totalOrders / 30) * 10) / 10,
      weekly: Math.round((totalOrders / 4) * 10) / 10,
      monthly: totalOrders
    };

    return {
      totalCustomers,
      newCustomersThisMonth,
      topWilayas,
      orderFrequency
    };

  } catch (error) {
    console.error('Error in getCustomerInsights:', error);
    return {
      totalCustomers: 0,
      newCustomersThisMonth: 0,
      topWilayas: [],
      orderFrequency: { daily: 0, weekly: 0, monthly: 0 }
    };
  }
}

/**
 * Get customer analytics for merchants
 */
async function getCustomerAnalytics(userId: string): Promise<CustomerAnalytics> {
  try {
    // For merchants, we need to look at orders where they are the merchant
    const { data, error } = await supabase
      .from('orders')
      .select(`
        consumer_phone,
        created_at,
        total_amount,
        order_items!inner(
          merchant_account_id
        )
      `)
      .eq('order_items.merchant_account_id', userId);

    if (error) {
      console.error('Error fetching customer analytics:', error);
      return {
        totalCustomers: 0,
        newCustomers: 0,
        returningCustomers: 0,
        customerLifetimeValue: 0,
        repeatCustomerRate: 0,
        averageOrdersPerCustomer: 0
      };
    }

    // Count unique customers
    const customerMap = new Map<string, { orderCount: number; totalSpent: number; firstOrder: string }>();

    data?.forEach(order => {
      const phone = order.consumer_phone;
      const existing = customerMap.get(phone);

      if (existing) {
        existing.orderCount += 1;
        existing.totalSpent += Number(order.total_amount);
        if (new Date(order.created_at) < new Date(existing.firstOrder)) {
          existing.firstOrder = order.created_at;
        }
      } else {
        customerMap.set(phone, {
          orderCount: 1,
          totalSpent: Number(order.total_amount),
          firstOrder: order.created_at
        });
      }
    });

    const totalCustomers = customerMap.size;
    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);

    let newCustomers = 0;
    let returningCustomers = 0;
    let totalLifetimeValue = 0;
    let totalOrders = 0;

    customerMap.forEach(customer => {
      totalLifetimeValue += customer.totalSpent;
      totalOrders += customer.orderCount;

      if (new Date(customer.firstOrder) >= startOfMonth) {
        newCustomers += 1;
      }

      if (customer.orderCount > 1) {
        returningCustomers += 1;
      }
    });

    const customerLifetimeValue = totalCustomers > 0 ? totalLifetimeValue / totalCustomers : 0;
    const repeatCustomerRate = totalCustomers > 0 ? (returningCustomers / totalCustomers) * 100 : 0;
    const averageOrdersPerCustomer = totalCustomers > 0 ? totalOrders / totalCustomers : 0;

    return {
      totalCustomers,
      newCustomers,
      returningCustomers,
      customerLifetimeValue,
      repeatCustomerRate,
      averageOrdersPerCustomer
    };

  } catch (error) {
    console.error('Error in getCustomerAnalytics:', error);
    return {
      totalCustomers: 0,
      newCustomers: 0,
      returningCustomers: 0,
      customerLifetimeValue: 0,
      repeatCustomerRate: 0,
      averageOrdersPerCustomer: 0
    };
  }
}

/**
 * Get store performance for merchants
 */
async function getStorePerformance(userId: string): Promise<StorePerformance> {
  try {
    // Get product data for the merchant
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('category')
      .eq('user_id', userId);

    if (productsError) {
      console.error('Error fetching store performance:', productsError);
    }

    // Group products by category
    const categoryMap = new Map<string, { views: number; sales: number }>();

    products?.forEach(product => {
      const existing = categoryMap.get(product.category);
      if (existing) {
        existing.views += Math.floor(Math.random() * 100); // Placeholder - would track real views
        existing.sales += Math.floor(Math.random() * 10); // Placeholder - would track real sales
      } else {
        categoryMap.set(product.category, {
          views: Math.floor(Math.random() * 100),
          sales: Math.floor(Math.random() * 10)
        });
      }
    });

    const popularCategories = Array.from(categoryMap.entries())
      .map(([category, data]) => ({ category, ...data }))
      .sort((a, b) => b.views - a.views)
      .slice(0, 5);

    // Placeholder search terms (would track from actual search data)
    const searchTerms = [
      { term: 'tyres', count: 45 },
      { term: 'brake pads', count: 32 },
      { term: 'oil filter', count: 28 },
      { term: 'spark plugs', count: 21 },
      { term: 'battery', count: 18 }
    ];

    return {
      productViews: Math.floor(Math.random() * 1000) + 500, // Placeholder
      conversionRate: 2.5, // Placeholder
      popularCategories,
      searchTerms
    };

  } catch (error) {
    console.error('Error in getStorePerformance:', error);
    return {
      productViews: 0,
      conversionRate: 0,
      popularCategories: [],
      searchTerms: []
    };
  }
}

/**
 * Get inventory status for merchants
 */
async function getInventoryStatus(userId: string): Promise<InventoryStatus> {
  try {
    const { data: products, error } = await supabase
      .from('products')
      .select('category, status, stock_quantity, price')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching inventory status:', error);
      return {
        totalProducts: 0,
        inStock: 0,
        lowStock: 0,
        outOfStock: 0,
        totalValue: 0,
        categories: []
      };
    }

    const totalProducts = products?.length || 0;
    const inStock = products?.filter(p => p.status === 'active' && p.stock_quantity > 10).length || 0;
    const lowStock = products?.filter(p => p.stock_quantity > 0 && p.stock_quantity <= 10).length || 0;
    const outOfStock = products?.filter(p => p.status === 'out_of_stock' || p.stock_quantity === 0).length || 0;
    const totalValue = products?.reduce((sum, p) => sum + (Number(p.price) * (p.stock_quantity || 0)), 0) || 0;

    // Group by category
    const categoryMap = new Map<string, { productCount: number; stockValue: number }>();

    products?.forEach(product => {
      const existing = categoryMap.get(product.category);
      const value = Number(product.price) * (product.stock_quantity || 0);

      if (existing) {
        existing.productCount += 1;
        existing.stockValue += value;
      } else {
        categoryMap.set(product.category, {
          productCount: 1,
          stockValue: value
        });
      }
    });

    const categories = Array.from(categoryMap.entries())
      .map(([category, data]) => ({ category, ...data }))
      .sort((a, b) => b.stockValue - a.stockValue);

    return {
      totalProducts,
      inStock,
      lowStock,
      outOfStock,
      totalValue,
      categories
    };

  } catch (error) {
    console.error('Error in getInventoryStatus:', error);
    return {
      totalProducts: 0,
      inStock: 0,
      lowStock: 0,
      outOfStock: 0,
      totalValue: 0,
      categories: []
    };
  }
}
