import { supabase } from './authService';

export interface ShippingCompany {
  id: string;
  company_name: string;
  contact_phone: string;
  contact_email: string;
  login_code: string;
  base_delivery_cost: number;
  cost_per_km: number;
  minimum_order_value: number;
  coverage_areas: string[];
  service_types: string[];
  is_active: boolean;
  commission_rate: number;
  maximum_weight_kg: number;
  total_orders_completed: number;
  average_rating: number;
}

export interface ShippingCalculation {
  company_id: string;
  company_name: string;
  base_cost: number;
  distance_cost: number;
  total_cost: number;
  estimated_delivery: string;
  notes: string;
  service_types: string[];
  rating: number;
}

export interface ManualShippingInput {
  order_id: string;
  shipping_company_id: string;
  custom_shipping_cost?: number;
  shipping_notes?: string;
  shipping_method?: string;
}

// Get all active shipping companies
export async function getActiveShippingCompanies(): Promise<ShippingCompany[]> {
  try {
    console.log('🚚 Fetching active shipping companies...');
    
    const { data, error } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('is_active', true)
      .order('company_name');

    if (error) {
      console.error('❌ Error fetching shipping companies:', error);
      throw error;
    }

    console.log('✅ Shipping companies fetched:', data?.length || 0);
    return data || [];
  } catch (error) {
    console.error('❌ Exception in getActiveShippingCompanies:', error);
    throw error;
  }
}

// Calculate shipping cost for an order
export async function calculateShippingCost(
  orderValue: number,
  deliveryWilaya: string,
  companyId?: string
): Promise<ShippingCalculation[]> {
  try {
    console.log('🧮 Calculating shipping cost:', {
      orderValue,
      deliveryWilaya,
      companyId
    });

    const companies = await getActiveShippingCompanies();
    const calculations: ShippingCalculation[] = [];

    for (const company of companies) {
      // Skip if specific company requested and this isn't it
      if (companyId && company.id !== companyId) continue;

      // Check if company serves this wilaya
      const servesArea = company.coverage_areas.includes('All Wilayas') || 
                        company.coverage_areas.includes(deliveryWilaya);

      if (!servesArea) {
        console.log(`⚠️ ${company.company_name} doesn't serve ${deliveryWilaya}`);
        continue;
      }

      // Check minimum order value
      if (orderValue < company.minimum_order_value) {
        console.log(`⚠️ Order value ${orderValue} below minimum ${company.minimum_order_value} for ${company.company_name}`);
        continue;
      }

      // Basic calculation (can be enhanced later with real distance)
      const baseDistance = 50; // Default 50km - will be replaced with real calculation
      const distanceCost = baseDistance * company.cost_per_km;
      const totalCost = company.base_delivery_cost + distanceCost;

      // Determine estimated delivery based on service types
      let estimatedDelivery = '2-3 business days';
      if (company.service_types.includes('Same Day')) {
        estimatedDelivery = 'Same day delivery';
      } else if (company.service_types.includes('Express')) {
        estimatedDelivery = '1-2 business days';
      }

      calculations.push({
        company_id: company.id,
        company_name: company.company_name,
        base_cost: company.base_delivery_cost,
        distance_cost: distanceCost,
        total_cost: totalCost,
        estimated_delivery: estimatedDelivery,
        notes: `Minimum order: ${company.minimum_order_value} DZD`,
        service_types: company.service_types,
        rating: company.average_rating
      });
    }

    console.log('✅ Shipping calculations completed:', calculations.length);
    return calculations;
  } catch (error) {
    console.error('❌ Exception in calculateShippingCost:', error);
    throw error;
  }
}

// Apply manual shipping to an order
export async function applyManualShipping(input: ManualShippingInput): Promise<boolean> {
  try {
    console.log('🚚 Applying manual shipping:', input);

    const { error } = await supabase
      .from('orders')
      .update({
        shipping_company_id: input.shipping_company_id,
        shipping_cost: input.custom_shipping_cost || 0,
        shipping_notes: input.shipping_notes || '',
        shipping_method: input.shipping_method || 'standard',
        updated_at: new Date().toISOString()
      })
      .eq('id', input.order_id);

    if (error) {
      console.error('❌ Error applying manual shipping:', error);
      throw error;
    }

    console.log('✅ Manual shipping applied successfully');
    return true;
  } catch (error) {
    console.error('❌ Exception in applyManualShipping:', error);
    throw error;
  }
}

// Get shipping options for checkout
export async function getShippingOptionsForCheckout(
  orderValue: number,
  deliveryWilaya: string
): Promise<ShippingCalculation[]> {
  try {
    console.log('🛒 Getting shipping options for checkout:', {
      orderValue,
      deliveryWilaya
    });
    
    const options = await calculateShippingCost(orderValue, deliveryWilaya);
    
    // Sort by total cost (cheapest first)
    options.sort((a, b) => a.total_cost - b.total_cost);
    
    console.log('✅ Checkout shipping options ready:', options.length);
    return options;
  } catch (error) {
    console.error('❌ Exception in getShippingOptionsForCheckout:', error);
    return [];
  }
}

// Get shipping company by ID
export async function getShippingCompanyById(companyId: string): Promise<ShippingCompany | null> {
  try {
    const { data, error } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('id', companyId)
      .single();

    if (error) {
      console.error('❌ Error fetching shipping company:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('❌ Exception in getShippingCompanyById:', error);
    return null;
  }
}
