/**
 * Product Service - Supabase Backend Integration
 *
 * This service replaces all localStorage operations with Supabase database calls
 * while maintaining identical API patterns for seamless frontend integration.
 */

import { createClient } from '@supabase/supabase-js';
import { TyreProduct, BrakeProduct, VehicleCompatibility, PricingTier } from '@/features/products/types/product.types';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Type definitions for database operations
export type AnyProduct = TyreProduct | BrakeProduct;

interface DatabaseProduct {
  id: string;
  user_id: string;
  name: string;
  sku: string;
  part_article_number?: string;
  category: string;
  subcategory?: string;
  description_and_specifications: string;
  primary_image?: string;
  additional_images: string[];
  manufacturer_logo?: string;
  manufacturer: string;
  supplier_name?: string;
  stock_quantity: number;
  retail_price?: number;
  minimum_order_quantity?: number;
  quotation_request_enabled: boolean;
  certifications: string[];
  production_capacity?: number;
  shipping_origin?: string;
  estimated_lead_time?: string;
  available_shipping_methods: string[];
  packaging_details?: string;
  inventory_update_date: string;
  status: string;
  admin_notes?: string;
  marketplace_section: 'wholesale' | 'retail';
  created_at: string;
  updated_at: string;
  images: string[];
  specifications: Record<string, string>;
  compatible_vehicles: string[];
}

interface DatabaseTyreSpec {
  product_id: string;
  width?: number;
  aspect_ratio?: number;
  rim_diameter?: number;
  load_index?: number;
  speed_rating?: string;
  season?: 'Summer' | 'Winter' | 'All-Season';
  tread_life?: string;
  traction_rating?: string;
  temperature_rating?: string;
}

interface DatabaseVehicleCompatibility {
  id: string;
  product_id: string;
  vehicle_id: string;
  vehicle_type: string;
  brand: string;
  model: string;
  generation?: string;
  engine_type?: string;
  display_name: string;
  engine_details: Record<string, any>;
}

interface DatabasePricingTier {
  id: string;
  product_id: string;
  min_quantity: number;
  max_quantity?: number;
  price: number;
}

/**
 * Transform database product to frontend product interface
 */
function transformDatabaseProduct(
  dbProduct: DatabaseProduct,
  tyreSpecs?: DatabaseTyreSpec,
  vehicleCompatibility: DatabaseVehicleCompatibility[] = [],
  pricingTiers: DatabasePricingTier[] = []
): AnyProduct {
  console.log('🔄 [PRODUCT_TRANSFORM] Transforming database product:', {
    id: dbProduct.id,
    name: dbProduct.name,
    supplier_name: dbProduct.supplier_name,
    shipping_origin: dbProduct.shipping_origin,
    user_id: dbProduct.user_id
  });
  const baseProduct = {
    id: dbProduct.id,
    name: dbProduct.name,
    sku: dbProduct.sku,
    partArticleNumber: dbProduct.part_article_number,
    category: dbProduct.category,
    subcategory: dbProduct.subcategory,
    descriptionAndSpecifications: dbProduct.description_and_specifications,
    primaryImage: dbProduct.primary_image,
    additionalImages: dbProduct.additional_images,
    manufacturerLogo: dbProduct.manufacturer_logo,
    manufacturer: dbProduct.manufacturer,
    supplierName: dbProduct.supplier_name,
    supplierAccountId: dbProduct.user_id, // CRITICAL: Map user_id to supplierAccountId
    shippingOrigin: dbProduct.shipping_origin, // CRITICAL: Map shipping_origin field
    stockQuantity: dbProduct.stock_quantity,
    retailPrice: dbProduct.retail_price,
    minimumOrderQuantity: dbProduct.minimum_order_quantity,
    quotationRequestEnabled: dbProduct.quotation_request_enabled,
    certifications: dbProduct.certifications,
    productionCapacity: dbProduct.production_capacity,
    estimatedLeadTime: dbProduct.estimated_lead_time,
    availableShippingMethods: dbProduct.available_shipping_methods,
    packagingDetails: dbProduct.packaging_details,
    inventoryUpdateDate: new Date(dbProduct.inventory_update_date),
    status: dbProduct.status as any,
    adminNotes: dbProduct.admin_notes,
    marketplaceSection: dbProduct.marketplace_section,
    createdAt: new Date(dbProduct.created_at),
    updatedAt: new Date(dbProduct.updated_at),
    images: dbProduct.images,
    specifications: dbProduct.specifications,
    compatibleVehicles: dbProduct.compatible_vehicles,
    wholesalePricingTiers: pricingTiers.map(tier => ({
      minQuantity: tier.min_quantity,
      maxQuantity: tier.max_quantity,
      price: tier.price
    })),
    vehicleTypeCompatibility: vehicleCompatibility.map(vc => ({
      id: vc.vehicle_id,
      type: vc.vehicle_type,
      brand: vc.brand,
      model: vc.model,
      generation: vc.generation,
      engineType: vc.engine_type,
      engineDetails: vc.engine_details,
      displayName: vc.display_name
    }))
  };

  let finalProduct: AnyProduct;

  if (dbProduct.category === 'tyres' && tyreSpecs) {
    finalProduct = {
      ...baseProduct,
      width: tyreSpecs.width,
      aspectRatio: tyreSpecs.aspect_ratio,
      rimDiameter: tyreSpecs.rim_diameter,
      loadIndex: tyreSpecs.load_index,
      speedRating: tyreSpecs.speed_rating,
      season: tyreSpecs.season,
      treadLife: tyreSpecs.tread_life,
      tractionRating: tyreSpecs.traction_rating,
      temperatureRating: tyreSpecs.temperature_rating
    } as TyreProduct;
  } else {
    finalProduct = baseProduct as BrakeProduct;
  }

  console.log('✅ [PRODUCT_TRANSFORM] Final transformed product:', {
    id: finalProduct.id,
    name: finalProduct.name,
    supplierName: finalProduct.supplierName,
    supplierAccountId: finalProduct.supplierAccountId,
    shippingOrigin: finalProduct.shippingOrigin
  });

  return finalProduct;
}

/**
 * Transform frontend product to database format
 */
function transformProductToDatabase(product: Partial<AnyProduct>, userId: string): {
  productData: Partial<DatabaseProduct>;
  tyreSpecs?: Partial<DatabaseTyreSpec>;
  vehicleCompatibility: VehicleCompatibility[];
  pricingTiers: PricingTier[];
} {
  const productData: Partial<DatabaseProduct> = {
    id: product.id,
    user_id: userId,
    name: product.name,
    sku: product.sku,
    part_article_number: product.partArticleNumber,
    category: product.category,
    subcategory: product.subcategory,
    description_and_specifications: product.descriptionAndSpecifications,
    primary_image: product.primaryImage,
    additional_images: product.additionalImages || [],
    manufacturer_logo: product.manufacturerLogo,
    manufacturer: product.manufacturer,
    supplier_name: product.supplierName,
    stock_quantity: product.stockQuantity || 0,
    retail_price: product.retailPrice,
    minimum_order_quantity: product.minimumOrderQuantity,
    quotation_request_enabled: product.quotationRequestEnabled || false,
    certifications: product.certifications || [],
    production_capacity: product.productionCapacity,
    shipping_origin: product.shippingOrigin,
    estimated_lead_time: product.estimatedLeadTime,
    available_shipping_methods: product.availableShippingMethods || [],
    packaging_details: product.packagingDetails,
    inventory_update_date: product.inventoryUpdateDate?.toISOString() || new Date().toISOString(),
    status: product.status || 'draft',
    admin_notes: product.adminNotes,
    marketplace_section: product.marketplaceSection || 'retail',
    images: product.images || [],
    specifications: product.specifications || {},
    compatible_vehicles: product.compatibleVehicles || []
  };

  let tyreSpecs: Partial<DatabaseTyreSpec> | undefined;
  if (product.category === 'tyres' && 'width' in product) {
    const tyreProduct = product as TyreProduct;
    tyreSpecs = {
      product_id: product.id!,
      width: tyreProduct.width,
      aspect_ratio: tyreProduct.aspectRatio,
      rim_diameter: tyreProduct.rimDiameter,
      load_index: tyreProduct.loadIndex,
      speed_rating: tyreProduct.speedRating,
      season: tyreProduct.season,
      tread_life: tyreProduct.treadLife,
      traction_rating: tyreProduct.tractionRating,
      temperature_rating: tyreProduct.temperatureRating
    };
  }

  return {
    productData,
    tyreSpecs,
    vehicleCompatibility: product.vehicleTypeCompatibility || [],
    pricingTiers: product.wholesalePricingTiers || []
  };
}

/**
 * Get current user ID from Supabase auth
 */
async function getCurrentUserId(): Promise<string> {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) {
    throw new Error('User not authenticated');
  }
  return user.id;
}

/**
 * Fetch products by category for the current user
 * Replaces localStorage.getItem(`products-${categoryId}`)
 */
export async function fetchProductsByCategory(categoryId: string): Promise<AnyProduct[]> {
  try {
    const userId = await getCurrentUserId();

    let query = supabase
      .from('products')
      .select('*')
      .eq('user_id', userId);

    // Handle unified "all-other-categories" table
    if (categoryId === 'all-other-categories') {
      // Import categories data dynamically to avoid circular dependencies
      const { getUnifiedTableCategories } = await import('@/data/categories');
      const categoriesToFetch = getUnifiedTableCategories();
      query = query.in('category', categoriesToFetch);
    } else {
      // Fetch products from specific category
      query = query.eq('category', categoryId);
    }

    const { data: products, error: productsError } = await query.order('created_at', { ascending: false });

    if (productsError) {
      console.error('Error fetching products:', productsError);
      throw productsError;
    }

    if (!products || products.length === 0) {
      return [];
    }

    const productIds = products.map(p => p.id);

    // Fetch related data in parallel
    const [tyreSpecsResult, vehicleCompatResult, pricingTiersResult] = await Promise.all([
      // Fetch tyre specifications
      supabase
        .from('tyre_specifications')
        .select('*')
        .in('product_id', productIds),

      // Fetch vehicle compatibility
      supabase
        .from('vehicle_compatibility')
        .select('*')
        .in('product_id', productIds),

      // Fetch pricing tiers
      supabase
        .from('wholesale_pricing_tiers')
        .select('*')
        .in('product_id', productIds)
        .order('min_quantity', { ascending: true })
    ]);

    const tyreSpecs = tyreSpecsResult.data || [];
    const vehicleCompat = vehicleCompatResult.data || [];
    const pricingTiers = pricingTiersResult.data || [];

    // Transform and combine data
    return products.map(product => {
      const productTyreSpecs = tyreSpecs.find(spec => spec.product_id === product.id);
      const productVehicleCompat = vehicleCompat.filter(vc => vc.product_id === product.id);
      const productPricingTiers = pricingTiers.filter(pt => pt.product_id === product.id);

      return transformDatabaseProduct(product, productTyreSpecs, productVehicleCompat, productPricingTiers);
    });

  } catch (error) {
    console.error('Error in fetchProductsByCategory:', error);
    throw error;
  }
}

/**
 * Fetch all products for the current user across all categories
 * Replaces the logic in useAllProducts hook
 */
export async function fetchAllProducts(): Promise<AnyProduct[]> {
  try {
    const userId = await getCurrentUserId();

    // Fetch all products for the user
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (productsError) {
      console.error('Error fetching all products:', productsError);
      throw productsError;
    }

    if (!products || products.length === 0) {
      return [];
    }

    const productIds = products.map(p => p.id);

    // Fetch related data in parallel
    const [tyreSpecsResult, vehicleCompatResult, pricingTiersResult] = await Promise.all([
      supabase.from('tyre_specifications').select('*').in('product_id', productIds),
      supabase.from('vehicle_compatibility').select('*').in('product_id', productIds),
      supabase.from('wholesale_pricing_tiers').select('*').in('product_id', productIds).order('min_quantity', { ascending: true })
    ]);

    const tyreSpecs = tyreSpecsResult.data || [];
    const vehicleCompat = vehicleCompatResult.data || [];
    const pricingTiers = pricingTiersResult.data || [];

    // Transform and combine data
    return products.map(product => {
      const productTyreSpecs = tyreSpecs.find(spec => spec.product_id === product.id);
      const productVehicleCompat = vehicleCompat.filter(vc => vc.product_id === product.id);
      const productPricingTiers = pricingTiers.filter(pt => pt.product_id === product.id);

      return transformDatabaseProduct(product, productTyreSpecs, productVehicleCompat, productPricingTiers);
    });

  } catch (error) {
    console.error('Error in fetchAllProducts:', error);
    throw error;
  }
}

/**
 * Fetch marketplace products by category (from ALL users for marketplace display)
 * This is different from fetchProductsByCategory which only fetches current user's products
 */
export async function fetchMarketplaceProductsByCategory(categoryId: string): Promise<AnyProduct[]> {
  try {
    let query = supabase
      .from('products')
      .select('*')
      .in('status', ['active', 'out_of_stock']);

    // Handle unified "all-other-categories" table for marketplace
    if (categoryId === 'all-other-categories') {
      const { getUnifiedTableCategories } = await import('@/data/categories');
      const categoriesToFetch = getUnifiedTableCategories();
      query = query.in('category', categoriesToFetch);
    } else {
      query = query.eq('category', categoryId);
    }

    const { data: products, error: productsError } = await query.order('created_at', { ascending: false });

    if (productsError) {
      console.error('Error fetching marketplace products:', productsError);
      throw productsError;
    }

    if (!products || products.length === 0) {
      return [];
    }

    const productIds = products.map(p => p.id);

    // Fetch related data in parallel
    const [tyreSpecsResult, vehicleCompatResult, pricingTiersResult] = await Promise.all([
      supabase.from('tyre_specifications').select('*').in('product_id', productIds),
      supabase.from('vehicle_compatibility').select('*').in('product_id', productIds),
      supabase.from('wholesale_pricing_tiers').select('*').in('product_id', productIds).order('min_quantity', { ascending: true })
    ]);

    const tyreSpecs = tyreSpecsResult.data || [];
    const vehicleCompat = vehicleCompatResult.data || [];
    const pricingTiers = pricingTiersResult.data || [];

    // Transform and combine data
    return products.map(product => {
      const productTyreSpecs = tyreSpecs.find(spec => spec.product_id === product.id);
      const productVehicleCompat = vehicleCompat.filter(vc => vc.product_id === product.id);
      const productPricingTiers = pricingTiers.filter(pt => pt.product_id === product.id);

      return transformDatabaseProduct(product, productTyreSpecs, productVehicleCompat, productPricingTiers);
    });

  } catch (error) {
    console.error('Error in fetchMarketplaceProductsByCategory:', error);
    throw error;
  }
}

/**
 * Create a new product
 * Replaces localStorage product creation logic
 */
export async function createProduct(product: Partial<AnyProduct>): Promise<AnyProduct> {
  try {
    const userId = await getCurrentUserId();

    if (!product.id) {
      throw new Error('Product ID is required');
    }

    const { productData, tyreSpecs, vehicleCompatibility, pricingTiers } = transformProductToDatabase(product, userId);

    // Start a transaction-like operation
    // 1. Insert main product
    const { data: insertedProduct, error: productError } = await supabase
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (productError) {
      console.error('Error creating product:', productError);
      throw productError;
    }

    // 2. Insert tyre specifications if applicable
    if (tyreSpecs && product.category === 'tyres') {
      const { error: tyreSpecError } = await supabase
        .from('tyre_specifications')
        .insert(tyreSpecs);

      if (tyreSpecError) {
        console.error('Error creating tyre specifications:', tyreSpecError);
        // Rollback: delete the product
        await supabase.from('products').delete().eq('id', product.id);
        throw tyreSpecError;
      }
    }

    // 3. Insert vehicle compatibility
    if (vehicleCompatibility.length > 0) {
      const vehicleCompatData = vehicleCompatibility.map(vc => ({
        product_id: product.id!,
        vehicle_id: vc.id,
        vehicle_type: vc.type,
        brand: vc.brand,
        model: vc.model,
        generation: vc.generation,
        engine_type: vc.engineType,
        display_name: vc.displayName,
        engine_details: vc.engineDetails || {}
      }));

      const { error: vehicleCompatError } = await supabase
        .from('vehicle_compatibility')
        .insert(vehicleCompatData);

      if (vehicleCompatError) {
        console.error('Error creating vehicle compatibility:', vehicleCompatError);
        // Rollback: delete the product and specs
        await supabase.from('products').delete().eq('id', product.id);
        throw vehicleCompatError;
      }
    }

    // 4. Insert pricing tiers
    if (pricingTiers.length > 0) {
      const pricingTierData = pricingTiers.map(pt => ({
        product_id: product.id!,
        min_quantity: pt.minQuantity,
        max_quantity: pt.maxQuantity,
        price: pt.price
      }));

      const { error: pricingTierError } = await supabase
        .from('wholesale_pricing_tiers')
        .insert(pricingTierData);

      if (pricingTierError) {
        console.error('Error creating pricing tiers:', pricingTierError);
        // Rollback: delete the product and related data
        await supabase.from('products').delete().eq('id', product.id);
        throw pricingTierError;
      }
    }

    // Fetch the complete product with all related data
    const [tyreSpecsResult, vehicleCompatResult, pricingTiersResult] = await Promise.all([
      supabase.from('tyre_specifications').select('*').eq('product_id', product.id!).maybeSingle(),
      supabase.from('vehicle_compatibility').select('*').eq('product_id', product.id!),
      supabase.from('wholesale_pricing_tiers').select('*').eq('product_id', product.id!).order('min_quantity', { ascending: true })
    ]);

    return transformDatabaseProduct(
      insertedProduct,
      tyreSpecsResult.data,
      vehicleCompatResult.data || [],
      pricingTiersResult.data || []
    );

  } catch (error) {
    console.error('Error in createProduct:', error);
    throw error;
  }
}

/**
 * Update an existing product
 * Replaces localStorage product update logic
 */
export async function updateProduct(productId: string, updates: Partial<AnyProduct>): Promise<AnyProduct> {
  try {
    const userId = await getCurrentUserId();

    const { productData, tyreSpecs, vehicleCompatibility, pricingTiers } = transformProductToDatabase(
      { ...updates, id: productId },
      userId
    );

    // 1. Update main product
    const { data: updatedProduct, error: productError } = await supabase
      .from('products')
      .update({
        ...productData,
        updated_at: new Date().toISOString()
      })
      .eq('id', productId)
      .eq('user_id', userId) // Ensure user can only update their own products
      .select()
      .single();

    if (productError) {
      console.error('Error updating product:', productError);
      throw productError;
    }

    // 2. Update tyre specifications if applicable
    if (tyreSpecs && updates.category === 'tyres') {
      const { error: tyreSpecError } = await supabase
        .from('tyre_specifications')
        .upsert({
          ...tyreSpecs,
          updated_at: new Date().toISOString()
        });

      if (tyreSpecError) {
        console.error('Error updating tyre specifications:', tyreSpecError);
        throw tyreSpecError;
      }
    }

    // 3. Update vehicle compatibility (replace all)
    if (vehicleCompatibility !== undefined) {
      // Delete existing compatibility
      await supabase
        .from('vehicle_compatibility')
        .delete()
        .eq('product_id', productId);

      // Insert new compatibility if any
      if (vehicleCompatibility.length > 0) {
        const vehicleCompatData = vehicleCompatibility.map(vc => ({
          product_id: productId,
          vehicle_id: vc.id,
          vehicle_type: vc.type,
          brand: vc.brand,
          model: vc.model,
          generation: vc.generation,
          engine_type: vc.engineType,
          display_name: vc.displayName,
          engine_details: vc.engineDetails || {}
        }));

        const { error: vehicleCompatError } = await supabase
          .from('vehicle_compatibility')
          .insert(vehicleCompatData);

        if (vehicleCompatError) {
          console.error('Error updating vehicle compatibility:', vehicleCompatError);
          throw vehicleCompatError;
        }
      }
    }

    // 4. Update pricing tiers (replace all)
    if (pricingTiers !== undefined) {
      // Delete existing pricing tiers
      await supabase
        .from('wholesale_pricing_tiers')
        .delete()
        .eq('product_id', productId);

      // Insert new pricing tiers if any
      if (pricingTiers.length > 0) {
        const pricingTierData = pricingTiers.map(pt => ({
          product_id: productId,
          min_quantity: pt.minQuantity,
          max_quantity: pt.maxQuantity,
          price: pt.price
        }));

        const { error: pricingTierError } = await supabase
          .from('wholesale_pricing_tiers')
          .insert(pricingTierData);

        if (pricingTierError) {
          console.error('Error updating pricing tiers:', pricingTierError);
          throw pricingTierError;
        }
      }
    }

    // Fetch the complete updated product with all related data
    const [tyreSpecsResult, vehicleCompatResult, pricingTiersResult] = await Promise.all([
      supabase.from('tyre_specifications').select('*').eq('product_id', productId).maybeSingle(),
      supabase.from('vehicle_compatibility').select('*').eq('product_id', productId),
      supabase.from('wholesale_pricing_tiers').select('*').eq('product_id', productId).order('min_quantity', { ascending: true })
    ]);

    return transformDatabaseProduct(
      updatedProduct,
      tyreSpecsResult.data,
      vehicleCompatResult.data || [],
      pricingTiersResult.data || []
    );

  } catch (error) {
    console.error('Error in updateProduct:', error);
    throw error;
  }
}

/**
 * Delete a product
 * Replaces localStorage product deletion logic
 */
export async function deleteProduct(productId: string): Promise<void> {
  try {
    const userId = await getCurrentUserId();

    // Delete the product (cascade will handle related data)
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', productId)
      .eq('user_id', userId); // Ensure user can only delete their own products

    if (error) {
      console.error('Error deleting product:', error);
      throw error;
    }

  } catch (error) {
    console.error('Error in deleteProduct:', error);
    throw error;
  }
}

/**
 * Delete multiple products
 * Replaces localStorage bulk deletion logic
 */
export async function deleteProducts(productIds: string[]): Promise<void> {
  try {
    const userId = await getCurrentUserId();

    // Delete the products (cascade will handle related data)
    const { error } = await supabase
      .from('products')
      .delete()
      .in('id', productIds)
      .eq('user_id', userId); // Ensure user can only delete their own products

    if (error) {
      console.error('Error deleting products:', error);
      throw error;
    }

  } catch (error) {
    console.error('Error in deleteProducts:', error);
    throw error;
  }
}

/**
 * Update product status
 * Replaces localStorage status update logic
 */
export async function updateProductStatus(productIds: string[], status: string): Promise<void> {
  try {
    const userId = await getCurrentUserId();

    const { error } = await supabase
      .from('products')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .in('id', productIds)
      .eq('user_id', userId); // Ensure user can only update their own products

    if (error) {
      console.error('Error updating product status:', error);
      throw error;
    }

  } catch (error) {
    console.error('Error in updateProductStatus:', error);
    throw error;
  }
}

/**
 * Interface for temporary image data during editing
 */
export interface TemporaryImage {
  id: string;
  file: File;
  dataUrl: string;
  type: 'primary' | 'additional';
}

/**
 * Convert File to base64 data URL for temporary storage
 */
export async function fileToDataUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        resolve(event.target.result as string);
      } else {
        reject(new Error('Failed to read image file'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read image file'));
    reader.readAsDataURL(file);
  });
}

/**
 * Upload product image to Supabase Storage
 */
export async function uploadProductImage(
  productId: string,
  file: File,
  imageType: 'primary' | 'additional' = 'primary'
): Promise<string> {
  try {
    console.log('🔄 Starting image upload process...', { productId, fileName: file.name, imageType });

    // Check authentication first
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      throw new Error('User not authenticated. Please log in and try again.');
    }

    const userId = user.id;
    console.log('✅ User authenticated:', { userId: userId.substring(0, 8) + '...' });

    // Validate file
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      throw new Error('File size must be less than 50MB');
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const fileName = `${userId}/${productId}/${imageType}_${Date.now()}.${fileExtension}`;
    console.log('📁 Generated file path:', fileName);

    // Upload to Supabase Storage
    console.log('⬆️ Uploading to Supabase Storage...');
    const { data, error } = await supabase.storage
      .from('product-images')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('❌ Supabase Storage upload error:', error);
      throw error;
    }

    console.log('✅ Upload successful:', data);

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('product-images')
      .getPublicUrl(fileName);

    console.log('🔗 Generated public URL:', publicUrl);
    return publicUrl;

  } catch (error) {
    console.error('❌ Error in uploadProductImage:', error);
    throw error;
  }
}

/**
 * Batch upload images during product save
 */
export async function batchUploadProductImages(
  productId: string,
  temporaryImages: TemporaryImage[]
): Promise<{ primaryImage?: string; additionalImages: string[] }> {
  try {
    console.log('🔄 Starting batch image upload...', { productId, imageCount: temporaryImages.length });

    const results = {
      primaryImage: undefined as string | undefined,
      additionalImages: [] as string[]
    };

    // Upload images in batches to avoid overwhelming the server
    const batchSize = 3;
    for (let i = 0; i < temporaryImages.length; i += batchSize) {
      const batch = temporaryImages.slice(i, i + batchSize);

      const batchResults = await Promise.allSettled(
        batch.map(tempImage => uploadProductImage(productId, tempImage.file, tempImage.type))
      );

      // Process batch results
      batchResults.forEach((result, index) => {
        const tempImage = batch[index];
        if (result.status === 'fulfilled') {
          if (tempImage.type === 'primary') {
            results.primaryImage = result.value;
          } else {
            results.additionalImages.push(result.value);
          }
        } else {
          console.error(`Failed to upload ${tempImage.file.name}:`, result.reason);
          throw new Error(`Failed to upload ${tempImage.file.name}: ${result.reason}`);
        }
      });
    }

    console.log('✅ Batch upload completed:', results);
    return results;

  } catch (error) {
    console.error('❌ Error in batch upload:', error);
    throw error;
  }
}

/**
 * Delete product image from Supabase Storage
 */
export async function deleteProductImage(imageUrl: string): Promise<void> {
  try {
    // Extract file path from URL
    const urlParts = imageUrl.split('/');
    const bucketIndex = urlParts.findIndex(part => part === 'product-images');

    if (bucketIndex === -1) {
      throw new Error('Invalid image URL');
    }

    const filePath = urlParts.slice(bucketIndex + 1).join('/');

    // Delete from Supabase Storage
    const { error } = await supabase.storage
      .from('product-images')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting image:', error);
      throw error;
    }

    console.log('✅ Successfully deleted image:', filePath);

  } catch (error) {
    console.error('Error in deleteProductImage:', error);
    throw error;
  }
}

/**
 * Replace product images with new ones, deleting old images first
 */
export async function replaceProductImages(
  productId: string,
  newImages: TemporaryImage[],
  oldImages: string[]
): Promise<{ primaryImage?: string; additionalImages: string[] }> {
  try {
    console.log('🔄 Starting image replacement...', {
      productId,
      newImageCount: newImages.length,
      oldImageCount: oldImages.length
    });

    // Step 1: Delete old images first
    if (oldImages.length > 0) {
      console.log('🗑️ Deleting old images:', oldImages);
      const deletePromises = oldImages
        .filter(imageUrl => imageUrl && imageUrl.trim() !== '')
        .map(async (imageUrl) => {
          try {
            await deleteProductImage(imageUrl);
          } catch (error) {
            console.error('❌ Failed to delete old image:', imageUrl, error);
            // Don't throw - continue with other deletions
          }
        });

      await Promise.allSettled(deletePromises);
      console.log('✅ Old images cleanup completed');
    }

    // Step 2: Upload new images
    if (newImages.length > 0) {
      console.log('⬆️ Uploading new images...');
      const uploadResults = await batchUploadProductImages(productId, newImages);
      console.log('✅ New images uploaded successfully');
      return uploadResults;
    }

    return { primaryImage: undefined, additionalImages: [] };

  } catch (error) {
    console.error('❌ Error in replaceProductImages:', error);
    throw error;
  }
}

/**
 * Upload manufacturer logo to Supabase Storage
 */
export async function uploadManufacturerLogo(
  manufacturerName: string,
  file: File
): Promise<string> {
  try {
    const userId = await getCurrentUserId();

    // Validate file
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit for logos
      throw new Error('File size must be less than 10MB');
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'png';
    const sanitizedName = manufacturerName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const fileName = `${userId}/logos/${sanitizedName}_${Date.now()}.${fileExtension}`;

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('manufacturer-logos')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Error uploading logo:', error);
      throw error;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('manufacturer-logos')
      .getPublicUrl(fileName);

    return publicUrl;

  } catch (error) {
    console.error('Error in uploadManufacturerLogo:', error);
    throw error;
  }
}

/**
 * Get product by ID using IDENTICAL logic as marketplace cards
 * This ensures ProductPage uses the exact same data source as marketplace
 */
export async function getProductByIdFromMarketplace(productId: string): Promise<AnyProduct | null> {
  console.log(`[MARKETPLACE_PRODUCT_FETCH] Starting fetch for product ID: ${productId}`);

  try {
    // Import categories data to determine category from product ID
    const { CATEGORIES } = await import('@/data/categories');

    // Determine category from product ID prefix
    let category = '';
    for (const cat of CATEGORIES) {
      if (productId.startsWith(`${cat.prefix}-`)) {
        category = cat.id;
        break;
      }
    }

    // Handle PROD prefix - search in all categories since PROD is used for all new categories
    if (!category && productId.startsWith('PROD-')) {
      console.log(`[MARKETPLACE_PRODUCT_FETCH] PROD prefix detected, searching all categories`);

      for (const cat of CATEGORIES) {
        const products = await fetchMarketplaceProductsByCategory(cat.id);
        const found = products.find(p => p.id === productId);
        if (found) {
          console.log(`[MARKETPLACE_PRODUCT_FETCH] Found PROD product in ${cat.id} category:`, found.name);
          return found;
        }
      }

      console.log(`[MARKETPLACE_PRODUCT_FETCH] PROD product ${productId} not found in any category`);
      return null;
    }

    if (!category) {
      // Try searching in all categories if ID format is unclear
      console.log(`[MARKETPLACE_PRODUCT_FETCH] Unknown product ID format, searching all categories`);

      for (const cat of CATEGORIES) {
        const products = await fetchMarketplaceProductsByCategory(cat.id);
        const found = products.find(p => p.id === productId);
        if (found) {
          console.log(`[MARKETPLACE_PRODUCT_FETCH] Found product in ${cat.id} category:`, found.name);
          return found;
        }
      }

      console.log(`[MARKETPLACE_PRODUCT_FETCH] Product ${productId} not found in any category`);
      return null;
    }

    console.log(`[MARKETPLACE_PRODUCT_FETCH] Fetching from ${category} category using marketplace logic`);

    // Use the EXACT same function that marketplace cards use
    const products = await fetchMarketplaceProductsByCategory(category);
    console.log(`[MARKETPLACE_PRODUCT_FETCH] Retrieved ${products.length} products from ${category} category`);

    // Find the specific product (same logic as marketplace cards)
    const product = products.find(p => p.id === productId);

    if (product) {
      console.log(`[MARKETPLACE_PRODUCT_FETCH] Successfully found product:`, {
        id: product.id,
        name: product.name,
        category: product.category,
        marketplaceSection: product.marketplaceSection,
        retailPrice: product.retailPrice,
        wholesalePricingTiers: product.wholesalePricingTiers?.length || 0
      });
      return product;
    } else {
      console.log(`[MARKETPLACE_PRODUCT_FETCH] Product ${productId} not found in ${category} category`);
      console.log(`[MARKETPLACE_PRODUCT_FETCH] Available product IDs:`, products.slice(0, 5).map(p => p.id));
      return null;
    }

  } catch (error) {
    console.error('[MARKETPLACE_PRODUCT_FETCH] Error in getProductByIdFromMarketplace:', error);
    return null;
  }
}

/**
 * Get product by ID for marketplace (public access) - DEPRECATED
 * Use getProductByIdFromMarketplace instead for consistency with marketplace cards
 */
export async function getProductById(productId: string): Promise<AnyProduct | null> {
  console.log(`[GET_PRODUCT_BY_ID_DEBUG] DEPRECATED: Redirecting to marketplace-consistent function`);
  return getProductByIdFromMarketplace(productId);
}

/**
 * Get product by ID for admin/owner access (restricted to user's products)
 * Used in admin panels where users should only see their own products
 */
export async function getProductByIdForOwner(productId: string): Promise<AnyProduct | null> {
  try {
    const userId = await getCurrentUserId();

    // Fetch main product data with user restriction
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('*')
      .eq('id', productId)
      .eq('user_id', userId)
      .single();

    if (productError) {
      if (productError.code === 'PGRST116') {
        return null; // Product not found
      }
      console.error('Error fetching product:', productError);
      throw productError;
    }

    // Fetch related data in parallel
    const [tyreSpecsResult, vehicleCompatResult, pricingTiersResult] = await Promise.all([
      supabase.from('tyre_specifications').select('*').eq('product_id', productId).maybeSingle(),
      supabase.from('vehicle_compatibility').select('*').eq('product_id', productId),
      supabase.from('wholesale_pricing_tiers').select('*').eq('product_id', productId).order('min_quantity', { ascending: true })
    ]);

    return transformDatabaseProduct(
      product,
      tyreSpecsResult.data,
      vehicleCompatResult.data || [],
      pricingTiersResult.data || []
    );

  } catch (error) {
    console.error('Error in getProductByIdForOwner:', error);
    throw error;
  }
}

/**
 * Fallback function to get product from centralized data source
 * Used when product is not found in Supabase database
 */
async function getProductFromCentralizedData(productId: string): Promise<AnyProduct | null> {
  console.log(`[CENTRALIZED_DEBUG] Searching for product ${productId} in centralized data...`);

  try {
    // Import centralized data using ES6 import for better browser compatibility
    const { getAllProducts } = await import('@/utils/centralizedProductData');

    // Get all products and search directly
    const allProducts = getAllProducts();
    console.log(`[CENTRALIZED_DEBUG] Total products in centralized data: ${allProducts.length}`);

    // Log first few product IDs for debugging
    const sampleIds = allProducts.slice(0, 5).map(p => p.id);
    console.log(`[CENTRALIZED_DEBUG] Sample product IDs:`, sampleIds);
    console.log(`[CENTRALIZED_DEBUG] Looking for product ID: "${productId}"`);

    const directMatch = allProducts.find(p => p.id === productId);
    if (directMatch) {
      console.log(`[CENTRALIZED_DEBUG] Found product ${productId} in centralized data:`, {
        id: directMatch.id,
        name: directMatch.name,
        category: directMatch.category,
        marketplaceSection: directMatch.marketplaceSection
      });
      return directMatch;
    }

    console.log(`[CENTRALIZED_DEBUG] Product ${productId} not found in centralized data`);
    return null;
  } catch (error) {
    console.error('[CENTRALIZED_DEBUG] Error accessing centralized data:', error);
    return null;
  }
}

// Export the supabase client as default for use in other services
export default supabase;
