/**
 * Category Image Service
 * Manages category and subcategory images in Supabase Storage
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

const supabase = supabaseUrl && supabaseAnonKey
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

export interface CategoryImageUpload {
  file: File;
  categoryId: string;
  type: 'category' | 'subcategory';
}

export interface CategoryImageResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload a category or subcategory image to Supabase Storage
 */
export const uploadCategoryImage = async ({
  file,
  categoryId,
  type
}: CategoryImageUpload): Promise<CategoryImageResult> => {
  if (!supabase) {
    return {
      success: false,
      error: 'Supabase client not initialized'
    };
  }

  try {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: 'Invalid file type. Only JPEG, PNG, WebP, and SVG files are allowed.'
      };
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        success: false,
        error: 'File size too large. Maximum size is 10MB.'
      };
    }

    // Generate file path
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'png';
    const filePath = `${type}/${categoryId}.${fileExtension}`;

    // Upload file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('category-images')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true // Replace existing file if it exists
      });

    if (error) {
      console.error('Error uploading category image:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('category-images')
      .getPublicUrl(filePath);

    return {
      success: true,
      url: urlData.publicUrl
    };

  } catch (error) {
    console.error('Error uploading category image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Delete a category or subcategory image from Supabase Storage
 */
export const deleteCategoryImage = async (
  categoryId: string,
  type: 'category' | 'subcategory'
): Promise<CategoryImageResult> => {
  if (!supabase) {
    return {
      success: false,
      error: 'Supabase client not initialized'
    };
  }

  try {
    // Try different file extensions
    const extensions = ['png', 'jpg', 'jpeg', 'webp', 'svg'];
    let deleted = false;

    for (const ext of extensions) {
      const filePath = `${type}/${categoryId}.${ext}`;

      const { error } = await supabase.storage
        .from('category-images')
        .remove([filePath]);

      if (!error) {
        deleted = true;
        break;
      }
    }

    return {
      success: deleted,
      error: deleted ? undefined : 'Image not found'
    };

  } catch (error) {
    console.error('Error deleting category image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Get the public URL for a category or subcategory image
 */
export const getCategoryImageUrl = (
  categoryId: string,
  type: 'category' | 'subcategory',
  extension: string = 'png'
): string => {
  if (!supabaseUrl) return '';

  const filePath = `${type}/${categoryId}.${extension}`;
  return `${supabaseUrl}/storage/v1/object/public/category-images/${filePath}`;
};

/**
 * Check if a category image exists
 */
export const checkCategoryImageExists = async (
  categoryId: string,
  type: 'category' | 'subcategory'
): Promise<boolean> => {
  if (!supabase) return false;

  try {
    const extensions = ['png', 'jpg', 'jpeg', 'webp', 'svg'];

    for (const ext of extensions) {
      const filePath = `${type}/${categoryId}.${ext}`;

      const { data, error } = await supabase.storage
        .from('category-images')
        .list(type, {
          search: `${categoryId}.${ext}`
        });

      if (!error && data && data.length > 0) {
        return true;
      }
    }

    return false;

  } catch (error) {
    console.error('Error checking category image existence:', error);
    return false;
  }
};

/**
 * List all category images
 */
export const listCategoryImages = async (type?: 'category' | 'subcategory') => {
  if (!supabase) return [];

  try {
    const { data, error } = await supabase.storage
      .from('category-images')
      .list(type || '', {
        limit: 1000,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (error) {
      console.error('Error listing category images:', error);
      return [];
    }

    return data || [];

  } catch (error) {
    console.error('Error listing category images:', error);
    return [];
  }
};

/**
 * Batch upload multiple category images
 */
export const batchUploadCategoryImages = async (
  uploads: CategoryImageUpload[]
): Promise<CategoryImageResult[]> => {
  const results: CategoryImageResult[] = [];

  for (const upload of uploads) {
    const result = await uploadCategoryImage(upload);
    results.push(result);
  }

  return results;
};

/**
 * Get fallback image URL for categories/subcategories without images
 */
export const getFallbackImageUrl = (type: 'category' | 'subcategory'): string => {
  // Return a simple SVG placeholder as data URL
  const size = type === 'category' ? 48 : 40;
  const iconSize = type === 'category' ? 24 : 20;
  const iconOffset = (size - iconSize) / 2;

  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" rx="${size/2}" fill="#F3F4F6"/>
      <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" x="${iconOffset}" y="${iconOffset}">
        <path d="M20 7L12 3L4 7V17H20V7Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9 21V12H15V21" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </svg>
  `)}`;
};
