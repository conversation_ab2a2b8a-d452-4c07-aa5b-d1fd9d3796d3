// This service handles all vehicle data operations using free data sources
// It combines data from the NHTSA API and our custom Algerian vehicle database

import { algerianVehicles } from './data/algerianVehicles';

// Base URL for the NHTSA vPIC API (completely free, no API key required)
const NHTSA_API_BASE_URL = 'https://vpic.nhtsa.dot.gov/api/vehicles';

/**
 * Fetch data from the NHTSA API
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} - API response
 */
const fetchFromNHTSA = async (endpoint, params = {}) => {
  try {
    // Build query string from params
    const queryParams = new URLSearchParams({
      format: 'json',
      ...params
    }).toString();

    const url = `${NHTSA_API_BASE_URL}${endpoint}?${queryParams}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`NHTSA API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching from NHTSA API:', error);
    return { Results: [] };
  }
};

/**
 * Get product counts for a vehicle (simulated)
 * In a real implementation, this would fetch from your product database
 * @param {Object} vehicle - Vehicle object
 * @returns {Object} - Product counts
 */
const getProductCounts = (vehicle) => {
  // Generate random but consistent counts based on vehicle ID
  const seed = vehicle.id || (vehicle.Make + vehicle.Model).length;
  const myCount = Math.max(5, Math.floor((seed * 7) % 30));
  const allCount = Math.max(myCount, Math.floor((seed * 13) % 50));

  return {
    myProductCount: myCount,
    allProductCount: allCount
  };
};

/**
 * Normalize vehicle data from different sources into a consistent format
 * @param {Object} vehicle - Vehicle data from any source
 * @param {string} source - Source of the data ('nhtsa' or 'algerian')
 * @returns {Object} - Normalized vehicle object
 */
const normalizeVehicleData = (vehicle, source) => {
  if (source === 'nhtsa') {
    return {
      id: vehicle.MakeId + '-' + vehicle.ModelId,
      type: 'car', // NHTSA primarily has cars
      brand: vehicle.Make_Name || vehicle.Make,
      model: vehicle.Model_Name || vehicle.Model,
      engineType: vehicle.EngineConfiguration || 'Standard',
      yearFrom: vehicle.ModelYear || 2015,
      yearTo: vehicle.ModelYear ? parseInt(vehicle.ModelYear) + 5 : 2023,
      ...getProductCounts(vehicle)
    };
  } else if (source === 'algerian') {
    return {
      id: vehicle.id || `alg-${vehicle.brand}-${vehicle.model}`.replace(/\s+/g, '-').toLowerCase(),
      type: vehicle.type || 'car',
      brand: vehicle.brand,
      model: vehicle.model,
      engineType: vehicle.engineType,
      yearFrom: vehicle.yearFrom || 2015,
      yearTo: vehicle.yearTo || 2023,
      ...getProductCounts(vehicle)
    };
  }

  return vehicle;
};

/**
 * Fetch vehicles with optional filters
 * @param {Object} filters - Optional filters to apply
 * @returns {Promise<Array>} - Array of vehicle objects
 */
export const fetchVehicles = async (filters = {}) => {
  try {
    // Start with Algerian vehicles (our custom database)
    let vehicles = [...algerianVehicles].map(v => normalizeVehicleData(v, 'algerian'));

    // If we need more data or specific international brands, fetch from NHTSA
    if (!filters.brand || !vehicles.some(v => v.brand === filters.brand)) {
      let nhtsaVehicles = [];

      if (filters.brand) {
        // Fetch specific make/brand
        const makeData = await fetchFromNHTSA('/GetModelsForMake/' + filters.brand);
        nhtsaVehicles = makeData.Results || [];
      } else {
        // Fetch popular makes/brands
        const popularMakes = ['Toyota', 'Volkswagen', 'Ford', 'Honda', 'Hyundai', 'Renault', 'Peugeot'];

        for (const make of popularMakes) {
          const makeData = await fetchFromNHTSA('/GetModelsForMake/' + make);
          nhtsaVehicles = [...nhtsaVehicles, ...(makeData.Results || [])];
        }
      }

      // Normalize NHTSA data
      const normalizedNHTSAVehicles = nhtsaVehicles.map(v => normalizeVehicleData(v, 'nhtsa'));

      // Combine with Algerian vehicles, avoiding duplicates
      const existingIds = new Set(vehicles.map(v => v.id));
      for (const vehicle of normalizedNHTSAVehicles) {
        if (!existingIds.has(vehicle.id)) {
          vehicles.push(vehicle);
          existingIds.add(vehicle.id);
        }
      }
    }

    // Apply filters
    if (filters.type) {
      vehicles = vehicles.filter(v => v.type === filters.type);
    }

    if (filters.brand) {
      vehicles = vehicles.filter(v => v.brand === filters.brand);
    }

    if (filters.model) {
      vehicles = vehicles.filter(v => v.model === filters.model);
    }

    if (filters.engineType) {
      vehicles = vehicles.filter(v => v.engineType === filters.engineType);
    }

    return vehicles;
  } catch (error) {
    console.error('Error fetching vehicles:', error);

    // Fallback to Algerian vehicles only if there's an error
    return algerianVehicles.map(v => normalizeVehicleData(v, 'algerian'));
  }
};

/**
 * Fetch market demand data for a specific vehicle
 * @param {string|number} vehicleId - The ID of the vehicle
 * @returns {Promise<Array>} - Array of market demand data
 */
export const fetchMarketDemand = async (vehicleId) => {
  try {
    // In a real implementation, this would fetch from your analytics database
    // For now, we'll generate simulated data based on the vehicle ID

    // Get the vehicle to customize the demand data
    const vehicles = await fetchVehicles();
    const vehicle = vehicles.find(v => v.id === vehicleId);

    if (!vehicle) {
      return [];
    }

    // Generate seed from vehicle properties for consistent random data
    const seed = typeof vehicleId === 'string'
      ? vehicleId.length
      : vehicleId;

    // Common part categories by vehicle type
    const categories = {
      car: ['Brake Pads', 'Oil Filters', 'Air Filters', 'Spark Plugs', 'Wiper Blades', 'Headlights', 'Batteries'],
      motorcycle: ['Chain Kits', 'Brake Pads', 'Oil Filters', 'Spark Plugs', 'Tires', 'Batteries', 'Exhaust Systems'],
      truck: ['Brake Pads', 'Air Filters', 'Oil Filters', 'Fuel Filters', 'Batteries', 'Alternators', 'Suspension Parts']
    };

    // Select categories based on vehicle type
    const vehicleCategories = categories[vehicle.type] || categories.car;

    // Generate demand data
    const demandData = vehicleCategories.map((category, index) => {
      const baseSeed = (seed + index) * (index + 1);
      const volume = 200 + Math.floor(baseSeed % 1500);
      const isTrendUp = (baseSeed % 2) === 0;
      const trendPercentage = 2 + Math.floor(baseSeed % 20);

      return {
        category,
        volume,
        trend: isTrendUp ? 'up' : 'down',
        trendPercentage
      };
    });

    // Sort by volume (highest first)
    return demandData.sort((a, b) => b.volume - a.volume);
  } catch (error) {
    console.error('Error fetching market demand:', error);
    return [];
  }
};

/**
 * Fetch unique brands for a specific vehicle type
 * @param {string} vehicleType - The type of vehicle (car, motorcycle, truck)
 * @returns {Promise<Array>} - Array of brand names
 */
export const fetchBrands = async (vehicleType) => {
  const vehicles = await fetchVehicles({ type: vehicleType });
  const brands = [...new Set(vehicles.map(v => v.brand))].sort();
  return brands;
};

/**
 * Fetch unique models for a specific brand and vehicle type
 * @param {string} brand - The brand name
 * @param {string} vehicleType - The type of vehicle
 * @returns {Promise<Array>} - Array of model names
 */
export const fetchModels = async (brand, vehicleType) => {
  const vehicles = await fetchVehicles({
    type: vehicleType,
    brand: brand
  });
  const models = [...new Set(vehicles.map(v => v.model))].sort();
  return models;
};

/**
 * Fetch unique engine types for a specific model, brand, and vehicle type
 * @param {string} model - The model name
 * @param {string} brand - The brand name
 * @param {string} vehicleType - The type of vehicle
 * @returns {Promise<Array>} - Array of engine types
 */
export const fetchEngineTypes = async (model, brand, vehicleType) => {
  const vehicles = await fetchVehicles({
    type: vehicleType,
    brand: brand,
    model: model
  });
  const engineTypes = [...new Set(vehicles.map(v => v.engineType))].sort();
  return engineTypes;
};
