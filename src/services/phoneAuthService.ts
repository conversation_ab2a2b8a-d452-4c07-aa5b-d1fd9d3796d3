/**
 * Phone Authentication Service
 * 
 * Production-ready phone-only authentication for Algerian consumers
 * Integrates Dexatel SMS OTP with Supabase authentication
 */

import { supabase } from '@/services/authService';
import { sendOTP, verifyOTP, validateAlgerianPhone } from './dexatelService';
import { User } from '@supabase/supabase-js';

// Types
export interface PhoneAuthStep1Response {
  success: boolean;
  verificationId?: string;
  error?: string;
  rateLimitInfo?: {
    remainingAttempts: number;
    resetTime?: string;
  };
}

export interface PhoneAuthStep2Response {
  success: boolean;
  user?: User;
  profile?: any;
  error?: string;
  action?: 'login' | 'signup';
}

export interface PhoneAuthSession {
  verificationId: string;
  phone: string;
  expiresAt: string;
  step: 1 | 2;
}

// Session storage for phone auth flow using localStorage for persistence
class PhoneAuthSessionManager {
  private static readonly STORAGE_KEY = 'phone_auth_sessions';

  static set(sessionId: string, session: PhoneAuthSession): void {
    try {
      const sessions = this.getAll();
      sessions[sessionId] = session;
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Error storing phone auth session:', error);
    }
  }

  static get(sessionId: string): PhoneAuthSession | null {
    try {
      const sessions = this.getAll();
      const session = sessions[sessionId];

      if (!session) return null;

      // Check if expired
      if (new Date() > new Date(session.expiresAt)) {
        this.delete(sessionId);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error retrieving phone auth session:', error);
      return null;
    }
  }

  static delete(sessionId: string): void {
    try {
      const sessions = this.getAll();
      delete sessions[sessionId];
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error('Error deleting phone auth session:', error);
    }
  }

  static getAll(): Record<string, PhoneAuthSession> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error parsing phone auth sessions:', error);
      return {};
    }
  }

  static cleanup(): void {
    try {
      const sessions = this.getAll();
      const now = new Date();
      let hasExpired = false;

      for (const [sessionId, session] of Object.entries(sessions)) {
        if (now > new Date(session.expiresAt)) {
          delete sessions[sessionId];
          hasExpired = true;
        }
      }

      if (hasExpired) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions));
      }
    } catch (error) {
      console.error('Error cleaning up phone auth sessions:', error);
    }
  }
}

/**
 * Step 1: Send OTP to phone number
 */
export async function sendPhoneOTP(phone: string): Promise<PhoneAuthStep1Response> {
  try {
    console.log('🔐 Starting phone authentication for:', phone);
    
    // Validate phone number format
    const validation = validateAlgerianPhone(phone);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error || 'Invalid phone number format'
      };
    }
    
    const formattedPhone = validation.formatted;
    console.log('📱 Formatted phone:', formattedPhone);
    
    // Send OTP via Dexatel
    const otpResult = await sendOTP(formattedPhone);
    
    if (!otpResult.success) {
      return {
        success: false,
        error: otpResult.error,
        rateLimitInfo: otpResult.rateLimitInfo
      };
    }
    
    // Store session
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    PhoneAuthSessionManager.set(sessionId, {
      verificationId: otpResult.data!.id,
      phone: formattedPhone,
      expiresAt: expiresAt.toISOString(),
      step: 1
    });
    
    console.log('✅ OTP sent successfully, session created:', sessionId);
    
    return {
      success: true,
      verificationId: sessionId, // Return session ID, not verification ID for security
      rateLimitInfo: otpResult.rateLimitInfo
    };
    
  } catch (error) {
    console.error('❌ Error in sendPhoneOTP:', error);
    return {
      success: false,
      error: 'Failed to send verification code. Please try again.'
    };
  }
}

/**
 * Step 2: Verify OTP and authenticate user
 */
export async function verifyPhoneOTP(sessionId: string, code: string): Promise<PhoneAuthStep2Response> {
  try {
    console.log('🔐 Verifying OTP for session:', sessionId);
    
    // Get session
    console.log('🔍 Looking for session:', sessionId);
    console.log('🔍 All stored sessions:', PhoneAuthSessionManager.getAll());

    const session = PhoneAuthSessionManager.get(sessionId);
    console.log('🔍 Retrieved session:', session);

    if (!session) {
      console.error('❌ Session not found or expired:', sessionId);
      return {
        success: false,
        error: 'Invalid or expired verification session'
      };
    }
    
    // Verify OTP with our local verification system
    console.log('🔐 Verifying OTP with verification ID:', session.verificationId);
    const verifyResult = await verifyOTP(session.verificationId, code);

    if (!verifyResult.success) {
      console.error('❌ OTP verification failed:', verifyResult.error);
      return {
        success: false,
        error: verifyResult.error
      };
    }

    console.log('✅ OTP verified successfully with Dexatel');

    // Handle user authentication in Supabase
    console.log('🔐 Proceeding with Supabase authentication for phone:', session.phone);
    const authResult = await handlePhoneAuthentication(session.phone, session.verificationId);

    if (authResult.success) {
      console.log('✅ Phone authentication completed successfully');
      // Clean up session only on success
      PhoneAuthSessionManager.delete(sessionId);
    } else {
      console.error('❌ Phone authentication failed:', authResult.error);
    }

    return authResult;
    
  } catch (error) {
    console.error('❌ Error in verifyPhoneOTP:', error);
    return {
      success: false,
      error: 'Failed to verify code. Please try again.'
    };
  }
}

/**
 * Handle user authentication after successful OTP verification
 */
async function handlePhoneAuthentication(phone: string, verificationId: string): Promise<PhoneAuthStep2Response> {
  try {
    // Call the database function to handle verification
    const { data: result, error: dbError } = await supabase
      .rpc('handle_phone_verification', {
        p_phone: phone,
        p_verification_id: verificationId
      });
    
    if (dbError) {
      console.error('Database error in phone verification:', dbError);
      return {
        success: false,
        error: 'Authentication failed. Please try again.'
      };
    }
    
    const userId = result.user_id;
    const action = result.action; // 'login' or 'signup'

    console.log(`📱 Phone authentication ${action} successful for user:`, userId);
    console.log('📱 Full database result:', result);

    // Validate userId
    if (!userId) {
      console.error('❌ CRITICAL: No user ID returned from database function');
      console.error('❌ Database result:', result);
      return {
        success: false,
        error: 'Authentication failed. Please try again.'
      };
    }
    
    // Create session manually since we've already verified the phone
    console.log('🔐 Creating manual session for verified phone user');
    const sessionResult = await createPhoneSession(userId, phone);

    if (!sessionResult.success) {
      console.error('❌ CRITICAL: Failed to create phone session');
      console.error('❌ Session result:', sessionResult);
      console.error('❌ User ID:', userId);
      console.error('❌ Phone:', phone);
      return {
        success: false,
        error: 'Failed to create authentication session'
      };
    }

    console.log('✅ Phone session created successfully');
    
    return {
      success: true,
      user: sessionResult.user,
      profile: sessionResult.profile,
      action: action as 'login' | 'signup'
    };
    
  } catch (error) {
    console.error('Error in handlePhoneAuthentication:', error);
    return {
      success: false,
      error: 'Authentication failed. Please try again.'
    };
  }
}

/**
 * Create phone session manually (stores in localStorage for AuthContext)
 */
async function createPhoneSession(userId: string, phone: string): Promise<{ success: boolean; user?: any; profile?: any }> {
  try {
    console.log('🔐 Creating phone session for user:', userId, 'phone:', phone);

    // Get user data (with multiple retries for new users)
    let profile = null;
    let profileError = null;
    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts && !profile) {
      attempts++;
      console.log(`🔍 Attempt ${attempts}/${maxAttempts} to fetch profile for user:`, userId);

      // Try to get profile by ID first, then by phone as fallback
      let { data: profileData, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .limit(1);

      // If no profile found by ID, try by phone (for duplicate cleanup)
      if (!profileData || profileData.length === 0) {
        console.log('🔍 No profile found by ID, trying by phone:', phone);
        const { data: phoneProfileData, error: phoneError } = await supabase
          .from('profiles')
          .select('*')
          .eq('phone', phone)
          .eq('role', 'consumer')
          .order('created_at', { ascending: false })
          .limit(1);

        if (phoneProfileData && phoneProfileData.length > 0) {
          profileData = phoneProfileData;
          fetchError = null;
          console.log('✅ Found profile by phone:', profileData[0].id);
        } else {
          fetchError = phoneError;
        }
      }

      if (profileData && profileData.length > 0) {
        profile = profileData[0]; // Take the first profile
        console.log('✅ Profile found:', profile);

        // If multiple profiles found, log warning
        if (profileData.length > 1) {
          console.warn('⚠️ Multiple profiles found for user:', userId, 'count:', profileData.length);
        }
        break;
      } else if (fetchError) {
        console.log('❌ Profile fetch error:', fetchError);
        profileError = fetchError;

        if (attempts < maxAttempts) {
          console.log('⏳ Waiting 1 second before retry...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    if (!profile) {
      console.error('❌ Failed to fetch profile after', maxAttempts, 'attempts. Last error:', profileError);
      return { success: false };
    }

    // Create a user object compatible with Supabase User type
    const user = {
      id: userId,
      phone: phone,
      email: profile.email || '',
      app_metadata: { provider: 'phone' },
      user_metadata: {
        phone: phone,
        role: profile.role,
        phone_verified: true
      },
      aud: 'authenticated',
      created_at: profile.created_at,
      phone_confirmed_at: profile.phone_verified_at || new Date().toISOString()
    };

    // Store phone authentication session in localStorage
    const phoneSession = {
      user,
      profile,
      authenticated_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year
    };

    localStorage.setItem('phone_auth_session', JSON.stringify(phoneSession));
    console.log('✅ Phone authentication session stored in localStorage');
    console.log('✅ Returning successful session with user:', user.id, 'and profile:', profile.id);

    return {
      success: true,
      user,
      profile
    };

  } catch (error) {
    console.error('❌ CRITICAL ERROR in createPhoneSession:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      userId,
      phone
    });
    return { success: false };
  }
}

/**
 * Check if phone number is already registered
 */
export async function checkPhoneExists(phone: string): Promise<{ exists: boolean; error?: string }> {
  try {
    const validation = validateAlgerianPhone(phone);
    if (!validation.isValid) {
      return { exists: false, error: validation.error };
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('id, phone_verified')
      .eq('phone', validation.formatted)
      .eq('role', 'consumer')
      .eq('phone_verified', true)
      .limit(1);

    if (error) {
      console.error('Error checking phone existence:', error);
      return { exists: false, error: 'Failed to check phone number' };
    }

    return { exists: data && data.length > 0 };

  } catch (error) {
    console.error('Error in checkPhoneExists:', error);
    return { exists: false, error: 'Failed to check phone number' };
  }
}

/**
 * PRODUCTION FEATURE: Login existing consumer with phone number only (no OTP)
 * This saves SMS costs and provides better UX for returning customers
 */
export async function loginExistingConsumer(phone: string, fullName?: string): Promise<PhoneAuthStep2Response> {
  try {
    console.log('🔐 Attempting phone-only login for existing consumer:', phone);

    // Validate phone number format
    const validation = validateAlgerianPhone(phone);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error || 'Invalid phone number format'
      };
    }

    const formattedPhone = validation.formatted;

    // Check if consumer exists
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, full_name, display_name, phone_verified')
      .eq('phone', formattedPhone)
      .eq('role', 'consumer')
      .limit(1);

    if (profileError) {
      console.error('Error checking consumer profile:', profileError);
      return {
        success: false,
        error: 'Failed to verify consumer account'
      };
    }

    if (!profiles || profiles.length === 0) {
      console.log('❌ Consumer not found for phone:', formattedPhone);
      return {
        success: false,
        error: 'CONSUMER_NOT_FOUND' // Special error code for new users
      };
    }

    const profile = profiles[0];
    console.log('✅ Found existing consumer:', profile.id);

    // Update last login and phone verification status
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        last_login: new Date().toISOString(),
        phone_verified: true,
        phone_verified_at: new Date().toISOString(),
        // Update name if provided and different
        ...(fullName && fullName.trim() && fullName !== profile.full_name ? {
          full_name: fullName.trim(),
          display_name: convertToPrivacyProtectedName(fullName.trim()).displayName
        } : {})
      })
      .eq('id', profile.id);

    if (updateError) {
      console.error('Error updating consumer login:', updateError);
      // Continue anyway - login is more important than update
    }

    // Create phone session
    const sessionResult = await createPhoneSession(profile.id, formattedPhone);

    if (!sessionResult.success) {
      console.error('❌ Failed to create phone session for existing consumer');
      return {
        success: false,
        error: 'Failed to create authentication session'
      };
    }

    console.log('✅ Phone-only login successful for existing consumer');

    return {
      success: true,
      user: sessionResult.user,
      profile: sessionResult.profile,
      action: 'login'
    };

  } catch (error) {
    console.error('Error in loginExistingConsumer:', error);
    return {
      success: false,
      error: 'Login failed. Please try again.'
    };
  }
}

/**
 * Convert full name to privacy-protected display name
 * Example: "Ahmed Ben Ali" -> "Ahmed B"
 */
function convertToPrivacyProtectedName(fullName: string): { displayName: string; fullName: string } {
  const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);

  if (nameParts.length === 0) {
    return { displayName: 'Consumer', fullName: fullName };
  }

  if (nameParts.length === 1) {
    return { displayName: nameParts[0], fullName: fullName };
  }

  // First name + last initial
  const firstName = nameParts[0];
  const lastInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();

  return {
    displayName: `${firstName} ${lastInitial}`,
    fullName: fullName
  };
}

/**
 * Get current phone auth session info
 */
export function getPhoneAuthSession(sessionId: string): PhoneAuthSession | null {
  return PhoneAuthSessionManager.get(sessionId);
}

/**
 * Get stored phone authentication session from localStorage
 */
export function getStoredPhoneSession(): { user: any; profile: any } | null {
  try {
    const stored = localStorage.getItem('phone_auth_session');
    if (!stored) return null;

    const session = JSON.parse(stored);

    // Check if session is expired
    if (new Date(session.expires_at) < new Date()) {
      localStorage.removeItem('phone_auth_session');
      return null;
    }

    return {
      user: session.user,
      profile: session.profile
    };
  } catch (error) {
    console.error('Error retrieving phone session:', error);
    localStorage.removeItem('phone_auth_session');
    return null;
  }
}

/**
 * Clear stored phone authentication session
 */
export function clearStoredPhoneSession(): void {
  localStorage.removeItem('phone_auth_session');
}

/**
 * Clean up expired sessions
 */
export function cleanupExpiredSessions(): void {
  PhoneAuthSessionManager.cleanup();
}

// Clean up expired sessions every 5 minutes
setInterval(cleanupExpiredSessions, 5 * 60 * 1000);
