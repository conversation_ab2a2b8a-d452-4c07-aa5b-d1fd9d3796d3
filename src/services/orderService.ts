/**
 * Order Service
 * 
 * Handles order creation, management, and tracking for AROUZ MARKET
 * Integrates with the comprehensive orders & shipments system
 */

import { supabase } from '@/services/authService';
import { CartItem } from '@/contexts/CartContext';
import { CheckoutData, PaymentMethod } from '@/pages/BasketPage';
import { calculateCheckoutFees } from './arouzFeeService';

// Types
export interface OrderItem {
  product_id: string;
  product_name: string;
  product_image?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  supplier_account_id?: string;
  supplier_name: string;
  supplier_phone?: string; // CRITICAL: Supplier contact phone for shipping companies
  supplier_city?: string;
  supplier_wilaya?: string;
  marketplace_section: 'wholesale' | 'retail';
  category?: string;
  subcategory?: string;
}

export interface CreateOrderRequest {
  consumer_phone: string;
  consumer_name: string | null;
  payment_method: PaymentMethod;
  delivery_address: string;
  delivery_coordinates: { lat: number; lng: number } | null;
  delivery_wilaya: string;
  google_maps_url: string;
  special_instructions: string | null;
  order_items: OrderItem[];
  // NEW: Shipping information
  shipping_company_id?: string;
  shipping_cost?: number;
  shipping_notes?: string;
  shipping_method?: string;
  // NEW: AROUZ fee information
  total_arouz_fees?: number;
  arouz_commission?: number;
  arouz_service_fee?: number;
  arouz_processing_fee?: number;
}

export interface Order {
  id: string;
  order_number: string;
  consumer_phone: string;
  consumer_name?: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  payment_method: PaymentMethod;
  delivery_address: string;
  delivery_coordinates?: { lat: number; lng: number };
  delivery_wilaya: string;
  google_maps_url: string;
  subtotal: number;
  total_shipping_cost: number;
  total_arouz_fees: number;
  total_amount: number;
  special_instructions?: string;
  estimated_delivery_date?: string;
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  delivered_at?: string;
}

export interface OrderWithItems extends Order {
  order_items: OrderItem[];
}

/**
 * Get current consumer information from authentication
 */
async function getCurrentConsumerInfo(): Promise<{ phone: string | null; name: string | null; profile: any | null }> {
  try {
    console.log('🔍 [CONSUMER_INFO] Getting current consumer info...');

    // First try to get from Supabase auth
    console.log('🔍 [CONSUMER_INFO] Checking Supabase auth...');
    const { data: { user } } = await supabase.auth.getUser();
    console.log('🔍 [CONSUMER_INFO] Supabase user:', {
      id: user?.id,
      phone: user?.phone,
      email: user?.email,
      role: user?.user_metadata?.role
    });

    if (user?.phone) {
      console.log('🔍 [CONSUMER_INFO] User has phone, getting profile...');
      // Get profile information for the user
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      console.log('🔍 [CONSUMER_INFO] Supabase auth profile:', profile);
      return {
        phone: user.phone,
        name: profile?.full_name || profile?.first_name || null,
        profile
      };
    }

    // Fallback to phone auth session
    console.log('🔍 [CONSUMER_INFO] No Supabase phone, checking phone auth session...');
    const { getStoredPhoneSession } = await import('./phoneAuthService');
    const phoneSession = getStoredPhoneSession();
    console.log('🔍 [CONSUMER_INFO] Phone session:', {
      hasSession: !!phoneSession,
      userPhone: phoneSession?.user?.phone,
      profilePhone: phoneSession?.profile?.phone,
      profileName: phoneSession?.profile?.full_name
    });

    if (phoneSession?.user?.phone) {
      console.log('🔍 [CONSUMER_INFO] Phone session found, getting profile from database...');
      // Get profile information for phone auth user
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('phone', phoneSession.user.phone)
        .eq('role', 'consumer')
        .single();

      console.log('🔍 [CONSUMER_INFO] Phone auth profile query result:', { profile, profileError });

      return {
        phone: phoneSession.user.phone,
        name: profile?.full_name || profile?.first_name || null,
        profile
      };
    }

    console.log('❌ [CONSUMER_INFO] No consumer info found');
    return { phone: null, name: null, profile: null };
  } catch (error) {
    console.error('Error getting consumer info:', error);
    return { phone: null, name: null, profile: null };
  }
}

/**
 * Get current consumer phone from authentication (backward compatibility)
 */
async function getCurrentConsumerPhone(): Promise<string | null> {
  const { phone } = await getCurrentConsumerInfo();
  return phone;
}

/**
 * Transform cart items to order items format
 */
function transformCartItemsToOrderItems(cartItems: CartItem[]): OrderItem[] {
  return cartItems.map(item => {
    const orderItem: OrderItem = {
      product_id: item.originalProductId || item.id.toString(),
      product_name: item.name,
      product_image: item.image || null,
      quantity: item.quantity,
      unit_price: item.price,
      total_price: item.price * item.quantity,
      supplier_account_id: item.supplierAccountId || '', // Use supplier account ID from cart
      supplier_name: item.supplierName || 'Unknown Supplier',
      supplier_phone: '', // Will be populated by database function from profiles table
      supplier_city: '', // Empty string instead of undefined
      supplier_wilaya: item.shippingOrigin || '', // Use shipping origin from cart item
      marketplace_section: (item.marketplaceSection || 'retail') as 'wholesale' | 'retail',
      category: item.category || '',
      subcategory: item.subcategory || ''
    };

    console.log('🔄 Transformed cart item to order item:', {
      product_name: orderItem.product_name,
      supplier_name: orderItem.supplier_name,
      supplier_wilaya: orderItem.supplier_wilaya,
      supplier_account_id: orderItem.supplier_account_id
    });
    return orderItem;
  });
}

/**
 * Create a new order with items
 */
export async function createOrder(
  checkoutData: CheckoutData,
  cartItems: CartItem[]
): Promise<{ success: boolean; order?: OrderWithItems; error?: string }> {
  try {
    console.log('🚀 [ORDER_SERVICE] Starting order creation...');
    console.log('🚀 [ORDER_SERVICE] Checkout data:', checkoutData);
    console.log('🚀 [ORDER_SERVICE] Cart items count:', cartItems.length);

    // Get complete consumer information including name
    console.log('🔍 [ORDER_SERVICE] Getting consumer info...');
    const { phone: consumerPhone, name: consumerName, profile: consumerProfile } = await getCurrentConsumerInfo();

    console.log('🔍 [ORDER_SERVICE] Consumer info result:', {
      phone: consumerPhone,
      name: consumerName,
      profile: consumerProfile ? 'Found' : 'Not found'
    });

    if (!consumerPhone) {
      console.error('❌ [ORDER_SERVICE] No consumer phone found - authentication required');
      return { success: false, error: 'Authentication required' };
    }

    // Validate required data
    console.log('✅ [ORDER_SERVICE] Validating checkout data...');
    if (!checkoutData.deliveryAddress || !checkoutData.deliveryWilaya || !checkoutData.paymentMethod) {
      console.error('❌ [ORDER_SERVICE] Missing required checkout information:', {
        deliveryAddress: !!checkoutData.deliveryAddress,
        deliveryWilaya: !!checkoutData.deliveryWilaya,
        paymentMethod: !!checkoutData.paymentMethod
      });
      return { success: false, error: 'Missing required checkout information' };
    }

    if (cartItems.length === 0) {
      console.error('❌ [ORDER_SERVICE] Cart is empty');
      return { success: false, error: 'Cart is empty' };
    }
    console.log('✅ [ORDER_SERVICE] Validation passed');

    // Transform cart items to order items
    console.log('🔄 [ORDER_SERVICE] Transforming cart items...');
    console.log('🔄 [ORDER_SERVICE] ORIGINAL CART ITEMS:', cartItems.map(item => ({
      id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.quantity,
      supplierName: item.supplierName,
      supplierAccountId: item.supplierAccountId,
      marketplaceSection: item.marketplaceSection,
      shippingOrigin: item.shippingOrigin
    })));

    const orderItems = transformCartItemsToOrderItems(cartItems);
    console.log('🔄 [ORDER_SERVICE] TRANSFORMED ORDER ITEMS:', orderItems.map(item => ({
      product_name: item.product_name,
      supplier_name: item.supplier_name,
      supplier_account_id: item.supplier_account_id,
      marketplace_section: item.marketplace_section,
      supplier_wilaya: item.supplier_wilaya,
      total_price: item.total_price
    })));

    // Prepare order data with consumer name and shipping info
    console.log('📋 [ORDER_SERVICE] Preparing order data...');
    const orderData: CreateOrderRequest = {
      consumer_phone: consumerPhone,
      consumer_name: consumerName, // CRITICAL: Use actual consumer name from profile
      payment_method: checkoutData.paymentMethod,
      delivery_address: checkoutData.deliveryAddress,
      delivery_coordinates: checkoutData.deliveryCoordinates,
      delivery_wilaya: checkoutData.deliveryWilaya,
      google_maps_url: checkoutData.googleMapsUrl,
      special_instructions: checkoutData.specialInstructions || null,
      order_items: orderItems,
      // NEW: Include shipping information
      shipping_company_id: checkoutData.shippingInfo?.company_id,
      shipping_cost: checkoutData.shippingInfo?.shipping_cost,
      shipping_notes: checkoutData.shippingInfo?.shipping_notes,
      shipping_method: checkoutData.shippingInfo?.shipping_method
    };

    console.log('📋 [ORDER_SERVICE] Final order data being sent to database:', orderData);

    // TEMPORARY FIX: Create order directly using service role to bypass RLS
    console.log('💾 [ORDER_SERVICE] Creating order directly (bypassing RLS)...');

    // Generate order number
    const orderNumber = `ARZ-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    // Calculate totals with AROUZ fees
    const subtotal = orderItems.reduce((sum, item) => sum + item.total_price, 0);
    const shippingCost = orderData.shipping_cost || 0;

    console.log('💰 [ORDER_SERVICE] Calculating AROUZ fees...');
    const feeBreakdown = await calculateCheckoutFees(subtotal, shippingCost, cartItems);

    const totalArouzFees = feeBreakdown.total_fees;
    const totalAmount = subtotal + shippingCost + totalArouzFees;

    console.log('💾 [ORDER_SERVICE] Order calculations:', {
      subtotal,
      shippingCost,
      totalArouzFees,
      totalAmount,
      feeBreakdown: feeBreakdown.fee_summary
    });

    // Create order record
    const { data: orderRecord, error: orderError } = await supabase
      .from('orders')
      .insert({
        order_number: orderNumber,
        consumer_phone: orderData.consumer_phone,
        consumer_name: orderData.consumer_name,
        payment_method: orderData.payment_method,
        delivery_address: orderData.delivery_address,
        delivery_wilaya: orderData.delivery_wilaya,
        google_maps_url: orderData.google_maps_url,
        special_instructions: orderData.special_instructions,
        subtotal: subtotal,
        total_amount: totalAmount,
        status: 'pending',
        // NEW: Include shipping information
        shipping_company_id: orderData.shipping_company_id,
        shipping_cost: orderData.shipping_cost || 0,
        shipping_notes: orderData.shipping_notes,
        shipping_method: orderData.shipping_method || 'standard',
        // NEW: Include AROUZ fee information
        total_arouz_fees: totalArouzFees,
        arouz_commission: feeBreakdown.fee_summary.marketplace_commission,
        arouz_service_fee: feeBreakdown.fee_summary.service_fee,
        arouz_processing_fee: feeBreakdown.fee_summary.processing_fee
      })
      .select()
      .single();

    console.log('💾 [ORDER_SERVICE] Order creation result:', { orderRecord, orderError });

    if (orderError) {
      console.error('❌ [ORDER_SERVICE] Error creating order:', orderError);
      console.error('❌ [ORDER_SERVICE] Full error details:', JSON.stringify(orderError, null, 2));
      return { success: false, error: `Failed to create order: ${orderError.message || 'Unknown error'}` };
    }

    const orderId = orderRecord.id;

    console.log('✅ [ORDER_SERVICE] Order created successfully with ID:', orderId);

    // Create order items
    console.log('💾 [ORDER_SERVICE] Creating order items...');
    const orderItemsData = orderItems.map(item => ({
      order_id: orderId,
      product_id: item.product_id,
      product_name: item.product_name,
      product_image: item.product_image,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price,
      supplier_account_id: item.supplier_account_id,
      supplier_name: item.supplier_name,
      supplier_phone: item.supplier_phone,
      supplier_city: item.supplier_city,
      supplier_wilaya: item.supplier_wilaya,
      marketplace_section: item.marketplace_section,
      category: item.category,
      subcategory: item.subcategory
    }));

    console.log('💾 [ORDER_SERVICE] ORDER ITEMS DATA TO INSERT:', orderItemsData.map(item => ({
      product_name: item.product_name,
      supplier_name: item.supplier_name,
      supplier_account_id: item.supplier_account_id,
      marketplace_section: item.marketplace_section,
      supplier_wilaya: item.supplier_wilaya,
      total_price: item.total_price
    })));

    const { data: insertedItems, error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItemsData)
      .select();

    if (itemsError) {
      console.error('❌ [ORDER_SERVICE] Error creating order items:', itemsError);
      // Try to clean up the order
      await supabase.from('orders').delete().eq('id', orderId);
      return { success: false, error: `Failed to create order items: ${itemsError.message}` };
    }

    console.log('✅ [ORDER_SERVICE] Order items created successfully');
    console.log('✅ [ORDER_SERVICE] INSERTED ITEMS RESULT:', insertedItems?.map(item => ({
      id: item.id,
      product_name: item.product_name,
      supplier_name: item.supplier_name,
      supplier_account_id: item.supplier_account_id,
      marketplace_section: item.marketplace_section,
      supplier_wilaya: item.supplier_wilaya,
      total_price: item.total_price
    })));

    // Create shipment record
    console.log('💾 [ORDER_SERVICE] Creating shipment record...');
    const { error: shipmentError } = await supabase
      .from('shipments')
      .insert({
        order_id: orderId,
        status: 'unassigned',
        arouz_shipping_fee: 15.00
      });

    if (shipmentError) {
      console.error('❌ [ORDER_SERVICE] Error creating shipment:', shipmentError);
      // Continue anyway - shipment can be created later
    } else {
      console.log('✅ [ORDER_SERVICE] Shipment created successfully');
    }

    // Fetch the created order with items
    const { data: order, error: fetchError } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (*)
      `)
      .eq('id', orderId)
      .single();

    if (fetchError) {
      console.error('Error fetching created order:', fetchError);
      return { success: false, error: 'Order created but failed to retrieve details' };
    }

    return { success: true, order: order as OrderWithItems };

  } catch (error) {
    console.error('Error in createOrder:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get orders for current consumer
 */
export async function getConsumerOrders(): Promise<{ success: boolean; orders?: Order[]; error?: string }> {
  try {
    console.log('🔍 [GET_CONSUMER_ORDERS] Starting getConsumerOrders...');

    const consumerPhone = await getCurrentConsumerPhone();
    console.log('🔍 [GET_CONSUMER_ORDERS] Consumer phone:', consumerPhone);

    if (!consumerPhone) {
      console.error('❌ [GET_CONSUMER_ORDERS] No consumer phone - authentication required');
      return { success: false, error: 'Authentication required' };
    }

    console.log('🔍 [GET_CONSUMER_ORDERS] Fetching orders from database...');
    const { data: orders, error } = await supabase
      .from('orders')
      .select('*')
      .eq('consumer_phone', consumerPhone)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ [GET_CONSUMER_ORDERS] Database error:', error);
      return { success: false, error: 'Failed to fetch orders' };
    }

    console.log('✅ [GET_CONSUMER_ORDERS] Orders fetched successfully:', {
      count: orders?.length || 0,
      orders: orders
    });

    return { success: true, orders: orders as Order[] };

  } catch (error) {
    console.error('❌ [GET_CONSUMER_ORDERS] Exception in getConsumerOrders:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get order details by ID
 */
export async function getOrderById(orderId: string): Promise<{ success: boolean; order?: OrderWithItems; error?: string }> {
  try {
    console.log('🔍 [GET_ORDER] Starting getOrderById for:', orderId);

    const consumerPhone = await getCurrentConsumerPhone();
    console.log('🔍 [GET_ORDER] Consumer phone:', consumerPhone);

    if (!consumerPhone) {
      console.error('❌ [GET_ORDER] No consumer phone - authentication required');
      return { success: false, error: 'Authentication required' };
    }

    console.log('🔍 [GET_ORDER] Fetching order from database...');

    // TEMPORARY FIX: Fetch order and order_items separately to bypass potential RLS issues
    console.log('🔍 [GET_ORDER] Step 1: Fetching order...');
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .eq('consumer_phone', consumerPhone)
      .single();

    if (orderError) {
      console.error('❌ [GET_ORDER] Error fetching order:', orderError);
      return { success: false, error: 'Order not found' };
    }

    console.log('✅ [GET_ORDER] Order fetched successfully:', orderData);

    // Step 2: Fetch order items separately - Fixed RLS policy
    console.log('🔍 [GET_ORDER] Step 2: Fetching order items...');

    // CRITICAL FIX: Fixed the RLS policy to allow consumers to see ALL items in their orders
    // The new policy allows consumers to see all items in their orders regardless of supplier
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items')
      .select('*')
      .eq('order_id', orderId);

    if (itemsError) {
      console.error('❌ [GET_ORDER] Error fetching order items:', itemsError);
      console.error('❌ [GET_ORDER] Full error details:', JSON.stringify(itemsError, null, 2));
      // Continue without items rather than failing completely
      console.log('⚠️ [GET_ORDER] Continuing without order items...');
    } else {
      console.log('✅ [GET_ORDER] Order items fetched successfully');
      console.log('✅ [GET_ORDER] RETRIEVED ORDER ITEMS:', orderItems?.map(item => ({
        id: item.id,
        product_name: item.product_name,
        supplier_name: item.supplier_name,
        supplier_account_id: item.supplier_account_id,
        marketplace_section: item.marketplace_section,
        supplier_wilaya: item.supplier_wilaya,
        total_price: item.total_price
      })));
      console.log('✅ [GET_ORDER] TOTAL ITEMS RETRIEVED:', orderItems?.length || 0);
    }

    // Step 3: Fetch shipments
    console.log('🔍 [GET_ORDER] Step 3: Fetching shipments...');
    const { data: shipments, error: shipmentsError } = await supabase
      .from('shipments')
      .select('*')
      .eq('order_id', orderId);

    if (shipmentsError) {
      console.error('❌ [GET_ORDER] Error fetching shipments:', shipmentsError);
    } else {
      console.log('✅ [GET_ORDER] Shipments fetched successfully:', shipments);
    }

    // Combine the data
    const order = {
      ...orderData,
      order_items: orderItems || [],
      shipments: shipments || []
    };

    console.log('✅ [GET_ORDER] Final combined order data:', {
      id: order.id,
      order_number: order.order_number,
      order_items_count: order.order_items?.length || 0,
      order_items: order.order_items,
      shipments_count: order.shipments?.length || 0
    });

    return { success: true, order: order as OrderWithItems };

  } catch (error) {
    console.error('❌ [GET_ORDER] Exception in getOrderById:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Cancel an order
 */
export async function cancelOrder(orderId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const consumerPhone = await getCurrentConsumerPhone();
    if (!consumerPhone) {
      return { success: false, error: 'Authentication required' };
    }

    const { error } = await supabase
      .from('orders')
      .update({ 
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .eq('consumer_phone', consumerPhone)
      .eq('status', 'pending'); // Only allow cancelling pending orders

    if (error) {
      console.error('Error cancelling order:', error);
      return { success: false, error: 'Failed to cancel order' };
    }

    return { success: true };

  } catch (error) {
    console.error('Error in cancelOrder:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
