/**
 * Shipping Company Authentication Service
 * 
 * Handles authentication for shipping companies using login codes
 * Uses PostgreSQL settings to store company context for RLS policies
 */

import { supabase } from '@/services/authService';

export interface ShippingCompanySession {
  id: string;
  company_name: string;
  login_code: string;
  contact_email?: string;
  contact_phone?: string;
  coverage_areas: string[];
  service_types: string[];
  commission_rate: number;
  is_active: boolean;
  authenticated_at: string;
}

export interface ShippingCompanyLoginResult {
  success: boolean;
  company?: ShippingCompanySession;
  error?: string;
}

/**
 * Authenticate shipping company with login code
 */
export async function authenticateShippingCompany(loginCode: string): Promise<ShippingCompanyLoginResult> {
  try {
    console.log('🚚 [SHIPPING_AUTH] Authenticating shipping company with code:', loginCode.substring(0, 4) + '...');

    // Validate login code format
    if (!loginCode || loginCode.length < 5) {
      return { success: false, error: 'Invalid login code format' };
    }

    // Find shipping company by login code
    const { data: company, error } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('login_code', loginCode)
      .eq('is_active', true)
      .single();

    if (error || !company) {
      console.error('❌ [SHIPPING_AUTH] Company not found or inactive:', error);
      return { success: false, error: 'Invalid login code or company is inactive' };
    }

    console.log('✅ [SHIPPING_AUTH] Company found:', company.company_name);

    // Set PostgreSQL setting for RLS policies
    const { error: settingError } = await supabase.rpc('set_app_config', {
      setting_name: 'app.shipping_company_code',
      setting_value: loginCode,
      is_local: false
    });

    if (settingError) {
      console.error('❌ [SHIPPING_AUTH] Error setting company context:', settingError);
      return { success: false, error: 'Authentication failed - unable to set company context' };
    }

    // Create session object
    const session: ShippingCompanySession = {
      id: company.id,
      company_name: company.company_name,
      login_code: company.login_code,
      contact_email: company.contact_email,
      contact_phone: company.contact_phone,
      coverage_areas: company.coverage_areas || [],
      service_types: company.service_types || [],
      commission_rate: company.commission_rate || 0,
      is_active: company.is_active,
      authenticated_at: new Date().toISOString()
    };

    // Store session in localStorage
    localStorage.setItem('shipping_company_session', JSON.stringify(session));

    console.log('✅ [SHIPPING_AUTH] Authentication successful for:', company.company_name);
    return { success: true, company: session };

  } catch (error) {
    console.error('❌ [SHIPPING_AUTH] Authentication exception:', error);
    return { success: false, error: 'An unexpected error occurred during authentication' };
  }
}

/**
 * Get current shipping company session
 */
export function getCurrentShippingCompanySession(): ShippingCompanySession | null {
  try {
    const sessionData = localStorage.getItem('shipping_company_session');
    if (!sessionData) {
      return null;
    }

    const session = JSON.parse(sessionData) as ShippingCompanySession;
    
    // Check if session is still valid (24 hours)
    const sessionAge = Date.now() - new Date(session.authenticated_at).getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    if (sessionAge > maxAge) {
      console.log('🚚 [SHIPPING_AUTH] Session expired, clearing...');
      clearShippingCompanySession();
      return null;
    }

    return session;
  } catch (error) {
    console.error('❌ [SHIPPING_AUTH] Error getting session:', error);
    clearShippingCompanySession();
    return null;
  }
}

/**
 * Clear shipping company session
 */
export function clearShippingCompanySession(): void {
  try {
    localStorage.removeItem('shipping_company_session');
    
    // Clear PostgreSQL setting
    supabase.rpc('set_app_config', {
      setting_name: 'app.shipping_company_code',
      setting_value: '',
      is_local: false
    }).catch(error => {
      console.error('❌ [SHIPPING_AUTH] Error clearing company context:', error);
    });

    console.log('✅ [SHIPPING_AUTH] Session cleared');
  } catch (error) {
    console.error('❌ [SHIPPING_AUTH] Error clearing session:', error);
  }
}

/**
 * Refresh shipping company session (extend expiry)
 */
export async function refreshShippingCompanySession(): Promise<boolean> {
  try {
    const currentSession = getCurrentShippingCompanySession();
    if (!currentSession) {
      return false;
    }

    // Re-authenticate to refresh session
    const result = await authenticateShippingCompany(currentSession.login_code);
    return result.success;
  } catch (error) {
    console.error('❌ [SHIPPING_AUTH] Error refreshing session:', error);
    return false;
  }
}

/**
 * Check if shipping company is authenticated
 */
export function isShippingCompanyAuthenticated(): boolean {
  const session = getCurrentShippingCompanySession();
  return session !== null;
}

/**
 * Initialize shipping company context for database queries
 * This should be called before making any database queries
 */
export async function initializeShippingCompanyContext(): Promise<boolean> {
  try {
    const session = getCurrentShippingCompanySession();
    if (!session) {
      return false;
    }

    // Set PostgreSQL setting for RLS policies
    const { error } = await supabase.rpc('set_app_config', {
      setting_name: 'app.shipping_company_code',
      setting_value: session.login_code,
      is_local: false
    });

    if (error) {
      console.error('❌ [SHIPPING_AUTH] Error initializing context:', error);
      return false;
    }

    console.log('✅ [SHIPPING_AUTH] Context initialized for:', session.company_name);
    return true;
  } catch (error) {
    console.error('❌ [SHIPPING_AUTH] Exception initializing context:', error);
    return false;
  }
}

/**
 * Logout shipping company
 */
export async function logoutShippingCompany(): Promise<void> {
  try {
    console.log('🚚 [SHIPPING_AUTH] Logging out shipping company...');
    clearShippingCompanySession();
    
    // Redirect to login page
    window.location.href = '/shipping/login';
  } catch (error) {
    console.error('❌ [SHIPPING_AUTH] Error during logout:', error);
  }
}
