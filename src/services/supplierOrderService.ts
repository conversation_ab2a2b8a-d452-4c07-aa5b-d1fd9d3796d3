/**
 * Supplier Order Service
 * 
 * Handles order management for suppliers and merchants
 * Provides comprehensive order tracking, status updates, and coordination features
 */

import { supabase } from '@/services/authService';

// Types for supplier order management
export interface SupplierOrderItem {
  id: string;
  order_id: string;
  product_id: string;
  product_name: string;
  product_image?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  supplier_account_id: string;
  supplier_name: string;
  supplier_phone?: string;
  supplier_city?: string;
  supplier_wilaya?: string;
  marketplace_section: 'wholesale' | 'retail';
  category?: string;
  subcategory?: string;
  created_at: string;
}

export interface SupplierOrder {
  id: string;
  order_number: string;
  consumer_phone: string;
  consumer_name?: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  payment_method: 'cash_on_delivery' | 'store_pickup';
  delivery_address: string;
  delivery_coordinates?: { lat: number; lng: number };
  delivery_wilaya: string;
  google_maps_url: string;
  subtotal: number;
  total_shipping_cost: number;
  total_arouz_fees: number;
  total_amount: number;
  special_instructions?: string;
  estimated_delivery_date?: string;
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  delivered_at?: string;
  // Shipping information
  shipping_company_id?: string;
  shipping_cost?: number;
  shipping_notes?: string;
  shipping_method?: string;
  // AROUZ fee breakdown
  arouz_commission?: number;
  arouz_service_fee?: number;
  arouz_processing_fee?: number;
}

export interface SupplierOrderWithItems extends SupplierOrder {
  order_items: SupplierOrderItem[];
  supplier_items: SupplierOrderItem[]; // Only items belonging to this supplier
  other_suppliers: Array<{
    supplier_name: string;
    supplier_wilaya: string;
    item_count: number;
    total_value: number;
  }>;
}

export interface OrderStatusUpdate {
  order_id: string;
  new_status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  notes?: string;
}

export interface SupplierOrderStats {
  total_orders: number;
  pending_orders: number;
  confirmed_orders: number;
  shipped_orders: number;
  delivered_orders: number;
  cancelled_orders: number;
  total_revenue: number;
  this_month_revenue: number;
  this_month_orders: number;
  average_order_value: number;
}

/**
 * Get orders containing products from the current supplier/merchant
 */
export async function getSupplierOrders(
  filters?: {
    status?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }
): Promise<{ success: boolean; orders?: SupplierOrderWithItems[]; total_count?: number; error?: string }> {
  try {
    console.log('🔍 [SUPPLIER_ORDERS] Getting supplier orders with filters:', filters);

    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    console.log('🔍 [SUPPLIER_ORDERS] Current user ID:', user.id);

    // CRITICAL SECURITY FIX: Use proper filtering to only get orders containing supplier's products
    // First, get order IDs that contain this supplier's products
    const { data: supplierOrderIds, error: orderIdsError } = await supabase
      .from('order_items')
      .select('order_id')
      .eq('supplier_account_id', user.id);

    if (orderIdsError) {
      console.error('❌ [SUPPLIER_ORDERS] Error fetching supplier order IDs:', orderIdsError);
      return { success: false, error: 'Failed to fetch orders' };
    }

    if (!supplierOrderIds || supplierOrderIds.length === 0) {
      console.log('ℹ️ [SUPPLIER_ORDERS] No orders found for supplier');
      return { success: true, orders: [], total_count: 0 };
    }

    const orderIds = supplierOrderIds.map(item => item.order_id);
    console.log('🔍 [SUPPLIER_ORDERS] Found order IDs for supplier:', orderIds);

    // Now build query for orders with proper filtering
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          order_id,
          product_id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_account_id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section,
          category,
          subcategory,
          created_at
        )
      `)
      .in('id', orderIds);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }

    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    if (filters?.search) {
      query = query.or(`order_number.ilike.%${filters.search}%,consumer_name.ilike.%${filters.search}%,consumer_phone.ilike.%${filters.search}%`);
    }

    // Apply pagination
    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
    }

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: orders, error, count } = await query;

    if (error) {
      console.error('❌ [SUPPLIER_ORDERS] Database error:', error);
      return { success: false, error: 'Failed to fetch orders' };
    }

    console.log('✅ [SUPPLIER_ORDERS] Raw orders fetched:', orders?.length || 0);

    if (!orders || orders.length === 0) {
      return { success: true, orders: [], total_count: 0 };
    }

    // Process orders to separate supplier items from other suppliers
    const processedOrders: SupplierOrderWithItems[] = orders.map(order => {
      const allItems = order.order_items || [];
      const supplierItems = allItems.filter(item => item.supplier_account_id === user.id);
      
      // Group other suppliers
      const otherSuppliersMap = new Map();
      allItems
        .filter(item => item.supplier_account_id !== user.id)
        .forEach(item => {
          const key = `${item.supplier_name}-${item.supplier_wilaya}`;
          if (!otherSuppliersMap.has(key)) {
            otherSuppliersMap.set(key, {
              supplier_name: item.supplier_name,
              supplier_wilaya: item.supplier_wilaya,
              item_count: 0,
              total_value: 0
            });
          }
          const supplier = otherSuppliersMap.get(key);
          supplier.item_count += item.quantity;
          supplier.total_value += item.total_price;
        });

      return {
        ...order,
        order_items: allItems,
        supplier_items: supplierItems,
        other_suppliers: Array.from(otherSuppliersMap.values())
      };
    });

    console.log('✅ [SUPPLIER_ORDERS] Processed orders:', {
      total: processedOrders.length,
      sample: processedOrders[0] ? {
        order_number: processedOrders[0].order_number,
        supplier_items: processedOrders[0].supplier_items.length,
        other_suppliers: processedOrders[0].other_suppliers.length
      } : null
    });

    return { 
      success: true, 
      orders: processedOrders,
      total_count: count || processedOrders.length
    };

  } catch (error) {
    console.error('❌ [SUPPLIER_ORDERS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get detailed order information for a specific order
 */
export async function getSupplierOrderById(orderId: string): Promise<{ success: boolean; order?: SupplierOrderWithItems; error?: string }> {
  try {
    console.log('🔍 [SUPPLIER_ORDER_DETAIL] Getting order details for:', orderId);

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    console.log('🔍 [SUPPLIER_ORDER_DETAIL] Current user ID:', user.id);
    console.log('🔍 [SUPPLIER_ORDER_DETAIL] Order ID to fetch:', orderId);

    // CRITICAL SECURITY FIX: First verify supplier has items in this order
    const { data: supplierOrderItems, error: itemsError } = await supabase
      .from('order_items')
      .select('order_id')
      .eq('order_id', orderId)
      .eq('supplier_account_id', user.id);

    if (itemsError) {
      console.error('❌ [SUPPLIER_ORDER_DETAIL] Error checking supplier access:', itemsError);
      return { success: false, error: 'Failed to verify order access' };
    }

    if (!supplierOrderItems || supplierOrderItems.length === 0) {
      console.error('❌ [SUPPLIER_ORDER_DETAIL] Supplier has no items in this order');
      return { success: false, error: 'Order not found or access denied' };
    }

    // Now get order with all items (supplier has verified access)
    const { data: order, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          order_id,
          product_id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_account_id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section,
          category,
          subcategory,
          created_at
        )
      `)
      .eq('id', orderId)
      .single();

    console.log('🔍 [SUPPLIER_ORDER_DETAIL] Raw query result:', { order, error });

    if (error) {
      console.error('❌ [SUPPLIER_ORDER_DETAIL] Database error:', error);
      return { success: false, error: `Order not found: ${error.message}` };
    }

    if (!order) {
      console.error('❌ [SUPPLIER_ORDER_DETAIL] No order returned from query');
      return { success: false, error: 'Order not found in database' };
    }

    console.log('✅ [SUPPLIER_ORDER_DETAIL] Order fetched:', {
      order_number: order.order_number,
      total_items: order.order_items?.length || 0,
      order_items_sample: order.order_items?.slice(0, 2)
    });

    // Check if supplier has items in this order
    const supplierItems = order.order_items?.filter(item => item.supplier_account_id === user.id) || [];

    console.log('🔍 [SUPPLIER_ORDER_DETAIL] Filtering supplier items:', {
      total_items: order.order_items?.length || 0,
      supplier_items_found: supplierItems.length,
      current_user_id: user.id,
      all_supplier_ids: order.order_items?.map(item => item.supplier_account_id) || []
    });

    if (supplierItems.length === 0) {
      console.error('❌ [SUPPLIER_ORDER_DETAIL] No items found for supplier:', {
        user_id: user.id,
        order_items: order.order_items?.map(item => ({
          id: item.id,
          supplier_account_id: item.supplier_account_id,
          product_name: item.product_name
        })) || []
      });
      return { success: false, error: 'You do not have access to this order - no items found for your account' };
    }

    // Process order data
    const allItems = order.order_items || [];
    
    // Group other suppliers
    const otherSuppliersMap = new Map();
    allItems
      .filter(item => item.supplier_account_id !== user.id)
      .forEach(item => {
        const key = `${item.supplier_name}-${item.supplier_wilaya}`;
        if (!otherSuppliersMap.has(key)) {
          otherSuppliersMap.set(key, {
            supplier_name: item.supplier_name,
            supplier_wilaya: item.supplier_wilaya,
            item_count: 0,
            total_value: 0
          });
        }
        const supplier = otherSuppliersMap.get(key);
        supplier.item_count += item.quantity;
        supplier.total_value += item.total_price;
      });

    const processedOrder: SupplierOrderWithItems = {
      ...order,
      order_items: allItems,
      supplier_items: supplierItems,
      other_suppliers: Array.from(otherSuppliersMap.values())
    };

    console.log('✅ [SUPPLIER_ORDER_DETAIL] Order details retrieved:', {
      order_number: processedOrder.order_number,
      supplier_items: processedOrder.supplier_items.length,
      other_suppliers: processedOrder.other_suppliers.length
    });

    return { success: true, order: processedOrder };

  } catch (error) {
    console.error('❌ [SUPPLIER_ORDER_DETAIL] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update order status (supplier can confirm orders)
 */
export async function updateOrderStatus(update: OrderStatusUpdate): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 [UPDATE_ORDER_STATUS] Updating order status:', update);

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    // Verify supplier has items in this order
    const { data: orderItems } = await supabase
      .from('order_items')
      .select('id')
      .eq('order_id', update.order_id)
      .eq('supplier_account_id', user.id);

    if (!orderItems || orderItems.length === 0) {
      return { success: false, error: 'You do not have access to this order' };
    }

    // Update order status
    const updateData: any = {
      status: update.new_status,
      updated_at: new Date().toISOString()
    };

    // Add timestamp for specific status changes
    if (update.new_status === 'confirmed') {
      updateData.confirmed_at = new Date().toISOString();
    } else if (update.new_status === 'delivered') {
      updateData.delivered_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', update.order_id);

    if (error) {
      console.error('❌ [UPDATE_ORDER_STATUS] Database error:', error);
      return { success: false, error: 'Failed to update order status' };
    }

    console.log('✅ [UPDATE_ORDER_STATUS] Order status updated successfully');
    return { success: true };

  } catch (error) {
    console.error('❌ [UPDATE_ORDER_STATUS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get supplier order statistics
 */
export async function getSupplierOrderStats(): Promise<{ success: boolean; stats?: SupplierOrderStats; error?: string }> {
  try {
    console.log('📊 [SUPPLIER_STATS] Getting supplier order statistics...');

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    // CRITICAL SECURITY FIX: Get orders containing supplier's products using proper filtering
    const { data: supplierOrderIds, error: orderIdsError } = await supabase
      .from('order_items')
      .select('order_id')
      .eq('supplier_account_id', user.id);

    if (orderIdsError) {
      console.error('❌ [SUPPLIER_STATS] Error fetching supplier order IDs:', orderIdsError);
      return { success: false, error: 'Failed to fetch statistics' };
    }

    if (!supplierOrderIds || supplierOrderIds.length === 0) {
      const emptyStats: SupplierOrderStats = {
        total_orders: 0,
        pending_orders: 0,
        confirmed_orders: 0,
        shipped_orders: 0,
        delivered_orders: 0,
        cancelled_orders: 0,
        total_revenue: 0,
        this_month_revenue: 0,
        this_month_orders: 0,
        average_order_value: 0
      };
      return { success: true, stats: emptyStats };
    }

    const orderIds = supplierOrderIds.map(item => item.order_id);

    // Get all orders with supplier's items
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        id,
        status,
        total_amount,
        created_at,
        order_items (
          supplier_account_id,
          total_price
        )
      `)
      .in('id', orderIds);

    if (error) {
      console.error('❌ [SUPPLIER_STATS] Database error:', error);
      return { success: false, error: 'Failed to fetch statistics' };
    }

    if (!orders || orders.length === 0) {
      const emptyStats: SupplierOrderStats = {
        total_orders: 0,
        pending_orders: 0,
        confirmed_orders: 0,
        shipped_orders: 0,
        delivered_orders: 0,
        cancelled_orders: 0,
        total_revenue: 0,
        this_month_revenue: 0,
        this_month_orders: 0,
        average_order_value: 0
      };
      return { success: true, stats: emptyStats };
    }

    // Calculate statistics
    const now = new Date();
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    let totalRevenue = 0;
    let thisMonthRevenue = 0;
    let thisMonthOrders = 0;

    const statusCounts = {
      pending: 0,
      confirmed: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0
    };

    orders.forEach(order => {
      // Count by status
      statusCounts[order.status as keyof typeof statusCounts]++;

      // Calculate revenue from supplier's items only
      const supplierItemsValue = order.order_items
        .filter(item => item.supplier_account_id === user.id)
        .reduce((sum, item) => sum + item.total_price, 0);

      totalRevenue += supplierItemsValue;

      // This month statistics
      const orderDate = new Date(order.created_at);
      if (orderDate >= thisMonthStart) {
        thisMonthRevenue += supplierItemsValue;
        thisMonthOrders++;
      }
    });

    const stats: SupplierOrderStats = {
      total_orders: orders.length,
      pending_orders: statusCounts.pending,
      confirmed_orders: statusCounts.confirmed,
      shipped_orders: statusCounts.shipped,
      delivered_orders: statusCounts.delivered,
      cancelled_orders: statusCounts.cancelled,
      total_revenue: totalRevenue,
      this_month_revenue: thisMonthRevenue,
      this_month_orders: thisMonthOrders,
      average_order_value: orders.length > 0 ? totalRevenue / orders.length : 0
    };

    console.log('✅ [SUPPLIER_STATS] Statistics calculated:', stats);
    return { success: true, stats };

  } catch (error) {
    console.error('❌ [SUPPLIER_STATS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
