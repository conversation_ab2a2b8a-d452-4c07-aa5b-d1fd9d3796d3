/**
 * Rate limiting service to prevent brute force attacks
 * 
 * This service implements a sliding window rate limiter that tracks:
 * - Login attempts by IP address
 * - Login attempts by username/email
 * - API requests by IP address
 * 
 * It uses localStorage for client-side persistence in this implementation,
 * but in a production environment, this would be implemented server-side.
 */

// Rate limit configurations
const RATE_LIMITS = {
  // Login attempts: 5 attempts per 15 minutes
  LOGIN_IP: { maxAttempts: 5, windowMs: 15 * 60 * 1000 },
  LOGIN_USER: { maxAttempts: 5, windowMs: 15 * 60 * 1000 },
  
  // API requests: 100 requests per minute
  API_IP: { maxAttempts: 100, windowMs: 60 * 1000 },
  
  // Admin actions: 30 requests per minute
  ADMIN_ACTION: { maxAttempts: 30, windowMs: 60 * 1000 },
};

// Interface for rate limit record
interface RateLimitRecord {
  attempts: Array<number>; // Timestamps of attempts
  blockedUntil?: number;   // Timestamp until which the entity is blocked
}

// Get rate limit record from localStorage
const getRateLimitRecord = (key: string): RateLimitRecord => {
  const stored = localStorage.getItem(`rateLimit:${key}`);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch (e) {
      console.error('Error parsing rate limit record:', e);
    }
  }
  return { attempts: [] };
};

// Save rate limit record to localStorage
const saveRateLimitRecord = (key: string, record: RateLimitRecord): void => {
  localStorage.setItem(`rateLimit:${key}`, JSON.stringify(record));
};

// Clean up expired attempts from the record
const cleanupExpiredAttempts = (record: RateLimitRecord, windowMs: number): RateLimitRecord => {
  const now = Date.now();
  const validAttempts = record.attempts.filter(timestamp => now - timestamp < windowMs);
  
  return {
    ...record,
    attempts: validAttempts,
    // If the entity was blocked but the block has expired, remove the block
    blockedUntil: record.blockedUntil && record.blockedUntil > now ? record.blockedUntil : undefined,
  };
};

/**
 * Check if an entity (IP, user) is rate limited - DISABLED: Always allow unlimited attempts
 *
 * @param type The type of rate limit to check (LOGIN_IP, LOGIN_USER, API_IP, ADMIN_ACTION)
 * @param identifier The identifier for the entity (IP address, username, etc.)
 * @returns Object with isLimited flag and timeRemaining in ms
 */
export const checkRateLimit = (
  type: keyof typeof RATE_LIMITS,
  identifier: string
): { isLimited: boolean; timeRemaining: number } => {
  // DISABLED: Rate limiting completely removed - always allow unlimited attempts
  return { isLimited: false, timeRemaining: 0 };
};

/**
 * Record an attempt for rate limiting - DISABLED: Always allow unlimited attempts
 *
 * @param type The type of rate limit (LOGIN_IP, LOGIN_USER, API_IP, ADMIN_ACTION)
 * @param identifier The identifier for the entity (IP address, username, etc.)
 * @returns Object with isLimited flag and timeRemaining in ms
 */
export const recordAttempt = (
  type: keyof typeof RATE_LIMITS,
  identifier: string
): { isLimited: boolean; timeRemaining: number } => {
  // DISABLED: Rate limiting completely removed - always allow unlimited attempts
  return { isLimited: false, timeRemaining: 0 };
};

/**
 * Reset rate limit for an entity
 * 
 * @param type The type of rate limit (LOGIN_IP, LOGIN_USER, API_IP, ADMIN_ACTION)
 * @param identifier The identifier for the entity (IP address, username, etc.)
 */
export const resetRateLimit = (type: keyof typeof RATE_LIMITS, identifier: string): void => {
  const key = `${type}:${identifier}`;
  saveRateLimitRecord(key, { attempts: [] });
};

/**
 * Get client IP address (placeholder implementation)
 * 
 * In a real implementation, this would be handled server-side
 */
export const getClientIP = (): string => {
  // In a real implementation, this would be provided by the server
  return '127.0.0.1';
};

/**
 * Format time remaining for user display
 * 
 * @param ms Time remaining in milliseconds
 * @returns Formatted time string (e.g., "5 minutes 30 seconds")
 */
export const formatTimeRemaining = (ms: number): string => {
  if (ms <= 0) return 'now';
  
  const seconds = Math.floor((ms / 1000) % 60);
  const minutes = Math.floor((ms / (1000 * 60)) % 60);
  const hours = Math.floor(ms / (1000 * 60 * 60));
  
  const parts = [];
  if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
  if (minutes > 0) parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
  if (seconds > 0) parts.push(`${seconds} second${seconds !== 1 ? 's' : ''}`);
  
  return parts.join(' ');
};
