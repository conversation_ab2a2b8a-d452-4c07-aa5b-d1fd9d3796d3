import supabase from '@/services/authService';

/**
 * Audit event interface
 */
export interface AuditEvent {
  userId: string;
  action: string;
  resource: string;
  details?: Record<string, any>;
  ip?: string;
  userAgent?: string;
}

/**
 * Log an audit event to the audit_logs table
 * 
 * @param event The audit event to log
 * @returns Promise with success status and any error
 */
export const logAuditEvent = async (event: AuditEvent): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // In development mode, just log to console
    if (import.meta.env.DEV) {
      console.log('AUDIT LOG:', event);
      return { success: true, error: null };
    }

    // Add client information
    const clientInfo = {
      ip: event.ip || getClientIP(),
      userAgent: event.userAgent || navigator.userAgent,
      timestamp: new Date().toISOString(),
    };

    // Insert audit log into Supabase
    const { error } = await supabase
      .from('audit_logs')
      .insert({
        user_id: event.userId,
        action: event.action,
        resource: event.resource,
        details: { ...event.details, client: clientInfo },
        created_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error logging audit event:', error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Unexpected error logging audit event:', error);
    return { success: false, error: error as Error };
  }
};

/**
 * Get audit logs for a specific user or action
 * 
 * @param filters Optional filters for the audit logs
 * @returns Promise with audit logs and any error
 */
export const getAuditLogs = async (filters: {
  userId?: string;
  action?: string;
  resource?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}): Promise<{ logs: any[]; error: Error | null }> => {
  try {
    // In development mode, return mock data
    if (import.meta.env.DEV) {
      console.log('DEV MODE: Returning mock audit logs');
      return { 
        logs: [
          {
            id: '1',
            user_id: filters.userId || 'user-123',
            action: 'login',
            resource: '/app/dashboard',
            details: { ip: '127.0.0.1', success: true },
            created_at: new Date().toISOString(),
          },
          {
            id: '2',
            user_id: filters.userId || 'user-123',
            action: 'product_create',
            resource: '/app/products',
            details: { productId: 'prod-123', name: 'Test Product' },
            created_at: new Date().toISOString(),
          }
        ], 
        error: null 
      };
    }

    // Build query
    let query = supabase
      .from('audit_logs')
      .select('*');

    // Apply filters
    if (filters.userId) {
      query = query.eq('user_id', filters.userId);
    }
    if (filters.action) {
      query = query.eq('action', filters.action);
    }
    if (filters.resource) {
      query = query.eq('resource', filters.resource);
    }
    if (filters.startDate) {
      query = query.gte('created_at', filters.startDate);
    }
    if (filters.endDate) {
      query = query.lte('created_at', filters.endDate);
    }

    // Apply pagination
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
    }

    // Order by created_at descending
    query = query.order('created_at', { ascending: false });

    // Execute query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching audit logs:', error);
      return { logs: [], error };
    }

    return { logs: data || [], error: null };
  } catch (error) {
    console.error('Unexpected error fetching audit logs:', error);
    return { logs: [], error: error as Error };
  }
};

/**
 * Get client IP address
 * 
 * Note: In a real implementation, this would be handled server-side
 * For client-side code, we can only provide a placeholder
 */
const getClientIP = (): string => {
  // In a real implementation, this would be provided by the server
  // For now, return a placeholder
  return '127.0.0.1';
};

/**
 * Log authentication events (login, logout, etc.)
 */
export const logAuthEvent = async (
  userId: string,
  action: 'login' | 'logout' | 'login_failed' | 'password_reset' | 'password_changed',
  details: Record<string, any> = {}
): Promise<void> => {
  try {
    await logAuditEvent({
      userId,
      action: `auth_${action}`,
      resource: '/auth',
      details: {
        ...details,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error(`Error logging ${action} event:`, error);
  }
};

/**
 * Log admin actions (create, update, delete, etc.)
 */
export const logAdminAction = async (
  userId: string,
  action: 'create' | 'update' | 'delete' | 'view',
  resource: string,
  resourceId: string,
  details: Record<string, any> = {}
): Promise<void> => {
  try {
    await logAuditEvent({
      userId,
      action: `${resource}_${action}`,
      resource: `/app/${resource}/${resourceId}`,
      details: {
        ...details,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error(`Error logging admin action ${action} on ${resource}:`, error);
  }
};
