import { supabase } from './authService';

export interface ShippingCompany {
  id: string;
  company_name: string;
  contact_phone?: string;
  contact_email?: string;
  coverage_areas: string[];
  service_types: string[];
  is_active: boolean;
  commission_rate: number;
  minimum_order_value: number;
  maximum_weight_kg?: number;
}

export interface SupplierShipment {
  id: string;
  order_id: string;
  shipping_company_id?: string;
  company_name?: string;
  status: 'unassigned' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered';
  assigned_by: string;
  delivery_cost?: number;
  arouz_shipping_fee: number;
  total_shipping_cost?: number;
  tracking_number?: string;
  tracking_url?: string;
  support_phone?: string;
  driver_name?: string;
  driver_phone?: string;
  estimated_delivery_date?: string;
  supplier_contact_info?: any;
  assigned_at?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get active shipping companies for supplier/merchant selection
 */
export const getShippingCompaniesForSupplier = async (): Promise<{ 
  success: boolean; 
  companies?: ShippingCompany[]; 
  error?: string 
}> => {
  try {
    console.log('🚚 [SUPPLIER_SHIPPING] Fetching active shipping companies...');

    const { data: companies, error } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('is_active', true)
      .order('company_name');

    if (error) {
      console.error('❌ [SUPPLIER_SHIPPING] Error fetching shipping companies:', error);
      return { success: false, error: 'Failed to fetch shipping companies' };
    }

    console.log('✅ [SUPPLIER_SHIPPING] Successfully fetched shipping companies:', companies?.length);
    return { success: true, companies: companies || [] };

  } catch (error) {
    console.error('❌ [SUPPLIER_SHIPPING] Exception in getShippingCompaniesForSupplier:', error);
    return { success: false, error: 'Failed to fetch shipping companies' };
  }
};

/**
 * Assign order to shipping company (supplier/merchant version with role-based access control)
 */
export const assignSupplierOrderToShipping = async (
  orderId: string, 
  shippingCompanyId: string
): Promise<{ success: boolean; shipment?: any; error?: string }> => {
  try {
    console.log('🚚 [SUPPLIER_SHIPPING] Assigning order to shipping company...', { orderId, shippingCompanyId });

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ [SUPPLIER_SHIPPING] Authentication error:', userError);
      return { success: false, error: 'Authentication required' };
    }

    // First, verify the order belongs to this supplier/merchant
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items')
      .select('supplier_account_id')
      .eq('order_id', orderId)
      .eq('supplier_account_id', user.id);

    if (itemsError) {
      console.error('❌ [SUPPLIER_SHIPPING] Error checking order ownership:', itemsError);
      return { success: false, error: 'Failed to verify order ownership' };
    }

    if (!orderItems || orderItems.length === 0) {
      console.error('❌ [SUPPLIER_SHIPPING] Order does not belong to current supplier');
      return { success: false, error: 'You can only assign orders containing your products' };
    }

    // Get the order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          supplier_account_id
        )
      `)
      .eq('id', orderId)
      .single();

    if (orderError || !order) {
      console.error('❌ [SUPPLIER_SHIPPING] Error fetching order:', orderError);
      return { success: false, error: 'Order not found' };
    }

    // Verify order status is confirmed (suppliers can only assign confirmed orders)
    if (order.status !== 'confirmed') {
      return { success: false, error: 'Only confirmed orders can be assigned to shipping companies' };
    }

    // Get shipping company details
    const { data: shippingCompany, error: companyError } = await supabase
      .from('shipping_companies')
      .select('*')
      .eq('id', shippingCompanyId)
      .single();

    if (companyError || !shippingCompany) {
      console.error('❌ [SUPPLIER_SHIPPING] Error fetching shipping company:', companyError);
      return { success: false, error: 'Shipping company not found' };
    }

    // Check if order already has a shipment assigned
    const { data: existingShipment } = await supabase
      .from('shipments')
      .select('id, shipping_company_id, company_name')
      .eq('order_id', orderId)
      .single();

    if (existingShipment) {
      if (existingShipment.shipping_company_id) {
        return { 
          success: false, 
          error: `Order already assigned to ${existingShipment.company_name}` 
        };
      }
    }

    // Prepare supplier contact information for shipping company
    const supplierItems = order.order_items.filter(item => item.supplier_account_id === user.id);
    const supplierContactInfo = {
      suppliers: supplierItems.map(item => ({
        name: item.supplier_name,
        phone: item.supplier_phone,
        city: item.supplier_city,
        wilaya: item.supplier_wilaya
      }))
    };

    // Get order item IDs for this supplier
    const orderItemIds = supplierItems.map(item => item.id);

    console.log('🔍 [SUPPLIER_SHIPPING] Order item IDs for shipment:', {
      supplierItemsCount: supplierItems.length,
      orderItemIds,
      supplierContactInfo
    });

    // Validate that we have order items
    if (orderItemIds.length === 0) {
      console.error('❌ [SUPPLIER_SHIPPING] No order items found for this supplier');
      return { success: false, error: 'No order items found for this supplier' };
    }

    // Create or update shipment record
    let shipment;
    if (existingShipment) {
      // Update existing unassigned shipment
      const { data: updatedShipment, error: updateError } = await supabase
        .from('shipments')
        .update({
          shipping_company_id: shippingCompanyId,
          company_name: shippingCompany.company_name,
          status: 'assigned',
          assigned_by: 'supplier',
          delivery_cost: 500.00, // Default delivery cost - can be updated later
          estimated_delivery_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 days from now
          supplier_contact_info: supplierContactInfo,
          order_item_ids: orderItemIds,
          assigned_at: new Date().toISOString()
        })
        .eq('id', existingShipment.id)
        .select()
        .single();

      if (updateError) {
        console.error('❌ [SUPPLIER_SHIPPING] Error updating shipment:', updateError);
        return { success: false, error: 'Failed to assign shipping company' };
      }
      shipment = updatedShipment;
    } else {
      // Create new shipment record
      const { data: newShipment, error: shipmentError } = await supabase
        .from('shipments')
        .insert({
          order_id: orderId,
          shipping_company_id: shippingCompanyId,
          company_name: shippingCompany.company_name,
          status: 'assigned',
          assigned_by: 'supplier',
          delivery_cost: 500.00, // Default delivery cost - can be updated later
          estimated_delivery_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 days from now
          supplier_contact_info: supplierContactInfo,
          order_item_ids: orderItemIds,
          assigned_at: new Date().toISOString()
        })
        .select()
        .single();

      if (shipmentError) {
        console.error('❌ [SUPPLIER_SHIPPING] Error creating shipment:', {
          error: shipmentError,
          errorCode: shipmentError.code,
          errorMessage: shipmentError.message,
          errorDetails: shipmentError.details,
          orderItemIds,
          supplierContactInfo
        });
        return { success: false, error: `Failed to create shipment record: ${shipmentError.message}` };
      }
      shipment = newShipment;
    }

    console.log('✅ [SUPPLIER_SHIPPING] Successfully assigned order to shipping company:', {
      orderId,
      shippingCompanyId,
      shipmentId: shipment.id
    });

    return { success: true, shipment };

  } catch (error) {
    console.error('❌ [SUPPLIER_SHIPPING] Exception in assignSupplierOrderToShipping:', error);
    return { success: false, error: 'Failed to assign order to shipping company' };
  }
};

/**
 * Get shipments for supplier/merchant orders
 */
export const getSupplierShipments = async (
  filters?: {
    status?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }
): Promise<{ 
  success: boolean; 
  shipments?: SupplierShipment[]; 
  total_count?: number; 
  error?: string 
}> => {
  try {
    console.log('📦 [SUPPLIER_SHIPPING] Getting supplier shipments with filters:', filters);

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ [SUPPLIER_SHIPPING] Authentication error:', userError);
      return { success: false, error: 'Authentication required' };
    }

    // CRITICAL SECURITY FIX: Use proper filtering to only get shipments for supplier's orders
    // First, get order IDs that contain this supplier's products
    const { data: supplierOrderIds, error: orderIdsError } = await supabase
      .from('order_items')
      .select('order_id')
      .eq('supplier_account_id', user.id);

    if (orderIdsError) {
      console.error('❌ [SUPPLIER_SHIPPING] Error fetching supplier order IDs:', orderIdsError);
      return { success: false, error: 'Failed to fetch shipments' };
    }

    if (!supplierOrderIds || supplierOrderIds.length === 0) {
      console.log('📦 [SUPPLIER_SHIPPING] No orders found for supplier, returning empty results');
      return {
        success: true,
        shipments: [],
        total_count: 0
      };
    }

    const orderIds = supplierOrderIds.map(item => item.order_id);
    console.log('📦 [SUPPLIER_SHIPPING] Found order IDs for supplier:', orderIds);

    // Build query for shipments with proper filtering
    let query = supabase
      .from('shipments')
      .select(`
        *,
        orders (
          id,
          order_number,
          status,
          created_at
        )
      `, { count: 'exact' })
      .in('order_id', orderIds);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }

    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    if (filters?.search) {
      query = query.or(`orders.order_number.ilike.%${filters.search}%,company_name.ilike.%${filters.search}%,tracking_number.ilike.%${filters.search}%`);
    }

    // Apply pagination
    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
    }

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: shipments, error, count } = await query;

    if (error) {
      console.error('❌ [SUPPLIER_SHIPPING] Database error:', error);
      return { success: false, error: 'Failed to fetch shipments' };
    }

    console.log('✅ [SUPPLIER_SHIPPING] Successfully fetched shipments:', shipments?.length);
    return { 
      success: true, 
      shipments: shipments || [], 
      total_count: count || 0 
    };

  } catch (error) {
    console.error('❌ [SUPPLIER_SHIPPING] Exception in getSupplierShipments:', error);
    return { success: false, error: 'Failed to fetch shipments' };
  }
};
