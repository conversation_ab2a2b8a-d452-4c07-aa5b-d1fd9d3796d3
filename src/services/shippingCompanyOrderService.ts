/**
 * Shipping Company Order Service
 * 
 * Handles order management for shipping companies
 * Provides access to assigned orders and shipment management
 */

import { supabase } from '@/services/authService';
import { initializeShippingCompanyContext } from './shippingCompanyAuthService';

export interface ShippingOrderItem {
  id: string;
  product_name: string;
  product_image?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  supplier_name: string;
  supplier_phone?: string;
  supplier_city?: string;
  supplier_wilaya?: string;
  marketplace_section: 'wholesale' | 'retail';
}

export interface SupplierContact {
  supplier_name: string;
  supplier_phone?: string;
  supplier_city?: string;
  supplier_wilaya?: string;
  supplier_account_id?: string;
  item_count: number;
  total_value: number;
}

export interface ShippingOrder {
  id: string;
  order_number: string;
  consumer_phone: string;
  consumer_name?: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  payment_method: 'cash_on_delivery' | 'store_pickup';
  delivery_address: string;
  delivery_wilaya: string;
  google_maps_url: string;
  subtotal: number;
  total_shipping_cost: number;
  total_arouz_fees: number;
  total_amount: number;
  special_instructions?: string;
  created_at: string;
  updated_at: string;
  // Shipment information
  shipment_id?: string;
  shipment_status?: 'unassigned' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered';
  tracking_number?: string;
  driver_name?: string;
  driver_phone?: string;
  estimated_delivery_date?: string;
}

export interface ShippingOrderWithDetails extends ShippingOrder {
  order_items: ShippingOrderItem[];
  supplier_contacts: SupplierContact[];
}

export interface ShipmentStatusUpdate {
  shipment_id: string;
  new_status: 'assigned' | 'picked_up' | 'in_transit' | 'delivered';
  tracking_number?: string;
  driver_name?: string;
  driver_phone?: string;
  notes?: string;
}

export interface ShippingCompanyStats {
  total_assigned_orders: number;
  pending_pickup: number;
  in_transit: number;
  delivered_today: number;
  total_revenue: number;
  average_delivery_time: number;
}

/**
 * Get orders assigned to the current shipping company
 */
export async function getShippingCompanyOrders(
  filters?: {
    status?: string;
    shipment_status?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }
): Promise<{ success: boolean; orders?: ShippingOrderWithDetails[]; total_count?: number; error?: string }> {
  try {
    console.log('🚚 [SHIPPING_ORDERS] Getting assigned orders with filters:', filters);

    // Initialize shipping company context
    const contextInitialized = await initializeShippingCompanyContext();
    if (!contextInitialized) {
      return { success: false, error: 'Authentication required' };
    }

    // Build query for orders assigned to this shipping company
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section
        ),
        shipments (
          id,
          status,
          tracking_number,
          driver_name,
          driver_phone,
          estimated_delivery_date,
          supplier_contact_info
        )
      `);

    // Apply filters
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.shipment_status) {
      query = query.eq('shipments.status', filters.shipment_status);
    }

    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }

    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    if (filters?.search) {
      query = query.or(`order_number.ilike.%${filters.search}%,consumer_name.ilike.%${filters.search}%,consumer_phone.ilike.%${filters.search}%`);
    }

    // Apply pagination
    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
    }

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: orders, error, count } = await query;

    if (error) {
      console.error('❌ [SHIPPING_ORDERS] Database error:', error);
      return { success: false, error: 'Failed to fetch orders' };
    }

    console.log('✅ [SHIPPING_ORDERS] Raw orders fetched:', orders?.length || 0);

    if (!orders || orders.length === 0) {
      return { success: true, orders: [], total_count: 0 };
    }

    // Process orders to extract supplier contact information
    const processedOrders: ShippingOrderWithDetails[] = orders.map(order => {
      const shipment = order.shipments[0]; // Should only be one shipment per order for now
      
      // Extract supplier contacts from order items
      const supplierContactsMap = new Map<string, SupplierContact>();
      
      order.order_items.forEach((item: any) => {
        const key = `${item.supplier_name}-${item.supplier_wilaya}`;
        if (!supplierContactsMap.has(key)) {
          supplierContactsMap.set(key, {
            supplier_name: item.supplier_name,
            supplier_phone: item.supplier_phone,
            supplier_city: item.supplier_city,
            supplier_wilaya: item.supplier_wilaya,
            item_count: 0,
            total_value: 0
          });
        }
        const contact = supplierContactsMap.get(key)!;
        contact.item_count += item.quantity;
        contact.total_value += item.total_price;
      });

      return {
        ...order,
        shipment_id: shipment?.id,
        shipment_status: shipment?.status,
        tracking_number: shipment?.tracking_number,
        driver_name: shipment?.driver_name,
        driver_phone: shipment?.driver_phone,
        estimated_delivery_date: shipment?.estimated_delivery_date,
        supplier_contacts: Array.from(supplierContactsMap.values())
      };
    });

    console.log('✅ [SHIPPING_ORDERS] Processed orders:', {
      total: processedOrders.length,
      sample: processedOrders[0] ? {
        order_number: processedOrders[0].order_number,
        supplier_contacts: processedOrders[0].supplier_contacts.length,
        shipment_status: processedOrders[0].shipment_status
      } : null
    });

    return { 
      success: true, 
      orders: processedOrders,
      total_count: count || processedOrders.length
    };

  } catch (error) {
    console.error('❌ [SHIPPING_ORDERS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get detailed order information for shipping company
 */
export async function getShippingOrderById(orderId: string): Promise<{ success: boolean; order?: ShippingOrderWithDetails; error?: string }> {
  try {
    console.log('🚚 [SHIPPING_ORDER_DETAIL] Getting order details for:', orderId);

    // Initialize shipping company context
    const contextInitialized = await initializeShippingCompanyContext();
    if (!contextInitialized) {
      return { success: false, error: 'Authentication required' };
    }

    // Get order with all details
    const { data: order, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section
        ),
        shipments (
          id,
          status,
          tracking_number,
          driver_name,
          driver_phone,
          estimated_delivery_date,
          supplier_contact_info
        )
      `)
      .eq('id', orderId)
      .single();

    if (error || !order) {
      console.error('❌ [SHIPPING_ORDER_DETAIL] Order not found:', error);
      return { success: false, error: 'Order not found or not assigned to your company' };
    }

    // Process supplier contacts
    const supplierContactsMap = new Map<string, SupplierContact>();
    
    order.order_items.forEach((item: any) => {
      const key = `${item.supplier_name}-${item.supplier_wilaya}`;
      if (!supplierContactsMap.has(key)) {
        supplierContactsMap.set(key, {
          supplier_name: item.supplier_name,
          supplier_phone: item.supplier_phone,
          supplier_city: item.supplier_city,
          supplier_wilaya: item.supplier_wilaya,
          item_count: 0,
          total_value: 0
        });
      }
      const contact = supplierContactsMap.get(key)!;
      contact.item_count += item.quantity;
      contact.total_value += item.total_price;
    });

    const shipment = order.shipments[0];
    const processedOrder: ShippingOrderWithDetails = {
      ...order,
      shipment_id: shipment?.id,
      shipment_status: shipment?.status,
      tracking_number: shipment?.tracking_number,
      driver_name: shipment?.driver_name,
      driver_phone: shipment?.driver_phone,
      estimated_delivery_date: shipment?.estimated_delivery_date,
      supplier_contacts: Array.from(supplierContactsMap.values())
    };

    console.log('✅ [SHIPPING_ORDER_DETAIL] Order details retrieved:', {
      order_number: processedOrder.order_number,
      supplier_contacts: processedOrder.supplier_contacts.length,
      shipment_status: processedOrder.shipment_status
    });

    return { success: true, order: processedOrder };

  } catch (error) {
    console.error('❌ [SHIPPING_ORDER_DETAIL] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update shipping order status (wrapper for updateShipmentStatus with additional fields)
 */
export async function updateShippingOrderStatus(
  orderId: string,
  updateData: {
    status: string;
    tracking_number?: string;
    driver_name?: string;
    driver_phone?: string;
    estimated_delivery_date?: string;
    notes?: string;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 [UPDATE_SHIPPING_ORDER_STATUS] Updating order status:', { orderId, updateData });

    // Initialize shipping company context
    const contextInitialized = await initializeShippingCompanyContext();
    if (!contextInitialized) {
      return { success: false, error: 'Authentication required' };
    }

    // Get the shipment for this order
    const { data: shipment, error: shipmentError } = await supabase
      .from('shipments')
      .select('id')
      .eq('order_id', orderId)
      .single();

    if (shipmentError || !shipment) {
      console.error('❌ [UPDATE_SHIPPING_ORDER_STATUS] Shipment not found:', shipmentError);
      return { success: false, error: 'Shipment not found for this order' };
    }

    // Prepare update data for shipment
    const shipmentUpdateData: any = {
      status: updateData.status,
      updated_at: new Date().toISOString()
    };

    // Add optional fields
    if (updateData.tracking_number) {
      shipmentUpdateData.tracking_number = updateData.tracking_number;
    }

    if (updateData.driver_name) {
      shipmentUpdateData.driver_name = updateData.driver_name;
    }

    if (updateData.driver_phone) {
      shipmentUpdateData.driver_phone = updateData.driver_phone;
    }

    if (updateData.estimated_delivery_date) {
      shipmentUpdateData.estimated_delivery_date = updateData.estimated_delivery_date;
    }

    if (updateData.notes) {
      shipmentUpdateData.notes = updateData.notes;
    }

    // Add status-specific timestamps
    if (updateData.status === 'picked_up') {
      shipmentUpdateData.picked_up_at = new Date().toISOString();
    } else if (updateData.status === 'delivered') {
      shipmentUpdateData.delivered_at = new Date().toISOString();
    }

    // Update shipment
    const { error: updateError } = await supabase
      .from('shipments')
      .update(shipmentUpdateData)
      .eq('id', shipment.id);

    if (updateError) {
      console.error('❌ [UPDATE_SHIPPING_ORDER_STATUS] Database error:', updateError);
      return { success: false, error: 'Failed to update shipment status' };
    }

    // Update order status if shipment is delivered
    if (updateData.status === 'delivered') {
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          status: 'delivered',
          delivered_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (orderError) {
        console.error('❌ [UPDATE_SHIPPING_ORDER_STATUS] Error updating order status:', orderError);
        // Continue anyway - shipment status was updated
      }
    }

    console.log('✅ [UPDATE_SHIPPING_ORDER_STATUS] Order status updated successfully');
    return { success: true };

  } catch (error) {
    console.error('❌ [UPDATE_SHIPPING_ORDER_STATUS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update shipment status
 */
export async function updateShipmentStatus(update: ShipmentStatusUpdate): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 [UPDATE_SHIPMENT_STATUS] Updating shipment status:', update);

    // Initialize shipping company context
    const contextInitialized = await initializeShippingCompanyContext();
    if (!contextInitialized) {
      return { success: false, error: 'Authentication required' };
    }

    // Update shipment status
    const updateData: any = {
      status: update.new_status,
      updated_at: new Date().toISOString()
    };

    // Add optional fields
    if (update.tracking_number) {
      updateData.tracking_number = update.tracking_number;
    }

    if (update.driver_name) {
      updateData.driver_name = update.driver_name;
    }

    if (update.driver_phone) {
      updateData.driver_phone = update.driver_phone;
    }

    // Set estimated delivery date for picked up orders
    if (update.new_status === 'picked_up') {
      const estimatedDate = new Date();
      estimatedDate.setDate(estimatedDate.getDate() + 2); // 2 days from pickup
      updateData.estimated_delivery_date = estimatedDate.toISOString().split('T')[0];
    }

    const { error } = await supabase
      .from('shipments')
      .update(updateData)
      .eq('id', update.shipment_id);

    if (error) {
      console.error('❌ [UPDATE_SHIPMENT_STATUS] Database error:', error);
      return { success: false, error: 'Failed to update shipment status' };
    }

    // Update order status if shipment is delivered
    if (update.new_status === 'delivered') {
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          status: 'delivered',
          delivered_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', (await supabase
          .from('shipments')
          .select('order_id')
          .eq('id', update.shipment_id)
          .single()
        ).data?.order_id);

      if (orderError) {
        console.error('❌ [UPDATE_SHIPMENT_STATUS] Error updating order status:', orderError);
        // Continue anyway - shipment status was updated
      }
    }

    console.log('✅ [UPDATE_SHIPMENT_STATUS] Shipment status updated successfully');
    return { success: true };

  } catch (error) {
    console.error('❌ [UPDATE_SHIPMENT_STATUS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get shipping company statistics
 */
export async function getShippingCompanyStats(): Promise<{ success: boolean; stats?: ShippingCompanyStats; error?: string }> {
  try {
    console.log('📊 [SHIPPING_STATS] Getting shipping company statistics...');

    // Initialize shipping company context
    const contextInitialized = await initializeShippingCompanyContext();
    if (!contextInitialized) {
      return { success: false, error: 'Authentication required' };
    }

    // Get all assigned orders with shipments
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        id,
        status,
        total_amount,
        created_at,
        delivered_at,
        shipments!inner (
          id,
          status,
          created_at,
          delivery_cost
        )
      `);

    if (error) {
      console.error('❌ [SHIPPING_STATS] Database error:', error);
      return { success: false, error: 'Failed to fetch statistics' };
    }

    if (!orders || orders.length === 0) {
      const emptyStats: ShippingCompanyStats = {
        total_assigned_orders: 0,
        pending_pickup: 0,
        in_transit: 0,
        delivered_today: 0,
        total_revenue: 0,
        average_delivery_time: 0
      };
      return { success: true, stats: emptyStats };
    }

    // Calculate statistics
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    let totalRevenue = 0;
    let deliveredToday = 0;
    let totalDeliveryTime = 0;
    let deliveredCount = 0;

    const shipmentStatusCounts = {
      unassigned: 0,
      assigned: 0,
      picked_up: 0,
      in_transit: 0,
      delivered: 0
    };

    orders.forEach(order => {
      const shipment = order.shipments[0];
      if (shipment) {
        // Count by shipment status
        shipmentStatusCounts[shipment.status as keyof typeof shipmentStatusCounts]++;

        // Calculate revenue from delivery costs
        totalRevenue += shipment.delivery_cost || 0;

        // Count deliveries today
        if (order.delivered_at) {
          const deliveredDate = new Date(order.delivered_at);
          if (deliveredDate >= todayStart) {
            deliveredToday++;
          }

          // Calculate average delivery time
          const orderDate = new Date(order.created_at);
          const deliveryTime = deliveredDate.getTime() - orderDate.getTime();
          totalDeliveryTime += deliveryTime;
          deliveredCount++;
        }
      }
    });

    const averageDeliveryTime = deliveredCount > 0
      ? Math.round(totalDeliveryTime / deliveredCount / (1000 * 60 * 60 * 24)) // Convert to days
      : 0;

    const stats: ShippingCompanyStats = {
      total_assigned_orders: orders.length,
      pending_pickup: shipmentStatusCounts.assigned,
      in_transit: shipmentStatusCounts.picked_up + shipmentStatusCounts.in_transit,
      delivered_today: deliveredToday,
      total_revenue: totalRevenue,
      average_delivery_time: averageDeliveryTime
    };

    console.log('✅ [SHIPPING_STATS] Statistics calculated:', stats);
    return { success: true, stats };

  } catch (error) {
    console.error('❌ [SHIPPING_STATS] Exception:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}
