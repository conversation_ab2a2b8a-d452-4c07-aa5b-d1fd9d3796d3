/**
 * Test file to verify wholesale pricing tier recalculation in cart
 * This creates a test product with known pricing tiers for testing
 */

import { TyreProduct, PricingTier } from '@/features/products/types/product.types';

// Create a test product with specific pricing tiers for easy testing
export const TEST_WHOLESALE_PRODUCT: TyreProduct = {
  id: 'TEST-WHOLESALE-001',
  name: 'TEST Wholesale Product',
  sku: 'TEST-001',
  partArticleNumber: '123456789',
  category: 'tyres',
  subcategory: 'Tyres',
  descriptionAndSpecifications: 'Test product for wholesale pricing tier verification',
  primaryImage: '/placeholder.svg',
  manufacturer: 'TEST BRAND',
  supplierName: 'Test Supplier',
  stockQuantity: 1000,
  minimumOrderQuantity: 1,
  
  // Specific pricing tiers for testing:
  // Tier 1: 1-99 pieces → 200.00 DZD per piece
  // Tier 2: 100-199 pieces → 100.00 DZD per piece  
  // Tier 3: 200+ pieces → 50.00 DZD per piece
  wholesalePricingTiers: [
    { minQuantity: 1, maxQuantity: 99, price: 200.00 },
    { minQuantity: 100, maxQuantity: 199, price: 100.00 },
    { minQuantity: 200, price: 50.00 }
  ] as PricingTier[],
  
  marketplaceSection: 'wholesale',
  
  // Tyre-specific fields
  width: 225,
  aspectRatio: 45,
  rimDiameter: 17,
  loadIndex: 91,
  speedRating: 'V',
  season: 'Summer',
  vehicleTypeCompatibility: [],
  treadLife: '40000 miles',
  tractionRating: 'A',
  temperatureRating: 'A',
  
  certifications: ['DOT', 'ECE'],
  shippingOrigin: 'Test Origin',
  estimatedLeadTime: '5 days',
  packagingDetails: 'Test packaging',
  inventoryUpdateDate: new Date(),
  status: 'active',
  createdAt: new Date(),
  updatedAt: new Date(),
};

/**
 * Test scenarios for wholesale pricing:
 * 
 * Scenario 1: Add 50 items (Tier 1)
 * - Expected: 50 × 200.00 = 10,000 DZD
 * 
 * Scenario 2: Increase to 100 items (crosses to Tier 2)
 * - Expected: 100 × 100.00 = 10,000 DZD (price should drop!)
 * 
 * Scenario 3: Increase to 150 items (still Tier 2)
 * - Expected: 150 × 100.00 = 15,000 DZD
 * 
 * Scenario 4: Increase to 200 items (crosses to Tier 3)
 * - Expected: 200 × 50.00 = 10,000 DZD (price should drop again!)
 */
