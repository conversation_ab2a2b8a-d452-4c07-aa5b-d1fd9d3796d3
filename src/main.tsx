
import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import './i18n/i18n'; // Import i18n configuration

// Ensure React is available globally for production
if (typeof window !== 'undefined') {
  (window as any).React = React;
}

// Initialize monitoring and analytics
import { monitoring, trackPageView } from './utils/monitoring';

// Track initial page load (security temporarily disabled for debugging)
try {
  trackPageView(window.location.pathname);
} catch (error) {
  console.warn('Monitoring initialization failed:', error);
}

createRoot(document.getElementById("root")!).render(<App />);
