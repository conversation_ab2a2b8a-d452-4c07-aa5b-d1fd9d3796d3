<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #fa7b00;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #e66a00;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🛒 AROUZ MARKET - Cart Persistence Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <div class="info test-result">
            <strong>How to test cart persistence:</strong><br>
            1. Open the marketplace at <a href="http://localhost:8081/my-vehicle-parts" target="_blank">http://localhost:8081/my-vehicle-parts</a><br>
            2. Add multiple different products to your cart<br>
            3. Refresh the page (F5 or Ctrl+R)<br>
            4. Check if all products maintain their correct images and titles<br>
            5. Use the buttons below to inspect localStorage data
        </div>
    </div>

    <div class="test-section">
        <h2>Cart Data Inspector</h2>
        <button onclick="inspectCartData()">🔍 Inspect Current Cart Data</button>
        <button onclick="clearCartData()">🗑️ Clear Cart Data</button>
        <button onclick="addTestData()">➕ Add Test Cart Items</button>
        
        <div id="cartDataDisplay"></div>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script>
        const CART_STORAGE_KEY = 'arouz_market_cart';

        function inspectCartData() {
            const cartData = localStorage.getItem(CART_STORAGE_KEY);
            const display = document.getElementById('cartDataDisplay');
            
            if (cartData) {
                try {
                    const parsed = JSON.parse(cartData);
                    display.innerHTML = `
                        <div class="success test-result">
                            <strong>✅ Cart data found in localStorage</strong><br>
                            Items count: ${parsed.length}<br>
                            <pre>${JSON.stringify(parsed, null, 2)}</pre>
                        </div>
                    `;
                    
                    // Validate data structure
                    validateCartData(parsed);
                } catch (error) {
                    display.innerHTML = `
                        <div class="error test-result">
                            <strong>❌ Error parsing cart data:</strong><br>
                            ${error.message}<br>
                            Raw data: <pre>${cartData}</pre>
                        </div>
                    `;
                }
            } else {
                display.innerHTML = `
                    <div class="info test-result">
                        <strong>ℹ️ No cart data found in localStorage</strong><br>
                        The cart is empty or hasn't been initialized yet.
                    </div>
                `;
            }
        }

        function validateCartData(cartItems) {
            const results = document.getElementById('testResults');
            let validationResults = [];

            cartItems.forEach((item, index) => {
                const requiredFields = ['id', 'name', 'price', 'quantity', 'image', 'category', 'originalProductId'];
                const missingFields = requiredFields.filter(field => !item.hasOwnProperty(field));
                
                if (missingFields.length === 0) {
                    validationResults.push(`
                        <div class="success test-result">
                            ✅ Item ${index + 1}: "${item.name}" - All required fields present
                        </div>
                    `);
                } else {
                    validationResults.push(`
                        <div class="error test-result">
                            ❌ Item ${index + 1}: "${item.name}" - Missing fields: ${missingFields.join(', ')}
                        </div>
                    `);
                }

                // Check for unique images
                if (item.image && item.image !== '/placeholder.svg' && item.image !== '/images/TyreParameters.png') {
                    validationResults.push(`
                        <div class="success test-result">
                            ✅ Item ${index + 1}: Has unique image - ${item.image}
                        </div>
                    `);
                } else {
                    validationResults.push(`
                        <div class="error test-result">
                            ⚠️ Item ${index + 1}: Using placeholder/generic image - ${item.image}
                        </div>
                    `);
                }
            });

            results.innerHTML = validationResults.join('');
        }

        function clearCartData() {
            localStorage.removeItem(CART_STORAGE_KEY);
            document.getElementById('cartDataDisplay').innerHTML = `
                <div class="success test-result">
                    ✅ Cart data cleared from localStorage
                </div>
            `;
            document.getElementById('testResults').innerHTML = '';
        }

        function addTestData() {
            const testItems = [
                {
                    id: 12345,
                    name: "Test Michelin Tyre 205/55R16",
                    price: 18500,
                    quantity: 2,
                    image: "/images/test-tyre.jpg",
                    category: "tyres",
                    subcategory: "summer",
                    manufacturer: "Michelin",
                    brand: "Michelin",
                    partArticleNumber: "MICH-205-55-16",
                    supplierName: "Premium Auto Parts",
                    originalProductId: "TYR-MICH-001"
                },
                {
                    id: 67890,
                    name: "Test Bosch Brake Pads Set",
                    price: 12000,
                    quantity: 1,
                    image: "/images/test-brake-pads.jpg",
                    category: "brake_parts",
                    subcategory: "brake_pads",
                    manufacturer: "Bosch",
                    brand: "Bosch",
                    partArticleNumber: "BOSCH-BP-001",
                    supplierName: "Brake Specialists",
                    originalProductId: "BRK-BOSCH-001"
                }
            ];

            localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(testItems));
            document.getElementById('cartDataDisplay').innerHTML = `
                <div class="success test-result">
                    ✅ Test cart data added to localStorage<br>
                    Refresh the marketplace page to see the test items in your cart.
                </div>
            `;
        }

        // Auto-inspect on page load
        window.onload = function() {
            inspectCartData();
        };
    </script>
</body>
</html>
