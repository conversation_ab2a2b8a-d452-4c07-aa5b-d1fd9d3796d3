/**
 * Utility functions for formatting values
 */

/**
 * Format a number as DZD currency
 * @param value The number to format
 * @param options Additional formatting options
 * @returns Formatted currency string
 */
export const formatDZD = (value: number | undefined, options?: {
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  compact?: boolean;
}): string => {
  if (value === undefined || value === null || isNaN(Number(value))) {
    return '-';
  }

  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 0,
    compact = false,
  } = options || {};

  return new Intl.NumberFormat('fr-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits,
    maximumFractionDigits,
    notation: compact ? 'compact' : 'standard',
  }).format(value);
};

/**
 * Format a number with thousand separators
 * @param value The number to format
 * @returns Formatted number string
 */
export const formatNumber = (value: number | undefined): string => {
  if (value === undefined || value === null || isNaN(Number(value))) {
    return '-';
  }

  return new Intl.NumberFormat('fr-DZ').format(value);
};
