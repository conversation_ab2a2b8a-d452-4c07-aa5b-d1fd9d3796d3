import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useState } from "react";

// Public Website Pages
import HomePage from "./pages/public/HomePage";
import AboutPage from "./pages/public/AboutPage";
import FeaturesPage from "./pages/public/FeaturesPage";
import RoadmapPage from "./pages/public/RoadmapPage";
import ContactPage from "./pages/public/ContactPage";

// Protected App Pages
import Index from "./pages/Index";
import ProductsPage from "./pages/ProductsPage";
import ProductsDataGridPage from "./pages/ProductsDataGridPage";
import ProductFormPage from "./pages/ProductFormPage";
import CategoryProductsPage from "./pages/CategoryProductsPage";
import NotFound from "./pages/NotFound";

// Components
import { ProtectedRoute } from "@/components/common/ProtectedRoute";
import { DevNavigation } from "@/components/common/DevNavigation";

const App = () => {
  // Create a client inside the component to ensure React context works correctly
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Public Website Routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/features" element={<FeaturesPage />} />
            <Route path="/roadmap" element={<RoadmapPage />} />
            <Route path="/contact" element={<ContactPage />} />

            {/* Protected App Routes - All redirected to home page during pre-launch */}
            <Route path="/app" element={<ProtectedRoute><Navigate to="/app/dashboard" /></ProtectedRoute>} />
            <Route path="/app/dashboard" element={<ProtectedRoute><Index /></ProtectedRoute>} />
            <Route path="/app/products" element={<ProtectedRoute><ProductsPage /></ProtectedRoute>} />
            <Route path="/app/products-table" element={<ProtectedRoute><ProductsDataGridPage /></ProtectedRoute>} />
            <Route path="/app/products/new" element={<ProtectedRoute><ProductFormPage /></ProtectedRoute>} />
            <Route path="/app/products/edit/:id" element={<ProtectedRoute><ProductFormPage /></ProtectedRoute>} />
            <Route path="/app/categories/:slug" element={<ProtectedRoute><CategoryProductsPage /></ProtectedRoute>} />

            {/* Legacy Routes - Redirect to new structure */}
            <Route path="/products" element={<Navigate to="/app/products" replace />} />
            <Route path="/products-table" element={<Navigate to="/app/products-table" replace />} />
            <Route path="/products/new" element={<Navigate to="/app/products/new" replace />} />
            <Route path="/products/edit/:id" element={<Navigate to="/app/products/edit/:id" replace />} />
            <Route path="/categories/:slug" element={<Navigate to="/app/categories/:slug" replace />} />

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>

          {/* Development Navigation - Only visible during development */}
          <DevNavigation />

        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
