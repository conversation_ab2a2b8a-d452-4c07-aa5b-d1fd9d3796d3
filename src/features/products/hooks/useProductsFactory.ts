/**
 * Products Hook Factory
 *
 * This factory returns the appropriate useProducts hook based on feature flags,
 * allowing seamless switching between localStorage and Supabase backends.
 */

import { isFeatureEnabled } from '@/config/features';

// Import both implementations
import { useProducts as useProductsLocalStorage } from './useProducts';
import { useProducts as useProductsSupabase } from './useProductsSupabase';

/**
 * Factory function that returns the appropriate useProducts hook
 * based on the current feature flag configuration
 */
export function createProductsHook() {
  // Check feature flag to determine which implementation to use
  const useSupabaseBackend = isFeatureEnabled('useSupabaseBackend');

  console.log('🔧 ProductsFactory - Feature flag useSupabaseBackend:', useSupabaseBackend);
  console.log('🔧 ProductsFactory - Environment VITE_USE_SUPABASE_BACKEND:', import.meta.env.VITE_USE_SUPABASE_BACKEND);

  if (useSupabaseBackend) {
    console.log('🔄 Using Supabase backend for products');
    return useProductsSupabase;
  } else {
    console.log('💾 Using localStorage backend for products');
    return useProductsLocalStorage;
  }
}

/**
 * Main useProducts hook that automatically selects the correct implementation
 * This is the hook that should be imported and used throughout the application
 */
export const useProducts = createProductsHook();

/**
 * Hook to get the current backend type being used
 */
export function useBackendType(): 'supabase' | 'localStorage' {
  return isFeatureEnabled('useSupabaseBackend') ? 'supabase' : 'localStorage';
}

/**
 * Hook to check if Supabase backend is enabled
 */
export function useIsSupabaseEnabled(): boolean {
  return isFeatureEnabled('useSupabaseBackend');
}

/**
 * Hook to check if localStorage backend is enabled
 */
export function useIsLocalStorageEnabled(): boolean {
  return isFeatureEnabled('useLocalStorage');
}

/**
 * Development helper to switch backends at runtime
 * Only available in development mode
 */
export function switchToSupabaseBackend(): void {
  if (import.meta.env.DEV) {
    const { enableFeature, disableFeature } = require('@/config/features');
    enableFeature('useSupabaseBackend');
    disableFeature('useLocalStorage');
    console.log('🔄 Switched to Supabase backend (reload required)');
  } else {
    console.warn('Backend switching is only available in development mode');
  }
}

/**
 * Development helper to switch to localStorage backend at runtime
 * Only available in development mode
 */
export function switchToLocalStorageBackend(): void {
  if (import.meta.env.DEV) {
    const { enableFeature, disableFeature } = require('@/config/features');
    disableFeature('useSupabaseBackend');
    enableFeature('useLocalStorage');
    console.log('💾 Switched to localStorage backend (reload required)');
  } else {
    console.warn('Backend switching is only available in development mode');
  }
}

// Export the specific implementations for direct use if needed
export { useProducts as useProductsLocalStorage } from './useProducts';
export { useProducts as useProductsSupabase } from './useProductsSupabase';
