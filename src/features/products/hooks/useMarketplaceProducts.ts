/**
 * Marketplace Products Hook
 * 
 * This hook fetches products from ALL users for marketplace display,
 * unlike useProducts which only fetches current user's products for admin panel.
 */

import { useQuery } from '@tanstack/react-query';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { fetchMarketplaceProductsByCategory } from '@/services/productService';

export const useMarketplaceProducts = (categoryId: string) => {
  // Fetch marketplace products using React Query
  const {
    data: products = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<(TyreProduct | BrakeProduct)[]>({
    queryKey: ['marketplace-products', categoryId],
    queryFn: () => fetchMarketplaceProductsByCategory(categoryId),
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter than admin products)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    products,
    isLoading,
    isError,
    error,
    refetch,
  };
};
