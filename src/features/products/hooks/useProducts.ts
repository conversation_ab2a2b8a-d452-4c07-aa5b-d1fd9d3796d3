import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { generateMockTyreProducts } from '../utils/mockData';
import { generateMockBrakeProducts } from '../utils/mockBrakeData';
import { generateProductId } from '../utils/idGenerator';
import { useUser } from '@/contexts/UserContext';

// In a real application, this would be an API call
const fetchProducts = async (categoryId: string, userRole: string): Promise<(TyreProduct | BrakeProduct)[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));

  let products: (TyreProduct | BrakeProduct)[] = [];

  // Handle unified "all-other-categories" table
  if (categoryId === 'all-other-categories') {
    // Fetch products from all categories except tyres
    const categoriesToFetch = ['brakes']; // Add more categories here as they are implemented

    for (const category of categoriesToFetch) {
      const storageKey = `products-${category}`;
      const storedData = localStorage.getItem(storageKey);

      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);
          const categoryProducts = parsedData.map((product: any) => ({
            ...product,
            createdAt: new Date(product.createdAt),
            updatedAt: new Date(product.updatedAt),
            inventoryUpdateDate: product.inventoryUpdateDate ? new Date(product.inventoryUpdateDate) : undefined
          }));
          products.push(...categoryProducts);
        } catch (error) {
          console.error(`Error parsing stored product data for ${category}:`, error);
        }
      } else {
        // Generate mock data for categories that don't have stored data
        if (category === 'brakes') {
          const mockProducts = generateMockBrakeProducts(100);
          products.push(...mockProducts);

          // Store the generated data
          try {
            localStorage.setItem(storageKey, JSON.stringify(mockProducts));
          } catch (error) {
            console.error(`Error storing mock data for ${category}:`, error);
          }
        }
      }
    }
  } else {
    // Handle individual category fetching (tyres, or legacy brakes access)
    const storageKey = `products-${categoryId}`;
    const storedData = localStorage.getItem(storageKey);

    if (storedData) {
      try {
        // Parse the stored data
        const parsedData = JSON.parse(storedData);

        // Convert string dates back to Date objects
        products = parsedData.map((product: any) => ({
          ...product,
          createdAt: new Date(product.createdAt),
          updatedAt: new Date(product.updatedAt),
          inventoryUpdateDate: product.inventoryUpdateDate ? new Date(product.inventoryUpdateDate) : undefined
        }));

        console.log(`Using stored product data from localStorage for ${categoryId}`);
      } catch (error) {
        console.error('Error parsing stored product data:', error);
        // If there's an error parsing the stored data, fall back to generating new data
        products = [];
      }
    }

    // If no valid data in localStorage, generate mock data based on category
    if (products.length === 0) {
      if (categoryId === 'tyres') {
        products = generateMockTyreProducts(100); // Reduced to 100 for better performance
      } else if (categoryId === 'brakes') {
        products = generateMockBrakeProducts(100); // 100 brake products
      }

      // Store the generated data in localStorage for future use
      try {
        localStorage.setItem(storageKey, JSON.stringify(products));
      } catch (error) {
        console.error('Error storing product data in localStorage:', error);
      }
    }
  }

  // Apply role-specific data transformations
  products = products.map(product => {
    // Set marketplaceSection based on user role
    if (userRole === 'supplier') {
      return {
        ...product,
        marketplaceSection: 'wholesale',
        // Ensure wholesalePricingTiers exists for suppliers
        wholesalePricingTiers: product.wholesalePricingTiers || [
          { minQuantity: 1, maxQuantity: 10, price: product.retailPrice || 100 },
          { minQuantity: 11, maxQuantity: 50, price: (product.retailPrice || 100) * 0.9 },
          { minQuantity: 51, price: (product.retailPrice || 100) * 0.8 }
        ]
      };
    } else if (userRole === 'merchant') {
      return {
        ...product,
        marketplaceSection: 'retail',
        // Ensure retailPrice exists for merchants
        retailPrice: product.retailPrice ||
          (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0
            ? product.wholesalePricingTiers[0].price * 1.3 // 30% markup
            : 100)
      };
    }

    // Default case - return product as is
    return product;
  });

  return products;
};

export const useProducts = (categoryId: string) => {
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const queryClient = useQueryClient();
  const { userRole } = useUser();

  // Fetch products using React Query
  const {
    data: products = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<(TyreProduct | BrakeProduct)[]>({
    queryKey: ['products', categoryId, userRole], // Include userRole in the query key
    queryFn: () => fetchProducts(categoryId, userRole),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Select/deselect products
  const selectProduct = useCallback((productId: string) => {
    setSelectedProductIds(prev => [...prev, productId]);
  }, []);

  const deselectProduct = useCallback((productId: string) => {
    setSelectedProductIds(prev => prev.filter(id => id !== productId));
  }, []);

  const toggleProductSelection = useCallback((productId: string) => {
    setSelectedProductIds(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  }, []);

  const selectAllProducts = useCallback(() => {
    setSelectedProductIds(products.map(p => p.id));
  }, [products]);

  const deselectAllProducts = useCallback(() => {
    setSelectedProductIds([]);
  }, []);

  // Reset selection when category changes
  useEffect(() => {
    setSelectedProductIds([]);
  }, [categoryId]);

  // Update a product in the cache and localStorage
  const updateProduct = useCallback((productId: string, updates: Partial<TyreProduct | BrakeProduct>) => {
    // Update the React Query cache for the current view
    queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
      const updatedData = oldData.map(product => {
        if (product.id === productId) {
          const updatedProduct = { ...product, ...updates, updatedAt: new Date() };

          // For unified table, also update the product in its actual category's localStorage
          if (categoryId === 'all-other-categories') {
            try {
              const actualCategory = updatedProduct.category;
              const actualStorageKey = `products-${actualCategory}`;
              const actualStoredData = localStorage.getItem(actualStorageKey);

              if (actualStoredData) {
                const actualProducts = JSON.parse(actualStoredData);
                const actualUpdatedData = actualProducts.map((p: any) =>
                  p.id === productId ? updatedProduct : p
                );
                localStorage.setItem(actualStorageKey, JSON.stringify(actualUpdatedData));
              }
            } catch (error) {
              console.error('Error updating product in actual category localStorage:', error);
            }
          } else {
            // For single category tables, update localStorage normally
            try {
              const storageKey = `products-${categoryId}`;
              const storedData = localStorage.getItem(storageKey);
              if (storedData) {
                const products = JSON.parse(storedData);
                const updatedProducts = products.map((p: any) =>
                  p.id === productId ? updatedProduct : p
                );
                localStorage.setItem(storageKey, JSON.stringify(updatedProducts));
              }
            } catch (error) {
              console.error('Error updating product data in localStorage:', error);
            }
          }

          return updatedProduct;
        }
        return product;
      });

      return updatedData;
    });
  }, [queryClient, categoryId, userRole]);

  // Add a new product
  const addProduct = useCallback(async (product: Partial<TyreProduct | BrakeProduct>) => {
    // Determine which category to save the product to
    const productCategory = product.category || (categoryId === 'all-other-categories' ? 'brakes' : categoryId);

    // Add role-specific fields
    let roleSpecificFields = {};

    if (userRole === 'supplier') {
      roleSpecificFields = {
        marketplaceSection: 'wholesale',
        // Ensure wholesalePricingTiers exists for suppliers
        wholesalePricingTiers: product.wholesalePricingTiers || [
          { minQuantity: 1, maxQuantity: 10, price: product.retailPrice || 100 },
          { minQuantity: 11, maxQuantity: 50, price: (product.retailPrice || 100) * 0.9 },
          { minQuantity: 51, price: (product.retailPrice || 100) * 0.8 }
        ]
      };
    } else if (userRole === 'merchant') {
      roleSpecificFields = {
        marketplaceSection: 'retail',
        // Ensure retailPrice exists for merchants
        retailPrice: product.retailPrice ||
          (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0
            ? product.wholesalePricingTiers[0].price * 1.3 // 30% markup
            : 100)
      };
    }

    // Determine marketplace section based on user role
    const marketplaceSection = userRole === 'supplier' ? 'wholesale' : 'retail';

    // Generate a unique ID if not provided, using the category-specific format and marketplace section
    const newProduct = {
      ...product,
      ...roleSpecificFields,
      id: product.id || generateProductId(productCategory, marketplaceSection),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log(`Adding product to category: ${productCategory} (via ${categoryId} table)`, newProduct);

    // Handle unified table: always save to the actual product category's localStorage
    const targetStorageKey = `products-${productCategory}`;

    try {
      const existingDataStr = localStorage.getItem(targetStorageKey);
      let existingData: (TyreProduct | BrakeProduct)[] = [];

      if (existingDataStr) {
        existingData = JSON.parse(existingDataStr);
        // Convert string dates back to Date objects
        existingData = existingData.map((item: any) => ({
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: new Date(item.updatedAt),
          inventoryUpdateDate: item.inventoryUpdateDate ? new Date(item.inventoryUpdateDate) : undefined
        }));
      }

      const updatedData = [...existingData, newProduct as TyreProduct | BrakeProduct];
      localStorage.setItem(targetStorageKey, JSON.stringify(updatedData));

      // Update the React Query cache for the current view (unified table or specific category)
      queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
        return [...oldData, newProduct as TyreProduct | BrakeProduct];
      });

      // If we're in a unified table and the product was saved to a different category,
      // also invalidate that category's cache
      if (categoryId === 'all-other-categories' && productCategory !== categoryId) {
        queryClient.invalidateQueries({ queryKey: ['products', productCategory, userRole] });
      }

    } catch (error) {
      console.error(`Error adding product to category ${productCategory}:`, error);
    }

    return newProduct;
  }, [queryClient, categoryId, userRole]);

  // Delete a product
  const deleteProduct = useCallback(async (productId: string) => {
    // Find the product to get its actual category
    const productToDelete = products.find(p => p.id === productId);

    // Update the React Query cache for the current view
    queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
      const updatedData = oldData.filter(product => product.id !== productId);

      // For unified table, also delete from the product's actual category localStorage
      if (categoryId === 'all-other-categories' && productToDelete) {
        try {
          const actualCategory = productToDelete.category;
          const actualStorageKey = `products-${actualCategory}`;
          const actualStoredData = localStorage.getItem(actualStorageKey);

          if (actualStoredData) {
            const actualProducts = JSON.parse(actualStoredData);
            const actualUpdatedData = actualProducts.filter((p: any) => p.id !== productId);
            localStorage.setItem(actualStorageKey, JSON.stringify(actualUpdatedData));
          }
        } catch (error) {
          console.error('Error deleting product from actual category localStorage:', error);
        }
      } else {
        // For single category tables, update localStorage normally
        try {
          const storageKey = `products-${categoryId}`;
          localStorage.setItem(storageKey, JSON.stringify(updatedData));
        } catch (error) {
          console.error('Error updating product data in localStorage after deletion:', error);
        }
      }

      return updatedData;
    });

    // Remove from selected products if it was selected
    setSelectedProductIds(prev => prev.filter(id => id !== productId));
  }, [queryClient, categoryId, userRole, products]);

  // Delete multiple products
  const deleteProducts = useCallback(async (productIds: string[]) => {
    // Group products by their actual categories for unified table
    const productsToDelete = products.filter(p => productIds.includes(p.id));
    const productsByCategory: Record<string, string[]> = {};

    productsToDelete.forEach(product => {
      const actualCategory = product.category;
      if (!productsByCategory[actualCategory]) {
        productsByCategory[actualCategory] = [];
      }
      productsByCategory[actualCategory].push(product.id);
    });

    // Update the React Query cache for the current view
    queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
      const updatedData = oldData.filter(product => !productIds.includes(product.id));

      // For unified table, also delete from each product's actual category localStorage
      if (categoryId === 'all-other-categories') {
        Object.entries(productsByCategory).forEach(([actualCategory, ids]) => {
          try {
            const actualStorageKey = `products-${actualCategory}`;
            const actualStoredData = localStorage.getItem(actualStorageKey);

            if (actualStoredData) {
              const actualProducts = JSON.parse(actualStoredData);
              const actualUpdatedData = actualProducts.filter((p: any) => !ids.includes(p.id));
              localStorage.setItem(actualStorageKey, JSON.stringify(actualUpdatedData));
            }
          } catch (error) {
            console.error(`Error deleting products from ${actualCategory} localStorage:`, error);
          }
        });
      } else {
        // For single category tables, update localStorage normally
        try {
          const storageKey = `products-${categoryId}`;
          localStorage.setItem(storageKey, JSON.stringify(updatedData));
        } catch (error) {
          console.error('Error updating product data in localStorage after bulk deletion:', error);
        }
      }

      return updatedData;
    });

    // Remove from selected products
    setSelectedProductIds(prev => prev.filter(id => !productIds.includes(id)));
  }, [queryClient, categoryId, userRole, products]);

  return {
    products,
    isLoading,
    isError,
    error,
    refetch,
    selectedProductIds,
    setSelectedProductIds,
    selectProduct,
    deselectProduct,
    toggleProductSelection,
    selectAllProducts,
    deselectAllProducts,
    updateProduct,
    addProduct,
    deleteProduct,
    deleteProducts,
  };
};
