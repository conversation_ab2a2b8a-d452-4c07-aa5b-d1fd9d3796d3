/**
 * Enhanced useProducts hook with Supabase backend integration
 *
 * This hook replaces the localStorage-based useProducts hook with full Supabase integration
 * while maintaining identical API patterns for seamless frontend compatibility.
 */

import { useState, useCallback } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { useUser } from '@/contexts/UserContext';
import {
  fetchProductsByCategory,
  createProduct,
  updateProduct,
  deleteProduct,
  deleteProducts,
  updateProductStatus,
  getProductById,
  AnyProduct
} from '@/services/productService';
import { toast } from 'sonner';

export const useProducts = (categoryId: string) => {
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const queryClient = useQueryClient();
  const { userRole } = useUser();

  // Fetch products using React Query with Supabase backend
  const {
    data: products = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<(TyreProduct | BrakeProduct)[]>({
    queryKey: ['products', categoryId, userRole],
    queryFn: () => fetchProductsByCategory(categoryId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Select/deselect products
  const selectProduct = useCallback((productId: string) => {
    setSelectedProductIds(prev => [...prev, productId]);
  }, []);

  const deselectProduct = useCallback((productId: string) => {
    setSelectedProductIds(prev => prev.filter(id => id !== productId));
  }, []);

  const toggleProductSelection = useCallback((productId: string) => {
    setSelectedProductIds(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  }, []);

  const selectAllProducts = useCallback(() => {
    setSelectedProductIds(products.map(product => product.id));
  }, [products]);

  const deselectAllProducts = useCallback(() => {
    setSelectedProductIds([]);
  }, []);

  // Update product mutation
  const updateProductMutation = useMutation({
    mutationFn: ({ productId, updates }: { productId: string; updates: Partial<TyreProduct | BrakeProduct> }) =>
      updateProduct(productId, updates),
    onSuccess: (updatedProduct) => {
      // Update the React Query cache
      queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
        return oldData.map(product =>
          product.id === updatedProduct.id ? updatedProduct : product
        );
      });

      // Also update the single product cache if it exists
      queryClient.setQueryData(['product', updatedProduct.id], updatedProduct);

      // Invalidate marketplace queries to ensure real-time updates
      queryClient.invalidateQueries({ queryKey: ['products'] });

      toast.success('Product updated successfully');
    },
    onError: (error) => {
      console.error('Error updating product:', error);
      toast.error('Failed to update product');
    }
  });

  // Create product mutation
  const createProductMutation = useMutation({
    mutationFn: (product: Partial<TyreProduct | BrakeProduct>) => createProduct(product),
    onSuccess: (newProduct) => {
      // Update the React Query cache
      queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
        return [newProduct, ...oldData];
      });

      // If the product belongs to a different category, invalidate that category's cache
      if (newProduct.category !== categoryId) {
        queryClient.invalidateQueries(['products', newProduct.category, userRole]);
      }

      // Invalidate marketplace queries to ensure real-time updates
      queryClient.invalidateQueries({ queryKey: ['products'] });

      toast.success('Product created successfully');
    },
    onError: (error) => {
      console.error('Error creating product:', error);
      toast.error('Failed to create product');
    }
  });

  // Delete product mutation
  const deleteProductMutation = useMutation({
    mutationFn: (productId: string) => deleteProduct(productId),
    onSuccess: (_, productId) => {
      // Update the React Query cache
      queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
        return oldData.filter(product => product.id !== productId);
      });

      // Remove from selected products
      setSelectedProductIds(prev => prev.filter(id => id !== productId));

      // Remove from single product cache
      queryClient.removeQueries(['product', productId]);

      toast.success('Product deleted successfully');
    },
    onError: (error) => {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  });

  // Delete multiple products mutation
  const deleteProductsMutation = useMutation({
    mutationFn: (productIds: string[]) => deleteProducts(productIds),
    onSuccess: (_, productIds) => {
      // Update the React Query cache
      queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
        return oldData.filter(product => !productIds.includes(product.id));
      });

      // Remove from selected products
      setSelectedProductIds(prev => prev.filter(id => !productIds.includes(id)));

      // Remove from single product caches
      productIds.forEach(id => {
        queryClient.removeQueries(['product', id]);
      });

      toast.success(`${productIds.length} products deleted successfully`);
    },
    onError: (error) => {
      console.error('Error deleting products:', error);
      toast.error('Failed to delete products');
    }
  });

  // Update product status mutation
  const updateProductStatusMutation = useMutation({
    mutationFn: ({ productIds, status }: { productIds: string[]; status: string }) =>
      updateProductStatus(productIds, status),
    onSuccess: (_, { productIds, status }) => {
      // Update the React Query cache
      queryClient.setQueryData(['products', categoryId, userRole], (oldData: (TyreProduct | BrakeProduct)[] = []) => {
        return oldData.map(product =>
          productIds.includes(product.id)
            ? { ...product, status: status as any, updatedAt: new Date() }
            : product
        );
      });

      toast.success(`Product status updated to ${status}`);
    },
    onError: (error) => {
      console.error('Error updating product status:', error);
      toast.error('Failed to update product status');
    }
  });

  // Wrapper functions to maintain API compatibility
  const updateProductWrapper = useCallback((productId: string, updates: Partial<TyreProduct | BrakeProduct>) => {
    updateProductMutation.mutate({ productId, updates });
  }, [updateProductMutation]);

  const addProduct = useCallback(async (product: Partial<TyreProduct | BrakeProduct>) => {
    // Generate ID if not provided, handling unified table category mapping
    if (!product.id) {
      const { generateUniqueProductId } = await import('@/features/products/utils/idGenerator');

      // Determine the actual category for ID generation
      let actualCategory = product.category;
      if (!actualCategory) {
        // If no category specified and we're in unified table, we need to determine from form data
        // This should be handled by the form components, but as fallback use brakes
        if (categoryId === 'all-other-categories') {
          console.warn('No category specified for unified table product, defaulting to brakes');
          actualCategory = 'brakes';
        } else {
          actualCategory = categoryId;
        }
      }

      // Validate that the category is supported
      const { categoryExists } = await import('@/data/categories');
      if (!categoryExists(actualCategory)) {
        console.warn(`Invalid category: ${actualCategory}, defaulting to brakes`);
        actualCategory = 'brakes';
      }

      // Determine marketplace section based on user role
      const marketplaceSection = userRole === 'supplier' ? 'wholesale' : 'retail';

      // Generate unique ID with proper category prefix
      const productId = await generateUniqueProductId(actualCategory, marketplaceSection);

      product = {
        ...product,
        id: productId,
        category: actualCategory, // Ensure category is set correctly
      };
    }

    return createProductMutation.mutateAsync(product);
  }, [createProductMutation, categoryId, userRole]);

  const deleteProductWrapper = useCallback(async (productId: string) => {
    return deleteProductMutation.mutateAsync(productId);
  }, [deleteProductMutation]);

  const deleteProductsWrapper = useCallback(async (productIds: string[]) => {
    return deleteProductsMutation.mutateAsync(productIds);
  }, [deleteProductsMutation]);

  const updateStatusWrapper = useCallback(async (productIds: string[], status: string) => {
    return updateProductStatusMutation.mutateAsync({ productIds, status });
  }, [updateProductStatusMutation]);

  // Get single product by ID (for admin/owner access)
  const getProduct = useCallback(async (productId: string): Promise<AnyProduct | null> => {
    // Try to get from cache first
    const cachedProduct = queryClient.getQueryData(['product', productId]);
    if (cachedProduct) {
      return cachedProduct as AnyProduct;
    }

    // Fetch from backend with user restriction for admin panels
    try {
      const { getProductByIdForOwner } = await import('@/services/productService');
      const product = await getProductByIdForOwner(productId);
      if (product) {
        // Cache the result
        queryClient.setQueryData(['product', productId], product);
      }
      return product;
    } catch (error) {
      console.error('Error fetching product by ID for owner:', error);
      return null;
    }
  }, [queryClient]);

  return {
    // Data
    products,
    isLoading: isLoading || createProductMutation.isPending || updateProductMutation.isPending ||
               deleteProductMutation.isPending || deleteProductsMutation.isPending ||
               updateProductStatusMutation.isPending,
    isError,
    error,

    // Actions
    refetch,

    // Selection
    selectedProductIds,
    setSelectedProductIds,
    selectProduct,
    deselectProduct,
    toggleProductSelection,
    selectAllProducts,
    deselectAllProducts,

    // CRUD operations (maintaining original API)
    updateProduct: updateProductWrapper,
    addProduct,
    deleteProduct: deleteProductWrapper,
    deleteProducts: deleteProductsWrapper,
    updateProductStatus: updateStatusWrapper,
    getProduct,

    // Mutation states for advanced UI feedback
    isCreating: createProductMutation.isPending,
    isUpdating: updateProductMutation.isPending,
    isDeleting: deleteProductMutation.isPending || deleteProductsMutation.isPending,
    isUpdatingStatus: updateProductStatusMutation.isPending,
  };
};
