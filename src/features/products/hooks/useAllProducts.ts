import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { TyreProduct, BrakeProduct, BaseProduct, ProductStatus } from '../types/product.types';
import { toast } from 'sonner';

// Define a type for all product types
export type AnyProduct = TyreProduct | BrakeProduct;

// Define filter types
export interface ProductFilters {
  search?: string;
  category?: string;
  status?: ProductStatus | 'all';
  priceRange?: [number, number] | null;
  manufacturer?: string;
  inStock?: boolean;
}

// Fetch products from all categories
const fetchAllProducts = async (): Promise<AnyProduct[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));

  // Get all category keys from localStorage
  const allProducts: AnyProduct[] = [];
  
  // Known categories to fetch
  const categories = ['tyres', 'brakes']; // Add more categories as they become available
  
  // Fetch products from each category
  for (const category of categories) {
    const storageKey = `products-${category}`;
    const storedData = localStorage.getItem(storageKey);
    
    if (storedData) {
      try {
        const categoryProducts = JSON.parse(storedData);
        allProducts.push(...categoryProducts);
      } catch (error) {
        console.error(`Error parsing ${category} products:`, error);
      }
    }
  }
  
  return allProducts;
};

export const useAllProducts = (initialFilters: ProductFilters = {}) => {
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const [filters, setFilters] = useState<ProductFilters>(initialFilters);
  const queryClient = useQueryClient();

  // Fetch all products using React Query
  const {
    data: allProducts = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<AnyProduct[]>({
    queryKey: ['all-products'],
    queryFn: fetchAllProducts,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Apply filters to products
  const filteredProducts = useCallback(() => {
    return allProducts.filter(product => {
      // Search filter (check name, description, SKU, barcode)
      if (filters.search && filters.search.trim() !== '') {
        const searchTerm = filters.search.toLowerCase();
        const searchFields = [
          product.name,
          product.description,
          product.sku,
          product.barcode,
          product.manufacturer,
          product.supplierName
        ].filter(Boolean).map(field => field?.toLowerCase());
        
        if (!searchFields.some(field => field?.includes(searchTerm))) {
          return false;
        }
      }
      
      // Category filter
      if (filters.category && filters.category !== 'all') {
        if (product.category !== filters.category) {
          return false;
        }
      }
      
      // Status filter
      if (filters.status && filters.status !== 'all') {
        if (product.status !== filters.status) {
          return false;
        }
      }
      
      // Price range filter
      if (filters.priceRange) {
        const [min, max] = filters.priceRange;
        const price = product.retailPrice || 0;
        if (price < min || price > max) {
          return false;
        }
      }
      
      // Manufacturer filter
      if (filters.manufacturer && filters.manufacturer !== 'all') {
        if (product.manufacturer !== filters.manufacturer) {
          return false;
        }
      }
      
      // In stock filter
      if (filters.inStock !== undefined) {
        if (filters.inStock && product.stockQuantity <= 0) {
          return false;
        }
      }
      
      return true;
    });
  }, [allProducts, filters]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<ProductFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Select/deselect products
  const toggleProductSelection = useCallback((productId: string) => {
    setSelectedProductIds(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  }, []);

  // Select all products
  const selectAllProducts = useCallback(() => {
    const productIds = filteredProducts().map(product => product.id);
    setSelectedProductIds(productIds);
  }, [filteredProducts]);

  // Deselect all products
  const deselectAllProducts = useCallback(() => {
    setSelectedProductIds([]);
  }, []);

  // Delete a product
  const deleteProduct = useCallback((productId: string) => {
    // Find the product to determine its category
    const product = allProducts.find(p => p.id === productId);
    if (!product) return;
    
    const category = product.category;
    const storageKey = `products-${category}`;
    
    // Get current products for this category
    const storedData = localStorage.getItem(storageKey);
    if (!storedData) return;
    
    try {
      const categoryProducts = JSON.parse(storedData);
      const updatedProducts = categoryProducts.filter((p: AnyProduct) => p.id !== productId);
      
      // Update localStorage
      localStorage.setItem(storageKey, JSON.stringify(updatedProducts));
      
      // Update React Query cache
      queryClient.setQueryData(['products', category], updatedProducts);
      
      // Also update the all-products cache
      queryClient.setQueryData(['all-products'], (oldData: AnyProduct[] = []) => {
        return oldData.filter(p => p.id !== productId);
      });
      
      // Remove from selection if selected
      setSelectedProductIds(prev => prev.filter(id => id !== productId));
      
      toast.success('Product deleted successfully');
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  }, [allProducts, queryClient]);

  // Delete multiple products
  const deleteProducts = useCallback((productIds: string[]) => {
    // Group products by category
    const productsByCategory: Record<string, string[]> = {};
    
    productIds.forEach(id => {
      const product = allProducts.find(p => p.id === id);
      if (!product) return;
      
      const category = product.category;
      if (!productsByCategory[category]) {
        productsByCategory[category] = [];
      }
      productsByCategory[category].push(id);
    });
    
    // Delete products from each category
    Object.entries(productsByCategory).forEach(([category, ids]) => {
      const storageKey = `products-${category}`;
      const storedData = localStorage.getItem(storageKey);
      if (!storedData) return;
      
      try {
        const categoryProducts = JSON.parse(storedData);
        const updatedProducts = categoryProducts.filter((p: AnyProduct) => !ids.includes(p.id));
        
        // Update localStorage
        localStorage.setItem(storageKey, JSON.stringify(updatedProducts));
        
        // Update React Query cache
        queryClient.setQueryData(['products', category], updatedProducts);
      } catch (error) {
        console.error(`Error deleting products from ${category}:`, error);
      }
    });
    
    // Update all-products cache
    queryClient.setQueryData(['all-products'], (oldData: AnyProduct[] = []) => {
      return oldData.filter(p => !productIds.includes(p.id));
    });
    
    // Clear selection
    setSelectedProductIds([]);
    
    toast.success(`${productIds.length} products deleted successfully`);
  }, [allProducts, queryClient]);

  // Update product status in bulk
  const updateProductsStatus = useCallback((productIds: string[], status: ProductStatus) => {
    // Group products by category
    const productsByCategory: Record<string, string[]> = {};
    
    productIds.forEach(id => {
      const product = allProducts.find(p => p.id === id);
      if (!product) return;
      
      const category = product.category;
      if (!productsByCategory[category]) {
        productsByCategory[category] = [];
      }
      productsByCategory[category].push(id);
    });
    
    // Update products in each category
    Object.entries(productsByCategory).forEach(([category, ids]) => {
      const storageKey = `products-${category}`;
      const storedData = localStorage.getItem(storageKey);
      if (!storedData) return;
      
      try {
        const categoryProducts = JSON.parse(storedData);
        const updatedProducts = categoryProducts.map((p: AnyProduct) => {
          if (ids.includes(p.id)) {
            return { ...p, status, updatedAt: new Date() };
          }
          return p;
        });
        
        // Update localStorage
        localStorage.setItem(storageKey, JSON.stringify(updatedProducts));
        
        // Update React Query cache
        queryClient.setQueryData(['products', category], updatedProducts);
      } catch (error) {
        console.error(`Error updating products in ${category}:`, error);
      }
    });
    
    // Update all-products cache
    queryClient.setQueryData(['all-products'], (oldData: AnyProduct[] = []) => {
      return oldData.map(p => {
        if (productIds.includes(p.id)) {
          return { ...p, status, updatedAt: new Date() };
        }
        return p;
      });
    });
    
    toast.success(`Status updated for ${productIds.length} products`);
  }, [allProducts, queryClient]);

  return {
    products: filteredProducts(),
    allProducts,
    isLoading,
    isError,
    error,
    refetch,
    filters,
    updateFilters,
    resetFilters,
    selectedProductIds,
    toggleProductSelection,
    selectAllProducts,
    deselectAllProducts,
    deleteProduct,
    deleteProducts,
    updateProductsStatus,
  };
};
