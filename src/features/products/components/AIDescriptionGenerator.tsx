import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Loader2, Search, Wand2, Image as ImageIcon, AlertCircle, Info } from 'lucide-react';
import { toast } from 'sonner';

interface AIDescriptionGeneratorProps {
  onDescriptionGenerated: (description: string) => void;
  initialDescription?: string;
}

export const AIDescriptionGenerator: React.FC<AIDescriptionGeneratorProps> = ({
  onDescriptionGenerated,
  initialDescription = '',
}) => {
  const [activeTab, setActiveTab] = useState<string>('partNumber');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [partNumber, setPartNumber] = useState<string>('');
  const [oemNumber, setOemNumber] = useState<string>('');
  const [eanCode, setEanCode] = useState<string>('');
  const [description, setDescription] = useState<string>(initialDescription);
  const [generatedDescription, setGeneratedDescription] = useState<string>('');
  const [error, setError] = useState<string>('');

  // Mock function to simulate AI-generated description
  const generateDescriptionFromPartNumber = async () => {
    setIsGenerating(true);
    setError('');
    
    try {
      // In a real implementation, this would be an API call to an AI service
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API delay
      
      // Mock response based on the input
      if (partNumber || oemNumber || eanCode) {
        const inputType = activeTab === 'partNumber' ? 'part number' : 
                         activeTab === 'oemNumber' ? 'OEM number' : 'EAN code';
        const inputValue = activeTab === 'partNumber' ? partNumber : 
                          activeTab === 'oemNumber' ? oemNumber : eanCode;
        
        // Generate a structured description based on the input type
        const generatedText = `
## Product Identification
- ${inputType.toUpperCase()}: ${inputValue}
- Brand: Brembo
- Component Type: Brake Disc
- Function: Front Axle

## Technical Specifications
- Diameter: 305mm
- Thickness: 28mm
- Minimum Thickness: 26mm
- Number of Holes: 5
- Hole Circle Ø: 120mm
- Centering Diameter: 67mm
- Surface: Coated
- Brake Disc Type: Internally Vented, Two-Piece
- Height: 45.5mm

## Vehicle Compatibility
- BMW 3 Series (E90/E91/E92/E93) 2005-2011
- BMW 5 Series (E60/E61) 2003-2010
- BMW X1 (E84) 2009-2015

## Additional Information
- High-carbon cast iron construction for improved thermal stability
- UV-coated for corrosion resistance
- Precision balanced for smooth operation
- Meets or exceeds OE specifications
`;
        
        setGeneratedDescription(generatedText);
        return generatedText;
      } else {
        throw new Error('Please enter a valid identification number');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate description');
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  // Mock function to simulate AI-generated description from image
  const generateDescriptionFromImage = async (file: File) => {
    setIsGenerating(true);
    setError('');
    
    try {
      // In a real implementation, this would be an API call to an AI service
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate API delay
      
      // Mock response for image-based generation
      const generatedText = `
## Product Identification
- Part Number: 09.B085.13
- Brand: Brembo
- Component Type: Brake Disc
- Function: Front Axle

## Technical Specifications
- Diameter: 305mm
- Thickness: 28mm
- Minimum Thickness: 26mm
- Number of Holes: 5
- Hole Circle Ø: 120mm
- Centering Diameter: 67mm
- Surface: Coated
- Brake Disc Type: Internally Vented, Two-Piece
- Height: 45.5mm

## Vehicle Compatibility
- BMW 3 Series (E90/E91/E92/E93) 2005-2011
- BMW 5 Series (E60/E61) 2003-2010
- BMW X1 (E84) 2009-2015

## Additional Information
- High-carbon cast iron construction for improved thermal stability
- UV-coated for corrosion resistance
- Precision balanced for smooth operation
- Meets or exceeds OE specifications
`;
      
      setGeneratedDescription(generatedText);
      return generatedText;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate description from image');
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      generateDescriptionFromImage(file);
    }
  };

  const handleGenerate = async () => {
    const result = await generateDescriptionFromPartNumber();
    if (result) {
      toast.success('Description generated successfully');
    }
  };

  const handleApply = () => {
    onDescriptionGenerated(generatedDescription);
    setDescription(generatedDescription);
    toast.success('Description applied successfully');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <Wand2 className="h-5 w-5 text-primary" />
          AI Description Generator
        </CardTitle>
        <CardDescription>
          Generate a structured product description using AI technology
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="partNumber">Part Number</TabsTrigger>
            <TabsTrigger value="oemNumber">OEM/OE Number</TabsTrigger>
            <TabsTrigger value="eanCode">EAN/UPC/GTIN</TabsTrigger>
          </TabsList>
          
          <TabsContent value="partNumber" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="partNumber">Manufacturer Part Number (MPN)</Label>
              <div className="flex gap-2">
                <Input
                  id="partNumber"
                  value={partNumber}
                  onChange={(e) => setPartNumber(e.target.value)}
                  placeholder="e.g., 09.B085.13"
                  className="flex-1"
                />
                <Button 
                  onClick={handleGenerate} 
                  disabled={isGenerating || !partNumber}
                  className="gap-2"
                >
                  {isGenerating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  Search
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Enter the manufacturer's part number to find detailed specifications
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="oemNumber" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="oemNumber">Original Equipment Number</Label>
              <div className="flex gap-2">
                <Input
                  id="oemNumber"
                  value={oemNumber}
                  onChange={(e) => setOemNumber(e.target.value)}
                  placeholder="e.g., 34116797602"
                  className="flex-1"
                />
                <Button 
                  onClick={handleGenerate} 
                  disabled={isGenerating || !oemNumber}
                  className="gap-2"
                >
                  {isGenerating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  Search
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Enter the OEM or OE number to find detailed specifications
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="eanCode" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="eanCode">EAN / UPC / GTIN Code</Label>
              <div className="flex gap-2">
                <Input
                  id="eanCode"
                  value={eanCode}
                  onChange={(e) => setEanCode(e.target.value)}
                  placeholder="e.g., 8020584039137"
                  className="flex-1"
                />
                <Button 
                  onClick={handleGenerate} 
                  disabled={isGenerating || !eanCode}
                  className="gap-2"
                >
                  {isGenerating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  Search
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Enter the EAN, UPC, or GTIN barcode to find detailed specifications
              </p>
            </div>
          </TabsContent>
        </Tabs>
        
        <Separator className="my-6" />
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Image Recognition</h3>
            <div>
              <Input
                type="file"
                id="imageUpload"
                className="hidden"
                accept="image/*"
                onChange={handleFileUpload}
              />
              <Label htmlFor="imageUpload" asChild>
                <Button variant="outline" className="gap-2 cursor-pointer">
                  <ImageIcon className="h-4 w-4" />
                  Upload Image
                </Button>
              </Label>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Upload an image of the product to automatically extract specifications using AI
          </p>
        </div>
        
        {error && (
          <Alert variant="destructive" className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {generatedDescription && (
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Generated Description</h3>
              <Button onClick={handleApply} size="sm">Apply</Button>
            </div>
            <div className="p-4 border rounded-md bg-muted/30 whitespace-pre-wrap font-mono text-sm">
              {generatedDescription}
            </div>
          </div>
        )}
        
        <Alert className="mt-6">
          <Info className="h-4 w-4" />
          <AlertTitle>Description Format</AlertTitle>
          <AlertDescription>
            The generated description follows a structured format with sections for Product Identification, 
            Technical Specifications, Vehicle Compatibility, and Additional Information. This format ensures 
            consistency across all products.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};
