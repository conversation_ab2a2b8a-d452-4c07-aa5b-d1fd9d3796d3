import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { TyreProduct } from '../types/product.types';
import { parseExcelFile, parseCsvFile, mapImportedDataToProducts, validateImportedProducts, generateProductTemplate, exportToExcel } from '../utils/import-export';
import { toast } from 'sonner';
import { FileSpreadsheet, Upload, Download, AlertTriangle, CheckCircle2, X, FileUp, Loader2 } from 'lucide-react';

interface ImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (products: Partial<TyreProduct>[]) => Promise<void>;
  categoryId: string;
}

export const ImportDialog: React.FC<ImportDialogProps> = ({
  isOpen,
  onClose,
  onImport,
  categoryId,
}) => {
  const [activeTab, setActiveTab] = useState<string>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [importedData, setImportedData] = useState<any[]>([]);
  const [mappedProducts, setMappedProducts] = useState<Partial<TyreProduct>[]>([]);
  const [validationResults, setValidationResults] = useState<{ valid: Partial<TyreProduct>[], invalid: { product: Partial<TyreProduct>, errors: string[] }[] } | null>(null);
  const [isImporting, setIsImporting] = useState<boolean>(false);
  const [importProgress, setImportProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;
    
    setFile(selectedFile);
    setImportedData([]);
    setMappedProducts([]);
    setValidationResults(null);
  };

  // Handle file upload and parsing
  const handleFileUpload = async () => {
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 200);
      
      // Parse file based on type
      let data: any[] = [];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      
      if (fileExtension === 'csv') {
        data = await parseCsvFile(file);
      } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        data = await parseExcelFile(file);
      } else {
        throw new Error('Unsupported file format. Please upload a CSV or Excel file.');
      }
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      // Map data to products
      const products = mapImportedDataToProducts(data, categoryId);
      
      // Validate products
      const validation = validateImportedProducts(products);
      
      setImportedData(data);
      setMappedProducts(products);
      setValidationResults(validation);
      
      // Move to review tab
      setActiveTab('review');
      
      toast.success(`File uploaded successfully. Found ${data.length} records.`);
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload file');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle import
  const handleImport = async () => {
    if (!validationResults || validationResults.valid.length === 0) {
      toast.error('No valid products to import');
      return;
    }
    
    setIsImporting(true);
    setImportProgress(0);
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => {
          const newProgress = prev + 5;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 100);
      
      // Import valid products
      await onImport(validationResults.valid);
      
      clearInterval(progressInterval);
      setImportProgress(100);
      
      toast.success(`Successfully imported ${validationResults.valid.length} products`);
      
      // Reset state and close dialog
      setTimeout(() => {
        setFile(null);
        setImportedData([]);
        setMappedProducts([]);
        setValidationResults(null);
        setActiveTab('upload');
        onClose();
      }, 1000);
    } catch (error) {
      console.error('Error importing products:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to import products');
    } finally {
      setIsImporting(false);
    }
  };

  // Handle template download
  const handleDownloadTemplate = () => {
    try {
      const template = generateProductTemplate(categoryId);
      exportToExcel(template, `product_import_template_${categoryId}`);
      toast.success('Template downloaded successfully');
    } catch (error) {
      console.error('Error downloading template:', error);
      toast.error('Failed to download template');
    }
  };

  // Reset state when dialog closes
  const handleDialogClose = (open: boolean) => {
    if (!open) {
      setFile(null);
      setImportedData([]);
      setMappedProducts([]);
      setValidationResults(null);
      setActiveTab('upload');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileUp className="h-5 w-5" />
            Import Products
          </DialogTitle>
          <DialogDescription>
            Import products from a CSV or Excel file
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" disabled={isImporting}>Upload File</TabsTrigger>
            <TabsTrigger value="review" disabled={!importedData.length || isImporting}>Review Data</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="flex-1 overflow-auto p-4 space-y-6">
            <div className="space-y-4">
              <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center gap-4">
                <FileSpreadsheet className="h-12 w-12 text-muted-foreground" />
                <div className="text-center">
                  <p className="text-sm font-medium">Drag and drop your file here or click to browse</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Supported formats: CSV, Excel (.xlsx, .xls)
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Browse Files
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  className="hidden"
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
              </div>

              {file && (
                <div className="bg-muted/50 rounded-md p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileSpreadsheet className="h-5 w-5 text-muted-foreground" />
                      <span className="text-sm font-medium">{file.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {(file.size / 1024).toFixed(1)} KB
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setFile(null)}
                      disabled={isUploading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {isUploading && (
                    <div className="mt-2">
                      <Progress value={uploadProgress} className="h-2" />
                      <p className="text-xs text-muted-foreground mt-1">
                        Processing file... {uploadProgress}%
                      </p>
                    </div>
                  )}
                </div>
              )}

              <div className="flex flex-col gap-2">
                <div className="flex items-center justify-between">
                  <Label>Need a template?</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownloadTemplate}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Download a template file with the correct headers for importing products.
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="review" className="flex-1 overflow-hidden flex flex-col">
            {validationResults && (
              <div className="flex-1 overflow-hidden flex flex-col gap-4 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      {validationResults.valid.length} Valid
                    </Badge>
                    {validationResults.invalid.length > 0 && (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {validationResults.invalid.length} Invalid
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Total: {mappedProducts.length} products
                  </p>
                </div>

                {validationResults.invalid.length > 0 && (
                  <Alert variant="destructive" className="bg-red-50 text-red-700 border-red-200">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Validation Errors</AlertTitle>
                    <AlertDescription>
                      Some products have validation errors and will not be imported.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex-1 overflow-hidden">
                  <ScrollArea className="h-[300px] rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">Status</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>SKU</TableHead>
                          <TableHead>Manufacturer</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead className="text-right">Stock</TableHead>
                          <TableHead className="text-right">Price</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mappedProducts.map((product, index) => {
                          const isValid = validationResults.valid.some(p => p === product);
                          const invalidEntry = validationResults.invalid.find(p => p.product === product);
                          
                          return (
                            <TableRow key={index} className={isValid ? '' : 'bg-red-50'}>
                              <TableCell>
                                {isValid ? (
                                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                                ) : (
                                  <AlertTriangle className="h-4 w-4 text-red-600" />
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="font-medium">{product.name || <span className="text-muted-foreground italic">Missing</span>}</div>
                                {!isValid && (
                                  <div className="text-xs text-red-600 mt-1">
                                    {invalidEntry?.errors.join(', ')}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>{product.sku || <span className="text-muted-foreground italic">Missing</span>}</TableCell>
                              <TableCell>{product.manufacturer || <span className="text-muted-foreground italic">Missing</span>}</TableCell>
                              <TableCell>{product.category}</TableCell>
                              <TableCell className="text-right">{product.stockQuantity}</TableCell>
                              <TableCell className="text-right">{product.retailPrice}</TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="border-t pt-4">
          {activeTab === 'upload' && (
            <div className="flex justify-between w-full">
              <Button
                variant="outline"
                onClick={() => handleDialogClose(false)}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleFileUpload}
                disabled={!file || isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload and Review
                  </>
                )}
              </Button>
            </div>
          )}

          {activeTab === 'review' && (
            <div className="flex justify-between w-full">
              <Button
                variant="outline"
                onClick={() => setActiveTab('upload')}
                disabled={isImporting}
              >
                Back
              </Button>
              <div className="flex items-center gap-2">
                {isImporting && (
                  <div className="flex items-center gap-2 mr-2">
                    <Progress value={importProgress} className="w-24 h-2" />
                    <span className="text-xs text-muted-foreground">{importProgress}%</span>
                  </div>
                )}
                <Button
                  onClick={handleImport}
                  disabled={!validationResults?.valid.length || isImporting}
                >
                  {isImporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Import {validationResults?.valid.length} Products
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
