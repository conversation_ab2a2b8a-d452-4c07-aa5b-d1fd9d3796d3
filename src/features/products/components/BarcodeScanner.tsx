import React, { useEffect, useRef, useState } from 'react';
import { BrowserMultiFormatReader, Result } from '@zxing/library';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { ScanBarcode, X } from 'lucide-react';

interface BarcodeScannerProps {
  isOpen: boolean;
  onClose: () => void;
  onScan: (barcode: string) => void;
}

export const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  isOpen,
  onClose,
  onScan,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const codeReaderRef = useRef<BrowserMultiFormatReader | null>(null);
  
  useEffect(() => {
    // Initialize the barcode reader
    codeReaderRef.current = new BrowserMultiFormatReader();
    
    return () => {
      // Clean up on unmount
      if (codeReaderRef.current) {
        codeReaderRef.current.reset();
      }
    };
  }, []);
  
  useEffect(() => {
    if (isOpen && videoRef.current) {
      startScanning();
    } else {
      stopScanning();
    }
    
    return () => {
      stopScanning();
    };
  }, [isOpen]);
  
  const startScanning = async () => {
    if (!codeReaderRef.current || !videoRef.current) return;
    
    try {
      setError(null);
      setIsScanning(true);
      
      // Get available video devices
      const videoInputDevices = await codeReaderRef.current.listVideoInputDevices();
      
      if (videoInputDevices.length === 0) {
        setError('No camera found. Please connect a camera and try again.');
        return;
      }
      
      // Use the first available camera
      const selectedDeviceId = videoInputDevices[0].deviceId;
      
      // Start decoding from the device with id 'selectedDeviceId'
      codeReaderRef.current.decodeFromVideoDevice(
        selectedDeviceId,
        videoRef.current,
        (result: Result | null, error: Error | undefined) => {
          if (result) {
            // Barcode detected
            const barcodeValue = result.getText();
            onScan(barcodeValue);
            stopScanning();
            onClose();
          }
          
          if (error && !(error instanceof TypeError)) {
            // Ignore TypeError as it's often just a frame without a barcode
            console.error('Barcode scanning error:', error);
          }
        }
      );
    } catch (err) {
      console.error('Error starting barcode scanner:', err);
      setError('Failed to start camera. Please check camera permissions and try again.');
      setIsScanning(false);
    }
  };
  
  const stopScanning = () => {
    if (codeReaderRef.current) {
      codeReaderRef.current.reset();
      setIsScanning(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        stopScanning();
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ScanBarcode className="h-5 w-5" />
            Scan Product Barcode
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col items-center gap-4 py-4">
          {error ? (
            <div className="text-destructive text-center p-4 border border-destructive/20 rounded-md bg-destructive/10">
              {error}
            </div>
          ) : (
            <>
              <div className="relative w-full aspect-video bg-black rounded-md overflow-hidden">
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                />
                {isScanning && (
                  <div className="absolute inset-0 border-2 border-primary animate-pulse pointer-events-none" />
                )}
              </div>
              <p className="text-sm text-muted-foreground text-center">
                Position the barcode within the camera view to scan automatically.
              </p>
            </>
          )}
        </div>
        
        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => {
              stopScanning();
              onClose();
            }}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          
          {error && (
            <Button
              onClick={() => {
                setError(null);
                startScanning();
              }}
            >
              <ScanBarcode className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
