import React, { useState } from 'react';
import { Save, BookmarkPlus, Trash, Check, X } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { ColumnFiltersState } from '@tanstack/react-table';
import { toast } from 'sonner';

interface FilterPreset {
  id: string;
  name: string;
  filters: ColumnFiltersState;
}

interface FilterPresetsMenuProps {
  columnFilters: ColumnFiltersState;
  onApplyPreset: (filters: ColumnFiltersState) => void;
}

export const FilterPresetsMenu: React.FC<FilterPresetsMenuProps> = ({
  columnFilters,
  onApplyPreset,
}) => {
  const [presets, setPresets] = useState<FilterPreset[]>(() => {
    // Load saved presets from localStorage
    const savedPresets = localStorage.getItem('productFilterPresets');
    return savedPresets ? JSON.parse(savedPresets) : [];
  });
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPresetName, setNewPresetName] = useState('');
  
  // Save a new preset
  const handleSavePreset = () => {
    if (!newPresetName.trim()) {
      toast.error('Please enter a name for your preset');
      return;
    }
    
    if (columnFilters.length === 0) {
      toast.error('No filters are currently active');
      return;
    }
    
    const newPreset: FilterPreset = {
      id: Date.now().toString(),
      name: newPresetName,
      filters: columnFilters,
    };
    
    const updatedPresets = [...presets, newPreset];
    setPresets(updatedPresets);
    
    // Save to localStorage
    localStorage.setItem('productFilterPresets', JSON.stringify(updatedPresets));
    
    setNewPresetName('');
    setIsDialogOpen(false);
    toast.success(`Filter preset "${newPresetName}" saved`);
  };
  
  // Delete a preset
  const handleDeletePreset = (id: string) => {
    const updatedPresets = presets.filter(preset => preset.id !== id);
    setPresets(updatedPresets);
    
    // Save to localStorage
    localStorage.setItem('productFilterPresets', JSON.stringify(updatedPresets));
    toast.success('Filter preset deleted');
  };
  
  // Apply a preset
  const handleApplyPreset = (preset: FilterPreset) => {
    onApplyPreset(preset.filters);
    toast.success(`Applied filter preset "${preset.name}"`);
  };
  
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-8">
            <BookmarkPlus className="h-4 w-4 mr-2" />
            Filter Presets
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Saved Filter Presets</DropdownMenuLabel>
          
          {presets.length === 0 ? (
            <div className="px-2 py-4 text-sm text-center text-muted-foreground">
              No saved presets
            </div>
          ) : (
            <>
              {presets.map(preset => (
                <DropdownMenuItem
                  key={preset.id}
                  className="flex items-center justify-between cursor-pointer"
                >
                  <span 
                    className="flex-1 truncate"
                    onClick={() => handleApplyPreset(preset)}
                  >
                    {preset.name}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeletePreset(preset.id);
                    }}
                  >
                    <Trash className="h-3.5 w-3.5" />
                    <span className="sr-only">Delete preset</span>
                  </Button>
                </DropdownMenuItem>
              ))}
            </>
          )}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => {
              if (columnFilters.length === 0) {
                toast.error('No filters are currently active');
                return;
              }
              setIsDialogOpen(true);
            }}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Current Filters
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Save Filter Preset</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label htmlFor="preset-name" className="text-sm font-medium">
                Preset Name
              </label>
              <Input
                id="preset-name"
                value={newPresetName}
                onChange={(e) => setNewPresetName(e.target.value)}
                placeholder="e.g., Low Stock Items"
              />
            </div>
            
            <div className="text-sm text-muted-foreground">
              This will save your current filter configuration for future use.
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSavePreset}>
              Save Preset
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
