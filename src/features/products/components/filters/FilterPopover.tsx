import React, { useState } from 'react';
import { Filter, X } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ProductColumnDef } from '../../types/product.types';
import { cn } from '@/lib/utils';

interface FilterPopoverProps {
  column: ProductColumnDef;
  value: any;
  onChange: (value: any) => void;
  onClear: () => void;
}

export const FilterPopover: React.FC<FilterPopoverProps> = ({
  column,
  value,
  onChange,
  onClear,
}) => {
  const [open, setOpen] = useState(false);
  const [localValue, setLocalValue] = useState<any>(value);
  
  const handleApply = () => {
    onChange(localValue);
    setOpen(false);
  };
  
  const handleClear = () => {
    setLocalValue(undefined);
    onClear();
    setOpen(false);
  };
  
  const renderFilterContent = () => {
    switch (column.filterType) {
      case 'text':
        return (
          <div className="space-y-2">
            <Label htmlFor={`filter-${column.id}`}>Filter by {column.header}</Label>
            <Input
              id={`filter-${column.id}`}
              value={localValue || ''}
              onChange={(e) => setLocalValue(e.target.value)}
              placeholder={`Search ${column.header.toLowerCase()}...`}
              className="w-full"
            />
          </div>
        );
        
      case 'number':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor={`filter-min-${column.id}`}>Minimum</Label>
              <Input
                id={`filter-min-${column.id}`}
                type="number"
                value={localValue?.min || ''}
                onChange={(e) => setLocalValue({
                  ...localValue,
                  min: e.target.value ? Number(e.target.value) : undefined
                })}
                placeholder="Min"
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`filter-max-${column.id}`}>Maximum</Label>
              <Input
                id={`filter-max-${column.id}`}
                type="number"
                value={localValue?.max || ''}
                onChange={(e) => setLocalValue({
                  ...localValue,
                  max: e.target.value ? Number(e.target.value) : undefined
                })}
                placeholder="Max"
                className="w-full"
              />
            </div>
          </div>
        );
        
      case 'select':
        return (
          <div className="space-y-2">
            <Label htmlFor={`filter-${column.id}`}>Select {column.header}</Label>
            <Select
              value={localValue || ''}
              onValueChange={(value) => setLocalValue(value)}
            >
              <SelectTrigger id={`filter-${column.id}`} className="w-full">
                <SelectValue placeholder={`Select ${column.header.toLowerCase()}...`} />
              </SelectTrigger>
              <SelectContent>
                {column.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
        
      case 'boolean':
        return (
          <div className="space-y-2">
            <Label htmlFor={`filter-${column.id}`}>Filter by {column.header}</Label>
            <Select
              value={localValue === undefined ? '' : String(localValue)}
              onValueChange={(value) => {
                if (value === '') {
                  setLocalValue(undefined);
                } else {
                  setLocalValue(value === 'true');
                }
              }}
            >
              <SelectTrigger id={`filter-${column.id}`} className="w-full">
                <SelectValue placeholder="Select..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Any</SelectItem>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
        
      default:
        return (
          <div className="text-sm text-muted-foreground">
            No filter available for this column type.
          </div>
        );
    }
  };
  
  // Format the filter value for display
  const getFilterDisplayValue = () => {
    if (value === undefined) return null;
    
    switch (column.filterType) {
      case 'text':
        return value;
        
      case 'number':
        if (value.min !== undefined && value.max !== undefined) {
          return `${value.min} - ${value.max}`;
        } else if (value.min !== undefined) {
          return `≥ ${value.min}`;
        } else if (value.max !== undefined) {
          return `≤ ${value.max}`;
        }
        return null;
        
      case 'select':
        const option = column.options?.find(opt => opt.value === value);
        return option?.label || value;
        
      case 'boolean':
        return value ? 'Yes' : 'No';
        
      default:
        return String(value);
    }
  };
  
  const displayValue = getFilterDisplayValue();
  const hasFilter = displayValue !== null;
  
  return (
    <div className="flex items-center">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 w-8 p-0",
              hasFilter && "text-primary"
            )}
          >
            <Filter className="h-4 w-4" />
            <span className="sr-only">Filter {column.header}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-4" align="start">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Filter {column.header}</h4>
              {hasFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-muted-foreground"
                  onClick={handleClear}
                >
                  Clear
                </Button>
              )}
            </div>
            
            {renderFilterContent()}
            
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleApply}
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {hasFilter && (
        <Badge 
          variant="outline" 
          className="ml-1 font-normal"
        >
          {displayValue}
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
            onClick={(e) => {
              e.stopPropagation();
              handleClear();
            }}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Clear filter</span>
          </Button>
        </Badge>
      )}
    </div>
  );
};
