import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Plus,
  RefreshCw,
  ScanBarcode,
  Upload,
  Download,
  Filter,
  Columns,
  MoreHorizontal,
  Trash
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { CategoryTableConfig } from '../types/product.types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface CategoryHeaderProps {
  tableConfig: CategoryTableConfig;
  selectedRows?: number;
  onAddProduct?: () => void;
  onImport?: () => void;
  onExport?: () => void;
  onRefresh?: () => void;
  onScanBarcode?: () => void;
  onDeleteSelected?: () => void;
}

export const CategoryHeader: React.FC<CategoryHeaderProps> = ({
  tableConfig,
  selectedRows = 0,
  onAddProduct,
  onImport,
  onExport,
  onRefresh,
  onScanBarcode,
  onDeleteSelected,
}) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col gap-4 mb-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{tableConfig.name}</h2>
          {tableConfig.description && (
            <p className="text-muted-foreground mt-1">{tableConfig.description}</p>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={onRefresh}
          >
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline">{t('products.refresh')}</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={onScanBarcode}
          >
            <ScanBarcode className="h-4 w-4" />
            <span className="hidden sm:inline">{t('products.scanBarcode')}</span>
          </Button>

          <Button
            variant="default"
            className="gap-1"
            onClick={onAddProduct}
          >
            <Plus className="h-4 w-4" />
            {t('products.addProduct')}
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap items-center gap-2 mt-2">
        <Button
          variant="outline"
          className="gap-1"
          onClick={onImport}
        >
          <Upload className="h-4 w-4" />
          {t('products.import')}
        </Button>

        <Button
          variant="outline"
          className="gap-1"
          onClick={onExport}
        >
          <Download className="h-4 w-4" />
          {t('products.export')}
        </Button>
      </div>

      {selectedRows > 0 && (
        <div className="bg-primary/10 border border-primary/20 rounded-md px-4 py-2 flex items-center justify-between">
          <span className="text-sm font-medium">
            {t('products.selectedItems', { count: selectedRows })}
          </span>
          <Button
            variant="outline"
            size="sm"
            className="gap-1 border-primary/30 hover:bg-primary/20"
            onClick={onDeleteSelected}
          >
            <Trash className="h-4 w-4" />
            {t('products.deleteSelected')}
          </Button>
        </div>
      )}
    </div>
  );
};
