import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Car, X, Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import VehicleCompatibilitySelector, { VehicleCompatibility } from '@/components/compatibility/VehicleCompatibilitySelector';

interface VehicleCompatibilityFieldProps {
  value: VehicleCompatibility[];
  onChange: (vehicles: VehicleCompatibility[]) => void;
  label?: string;
  description?: string;
}

const VehicleCompatibilityField: React.FC<VehicleCompatibilityFieldProps> = ({
  value = [],
  onChange,
  label = 'Vehicle Type Compatibility',
  description = 'Select compatible vehicles from the compatibility hub database.'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter vehicles based on search query
  const filteredVehicles = value.filter(vehicle =>
    vehicle.displayName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <Label>{label}</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(true)}
          className="h-8"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Vehicles
        </Button>
      </div>

      <p className="text-sm text-muted-foreground">
        {description}
      </p>

      <div className="border rounded-md p-3 space-y-2">
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            {value.length} {value.length === 1 ? 'vehicle' : 'vehicles'} selected
          </div>
          {value.length > 0 && (
            <div className="relative w-[200px]">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search vehicles..."
                className="pl-8 h-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          )}
        </div>

        {value.length > 0 ? (
          <div className="h-[150px] overflow-y-auto">
            <div className="flex flex-wrap gap-2 p-1">
              {filteredVehicles.map((vehicle) => (
                <Badge
                  key={vehicle.id}
                  variant="secondary"
                  className="flex items-center gap-1 py-1 px-2 max-w-full"
                >
                  <div className="flex items-center gap-1 overflow-hidden">
                    <Car className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="whitespace-normal break-words">{vehicle.displayName}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0 ml-1 flex-shrink-0"
                      onClick={() => {
                        onChange(value.filter(v => v.id !== vehicle.id));
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </Badge>
              ))}

              {filteredVehicles.length === 0 && searchQuery && (
                <div className="w-full text-center text-sm text-muted-foreground py-4">
                  No vehicles match your search
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 text-muted-foreground">
            <Car className="h-10 w-10 mb-2 opacity-20" />
            <p className="text-sm">No vehicles selected</p>
            <p className="text-xs">Click "Add Vehicles" to select compatible vehicles</p>
          </div>
        )}
      </div>

      <VehicleCompatibilitySelector
        value={value}
        onChange={onChange}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </div>
  );
};

export default VehicleCompatibilityField;
