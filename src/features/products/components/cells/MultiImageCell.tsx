import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { Plus, Images, X, Upload, ChevronLeft, ChevronRight, Save, Loader2, Wand2 } from 'lucide-react';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AIImageEnhancer } from '../AIImageEnhancer';
import { fileToDataUrl, type TemporaryImage } from '@/services/productService';
import { isFeatureEnabled } from '@/config/features';
import 'react-photo-view/dist/react-photo-view.css';

interface MultiImageCellProps {
  value: string[] | undefined;
  onChange?: (value: string[]) => void;
  onTemporaryImagesChange?: (tempImages: TemporaryImage[]) => void; // For deferred upload
  isEditable?: boolean;
  productId?: string; // Product ID for Supabase storage
}

export const MultiImageCell: React.FC<MultiImageCellProps> = memo(({
  value = [],
  onChange,
  onTemporaryImagesChange,
  isEditable = false,
  productId,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [tempImages, setTempImages] = useState<string[]>(value?.length ? value : []);
  const [temporaryImageFiles, setTemporaryImageFiles] = useState<File[]>([]);
  const [isAIEnhancerOpen, setIsAIEnhancerOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Prevent dialog from opening multiple times
  const dialogOpeningRef = useRef(false);

  // Reset tempImages when value changes from outside
  useEffect(() => {
    setTempImages(value?.length ? value : []);
    setTemporaryImageFiles([]);
  }, [value]);

  const images = value?.length ? value : [];

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Limit the number of files to prevent UI issues
    if (tempImages.length + files.length > 50) {
      toast.error(`You can upload a maximum of 50 images. Please select fewer images.`);
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    // Validate files
    const validFiles = Array.from(files).filter(file => {
      if (!file.type.startsWith('image/')) {
        toast.error(`File ${file.name} is not an image`);
        return false;
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error(`File ${file.name} exceeds 5MB limit`);
        return false;
      }

      return true;
    });

    if (validFiles.length === 0) {
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    setIsUploading(true);
    toast.info(`Processing ${validFiles.length} image(s)...`);

    try {
      // Convert all files to base64 for temporary storage during editing
      const batchSize = 5;
      const newImages: string[] = [];
      const newFiles: File[] = [];

      for (let i = 0; i < validFiles.length; i += batchSize) {
        const batch = validFiles.slice(i, i + batchSize);

        // Process batch in parallel
        const batchResults = await Promise.allSettled(
          batch.map(file => fileToDataUrl(file))
        );

        // Add successful results to newImages
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            newImages.push(result.value);
            newFiles.push(batch[index]);
          } else {
            console.error(`Failed to process ${batch[index].name}:`, result.reason);
            toast.error(`Failed to process ${batch[index].name}`);
          }
        });
      }

      // Update state with all processed images
      if (newImages.length > 0) {
        setTempImages(prev => [...prev, ...newImages]);
        setTemporaryImageFiles(prev => [...prev, ...newFiles]);

        // Notify parent component about temporary images for deferred upload
        if (onTemporaryImagesChange) {
          const tempImageData: TemporaryImage[] = newFiles.map((file, index) => ({
            id: `temp_${Date.now()}_${index}`,
            file,
            dataUrl: newImages[index],
            type: 'additional'
          }));
          onTemporaryImagesChange(tempImageData);
        }

        toast.success(`${newImages.length} image(s) processed successfully (will be uploaded when you save the product)`);
      } else {
        toast.error('Failed to process any images');
      }
    } catch (error) {
      console.error('Error processing images:', error);
      toast.error('An error occurred while processing images');
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveImage = (index: number) => {
    const newImages = [...tempImages];
    const newFiles = [...temporaryImageFiles];

    newImages.splice(index, 1);
    if (index < newFiles.length) {
      newFiles.splice(index, 1);
    }

    setTempImages(newImages);
    setTemporaryImageFiles(newFiles);

    if (selectedImageIndex >= newImages.length) {
      setSelectedImageIndex(Math.max(0, newImages.length - 1));
    }

    // Update temporary images for deferred upload
    if (onTemporaryImagesChange && newFiles.length > 0) {
      const tempImageData: TemporaryImage[] = newFiles.map((file, idx) => ({
        id: `temp_${Date.now()}_${idx}`,
        file,
        dataUrl: newImages[idx],
        type: 'additional'
      }));
      onTemporaryImagesChange(tempImageData);
    } else if (onTemporaryImagesChange) {
      onTemporaryImagesChange([]);
    }

    toast.success('Image removed');
  };

  const handleSaveChanges = () => {
    if (onChange && JSON.stringify(tempImages) !== JSON.stringify(value)) {
      // For deferred upload pattern, we only update the form state
      // The actual upload will happen when the product is saved
      onChange(tempImages);
      toast.success('Image changes saved (will be uploaded when you save the product)');
    }
    setIsDialogOpen(false);
  };

  const handleDialogClose = useCallback((open: boolean) => {
    // If dialog is being closed
    if (!open) {
      // Reset to original value if dialog is closed without saving
      setTempImages(value?.length ? value : []);
      setTemporaryImageFiles([]);

      // Reset temporary images state
      if (onTemporaryImagesChange) {
        onTemporaryImagesChange([]);
      }

      // Ensure we set the state to closed
      setIsDialogOpen(false);
    } else {
      // If dialog is being opened, make sure we're not in a flickering state
      if (!dialogOpeningRef.current) {
        setIsDialogOpen(true);
      }
    }
  }, [value, onTemporaryImagesChange]);

  const navigateImages = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setSelectedImageIndex(prev => (prev > 0 ? prev - 1 : prev));
    } else {
      setSelectedImageIndex(prev => (prev < tempImages.length - 1 ? prev + 1 : prev));
    }
  };

  // Image gallery dialog for viewing and editing multiple images - memoized to prevent re-renders
  const ImageGalleryDialog = useCallback(() => (
    <Dialog
      open={isDialogOpen}
      onOpenChange={handleDialogClose}
      modal={true} // Force modal behavior to prevent event bubbling
    >
      <DialogContent
        className="sm:max-w-xl max-h-[90vh] overflow-hidden flex flex-col"
        onPointerDownOutside={(e) => {
          // Always prevent closing on outside click to avoid flickering
          e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          // Prevent closing on escape key during upload
          if (isUploading) {
            e.preventDefault();
          }
        }}
        onClick={(e) => {
          // Prevent event bubbling
          e.stopPropagation();
        }}
      >
        <DialogHeader>
          <DialogTitle>Images ({tempImages.length})</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="gallery" className="flex-1 overflow-hidden flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="gallery">Gallery View</TabsTrigger>
            <TabsTrigger value="grid">Grid View</TabsTrigger>
          </TabsList>

          <TabsContent value="gallery" className="flex-1 overflow-auto">
            {tempImages.length > 0 ? (
              <div className="flex flex-col gap-4 p-1">
                <div className="relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden">
                  {isUploading ? (
                    <div className="flex flex-col items-center justify-center">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <span className="text-sm mt-2">Processing images...</span>
                    </div>
                  ) : (
                    <img
                      src={tempImages[selectedImageIndex]}
                      alt={`Product ${selectedImageIndex + 1}`}
                      className="max-w-full max-h-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = '/placeholder.svg';
                      }}
                    />
                  )}

                  {tempImages.length > 1 && !isUploading && (
                    <>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute left-2 bg-black/20 hover:bg-black/40 text-white rounded-full"
                        onClick={() => navigateImages('prev')}
                        disabled={selectedImageIndex === 0}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 bg-black/20 hover:bg-black/40 text-white rounded-full"
                        onClick={() => navigateImages('next')}
                        disabled={selectedImageIndex === tempImages.length - 1}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </>
                  )}

                  {isEditable && !isUploading && (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-7 w-7"
                      onClick={() => handleRemoveImage(selectedImageIndex)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <ScrollArea className="w-full" orientation="horizontal">
                  <div className="flex gap-2 pb-2 min-w-max">
                    {tempImages.map((img, index) => (
                      <div
                        key={index}
                        className={cn(
                          "h-14 w-14 rounded-md overflow-hidden border cursor-pointer flex-shrink-0",
                          selectedImageIndex === index ? "border-primary border-2" : "border-border"
                        )}
                        onClick={() => setSelectedImageIndex(index)}
                      >
                        <img
                          src={img}
                          alt={`Thumbnail ${index + 1}`}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder.svg';
                          }}
                        />
                      </div>
                    ))}

                    {isEditable && !isUploading && (
                      <div
                        className="h-14 w-14 rounded-md border border-dashed border-muted-foreground/30 flex items-center justify-center cursor-pointer hover:bg-muted/50 flex-shrink-0"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Plus className="h-6 w-6 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 gap-4">
                <div className="flex flex-col items-center justify-center text-muted-foreground">
                  <Images className="w-16 h-16" />
                  <span className="text-sm mt-2">No images available</span>
                </div>

                {isEditable && !isUploading && (
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Images
                  </Button>
                )}

                {isUploading && (
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="text-sm mt-2">Processing images...</span>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="grid" className="flex-1 overflow-auto">
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-2 p-1 min-w-max">
              {tempImages.length > 0 ? (
                <>
                  {tempImages.map((img, index) => (
                    <div
                      key={index}
                      className="relative aspect-square rounded-md overflow-hidden border border-border group h-20 w-20"
                    >
                      <img
                        src={img}
                        alt={`Image ${index + 1}`}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                        onClick={() => {
                          setSelectedImageIndex(index);
                          const galleryTab = document.querySelector('[data-value="gallery"]') as HTMLElement;
                          galleryTab?.click();
                        }}
                      />
                      {isEditable && !isUploading && (
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveImage(index);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))}

                  {isEditable && !isUploading && (
                    <div
                      className="aspect-square rounded-md border border-dashed border-muted-foreground/30 flex items-center justify-center cursor-pointer hover:bg-muted/50"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Plus className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                </>
              ) : (
                <div className="col-span-full flex flex-col items-center justify-center p-8 gap-4">
                  <div className="flex flex-col items-center justify-center text-muted-foreground">
                    <Images className="w-16 h-16" />
                    <span className="text-sm mt-2">No images available</span>
                  </div>

                  {isEditable && !isUploading && (
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isUploading}
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Images
                    </Button>
                  )}

                  {isUploading && (
                    <div className="flex flex-col items-center justify-center">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <span className="text-sm mt-2">Processing images...</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {isEditable && (
          <DialogFooter className="flex sm:justify-between mt-4">
            <div className="flex gap-2">
              {tempImages.length > 0 && !isUploading && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Add More
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsAIEnhancerOpen(true)}
                    disabled={isUploading || selectedImageIndex >= tempImages.length}
                  >
                    <Wand2 className="mr-2 h-4 w-4" />
                    AI Enhance
                  </Button>
                </>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="secondary" size="sm" onClick={() => handleDialogClose(false)} disabled={isUploading}>
                Cancel
              </Button>
              <Button variant="default" size="sm" onClick={handleSaveChanges} disabled={isUploading}>
                <Save className="mr-2 h-4 w-4" />
                Save
              </Button>
            </div>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  ), [isDialogOpen, tempImages, isUploading, isEditable, selectedImageIndex, handleDialogClose, handleRemoveImage, handleSaveChanges, fileInputRef, navigateImages]);

  // Memoize the click handler to prevent unnecessary re-renders
  const handleImageClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling
    e.preventDefault(); // Prevent default behavior

    // Prevent multiple rapid clicks from causing issues
    if (!isDialogOpen && !dialogOpeningRef.current) {
      dialogOpeningRef.current = true;
      // Small delay to prevent multiple rapid clicks
      setTimeout(() => {
        setIsDialogOpen(true);
        dialogOpeningRef.current = false;
      }, 50);
    }
  }, [isDialogOpen]);

  return (
    <>
      <div
        className={cn(
          "relative h-12 rounded-md overflow-hidden border border-border flex items-center group",
          images.length === 0 && "bg-muted justify-center w-12",
          images.length > 0 && "p-1 w-auto min-w-[100px] max-w-[200px]",
          "cursor-pointer hover:bg-accent/50 transition-colors"
        )}
        style={{ minWidth: images.length === 0 ? '48px' : '100px' }}
        onClick={handleImageClick}
        onMouseDown={(e) => e.stopPropagation()} // Prevent event bubbling on mouse down
        onMouseUp={(e) => e.stopPropagation()} // Prevent event bubbling on mouse up
        role="button"
        tabIndex={0}
        aria-label={images.length > 0 ? "View or edit images" : "Add images"}
      >
        {isUploading ? (
          <div className="flex flex-col items-center justify-center w-full text-muted-foreground">
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
            <span className="text-[10px] mt-1">Processing...</span>
          </div>
        ) : images.length > 0 ? (
          <ScrollArea className="w-full h-full" orientation="horizontal">
            <div className="flex items-center h-full space-x-1 p-0.5">
              {images.map((img, index) => (
                <div
                  key={index}
                  className="relative h-8 w-8 flex-shrink-0 rounded-sm overflow-hidden flex items-center justify-center"
                >
                  <img
                    src={img}
                    alt={`Product ${index + 1}`}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder.svg';
                    }}
                  />
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <Images className="w-5 h-5" />
            <span className="text-[10px] mt-0.5">Add Images</span>
          </div>
        )}
      </div>

      {isEditable && (
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          className="hidden"
          onChange={handleImageUpload}
          aria-hidden="true"
        />
      )}

      {/* Use the memoized dialog component */}
      <ImageGalleryDialog />

      {/* AI Image Enhancer Dialog */}
      {isAIEnhancerOpen && tempImages.length > 0 && selectedImageIndex < tempImages.length && (
        <AIImageEnhancer
          imageUrl={tempImages[selectedImageIndex]}
          onEnhancedImage={(enhancedImageUrl) => {
            const newImages = [...tempImages];
            newImages[selectedImageIndex] = enhancedImageUrl;
            setTempImages(newImages);
          }}
          isOpen={isAIEnhancerOpen}
          onClose={() => setIsAIEnhancerOpen(false)}
        />
      )}
    </>
  );
});
