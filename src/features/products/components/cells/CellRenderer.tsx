import React from 'react';
import { ProductColumnDef } from '../../types/product.types';
import { ImageCell } from './ImageCell';
import { MultiImageCell } from './MultiImageCell';
import { StatusCell } from './StatusCell';
import { PricingTiersCell } from './PricingTiersCell';
import { SpecificationsCell } from './SpecificationsCell';
import { VehicleCompatibilityCell } from './VehicleCompatibilityCell';
import { format } from 'date-fns';
import { getCategoryDisplayName } from '../../utils/categoryMapping';
import { Badge } from '@/components/ui/badge';
import { formatDZD, formatNumber } from '@/lib/formatters';
import { getFormattedCity } from '@/data/algeria-cities';

interface CellRendererProps {
  column: ProductColumnDef;
  value: any;
  onChange?: (value: any) => void;
  isEditable?: boolean;
  rowData?: any; // Row data to access product ID
}

export const CellRenderer: React.FC<CellRendererProps> = ({
  column,
  value,
  onChange,
  isEditable = false,
  rowData,
}) => {
  // Only allow editing if the column is editable and isEditable is true
  const canEdit = column.editable && isEditable;

  // Extract product ID from row data (could be 'id', 'barcode', or other identifier)
  const productId = rowData?.id || rowData?.barcode;

  switch (column.cellType) {
    case 'image':
      return (
        <ImageCell
          value={value}
          onChange={canEdit ? onChange : undefined}
          isEditable={canEdit}
          productId={productId}
        />
      );

    case 'multiImage':
      return (
        <MultiImageCell
          value={value}
          onChange={canEdit ? onChange : undefined}
          isEditable={canEdit}
          productId={productId}
        />
      );

    case 'status':
      return (
        <StatusCell
          value={value}
          onChange={canEdit ? onChange : undefined}
          isEditable={canEdit}
        />
      );

    case 'pricingTiers':
      return (
        <PricingTiersCell
          value={value}
          onChange={canEdit ? onChange : undefined}
          isEditable={canEdit}
        />
      );

    case 'specifications':
      return (
        <SpecificationsCell
          value={value}
          onChange={canEdit ? onChange : undefined}
          isEditable={canEdit}
        />
      );

    case 'vehicleCompatibility':
      return (
        <VehicleCompatibilityCell
          value={value}
          onChange={canEdit ? onChange : undefined}
          isEditable={canEdit}
        />
      );

    case 'date':
      if (value instanceof Date) {
        return (
          <div className="text-sm">
            {format(value, 'PPP')}
          </div>
        );
      }
      return <div className="text-sm text-muted-foreground">No date</div>;

    case 'number':
      // Special handling for price fields
      if (column.id === 'retailPrice') {
        return (
          <div className="text-sm text-right">
            {typeof value === 'number'
              ? formatDZD(value)
              : <span className="text-muted-foreground">-</span>}
          </div>
        );
      }

      return (
        <div className="text-sm text-right">
          {typeof value === 'number'
            ? formatNumber(value)
            : <span className="text-muted-foreground">-</span>}
        </div>
      );

    case 'text':
    default:
      // Special handling for category field
      if (column.id === 'category' && value) {
        return (
          <Badge variant="outline" className="bg-[#f0f7ff] text-[#0073ea] border-[#0073ea]/30">
            {getCategoryDisplayName(value)}
          </Badge>
        );
      }

      // Special handling for shipping origin field
      if (column.id === 'shippingOrigin' && value) {
        return (
          <div className="text-sm truncate max-w-full" title={getFormattedCity(value)}>
            {getFormattedCity(value) || <span className="text-muted-foreground">-</span>}
          </div>
        );
      }

      return (
        <div className="text-sm truncate max-w-full" title={value}>
          {value || <span className="text-muted-foreground">-</span>}
        </div>
      );
  }
};
