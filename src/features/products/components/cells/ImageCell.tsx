import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import { createPortal } from 'react-dom';
import { Camera, X, Upload, Plus, Save, Loader2, Wand2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { PhotoView } from 'react-photo-view';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AIImageEnhancer } from '../AIImageEnhancer';
import { fileToDataUrl, type TemporaryImage } from '@/services/productService';
import { isFeatureEnabled } from '@/config/features';
import 'react-photo-view/dist/react-photo-view.css';

interface ImageCellProps {
  value: string | undefined;
  onChange?: (value: string) => void;
  onTemporaryImageChange?: (tempImage: TemporaryImage | null) => void; // For deferred upload
  isEditable?: boolean;
  productId?: string; // Product ID for Supabase storage
}

export const ImageCell: React.FC<ImageCellProps> = memo(({
  value,
  onChange,
  onTemporaryImageChange,
  isEditable = false,
  productId,
}) => {
  const imageUrl = value || '/placeholder.svg';
  const [isUploading, setIsUploading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [tempImage, setTempImage] = useState<string | undefined>(value);
  const [temporaryImageFile, setTemporaryImageFile] = useState<File | null>(null);
  const [isAIEnhancerOpen, setIsAIEnhancerOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Prevent dialog from opening multiple times
  const dialogOpeningRef = useRef(false);

  // Reset tempImage when value changes from outside
  useEffect(() => {
    setTempImage(value);
    setTemporaryImageFile(null);
  }, [value]);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB');
      if (fileInputRef.current) fileInputRef.current.value = '';
      return;
    }

    setIsUploading(true);
    toast.info('Processing image...');

    try {
      // Convert to base64 for temporary storage during editing
      const dataUrl = await fileToDataUrl(file);

      // Store the temporary image data
      setTempImage(dataUrl);
      setTemporaryImageFile(file);

      // Notify parent component about temporary image for deferred upload
      if (onTemporaryImageChange) {
        const tempImageData: TemporaryImage = {
          id: `temp_${Date.now()}`,
          file,
          dataUrl,
          type: 'primary'
        };
        onTemporaryImageChange(tempImageData);
      }

      toast.success('Image processed successfully (will be uploaded when you save the product)');
    } catch (error) {
      console.error('❌ Error processing image:', error);
      toast.error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveImage = () => {
    setTempImage('');
    setTemporaryImageFile(null);

    // Notify parent component about removal
    if (onTemporaryImageChange) {
      onTemporaryImageChange(null);
    }

    toast.success('Image removed');
  };

  const handleSaveChanges = () => {
    if (onChange && tempImage !== value) {
      // For deferred upload pattern, we only update the form state
      // The actual upload will happen when the product is saved
      onChange(tempImage || '');
      toast.success('Image changes saved (will be uploaded when you save the product)');
    }
    setIsDialogOpen(false);
  };

  const handleDialogClose = useCallback((open: boolean) => {
    // If dialog is being closed
    if (!open) {
      // Reset to original value if dialog is closed without saving
      setTempImage(value);
      setTemporaryImageFile(null);

      // Reset temporary image state
      if (onTemporaryImageChange) {
        onTemporaryImageChange(null);
      }

      // Ensure we set the state to closed
      setIsDialogOpen(false);
    } else {
      // If dialog is being opened, make sure we're not in a flickering state
      if (!dialogOpeningRef.current) {
        setIsDialogOpen(true);
      }
    }
  }, [value, onTemporaryImageChange]);

  // Image popup dialog for viewing and editing - memoized to prevent re-renders
  // Using portal rendering to avoid nested dialog conflicts that cause blank page issues
  const ImageDialog = useCallback(() => (
    <Dialog
      open={isDialogOpen}
      onOpenChange={handleDialogClose}
      modal={true} // Force modal behavior to prevent event bubbling
    >
      <DialogContent
        className="sm:max-w-md max-h-[90vh] overflow-hidden flex flex-col"
        style={{
          zIndex: 9999, // Ensure dialog appears above parent dialog
          position: 'fixed' // Ensure proper positioning
        }}
        onPointerDownOutside={(e) => {
          // Always prevent closing on outside click to avoid flickering
          e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          // Prevent closing on escape key during upload
          if (isUploading) {
            e.preventDefault();
          }
        }}
        onClick={(e) => {
          // Prevent event bubbling
          e.stopPropagation();
        }}
      >
        <DialogHeader>
          <DialogTitle>Image</DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-auto p-4">
          <div className="relative w-full h-64 flex items-center justify-center bg-muted/20 rounded-md overflow-hidden">
            {isUploading ? (
              <div className="flex flex-col items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="text-sm mt-2">Processing image...</span>
              </div>
            ) : tempImage ? (
              <PhotoView src={tempImage}>
                <img
                  src={tempImage}
                  alt="Product"
                  className="max-w-full max-h-full object-contain cursor-zoom-in"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
              </PhotoView>
            ) : (
              <div className="flex flex-col items-center justify-center text-muted-foreground">
                <Camera className="w-12 h-12" />
                <span className="text-sm mt-2">No image available</span>
              </div>
            )}
          </div>

          {isEditable && !isUploading && (
            <div className="mt-4 flex flex-col gap-2">
              <p className="text-sm text-muted-foreground">Drag and drop an image here or click the button below to upload.</p>
              <Button
                variant="outline"
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event bubbling
                  fileInputRef.current?.click();
                }}
                disabled={isUploading}
              >
                <Upload className="mr-2 h-4 w-4" />
                {tempImage ? 'Change Image' : 'Upload Image'}
              </Button>
            </div>
          )}
        </div>

        {isEditable && (
          <DialogFooter className="flex sm:justify-between border-t pt-4">
            <div className="flex gap-2">
              {tempImage && !isUploading && (
                <>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent event bubbling
                      handleRemoveImage();
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Remove
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent event bubbling
                      setIsAIEnhancerOpen(true);
                    }}
                  >
                    <Wand2 className="mr-2 h-4 w-4" />
                    AI Enhance
                  </Button>
                </>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event bubbling
                  handleDialogClose(false);
                }}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent event bubbling
                  handleSaveChanges();
                }}
                disabled={isUploading}
              >
                <Save className="mr-2 h-4 w-4" />
                Save
              </Button>
            </div>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  ), [isDialogOpen, tempImage, isUploading, isEditable, handleDialogClose, handleRemoveImage, handleSaveChanges, fileInputRef]);

  // Memoize the click handler to prevent unnecessary re-renders
  // ALWAYS open the nested dialog first - this is the required workflow
  const handleImageClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling
    e.preventDefault(); // Prevent default behavior

    // Always open dialog for both empty and existing images - this is the required nested dialog workflow
    if (!isDialogOpen && !dialogOpeningRef.current) {
      dialogOpeningRef.current = true;
      // Small delay to prevent multiple rapid clicks
      setTimeout(() => {
        setIsDialogOpen(true);
        dialogOpeningRef.current = false;
      }, 50);
    }
  }, [isDialogOpen]);

  return (
    <>
      <div
        className={cn(
          "relative w-12 h-12 rounded-md overflow-hidden border border-border flex items-center justify-center group",
          !value && "bg-muted",
          "cursor-pointer hover:bg-accent/50 transition-colors"
        )}
        onClick={handleImageClick}
        onMouseDown={(e) => e.stopPropagation()} // Prevent event bubbling on mouse down
        onMouseUp={(e) => e.stopPropagation()} // Prevent event bubbling on mouse up
        role="button"
        tabIndex={0}
        aria-label={value ? "View or edit image" : "Add image"}
      >
        {isUploading ? (
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
            <span className="text-[10px] mt-1">Processing...</span>
          </div>
        ) : value ? (
          <div className="w-full h-full flex items-center justify-center p-1">
            <img
              src={imageUrl}
              alt="Product"
              className="max-w-full max-h-full object-contain group-hover:scale-105 transition-transform"
              onError={(e) => {
                e.currentTarget.src = '/placeholder.svg';
              }}
            />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <Camera className="w-5 h-5" />
            <span className="text-[10px] mt-0.5">Add Image</span>
          </div>
        )}
      </div>

      {isEditable && (
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleImageUpload}
          aria-hidden="true"
        />
      )}

      {/* Use portal rendering to avoid nested dialog conflicts while preserving nested dialog workflow */}
      {typeof window !== 'undefined' && isDialogOpen && createPortal(<ImageDialog />, document.body)}

      {/* AI Image Enhancer Dialog - also using portal for consistency */}
      {isAIEnhancerOpen && tempImage && typeof window !== 'undefined' && createPortal(
        <AIImageEnhancer
          imageUrl={tempImage}
          onEnhancedImage={(enhancedImageUrl) => {
            setTempImage(enhancedImageUrl);
          }}
          isOpen={isAIEnhancerOpen}
          onClose={() => setIsAIEnhancerOpen(false)}
        />,
        document.body
      )}
    </>
  );
});
