import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Plus, Trash, AlertTriangle } from 'lucide-react';
import { PricingTier } from '../../types/product.types';
import { formatDZD } from '@/lib/formatters';
import { Button } from '@/components/ui/button';
import { validatePricingTiers, suggestNextTier, formatTierDisplay } from '@/utils/pricingTierUtils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface PricingTiersCellProps {
  value: PricingTier[] | undefined;
  onChange?: (value: PricingTier[]) => void;
  isEditable?: boolean;
}

export const PricingTiersCell: React.FC<PricingTiersCellProps> = ({
  value = [],
  onChange,
  isEditable = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTiers, setEditingTiers] = useState<PricingTier[]>([]);
  const [validationResult, setValidationResult] = useState(validatePricingTiers([]));

  const tiers = value || [];

  const handleOpenDialog = () => {
    setEditingTiers([...tiers]);
    setIsDialogOpen(true);
  };

  const handleAddTier = () => {
    const suggestedTier = suggestNextTier(editingTiers);
    const newTier: PricingTier = {
      minQuantity: suggestedTier.minQuantity || 1,
      maxQuantity: suggestedTier.maxQuantity,
      price: suggestedTier.price || 0,
    };

    const updatedTiers = [...editingTiers, newTier];
    setEditingTiers(updatedTiers);
    setValidationResult(validatePricingTiers(updatedTiers));
  };

  const handleRemoveTier = (index: number) => {
    const newTiers = [...editingTiers];
    newTiers.splice(index, 1);
    setEditingTiers(newTiers);
    setValidationResult(validatePricingTiers(newTiers));
  };

  const handleTierChange = (index: number, field: keyof PricingTier, value: number) => {
    const newTiers = [...editingTiers];
    newTiers[index] = { ...newTiers[index], [field]: value };
    setEditingTiers(newTiers);
    setValidationResult(validatePricingTiers(newTiers));
  };

  const handleSave = () => {
    const validation = validatePricingTiers(editingTiers);

    if (!validation.isValid) {
      // Don't save if validation fails
      console.warn('Cannot save invalid pricing tiers:', validation.errors);
      return;
    }

    console.log('PricingTiersCell saving:', editingTiers);
    if (onChange) {
      try {
        onChange(editingTiers);
        console.log('PricingTiersCell saved successfully');
        setIsDialogOpen(false);
      } catch (error) {
        console.error('Error saving pricing tiers:', error);
      }
    } else {
      console.warn('No onChange handler provided to PricingTiersCell');
    }
  };

  const formatPrice = (price: number) => {
    return formatDZD(price, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
  };

  const getSummary = () => {
    if (tiers.length === 0) {
      return 'No pricing tiers';
    }

    if (tiers.length === 1) {
      return `${formatPrice(tiers[0].price)}`;
    }

    const minPrice = Math.min(...tiers.map(t => t.price));
    const maxPrice = Math.max(...tiers.map(t => t.price));

    return `${tiers.length} tiers: ${formatPrice(minPrice)} - ${formatPrice(maxPrice)}`;
  };

  // Log for debugging
  console.log('PricingTiersCell render:', { isEditable, tiers: tiers.length });

  return (
    <>
      {isEditable ? (
        <div
          className={cn(
            "flex items-center justify-between p-2 rounded-md border border-transparent hover:border-border hover:bg-accent/20 transition-colors",
            "cursor-pointer"
          )}
          onClick={() => {
            console.log('PricingTiersCell edit clicked, opening dialog');
            handleOpenDialog();
          }}
        >
          <span className="text-sm truncate">{getSummary()}</span>
        </div>
      ) : (
        <Popover open={isExpanded} onOpenChange={setIsExpanded}>
          <PopoverTrigger asChild>
            <div
              className={cn(
                "flex items-center justify-between p-2 rounded-md border border-transparent hover:border-border hover:bg-accent/20 transition-colors",
                isExpanded && "border-border bg-accent/20"
              )}
            >
              <span className="text-sm truncate">{getSummary()}</span>
              <button
                className="text-muted-foreground hover:text-foreground"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsExpanded(!isExpanded);
                }}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            </div>
          </PopoverTrigger>

          {tiers.length > 0 && (
            <PopoverContent className="w-[200px] p-0" align="start">
              <div className="p-2 space-y-1">
                <div className="text-xs font-medium text-muted-foreground mb-2">
                  Wholesale Pricing Tiers
                </div>
                {tiers
                  .sort((a, b) => a.minQuantity - b.minQuantity)
                  .map((tier, index) => (
                  <div key={index} className="text-sm py-1">
                    <div className="font-medium">{formatTierDisplay(tier)}</div>
                  </div>
                ))}
              </div>
            </PopoverContent>
          )}
        </Popover>
      )}

      {isEditable && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Wholesale Pricing Tiers</DialogTitle>
            </DialogHeader>

            <div className="space-y-4 my-4">
              {/* Validation Messages */}
              {validationResult.errors.length > 0 && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center space-x-2 text-red-700 mb-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="font-medium">Validation Errors</span>
                  </div>
                  <ul className="text-sm text-red-600 space-y-1">
                    {validationResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {validationResult.warnings.length > 0 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center space-x-2 text-yellow-700 mb-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="font-medium">Warnings</span>
                  </div>
                  <ul className="text-sm text-yellow-600 space-y-1">
                    {validationResult.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {editingTiers.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  No pricing tiers defined
                </div>
              ) : (
                <div className="space-y-3">
                  {editingTiers
                    .sort((a, b) => a.minQuantity - b.minQuantity)
                    .map((tier, index) => (
                    <div key={index} className="space-y-2">
                      <div className="text-sm font-medium text-gray-700">
                        {formatTierDisplay(tier)}
                      </div>
                      <div className="grid grid-cols-[1fr,1fr,1fr,auto] gap-2 items-center">
                        <div>
                          <Label htmlFor={`min-qty-${index}`} className="text-xs">Min Qty</Label>
                          <Input
                            id={`min-qty-${index}`}
                            type="number"
                            min="1"
                            value={tier.minQuantity}
                            onChange={(e) => handleTierChange(index, 'minQuantity', parseInt(e.target.value) || 1)}
                          />
                        </div>
                        <div>
                          <Label htmlFor={`max-qty-${index}`} className="text-xs">Max Qty</Label>
                          <Input
                            id={`max-qty-${index}`}
                            type="number"
                            min={tier.minQuantity + 1}
                            value={tier.maxQuantity || ''}
                            placeholder="∞"
                            onChange={(e) => {
                              const value = e.target.value ? parseInt(e.target.value) : undefined;
                              handleTierChange(index, 'maxQuantity', value as any);
                            }}
                          />
                        </div>
                        <div>
                          <Label htmlFor={`price-${index}`} className="text-xs">Price per piece (DZD)</Label>
                          <Input
                            id={`price-${index}`}
                            type="number"
                            min="0"
                            step="0.01"
                            value={tier.price}
                            onChange={(e) => handleTierChange(index, 'price', parseFloat(e.target.value) || 0)}
                          />
                        </div>
                        <div className="pt-5">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveTier(index)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={handleAddTier}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Pricing Tier
              </Button>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={!validationResult.isValid}
                className={!validationResult.isValid ? 'opacity-50 cursor-not-allowed' : ''}
              >
                Save Changes
                {!validationResult.isValid && ' (Fix errors first)'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};
