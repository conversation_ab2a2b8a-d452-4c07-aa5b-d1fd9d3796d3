import React, { useState } from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProductStatus } from '../../types/product.types';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';

interface StatusOption {
  label: string;
  value: ProductStatus;
  color: string;
}

const statusOptions: StatusOption[] = [
  { 
    label: 'Active', 
    value: 'active', 
    color: 'bg-green-100 text-green-800 border-green-200' 
  },
  { 
    label: 'Draft', 
    value: 'draft', 
    color: 'bg-gray-100 text-gray-800 border-gray-200' 
  },
  { 
    label: 'Pending Approval', 
    value: 'pending_approval', 
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200' 
  },
  { 
    label: 'Out of Stock', 
    value: 'out_of_stock', 
    color: 'bg-red-100 text-red-800 border-red-200' 
  },
  { 
    label: 'Discontinued', 
    value: 'discontinued', 
    color: 'bg-purple-100 text-purple-800 border-purple-200' 
  },
];

interface StatusCellProps {
  value: ProductStatus;
  onChange?: (value: ProductStatus) => void;
  isEditable?: boolean;
}

export const StatusCell: React.FC<StatusCellProps> = ({
  value,
  onChange,
  isEditable = false,
}) => {
  const [open, setOpen] = useState(false);
  
  const selectedStatus = statusOptions.find(option => option.value === value) || statusOptions[0];
  
  const handleSelect = (status: StatusOption) => {
    if (onChange) {
      onChange(status.value);
    }
    setOpen(false);
  };
  
  return (
    <div className="flex items-center">
      {isEditable ? (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-1 h-7 px-2 rounded-full border text-xs font-medium",
                selectedStatus.color
              )}
            >
              {selectedStatus.label}
              <ChevronDown className="h-3 w-3 opacity-50" />
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-0" align="start">
            <Command>
              <CommandInput placeholder="Search status..." />
              <CommandEmpty>No status found.</CommandEmpty>
              <CommandGroup>
                {statusOptions.map((status) => (
                  <CommandItem
                    key={status.value}
                    value={status.value}
                    onSelect={() => handleSelect(status)}
                    className="flex items-center gap-2"
                  >
                    <div
                      className={cn(
                        "h-3 w-3 rounded-full",
                        status.value === 'active' && "bg-green-500",
                        status.value === 'draft' && "bg-gray-400",
                        status.value === 'pending_approval' && "bg-yellow-500",
                        status.value === 'out_of_stock' && "bg-red-500",
                        status.value === 'discontinued' && "bg-purple-500"
                      )}
                    />
                    <span>{status.label}</span>
                    {status.value === value && (
                      <Check className="h-4 w-4 ml-auto" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
      ) : (
        <div
          className={cn(
            "h-7 px-2 rounded-full border flex items-center text-xs font-medium",
            selectedStatus.color
          )}
        >
          {selectedStatus.label}
        </div>
      )}
    </div>
  );
};
