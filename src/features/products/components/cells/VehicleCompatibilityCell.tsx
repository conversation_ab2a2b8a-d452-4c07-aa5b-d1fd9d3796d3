import React, { useState, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Car, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import VehicleCompatibilitySelector, { VehicleCompatibility } from '@/components/compatibility/VehicleCompatibilitySelector';

interface VehicleCompatibilityCellProps {
  value: VehicleCompatibility[] | undefined;
  onChange?: (value: VehicleCompatibility[]) => void;
  isEditable?: boolean;
}

export const VehicleCompatibilityCell: React.FC<VehicleCompatibilityCellProps> = ({
  value = [],
  onChange,
  isEditable = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSelectorOpen, setIsSelectorOpen] = useState(false);
  const [expanded, setExpanded] = useState(false);

  const vehicles = Array.isArray(value) ? value : [];

  const handleChange = useCallback((newVehicles: VehicleCompatibility[]) => {
    if (onChange) {
      onChange(newVehicles);
    }
  }, [onChange]);

  // If there are no vehicles, show a placeholder
  if (vehicles.length === 0) {
    return (
      <div
        className={cn(
          "flex items-center justify-center h-full text-muted-foreground text-sm",
          isEditable && "cursor-pointer hover:bg-muted/30"
        )}
        onClick={() => isEditable && setIsSelectorOpen(true)}
      >
        {isEditable ? "Add compatibility" : "No compatibility data"}

        {isEditable && (
          <VehicleCompatibilitySelector
            value={vehicles}
            onChange={handleChange}
            isOpen={isSelectorOpen}
            onClose={() => setIsSelectorOpen(false)}
          />
        )}
      </div>
    );
  }

  // If there are only a few vehicles, show them directly
  if (vehicles.length <= 2) {
    return (
      <div
        className={cn(
          "flex flex-wrap gap-1 p-1",
          isEditable && "cursor-pointer hover:bg-muted/30"
        )}
        onClick={() => isEditable && setIsSelectorOpen(true)}
      >
        {vehicles.map((vehicle, index) => (
          <Badge
            key={index}
            variant="outline"
            className="flex items-center gap-1 py-0.5 px-1.5 text-xs"
          >
            <Car className="h-3 w-3 mr-1" />
            <span className="truncate max-w-[150px]">{vehicle.displayName}</span>
          </Badge>
        ))}

        {isEditable && (
          <VehicleCompatibilitySelector
            value={vehicles}
            onChange={handleChange}
            isOpen={isSelectorOpen}
            onClose={() => setIsSelectorOpen(false)}
          />
        )}
      </div>
    );
  }

  // For more vehicles, show a summary with a popover for details
  return (
    <div className="relative h-full">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div
            className={cn(
              "flex items-center gap-1 p-1 h-full",
              isEditable && "cursor-pointer hover:bg-muted/30"
            )}
            onClick={(e) => {
              if (isEditable) {
                e.stopPropagation();
                setIsSelectorOpen(true);
              } else {
                e.stopPropagation();
                setIsOpen(true);
              }
            }}
          >
            <Badge variant="outline" className="py-0.5 px-1.5 text-xs">
              <Car className="h-3 w-3 mr-1" />
              <span>{vehicles.length} vehicles</span>
            </Badge>

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                setIsOpen(!isOpen);
              }}
            >
              {isOpen ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
            </Button>
          </div>
        </PopoverTrigger>

        <PopoverContent
          className="w-80 p-2"
          align="start"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Compatible Vehicles</h4>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? "Collapse" : "Expand all"}
              </Button>
            </div>

            <div className="h-60 overflow-y-auto pr-1">
              <div className="space-y-1">
                {vehicles.map((vehicle, index) => (
                  <div
                    key={index}
                    className="text-xs p-1.5 rounded-md border bg-muted/20"
                  >
                    <div className="flex items-center gap-1">
                      <Car className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium">{vehicle.brand}</span>
                    </div>

                    {(expanded || index < 3) && (
                      <div className="ml-4 mt-1 text-muted-foreground">
                        {vehicle.model && <div>Model: {vehicle.model}</div>}
                        {vehicle.generation && <div>Generation: {vehicle.generation}</div>}
                        {vehicle.engineDetails?.fullName && (
                          <div>Engine: {vehicle.engineDetails.fullName}</div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {isEditable && (
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-2"
                onClick={() => {
                  setIsOpen(false);
                  setIsSelectorOpen(true);
                }}
              >
                Edit Compatibility
              </Button>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {isEditable && (
        <VehicleCompatibilitySelector
          value={vehicles}
          onChange={handleChange}
          isOpen={isSelectorOpen}
          onClose={() => setIsSelectorOpen(false)}
        />
      )}
    </div>
  );
};
