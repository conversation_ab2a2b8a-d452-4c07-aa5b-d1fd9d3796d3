import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Plus, Trash, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface SpecificationsItem {
  key: string;
  value: string;
}

interface SpecificationsCellProps {
  value: Record<string, string> | undefined;
  onChange?: (value: Record<string, string>) => void;
  isEditable?: boolean;
}

export const SpecificationsCell: React.FC<SpecificationsCellProps> = ({
  value,
  onChange,
  isEditable = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [specifications, setSpecifications] = useState<SpecificationsItem[]>(
    value ? Object.entries(value).map(([key, value]) => ({ key, value })) : []
  );
  const [newKey, setNewKey] = useState('');
  const [newValue, setNewValue] = useState('');
  const [editIndex, setEditIndex] = useState<number | null>(null);

  // Convert specifications array to object for saving
  const handleSave = () => {
    const specsObject = specifications.reduce((acc, spec) => {
      acc[spec.key] = spec.value;
      return acc;
    }, {} as Record<string, string>);
    
    onChange?.(specsObject);
    setIsOpen(false);
  };

  // Add a new specification
  const handleAddSpec = () => {
    if (newKey.trim() === '') return;
    
    setSpecifications([...specifications, { key: newKey, value: newValue }]);
    setNewKey('');
    setNewValue('');
  };

  // Remove a specification
  const handleRemoveSpec = (index: number) => {
    setSpecifications(specifications.filter((_, i) => i !== index));
  };

  // Start editing a specification
  const handleEditStart = (index: number) => {
    setEditIndex(index);
    setNewKey(specifications[index].key);
    setNewValue(specifications[index].value);
  };

  // Save edited specification
  const handleEditSave = () => {
    if (editIndex === null) return;
    
    const updatedSpecs = [...specifications];
    updatedSpecs[editIndex] = { key: newKey, value: newValue };
    
    setSpecifications(updatedSpecs);
    setEditIndex(null);
    setNewKey('');
    setNewValue('');
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditIndex(null);
    setNewKey('');
    setNewValue('');
  };

  return (
    <div className="flex items-center">
      <div className="text-sm">
        {value && Object.keys(value).length > 0 ? (
          <div className="flex items-center gap-1">
            <Badge variant="outline" className="font-normal">
              {Object.keys(value).length} specs
            </Badge>
            {isEditable && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setIsOpen(true)}
              >
                <Edit className="h-3 w-3" />
                <span className="sr-only">Edit specifications</span>
              </Button>
            )}
          </div>
        ) : (
          <div className="flex items-center gap-1">
            <span className="text-muted-foreground">No specs</span>
            {isEditable && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setIsOpen(true)}
              >
                <Plus className="h-3 w-3" />
                <span className="sr-only">Add specifications</span>
              </Button>
            )}
          </div>
        )}
      </div>

      {isEditable && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Edit Specifications</DialogTitle>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              {/* List of existing specifications */}
              <div className="max-h-[300px] overflow-y-auto">
                {specifications.length === 0 ? (
                  <div className="text-center text-muted-foreground py-4">
                    No specifications added yet
                  </div>
                ) : (
                  <div className="space-y-2">
                    {specifications.map((spec, index) => (
                      <div 
                        key={index} 
                        className={cn(
                          "flex items-center justify-between p-2 rounded-md",
                          editIndex === index ? "bg-muted" : "hover:bg-muted/50"
                        )}
                      >
                        {editIndex === index ? (
                          <div className="flex-1 grid grid-cols-2 gap-2">
                            <Input
                              value={newKey}
                              onChange={(e) => setNewKey(e.target.value)}
                              placeholder="Key"
                            />
                            <Input
                              value={newValue}
                              onChange={(e) => setNewValue(e.target.value)}
                              placeholder="Value"
                            />
                            <div className="col-span-2 flex justify-end gap-2 mt-1">
                              <Button 
                                type="button" 
                                variant="outline" 
                                size="sm"
                                onClick={handleEditCancel}
                              >
                                Cancel
                              </Button>
                              <Button 
                                type="button" 
                                size="sm"
                                onClick={handleEditSave}
                              >
                                Save
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <>
                            <div className="flex-1 grid grid-cols-2 gap-2">
                              <div className="font-medium">{spec.key}:</div>
                              <div>{spec.value}</div>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-7 w-7 p-0"
                                onClick={() => handleEditStart(index)}
                              >
                                <Edit className="h-3.5 w-3.5" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                                onClick={() => handleRemoveSpec(index)}
                              >
                                <Trash className="h-3.5 w-3.5" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Add new specification */}
              {editIndex === null && (
                <div className="grid grid-cols-[1fr_1fr_auto] gap-2 items-center">
                  <div>
                    <Label htmlFor="new-spec-key">Key</Label>
                    <Input
                      id="new-spec-key"
                      value={newKey}
                      onChange={(e) => setNewKey(e.target.value)}
                      placeholder="e.g., Material"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-spec-value">Value</Label>
                    <Input
                      id="new-spec-value"
                      value={newValue}
                      onChange={(e) => setNewValue(e.target.value)}
                      placeholder="e.g., Rubber"
                    />
                  </div>
                  <div className="pt-6">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAddSpec}
                      disabled={!newKey.trim()}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>
              )}
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button type="button" onClick={handleSave}>
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
