import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { BrakeProduct } from '@/features/products/types/product.types';
import { Save, X } from 'lucide-react';
import { generateUniqueProductId } from '../utils/idGenerator';
import { useUser } from '@/contexts/UserContext';
import { CATEGORIES, getCategoryById, getSubcategoriesForCategory } from '@/data/categories';

interface UnifiedProductFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Partial<BrakeProduct>) => Promise<void>;
  initialBarcode?: string;
  defaultCategory?: string;
}

export const UnifiedProductFormDialog: React.FC<UnifiedProductFormDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  initialBarcode = '',
  defaultCategory = 'brakes'
}) => {
  const { isSupplier } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with default values
  const { register, handleSubmit, formState: { errors }, reset, setValue, watch } = useForm<Partial<BrakeProduct>>({
    defaultValues: {
      name: '',
      sku: '',
      partArticleNumber: initialBarcode,
      category: defaultCategory,
      subcategory: '',
      descriptionAndSpecifications: '',
      manufacturer: '',
      supplierName: '',
      stockQuantity: 0,
      retailPrice: 0,
      shippingOrigin: '',
      minimumOrderQuantity: undefined,
      quotationRequestEnabled: false,
      status: 'draft',
    }
  });

  // Watch the category field to update subcategories
  const selectedCategory = watch('category');
  const selectedSubcategory = watch('subcategory');

  // Get available subcategories for selected category
  const availableSubcategories = selectedCategory ? getSubcategoriesForCategory(selectedCategory) : [];

  // Reset subcategory when category changes
  useEffect(() => {
    if (selectedCategory) {
      setValue('subcategory', '');
    }
  }, [selectedCategory, setValue]);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      reset({
        name: '',
        sku: '',
        partArticleNumber: initialBarcode,
        category: defaultCategory,
        subcategory: '',
        descriptionAndSpecifications: '',
        manufacturer: '',
        supplierName: '',
        stockQuantity: 0,
        retailPrice: 0,
        shippingOrigin: '',
        minimumOrderQuantity: undefined,
        quotationRequestEnabled: false,
        status: 'draft',
      });
    }
  }, [isOpen, reset, initialBarcode, defaultCategory]);

  const onSubmit = async (data: Partial<BrakeProduct>) => {
    if (!data.category) {
      toast.error('Please select a category');
      return;
    }

    if (!data.subcategory) {
      toast.error('Please select a subcategory');
      return;
    }

    setIsSubmitting(true);
    try {
      // Determine marketplace section based on user role
      const marketplaceSection = isSupplier() ? 'wholesale' : 'retail';
      
      // Generate a product ID with the appropriate prefix and suffix based on category and user role
      const productId = await generateUniqueProductId(data.category, marketplaceSection);

      // Add ID and category to the data
      const productData = {
        ...data,
        id: productId,
        category: data.category, // Ensure category is explicitly set
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      console.log('Creating product with data:', productData);
      await onSave(productData);
      
      toast.success('Product created successfully');
      onClose();
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error('Failed to create product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get available categories (all except tyres for unified table)
  const availableCategories = CATEGORIES.filter(cat => cat.id !== 'tyres');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Product</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Category Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Category *</Label>
              <Select
                value={selectedCategory}
                onValueChange={(value) => setValue('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {availableCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && <p className="text-red-500 text-sm">Category is required</p>}
            </div>

            <div>
              <Label htmlFor="subcategory">Subcategory *</Label>
              <Select
                value={selectedSubcategory}
                onValueChange={(value) => setValue('subcategory', value)}
                disabled={!selectedCategory}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select subcategory" />
                </SelectTrigger>
                <SelectContent>
                  {availableSubcategories.map((subcategory) => (
                    <SelectItem key={subcategory.id} value={subcategory.id}>
                      {subcategory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.subcategory && <p className="text-red-500 text-sm">Subcategory is required</p>}
            </div>
          </div>

          {/* Basic Product Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Product Name *</Label>
              <Input
                id="name"
                {...register('name', { required: 'Product name is required' })}
                placeholder="Enter product name"
              />
              {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
            </div>

            <div>
              <Label htmlFor="sku">SKU</Label>
              <Input
                id="sku"
                {...register('sku')}
                placeholder="Enter SKU"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="partArticleNumber">Part/Article Number</Label>
              <Input
                id="partArticleNumber"
                {...register('partArticleNumber')}
                placeholder="Enter part number"
              />
            </div>

            <div>
              <Label htmlFor="manufacturer">Manufacturer</Label>
              <Input
                id="manufacturer"
                {...register('manufacturer')}
                placeholder="Enter manufacturer"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="descriptionAndSpecifications">Description & Specifications</Label>
            <Textarea
              id="descriptionAndSpecifications"
              {...register('descriptionAndSpecifications')}
              placeholder="Enter product description and specifications"
              rows={4}
            />
          </div>

          {/* Pricing and Stock */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="retailPrice">Retail Price (DZD)</Label>
              <Input
                id="retailPrice"
                type="number"
                step="0.01"
                {...register('retailPrice', { valueAsNumber: true })}
                placeholder="0.00"
              />
            </div>

            <div>
              <Label htmlFor="stockQuantity">Stock Quantity</Label>
              <Input
                id="stockQuantity"
                type="number"
                {...register('stockQuantity', { valueAsNumber: true })}
                placeholder="0"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              <Save className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Creating...' : 'Create Product'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
