import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { BrakeProduct, PricingTier, VehicleCompatibility } from '../types/product.types';
import { toast } from 'sonner';
import { ImagePlus, Save, X, Plus, Trash, Wand2 } from 'lucide-react';
import { ImageCell } from './cells/ImageCell';
import { MultiImageCell } from './cells/MultiImageCell';
import { Card, CardContent } from '@/components/ui/card';
import { generateProductId } from '../utils/idGenerator';
import { AIDescriptionGenerator } from './AIDescriptionGenerator';
import VehicleCompatibilityField from './VehicleCompatibilityField';
import { useUser } from '@/contexts/UserContext';

interface BrakeProductFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Partial<BrakeProduct>) => Promise<void>;
  categories: { id: string; name: string }[];
  initialBarcode?: string;
}

export const BrakeProductFormDialog: React.FC<BrakeProductFormDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  categories,
  initialBarcode = '',
}) => {
  const { userRole, isSupplier, isMerchant } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [primaryImage, setPrimaryImage] = useState<string>('');
  const [additionalImages, setAdditionalImages] = useState<string[]>([]);
  const [pricingTiers, setPricingTiers] = useState<PricingTier[]>([]);
  const [vehicleCompatibility, setVehicleCompatibility] = useState<VehicleCompatibility[]>([]);
  const [certifications, setCertifications] = useState<string[]>([]);
  const [shippingMethods, setShippingMethods] = useState<string[]>([]);
  const [showAIGenerator, setShowAIGenerator] = useState(false);

  // Initialize form with default values
  const { register, handleSubmit, formState: { errors }, reset, setValue, watch } = useForm<Partial<BrakeProduct>>({
    defaultValues: {
      name: '',
      sku: '',
      partArticleNumber: initialBarcode,
      category: 'brakes',
      subcategory: 'Brake Discs',
      descriptionAndSpecifications: '',
      manufacturer: '',
      supplierName: '',
      stockQuantity: 0,
      retailPrice: 0,
      shippingOrigin: '',
      minimumOrderQuantity: undefined,
      quotationRequestEnabled: false,
      status: 'draft',
    }
  });

  // Watch the category field to conditionally render fields
  const selectedCategory = watch('category');
  const selectedSubcategory = watch('subcategory');

  // Handle form submission
  const onSubmit = async (data: Partial<BrakeProduct>) => {
    // Role-based pricing validation
    if (isSupplier()) {
      // Suppliers must have at least one wholesale pricing tier
      if (!pricingTiers || pricingTiers.length === 0) {
        setActiveTab('pricing');
        toast.error('Wholesale pricing required', {
          description: 'Please add at least one wholesale pricing tier.',
          duration: 5000,
        });
        return;
      }

      // Validate that all pricing tiers have valid data
      const invalidTiers = pricingTiers.filter(tier =>
        !tier.minQuantity || tier.minQuantity <= 0 || !tier.price || tier.price <= 0
      );
      if (invalidTiers.length > 0) {
        setActiveTab('pricing');
        toast.error('Invalid pricing tiers', {
          description: 'Please ensure all pricing tiers have valid minimum quantity and price values.',
          duration: 5000,
        });
        return;
      }
    } else if (isMerchant()) {
      // Merchants must have a retail price
      if (!data.retailPrice || data.retailPrice <= 0) {
        setActiveTab('pricing');
        toast.error('Retail price required', {
          description: 'Please enter a valid retail price greater than 0.',
          duration: 5000,
        });
        return;
      }
    }

    setIsSubmitting(true);
    try {
      // Determine marketplace section based on user role
      const marketplaceSection = isSupplier() ? 'wholesale' : 'retail';

      // Ensure category is set correctly
      const productCategory = data.category || 'brakes';

      // Generate a product ID with the appropriate prefix and suffix based on category and user role
      const productId = generateProductId(productCategory, marketplaceSection);

      // Add images, arrays, and ID to the data
      const productData = {
        ...data,
        id: productId, // Set the generated ID
        category: productCategory, // Ensure category is explicitly set
        primaryImage,
        additionalImages,
        vehicleTypeCompatibility: vehicleCompatibility,
        certifications,
        availableShippingMethods: shippingMethods,
        wholesalePricingTiers: pricingTiers,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save the product
      await onSave(productData);

      // Reset form and state
      reset();
      setPrimaryImage('');
      setAdditionalImages([]);
      setPricingTiers([]);
      setVehicleCompatibility([]);
      setCertifications([]);
      setShippingMethods([]);
      setActiveTab('basic');

      // Show success message
      toast.success('Product created successfully');

      // Close the dialog
      onClose();
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error('Failed to create product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle adding a new pricing tier
  const addPricingTier = () => {
    setPricingTiers([...pricingTiers, { minQuantity: 1, price: 0 }]);
  };

  // Handle removing a pricing tier
  const removePricingTier = (index: number) => {
    setPricingTiers(pricingTiers.filter((_, i) => i !== index));
  };

  // Handle updating a pricing tier
  const updatePricingTier = (index: number, field: keyof PricingTier, value: any) => {
    const updatedTiers = [...pricingTiers];
    updatedTiers[index] = { ...updatedTiers[index], [field]: value };
    setPricingTiers(updatedTiers);
  };



  // Handle description generation from AI
  const handleDescriptionGenerated = (description: string) => {
    setValue('description', description);
    setShowAIGenerator(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Add New Brake Product</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 py-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-6 bg-muted">
              <TabsTrigger value="basic" className="data-[state=active]:bg-background">
                Basic Information
              </TabsTrigger>
              <TabsTrigger value="images" className="data-[state=active]:bg-background">
                Images
              </TabsTrigger>
              <TabsTrigger value="details" className="data-[state=active]:bg-background">
                Product Details
              </TabsTrigger>
              <TabsTrigger value="description" className="data-[state=active]:bg-background">
                Description
              </TabsTrigger>
              <TabsTrigger value="pricing" className="data-[state=active]:bg-background">
                Pricing & Inventory
                {((isSupplier() && (!pricingTiers || pricingTiers.length === 0)) ||
                  (isMerchant() && (!watch('retailPrice') || watch('retailPrice') <= 0))) && (
                  <span className="ml-1 text-destructive">*</span>
                )}
              </TabsTrigger>
              <TabsTrigger value="shipping" className="data-[state=active]:bg-background">
                Shipping
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="name">Product Name <span className="text-destructive">*</span></Label>
                    <Input
                      id="name"
                      {...register('name', { required: 'Product name is required' })}
                      placeholder="Enter product name"
                      className={errors.name ? 'border-destructive' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sku">SKU</Label>
                    <Input
                      id="sku"
                      {...register('sku')}
                      placeholder="Enter SKU (optional)"
                      className={errors.sku ? 'border-destructive' : ''}
                    />
                    {errors.sku && (
                      <p className="text-sm text-destructive">{errors.sku.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="partArticleNumber">Part Article Number <span className="text-destructive">*</span></Label>
                    <Input
                      id="partArticleNumber"
                      {...register('partArticleNumber', { required: 'Part Article Number is required' })}
                      placeholder="Enter part article number"
                      defaultValue={initialBarcode}
                      className={errors.partArticleNumber ? 'border-destructive' : ''}
                    />
                    {errors.partArticleNumber && (
                      <p className="text-sm text-destructive">{errors.partArticleNumber.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category <span className="text-destructive">*</span></Label>
                    <Select
                      value={selectedCategory}
                      onValueChange={(value) => setValue('category', value)}
                    >
                      <SelectTrigger id="category" className={errors.category ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.category && (
                      <p className="text-sm text-destructive">{errors.category.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subcategory">Subcategory <span className="text-destructive">*</span></Label>
                    <Select
                      value={selectedSubcategory}
                      onValueChange={(value) => setValue('subcategory', value)}
                    >
                      <SelectTrigger id="subcategory" className={errors.subcategory ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select subcategory" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Vacuum Brake Booster">Vacuum Brake Booster</SelectItem>
                        <SelectItem value="Vacuum Pump">Vacuum Pump</SelectItem>
                        <SelectItem value="Brake Force Distributor">Brake Force Distributor</SelectItem>
                        <SelectItem value="Master Brake Cylinder">Master Brake Cylinder</SelectItem>
                        <SelectItem value="Repair Kit, Brake Master Cylinder">Repair Kit, Brake Master Cylinder</SelectItem>
                        <SelectItem value="Brake Fluid Reservoir">Brake Fluid Reservoir</SelectItem>
                        <SelectItem value="Brake Cylinder">Brake Cylinder</SelectItem>
                        <SelectItem value="Repair Kit, Wheel Brake Cylinder">Repair Kit, Wheel Brake Cylinder</SelectItem>
                        <SelectItem value="Brake Pads">Brake Pads</SelectItem>
                        <SelectItem value="Brake Pad Wear Indicator">Brake Pad Wear Indicator</SelectItem>
                        <SelectItem value="Accessory Kit, Disc Brake Pad">Accessory Kit, Disc Brake Pad</SelectItem>
                        <SelectItem value="High Performance Brake Caliper">High Performance Brake Caliper</SelectItem>
                        <SelectItem value="Drum Brake">Drum Brake</SelectItem>
                        <SelectItem value="Brake Shoes">Brake Shoes</SelectItem>
                        <SelectItem value="Accessories, Brake Shoe">Accessories, Brake Shoe</SelectItem>
                        <SelectItem value="Brake Drum">Brake Drum</SelectItem>
                        <SelectItem value="Brake Disc, Drum Brake">Brake Disc, Drum Brake</SelectItem>
                        <SelectItem value="Drum Brake Regulator">Drum Brake Regulator</SelectItem>
                        <SelectItem value="Brake Discs">Brake Discs</SelectItem>
                        <SelectItem value="Brake Discs and Pads">Brake Discs and Pads</SelectItem>
                        <SelectItem value="Anchor Plate">Anchor Plate</SelectItem>
                        <SelectItem value="High Performance Brake Disc">High Performance Brake Disc</SelectItem>
                        <SelectItem value="Handbrake">Handbrake</SelectItem>
                        <SelectItem value="Handbrake Cable">Handbrake Cable</SelectItem>
                        <SelectItem value="Handbrake Shoes">Handbrake Shoes</SelectItem>
                        <SelectItem value="Repair Kit, Handbrake Axle">Repair Kit, Handbrake Axle</SelectItem>
                        <SelectItem value="Switch, Handbrake Warning Light">Switch, Handbrake Warning Light</SelectItem>
                        <SelectItem value="Brake Hose">Brake Hose</SelectItem>
                        <SelectItem value="Brake Fluid">Brake Fluid</SelectItem>
                        <SelectItem value="Brake Line">Brake Line</SelectItem>
                        <SelectItem value="Vacuum Hose, Brake System">Vacuum Hose, Brake System</SelectItem>
                        <SelectItem value="Pressure Accumulator, Brake System">Pressure Accumulator, Brake System</SelectItem>
                        <SelectItem value="Pressure Switch, Hydraulic Brake System">Pressure Switch, Hydraulic Brake System</SelectItem>
                        <SelectItem value="ABS Sensor">ABS Sensor</SelectItem>
                        <SelectItem value="ABS Ring">ABS Ring</SelectItem>
                        <SelectItem value="ABS Pump">ABS Pump</SelectItem>
                        <SelectItem value="Overvoltage Relay, ABS">Overvoltage Relay, ABS</SelectItem>
                        <SelectItem value="ESP Sensor">ESP Sensor</SelectItem>
                        <SelectItem value="Control Unit, Brake/Vehicle Dynamics">Control Unit, Brake/Vehicle Dynamics</SelectItem>
                        <SelectItem value="Brake Caliper">Brake Caliper</SelectItem>
                        <SelectItem value="Brake Caliper Repair Kit">Brake Caliper Repair Kit</SelectItem>
                        <SelectItem value="Guide Sleeve Set, Brake Caliper">Guide Sleeve Set, Brake Caliper</SelectItem>
                        <SelectItem value="Brake Caliper Holder">Brake Caliper Holder</SelectItem>
                        <SelectItem value="Piston, Caliper">Piston, Caliper</SelectItem>
                        <SelectItem value="Brake Disc Screw">Brake Disc Screw</SelectItem>
                        <SelectItem value="Brake Light Switch">Brake Light Switch</SelectItem>
                        <SelectItem value="Threadlockers">Threadlockers</SelectItem>
                        <SelectItem value="Multifunctional Lubricant">Multifunctional Lubricant</SelectItem>
                        <SelectItem value="Mounting Paste">Mounting Paste</SelectItem>
                        <SelectItem value="Spray Grease">Spray Grease</SelectItem>
                        <SelectItem value="Brake Caliper Paint">Brake Caliper Paint</SelectItem>
                        <SelectItem value="Brake Tools">Brake Tools</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.subcategory && (
                      <p className="text-sm text-destructive">{errors.subcategory.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status <span className="text-destructive">*</span></Label>
                    <Select
                      value={watch('status')}
                      onValueChange={(value) => setValue('status', value as any)}
                    >
                      <SelectTrigger id="status" className={errors.status ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending_approval">Pending Approval</SelectItem>
                        <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                        <SelectItem value="discontinued">Discontinued</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && (
                      <p className="text-sm text-destructive">{errors.status.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="images" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Primary Product Image <span className="text-destructive">*</span></Label>
                    <Card>
                      <CardContent className="p-4">
                        <ImageCell
                          value={primaryImage}
                          onChange={setPrimaryImage}
                          className="w-full h-40 border-2 border-dashed border-muted-foreground/25 rounded-md"
                        />
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Additional Product Images</Label>
                    <Card>
                      <CardContent className="p-4">
                        <MultiImageCell
                          value={additionalImages}
                          onChange={setAdditionalImages}
                          className="w-full min-h-40 border-2 border-dashed border-muted-foreground/25 rounded-md"
                        />
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="manufacturer">Manufacturer <span className="text-destructive">*</span></Label>
                    <Input
                      id="manufacturer"
                      {...register('manufacturer', { required: 'Manufacturer is required' })}
                      placeholder="Enter manufacturer"
                      className={errors.manufacturer ? 'border-destructive' : ''}
                    />
                    {errors.manufacturer && (
                      <p className="text-sm text-destructive">{errors.manufacturer.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="supplierName">Supplier Name</Label>
                    <Input
                      id="supplierName"
                      {...register('supplierName')}
                      placeholder="Enter supplier name"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <VehicleCompatibilityField
                    value={vehicleCompatibility}
                    onChange={setVehicleCompatibility}
                    label="Vehicle Type Compatibility"
                    description="Select compatible vehicles from the compatibility hub database."
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="description" className="space-y-6">
              {showAIGenerator ? (
                <AIDescriptionGenerator
                  onDescriptionGenerated={handleDescriptionGenerated}
                  initialDescription={watch('descriptionAndSpecifications')}
                />
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="descriptionAndSpecifications">
                      Product Description and Specifications <span className="text-destructive">*</span>
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAIGenerator(true)}
                      className="gap-1"
                    >
                      <Wand2 className="h-4 w-4" /> Generate with AI
                    </Button>
                  </div>
                  <Textarea
                    id="descriptionAndSpecifications"
                    {...register('descriptionAndSpecifications', { required: 'Description and specifications are required' })}
                    placeholder="Enter a detailed, structured description of the product including technical specifications and compatibility information."
                    className={`min-h-[300px] ${errors.descriptionAndSpecifications ? 'border-destructive' : ''}`}
                  />
                  {errors.descriptionAndSpecifications && (
                    <p className="text-sm text-destructive">{errors.descriptionAndSpecifications.message}</p>
                  )}
                  <p className="text-sm text-muted-foreground">
                    For brake products, please include detailed technical specifications in a structured format.
                    Use markdown formatting for better readability (e.g., ## Technical Specifications).
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="pricing" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  {/* Only show retail price field for merchant accounts */}
                  {!isSupplier() && (
                    <div className="space-y-2">
                      <Label htmlFor="retailPrice">Retail Price (DZD) <span className="text-destructive">*</span></Label>
                      <Input
                        id="retailPrice"
                        type="number"
                        step="0.01"
                        {...register('retailPrice', {
                          valueAsNumber: true,
                          required: isMerchant() ? 'Retail price is required for merchant accounts' : false,
                          min: { value: 0.01, message: 'Retail price must be greater than 0' }
                        })}
                        placeholder="Enter retail price in DZD"
                        className={errors.retailPrice ? 'border-destructive' : ''}
                      />
                      {errors.retailPrice && (
                        <p className="text-sm text-destructive">{errors.retailPrice.message}</p>
                      )}
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="stockQuantity">Stock Quantity <span className="text-destructive">*</span></Label>
                    <Input
                      id="stockQuantity"
                      type="number"
                      {...register('stockQuantity', {
                        required: 'Stock quantity is required',
                        valueAsNumber: true,
                      })}
                      placeholder="0"
                      className={errors.stockQuantity ? 'border-destructive' : ''}
                    />
                    {errors.stockQuantity && (
                      <p className="text-sm text-destructive">{errors.stockQuantity.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="minimumOrderQuantity">Minimum Order Quantity</Label>
                    <Input
                      id="minimumOrderQuantity"
                      type="number"
                      {...register('minimumOrderQuantity', { valueAsNumber: true })}
                      placeholder="1"
                    />
                  </div>

                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="quotationRequestEnabled"
                      checked={watch('quotationRequestEnabled')}
                      onCheckedChange={(checked) => setValue('quotationRequestEnabled', !!checked)}
                    />
                    <Label htmlFor="quotationRequestEnabled" className="cursor-pointer">
                      Enable Quotation Requests
                    </Label>
                  </div>
                </div>

                {/* Only show wholesale pricing tiers for supplier accounts */}
                {isSupplier() ? (
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label>Wholesale Pricing Tiers <span className="text-destructive">*</span></Label>
                        <Button
                          type="button"
                          onClick={addPricingTier}
                          size="sm"
                          variant="outline"
                          className="gap-1"
                        >
                          <Plus className="h-4 w-4" /> Add Tier
                        </Button>
                      </div>
                      {(!pricingTiers || pricingTiers.length === 0) && (
                        <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                          At least one wholesale pricing tier is required for supplier accounts.
                        </div>
                      )}

                    {pricingTiers.length > 0 ? (
                      <div className="space-y-3">
                        {pricingTiers.map((tier, index) => (
                          <div key={index} className="flex items-end gap-2 border p-2 rounded-md">
                            <div className="space-y-1 flex-1">
                              <Label className="text-xs">Min Quantity</Label>
                              <Input
                                type="number"
                                value={tier.minQuantity}
                                onChange={(e) => updatePricingTier(index, 'minQuantity', parseInt(e.target.value))}
                                placeholder="1"
                                className="h-8"
                              />
                            </div>
                            <div className="space-y-1 flex-1">
                              <Label className="text-xs">Max Quantity</Label>
                              <Input
                                type="number"
                                value={tier.maxQuantity || ''}
                                onChange={(e) => updatePricingTier(index, 'maxQuantity', e.target.value ? parseInt(e.target.value) : undefined)}
                                placeholder="Optional"
                                className="h-8"
                              />
                            </div>
                            <div className="space-y-1 flex-1">
                              <Label className="text-xs">Price</Label>
                              <Input
                                type="number"
                                step="0.01"
                                value={tier.price}
                                onChange={(e) => updatePricingTier(index, 'price', parseFloat(e.target.value))}
                                placeholder="0.00"
                                className="h-8"
                              />
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removePricingTier(index)}
                              className="h-8 w-8 p-0"
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="border border-dashed rounded-md p-4 text-center text-muted-foreground text-sm">
                        No pricing tiers added yet
                      </div>
                    )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>Wholesale pricing tiers are only available for supplier accounts.</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="shipping" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="shippingOrigin">Shipping Origin <span className="text-destructive">*</span></Label>
                    <Input
                      id="shippingOrigin"
                      {...register('shippingOrigin', { required: 'Shipping origin is required' })}
                      placeholder="Enter shipping origin (country)"
                      className={errors.shippingOrigin ? 'border-destructive' : ''}
                    />
                    {errors.shippingOrigin && (
                      <p className="text-sm text-destructive">{errors.shippingOrigin.message}</p>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Product
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
