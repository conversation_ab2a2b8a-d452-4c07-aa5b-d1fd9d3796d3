import React, { useState, useEffect } from 'react';
import { useForm, FieldError } from 'react-hook-form';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { TyreProduct, PricingTier } from '../types/product.types';
import { toast } from 'sonner';
import { Save, X, Plus, Trash, AlertCircle, Sparkles } from 'lucide-react';
import { ImageCell } from './cells/ImageCell';
import { MultiImageCell } from './cells/MultiImageCell';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MagicAIButton } from './MagicAIButton';
import { ALGERIAN_CITIES, getFormattedCity } from '@/data/algeria-cities';
import VehicleCompatibilityField from './VehicleCompatibilityField';
import { VehicleCompatibility } from '@/components/compatibility/VehicleCompatibilitySelector';
import { useUser } from '@/contexts/UserContext';
import { batchUploadProductImages, deleteProductImage, type TemporaryImage } from '@/services/productService';
import { CATEGORIES, getSubcategoriesByCategory, getCategoryImageUrl, getSubcategoryImageUrl } from '@/data/categoryData';

interface ProductEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: TyreProduct) => Promise<void>;
  product: TyreProduct;
  categories: { id: string; name: string }[];
}

export const ProductEditDialog: React.FC<ProductEditDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  product,
  categories,
}) => {
  const { userRole, isSupplier, isMerchant } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [primaryImage, setPrimaryImage] = useState<string>(product.primaryImage || '');
  const [additionalImages, setAdditionalImages] = useState<string[]>(product.additionalImages || []);
  const [pricingTiers, setPricingTiers] = useState<PricingTier[]>(product.wholesalePricingTiers || []);
  const [vehicleCompatibility, setVehicleCompatibility] = useState<VehicleCompatibility[]>(
    Array.isArray(product.vehicleTypeCompatibility)
      ? (typeof product.vehicleTypeCompatibility[0] === 'string'
        ? (product.vehicleTypeCompatibility as string[]).map(type => ({
            id: type.toLowerCase().replace(/\s+/g, '-'),
            type: 'car',
            brand: type,
            model: '',
            displayName: type
          }))
        : product.vehicleTypeCompatibility as VehicleCompatibility[])
      : []
  );
  const [shippingMethods, setShippingMethods] = useState<string[]>(product.availableShippingMethods || []);
  const [newShippingMethod, setNewShippingMethod] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);

  // Temporary image state for deferred upload
  const [temporaryPrimaryImage, setTemporaryPrimaryImage] = useState<TemporaryImage | null>(null);
  const [temporaryAdditionalImages, setTemporaryAdditionalImages] = useState<TemporaryImage[]>([]);

  // Track original images for cleanup when replacing
  const [originalPrimaryImage, setOriginalPrimaryImage] = useState<string>('');
  const [originalAdditionalImages, setOriginalAdditionalImages] = useState<string[]>([]);

  // Map field names to tabs for validation redirection
  const fieldToTabMapping: Record<string, string> = {
    // Basic Information tab
    name: 'basic',
    sku: 'basic',
    partArticleNumber: 'basic',
    category: 'basic',
    subcategory: 'basic',
    status: 'basic',
    descriptionAndSpecifications: 'basic',

    // Product Details tab
    manufacturer: 'details',
    supplierName: 'details',
    productionCapacity: 'details',

    // Tyre Attributes tab
    width: 'tyre-attributes',
    aspectRatio: 'tyre-attributes',
    rimDiameter: 'tyre-attributes',
    loadIndex: 'tyre-attributes',
    speedRating: 'tyre-attributes',
    season: 'tyre-attributes',

    // Inventory tab
    stockQuantity: 'inventory',
    minimumOrderQuantity: 'inventory',
    retailPrice: 'inventory',
    quotationRequestEnabled: 'inventory',

    // Shipping tab
    shippingOrigin: 'shipping',

    // Images tab fields
    primaryImage: 'images',
    additionalImages: 'images',
  };

  // Initialize form with product values
  const { register, handleSubmit, formState: { errors }, reset, setValue, watch, unregister } = useForm<TyreProduct>({
    defaultValues: {
      ...product,
    }
  });

  // Update form when product changes
  useEffect(() => {
    reset(product);
    setPrimaryImage(product.primaryImage || '');
    setAdditionalImages(product.additionalImages || []);
    setPricingTiers(product.wholesalePricingTiers || []);

    // Track original images for cleanup when replacing
    setOriginalPrimaryImage(product.primaryImage || '');
    setOriginalAdditionalImages(product.additionalImages || []);

    // Convert string array to VehicleCompatibility objects if needed
    if (Array.isArray(product.vehicleTypeCompatibility)) {
      if (typeof product.vehicleTypeCompatibility[0] === 'string') {
        const vehicleCompat = (product.vehicleTypeCompatibility as string[]).map(type => ({
          id: type.toLowerCase().replace(/\s+/g, '-'),
          type: 'car',
          brand: type,
          model: '',
          displayName: type
        }));
        setVehicleCompatibility(vehicleCompat);
      } else {
        setVehicleCompatibility(product.vehicleTypeCompatibility as VehicleCompatibility[]);
      }
    } else {
      setVehicleCompatibility([]);
    }

    setShippingMethods(product.availableShippingMethods || []);
  }, [product, reset]);

  // Watch the category field to conditionally render fields
  const selectedCategory = watch('category');
  const selectedSubcategory = watch('subcategory');

  // Handle category changes
  useEffect(() => {
    // If category changes, reset subcategory to prevent invalid combinations
    if (selectedCategory !== product.category) {
      console.log(`Category changed from ${product.category} to ${selectedCategory}, resetting subcategory`);
      setValue('subcategory', '');
    }
  }, [selectedCategory, product.category, setValue]);

  // Clear validation error when tab changes
  useEffect(() => {
    setValidationError(null);
  }, [activeTab]);

  // Handle subcategory changes
  useEffect(() => {
    // If the subcategory changes from "Tyres" to something else
    if (selectedSubcategory !== 'Tyres') {
      // Switch to the "details" tab if currently on "tyre-attributes" tab
      if (activeTab === 'tyre-attributes') {
        setActiveTab('details');
      }

      // Clear all tyre-specific fields
      setValue('width', undefined);
      setValue('aspectRatio', undefined);
      setValue('rimDiameter', undefined);
      setValue('loadIndex', undefined);
      setValue('speedRating', '');
      setValue('season', undefined);

      // Unregister tyre-specific fields to remove validation
      unregister(['width', 'aspectRatio', 'rimDiameter', 'loadIndex', 'speedRating', 'season']);

      console.log('Unregistered tyre-specific fields');
    } else {
      // Re-register tyre-specific fields with validation when subcategory is "Tyres"
      // This will happen automatically when the fields are rendered
      console.log('Subcategory is Tyres, fields will be registered with validation');
    }

    // For Tyres category, hide vehicle compatibility for Tyres subcategory
    if (selectedCategory === 'tyres' && selectedSubcategory === 'Tyres') {
      // Clear vehicle compatibility for Tyres subcategory
      setVehicleCompatibility([]);
    }
  }, [selectedSubcategory, selectedCategory, activeTab, setValue, unregister]);

  // Find the first tab with validation errors
  const findTabWithErrors = (errors: Record<string, FieldError>) => {
    const errorFields = Object.keys(errors);
    if (errorFields.length === 0) return null;

    // Find the first field with an error and get its tab
    for (const field of errorFields) {
      if (field in fieldToTabMapping) {
        return fieldToTabMapping[field];
      }
    }

    return null;
  };

  // Handle form submission
  const onSubmit = async (data: TyreProduct) => {
    // Clear previous validation errors
    setValidationError(null);

    // Debug log to help diagnose issues
    console.log('Form submission data:', data);

    // Check for validation errors and redirect to the appropriate tab
    if (Object.keys(errors).length > 0) {
      const tabWithErrors = findTabWithErrors(errors);
      if (tabWithErrors && tabWithErrors !== activeTab) {
        setActiveTab(tabWithErrors);
        setValidationError('Please fill in all required fields');
        toast.error('Please fill in all required fields', {
          description: `Missing required information in the ${tabWithErrors.charAt(0).toUpperCase() + tabWithErrors.slice(1)} tab.`,
          duration: 5000,
        });
        return;
      }
    }

    // Validate that primary image is provided
    if (!primaryImage) {
      setActiveTab('images');
      setValidationError('Please upload a primary image for the product');
      toast.error('Image required', {
        description: 'Please upload a primary image for the product.',
        duration: 5000,
      });
      return;
    }

    // Validate that subcategory is selected
    if (!data.subcategory) {
      setActiveTab('basic');
      setValidationError('Please select a subcategory');
      toast.error('Subcategory required', {
        description: 'Please select a subcategory for the product.',
        duration: 5000,
      });
      return;
    }

    console.log('Form data before processing:', data);

    // For non-Tyres subcategories, ensure all tyre-specific fields are cleared
    if (data.subcategory !== 'Tyres') {
      // Explicitly set all tyre-specific fields to undefined
      data.width = undefined;
      data.aspectRatio = undefined;
      data.rimDiameter = undefined;
      data.loadIndex = undefined;
      data.speedRating = '';
      data.season = undefined;

      console.log('Cleared tyre-specific fields for non-Tyres subcategory');
    }
    // Validate tyre-specific fields only if subcategory is "Tyres"
    else if (data.subcategory === 'Tyres') {
      console.log('Validating tyre-specific fields for Tyres subcategory');
      const tyreFieldsErrors = [];

      if (!data.width) tyreFieldsErrors.push('Width');
      if (!data.aspectRatio) tyreFieldsErrors.push('Aspect Ratio');
      if (!data.rimDiameter) tyreFieldsErrors.push('Rim Diameter');
      if (!data.loadIndex) tyreFieldsErrors.push('Load Index');
      if (!data.speedRating) tyreFieldsErrors.push('Speed Rating');

      if (tyreFieldsErrors.length > 0) {
        setActiveTab('tyre-attributes');
        setValidationError(`Please fill in all required tyre specification fields: ${tyreFieldsErrors.join(', ')}`);
        toast.error('Tyre specifications required', {
          description: `Missing required tyre specifications: ${tyreFieldsErrors.join(', ')}`,
          duration: 5000,
        });
        console.log('Tyre fields validation failed:', tyreFieldsErrors);
        return;
      }
    }

    console.log('Form data after processing:', data);

    setIsSubmitting(true);
    try {
      let finalPrimaryImage = primaryImage;
      let finalAdditionalImages = additionalImages;

      // Handle deferred image uploads if there are temporary images
      const allTemporaryImages = [
        ...(temporaryPrimaryImage ? [temporaryPrimaryImage] : []),
        ...temporaryAdditionalImages
      ];

      if (allTemporaryImages.length > 0) {
        toast.info('Uploading images to cloud storage...');

        try {
          // Identify images that need to be deleted
          const imagesToDelete: string[] = [];

          // Check if primary image is being replaced
          if (temporaryPrimaryImage && originalPrimaryImage && originalPrimaryImage !== primaryImage) {
            imagesToDelete.push(originalPrimaryImage);
          }

          // Check if additional images are being replaced
          if (temporaryAdditionalImages.length > 0) {
            // Find images that are being replaced (not in current additionalImages)
            const currentImageUrls = additionalImages.filter(img => !img.startsWith('data:'));
            const imagesToKeep = new Set(currentImageUrls);

            originalAdditionalImages.forEach(originalImg => {
              if (originalImg && !imagesToKeep.has(originalImg)) {
                imagesToDelete.push(originalImg);
              }
            });
          }

          // Delete old images first (before uploading new ones)
          if (imagesToDelete.length > 0) {
            console.log('🗑️ Deleting old images:', imagesToDelete);
            await deleteOldImages(imagesToDelete);
            toast.info('Cleaned up old images...');
          }

          // Upload new images
          const uploadResults = await batchUploadProductImages(product.id, allTemporaryImages);

          // Update image URLs with uploaded versions - REPLACE, don't add
          if (uploadResults.primaryImage) {
            finalPrimaryImage = uploadResults.primaryImage;
          }

          if (uploadResults.additionalImages.length > 0) {
            // REPLACE temporary images with uploaded URLs, keep existing non-temporary images
            const existingImages = additionalImages.filter(img => !img.startsWith('data:'));

            // Replace temporary images with uploaded ones
            const tempImageCount = temporaryAdditionalImages.length;
            const nonTempImages = existingImages.slice(0, existingImages.length - tempImageCount);
            finalAdditionalImages = [...nonTempImages, ...uploadResults.additionalImages];
          }

          toast.success('Images uploaded successfully');
        } catch (uploadError) {
          console.error('Error uploading images:', uploadError);
          toast.error('Failed to upload images. Product will be saved with temporary images.');
          // Continue with temporary images if upload fails
        }
      }

      // Add images and arrays to the data
      const updatedProduct = {
        ...data,
        primaryImage: finalPrimaryImage,
        additionalImages: finalAdditionalImages,
        vehicleTypeCompatibility: selectedCategory === 'tyres' && selectedSubcategory === 'Tyres' ? undefined : vehicleCompatibility,
        availableShippingMethods: shippingMethods,
        wholesalePricingTiers: pricingTiers,
        updatedAt: new Date(),
      };

      await onSave(updatedProduct);
      toast.success('Product updated successfully');

      // Clear temporary images and original image tracking after successful save
      setTemporaryPrimaryImage(null);
      setTemporaryAdditionalImages([]);
      setOriginalPrimaryImage('');
      setOriginalAdditionalImages([]);

      onClose();
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('Failed to update product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to delete old images from Supabase Storage
  const deleteOldImages = async (imagesToDelete: string[]) => {
    const deletePromises = imagesToDelete
      .filter(imageUrl => imageUrl && imageUrl.trim() !== '') // Only delete valid URLs
      .map(async (imageUrl) => {
        try {
          await deleteProductImage(imageUrl);
          console.log('✅ Deleted old image:', imageUrl);
        } catch (error) {
          console.error('❌ Failed to delete old image:', imageUrl, error);
          // Don't throw error - continue with other deletions
        }
      });

    await Promise.allSettled(deletePromises);
  };

  // Handle dialog close
  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      // Clear temporary images and original image tracking when closing without saving
      setTemporaryPrimaryImage(null);
      setTemporaryAdditionalImages([]);
      setOriginalPrimaryImage('');
      setOriginalAdditionalImages([]);
      onClose();
    }
  };

  // Helper functions for array fields
  const addVehicleType = () => {
    if (newVehicleType.trim() && !vehicleTypes.includes(newVehicleType.trim())) {
      setVehicleTypes([...vehicleTypes, newVehicleType.trim()]);
      setNewVehicleType('');
    }
  };

  const removeVehicleType = (type: string) => {
    setVehicleTypes(vehicleTypes.filter(t => t !== type));
  };



  const addShippingMethod = () => {
    if (newShippingMethod.trim() && !shippingMethods.includes(newShippingMethod.trim())) {
      setShippingMethods([...shippingMethods, newShippingMethod.trim()]);
      setNewShippingMethod('');
    }
  };

  const removeShippingMethod = (method: string) => {
    setShippingMethods(shippingMethods.filter(m => m !== method));
  };

  const addPricingTier = () => {
    setPricingTiers([
      ...pricingTiers,
      { minQuantity: 1, price: 0 }
    ]);
  };

  const updatePricingTier = (index: number, field: keyof PricingTier, value: number) => {
    const updatedTiers = [...pricingTiers];
    updatedTiers[index] = { ...updatedTiers[index], [field]: value };
    setPricingTiers(updatedTiers);
  };

  const removePricingTier = (index: number) => {
    setPricingTiers(pricingTiers.filter((_, i) => i !== index));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Edit Product</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 py-4">
          {validationError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {validationError}
              </AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-6 bg-muted">
              <TabsTrigger value="basic" className="data-[state=active]:bg-background">
                Basic Information
                {Object.keys(errors).some(field => fieldToTabMapping[field] === 'basic') && (
                  <span className="ml-1 text-destructive">*</span>
                )}
              </TabsTrigger>
              <TabsTrigger value="images" className="data-[state=active]:bg-background">
                Images
                {(!primaryImage) && (
                  <span className="ml-1 text-destructive">*</span>
                )}
              </TabsTrigger>
              <TabsTrigger value="details" className="data-[state=active]:bg-background">
                Product Details
                {Object.keys(errors).some(field => fieldToTabMapping[field] === 'details') && (
                  <span className="ml-1 text-destructive">*</span>
                )}
              </TabsTrigger>
              {watch('subcategory') === 'Tyres' && (
                <TabsTrigger value="tyre-attributes" className="data-[state=active]:bg-background">
                  Tyre Attributes
                  {Object.keys(errors).some(field => fieldToTabMapping[field] === 'tyre-attributes') && (
                    <span className="ml-1 text-destructive">*</span>
                  )}
                </TabsTrigger>
              )}
              <TabsTrigger value="inventory" className="data-[state=active]:bg-background">
                Inventory
                {Object.keys(errors).some(field => fieldToTabMapping[field] === 'inventory') && (
                  <span className="ml-1 text-destructive">*</span>
                )}
              </TabsTrigger>
              <TabsTrigger value="shipping" className="data-[state=active]:bg-background">
                Shipping
                {Object.keys(errors).some(field => fieldToTabMapping[field] === 'shipping') && (
                  <span className="ml-1 text-destructive">*</span>
                )}
              </TabsTrigger>
              <TabsTrigger value="pricing" className="data-[state=active]:bg-background">
                Pricing
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="id">Product ID</Label>
                    <Input
                      id="id"
                      {...register('id')}
                      readOnly
                      className="bg-muted"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Product Name <span className="text-destructive">*</span></Label>
                    <Input
                      id="name"
                      {...register('name', { required: 'Product name is required' })}
                      placeholder="Enter product name"
                      className={errors.name ? 'border-destructive' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sku">SKU</Label>
                    <Input
                      id="sku"
                      {...register('sku')}
                      placeholder="Enter SKU (optional)"
                      className={errors.sku ? 'border-destructive' : ''}
                    />
                    {errors.sku && (
                      <p className="text-sm text-destructive">{errors.sku.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="partArticleNumber">Part Article Number <span className="text-destructive">*</span></Label>
                    <Input
                      id="partArticleNumber"
                      {...register('partArticleNumber', { required: 'Part Article Number is required' })}
                      placeholder="Enter part article number"
                      className={errors.partArticleNumber ? 'border-destructive' : ''}
                    />
                    {errors.partArticleNumber && (
                      <p className="text-sm text-destructive">{errors.partArticleNumber.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category <span className="text-destructive">*</span></Label>
                    <Select
                      value={watch('category')}
                      onValueChange={(value) => {
                        console.log(`Category changed to: ${value}`);
                        setValue('category', value);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {CATEGORIES.filter(cat => cat.id !== 'all').map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            <div className="flex items-center gap-2">
                              <img
                                src={getCategoryImageUrl(category.id)}
                                alt={category.displayName}
                                className="w-4 h-4 object-contain"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                }}
                              />
                              {category.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subcategory">Subcategory <span className="text-destructive">*</span></Label>
                    <Select
                      value={watch('subcategory') || ''}
                      onValueChange={(value) => setValue('subcategory', value)}
                      {...register('subcategory', { required: 'Subcategory is required' })}
                    >
                      <SelectTrigger className={errors.subcategory ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select subcategory" />
                      </SelectTrigger>
                      <SelectContent>
                        {getSubcategoriesByCategory(selectedCategory).map((subcategory) => (
                          <SelectItem key={subcategory.id} value={subcategory.name}>
                            <div className="flex items-center gap-2">
                              <img
                                src={getSubcategoryImageUrl(subcategory.id)}
                                alt={subcategory.displayName}
                                className="w-4 h-4 object-contain"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                }}
                              />
                              {subcategory.displayName}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.subcategory && (
                      <p className="text-sm text-destructive">{errors.subcategory.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status <span className="text-destructive">*</span></Label>
                    <Select
                      value={watch('status')}
                      onValueChange={(value) => setValue('status', value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending_approval">Pending Approval</SelectItem>
                        <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                        <SelectItem value="discontinued">Discontinued</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="descriptionAndSpecifications">Description and Specifications</Label>
                <div className="flex items-center mb-2">
                  <div className="bg-orange-50 text-orange-700 px-2 py-1 rounded-md text-xs font-medium mr-2">
                    Dual-Section Format
                  </div>
                  <span className="text-xs text-muted-foreground">
                    Use structured format with separate description and specifications sections
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 ml-auto text-xs"
                    onClick={() => {
                      const template = selectedCategory === 'tyres'
                        ? `Part 1 - Product Description:
High-performance ${selectedSubcategory || 'tyre'} designed for optimal performance and safety. Features advanced compound technology for superior grip and durability. Suitable for various driving conditions and vehicle types.

Part 2 - Specifications:
- Width: [Enter width in mm]
- Aspect Ratio: [Enter aspect ratio]
- Rim Diameter: [Enter rim diameter in inches]
- Load Index: [Enter load index]
- Speed Rating: [Enter speed rating]
- Season: [Summer/Winter/All-Season]
- Tread Pattern: [Enter tread pattern]
- Compound Type: [Enter compound type]
- DOT Compliance: Yes
- ECE Marking: [Enter ECE marking]`
                        : selectedCategory === 'brakes'
                        ? `Part 1 - Product Description:
High-quality ${selectedSubcategory || 'brake component'} engineered for reliable braking performance and safety. Manufactured to OEM standards with premium materials for extended service life and consistent performance.

Part 2 - Specifications:
- Part Number: [Enter part number]
- Brand: [Enter brand]
- Component Type: [Enter component type]
- Material: [Enter material composition]
- Dimensions: [Enter dimensions]
- Weight: [Enter weight]
- Compatibility: [Enter vehicle compatibility]
- OE Reference Numbers: [Enter OE numbers]
- Certification: [Enter certifications]
- Installation Notes: [Enter installation requirements]`
                        : `Part 1 - Product Description:
High-quality automotive part designed for reliable performance and durability. Manufactured to meet or exceed OEM specifications with premium materials and advanced engineering.

Part 2 - Specifications:
- Part Number: [Enter part number]
- Brand: [Enter brand]
- Material: [Enter material]
- Dimensions: [Enter dimensions]
- Weight: [Enter weight]
- Compatibility: [Enter compatibility]
- Certification: [Enter certifications]
- Installation: [Enter installation notes]
- Warranty: [Enter warranty information]`;

                      setValue('descriptionAndSpecifications', template);
                    }}
                  >
                    Insert Template
                  </Button>
                </div>
                <Textarea
                  id="descriptionAndSpecifications"
                  {...register('descriptionAndSpecifications')}
                  placeholder="Enter product description and specifications using the dual-section format above"
                  className="min-h-[150px]"
                />
              </div>

              {/* Vehicle Type Compatibility Field - Only show for Brake Parts or non-Tyres subcategory */}
              {(selectedCategory === 'brakes' || (selectedCategory === 'tyres' && selectedSubcategory !== 'Tyres')) && (
                <div className="mt-6">
                  <VehicleCompatibilityField
                    value={vehicleCompatibility}
                    onChange={setVehicleCompatibility}
                    label="Vehicle Type Compatibility"
                    description="Select compatible vehicles from the compatibility hub database."
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="images" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label>Primary Image <span className="text-destructive">*</span></Label>
                  <div className={`border rounded-md p-4 bg-muted/20 ${!primaryImage ? 'border-destructive' : ''}`}>
                    <ImageCell
                      value={primaryImage}
                      onChange={setPrimaryImage}
                      onTemporaryImageChange={setTemporaryPrimaryImage}
                      isEditable={true}
                      productId={product.id}
                    />
                  </div>
                  {!primaryImage && activeTab === 'images' && (
                    <p className="text-sm text-destructive">Primary image is required</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label>Additional Images</Label>
                  <div className="border rounded-md p-4 bg-muted/20 overflow-hidden" style={{ minHeight: '60px' }}>
                    <MultiImageCell
                      value={additionalImages}
                      onChange={setAdditionalImages}
                      onTemporaryImagesChange={setTemporaryAdditionalImages}
                      isEditable={true}
                      productId={product.id}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="manufacturer">Manufacturer <span className="text-destructive">*</span></Label>
                    <Input
                      id="manufacturer"
                      {...register('manufacturer', { required: 'Manufacturer is required' })}
                      placeholder="Enter manufacturer"
                      className={errors.manufacturer ? 'border-destructive' : ''}
                    />
                    {errors.manufacturer && (
                      <p className="text-sm text-destructive">{errors.manufacturer.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="supplierName">Supplier Name</Label>
                    <Input
                      id="supplierName"
                      {...register('supplierName')}
                      placeholder="Enter supplier name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="productionCapacity">Production Capacity (units/month)</Label>
                    <Input
                      id="productionCapacity"
                      type="number"
                      {...register('productionCapacity', { valueAsNumber: true })}
                      placeholder="Enter production capacity"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {watch('subcategory') === 'Tyres' && (
              <TabsContent value="tyre-attributes" className="space-y-6">
                <div className="space-y-4">
                  <div className="bg-muted/30 p-4 rounded-md mb-4">
                    <h3 className="text-md font-medium mb-2">Tyre Specifications</h3>
                    <p className="text-sm text-muted-foreground">
                      These fields are critical for the tyre search engine. Please enter the exact values without any additional symbols or text.
                    </p>
                  </div>

                  {/* Tyre Parameters Image */}
                  <div className="flex justify-center mb-6">
                    <div className="relative rounded-lg shadow-md border border-muted" style={{ maxWidth: '500px' }}>
                      <div className="p-2">
                        <img
                          src="/images/TyreParameters.png"
                          alt="Tyre Parameters Diagram"
                          className="w-full h-auto object-contain"
                          style={{ maxHeight: '250px' }}
                          draggable="false"
                        />
                      </div>
                      <div className="bg-background/80 backdrop-blur-sm p-2 text-center border-t border-muted">
                        <p className="text-sm font-medium">Tyre Parameters Reference</p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label htmlFor="width">Width <span className="text-destructive">*</span></Label>
                        <Input
                          id="width"
                          type="number"
                          {...register('width', {
                            required: 'Width is required',
                            valueAsNumber: true,
                            min: { value: 1, message: 'Width must be a positive number' },
                            disabled: selectedSubcategory !== 'Tyres'
                          })}
                          placeholder="e.g., 205"
                          className={errors.width ? 'border-destructive' : ''}
                          disabled={selectedSubcategory !== 'Tyres'}
                        />
                        {errors.width && (
                          <p className="text-sm text-destructive">{errors.width.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="aspectRatio">Aspect Ratio / Height <span className="text-destructive">*</span></Label>
                        <Input
                          id="aspectRatio"
                          type="number"
                          {...register('aspectRatio', {
                            required: 'Aspect ratio is required',
                            valueAsNumber: true,
                            min: { value: 1, message: 'Aspect ratio must be a positive number' },
                            disabled: selectedSubcategory !== 'Tyres'
                          })}
                          placeholder="e.g., 60"
                          className={errors.aspectRatio ? 'border-destructive' : ''}
                          disabled={selectedSubcategory !== 'Tyres'}
                        />
                        {errors.aspectRatio && (
                          <p className="text-sm text-destructive">{errors.aspectRatio.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="rimDiameter">Diameter / Rim Size <span className="text-destructive">*</span></Label>
                        <Input
                          id="rimDiameter"
                          type="number"
                          {...register('rimDiameter', {
                            required: 'Rim diameter is required',
                            valueAsNumber: true,
                            min: { value: 1, message: 'Rim diameter must be a positive number' },
                            disabled: selectedSubcategory !== 'Tyres'
                          })}
                          placeholder="e.g., 16"
                          className={errors.rimDiameter ? 'border-destructive' : ''}
                          disabled={selectedSubcategory !== 'Tyres'}
                        />
                        {errors.rimDiameter && (
                          <p className="text-sm text-destructive">{errors.rimDiameter.message}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label htmlFor="loadIndex">Load Index <span className="text-destructive">*</span></Label>
                        <Input
                          id="loadIndex"
                          type="number"
                          {...register('loadIndex', {
                            required: 'Load index is required',
                            valueAsNumber: true,
                            min: { value: 1, message: 'Load index must be a positive number' },
                            disabled: selectedSubcategory !== 'Tyres'
                          })}
                          placeholder="e.g., 97"
                          className={errors.loadIndex ? 'border-destructive' : ''}
                          disabled={selectedSubcategory !== 'Tyres'}
                        />
                        {errors.loadIndex && (
                          <p className="text-sm text-destructive">{errors.loadIndex.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="speedRating">Speed Rating <span className="text-destructive">*</span></Label>
                        <Select
                          onValueChange={(value) => setValue('speedRating', value)}
                          defaultValue={watch('speedRating') || ""}
                          {...register('speedRating', {
                            required: 'Speed rating is required',
                            disabled: selectedSubcategory !== 'Tyres'
                          })}
                          disabled={selectedSubcategory !== 'Tyres'}
                        >
                          <SelectTrigger className={errors.speedRating ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Select speed rating" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Q">Q</SelectItem>
                            <SelectItem value="R">R</SelectItem>
                            <SelectItem value="S">S</SelectItem>
                            <SelectItem value="T">T</SelectItem>
                            <SelectItem value="U">U</SelectItem>
                            <SelectItem value="H">H</SelectItem>
                            <SelectItem value="V">V</SelectItem>
                            <SelectItem value="W">W</SelectItem>
                            <SelectItem value="Y">Y</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.speedRating && (
                          <p className="text-sm text-destructive">{errors.speedRating.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="season">Season</Label>
                        <Select
                          onValueChange={(value) => setValue('season', value as any)}
                          defaultValue={watch('season') || ""}
                          disabled={selectedSubcategory !== 'Tyres'}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select season" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Summer">Summer</SelectItem>
                            <SelectItem value="Winter">Winter</SelectItem>
                            <SelectItem value="All-Season">All-Season</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}

            <TabsContent value="inventory" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="stockQuantity">Stock Quantity <span className="text-destructive">*</span></Label>
                    <Input
                      id="stockQuantity"
                      type="number"
                      {...register('stockQuantity', {
                        required: 'Stock quantity is required',
                        valueAsNumber: true,
                        min: { value: 0, message: 'Stock quantity cannot be negative' }
                      })}
                      placeholder="Enter stock quantity"
                      className={errors.stockQuantity ? 'border-destructive' : ''}
                    />
                    {errors.stockQuantity && (
                      <p className="text-sm text-destructive">{errors.stockQuantity.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="minimumOrderQuantity">Minimum Order Quantity</Label>
                    <Input
                      id="minimumOrderQuantity"
                      type="number"
                      {...register('minimumOrderQuantity', { valueAsNumber: true })}
                      placeholder="Enter minimum order quantity"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  {/* Only show retail price field for merchant accounts */}
                  {!isSupplier() && (
                    <div className="space-y-2">
                      <Label htmlFor="retailPrice">Retail Price (DZD)</Label>
                      <Input
                        id="retailPrice"
                        type="number"
                        step="0.01"
                        {...register('retailPrice', { valueAsNumber: true })}
                        placeholder="Enter retail price in DZD"
                      />
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="quotationRequestEnabled">Quotation Request</Label>
                    <Select
                      defaultValue={product.quotationRequestEnabled ? 'true' : 'false'}
                      onValueChange={(value) => setValue('quotationRequestEnabled', value === 'true')}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select option" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Enabled</SelectItem>
                        <SelectItem value="false">Disabled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="shipping" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="shippingOrigin">Shipping Origin <span className="text-destructive">*</span></Label>
                    <Select
                      onValueChange={(value) => setValue('shippingOrigin', value)}
                      defaultValue={watch('shippingOrigin') || ""}
                      {...register('shippingOrigin', { required: 'Shipping origin is required' })}
                    >
                      <SelectTrigger className={errors.shippingOrigin ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select shipping origin">
                          {watch('shippingOrigin') ? getFormattedCity(watch('shippingOrigin')) : "Select shipping origin"}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {ALGERIAN_CITIES.map((city) => (
                          <SelectItem key={city.code} value={city.code}>
                            {city.code} - {city.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.shippingOrigin && (
                      <p className="text-sm text-destructive">{errors.shippingOrigin.message}</p>
                    )}
                  </div>


                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label>Available Shipping Methods</Label>
                    <div className="flex items-center gap-2 mb-2">
                      <Input
                        value={newShippingMethod}
                        onChange={(e) => setNewShippingMethod(e.target.value)}
                        placeholder="Add shipping method"
                        className="flex-1"
                      />
                      <Button type="button" size="sm" onClick={addShippingMethod}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {shippingMethods.map((method, index) => (
                        <div key={index} className="flex items-center bg-muted rounded-md px-2 py-1">
                          <span className="text-sm">{method}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 p-0 ml-1"
                            onClick={() => removeShippingMethod(method)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>


                </div>
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-6">
              {/* Only show wholesale pricing tiers for supplier accounts */}
              {isSupplier() ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-lg font-medium">Wholesale Pricing Tiers</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addPricingTier}>
                      <Plus className="h-4 w-4 mr-2" /> Add Tier
                    </Button>
                  </div>

                  {pricingTiers.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No pricing tiers defined. Add a tier to define wholesale pricing.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {pricingTiers.map((tier, index) => (
                        <Card key={index} className="overflow-hidden">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-4">
                              <div className="flex-1">
                                <Label htmlFor={`tier-${index}-min`} className="mb-2 block">Min Quantity</Label>
                                <Input
                                  id={`tier-${index}-min`}
                                  type="number"
                                  value={tier.minQuantity}
                                  onChange={(e) => updatePricingTier(index, 'minQuantity', parseInt(e.target.value))}
                                  min="1"
                                />
                              </div>
                              <div className="flex-1">
                                <Label htmlFor={`tier-${index}-max`} className="mb-2 block">Max Quantity (optional)</Label>
                                <Input
                                  id={`tier-${index}-max`}
                                  type="number"
                                  value={tier.maxQuantity || ''}
                                  onChange={(e) => {
                                    const value = e.target.value ? parseInt(e.target.value) : undefined;
                                    updatePricingTier(index, 'maxQuantity', value as any);
                                  }}
                                  min={tier.minQuantity + 1}
                                  placeholder="No limit"
                                />
                              </div>
                              <div className="flex-1">
                                <Label htmlFor={`tier-${index}-price`} className="mb-2 block">Price per Unit</Label>
                                <Input
                                  id={`tier-${index}-price`}
                                  type="number"
                                  step="0.01"
                                  value={tier.price}
                                  onChange={(e) => updatePricingTier(index, 'price', parseFloat(e.target.value))}
                                  min="0"
                                />
                              </div>
                              <div className="flex items-end">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="text-destructive"
                                  onClick={() => removePricingTier(index)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>Wholesale pricing tiers are only available for supplier accounts.</p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <div className="flex-1 flex justify-start">
              <MagicAIButton
                primaryImage={primaryImage}
                additionalImages={additionalImages}
                description={watch('description')}
                onAutoFill={(data) => {
                  // Update form fields with AI-generated data
                  Object.entries(data).forEach(([key, value]) => {
                    if (value !== undefined) {
                      setValue(key as any, value);
                    }
                  });

                  // If vehicle types were provided, update the state
                  if (data.vehicleTypeCompatibility?.length) {
                    // Convert string array to VehicleCompatibility objects if needed
                    if (typeof data.vehicleTypeCompatibility[0] === 'string') {
                      const vehicleCompat = data.vehicleTypeCompatibility.map((type: string) => ({
                        id: type.toLowerCase().replace(/\s+/g, '-'),
                        type: 'car',
                        brand: type,
                        model: '',
                        displayName: type
                      }));
                      setVehicleCompatibility(vehicleCompat);
                    } else {
                      setVehicleCompatibility(data.vehicleTypeCompatibility);
                    }
                  }

                  toast.success('Product information auto-filled by AI');
                }}
                disabled={isSubmitting || (!primaryImage && additionalImages.length === 0 && !watch('description'))}
              />
            </div>

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
