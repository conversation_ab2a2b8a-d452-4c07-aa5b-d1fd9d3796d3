import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { <PERSON><PERSON><PERSON>, Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TyreProduct } from '../types/product.types';

interface MagicAIButtonProps {
  primaryImage?: string;
  additionalImages?: string[];
  description?: string;
  onAutoFill: (data: Partial<TyreProduct>) => void;
  disabled?: boolean;
}

export const MagicAIButton: React.FC<MagicAIButtonProps> = ({
  primaryImage,
  additionalImages = [],
  description,
  onAutoFill,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const hasImages = !!primaryImage || additionalImages.length > 0;
  const hasDescription = !!description && description.trim().length > 0;

  const runMagicAI = async () => {
    if (!hasImages && !hasDescription) {
      setError('Please upload at least one image or add a product description before using Magic AI');
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 5;
          return newProgress >= 95 ? 95 : newProgress;
        });
      }, 150);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // In a real implementation, we would send the images and description to an AI service
      // and get back product information
      
      // For now, we'll simulate a response with mock data
      const mockProductData: Partial<TyreProduct> = {
        name: hasDescription ? 'AI Generated Product Name' : 'Premium All-Season Tire',
        manufacturer: 'Continental',
        width: 205,
        aspectRatio: 55,
        rimDiameter: 16,
        loadIndex: 91,
        speedRating: 'V',
        season: 'All-Season',
        treadLife: '60,000 miles',
        tractionRating: 'A',
        temperatureRating: 'A',
        vehicleTypeCompatibility: ['Sedan', 'Compact SUV'],
        certifications: ['DOT', 'ECE'],
      };
      
      clearInterval(progressInterval);
      setProgress(100);
      
      // Notify the parent component of the auto-filled data
      onAutoFill(mockProductData);
      
      toast.success('Product information auto-filled successfully');
      
      // Close the dialog after a short delay to show 100% progress
      setTimeout(() => {
        setIsOpen(false);
        setIsProcessing(false);
      }, 500);
      
    } catch (error) {
      console.error('Error using Magic AI:', error);
      setError('Failed to analyze product. Please try again.');
      setIsProcessing(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="gap-1"
        onClick={() => setIsOpen(true)}
        disabled={disabled || (!hasImages && !hasDescription)}
      >
        <Sparkles className="h-4 w-4 text-amber-500" />
        Magic AI
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-amber-500" />
              Magic AI
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <p className="text-sm">
                Magic AI will analyze your product images and description to automatically fill in product details.
              </p>

              <div className="space-y-2 text-sm">
                <p className="font-medium">Available data for analysis:</p>
                <ul className="list-disc list-inside space-y-1 pl-2">
                  <li className={hasImages ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}>
                    {hasImages ? `${additionalImages.length + (primaryImage ? 1 : 0)} images available` : 'No images available'}
                  </li>
                  <li className={hasDescription ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}>
                    {hasDescription ? 'Product description available' : 'No product description available'}
                  </li>
                </ul>
              </div>

              {isProcessing && (
                <div className="space-y-2">
                  <Progress value={progress} className="h-2" />
                  <p className="text-xs text-center text-muted-foreground">
                    Analyzing product... {progress}%
                  </p>
                </div>
              )}

              <div className="rounded-md bg-muted/50 p-3 text-sm">
                <p className="font-medium mb-2">Magic AI will attempt to identify:</p>
                <ul className="list-disc list-inside space-y-1 pl-2 text-muted-foreground">
                  <li>Product name and manufacturer</li>
                  <li>Technical specifications (size, dimensions, etc.)</li>
                  <li>Performance ratings and certifications</li>
                  <li>Compatible vehicle types</li>
                  <li>Other relevant product details</li>
                </ul>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={runMagicAI}
              disabled={isProcessing || (!hasImages && !hasDescription)}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Run Magic AI
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
