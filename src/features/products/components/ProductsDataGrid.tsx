import React, { useState, useEffect, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { CategoryTableConfig } from '../types/product.types';
import { CellRenderer } from './cells/CellRenderer';
import { FilterPopover } from './filters/FilterPopover';
import { FilterPresetsMenu } from './filters/FilterPresetsMenu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Search,
  SlidersHorizontal,
  Loader2,
  Pencil,
  Trash,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UserContext';
// Dropdown menu imports removed as we're using direct buttons

interface ProductsDataGridProps {
  data: any[];
  tableConfig: CategoryTableConfig;
  isLoading?: boolean;
  onRowClick?: (row: any) => void;
  onCellEdit?: (rowId: string, columnId: string, value: any) => Promise<void> | void;
  onEditRow?: (row: any) => void;
  onDeleteRow?: (row: any) => void;
  onSelectionChange?: (selectedRowIds: string[]) => void;
  highlightedRowId?: string | null;
}

export const ProductsDataGrid: React.FC<ProductsDataGridProps> = ({
  data,
  tableConfig,
  isLoading = false,
  onRowClick,
  onCellEdit,
  onEditRow,
  onDeleteRow,
  onSelectionChange,
  highlightedRowId = null,
}) => {
  const navigate = useNavigate();
  const { userRole, isSupplier, isMerchant } = useUser();

  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [rowSelection, setRowSelection] = useState({});
  const [editingCell, setEditingCell] = useState<{rowId: string; columnId: string} | null>(null);
  const [lastEditedRowId, setLastEditedRowId] = useState<string | null>(null);
  const [highlightTimeout, setHighlightTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Handle cell edit with role-based permissions
  const handleCellEdit = async (rowId: string, columnId: string, value: any) => {
    console.log(`Attempting to edit cell: rowId=${rowId}, columnId=${columnId}, userRole=${userRole}`);

    // Check role-specific permissions
    if (
      (columnId === 'wholesalePricingTiers' && !isSupplier()) ||
      (columnId === 'retailPrice' && !isMerchant())
    ) {
      console.error(`Permission denied: ${userRole} cannot edit ${columnId}`);
      toast.error(`You don't have permission to edit this field as a ${userRole} user.`);
      setEditingCell(null);
      return;
    }

    setIsSaving(true);

    // Clear any existing highlight timeout
    if (highlightTimeout) {
      clearTimeout(highlightTimeout);
      setHighlightTimeout(null);
    }

    // Clear the editing cell state
    setEditingCell(null);

    try {
      console.log(`Saving cell edit: ${columnId} = ${JSON.stringify(value)}`);

      // Call the onCellEdit callback and wait for it to complete if it returns a Promise
      if (onCellEdit) {
        const result = onCellEdit(rowId, columnId, value);
        if (result instanceof Promise) {
          await result;
        }
        console.log(`Cell edit saved successfully`);
      } else {
        console.warn('No onCellEdit callback provided');
      }

      // Set the last edited row ID for highlighting
      setLastEditedRowId(rowId);

      // Set a timeout to clear the highlight after 3 seconds
      const timeout = setTimeout(() => {
        setLastEditedRowId(null);
      }, 3000);

      setHighlightTimeout(timeout);
    } catch (error) {
      console.error('Error updating cell:', error);
      toast.error('Failed to update cell. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Clean up the timeout when component unmounts
  useEffect(() => {
    return () => {
      if (highlightTimeout) {
        clearTimeout(highlightTimeout);
      }
    };
  }, [highlightTimeout]);



  // Update selection when rowSelection changes
  useEffect(() => {
    if (onSelectionChange) {
      const selectedRowIds = Object.keys(rowSelection);
      onSelectionChange(selectedRowIds);
    }
  }, [rowSelection, onSelectionChange]);

  // Create columns from table config
  const columns = useMemo<ColumnDef<any>[]>(() => {
    // Add selection column
    const selectionColumn: ColumnDef<any> = {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      ),
      enableSorting: false,
      enableColumnFilter: false,
      size: 40,
    };

    // Add action column with direct edit and delete buttons
    const actionColumn: ColumnDef<any> = {
      id: 'actions',
      header: () => <div className="text-center">Actions</div>,
      cell: ({ row }) => (
        <div
          className="flex items-center justify-center gap-2"
          data-action-cell="true"
          onClick={(e) => {
            // Prevent row click event from firing when clicking on the action cell
            e.stopPropagation();
          }}
        >
          {/* Edit Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
            onClick={(e) => {
              e.stopPropagation();
              // Call the onEditRow callback instead of navigating
              if (onEditRow) {
                onEditRow(row.original);
              } else {
                // Fallback to navigation if no callback is provided
                navigate(`/app/products/edit/${row.original.id}`);
              }
            }}
            title="Edit"
          >
            <Pencil className="h-4 w-4" />
            <span className="sr-only">Edit</span>
          </Button>

          {/* Delete Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-100"
            onClick={(e) => {
              e.stopPropagation();
              onDeleteRow?.(row.original);
            }}
            title="Delete"
          >
            <Trash className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
      ),
      enableSorting: false,
      enableColumnFilter: false,
      size: 100, // Slightly wider to accommodate two buttons
    };

    // Combine selection column, data columns, and action column
    try {
      // Ensure tableConfig and columns exist
      if (!tableConfig || !tableConfig.columns || !Array.isArray(tableConfig.columns)) {
        console.error('Invalid table configuration:', tableConfig);
        return [selectionColumn, actionColumn];
      }

      return [
        selectionColumn,
        ...tableConfig.columns.map(colDef => ({
        id: colDef.id,
        accessorKey: colDef.accessorKey,
        header: ({ column }) => {
        return (
          <div className="flex items-center gap-1 w-full">
            <span className="font-medium text-sm whitespace-normal">{colDef.header}</span>
            {colDef.enableFiltering && colDef.filterType && (
              <FilterPopover
                column={colDef}
                value={column.getFilterValue()}
                onChange={(value) => column.setFilterValue(value)}
                onClear={() => column.setFilterValue(undefined)}
              />
            )}
          </div>
        );
      },
      size: colDef.size,
      enableSorting: colDef.enableSorting,
      enableColumnFilter: colDef.enableFiltering,
      cell: ({ row, getValue }) => {
        const isEditing = editingCell?.rowId === row.id && editingCell?.columnId === colDef.id;

        // Check role-specific permissions for editing
        const canEdit = colDef.editable && !(
          (colDef.id === 'wholesalePricingTiers' && !isSupplier()) ||
          (colDef.id === 'retailPrice' && !isMerchant())
        );

        // Get the value for this cell
        const value = getValue();

        // Log for debugging
        if (colDef.id === 'wholesalePricingTiers' || colDef.id === 'retailPrice') {
          console.log(`Rendering ${colDef.id} cell for row ${row.id}:`, {
            value,
            canEdit,
            userRole,
            isSupplier: isSupplier(),
            isMerchant: isMerchant()
          });
        }

        return (
          <div
            className={cn(
              "w-full h-full",
              canEdit && "cursor-pointer hover:bg-muted/30"
            )}
            onClick={() => {
              if (canEdit && !isEditing) {
                console.log(`Starting edit for ${colDef.id} in row ${row.id}`);
                setEditingCell({ rowId: row.id, columnId: colDef.id });
              } else if (colDef.editable && !canEdit) {
                // Show a message if the user tries to edit a field they don't have permission for
                console.warn(`Permission denied: ${userRole} tried to edit ${colDef.id}`);
                toast.error(`You don't have permission to edit this field as a ${userRole} user.`);
              }
            }}
          >
            <CellRenderer
              column={colDef}
              value={value}
              onChange={(newValue) => handleCellEdit(row.id, colDef.id, newValue)}
              isEditable={isEditing}
              rowData={row.original}
            />
          </div>
        );
      },
    })),
        actionColumn,
      ];
    } catch (error) {
      console.error('Error creating columns:', error);
      return [selectionColumn, actionColumn];
    }
  }, [tableConfig, editingCell, onCellEdit, userRole, isSupplier, isMerchant, navigate]);

  // Initialize table
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    debugTable: true,
  });

  // Set default pagination
  useEffect(() => {
    table.setPageSize(50);
  }, [table]);

  // Virtualization setup
  const { rows } = table.getRowModel();
  const parentRef = React.useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    estimateSize: () => 48, // estimated row height
    getScrollElement: () => parentRef.current,
    overscan: 10,
  });

  // Calculate total width of all columns
  const totalWidth = useMemo(() => {
    return table.getAllColumns().reduce((acc, column) => {
      return acc + (column.getSize() || 150);
    }, 0);
  }, [table.getAllColumns()]);



  return (
    <div className="flex flex-col gap-4">
      {/* Search and filters */}
      <div className="flex items-center justify-between">
        <div className="relative w-72">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            className="pl-8"
          />
        </div>

        <div className="flex items-center gap-2">
          {/* Active filters count */}
          {table.getState().columnFilters.length > 0 && (
            <Badge variant="secondary" className="font-normal">
              <SlidersHorizontal className="h-3 w-3 mr-1" />
              {table.getState().columnFilters.length} active {table.getState().columnFilters.length === 1 ? 'filter' : 'filters'}
            </Badge>
          )}

          {/* Filter presets */}
          <FilterPresetsMenu
            columnFilters={table.getState().columnFilters}
            onApplyPreset={(filters) => {
              // Clear existing filters first
              table.resetColumnFilters();
              // Apply each filter from the preset
              filters.forEach(filter => {
                table.getColumn(filter.id)?.setFilterValue(filter.value);
              });
            }}
          />
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <div className="overflow-hidden">
          {/* Main scrollable container */}
          <div
            ref={parentRef}
            className="overflow-auto"
            style={{ height: '600px', width: '100%' }}
          >
            {/* Table header - now inside the scrollable container */}
            <div
              className="sticky top-0 z-20 border-b bg-[#f5f6f8] shadow-sm"
              style={{ width: `${Math.max(totalWidth, 800)}px` }}
            >
              {table.getHeaderGroups().map((headerGroup) => (
                <div
                  key={headerGroup.id}
                  className="flex"
                >
                  {headerGroup.headers.map((header) => (
                    <div
                      key={header.id}
                      className={cn(
                        "flex items-center border-r p-3 last:border-r-0 relative overflow-visible",
                        header.column.getCanSort() && "cursor-pointer select-none"
                      )}
                      style={{ width: `${header.getSize()}px` }}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div className="flex items-center justify-between w-full overflow-visible">
                        <div className="flex items-center pr-3 w-full overflow-visible">
                          <div className="w-full overflow-visible whitespace-normal">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                          </div>
                          <div className="flex-shrink-0 ml-1">
                            {{
                              asc: ' 🔼',
                              desc: ' 🔽',
                            }[header.column.getIsSorted() as string] ?? null}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>

            {/* Table body with virtualization */}
            {isLoading || isSaving ? (
              <div className="flex items-center justify-center h-24">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  <span className="text-sm text-muted-foreground">
                    {isLoading ? 'Loading data...' : 'Saving changes...'}
                  </span>
                </div>
              </div>
            ) : rows.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-24 text-muted-foreground">
                <p>No products found</p>
                <p className="text-sm">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div
                style={{
                  height: `${rowVirtualizer.getTotalSize()}px`,
                  width: `${Math.max(totalWidth, 800)}px`,
                  position: 'relative',
                }}
              >
                {rowVirtualizer.getVirtualItems().map((virtualRow) => {
                  const row = rows[virtualRow.index];
                  return (
                    <div
                      key={row.id}
                      className={cn(
                        "absolute flex border-b hover:bg-muted/50 transition-colors",
                        row.getIsSelected() && "bg-primary/5",
                        (lastEditedRowId === row.id || highlightedRowId === row.id) && "bg-yellow-50 dark:bg-yellow-900/20 animate-pulse-gentle"
                      )}
                      style={{
                        height: `${virtualRow.size}px`,
                        transform: `translateY(${virtualRow.start}px)`,
                        width: '100%'
                      }}
                      onClick={(e) => {
                        // Don't trigger row click when clicking on a cell that's being edited
                        if (editingCell?.rowId === row.id) return;

                        // Don't trigger row click when clicking on action buttons
                        const target = e.target as HTMLElement;
                        if (target.closest('[data-action-cell="true"]')) return;

                        onRowClick?.(row.original);
                      }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <div
                          key={cell.id}
                          className="flex items-center border-r p-2 last:border-r-0"
                          style={{ width: cell.column.columnDef.size ? `${cell.column.columnDef.size}px` : '150px' }}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </div>
                      ))}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between border-t p-2">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{' '}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <select
                className="h-8 w-16 rounded-md border border-input bg-transparent"
                value={table.getState().pagination.pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value));
                }}
              >
                {[10, 20, 50, 100].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
