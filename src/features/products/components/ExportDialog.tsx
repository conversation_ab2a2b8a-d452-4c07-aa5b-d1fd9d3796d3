import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { TyreProduct } from '../types/product.types';
import { exportToExcel, exportToCsv, prepareProductsForExport } from '../utils/import-export';
import { toast } from 'sonner';
import { FileDown, FileSpreadsheet, FileText, Loader2 } from 'lucide-react';
import { CategoryTableConfig } from '../types/product.types';

interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  products: TyreProduct[];
  selectedProductIds?: string[];
  tableConfig: CategoryTableConfig;
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onClose,
  products,
  selectedProductIds = [],
  tableConfig,
}) => {
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel'>('excel');
  const [exportScope, setExportScope] = useState<'all' | 'selected' | 'filtered'>(
    selectedProductIds.length > 0 ? 'selected' : 'all'
  );
  const [filename, setFilename] = useState<string>(`${tableConfig.id}_products_export`);
  const [selectedColumns, setSelectedColumns] = useState<string[]>(
    tableConfig.columns.map(col => col.id)
  );
  const [isExporting, setIsExporting] = useState<boolean>(false);

  // Handle column selection
  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns(prev => {
      if (prev.includes(columnId)) {
        return prev.filter(id => id !== columnId);
      } else {
        return [...prev, columnId];
      }
    });
  };

  // Handle select all columns
  const handleSelectAllColumns = () => {
    setSelectedColumns(tableConfig.columns.map(col => col.id));
  };

  // Handle deselect all columns
  const handleDeselectAllColumns = () => {
    setSelectedColumns([]);
  };

  // Handle export
  const handleExport = async () => {
    if (selectedColumns.length === 0) {
      toast.error('Please select at least one column to export');
      return;
    }

    setIsExporting(true);

    try {
      // Get products to export based on scope
      let productsToExport: TyreProduct[] = [];
      
      if (exportScope === 'all') {
        productsToExport = products;
      } else if (exportScope === 'selected') {
        productsToExport = products.filter(p => selectedProductIds.includes(p.id));
      } else {
        // For 'filtered', we're already showing filtered products
        productsToExport = products;
      }

      // Prepare data for export
      let exportData = prepareProductsForExport(productsToExport);

      // Filter columns
      exportData = exportData.map(item => {
        const filteredItem: Record<string, any> = {};
        selectedColumns.forEach(columnId => {
          const column = tableConfig.columns.find(col => col.id === columnId);
          if (column) {
            filteredItem[column.header] = item[columnId];
          }
        });
        return filteredItem;
      });

      // Export based on format
      if (exportFormat === 'excel') {
        exportToExcel(exportData, filename);
      } else {
        exportToCsv(exportData, filename);
      }

      toast.success(`Successfully exported ${productsToExport.length} products`);
      onClose();
    } catch (error) {
      console.error('Error exporting products:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to export products');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileDown className="h-5 w-5" />
            Export Products
          </DialogTitle>
          <DialogDescription>
            Export your products to CSV or Excel format
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Export Format</Label>
              <RadioGroup
                value={exportFormat}
                onValueChange={(value) => setExportFormat(value as 'csv' | 'excel')}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="excel" id="excel" />
                  <Label htmlFor="excel" className="flex items-center gap-1 cursor-pointer">
                    <FileSpreadsheet className="h-4 w-4" />
                    Excel (.xlsx)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="csv" id="csv" />
                  <Label htmlFor="csv" className="flex items-center gap-1 cursor-pointer">
                    <FileText className="h-4 w-4" />
                    CSV
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label>Export Scope</Label>
              <RadioGroup
                value={exportScope}
                onValueChange={(value) => setExportScope(value as 'all' | 'selected' | 'filtered')}
                className="flex flex-col gap-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all" className="cursor-pointer">
                    All Products ({products.length})
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="selected"
                    id="selected"
                    disabled={selectedProductIds.length === 0}
                  />
                  <Label
                    htmlFor="selected"
                    className={`cursor-pointer ${selectedProductIds.length === 0 ? 'text-muted-foreground' : ''}`}
                  >
                    Selected Products ({selectedProductIds.length})
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="filename">Filename</Label>
              <Input
                id="filename"
                value={filename}
                onChange={(e) => setFilename(e.target.value)}
                placeholder="Enter filename"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Columns to Export</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAllColumns}
                    className="h-7 text-xs"
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeselectAllColumns}
                    className="h-7 text-xs"
                  >
                    Deselect All
                  </Button>
                </div>
              </div>
              <div className="max-h-[200px] overflow-y-auto border rounded-md p-2">
                <div className="space-y-2">
                  {tableConfig.columns.map((column) => (
                    <div key={column.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`column-${column.id}`}
                        checked={selectedColumns.includes(column.id)}
                        onCheckedChange={() => handleColumnToggle(column.id)}
                      />
                      <Label
                        htmlFor={`column-${column.id}`}
                        className="cursor-pointer text-sm"
                      >
                        {column.header}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isExporting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || selectedColumns.length === 0}
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <FileDown className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
