import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TyreProduct } from '../types/product.types';
import { toast } from 'sonner';
import { Save, X, Loader2, Pencil } from 'lucide-react';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface QuickEditPanelProps {
  product: TyreProduct;
  onSave: (product: TyreProduct) => Promise<void>;
  onClose: () => void;
  className?: string;
}

export const QuickEditPanel: React.FC<QuickEditPanelProps> = ({
  product,
  onSave,
  onClose,
  className,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stockQuantity, setStockQuantity] = useState(product.stockQuantity);
  const [retailPrice, setRetailPrice] = useState(product.retailPrice || 0);
  const [status, setStatus] = useState(product.status);

  // Update state when product changes
  useEffect(() => {
    setStockQuantity(product.stockQuantity);
    setRetailPrice(product.retailPrice || 0);
    setStatus(product.status);
  }, [product]);

  // Handle save
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      const updatedProduct = {
        ...product,
        stockQuantity,
        retailPrice,
        status,
        updatedAt: new Date(),
      };

      await onSave(updatedProduct);
      toast.success('Product updated successfully');
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('Failed to update product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        handleSave();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose, stockQuantity, retailPrice, status]);

  return (
    <Card className={cn("w-80 shadow-lg", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Pencil className="h-4 w-4" />
          Quick Edit
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="font-medium">{product.name}</div>
          <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
          {product.barcode && (
            <div className="text-sm text-muted-foreground">Barcode: {product.barcode}</div>
          )}
        </div>

        <div className="space-y-3">
          <div className="space-y-1">
            <Label htmlFor="stockQuantity">Stock Quantity</Label>
            <Input
              id="stockQuantity"
              type="number"
              value={stockQuantity}
              onChange={(e) => setStockQuantity(Number(e.target.value))}
              min={0}
              className="h-9"
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="retailPrice">Retail Price (DZD)</Label>
            <Input
              id="retailPrice"
              type="number"
              step="0.01"
              value={retailPrice}
              onChange={(e) => setRetailPrice(Number(e.target.value))}
              min={0}
              className="h-9"
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="status">Status</Label>
            <select
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value as any)}
              className="w-full h-9 rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm"
            >
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="pending_approval">Pending Approval</option>
              <option value="out_of_stock">Out of Stock</option>
              <option value="discontinued">Discontinued</option>
            </select>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-3">
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          disabled={isSubmitting}
        >
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button
          size="sm"
          onClick={handleSave}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};
