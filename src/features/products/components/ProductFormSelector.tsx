import React, { useState, useEffect } from 'react';
import { TyreProduct, BrakeProduct } from '../types/product.types';
import { ProductFormDialog } from './ProductFormDialog';
import { BrakeProductFormDialog } from './BrakeProductFormDialog';
import { UnifiedProductFormDialog } from './UnifiedProductFormDialog';
import { ProductEditDialog } from './ProductEditDialog';
import { BrakeProductEditDialog } from './BrakeProductEditDialog';
import { getCategoryDisplayName } from '../utils/categoryMapping';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

interface ProductFormSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Partial<TyreProduct | BrakeProduct>) => Promise<void>;
  categories: { id: string; name: string }[];
  initialBarcode?: string;
  categoryId: string;
}

interface UnifiedProductFormSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: TyreProduct | BrakeProduct) => Promise<void>;
  initialProduct?: Partial<TyreProduct | BrakeProduct> | null;
  isEditMode: boolean;
  categories: { id: string; name: string }[];
}

export const ProductFormSelector: React.FC<ProductFormSelectorProps> = ({
  isOpen,
  onClose,
  onSave,
  categories,
  initialBarcode = '',
  categoryId,
}) => {
  // For unified table (all-other-categories), use the unified form
  if (categoryId === 'all-other-categories') {
    return (
      <UnifiedProductFormDialog
        isOpen={isOpen}
        onClose={onClose}
        onSave={onSave as (product: Partial<BrakeProduct>) => Promise<void>}
        initialBarcode={initialBarcode}
        defaultCategory="brakes"
      />
    );
  }

  // For specific categories, use category-specific forms
  if (categoryId === 'brakes') {
    return (
      <BrakeProductFormDialog
        isOpen={isOpen}
        onClose={onClose}
        onSave={onSave as (product: Partial<BrakeProduct>) => Promise<void>}
        categories={categories}
        initialBarcode={initialBarcode}
      />
    );
  }

  // Default to the tyre product form
  return (
    <ProductFormDialog
      isOpen={isOpen}
      onClose={onClose}
      onSave={onSave as (product: Partial<TyreProduct>) => Promise<void>}
      categories={categories}
      initialBarcode={initialBarcode}
    />
  );
};

interface ProductEditSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (product: Partial<TyreProduct | BrakeProduct>) => Promise<void>;
  product: TyreProduct | BrakeProduct;
  categories: { id: string; name: string }[];
}

export const ProductEditSelector: React.FC<ProductEditSelectorProps> = ({
  isOpen,
  onClose,
  onSave,
  product,
  categories,
}) => {
  // Render the appropriate form based on the product category
  if (product.category === 'brakes') {
    return (
      <BrakeProductEditDialog
        isOpen={isOpen}
        onClose={onClose}
        onSave={onSave as (product: Partial<BrakeProduct>) => Promise<void>}
        product={product as BrakeProduct}
        categories={categories}
      />
    );
  }

  // Default to the tyre product edit form
  return (
    <ProductEditDialog
      isOpen={isOpen}
      onClose={onClose}
      onSave={onSave as (product: Partial<TyreProduct>) => Promise<void>}
      product={product as TyreProduct}
      categories={categories}
    />
  );
};

export const UnifiedProductFormSelector: React.FC<UnifiedProductFormSelectorProps> = ({
  isOpen,
  onClose,
  onSave,
  initialProduct,
  isEditMode,
  categories,
}) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string>(initialProduct?.category || 'tyres');
  const [showForm, setShowForm] = useState<boolean>(!!initialProduct?.category || !isEditMode);

  // Update selected category when initialProduct changes
  useEffect(() => {
    if (initialProduct?.category) {
      setSelectedCategory(initialProduct.category);
      setShowForm(true);
    }
  }, [initialProduct]);

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setShowForm(true);
  };

  // If we're in edit mode and have a product, render the appropriate edit form
  if (isEditMode && initialProduct) {
    if (initialProduct.category === 'brakes') {
      return (
        <BrakeProductEditDialog
          isOpen={isOpen}
          onClose={onClose}
          onSave={onSave as (product: BrakeProduct) => Promise<void>}
          product={initialProduct as BrakeProduct}
          categories={categories}
        />
      );
    } else {
      // Default to tyres
      return (
        <ProductEditDialog
          isOpen={isOpen}
          onClose={onClose}
          onSave={onSave as (product: TyreProduct) => Promise<void>}
          product={initialProduct as TyreProduct}
          categories={categories}
        />
      );
    }
  }

  // For new products, first show category selection if no category is selected yet
  if (!showForm) {
    return (
      <Card className="bg-white rounded-lg shadow-sm">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">{t('products.selectCategory')}</h2>
          <p className="text-muted-foreground mb-6">
            {t('products.selectCategoryDescription', 'Select a product category to continue')}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant="outline"
                className="h-auto p-6 flex flex-col items-center justify-center gap-2 hover:bg-muted/50"
                onClick={() => handleCategorySelect(category.id)}
              >
                <span className="text-lg font-medium">{category.name}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // For new products with selected category, show the appropriate form
  if (selectedCategory === 'tyres') {
    return (
      <ProductFormDialog
        isOpen={isOpen}
        onClose={onClose}
        onSave={onSave as (product: Partial<TyreProduct>) => Promise<void>}
        categories={categories}
        initialBarcode={initialProduct?.barcode || ''}
        initialCategory={selectedCategory}
      />
    );
  } else {
    // For all other categories (brakes, filters, etc.), use unified form
    return (
      <UnifiedProductFormDialog
        isOpen={isOpen}
        onClose={onClose}
        onSave={onSave as (product: Partial<BrakeProduct>) => Promise<void>}
        initialBarcode={initialProduct?.barcode || ''}
        defaultCategory={selectedCategory || 'brakes'}
      />
    );
  }
};
