import { read, utils, write } from 'xlsx';
import { saveAs } from 'file-saver';
import <PERSON> from 'papaparse';
import { TyreProduct, BrakeProduct } from '../types/product.types';

/**
 * Parse an Excel file and return the data as an array of objects
 */
export const parseExcelFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          reject(new Error('Failed to read file'));
          return;
        }

        const workbook = read(data, { type: 'binary' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON with headers
        const jsonData = utils.sheet_to_json(worksheet, { header: 1 });

        // Extract headers (first row)
        const headers = jsonData[0] as string[];

        // Convert to array of objects
        const result = jsonData.slice(1).map((row: any) => {
          const obj: Record<string, any> = {};
          headers.forEach((header, index) => {
            obj[header] = row[index];
          });
          return obj;
        });

        resolve(result);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsBinaryString(file);
  });
};

/**
 * Parse a CSV file and return the data as an array of objects
 */
export const parseCsvFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        resolve(results.data);
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

/**
 * Export data to an Excel file
 */
export const exportToExcel = (data: any[], filename: string): void => {
  // Create a worksheet
  const worksheet = utils.json_to_sheet(data);

  // Create a workbook
  const workbook = utils.book_new();
  utils.book_append_sheet(workbook, worksheet, 'Products');

  // Generate Excel file
  const excelBuffer = write(workbook, { bookType: 'xlsx', type: 'array' });

  // Save file
  const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
  saveAs(blob, `${filename}.xlsx`);
};

/**
 * Export data to a CSV file
 */
export const exportToCsv = (data: any[], filename: string): void => {
  const csv = Papa.unparse(data);
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, `${filename}.csv`);
};

/**
 * Generate a template for importing products
 */
export const generateProductTemplate = (categoryId: string): any[] => {
  // Create a template with headers based on the category
  const baseTemplate = {
    name: '',
    sku: '',
    partArticleNumber: '',
    category: categoryId,
    subcategory: '',
    descriptionAndSpecifications: '',
    manufacturer: '',
    supplierName: '',
    stockQuantity: 0,
    retailPrice: 0,
    status: 'draft',
  };

  // Add category-specific fields
  if (categoryId === 'tyres') {
    return [{
      ...baseTemplate,
      width: '',
      aspectRatio: '',
      rimDiameter: '',
      loadIndex: '',
      speedRating: '',
      season: '',
    }];
  } else if (categoryId === 'brakes') {
    return [{
      ...baseTemplate,
      // No additional fields needed for brakes as we use the structured description
      // for technical specifications
      shippingOrigin: '',
    }];
  }

  return [baseTemplate];
};

/**
 * Map imported data to product objects
 */
export const mapImportedDataToProducts = (data: any[], categoryId: string): Partial<TyreProduct | BrakeProduct>[] => {
  return data.map(item => {
    const baseProduct: Partial<TyreProduct | BrakeProduct> = {
      name: item.name || '',
      sku: item.sku || '',
      barcode: item.barcode || '',
      category: categoryId,
      subcategory: item.subcategory || '',
      description: item.description || '',
      manufacturer: item.manufacturer || '',
      supplierName: item.supplierName || '',
      stockQuantity: Number(item.stockQuantity) || 0,
      retailPrice: Number(item.retailPrice) || 0,
      status: item.status || 'draft',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add category-specific fields
    if (categoryId === 'tyres') {
      return {
        ...baseProduct,
        width: Number(item.width) || undefined,
        aspectRatio: Number(item.aspectRatio) || undefined,
        rimDiameter: Number(item.rimDiameter) || undefined,
        loadIndex: Number(item.loadIndex) || undefined,
        speedRating: item.speedRating || undefined,
        season: item.season || undefined,
      };
    } else if (categoryId === 'brakes') {
      return {
        ...baseProduct,
        shippingOrigin: item.shippingOrigin || '',
        // Vehicle compatibility would be handled separately
      };
    }

    return baseProduct;
  });
};

/**
 * Validate imported products
 */
export const validateImportedProducts = (products: Partial<TyreProduct | BrakeProduct>[]): {
  valid: Partial<TyreProduct | BrakeProduct>[],
  invalid: { product: Partial<TyreProduct | BrakeProduct>, errors: string[] }[]
} => {
  const valid: Partial<TyreProduct | BrakeProduct>[] = [];
  const invalid: { product: Partial<TyreProduct | BrakeProduct>, errors: string[] }[] = [];

  products.forEach(product => {
    const errors: string[] = [];

    // Validate required fields for all products
    if (!product.name) errors.push('Name is required');
    if (!product.sku) errors.push('SKU is required');
    if (!product.manufacturer) errors.push('Manufacturer is required');

    // Category-specific validation
    if (product.category === 'brakes') {
      if (!product.description) errors.push('Description is required for brake products');
      if (!product.shippingOrigin) errors.push('Shipping origin is required');
    } else if (product.category === 'tyres') {
      // Tyre-specific validation
      const tyreProduct = product as Partial<TyreProduct>;
      if (tyreProduct.width === undefined) errors.push('Width is required for tyre products');
      if (tyreProduct.aspectRatio === undefined) errors.push('Aspect ratio is required for tyre products');
      if (tyreProduct.rimDiameter === undefined) errors.push('Rim diameter is required for tyre products');
      if (tyreProduct.loadIndex === undefined) errors.push('Load index is required for tyre products');
      if (!tyreProduct.speedRating) errors.push('Speed rating is required for tyre products');
    }

    // Add to appropriate array
    if (errors.length === 0) {
      valid.push(product);
    } else {
      invalid.push({ product, errors });
    }
  });

  return { valid, invalid };
};

/**
 * Prepare products for export
 */
export const prepareProductsForExport = (products: (TyreProduct | BrakeProduct)[]): any[] => {
  return products.map(product => {
    const baseProduct = {
      id: product.id,
      name: product.name,
      sku: product.sku,
      barcode: product.barcode || '',
      category: product.category,
      subcategory: product.subcategory || '',
      description: product.description || '',
      manufacturer: product.manufacturer,
      supplierName: product.supplierName || '',
      stockQuantity: product.stockQuantity,
      retailPrice: product.retailPrice || 0,
      status: product.status,
      shippingOrigin: product.shippingOrigin || '',
    };

    // Add category-specific fields
    if (product.category === 'tyres') {
      const tyreProduct = product as TyreProduct;
      return {
        ...baseProduct,
        width: tyreProduct.width || '',
        aspectRatio: tyreProduct.aspectRatio || '',
        rimDiameter: tyreProduct.rimDiameter || '',
        loadIndex: tyreProduct.loadIndex || '',
        speedRating: tyreProduct.speedRating || '',
        season: tyreProduct.season || '',
      };
    } else if (product.category === 'brakes') {
      const brakeProduct = product as BrakeProduct;
      return {
        ...baseProduct,
        // No additional fields needed for brakes as we use the structured description
        // for technical specifications
      };
    }

    return baseProduct;
  });
};
