/**
 * Utility functions for generating product IDs
 */

import { getCategoryPrefix as getCategoryPrefixFromData } from '@/data/categories';

/**
 * Get the correct prefix for a given category
 * @param category The product category
 * @returns The appropriate prefix for the category
 */
export const getCategoryPrefix = (category: string): string => {
  return getCategoryPrefixFromData(category);
};

/**
 * Generate a unique product ID with the appropriate prefix based on category and marketplace section
 * @param category The product category
 * @param marketplaceSection The marketplace section ('wholesale' or 'retail')
 * @param sequentialNumber Optional sequential number for consistent IDs
 * @returns A unique product ID with the appropriate prefix and suffix
 */
export const generateProductId = (
  category: string,
  marketplaceSection: 'wholesale' | 'retail' = 'retail',
  sequentialNumber?: number
): string => {
  // Use provided sequential number or generate a more robust unique number
  let seqNum: number;

  if (sequentialNumber !== undefined) {
    seqNum = sequentialNumber;
  } else {
    // Generate a more robust unique number using timestamp + random component
    const timestamp = Date.now();
    const randomComponent = Math.floor(Math.random() * 1000);
    // Take last 6 digits to ensure it fits the format, but add randomness to avoid collisions
    seqNum = parseInt((timestamp.toString() + randomComponent.toString().padStart(3, '0')).slice(-6));

    // Ensure it's at least 100000 to maintain 6-digit format
    if (seqNum < 100000) {
      seqNum = 100000 + seqNum;
    }
  }

  // Determine prefix based on category using centralized mapping
  const prefix = getCategoryPrefix(category);

  // Determine suffix based on marketplace section
  const suffix = marketplaceSection === 'wholesale' ? 'WHOLESALE' : 'RETAIL';

  // Return formatted ID
  return `${prefix}-${seqNum}-${suffix}`;
};

/**
 * Generate a product ID for a specific index (for consistent mock data)
 * @param category The product category
 * @param index The index for sequential numbering
 * @param marketplaceSection The marketplace section
 * @returns A consistent product ID
 */
export const generateProductIdForIndex = (
  category: string,
  index: number,
  marketplaceSection: 'wholesale' | 'retail'
): string => {
  return generateProductId(category, marketplaceSection, 100000 + index);
};

/**
 * Check if a product ID has the correct format for its category
 * @param id The product ID to check
 * @param category The product category
 * @returns True if the ID has the correct format, false otherwise
 */
export const hasCorrectIdFormat = (id: string, category: string): boolean => {
  const expectedPrefix = getCategoryPrefix(category);

  // New format: PREFIX-XXXXXX-SUFFIX (e.g., TYR-100001-WHOLESALE, PROD-455146-WHOLESALE)
  const newFormatPattern = /^(TYR|BRK|FLT|PROD)-\d{6}-(WHOLESALE|RETAIL)$/;

  if (newFormatPattern.test(id)) {
    // Accept PROD prefix for all categories as per user requirement
    return id.startsWith(`${expectedPrefix}-`) || id.startsWith('PROD-');
  }

  // Legacy format support: PREFIX-XXXXXX (for backward compatibility)
  return id.startsWith(`${expectedPrefix}-`) || id.startsWith('PROD-');
};

/**
 * Extract marketplace section from product ID
 * @param id The product ID
 * @returns The marketplace section or null if not found
 */
export const extractMarketplaceSectionFromId = (id: string): 'wholesale' | 'retail' | null => {
  if (id.endsWith('-WHOLESALE')) {
    return 'wholesale';
  } else if (id.endsWith('-RETAIL')) {
    return 'retail';
  }
  return null;
};

/**
 * Check if a product ID is valid (new format with suffix)
 * @param productId The product ID to validate
 * @returns True if the format is valid
 */
export const isValidProductId = (productId: string): boolean => {
  // Check for new format: PREFIX-XXXXXX-SUFFIX
  const newFormatPattern = /^(TYR|BRK|FLT|PROD)-\d{6}-(WHOLESALE|RETAIL)$/;

  if (newFormatPattern.test(productId)) {
    return true;
  }

  // Legacy format support: PREFIX-XXXXXX
  const legacyFormatPattern = /^(TYR|BRK|FLT|PROD)-\d{6}$/;
  return legacyFormatPattern.test(productId);
};

/**
 * Generate a unique product ID with database collision checking
 * @param category The product category
 * @param marketplaceSection The marketplace section
 * @param maxRetries Maximum number of retries if collision occurs
 * @returns A promise that resolves to a unique product ID
 */
export const generateUniqueProductId = async (
  category: string,
  marketplaceSection: 'wholesale' | 'retail' = 'retail',
  maxRetries: number = 5
): Promise<string> => {
  try {
    // Import supabase dynamically to avoid circular dependencies
    const { default: supabase } = await import('@/services/supabaseService');

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      const candidateId = generateProductId(category, marketplaceSection);

      // Check if this ID already exists in the database
      const { data, error } = await supabase
        .from('products')
        .select('id')
        .eq('id', candidateId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        // If there's an error other than "not found", log it but continue
        console.warn(`Error checking product ID uniqueness: ${error.message}`);
      }

      if (!data) {
        // ID doesn't exist, we can use it
        console.log(`Generated unique product ID: ${candidateId} (attempt ${attempt + 1})`);
        return candidateId;
      }

      console.log(`Product ID collision detected: ${candidateId}, retrying... (attempt ${attempt + 1})`);
    }

    // If we've exhausted retries, fall back to timestamp-based generation
    const fallbackId = generateProductId(category, marketplaceSection, Date.now() % 900000);
    console.warn(`Failed to generate unique ID after ${maxRetries} attempts, using fallback: ${fallbackId}`);
    return fallbackId;
  } catch (importError) {
    // If we can't import supabase, fall back to regular generation
    console.warn('Could not import supabase for ID collision checking, using regular generation:', importError);
    return generateProductId(category, marketplaceSection);
  }
};
