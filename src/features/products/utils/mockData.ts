import { TyreProduct, PricingTier, ProductStatus } from '../types/product.types';

// Helper function to generate random pricing tiers
const generatePricingTiers = (): PricingTier[] => {
  const tiers: PricingTier[] = [];

  // Base price between $50 and $200
  const basePrice = 50 + Math.random() * 150;

  // First tier (1-10 units)
  tiers.push({
    minQuantity: 1,
    maxQuantity: 10,
    price: basePrice,
  });

  // Second tier (11-50 units)
  tiers.push({
    minQuantity: 11,
    maxQuantity: 50,
    price: basePrice * 0.9, // 10% discount
  });

  // Third tier (51+ units)
  tiers.push({
    minQuantity: 51,
    price: basePrice * 0.8, // 20% discount
  });

  return tiers;
};

// Helper function to generate random status
const generateStatus = (): ProductStatus => {
  const statuses: ProductStatus[] = ['active', 'draft', 'pending_approval', 'out_of_stock', 'discontinued'];
  const weights = [0.7, 0.1, 0.1, 0.05, 0.05]; // 70% active, 10% draft, etc.

  const random = Math.random();
  let sum = 0;

  for (let i = 0; i < weights.length; i++) {
    sum += weights[i];
    if (random < sum) {
      return statuses[i];
    }
  }

  return 'active';
};

// Generate mock tyre products
export const generateMockTyreProducts = (count: number): TyreProduct[] => {
  const products: TyreProduct[] = [];

  const manufacturers = [
    'Michelin', 'Bridgestone', 'Continental', 'Pirelli', 'Goodyear',
    'Dunlop', 'Hankook', 'Yokohama', 'Toyo', 'Maxxis'
  ];

  const tyreModels = [
    'Pilot Sport 4', 'Potenza RE980AS', 'ExtremeContact DWS06', 'P Zero',
    'Eagle F1 Asymmetric', 'SP Sport Maxx', 'Ventus V12 evo2', 'ADVAN Sport V105',
    'Proxes T1 Sport', 'Premitra HP5'
  ];

  const widths = [185, 195, 205, 215, 225, 235, 245, 255, 265, 275, 285, 295, 305, 315];
  const aspectRatios = [35, 40, 45, 50, 55, 60, 65, 70];
  const rimDiameters = [15, 16, 17, 18, 19, 20, 21, 22];
  const speedRatings = ['H', 'V', 'W', 'Y', 'Z', 'R', 'S', 'T'];
  const seasons = ['Summer', 'Winter', 'All-Season'] as const;
  const vehicleTypes = [
    'Passenger Car', 'SUV', 'Truck', 'Off-Road',
    'Performance', 'Luxury', 'Compact', 'Sedan'
  ];

  for (let i = 0; i < count; i++) {
    const manufacturerIndex = Math.floor(Math.random() * manufacturers.length);
    const manufacturer = manufacturers[manufacturerIndex];
    const model = tyreModels[Math.floor(Math.random() * tyreModels.length)];

    const width = widths[Math.floor(Math.random() * widths.length)];
    const aspectRatio = aspectRatios[Math.floor(Math.random() * aspectRatios.length)];
    const rimDiameter = rimDiameters[Math.floor(Math.random() * rimDiameters.length)];
    const speedRating = speedRatings[Math.floor(Math.random() * speedRatings.length)];
    const loadIndex = 75 + Math.floor(Math.random() * 30); // 75-104
    const season = seasons[Math.floor(Math.random() * seasons.length)];

    const vehicleTypeCount = 1 + Math.floor(Math.random() * 3); // 1-3 vehicle types
    const vehicleTypeCompatibility: string[] = [];
    for (let j = 0; j < vehicleTypeCount; j++) {
      const vehicleType = vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];
      if (!vehicleTypeCompatibility.includes(vehicleType)) {
        vehicleTypeCompatibility.push(vehicleType);
      }
    }

    const stockQuantity = Math.floor(Math.random() * 100);
    const status = generateStatus();

    const product: TyreProduct = {
      id: `TYR-${100000 + i}`,
      name: `${manufacturer} ${model} ${width}/${aspectRatio}R${rimDiameter}`,
      sku: `${manufacturer.substring(0, 3).toUpperCase()}-${width}${aspectRatio}R${rimDiameter}-${loadIndex}${speedRating}`,
      barcode: `${Math.floor(Math.random() * 10000000000000)}`,
      category: 'tyres',
      subcategory: 'Tyres',
      description: `${manufacturer} ${model} ${width}/${aspectRatio}R${rimDiameter} ${loadIndex}${speedRating} ${season} tyre for ${vehicleTypeCompatibility.join(', ')}`,
      primaryImage: Math.random() > 0.7 ? '/placeholder.svg' : undefined,
      additionalImages: Math.random() > 0.8 ? Array(Math.floor(Math.random() * 3) + 1).fill('/placeholder.svg') : [],
      manufacturer,
      supplierName: 'Global Tyre Distributors',
      stockQuantity,
      wholesalePricingTiers: generatePricingTiers(),
      minimumOrderQuantity: 2,
      quotationRequestEnabled: Math.random() > 0.5,
      retailPrice: 80 + Math.random() * 200,
      width,
      aspectRatio,
      rimDiameter,
      loadIndex,
      speedRating,
      season,
      vehicleTypeCompatibility,
      treadLife: `${30000 + Math.floor(Math.random() * 40000)} miles`,
      tractionRating: ['A', 'AA', 'B', 'C'][Math.floor(Math.random() * 4)],
      temperatureRating: ['A', 'B', 'C'][Math.floor(Math.random() * 3)],
      certifications: ['DOT', 'ECE', 'ISO 9001'],
      shippingOrigin: ['China', 'Japan', 'Germany', 'USA', 'South Korea'][Math.floor(Math.random() * 5)],
      estimatedLeadTime: `${3 + Math.floor(Math.random() * 10)} days`,
      packagingDetails: 'Individual cardboard boxes, palletized for bulk orders',
      inventoryUpdateDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      status,
      adminNotes: Math.random() > 0.7 ? 'Special pricing available for bulk orders' : undefined,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
    };

    products.push(product);
  }

  return products;
};
