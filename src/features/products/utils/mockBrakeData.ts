import { BrakeProduct, PricingTier, ProductStatus } from '../types/product.types';

// Helper function to generate random pricing tiers
const generatePricingTiers = (): PricingTier[] => {
  const tiers: PricingTier[] = [];

  // Base price between $30 and $150
  const basePrice = 30 + Math.random() * 120;

  // First tier (1-10 units)
  tiers.push({
    minQuantity: 1,
    maxQuantity: 10,
    price: basePrice,
  });

  // Second tier (11-50 units)
  tiers.push({
    minQuantity: 11,
    maxQuantity: 50,
    price: basePrice * 0.9, // 10% discount
  });

  // Third tier (51+ units)
  tiers.push({
    minQuantity: 51,
    price: basePrice * 0.8, // 20% discount
  });

  return tiers;
};

// Helper function to generate random status
const generateStatus = (): ProductStatus => {
  const statuses: ProductStatus[] = ['active', 'draft', 'pending_approval', 'out_of_stock', 'discontinued'];
  const weights = [0.7, 0.1, 0.1, 0.05, 0.05]; // 70% active, 10% draft, etc.

  const random = Math.random();
  let sum = 0;

  for (let i = 0; i < weights.length; i++) {
    sum += weights[i];
    if (random < sum) {
      return statuses[i];
    }
  }

  return 'active';
};

/**
 * Generate mock brake products
 */
export const generateMockBrakeProducts = (count: number): BrakeProduct[] => {
  const products: BrakeProduct[] = [];

  // Sample data for brake products
  const manufacturers = [
    'Brembo', 'ATE', 'TRW', 'Bosch', 'Ferodo', 'Textar', 'Zimmermann', 'Pagid', 'EBC', 'Akebono'
  ];

  const brakeDiscModels = [
    'High Carbon', 'Sport', 'Premium', 'Coated', 'Vented', 'Drilled', 'Slotted', 'Composite', 'Ceramic'
  ];

  const brakePadModels = [
    'Ceramic', 'Semi-Metallic', 'Organic', 'Low-Dust', 'Performance', 'Heavy Duty', 'Sport', 'Racing'
  ];

  const subcategories = [
    'Brake Discs', 'Brake Pads', 'Brake Caliper', 'Brake Shoes', 'Brake Drums', 'Brake Fluid',
    'Brake Hose', 'ABS Sensor', 'Brake Caliper Repair Kit', 'Master Brake Cylinder'
  ];

  const vehicleCompatibility = [
    ['BMW 3 Series (E90/E91/E92/E93) 2005-2011', 'BMW 5 Series (E60/E61) 2003-2010', 'BMW X1 (E84) 2009-2015'],
    ['Audi A4 (B8) 2008-2015', 'Audi A6 (C6) 2004-2011', 'Audi Q5 (8R) 2008-2017'],
    ['Mercedes-Benz C-Class (W204) 2007-2014', 'Mercedes-Benz E-Class (W212) 2009-2016'],
    ['Volkswagen Golf Mk6 2008-2013', 'Volkswagen Passat B6 2005-2010', 'Volkswagen Tiguan 2007-2016'],
    ['Ford Focus Mk3 2011-2018', 'Ford Mondeo Mk4 2007-2014', 'Ford Kuga 2008-2012'],
    ['Toyota Corolla E140/E150 2006-2013', 'Toyota Avensis T27 2008-2018', 'Toyota RAV4 XA30 2005-2012'],
    ['Honda Civic 8th Gen 2006-2011', 'Honda Accord 8th Gen 2008-2012', 'Honda CR-V 3rd Gen 2007-2011'],
    ['Nissan Qashqai J10 2006-2013', 'Nissan X-Trail T31 2007-2013', 'Nissan Juke 2010-2019'],
  ];

  const shippingOrigins = ['China', 'Japan', 'Germany', 'USA', 'South Korea', 'Italy', 'Spain', 'France'];

  for (let i = 0; i < count; i++) {
    const manufacturerIndex = Math.floor(Math.random() * manufacturers.length);
    const manufacturer = manufacturers[manufacturerIndex];
    
    const subcategoryIndex = Math.floor(Math.random() * subcategories.length);
    const subcategory = subcategories[subcategoryIndex];
    
    let model = '';
    if (subcategory === 'Brake Discs') {
      model = brakeDiscModels[Math.floor(Math.random() * brakeDiscModels.length)];
    } else if (subcategory === 'Brake Pads') {
      model = brakePadModels[Math.floor(Math.random() * brakePadModels.length)];
    }
    
    const compatibilityIndex = Math.floor(Math.random() * vehicleCompatibility.length);
    const vehicleCompat = vehicleCompatibility[compatibilityIndex];
    
    const stockQuantity = Math.floor(Math.random() * 100) + 1;
    const status = generateStatus();
    
    // Generate structured description based on subcategory
    let description = '';
    if (subcategory === 'Brake Discs') {
      description = `## Product Identification
- Part Number: ${manufacturer.substring(0, 3).toUpperCase()}-${100000 + i}
- Brand: ${manufacturer}
- Component Type: Brake Disc
- Function: ${Math.random() > 0.5 ? 'Front Axle' : 'Rear Axle'}

## Technical Specifications
- Diameter: ${260 + Math.floor(Math.random() * 80)}mm
- Thickness: ${20 + Math.floor(Math.random() * 15)}mm
- Minimum Thickness: ${18 + Math.floor(Math.random() * 10)}mm
- Number of Holes: ${4 + Math.floor(Math.random() * 2)}
- Hole Circle Ø: ${100 + Math.floor(Math.random() * 40)}mm
- Centering Diameter: ${60 + Math.floor(Math.random() * 20)}mm
- Surface: ${Math.random() > 0.5 ? 'Coated' : 'Uncoated'}
- Brake Disc Type: ${Math.random() > 0.5 ? 'Internally Vented' : 'Solid'}
- Height: ${40 + Math.floor(Math.random() * 15)}mm

## Vehicle Compatibility
${vehicleCompat.map(v => `- ${v}`).join('\n')}

## Additional Information
- ${Math.random() > 0.5 ? 'High-carbon cast iron construction for improved thermal stability' : 'Premium quality cast iron for durability and performance'}
- ${Math.random() > 0.5 ? 'UV-coated for corrosion resistance' : 'Special anti-corrosion coating'}
- Precision balanced for smooth operation
- Meets or exceeds OE specifications`;
    } else if (subcategory === 'Brake Pads') {
      description = `## Product Identification
- Part Number: ${manufacturer.substring(0, 3).toUpperCase()}-${100000 + i}
- Brand: ${manufacturer}
- Component Type: Brake Pads
- Function: ${Math.random() > 0.5 ? 'Front Axle' : 'Rear Axle'}

## Technical Specifications
- Material: ${model}
- Width: ${100 + Math.floor(Math.random() * 50)}mm
- Height: ${50 + Math.floor(Math.random() * 30)}mm
- Thickness: ${15 + Math.floor(Math.random() * 10)}mm
- Wear Indicator: ${Math.random() > 0.5 ? 'Yes' : 'No'}
- Brake System: ${Math.random() > 0.5 ? 'Bosch' : 'ATE'}
- WVA Number: ${80000 + Math.floor(Math.random() * 10000)}

## Vehicle Compatibility
${vehicleCompat.map(v => `- ${v}`).join('\n')}

## Additional Information
- ${Math.random() > 0.5 ? 'Low dust formulation for cleaner wheels' : 'High performance compound for improved braking'}
- ${Math.random() > 0.5 ? 'Includes fitting accessories' : 'Hardware kit included'}
- Chamfered and slotted for noise reduction
- Meets or exceeds OE specifications`;
    } else {
      description = `## Product Identification
- Part Number: ${manufacturer.substring(0, 3).toUpperCase()}-${100000 + i}
- Brand: ${manufacturer}
- Component Type: ${subcategory}
- Function: ${Math.random() > 0.5 ? 'Front Axle' : 'Rear Axle'}

## Technical Specifications
- Material: High-grade ${Math.random() > 0.5 ? 'aluminum' : 'steel'}
- Weight: ${0.5 + Math.random() * 2.5}kg
- Dimensions: Various
- OE Quality: Yes

## Vehicle Compatibility
${vehicleCompat.map(v => `- ${v}`).join('\n')}

## Additional Information
- Direct replacement for original part
- Easy installation
- Meets or exceeds OE specifications
- 2-year manufacturer warranty`;
    }

    const product: BrakeProduct = {
      id: `BRK-${100000 + i}`,
      name: `${manufacturer} ${model} ${subcategory} ${Math.random() > 0.5 ? 'Front' : 'Rear'} ${Math.random() > 0.5 ? 'Axle' : ''}`.trim(),
      sku: `${manufacturer.substring(0, 3).toUpperCase()}-${subcategory.substring(0, 2).toUpperCase()}-${100000 + i}`,
      barcode: `${Math.floor(Math.random() * 10000000000000)}`,
      category: 'brakes',
      subcategory,
      description,
      primaryImage: Math.random() > 0.3 ? '/placeholder.svg' : undefined,
      additionalImages: Math.random() > 0.7 ? Array(Math.floor(Math.random() * 3) + 1).fill('/placeholder.svg') : [],
      manufacturer,
      supplierName: 'Global Brake Parts Distributors',
      stockQuantity,
      wholesalePricingTiers: generatePricingTiers(),
      minimumOrderQuantity: Math.random() > 0.5 ? 2 : 1,
      quotationRequestEnabled: Math.random() > 0.7,
      retailPrice: 40 + Math.random() * 150,
      vehicleCompatibility: vehicleCompat,
      certifications: ['ECE R90', 'ISO 9001', 'TÜV'],
      shippingOrigin: shippingOrigins[Math.floor(Math.random() * shippingOrigins.length)],
      availableShippingMethods: ['Standard', 'Express', 'Economy'],
      inventoryUpdateDate: new Date(),
      status,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      updatedAt: new Date(),
    };

    products.push(product);
  }

  return products;
};
