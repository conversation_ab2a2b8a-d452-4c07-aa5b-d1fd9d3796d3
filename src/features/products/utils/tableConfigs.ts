import { CategoryTableConfig } from '../types/product.types';

/**
 * Table configuration for All Other Categories (unified table for all categories except Tyres)
 */
export const allOtherCategoriesTableConfig: CategoryTableConfig = {
  id: 'all-other-categories',
  name: 'All Other Categories',
  description: 'Comprehensive parts and systems for all vehicle categories',
  columns: [
    {
      id: 'id',
      header: 'Product ID',
      accessorKey: 'id',
      enableSorting: true,
      enableFiltering: true,
      size: 120,
      cellType: 'text',
      filterType: 'text',
      editable: false,
      required: true,
    },
    {
      id: 'name',
      header: 'Product Name',
      accessorKey: 'name',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      minSize: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'sku',
      header: 'SKU',
      accessorKey: 'sku',
      enableSorting: true,
      enableFiltering: true,
      size: 120,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'partArticleNumber',
      header: 'Part Article Number',
      accessorKey: 'partArticleNumber',
      enableSorting: true,
      enableFiltering: true,
      size: 140,
      cellType: 'text',
      filterType: 'text',
      editable: true,
    },
    {
      id: 'category',
      header: 'Category',
      accessorKey: 'category',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'select',
      editable: false,
      required: true,
    },
    {
      id: 'subcategory',
      header: 'Subcategory',
      accessorKey: 'subcategory',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'select',
      editable: true,
      options: [
        { label: 'Vacuum Brake Booster', value: 'Vacuum Brake Booster' },
        { label: 'Vacuum Pump', value: 'Vacuum Pump' },
        { label: 'Brake Force Distributor', value: 'Brake Force Distributor' },
        { label: 'Master Brake Cylinder', value: 'Master Brake Cylinder' },
        { label: 'Repair Kit, Brake Master Cylinder', value: 'Repair Kit, Brake Master Cylinder' },
        { label: 'Brake Fluid Reservoir', value: 'Brake Fluid Reservoir' },
        { label: 'Brake Cylinder', value: 'Brake Cylinder' },
        { label: 'Repair Kit, Wheel Brake Cylinder', value: 'Repair Kit, Wheel Brake Cylinder' },
        { label: 'Brake Pads', value: 'Brake Pads' },
        { label: 'Brake Pad Wear Indicator', value: 'Brake Pad Wear Indicator' },
        { label: 'Accessory Kit, Disc Brake Pad', value: 'Accessory Kit, Disc Brake Pad' },
        { label: 'High Performance Brake Caliper', value: 'High Performance Brake Caliper' },
        { label: 'Drum Brake', value: 'Drum Brake' },
        { label: 'Brake Shoes', value: 'Brake Shoes' },
        { label: 'Accessories, Brake Shoe', value: 'Accessories, Brake Shoe' },
        { label: 'Brake Drum', value: 'Brake Drum' },
        { label: 'Brake Disc, Drum Brake', value: 'Brake Disc, Drum Brake' },
        { label: 'Drum Brake Regulator', value: 'Drum Brake Regulator' },
        { label: 'Brake Discs', value: 'Brake Discs' },
        { label: 'Brake Discs and Pads', value: 'Brake Discs and Pads' },
        { label: 'Anchor Plate', value: 'Anchor Plate' },
        { label: 'High Performance Brake Disc', value: 'High Performance Brake Disc' },
        { label: 'Handbrake', value: 'Handbrake' },
        { label: 'Handbrake Cable', value: 'Handbrake Cable' },
        { label: 'Handbrake Shoes', value: 'Handbrake Shoes' },
        { label: 'Repair Kit, Handbrake Axle', value: 'Repair Kit, Handbrake Axle' },
        { label: 'Switch, Handbrake Warning Light', value: 'Switch, Handbrake Warning Light' },
        { label: 'Brake Hose', value: 'Brake Hose' },
        { label: 'Brake Fluid', value: 'Brake Fluid' },
        { label: 'Brake Line', value: 'Brake Line' },
        { label: 'Vacuum Hose, Brake System', value: 'Vacuum Hose, Brake System' },
        { label: 'Pressure Accumulator, Brake System', value: 'Pressure Accumulator, Brake System' },
        { label: 'Pressure Switch, Hydraulic Brake System', value: 'Pressure Switch, Hydraulic Brake System' },
        { label: 'ABS Sensor', value: 'ABS Sensor' },
        { label: 'ABS Ring', value: 'ABS Ring' },
        { label: 'ABS Pump', value: 'ABS Pump' },
        { label: 'Overvoltage Relay, ABS', value: 'Overvoltage Relay, ABS' },
        { label: 'ESP Sensor', value: 'ESP Sensor' },
        { label: 'Control Unit, Brake/Vehicle Dynamics', value: 'Control Unit, Brake/Vehicle Dynamics' },
        { label: 'Brake Caliper', value: 'Brake Caliper' },
        { label: 'Brake Caliper Repair Kit', value: 'Brake Caliper Repair Kit' },
        { label: 'Guide Sleeve Set, Brake Caliper', value: 'Guide Sleeve Set, Brake Caliper' },
        { label: 'Brake Caliper Holder', value: 'Brake Caliper Holder' },
        { label: 'Piston, Caliper', value: 'Piston, Caliper' },
        { label: 'Brake Disc Screw', value: 'Brake Disc Screw' },
        { label: 'Brake Light Switch', value: 'Brake Light Switch' },
        { label: 'Threadlockers', value: 'Threadlockers' },
        { label: 'Multifunctional Lubricant', value: 'Multifunctional Lubricant' },
        { label: 'Mounting Paste', value: 'Mounting Paste' },
        { label: 'Spray Grease', value: 'Spray Grease' },
        { label: 'Brake Caliper Paint', value: 'Brake Caliper Paint' },
        { label: 'Brake Tools', value: 'Brake Tools' },
      ],
    },
    {
      id: 'description',
      header: 'Description',
      accessorKey: 'description',
      enableSorting: false,
      enableFiltering: true,
      size: 300,
      minSize: 200,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'vehicleTypeCompatibility',
      header: 'Vehicle Type Compatibility',
      accessorKey: 'vehicleTypeCompatibility',
      enableSorting: false,
      enableFiltering: false,
      size: 280,
      minSize: 200,
      cellType: 'vehicleCompatibility',
      editable: true,
    },
    {
      id: 'primaryImage',
      header: 'Primary Image',
      accessorKey: 'primaryImage',
      enableSorting: false,
      enableFiltering: false,
      size: 80,
      minSize: 60,
      cellType: 'image',
      editable: true,
      required: true,
    },
    {
      id: 'additionalImages',
      header: 'Additional Images',
      accessorKey: 'additionalImages',
      enableSorting: false,
      enableFiltering: false,
      size: 120,
      minSize: 80,
      cellType: 'multiImage',
      editable: true,
    },
    {
      id: 'manufacturer',
      header: 'Brand / Manufacturer',
      accessorKey: 'manufacturer',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'supplierName',
      header: 'Supplier Name',
      accessorKey: 'supplierName',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
    },
    {
      id: 'stockQuantity',
      header: 'Stock Quantity',
      accessorKey: 'stockQuantity',
      enableSorting: true,
      enableFiltering: true,
      size: 120,
      cellType: 'number',
      filterType: 'number',
      editable: true,
      required: true,
    },
    {
      id: 'wholesalePricingTiers',
      header: 'Wholesale Pricing Tiers',
      accessorKey: 'wholesalePricingTiers',
      enableSorting: false,
      enableFiltering: false,
      size: 180,
      cellType: 'pricingTiers',
      editable: true,
    },
    {
      id: 'minimumOrderQuantity',
      header: 'Minimum Order Quantity',
      accessorKey: 'minimumOrderQuantity',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'number',
      filterType: 'number',
      editable: true,
    },
    {
      id: 'quotationRequestEnabled',
      header: 'Quotation Request',
      accessorKey: 'quotationRequestEnabled',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'boolean',
      editable: true,
    },
    {
      id: 'retailPrice',
      header: 'Retail Price',
      accessorKey: 'retailPrice',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'number',
      filterType: 'number',
      editable: true,
    },
    {
      id: 'shippingOrigin',
      header: 'Shipping Origin',
      accessorKey: 'shippingOrigin',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'inventoryUpdateDate',
      header: 'Inventory Update Date',
      accessorKey: 'inventoryUpdateDate',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      cellType: 'date',
      filterType: 'date',
      editable: true,
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      enableSorting: true,
      enableFiltering: true,
      size: 160,
      cellType: 'status',
      filterType: 'select',
      editable: true,
      required: true,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Draft', value: 'draft' },
        { label: 'Pending Approval', value: 'pending_approval' },
        { label: 'Out of Stock', value: 'out_of_stock' },
        { label: 'Discontinued', value: 'discontinued' },
      ],
    },
  ],
  defaultSortColumn: 'name',
  defaultSortDirection: 'asc',
};

/**
 * Table configuration for Tyres & Related Products category
 */
export const tyresTableConfig: CategoryTableConfig = {
  id: 'tyres',
  name: 'Tyres & Related Products',
  description: 'Tyres, wheel covers, and related automotive products',
  columns: [
    {
      id: 'id',
      header: 'Product ID',
      accessorKey: 'id',
      enableSorting: true,
      enableFiltering: true,
      size: 120,
      cellType: 'text',
      filterType: 'text',
      editable: false,
      required: true,
    },
    {
      id: 'name',
      header: 'Product Name',
      accessorKey: 'name',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      minSize: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'sku',
      header: 'SKU',
      accessorKey: 'sku',
      enableSorting: true,
      enableFiltering: true,
      size: 120,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'partArticleNumber',
      header: 'Part Article Number',
      accessorKey: 'partArticleNumber',
      enableSorting: true,
      enableFiltering: true,
      size: 140,
      cellType: 'text',
      filterType: 'text',
      editable: true,
    },
    {
      id: 'category',
      header: 'Category',
      accessorKey: 'category',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'select',
      editable: false,
      required: true,
    },
    {
      id: 'subcategory',
      header: 'Subcategory',
      accessorKey: 'subcategory',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'select',
      editable: true,
      options: [
        { label: 'Tyres', value: 'Tyres' },
        { label: 'Wheel Covers', value: 'Wheel Covers' },
        { label: 'Tire Bag Sets', value: 'Tire Bag Sets' },
      ],
    },
    {
      id: 'description',
      header: 'Description',
      accessorKey: 'description',
      enableSorting: false,
      enableFiltering: true,
      size: 200,
      minSize: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
    },
    {
      id: 'primaryImage',
      header: 'Primary Image',
      accessorKey: 'primaryImage',
      enableSorting: false,
      enableFiltering: false,
      size: 80,
      minSize: 60,
      cellType: 'image',
      editable: true,
    },
    {
      id: 'additionalImages',
      header: 'Additional Images',
      accessorKey: 'additionalImages',
      enableSorting: false,
      enableFiltering: false,
      size: 120,
      minSize: 80,
      cellType: 'multiImage',
      editable: true,
    },
    {
      id: 'manufacturer',
      header: 'Brand / Manufacturer',
      accessorKey: 'manufacturer',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },
    {
      id: 'supplierName',
      header: 'Supplier Name',
      accessorKey: 'supplierName',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'text',
      editable: true,
    },
    {
      id: 'stockQuantity',
      header: 'Stock Quantity',
      accessorKey: 'stockQuantity',
      enableSorting: true,
      enableFiltering: true,
      size: 120,
      cellType: 'number',
      filterType: 'number',
      editable: true,
      required: true,
    },
    {
      id: 'wholesalePricingTiers',
      header: 'Wholesale Pricing Tiers',
      accessorKey: 'wholesalePricingTiers',
      enableSorting: false,
      enableFiltering: false,
      size: 180,
      cellType: 'pricingTiers',
      editable: true,
    },
    {
      id: 'minimumOrderQuantity',
      header: 'Minimum Order Quantity',
      accessorKey: 'minimumOrderQuantity',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'number',
      filterType: 'number',
      editable: true,
    },
    {
      id: 'quotationRequestEnabled',
      header: 'Quotation Request',
      accessorKey: 'quotationRequestEnabled',
      enableSorting: true,
      enableFiltering: true,
      size: 150,
      cellType: 'text',
      filterType: 'boolean',
      editable: true,
    },
    {
      id: 'retailPrice',
      header: 'Retail Price',
      accessorKey: 'retailPrice',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'number',
      filterType: 'number',
      editable: true,
    },
    {
      id: 'width',
      header: 'Width',
      accessorKey: 'width',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'number',
      filterType: 'number',
      editable: true,
      required: true,
    },
    {
      id: 'aspectRatio',
      header: 'Aspect Ratio / Height',
      accessorKey: 'aspectRatio',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      cellType: 'number',
      filterType: 'number',
      editable: true,
      required: true,
    },
    {
      id: 'rimDiameter',
      header: 'Diameter / Rim Size',
      accessorKey: 'rimDiameter',
      enableSorting: true,
      enableFiltering: true,
      size: 220,
      cellType: 'number',
      filterType: 'number',
      editable: true,
      required: true,
    },
    {
      id: 'loadIndex',
      header: 'Load Index',
      accessorKey: 'loadIndex',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'number',
      filterType: 'number',
      editable: true,
      required: true,
    },
    {
      id: 'speedRating',
      header: 'Speed Rating',
      accessorKey: 'speedRating',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'text',
      filterType: 'select',
      editable: true,
      required: true,
      options: [
        { label: 'Q', value: 'Q' },
        { label: 'R', value: 'R' },
        { label: 'S', value: 'S' },
        { label: 'T', value: 'T' },
        { label: 'U', value: 'U' },
        { label: 'H', value: 'H' },
        { label: 'V', value: 'V' },
        { label: 'W', value: 'W' },
        { label: 'Y', value: 'Y' },
      ],
    },
    {
      id: 'season',
      header: 'Season',
      accessorKey: 'season',
      enableSorting: true,
      enableFiltering: true,
      size: 160,
      cellType: 'text',
      filterType: 'select',
      editable: true,
      options: [
        { label: 'Summer', value: 'Summer' },
        { label: 'Winter', value: 'Winter' },
        { label: 'All-Season', value: 'All-Season' },
      ],
    },
    {
      id: 'vehicleTypeCompatibility',
      header: 'Vehicle Type Compatibility',
      accessorKey: 'vehicleTypeCompatibility',
      enableSorting: false,
      enableFiltering: false,
      size: 280,
      minSize: 200,
      cellType: 'vehicleCompatibility',
      editable: true,
    },
    {
      id: 'treadLife',
      header: 'Tread Life',
      accessorKey: 'treadLife',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'text',
      filterType: 'text',
      editable: true,
    },
    {
      id: 'tractionRating',
      header: 'Traction Rating',
      accessorKey: 'tractionRating',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      cellType: 'text',
      filterType: 'select',
      editable: true,
      options: [
        { label: 'AA', value: 'AA' },
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
      ],
    },
    {
      id: 'temperatureRating',
      header: 'Temperature Rating',
      accessorKey: 'temperatureRating',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      cellType: 'text',
      filterType: 'select',
      editable: true,
      options: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
      ],
    },

    {
      id: 'shippingOrigin',
      header: 'Shipping Origin',
      accessorKey: 'shippingOrigin',
      enableSorting: true,
      enableFiltering: true,
      size: 180,
      cellType: 'text',
      filterType: 'text',
      editable: true,
      required: true,
    },


    {
      id: 'inventoryUpdateDate',
      header: 'Inventory Update Date',
      accessorKey: 'inventoryUpdateDate',
      enableSorting: true,
      enableFiltering: true,
      size: 200,
      cellType: 'date',
      filterType: 'date',
      editable: true,
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      enableSorting: true,
      enableFiltering: true,
      size: 160,
      cellType: 'status',
      filterType: 'select',
      editable: true,
      required: true,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Draft', value: 'draft' },
        { label: 'Pending Approval', value: 'pending_approval' },
        { label: 'Out of Stock', value: 'out_of_stock' },
        { label: 'Discontinued', value: 'discontinued' },
      ],
    },


  ],
  defaultSortColumn: 'name',
  defaultSortDirection: 'asc',
};

/**
 * Map of all category table configurations
 */
export const categoryTableConfigs: Record<string, CategoryTableConfig> = {
  tyres: tyresTableConfig,
  'all-other-categories': allOtherCategoriesTableConfig,
  // Keep brakes for backward compatibility during transition
  brakes: allOtherCategoriesTableConfig,
};

/**
 * Get table configuration by category ID and user role
 */
export function getTableConfigForCategory(
  categoryId: string,
  userRole: string = 'supplier' // Default to supplier for backward compatibility
): CategoryTableConfig {
  try {
    console.log(`Getting table config for category: ${categoryId}, role: ${userRole}`);

    // Get the base configuration
    const baseConfig = categoryTableConfigs[categoryId] || tyresTableConfig;

    if (!baseConfig) {
      console.error(`No table configuration found for category: ${categoryId}, using tyres config as fallback`);
      return tyresTableConfig;
    }

    // Create a deep copy to avoid modifying the original
    let configCopy: CategoryTableConfig;
    try {
      configCopy = JSON.parse(JSON.stringify(baseConfig)) as CategoryTableConfig;
    } catch (error) {
      console.error('Error creating deep copy of table config:', error);
      return baseConfig; // Return the original if JSON operations fail
    }

    // Apply role-based column filtering
    try {
      // First pass: add role permissions to specific columns
      configCopy.columns = configCopy.columns.map(column => {
        // Add role-specific permissions to columns if not already defined
        if (column.id === 'wholesalePricingTiers' && !column.allowedRoles) {
          column.allowedRoles = ['supplier'];
        }

        if (column.id === 'retailPrice' && !column.allowedRoles) {
          column.allowedRoles = ['merchant'];
        }

        return column;
      });

      // Second pass: filter columns based on role
      configCopy.columns = configCopy.columns.filter(column => {
        // If allowedRoles is defined, check if the current role is allowed
        if (column.allowedRoles && Array.isArray(column.allowedRoles) &&
            !column.allowedRoles.includes(userRole as any)) {
          return false; // Hide this column for this role
        }

        // Keep all other columns
        return true;
      });
    } catch (error) {
      console.error('Error applying role-based column filtering:', error);
      // If filtering fails, return the unfiltered copy
      return configCopy;
    }

    return configCopy;
  } catch (error) {
    console.error('Unexpected error in getTableConfigForCategory:', error);
    // Return tyres config as a last resort fallback
    return tyresTableConfig;
  }
}
