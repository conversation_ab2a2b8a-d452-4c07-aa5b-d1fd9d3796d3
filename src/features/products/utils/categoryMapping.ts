/**
 * Utility functions for mapping category IDs to display names
 */

/**
 * Map of category IDs to their display names
 */
export const CATEGORY_DISPLAY_NAMES: Record<string, string> = {
  'tyres': 'Tyres & Related Products',
  'brakes': 'Brake Parts & Systems',
  // Add more categories as needed
};

/**
 * Get the display name for a category ID
 * @param categoryId The category ID
 * @returns The display name for the category, or the category ID if not found
 */
export const getCategoryDisplayName = (categoryId: string): string => {
  return CATEGORY_DISPLAY_NAMES[categoryId] || categoryId;
};
