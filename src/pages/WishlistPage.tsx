/**
 * Wishlist Page - Consumer Wishlist Management
 * 
 * Displays and manages consumer's wishlist items
 * 🎯 CRITICAL: Preserves existing authentication system - integrates with phone auth
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { FilterProvider } from '@/contexts/FilterContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Heart,
  ShoppingCart,
  Trash2,
  ArrowLeft,
  Star,
  Package,
  AlertCircle
} from 'lucide-react';
import { getConsumerWishlist, removeFromWishlist, WishlistItem } from '@/services/wishlistService';
import { useCart } from '@/contexts/CartContext';
import { ConsumerAuthGuard } from '@/components/auth/ConsumerAuthGuard';

export default function WishlistPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { addItem } = useCart();
  
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const [removingItems, setRemovingItems] = useState<Set<string>>(new Set());

  // Check authentication and load wishlist
  useEffect(() => {
    const checkAuthAndLoadWishlist = async () => {
      try {
        // Check authentication
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        
        if (!isAuth) {
          navigate('/');
          return;
        }
        
        setIsConsumerAuthenticated(true);
        
        // Load wishlist
        const result = await getConsumerWishlist();
        if (result.success) {
          setWishlistItems(result.items || []);
        } else {
          toast.error(t('marketplace.wishlistError'), {
            description: result.error || t('marketplace.wishlistErrorDescription')
          });
        }
      } catch (error) {
        console.error('Error loading wishlist:', error);
        toast.error(t('marketplace.wishlistError'), {
          description: t('marketplace.wishlistErrorDescription')
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndLoadWishlist();
  }, [navigate, toast, t]);

  // Handle remove from wishlist
  const handleRemoveFromWishlist = async (productId: string) => {
    setRemovingItems(prev => new Set(prev).add(productId));
    
    try {
      const result = await removeFromWishlist(productId);
      if (result.success) {
        setWishlistItems(prev => prev.filter(item => item.product_id !== productId));
        toast.success(t('marketplace.removedFromWishlist'), {
          description: t('marketplace.removedFromWishlistDescription')
        });
        
        // Dispatch event to update wishlist count in header
        window.dispatchEvent(new CustomEvent('wishlist:updated'));
      } else {
        toast.error(t('marketplace.wishlistError'), {
          description: result.error || t('marketplace.wishlistErrorDescription')
        });
      }
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast.error(t('marketplace.wishlistError'), {
        description: t('marketplace.wishlistErrorDescription')
      });
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  };

  // Handle add to cart
  const handleAddToCart = (item: WishlistItem) => {
    try {
      // Convert product ID string to number for cart
      const getCartItemId = (productId: string): number => {
        let hash = 0;
        for (let i = 0; i < productId.length; i++) {
          const char = productId.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash;
        }
        return Math.abs(hash);
      };

      addItem({
        id: getCartItemId(item.product_id),
        name: item.product_name,
        price: item.product_price || 0,
        image: item.product_image || '',
        manufacturer: item.product_manufacturer || '',
        quantity: 1
      });

      toast.success(t('marketplace.addToCart'), {
        description: `${item.product_name} ${t('marketplace.addedToCart')}`
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart', {
        description: 'Please try again'
      });
    }
  };

  // Navigate to product page
  const handleProductClick = (productId: string) => {
    navigate(`/${productId}`);
  };

  if (!isConsumerAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <ConsumerAuthGuard
      action="wishlist"
      fallback={
        <FilterProvider>
          <MarketplaceLayout>
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="text-center">
                <Heart className="h-16 w-16 text-[#fa7b00] mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Your Wishlist</h2>
                <p className="text-gray-600 mb-6">Save your favorite products and shop them later.</p>
                <button
                  onClick={() => {
                    const authModal = document.getElementById('auth-modal-trigger');
                    if (authModal) {
                      authModal.click();
                    }
                  }}
                  className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white px-6 py-3 rounded-lg font-medium"
                >
                  {t('auth.loginOrSignUp')}
                </button>
              </div>
            </div>
          </MarketplaceLayout>
        </FilterProvider>
      }
    >
      <FilterProvider>
        <MarketplaceLayout>
          <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/')}
                  className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t('actions.back')}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                    <Heart className="h-8 w-8 text-[#fa7b00]" />
                    {t('marketplace.myWishlist')}
                  </h1>
                  <p className="text-gray-600 mt-2">
                    {wishlistItems.length} {wishlistItems.length === 1 ? t('marketplace.item') : t('marketplace.items')}
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(8)].map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <Skeleton className="h-48 w-full" />
                    <CardContent className="p-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-4" />
                      <div className="flex gap-2">
                        <Skeleton className="h-10 flex-1" />
                        <Skeleton className="h-10 w-10" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : wishlistItems.length === 0 ? (
              <div className="text-center py-16">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {t('marketplace.wishlistEmpty')}
                </h3>
                <p className="text-gray-600 mb-6">
                  {t('marketplace.wishlistEmptyDescription')}
                </p>
                <Button
                  onClick={() => navigate('/')}
                  className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                >
                  {t('marketplace.continueShopping')}
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {wishlistItems.map((item) => (
                  <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <img
                        src={item.product_image || '/placeholder-product.jpg'}
                        alt={item.product_name}
                        className="w-full h-48 object-cover cursor-pointer"
                        onClick={() => handleProductClick(item.product_id)}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFromWishlist(item.product_id)}
                        disabled={removingItems.has(item.product_id)}
                        className="absolute top-2 right-2 bg-white/80 hover:bg-white text-red-500 hover:text-red-600 rounded-full p-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <CardContent className="p-4">
                      <h3 
                        className="font-semibold text-gray-900 mb-2 line-clamp-2 cursor-pointer hover:text-[#fa7b00]"
                        onClick={() => handleProductClick(item.product_id)}
                      >
                        {item.product_name}
                      </h3>
                      
                      {item.product_manufacturer && (
                        <p className="text-sm text-gray-600 mb-2">
                          {item.product_manufacturer}
                        </p>
                      )}
                      
                      {item.product_price && (
                        <p className="text-lg font-bold text-[#fa7b00] mb-4">
                          {item.product_price.toLocaleString()} DA
                        </p>
                      )}
                      
                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleAddToCart(item)}
                          className="flex-1 bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          {t('marketplace.addToCart')}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
            </div>
          </div>
        </MarketplaceLayout>
      </FilterProvider>
    </ConsumerAuthGuard>
  );
}
