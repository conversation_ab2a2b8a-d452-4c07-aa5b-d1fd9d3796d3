import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Package,
  User,
  MapPin,
  Phone,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  Truck,
  AlertCircle,
  ExternalLink,
  Building2,
  CreditCard,
  Users,
  RefreshCw,
  Edit
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  getShippingOrderById,
  updateShippingOrderStatus,
  ShippingOrderWithDetails
} from '@/services/shippingCompanyOrderService';
import {
  getCurrentShippingCompanySession,
  logoutShippingCompany
} from '@/services/shippingCompanyAuthService';

export default function ShippingOrderDetailPage() {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Authentication state
  const [companySession, setCompanySession] = useState(getCurrentShippingCompanySession());

  // Data state
  const [order, setOrder] = useState<ShippingOrderWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // Update dialog state
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [updateData, setUpdateData] = useState({
    status: '',
    tracking_number: '',
    driver_name: '',
    driver_phone: '',
    estimated_delivery_date: '',
    notes: ''
  });

  // Check authentication
  useEffect(() => {
    if (!companySession) {
      console.log('🚚 No valid session, redirecting to login...');
      navigate('/shipping/login');
      return;
    }
    console.log('🚚 Authenticated as:', companySession.company_name);
  }, [companySession, navigate]);

  // Load order details
  const loadOrderDetails = async () => {
    if (!orderId || !companySession) return;

    try {
      setIsLoading(true);
      console.log('🔍 [SHIPPING_ORDER_DETAIL] Loading order details for:', orderId);

      const result = await getShippingOrderById(orderId);
      if (result.success && result.order) {
        setOrder(result.order);
        
        // Initialize update form with current data
        const shipment = result.order.shipments?.[0];
        setUpdateData({
          status: shipment?.status || 'assigned',
          tracking_number: shipment?.tracking_number || '',
          driver_name: shipment?.driver_name || '',
          driver_phone: shipment?.driver_phone || '',
          estimated_delivery_date: shipment?.estimated_delivery_date || '',
          notes: shipment?.notes || ''
        });
      } else {
        toast({
          title: 'Failed to load order',
          description: result.error || 'Please try again',
          variant: 'destructive'
        });
        navigate('/shipping/dashboard');
      }
    } catch (error) {
      console.error('❌ [SHIPPING_ORDER_DETAIL] Error loading order:', error);
      toast({
        title: 'Failed to load order',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!order || !companySession) return;

    try {
      setIsUpdating(true);
      console.log('🔄 [SHIPPING_ORDER_DETAIL] Updating order status...', updateData);

      const result = await updateShippingOrderStatus(order.id, updateData);

      if (result.success) {
        toast({
          title: 'Order Updated Successfully',
          description: 'Order status and details have been updated.',
        });

        // Refresh order details
        await loadOrderDetails();
        setIsUpdateDialogOpen(false);
      } else {
        toast({
          title: 'Update Failed',
          description: result.error || 'Failed to update order',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('❌ [SHIPPING_ORDER_DETAIL] Error updating order:', error);
      toast({
        title: 'Update Failed',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle logout
  const handleLogout = () => {
    logoutShippingCompany();
    navigate('/shipping/login');
  };

  useEffect(() => {
    if (companySession && orderId) {
      loadOrderDetails();
    }
  }, [companySession, orderId]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'assigned': return 'secondary';
      case 'picked_up': return 'default';
      case 'in_transit': return 'default';
      case 'delivered': return 'default';
      case 'failed_delivery': return 'destructive';
      default: return 'secondary';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'assigned': return Clock;
      case 'picked_up': return Package;
      case 'in_transit': return Truck;
      case 'delivered': return CheckCircle;
      case 'failed_delivery': return AlertCircle;
      default: return Clock;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-[#fa7b00] rounded-lg flex items-center justify-center">
                  <Truck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {companySession?.company_name || 'Shipping Portal'}
                  </h1>
                  <p className="text-gray-600">Order Details</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" onClick={() => navigate('/shipping/dashboard')}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
                <Button variant="outline" onClick={handleLogout}>
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="container mx-auto px-4 py-8">
          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-100 rounded-lg animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Order not found</h2>
          <p className="text-gray-600 mb-6">The order you're looking for doesn't exist or is not assigned to your company.</p>
          <Button onClick={() => navigate('/shipping/dashboard')} className="bg-[#fa7b00] hover:bg-[#fa7b00]/90">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const shipment = order.shipments?.[0];
  const StatusIcon = getStatusIcon(shipment?.status || 'assigned');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-[#fa7b00] rounded-lg flex items-center justify-center">
                <Truck className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {companySession?.company_name || 'Shipping Portal'}
                </h1>
                <p className="text-gray-600">Order Details</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={() => navigate('/shipping/dashboard')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Order Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                  <StatusIcon className="h-8 w-8 text-[#fa7b00]" />
                  {order.order_number}
                </h1>
                <p className="text-gray-600 mt-1">
                  Order placed on {formatDate(order.created_at)}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Badge variant={getStatusBadgeVariant(shipment?.status || 'assigned')} className="text-sm px-3 py-1">
                {(shipment?.status || 'assigned').replace('_', ' ').toUpperCase()}
              </Badge>
              <Button
                onClick={() => setIsUpdateDialogOpen(true)}
                className="bg-[#fa7b00] hover:bg-[#e56b00] text-white"
              >
                <Edit className="h-4 w-4 mr-2" />
                Update Status
              </Button>
              <Button
                onClick={() => loadOrderDetails()}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Order Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Customer Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#fa7b00] rounded-full"></div>
                      <div>
                        <span className="text-sm text-gray-600">Name</span>
                        <p className="font-medium text-gray-900">
                          {order.consumer_name || 'Consumer'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-[#fa7b00]" />
                      <div>
                        <span className="text-sm text-gray-600">Phone</span>
                        <p className="font-medium text-gray-900">{order.consumer_phone}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Delivery Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Delivery Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#fa7b00] rounded-full"></div>
                      <div>
                        <span className="text-sm text-gray-600">Wilaya</span>
                        <p className="font-medium text-gray-900">{order.delivery_wilaya}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-4 w-4 text-[#fa7b00]" />
                      <div>
                        <span className="text-sm text-gray-600">Payment Method</span>
                        <p className="font-medium text-gray-900">
                          {order.payment_method === 'cash_on_delivery' ? 'Cash on Delivery' : 'Store Pickup'}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-[#fa7b00] rounded-full mt-2"></div>
                    <div className="flex-1">
                      <span className="text-sm text-gray-600">Address</span>
                      <p className="font-medium text-gray-900 mt-1">{order.delivery_address}</p>
                    </div>
                  </div>
                  {order.special_instructions && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-[#fa7b00] rounded-full mt-2"></div>
                      <div className="flex-1">
                        <span className="text-sm text-gray-600">Special Instructions</span>
                        <p className="font-medium text-gray-900 mt-1">{order.special_instructions}</p>
                      </div>
                    </div>
                  )}
                  <div className="pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(order.google_maps_url, '_blank')}
                      className="text-[#fa7b00] border-[#fa7b00] hover:bg-[#fa7b00] hover:text-white"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View on Google Maps
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Order Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Order Items ({order.order_items?.length || 0})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.order_items?.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                        <img
                          src={item.product_image || '/placeholder.svg'}
                          alt={item.product_name}
                          className="w-16 h-16 object-cover rounded-md"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 truncate">
                            {item.product_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {item.quantity} × {formatCurrency(item.unit_price)}
                          </p>
                          <p className="text-xs text-gray-400">
                            Supplier: {item.supplier_name} • {item.supplier_wilaya}
                          </p>
                          <p className="text-xs text-gray-400">
                            Phone: {item.supplier_phone}
                          </p>
                        </div>
                        <p className="font-semibold text-lg">
                          {formatCurrency(item.total_price)}
                        </p>
                      </div>
                    ))}
                  </div>

                  <Separator className="my-4" />

                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-lg">Total Order Value:</span>
                    <span className="font-bold text-xl text-[#fa7b00]">
                      {formatCurrency(order.total_amount)}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Shipment Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="h-5 w-5" />
                    Shipment Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Status</span>
                    <Badge variant={getStatusBadgeVariant(shipment?.status || 'assigned')}>
                      {(shipment?.status || 'assigned').replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                  {shipment?.tracking_number && (
                    <div className="flex justify-between text-sm">
                      <span>Tracking Number</span>
                      <span className="font-medium">{shipment.tracking_number}</span>
                    </div>
                  )}
                  {shipment?.driver_name && (
                    <div className="flex justify-between text-sm">
                      <span>Driver</span>
                      <span className="font-medium">{shipment.driver_name}</span>
                    </div>
                  )}
                  {shipment?.driver_phone && (
                    <div className="flex justify-between text-sm">
                      <span>Driver Phone</span>
                      <span className="font-medium">{shipment.driver_phone}</span>
                    </div>
                  )}
                  {shipment?.estimated_delivery_date && (
                    <div className="flex justify-between text-sm">
                      <span>Est. Delivery</span>
                      <span className="font-medium">
                        {new Date(shipment.estimated_delivery_date).toLocaleDateString('fr-DZ')}
                      </span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>Delivery Fee</span>
                    <span>{formatCurrency(shipment?.delivery_cost || 500)}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Order Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Order Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-[#fa7b00] rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Order Placed</p>
                        <p className="text-xs text-gray-500">{formatDate(order.created_at)}</p>
                      </div>
                    </div>
                    {shipment?.assigned_at && (
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <div>
                          <p className="font-medium text-sm">Assigned to Shipping</p>
                          <p className="text-xs text-gray-500">{formatDate(shipment.assigned_at)}</p>
                        </div>
                      </div>
                    )}
                    {shipment?.picked_up_at && (
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div>
                          <p className="font-medium text-sm">Picked Up</p>
                          <p className="text-xs text-gray-500">{formatDate(shipment.picked_up_at)}</p>
                        </div>
                      </div>
                    )}
                    {shipment?.delivered_at && (
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <div>
                          <p className="font-medium text-sm">Delivered</p>
                          <p className="text-xs text-gray-500">{formatDate(shipment.delivered_at)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Update Status Dialog */}
          <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Update Order Status</DialogTitle>
                <DialogDescription>
                  Update the shipment status and details for this order.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={updateData.status}
                      onValueChange={(value) => setUpdateData(prev => ({ ...prev, status: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="assigned">Assigned</SelectItem>
                        <SelectItem value="picked_up">Picked Up</SelectItem>
                        <SelectItem value="in_transit">In Transit</SelectItem>
                        <SelectItem value="delivered">Delivered</SelectItem>
                        <SelectItem value="failed_delivery">Failed Delivery</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tracking_number">Tracking Number</Label>
                    <Input
                      id="tracking_number"
                      value={updateData.tracking_number}
                      onChange={(e) => setUpdateData(prev => ({ ...prev, tracking_number: e.target.value }))}
                      placeholder="Enter tracking number"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="driver_name">Driver Name</Label>
                    <Input
                      id="driver_name"
                      value={updateData.driver_name}
                      onChange={(e) => setUpdateData(prev => ({ ...prev, driver_name: e.target.value }))}
                      placeholder="Enter driver name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="driver_phone">Driver Phone</Label>
                    <Input
                      id="driver_phone"
                      value={updateData.driver_phone}
                      onChange={(e) => setUpdateData(prev => ({ ...prev, driver_phone: e.target.value }))}
                      placeholder="Enter driver phone"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="estimated_delivery_date">Estimated Delivery Date</Label>
                  <Input
                    id="estimated_delivery_date"
                    type="date"
                    value={updateData.estimated_delivery_date}
                    onChange={(e) => setUpdateData(prev => ({ ...prev, estimated_delivery_date: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={updateData.notes}
                    onChange={(e) => setUpdateData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Enter any additional notes..."
                    rows={3}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsUpdateDialogOpen(false)}
                  disabled={isUpdating}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleStatusUpdate}
                  disabled={isUpdating}
                  className="bg-[#fa7b00] hover:bg-[#e56b00]"
                >
                  {isUpdating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Update Order
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
