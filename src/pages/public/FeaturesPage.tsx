import React from 'react';
import { useTranslation } from 'react-i18next';
import { PublicLayout } from '@/components/layout/PublicLayout';
import { FeatureCard } from '@/components/public/FeatureCard';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Database, ShoppingBag, Cpu, Store, 
  Smartphone, ArrowRight, BarChart4, 
  Truck, Scan, Printer, CreditCard, Search,
  ShieldCheck, Zap, Layers, RefreshCw
} from 'lucide-react';

export default function FeaturesPage() {
  const { t } = useTranslation();
  
  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-midnight-blue mb-6">
              {t('public.featuresTitle')}
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              {t('public.featuresSubtitle')}
            </p>
          </div>
        </div>
      </section>
      
      {/* Inventory Data Grid Section */}
      <section className="py-20">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center rounded-full border border-electric-orange/30 bg-electric-orange/10 px-3 py-1 text-sm text-electric-orange mb-4">
                <span className="font-medium">
                  {t('public.featureHighlight')}
                </span>
              </div>
              <h2 className="text-3xl font-bold text-midnight-blue mb-4">
                {t('public.inventoryDataGridTitle')}
              </h2>
              <p className="text-muted-foreground mb-6">
                {t('public.inventoryDataGridDescription')}
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-electric-orange/20">
                    <Zap className="h-3 w-3 text-electric-orange" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">{t('public.aiPoweredAlerts')}</h4>
                    <p className="text-sm text-muted-foreground">{t('public.aiPoweredAlertsDescription')}</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-electric-orange/20">
                    <BarChart4 className="h-3 w-3 text-electric-orange" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">{t('public.advancedAnalytics')}</h4>
                    <p className="text-sm text-muted-foreground">{t('public.advancedAnalyticsDescription')}</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-electric-orange/20">
                    <RefreshCw className="h-3 w-3 text-electric-orange" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">{t('public.csvImportExport')}</h4>
                    <p className="text-sm text-muted-foreground">{t('public.csvImportExportDescription')}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="rounded-lg border border-border/50 bg-muted/30 aspect-[4/3] flex items-center justify-center">
              <div className="text-center p-8">
                <Database className="h-12 w-12 text-electric-orange mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-midnight-blue mb-2">
                  {t('public.inventoryPreview')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {t('public.inventoryPreviewDescription')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Part Matching Engine Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1 rounded-lg border border-border/50 bg-white aspect-[4/3] flex items-center justify-center">
              <div className="text-center p-8">
                <Cpu className="h-12 w-12 text-electric-orange mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-midnight-blue mb-2">
                  {t('public.matchingPreview')}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {t('public.matchingPreviewDescription')}
                </p>
              </div>
            </div>
            
            <div className="order-1 lg:order-2">
              <div className="inline-flex items-center rounded-full border border-electric-orange/30 bg-electric-orange/10 px-3 py-1 text-sm text-electric-orange mb-4">
                <span className="font-medium">
                  {t('public.featureHighlight')}
                </span>
              </div>
              <h2 className="text-3xl font-bold text-midnight-blue mb-4">
                {t('public.partMatchingTitle')}
              </h2>
              <p className="text-muted-foreground mb-6">
                {t('public.partMatchingDescription')}
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-electric-orange/20">
                    <Search className="h-3 w-3 text-electric-orange" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">{t('public.carModelSearch')}</h4>
                    <p className="text-sm text-muted-foreground">{t('public.carModelSearchDescription')}</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-electric-orange/20">
                    <Layers className="h-3 w-3 text-electric-orange" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">{t('public.compatibilityCheck')}</h4>
                    <p className="text-sm text-muted-foreground">{t('public.compatibilityCheckDescription')}</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-electric-orange/20">
                    <ShieldCheck className="h-3 w-3 text-electric-orange" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium">{t('public.qualityAssurance')}</h4>
                    <p className="text-sm text-muted-foreground">{t('public.qualityAssuranceDescription')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Other Features Section */}
      <section className="py-20">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-midnight-blue mb-4">
              {t('public.moreFeatures')}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('public.moreFeaturesDescription')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <FeatureCard
              icon={Store}
              title={t('public.merchantPanelTitle')}
              description={t('public.merchantPanelDescription')}
            />
            <FeatureCard
              icon={Scan}
              title={t('public.posIntegrationTitle')}
              description={t('public.posIntegrationDescription')}
            />
            <FeatureCard
              icon={Smartphone}
              title={t('public.mobileAppTitle')}
              description={t('public.mobileAppDescription')}
            />
            <FeatureCard
              icon={Printer}
              title={t('public.receiptPrintingTitle')}
              description={t('public.receiptPrintingDescription')}
            />
            <FeatureCard
              icon={CreditCard}
              title={t('public.paymentProcessingTitle')}
              description={t('public.paymentProcessingDescription')}
            />
            <FeatureCard
              icon={Truck}
              title={t('public.deliveryTrackingTitle')}
              description={t('public.deliveryTrackingDescription')}
            />
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-midnight-blue text-white">
        <div className="container">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div>
              <h2 className="text-3xl font-bold mb-2">
                {t('public.readyToJoin')}
              </h2>
              <p className="text-white/70">
                {t('public.readyToJoinDescription')}
              </p>
            </div>
            
            <Button 
              size="lg"
              className="bg-electric-orange hover:bg-electric-orange/90 text-white"
              asChild
            >
              <Link to="/#waitlist">
                {t('public.joinWaitlist')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </PublicLayout>
  );
}
