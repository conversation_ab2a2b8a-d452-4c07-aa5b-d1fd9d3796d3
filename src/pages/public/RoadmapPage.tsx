import React from 'react';
import { useTranslation } from 'react-i18next';
import { PublicLayout } from '@/components/layout/PublicLayout';
import { RoadmapTimeline } from '@/components/public/RoadmapTimeline';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

export default function RoadmapPage() {
  const { t } = useTranslation();
  
  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-midnight-blue mb-6">
              {t('public.roadmapTitle')}
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              {t('public.roadmapSubtitle')}
            </p>
          </div>
        </div>
      </section>
      
      {/* Timeline Section */}
      <section className="py-20">
        <div className="container">
          <RoadmapTimeline />
        </div>
      </section>
      
      {/* Future Vision Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-midnight-blue mb-6">
              {t('public.beyondRoadmap')}
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              {t('public.beyondRoadmapDescription')}
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left mt-12">
              <div className="bg-white p-6 rounded-lg border border-border/50 shadow-sm">
                <h3 className="text-xl font-semibold mb-3">{t('public.futureVision1Title')}</h3>
                <p className="text-muted-foreground">{t('public.futureVision1Description')}</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg border border-border/50 shadow-sm">
                <h3 className="text-xl font-semibold mb-3">{t('public.futureVision2Title')}</h3>
                <p className="text-muted-foreground">{t('public.futureVision2Description')}</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg border border-border/50 shadow-sm">
                <h3 className="text-xl font-semibold mb-3">{t('public.futureVision3Title')}</h3>
                <p className="text-muted-foreground">{t('public.futureVision3Description')}</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-midnight-blue text-white">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">
              {t('public.bePartOfFuture')}
            </h2>
            <p className="text-white/70 mb-8">
              {t('public.bePartOfFutureDescription')}
            </p>
            
            <Button 
              size="lg"
              className="bg-electric-orange hover:bg-electric-orange/90 text-white"
              asChild
            >
              <Link to="/#waitlist">
                {t('public.joinWaitlistToday')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </PublicLayout>
  );
}
