import React from 'react';
import { useTranslation } from 'react-i18next';
import { PublicLayout } from '@/components/layout/PublicLayout';
import { ContactForm } from '@/components/public/ContactForm';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';

export default function ContactPage() {
  const { t } = useTranslation();
  
  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-midnight-blue mb-6">
              {t('public.contactTitle')}
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              {t('public.contactSubtitle')}
            </p>
          </div>
        </div>
      </section>
      
      {/* Contact Form Section */}
      <section className="py-20">
        <div className="container">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold text-midnight-blue mb-6">
                {t('public.getInTouch')}
              </h2>
              <p className="text-muted-foreground mb-8">
                {t('public.getInTouchDescription')}
              </p>
              
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-electric-orange/10 text-electric-orange">
                    <Mail className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{t('public.email')}</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-electric-orange/10 text-electric-orange">
                    <Phone className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{t('public.phone')}</h3>
                    <p className="text-muted-foreground">+213 XX XX XX XX</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-electric-orange/10 text-electric-orange">
                    <MapPin className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{t('public.address')}</h3>
                    <p className="text-muted-foreground">Algiers, Algeria</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-electric-orange/10 text-electric-orange">
                    <Clock className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{t('public.businessHours')}</h3>
                    <p className="text-muted-foreground">{t('public.businessHoursDetails')}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-lg border border-border/50 shadow-sm">
              <h2 className="text-2xl font-bold text-midnight-blue mb-6">
                {t('public.sendMessage')}
              </h2>
              <ContactForm />
            </div>
          </div>
        </div>
      </section>
      
      {/* Map Section */}
      <section className="py-12 bg-muted/30">
        <div className="container">
          <div className="rounded-lg overflow-hidden border border-border/50 shadow-sm h-[400px] bg-white flex items-center justify-center">
            <div className="text-center p-8">
              <MapPin className="h-12 w-12 text-electric-orange mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-midnight-blue mb-2">
                {t('public.mapPlaceholder')}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t('public.mapPlaceholderDescription')}
              </p>
            </div>
          </div>
        </div>
      </section>
    </PublicLayout>
  );
}
