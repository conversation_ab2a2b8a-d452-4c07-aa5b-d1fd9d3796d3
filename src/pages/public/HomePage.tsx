import React from 'react';
import { useTranslation } from 'react-i18next';
import { PublicLayout } from '@/components/layout/PublicLayout';
import { Hero } from '@/components/public/Hero';
import { FeatureCard } from '@/components/public/FeatureCard';
import { WaitlistForm } from '@/components/public/WaitlistForm';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Database, ShoppingBag, Cpu, Store, 
  Smartphone, ArrowRight, Users, BarChart 
} from 'lucide-react';

export default function HomePage() {
  const { t } = useTranslation();
  
  return (
    <PublicLayout>
      {/* Hero Section */}
      <Hero />
      
      {/* Core Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-midnight-blue mb-4">
              {t('public.coreFeatures')}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('public.coreFeaturesDescription')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <FeatureCard
              icon={Database}
              title={t('public.featureInventoryTitle')}
              description={t('public.featureInventoryDescription')}
            />
            <FeatureCard
              icon={Cpu}
              title={t('public.featureMatchingTitle')}
              description={t('public.featureMatchingDescription')}
            />
            <FeatureCard
              icon={ShoppingBag}
              title={t('public.featurePOSTitle')}
              description={t('public.featurePOSDescription')}
            />
            <FeatureCard
              icon={Smartphone}
              title={t('public.featureMobileTitle')}
              description={t('public.featureMobileDescription')}
            />
          </div>
          
          <div className="mt-12 text-center">
            <Button 
              variant="outline" 
              className="border-electric-orange text-electric-orange hover:bg-electric-orange/10"
              asChild
            >
              <Link to="/features">
                {t('public.exploreAllFeatures')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
      
      {/* Target Users Section */}
      <section className="py-20">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-midnight-blue mb-4">
              {t('public.builtFor')}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('public.builtForDescription')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center text-center p-6 rounded-lg border border-border/50 bg-background hover:border-electric-orange/50 hover:shadow-md transition-all">
              <div className="w-16 h-16 rounded-full bg-electric-orange/10 flex items-center justify-center mb-4">
                <Store className="h-8 w-8 text-electric-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('public.manufacturers')}</h3>
              <p className="text-muted-foreground">{t('public.manufacturersDescription')}</p>
            </div>
            
            <div className="flex flex-col items-center text-center p-6 rounded-lg border border-border/50 bg-background hover:border-electric-orange/50 hover:shadow-md transition-all">
              <div className="w-16 h-16 rounded-full bg-electric-orange/10 flex items-center justify-center mb-4">
                <ShoppingBag className="h-8 w-8 text-electric-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('public.merchants')}</h3>
              <p className="text-muted-foreground">{t('public.merchantsDescription')}</p>
            </div>
            
            <div className="flex flex-col items-center text-center p-6 rounded-lg border border-border/50 bg-background hover:border-electric-orange/50 hover:shadow-md transition-all">
              <div className="w-16 h-16 rounded-full bg-electric-orange/10 flex items-center justify-center mb-4">
                <Users className="h-8 w-8 text-electric-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('public.garages')}</h3>
              <p className="text-muted-foreground">{t('public.garagesDescription')}</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Launch Banner */}
      <section className="py-16 bg-midnight-blue text-white">
        <div className="container">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div>
              <h2 className="text-3xl font-bold mb-2">
                {t('public.launchingQ3')}
              </h2>
              <p className="text-white/70">
                {t('public.launchingDescription')}
              </p>
            </div>
            
            <Button 
              size="lg"
              className="bg-electric-orange hover:bg-electric-orange/90 text-white"
              asChild
            >
              <a href="#waitlist">
                {t('public.joinPreLaunch')}
              </a>
            </Button>
          </div>
        </div>
      </section>
      
      {/* Waitlist Section */}
      <section className="py-20" id="waitlist-section">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-midnight-blue mb-4">
              {t('public.joinOurWaitlist')}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('public.waitlistPageDescription')}
            </p>
          </div>
          
          <WaitlistForm />
        </div>
      </section>
    </PublicLayout>
  );
}
