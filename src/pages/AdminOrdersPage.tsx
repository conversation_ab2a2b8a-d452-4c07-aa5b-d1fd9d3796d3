import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AdminPanelLayout } from '@/components/layout/AdminPanelLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
  Package,
  Search,
  Filter,
  Truck,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  Phone,
  User,
  DollarSign,
  RefreshCw,
  Eye
} from 'lucide-react';
import {
  getAdminOrders,
  getShippingCompanies,
  assignOrderToShipping,
  AdminOrderWithItems,
  ShippingCompany
} from '@/services/adminOrderService';

// Use types from service

export default function AdminOrdersPage() {
  const { t } = useTranslation();
  const { toast } = useToast();

  // State management
  const [orders, setOrders] = useState<AdminOrderWithItems[]>([]);
  const [shippingCompanies, setShippingCompanies] = useState<ShippingCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('confirmed');
  const [wilayaFilter, setWilayaFilter] = useState<string>('all');
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());

  // Load orders and shipping companies
  const loadData = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      console.log('📊 [ADMIN_ORDERS] Loading orders and shipping companies...');

      // Load orders and shipping companies in parallel
      const [ordersResult, companiesResult] = await Promise.all([
        getAdminOrders({
          status: statusFilter === 'all' ? undefined : statusFilter,
          wilaya: wilayaFilter === 'all' ? undefined : wilayaFilter,
          search: searchTerm || undefined,
          limit: 50 // Load more orders for admin view
        }),
        getShippingCompanies()
      ]);

      if (ordersResult.success) {
        setOrders(ordersResult.orders || []);
      } else {
        throw new Error(ordersResult.error);
      }

      if (companiesResult.success) {
        setShippingCompanies(companiesResult.companies || []);
      } else {
        console.warn('Failed to load shipping companies:', companiesResult.error);
      }

    } catch (error) {
      console.error('❌ [ADMIN_ORDERS] Error loading data:', error);
      toast({
        title: 'Error Loading Data',
        description: 'Failed to load orders and shipping companies.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Assign order to shipping company
  const handleAssignOrder = async (orderId: string, shippingCompanyId: string) => {
    try {
      console.log('🚚 [ADMIN_ORDERS] Assigning order to shipping company...', { orderId, shippingCompanyId });

      const result = await assignOrderToShipping(orderId, shippingCompanyId);

      if (result.success) {
        // Update local state
        setOrders(prev => prev.map(order =>
          order.id === orderId
            ? {
                ...order,
                has_shipment: true,
                assigned_company: shippingCompanies.find(c => c.id === shippingCompanyId)?.company_name,
                shipment_id: result.shipment?.id
              }
            : order
        ));

        toast({
          title: 'Order Assigned Successfully',
          description: 'Order has been assigned to shipping company and will appear in their dashboard.',
        });
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      console.error('❌ [ADMIN_ORDERS] Error assigning order:', error);
      toast({
        title: 'Assignment Failed',
        description: error instanceof Error ? error.message : 'Failed to assign order to shipping company.',
        variant: 'destructive',
      });
    }
  };

  // Get available shipping companies for a wilaya
  const getAvailableShippingCompanies = (wilaya: string) => {
    return shippingCompanies.filter(company => 
      company.is_active && company.coverage_areas.includes(wilaya)
    );
  };

  // Filter orders
  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchTerm || 
      order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.consumer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.consumer_phone.includes(searchTerm);
    
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesWilaya = wilayaFilter === 'all' || order.delivery_wilaya === wilayaFilter;
    
    return matchesSearch && matchesStatus && matchesWilaya;
  });

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get unique wilayas from orders
  const uniqueWilayas = Array.from(new Set(orders.map(order => order.delivery_wilaya)));

  // Calculate item count and supplier locations for display
  const getOrderDisplayData = (order: AdminOrderWithItems) => {
    const itemCount = order.order_items.reduce((sum, item) => sum + item.quantity, 0);
    const supplierLocations = Array.from(new Set(order.order_items.map(item => item.supplier_wilaya)));
    return { itemCount, supplierLocations };
  };

  return (
    <AdminPanelLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Orders Management</h1>
            <p className="text-gray-600 mt-1">
              Assign confirmed orders to shipping companies for delivery
            </p>
          </div>
          <Button
            onClick={() => loadData(true)}
            disabled={isRefreshing}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{orders.length}</div>
              <p className="text-xs text-muted-foreground">All orders</p>
            </CardContent>
          </Card>

          <Card className="border-orange-200 bg-orange-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-800">
                Needs Assignment
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-800">
                {orders.filter(o => o.status === 'confirmed' && !o.has_shipment).length}
              </div>
              <p className="text-xs text-orange-600">Awaiting shipping assignment</p>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-800">
                Assigned
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-800">
                {orders.filter(o => o.has_shipment).length}
              </div>
              <p className="text-xs text-green-600">Assigned to shipping companies</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Partners</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{shippingCompanies.filter(c => c.is_active).length}</div>
              <p className="text-xs text-muted-foreground">Shipping companies</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by order number, customer name, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full md:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-48">
                <Select value={wilayaFilter} onValueChange={setWilayaFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by wilaya" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Wilayas</SelectItem>
                    {uniqueWilayas.map(wilaya => (
                      <SelectItem key={wilaya} value={wilaya}>{wilaya}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Orders ({filteredOrders.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200 animate-pulse rounded-lg" />
                ))}
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' || wilayaFilter !== 'all'
                    ? 'Try adjusting your filters or search terms'
                    : 'Orders will appear here when they need shipping assignment'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <OrderCard
                    key={order.id}
                    order={order}
                    shippingCompanies={getAvailableShippingCompanies(order.delivery_wilaya)}
                    onAssign={handleAssignOrder}
                    formatCurrency={formatCurrency}
                    formatDate={formatDate}
                    getOrderDisplayData={getOrderDisplayData}
                  />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}

// Order Card Component
interface OrderCardProps {
  order: AdminOrderWithItems;
  shippingCompanies: ShippingCompany[];
  onAssign: (orderId: string, shippingCompanyId: string) => void;
  formatCurrency: (amount: number) => string;
  formatDate: (date: string) => string;
  getOrderDisplayData: (order: AdminOrderWithItems) => { itemCount: number; supplierLocations: string[] };
}

function OrderCard({ order, shippingCompanies, onAssign, formatCurrency, formatDate, getOrderDisplayData }: OrderCardProps) {
  const [selectedCompany, setSelectedCompany] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);

  const handleAssign = async () => {
    if (!selectedCompany) return;
    
    setIsAssigning(true);
    try {
      await onAssign(order.id, selectedCompany);
    } finally {
      setIsAssigning(false);
      setSelectedCompany('');
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-6 space-y-4">
      {/* Order Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${order.has_shipment ? 'bg-green-100' : 'bg-orange-100'}`}>
            {order.has_shipment ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-orange-600" />
            )}
          </div>
          <div>
            <h3 className="font-semibold text-lg">{order.order_number}</h3>
            <p className="text-sm text-gray-600">
              Confirmed {order.confirmed_at ? formatDate(order.confirmed_at) : formatDate(order.created_at)}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="font-semibold text-lg">{formatCurrency(order.total_amount)}</div>
          <Badge variant={order.has_shipment ? 'default' : 'destructive'}>
            {order.has_shipment ? 'Assigned' : 'Needs Assignment'}
          </Badge>
        </div>
      </div>

      {/* Order Details */}
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-400" />
            <span className="font-medium">{order.consumer_name}</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">{order.consumer_phone}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">{order.delivery_wilaya}</span>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Package className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">{getOrderDisplayData(order).itemCount} items</span>
          </div>
          <div className="flex items-center gap-2">
            <Truck className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              Suppliers: {getOrderDisplayData(order).supplierLocations.join(', ')}
            </span>
          </div>
          {order.assigned_company && (
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-600 font-medium">
                Assigned to {order.assigned_company}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Assignment Section */}
      {!order.has_shipment && (
        <div className="border-t pt-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Select value={selectedCompany} onValueChange={setSelectedCompany}>
                <SelectTrigger>
                  <SelectValue placeholder="Select shipping company..." />
                </SelectTrigger>
                <SelectContent>
                  {shippingCompanies.length === 0 ? (
                    <SelectItem value="" disabled>
                      No shipping companies available for {order.delivery_wilaya}
                    </SelectItem>
                  ) : (
                    shippingCompanies.map(company => (
                      <SelectItem key={company.id} value={company.id}>
                        {company.company_name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
            <Button
              onClick={handleAssign}
              disabled={!selectedCompany || isAssigning || shippingCompanies.length === 0}
              className="bg-[#fa7b00] hover:bg-[#e56f00]"
            >
              {isAssigning ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Assigning...
                </div>
              ) : (
                'Assign to Shipping'
              )}
            </Button>
          </div>
          {shippingCompanies.length === 0 && (
            <p className="text-sm text-red-600 mt-2">
              ⚠️ No shipping companies cover {order.delivery_wilaya}. Please add coverage or contact shipping partners.
            </p>
          )}
        </div>
      )}
    </div>
  );
}
