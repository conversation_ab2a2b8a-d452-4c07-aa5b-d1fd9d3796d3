import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { assignSupplierOrderToShipping, getShippingCompaniesForSupplier } from '@/services/supplierShippingService';

export default function TestShippingAssignment() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testAssignment = async () => {
    setLoading(true);
    try {
      console.log('🧪 Testing shipping assignment...');
      
      // First get shipping companies
      const companiesResult = await getShippingCompaniesForSupplier();
      console.log('📋 Companies result:', companiesResult);
      
      if (companiesResult.success && companiesResult.companies && companiesResult.companies.length > 0) {
        const firstCompany = companiesResult.companies[0];
        console.log('🚚 Using first company:', firstCompany);
        
        // Try to assign the order
        const assignResult = await assignSupplierOrderToShipping(
          '22be00c5-6bc4-4f7d-a68b-09b7c57413fa',
          firstCompany.id
        );
        
        console.log('✅ Assignment result:', assignResult);
        setResult(assignResult);
      } else {
        setResult({ success: false, error: 'No shipping companies available' });
      }
    } catch (error) {
      console.error('❌ Test error:', error);
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <Card>
        <CardHeader>
          <CardTitle>Test Shipping Assignment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testAssignment} 
            disabled={loading}
            className="bg-[#fa7b00] hover:bg-[#e56b00]"
          >
            {loading ? 'Testing...' : 'Test Assignment'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Result:</h3>
              <pre className="text-sm bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
