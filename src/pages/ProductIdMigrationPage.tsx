import React, { useState, useEffect } from 'react';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  checkMigrationStatus, 
  smartMigrateAllProducts, 
  migrateAllProducts 
} from '@/utils/productIdMigration';
import { RefreshCw, CheckCircle, AlertTriangle, Database, Zap } from 'lucide-react';
import { toast } from 'sonner';

export default function ProductIdMigrationPage() {
  const [migrationStatus, setMigrationStatus] = useState<{
    tyres: { total: number; migrated: number; needsMigration: number };
    brakes: { total: number; migrated: number; needsMigration: number };
  } | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [lastMigrationResult, setLastMigrationResult] = useState<{
    tyres: number;
    brakes: number;
    total: number;
  } | null>(null);

  // Check migration status on component mount
  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = () => {
    const status = checkMigrationStatus();
    setMigrationStatus(status);
  };

  const runSmartMigration = async () => {
    setIsLoading(true);
    try {
      const result = smartMigrateAllProducts();
      setLastMigrationResult(result);
      checkStatus(); // Refresh status after migration
      
      if (result.total > 0) {
        toast.success(`Successfully migrated ${result.total} products!`, {
          description: `Tyres: ${result.tyres}, Brakes: ${result.brakes}`,
        });
      } else {
        toast.info('No products needed migration');
      }
    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Migration failed. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  const runBasicMigration = async () => {
    setIsLoading(true);
    try {
      const result = migrateAllProducts('wholesale', 'retail');
      setLastMigrationResult(result);
      checkStatus(); // Refresh status after migration
      
      if (result.total > 0) {
        toast.success(`Successfully migrated ${result.total} products!`, {
          description: `Tyres: ${result.tyres}, Brakes: ${result.brakes}`,
        });
      } else {
        toast.info('No products needed migration');
      }
    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Migration failed. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  const getTotalProducts = () => {
    if (!migrationStatus) return 0;
    return migrationStatus.tyres.total + migrationStatus.brakes.total;
  };

  const getTotalMigrated = () => {
    if (!migrationStatus) return 0;
    return migrationStatus.tyres.migrated + migrationStatus.brakes.migrated;
  };

  const getTotalNeedsMigration = () => {
    if (!migrationStatus) return 0;
    return migrationStatus.tyres.needsMigration + migrationStatus.brakes.needsMigration;
  };

  const getMigrationProgress = () => {
    const total = getTotalProducts();
    const migrated = getTotalMigrated();
    return total > 0 ? (migrated / total) * 100 : 0;
  };

  const isFullyMigrated = () => {
    return getTotalNeedsMigration() === 0 && getTotalProducts() > 0;
  };

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Product ID Migration</h1>
          <p className="text-gray-600 mb-6">
            Migrate existing products from the old ID format (TYR-100001) to the new format with marketplace section suffixes (TYR-100001-WHOLESALE/RETAIL).
          </p>
        </div>

        {/* Migration Status Overview */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Migration Status
              <Button
                variant="outline"
                size="sm"
                onClick={checkStatus}
                className="ml-auto"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {migrationStatus ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm text-gray-600">
                    {getTotalMigrated()} / {getTotalProducts()} products migrated
                  </span>
                </div>
                <Progress value={getMigrationProgress()} className="h-2" />
                
                {isFullyMigrated() ? (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      All products have been successfully migrated to the new ID format!
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert className="border-orange-200 bg-orange-50">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <AlertDescription className="text-orange-800">
                      {getTotalNeedsMigration()} products still need to be migrated to the new ID format.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Tyres Category</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Total Products:</span>
                          <Badge variant="outline">{migrationStatus.tyres.total}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Migrated:</span>
                          <Badge variant="secondary">{migrationStatus.tyres.migrated}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Needs Migration:</span>
                          <Badge variant={migrationStatus.tyres.needsMigration > 0 ? "destructive" : "default"}>
                            {migrationStatus.tyres.needsMigration}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Brakes Category</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Total Products:</span>
                          <Badge variant="outline">{migrationStatus.brakes.total}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Migrated:</span>
                          <Badge variant="secondary">{migrationStatus.brakes.migrated}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Needs Migration:</span>
                          <Badge variant={migrationStatus.brakes.needsMigration > 0 ? "destructive" : "default"}>
                            {migrationStatus.brakes.needsMigration}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">Loading migration status...</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Migration Actions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Migration Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border-blue-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Zap className="h-5 w-5 text-blue-600" />
                      Smart Migration (Recommended)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">
                      Intelligently assigns marketplace sections based on product characteristics. 
                      Products with wholesale pricing → WHOLESALE, products with retail pricing → RETAIL.
                    </p>
                    <Button 
                      onClick={runSmartMigration}
                      disabled={isLoading || isFullyMigrated()}
                      className="w-full"
                    >
                      {isLoading ? 'Migrating...' : 'Run Smart Migration'}
                    </Button>
                  </CardContent>
                </Card>

                <Card className="border-gray-200">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Database className="h-5 w-5 text-gray-600" />
                      Basic Migration
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">
                      Assigns all products to WHOLESALE suffix by default. 
                      Use this if you want manual control over marketplace sections.
                    </p>
                    <Button 
                      variant="outline"
                      onClick={runBasicMigration}
                      disabled={isLoading || isFullyMigrated()}
                      className="w-full"
                    >
                      {isLoading ? 'Migrating...' : 'Run Basic Migration'}
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Last Migration Result */}
        {lastMigrationResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Last Migration Result
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{lastMigrationResult.tyres}</div>
                  <div className="text-sm text-gray-600">Tyres Migrated</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{lastMigrationResult.brakes}</div>
                  <div className="text-sm text-gray-600">Brakes Migrated</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">{lastMigrationResult.total}</div>
                  <div className="text-sm text-gray-600">Total Migrated</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Migration Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p>• <strong>Smart Migration</strong> is recommended as it intelligently assigns marketplace sections based on product pricing structure.</p>
              <p>• Products with wholesale pricing tiers will get <code className="bg-gray-100 px-1 rounded">-WHOLESALE</code> suffix.</p>
              <p>• Products with retail pricing will get <code className="bg-gray-100 px-1 rounded">-RETAIL</code> suffix.</p>
              <p>• Products with both or neither pricing will be distributed evenly between wholesale and retail.</p>
              <p>• After migration, new products will automatically use the correct ID format based on the user's role.</p>
              <p>• <strong>Supplier accounts</strong> will generate IDs with <code className="bg-gray-100 px-1 rounded">-WHOLESALE</code> suffix.</p>
              <p>• <strong>Merchant accounts</strong> will generate IDs with <code className="bg-gray-100 px-1 rounded">-RETAIL</code> suffix.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MarketplaceLayout>
  );
}
