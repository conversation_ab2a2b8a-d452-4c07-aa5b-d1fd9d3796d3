import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { AlertTriangle, CheckCircle, Shield, Eye, Database } from 'lucide-react';
import { supabase } from '@/services/authService';
import { getSupplierOrders } from '@/services/supplierOrderService';

interface SecurityTestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export default function OrderSecurityTestPage() {
  const { toast } = useToast();
  const [testResults, setTestResults] = useState<SecurityTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    getCurrentUserInfo();
  }, []);

  const getCurrentUserInfo = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        setCurrentUser({ ...user, profile });
      }
    } catch (error) {
      console.error('Error getting user info:', error);
    }
  };

  const runSecurityTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    const results: SecurityTestResult[] = [];

    try {
      // Test 1: Check if RLS is enabled
      results.push(await testRLSEnabled());

      // Test 2: Test supplier order filtering
      results.push(await testSupplierOrderFiltering());

      // Test 3: Test direct database access (should be blocked)
      results.push(await testDirectDatabaseAccess());

      // Test 4: Test order items access
      results.push(await testOrderItemsAccess());

      // Test 5: Test cross-supplier access
      results.push(await testCrossSupplierAccess());

      setTestResults(results);

      const failedTests = results.filter(r => r.status === 'fail').length;
      const warningTests = results.filter(r => r.status === 'warning').length;

      if (failedTests === 0 && warningTests === 0) {
        toast({
          title: '🛡️ Security Tests Passed!',
          description: 'All security measures are working correctly.',
          variant: 'default'
        });
      } else {
        toast({
          title: '⚠️ Security Issues Found',
          description: `${failedTests} failed, ${warningTests} warnings`,
          variant: 'destructive'
        });
      }

    } catch (error) {
      console.error('Error running security tests:', error);
      toast({
        title: 'Test Error',
        description: 'Failed to run security tests',
        variant: 'destructive'
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testRLSEnabled = async (): Promise<SecurityTestResult> => {
    try {
      const { data, error } = await supabase
        .rpc('check_rls_status');

      if (error) {
        // If the function doesn't exist, check manually
        const { data: rlsData, error: rlsError } = await supabase
          .from('pg_class')
          .select('relname, relrowsecurity')
          .in('relname', ['orders', 'order_items', 'shipments']);

        if (rlsError) {
          return {
            test: 'RLS Status Check',
            status: 'warning',
            message: 'Could not verify RLS status',
            details: rlsError
          };
        }

        const tablesWithoutRLS = rlsData?.filter(table => !table.relrowsecurity) || [];
        
        if (tablesWithoutRLS.length > 0) {
          return {
            test: 'RLS Status Check',
            status: 'fail',
            message: `RLS not enabled on: ${tablesWithoutRLS.map(t => t.relname).join(', ')}`,
            details: tablesWithoutRLS
          };
        }
      }

      return {
        test: 'RLS Status Check',
        status: 'pass',
        message: 'RLS is enabled on all critical tables'
      };
    } catch (error) {
      return {
        test: 'RLS Status Check',
        status: 'fail',
        message: 'Failed to check RLS status',
        details: error
      };
    }
  };

  const testSupplierOrderFiltering = async (): Promise<SecurityTestResult> => {
    try {
      const result = await getSupplierOrders({ limit: 10 });
      
      if (!result.success) {
        return {
          test: 'Supplier Order Filtering',
          status: 'fail',
          message: 'Failed to fetch supplier orders',
          details: result.error
        };
      }

      const orders = result.orders || [];
      
      // Check if all orders contain items from current supplier
      const currentUserId = currentUser?.id;
      let hasInvalidOrders = false;
      let invalidOrderDetails: any[] = [];

      for (const order of orders) {
        const supplierItems = order.supplier_items || [];
        const hasCurrentSupplierItems = supplierItems.some(item => 
          item.supplier_account_id === currentUserId
        );

        if (!hasCurrentSupplierItems) {
          hasInvalidOrders = true;
          invalidOrderDetails.push({
            order_id: order.id,
            order_number: order.order_number,
            supplier_items_count: supplierItems.length,
            current_user_id: currentUserId
          });
        }
      }

      if (hasInvalidOrders) {
        return {
          test: 'Supplier Order Filtering',
          status: 'fail',
          message: `Found ${invalidOrderDetails.length} orders without supplier's items`,
          details: invalidOrderDetails
        };
      }

      return {
        test: 'Supplier Order Filtering',
        status: 'pass',
        message: `Successfully filtered ${orders.length} orders containing supplier's items`
      };
    } catch (error) {
      return {
        test: 'Supplier Order Filtering',
        status: 'fail',
        message: 'Exception during supplier order filtering test',
        details: error
      };
    }
  };

  const testDirectDatabaseAccess = async (): Promise<SecurityTestResult> => {
    try {
      // Try to access all orders directly (should be blocked by RLS)
      const { data, error } = await supabase
        .from('orders')
        .select('id, order_number, consumer_name')
        .limit(100);

      if (error) {
        return {
          test: 'Direct Database Access',
          status: 'pass',
          message: 'Direct access properly blocked by RLS',
          details: error.message
        };
      }

      // If we get data, check if it's properly filtered
      const orderCount = data?.length || 0;
      
      if (orderCount === 0) {
        return {
          test: 'Direct Database Access',
          status: 'pass',
          message: 'No orders returned - proper filtering'
        };
      }

      return {
        test: 'Direct Database Access',
        status: 'warning',
        message: `Got ${orderCount} orders via direct access - verify these are legitimate`,
        details: data?.slice(0, 3)
      };
    } catch (error) {
      return {
        test: 'Direct Database Access',
        status: 'pass',
        message: 'Direct access blocked by security',
        details: error
      };
    }
  };

  const testOrderItemsAccess = async (): Promise<SecurityTestResult> => {
    try {
      const { data, error } = await supabase
        .from('order_items')
        .select('id, supplier_account_id, product_name')
        .limit(50);

      if (error) {
        return {
          test: 'Order Items Access',
          status: 'warning',
          message: 'Order items access blocked',
          details: error.message
        };
      }

      const currentUserId = currentUser?.id;
      const invalidItems = data?.filter(item => 
        item.supplier_account_id !== currentUserId
      ) || [];

      if (invalidItems.length > 0) {
        return {
          test: 'Order Items Access',
          status: 'fail',
          message: `Found ${invalidItems.length} items from other suppliers`,
          details: invalidItems.slice(0, 3)
        };
      }

      return {
        test: 'Order Items Access',
        status: 'pass',
        message: `Properly filtered ${data?.length || 0} order items`
      };
    } catch (error) {
      return {
        test: 'Order Items Access',
        status: 'fail',
        message: 'Exception during order items access test',
        details: error
      };
    }
  };

  const testCrossSupplierAccess = async (): Promise<SecurityTestResult> => {
    try {
      // Try to access a specific order that might belong to another supplier
      const { data: allOrderIds } = await supabase
        .from('order_items')
        .select('order_id')
        .neq('supplier_account_id', currentUser?.id)
        .limit(1);

      if (!allOrderIds || allOrderIds.length === 0) {
        return {
          test: 'Cross-Supplier Access',
          status: 'pass',
          message: 'No other supplier orders found to test'
        };
      }

      const otherSupplierOrderId = allOrderIds[0].order_id;

      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', otherSupplierOrderId)
        .single();

      if (error || !data) {
        return {
          test: 'Cross-Supplier Access',
          status: 'pass',
          message: 'Cross-supplier access properly blocked'
        };
      }

      return {
        test: 'Cross-Supplier Access',
        status: 'fail',
        message: 'Was able to access another supplier\'s order',
        details: { order_id: otherSupplierOrderId, order_number: data.order_number }
      };
    } catch (error) {
      return {
        test: 'Cross-Supplier Access',
        status: 'pass',
        message: 'Cross-supplier access blocked by security',
        details: error
      };
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'fail': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      default: return <Eye className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pass': return <Badge className="bg-green-100 text-green-800">PASS</Badge>;
      case 'fail': return <Badge className="bg-red-100 text-red-800">FAIL</Badge>;
      case 'warning': return <Badge className="bg-yellow-100 text-yellow-800">WARNING</Badge>;
      default: return <Badge>UNKNOWN</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Order Security Test
          </h1>
          <p className="text-gray-600 mt-1">
            Verify that order access controls are working properly
          </p>
        </div>
        <Button
          onClick={runSecurityTests}
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          <Database className="h-4 w-4" />
          {isRunning ? 'Running Tests...' : 'Run Security Tests'}
        </Button>
      </div>

      {currentUser && (
        <Card>
          <CardHeader>
            <CardTitle>Current User</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>User ID:</strong> {currentUser.id}</div>
              <div><strong>Email:</strong> {currentUser.email}</div>
              <div><strong>Role:</strong> {currentUser.profile?.role}</div>
              <div><strong>Company:</strong> {currentUser.profile?.company_name}</div>
            </div>
          </CardContent>
        </Card>
      )}

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Security Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <h3 className="font-semibold">{result.test}</h3>
                    </div>
                    {getStatusBadge(result.status)}
                  </div>
                  <p className="text-gray-700 mb-2">{result.message}</p>
                  {result.details && (
                    <details className="text-xs text-gray-600">
                      <summary className="cursor-pointer">View Details</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
