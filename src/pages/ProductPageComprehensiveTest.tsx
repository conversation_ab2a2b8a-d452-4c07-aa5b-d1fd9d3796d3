import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';
import { fetchMarketplaceProductsByCategory } from '@/services/productService';

export default function ProductPageComprehensiveTest() {
  const { productId } = useParams<{ productId: string }>();
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState(true);

  // Test 1: React Query Hooks (IDENTICAL to ProductPage)
  const { products: tyreProducts, isLoading: tyresLoading, isError: tyresError, error: tyreError } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading, isError: brakesError, error: brakeError } = useMarketplaceProducts('brakes');

  // Test 2: Direct Service Calls
  const [directTyreProducts, setDirectTyreProducts] = useState<any[]>([]);
  const [directBrakeProducts, setDirectBrakeProducts] = useState<any[]>([]);

  useEffect(() => {
    const runComprehensiveTests = async () => {
      console.log('🧪 [COMPREHENSIVE_TEST] Starting all tests for:', productId);
      
      const results: any = {
        timestamp: new Date().toISOString(),
        productId,
        tests: {}
      };

      // Test 1: React Query State
      results.tests.reactQuery = {
        tyreProducts: tyreProducts?.length || 0,
        brakeProducts: brakeProducts?.length || 0,
        tyresLoading,
        brakesLoading,
        tyresError,
        brakesError,
        tyreErrorMessage: tyreError?.message,
        brakeErrorMessage: brakeError?.message
      };

      // Test 2: Direct Service Calls
      try {
        console.log('🧪 [COMPREHENSIVE_TEST] Testing direct service calls');
        const directTyres = await fetchMarketplaceProductsByCategory('tyres');
        const directBrakes = await fetchMarketplaceProductsByCategory('brakes');
        
        setDirectTyreProducts(directTyres);
        setDirectBrakeProducts(directBrakes);
        
        results.tests.directService = {
          tyreProducts: directTyres.length,
          brakeProducts: directBrakes.length,
          success: true
        };
      } catch (error) {
        results.tests.directService = {
          success: false,
          error: error.message
        };
      }

      // Test 3: Product Filtering (IDENTICAL to ProductPage)
      const retailTyreProducts = tyreProducts.filter(product => {
        const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
        const isVisible = product.status === 'active' || product.status === 'out_of_stock';
        return hasRetailPrice && isVisible;
      });

      const retailBrakeProducts = brakeProducts.filter(product => {
        const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
        const isVisible = product.status === 'active' || product.status === 'out_of_stock';
        return hasRetailPrice && isVisible;
      });

      const allProducts = [...retailTyreProducts, ...retailBrakeProducts];
      const foundProduct = allProducts.find(p => p.id === productId);

      results.tests.filtering = {
        originalTyres: tyreProducts.length,
        originalBrakes: brakeProducts.length,
        filteredTyres: retailTyreProducts.length,
        filteredBrakes: retailBrakeProducts.length,
        totalFiltered: allProducts.length,
        productFound: !!foundProduct,
        productName: foundProduct?.name,
        productStatus: foundProduct?.status,
        productRetailPrice: foundProduct?.retailPrice
      };

      // Test 4: Sample Product IDs
      results.tests.sampleProducts = {
        first10ProductIds: allProducts.slice(0, 10).map(p => ({ id: p.id, name: p.name, status: p.status, retailPrice: p.retailPrice })),
        searchedProductId: productId,
        exactMatch: allProducts.find(p => p.id === productId)
      };

      // Test 5: Component Lifecycle
      results.tests.lifecycle = {
        componentMounted: true,
        reactQueryWorking: !tyresLoading && !brakesLoading,
        hasData: tyreProducts.length > 0 || brakeProducts.length > 0,
        timestamp: new Date().toISOString()
      };

      console.log('🧪 [COMPREHENSIVE_TEST] All tests completed:', results);
      setTestResults(results);
      setLoading(false);
    };

    if (!tyresLoading && !brakesLoading) {
      runComprehensiveTests();
    }
  }, [productId, tyreProducts, brakeProducts, tyresLoading, brakesLoading]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PASS': return <Badge className="bg-green-100 text-green-800">PASS</Badge>;
      case 'FAIL': return <Badge className="bg-red-100 text-red-800">FAIL</Badge>;
      case 'WARN': return <Badge className="bg-yellow-100 text-yellow-800">WARN</Badge>;
      default: return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading || tyresLoading || brakesLoading) {
    return (
      <MarketplaceLayout>
        <div className="container py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Running Comprehensive Tests...</h1>
            <p>Testing ProductPage data flow for: {productId}</p>
          </div>
        </div>
      </MarketplaceLayout>
    );
  }

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">ProductPage Comprehensive Test</h1>
            <p className="text-gray-600">Testing product ID: <code className="bg-gray-100 px-2 py-1 rounded">{productId}</code></p>
          </div>

          {/* Test Results Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            {/* React Query Test */}
            <Card>
              <CardHeader>
                <CardTitle>React Query Test</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Tyre Products:</span>
                    <span>{testResults.tests?.reactQuery?.tyreProducts || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Brake Products:</span>
                    <span>{testResults.tests?.reactQuery?.brakeProducts || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Loading State:</span>
                    <span>{testResults.tests?.reactQuery?.tyresLoading || testResults.tests?.reactQuery?.brakesLoading ? 'Loading' : 'Complete'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Errors:</span>
                    <span>{testResults.tests?.reactQuery?.tyresError || testResults.tests?.reactQuery?.brakesError ? 'Yes' : 'None'}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Filtering Test */}
            <Card>
              <CardHeader>
                <CardTitle>Product Filtering Test</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Original Tyres:</span>
                    <span>{testResults.tests?.filtering?.originalTyres || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Filtered Tyres:</span>
                    <span>{testResults.tests?.filtering?.filteredTyres || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Original Brakes:</span>
                    <span>{testResults.tests?.filtering?.originalBrakes || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Filtered Brakes:</span>
                    <span>{testResults.tests?.filtering?.filteredBrakes || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Products:</span>
                    <span>{testResults.tests?.filtering?.totalFiltered || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Product Found:</span>
                    {getStatusBadge(testResults.tests?.filtering?.productFound ? 'PASS' : 'FAIL')}
                  </div>
                  {testResults.tests?.filtering?.productFound && (
                    <div className="mt-2 p-2 bg-green-50 rounded">
                      <p className="font-medium">{testResults.tests?.filtering?.productName}</p>
                      <p className="text-xs text-gray-600">Status: {testResults.tests?.filtering?.productStatus}</p>
                      <p className="text-xs text-gray-600">Price: {testResults.tests?.filtering?.productRetailPrice} DZD</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button onClick={() => window.location.reload()}>
              Rerun Tests
            </Button>
            <Button variant="outline" onClick={() => window.history.back()}>
              Go Back
            </Button>
            <Button 
              onClick={() => window.open(`/${productId}`, '_blank')}
              className="bg-[#fa7b00] hover:bg-[#fa7b00]/90"
            >
              Test ProductPage
            </Button>
          </div>

        </div>
      </div>
    </MarketplaceLayout>
  );
}
