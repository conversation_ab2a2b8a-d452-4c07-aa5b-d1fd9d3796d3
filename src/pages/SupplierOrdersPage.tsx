import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Package,
  Search,
  Filter,
  Eye,
  CheckCircle,
  Clock,
  Truck,
  AlertCircle,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import {
  getSupplierOrders,
  getSupplierOrderStats,
  SupplierOrderWithItems,
  SupplierOrderStats
} from '@/services/supplierOrderService';
import {
  getShippingCompaniesForSupplier,
  assignSupplierOrderToShipping,
  ShippingCompany
} from '@/services/supplierShippingService';
import { StatsCard } from '@/components/dashboard/StatsCard';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export default function SupplierOrdersPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  // State management
  const [orders, setOrders] = useState<SupplierOrderWithItems[]>([]);
  const [stats, setStats] = useState<SupplierOrderStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Shipping assignment state
  const [shippingCompanies, setShippingCompanies] = useState<ShippingCompany[]>([]);
  const [isShippingDialogOpen, setIsShippingDialogOpen] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string>('');
  const [selectedShippingCompanyId, setSelectedShippingCompanyId] = useState<string>('');
  const [isAssigningShipping, setIsAssigningShipping] = useState(false);

  const ITEMS_PER_PAGE = 10;

  // Load shipping companies
  const loadShippingCompanies = async () => {
    try {
      const result = await getShippingCompaniesForSupplier();
      if (result.success) {
        setShippingCompanies(result.companies || []);
      }
    } catch (error) {
      console.error('❌ [SUPPLIER_ORDERS_PAGE] Error loading shipping companies:', error);
    }
  };

  // Handle shipping assignment
  const handleAssignShipping = async () => {
    if (!selectedOrderId || !selectedShippingCompanyId) return;

    try {
      setIsAssigningShipping(true);
      console.log('🚚 [SUPPLIER_ORDERS_PAGE] Assigning order to shipping company...', {
        orderId: selectedOrderId,
        shippingCompanyId: selectedShippingCompanyId
      });

      const result = await assignSupplierOrderToShipping(selectedOrderId, selectedShippingCompanyId);

      if (result.success) {
        toast({
          title: 'Order Assigned Successfully',
          description: 'Order has been assigned to shipping company and will appear in their dashboard.',
        });

        // Refresh orders list
        loadData(true);

        // Close dialog and reset state
        setIsShippingDialogOpen(false);
        setSelectedOrderId('');
        setSelectedShippingCompanyId('');
      } else {
        toast({
          title: 'Assignment Failed',
          description: result.error || 'Failed to assign order to shipping company',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('❌ [SUPPLIER_ORDERS_PAGE] Error assigning shipping:', error);
      toast({
        title: 'Assignment Failed',
        description: 'Failed to assign order to shipping company',
        variant: 'destructive'
      });
    } finally {
      setIsAssigningShipping(false);
    }
  };

  // Open shipping assignment dialog
  const openShippingDialog = (orderId: string) => {
    setSelectedOrderId(orderId);
    setSelectedShippingCompanyId('');
    setIsShippingDialogOpen(true);
  };

  // Load orders and statistics
  const loadData = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      console.log('📊 [SUPPLIER_ORDERS_PAGE] Loading orders and stats...');

      // Load statistics
      const statsResult = await getSupplierOrderStats();
      if (statsResult.success) {
        setStats(statsResult.stats || null);
      }

      // Load orders with filters
      const filters = {
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchTerm || undefined,
        limit: ITEMS_PER_PAGE,
        offset: (currentPage - 1) * ITEMS_PER_PAGE
      };

      const ordersResult = await getSupplierOrders(filters);
      if (ordersResult.success) {
        setOrders(ordersResult.orders || []);
        setTotalCount(ordersResult.total_count || 0);
      } else {
        toast({
          title: 'Failed to load orders',
          description: ordersResult.error || 'Please try again',
          variant: 'destructive'
        });
      }

    } catch (error) {
      console.error('❌ [SUPPLIER_ORDERS_PAGE] Error loading data:', error);
      toast({
        title: 'Failed to load data',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadData();
    loadShippingCompanies();
  }, [currentPage, statusFilter]);

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        loadData();
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'confirmed': return 'default';
      case 'shipped': return 'outline';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock;
      case 'confirmed': return CheckCircle;
      case 'shipped': return Truck;
      case 'delivered': return CheckCircle;
      case 'cancelled': return AlertCircle;
      default: return Clock;
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Order Management</h1>
            <p className="text-gray-600 mt-1">
              Manage orders containing your products and coordinate with shipping companies
            </p>
          </div>
          <Button
            onClick={() => loadData(true)}
            disabled={isRefreshing}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Statistics Cards */}
        {isLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
        ) : stats ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Total Orders"
              value={stats.total_orders}
              icon={<Package className="h-6 w-6" />}
              description="All time orders"
            />
            <StatsCard
              title="Pending Orders"
              value={stats.pending_orders}
              icon={<Clock className="h-6 w-6" />}
              description="Awaiting confirmation"
            />
            <StatsCard
              title="This Month Revenue"
              value={formatCurrency(stats.this_month_revenue)}
              icon={<DollarSign className="h-6 w-6" />}
              description={`${stats.this_month_orders} orders`}
            />
            <StatsCard
              title="Average Order Value"
              value={formatCurrency(stats.average_order_value)}
              icon={<TrendingUp className="h-6 w-6" />}
              description="Per order average"
            />
          </div>
        ) : null}

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by order number, customer name, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full md:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Orders ({totalCount})
              </span>
              {totalPages > 1 && (
                <span className="text-sm text-gray-500">
                  Page {currentPage} of {totalPages}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-24" />
                ))}
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your filters or search terms'
                    : 'Orders containing your products will appear here'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {orders.map((order) => {
                  const StatusIcon = getStatusIcon(order.status);
                  const supplierValue = order.supplier_items.reduce((sum, item) => sum + item.total_price, 0);
                  
                  return (
                    <div
                      key={order.id}
                      className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <StatusIcon className="h-6 w-6 text-[#fa7b00]" />
                          <div>
                            <h3 className="font-semibold text-lg">{order.order_number}</h3>
                            <p className="text-sm text-gray-600">
                              {formatDate(order.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge variant={getStatusBadgeVariant(order.status)}>
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </Badge>
                          {order.status === 'confirmed' && (
                            <Button
                              onClick={() => openShippingDialog(order.id)}
                              size="sm"
                              variant="default"
                              className="flex items-center gap-2 bg-[#fa7b00] hover:bg-[#e56b00]"
                            >
                              <Truck className="h-4 w-4" />
                              Assign Shipping
                            </Button>
                          )}
                          <Button
                            onClick={() => navigate(`/app/orders/${order.id}`)}
                            size="sm"
                            variant="outline"
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {order.consumer_name || 'Consumer'} - {order.consumer_phone}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{order.delivery_wilaya}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span className="text-sm font-medium">
                            Your items: {formatCurrency(supplierValue)}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-600">
                            Your products: {order.supplier_items.length}
                          </span>
                          {order.other_suppliers.length > 0 && (
                            <span className="text-sm text-gray-600">
                              Other suppliers: {order.other_suppliers.length}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          Total order: {formatCurrency(order.total_amount)}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-6">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Shipping Assignment Dialog */}
        <Dialog open={isShippingDialogOpen} onOpenChange={setIsShippingDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Assign Order to Shipping Company</DialogTitle>
              <DialogDescription>
                Select a shipping company to handle the delivery of this order.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <label htmlFor="shipping-company" className="text-sm font-medium">
                  Shipping Company
                </label>
                <Select
                  value={selectedShippingCompanyId}
                  onValueChange={setSelectedShippingCompanyId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a shipping company" />
                  </SelectTrigger>
                  <SelectContent>
                    {shippingCompanies.map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{company.company_name}</span>
                          <span className="text-xs text-gray-500">
                            Coverage: {company.coverage_areas.slice(0, 3).join(', ')}
                            {company.coverage_areas.length > 3 && ` +${company.coverage_areas.length - 3} more`}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsShippingDialogOpen(false)}
                disabled={isAssigningShipping}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAssignShipping}
                disabled={!selectedShippingCompanyId || isAssigningShipping}
                className="bg-[#fa7b00] hover:bg-[#e56b00]"
              >
                {isAssigningShipping ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Assigning...
                  </>
                ) : (
                  <>
                    <Truck className="h-4 w-4 mr-2" />
                    Assign Order
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
}
