/**
 * Phone Authentication Test Page
 * 
 * Development page for testing the phone-only authentication system
 * Includes manual testing controls and status monitoring
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Phone, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  User,
  Database,
  Wifi,
  Key
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { EnhancedPhoneAuthModal } from '@/components/auth/EnhancedPhoneAuthModal';
import { AutoAuthModal, triggerAuthModal } from '@/components/auth/AutoAuthModal';
import { useAuth } from '@/contexts/AuthContext';
import { validateAlgerianPhone } from '@/services/dexatelService';
import { sendPhoneOTP, verifyPhoneOTP, checkPhoneExists } from '@/services/phoneAuthService';
import { dexatelDebugger } from '@/services/dexatelDebugger';

export function PhoneAuthTest() {
  const { toast } = useToast();
  const { user, isAuthenticated, profile } = useAuth();
  
  // State
  const [showModal, setShowModal] = useState(false);
  const [testPhone, setTestPhone] = useState('');
  const [testOTP, setTestOTP] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  
  // Environment checks
  const hasApiKey = !!import.meta.env.VITE_DEXATEL_API_KEY;
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const isOnline = navigator.onLine;
  
  // Add test result
  const addTestResult = (test: string, success: boolean, message: string, data?: any) => {
    const result = {
      id: Date.now(),
      test,
      success,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
  };
  
  // Test phone validation
  const testPhoneValidation = () => {
    const phones = [
      '+213555123456',
      '0555123456',
      '555123456',
      '+213 555 123 456',
      '213555123456',
      'invalid',
      '+212555123456', // Morocco (should fail)
    ];
    
    phones.forEach(phone => {
      const result = validateAlgerianPhone(phone);
      addTestResult(
        'Phone Validation',
        result.isValid,
        `${phone} → ${result.isValid ? result.formatted : result.error}`,
        result
      );
    });
  };
  
  // Test OTP sending
  const testSendOTP = async () => {
    if (!testPhone.trim()) {
      toast({
        title: "Error",
        description: "Please enter a phone number to test",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const result = await sendPhoneOTP(testPhone);
      
      if (result.success) {
        setSessionId(result.verificationId!);
        addTestResult('Send OTP', true, `OTP sent to ${testPhone}`, result);
        toast({
          title: "OTP Sent",
          description: `Verification code sent to ${testPhone}`,
        });
      } else {
        addTestResult('Send OTP', false, result.error!, result);
        toast({
          title: "Failed to Send OTP",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      addTestResult('Send OTP', false, `Error: ${error}`, { error });
      toast({
        title: "Error",
        description: "Failed to send OTP",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Test OTP verification
  const testVerifyOTP = async () => {
    if (!sessionId || !testOTP.trim()) {
      toast({
        title: "Error",
        description: "Please send OTP first and enter the verification code",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const result = await verifyPhoneOTP(sessionId, testOTP);
      
      if (result.success) {
        addTestResult('Verify OTP', true, `Authentication successful: ${result.action}`, result);
        toast({
          title: "Verification Successful",
          description: `${result.action === 'signup' ? 'Account created' : 'Logged in'} successfully!`,
        });
      } else {
        addTestResult('Verify OTP', false, result.error!, result);
        toast({
          title: "Verification Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      addTestResult('Verify OTP', false, `Error: ${error}`, { error });
      toast({
        title: "Error",
        description: "Failed to verify OTP",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Test phone existence check
  const testPhoneExists = async () => {
    if (!testPhone.trim()) {
      toast({
        title: "Error",
        description: "Please enter a phone number to test",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const result = await checkPhoneExists(testPhone);
      
      if (result.error) {
        addTestResult('Phone Exists', false, result.error, result);
      } else {
        addTestResult(
          'Phone Exists', 
          true, 
          `${testPhone} ${result.exists ? 'exists' : 'does not exist'} in database`, 
          result
        );
      }
    } catch (error) {
      addTestResult('Phone Exists', false, `Error: ${error}`, { error });
    } finally {
      setIsLoading(false);
    }
  };
  
  // COMPREHENSIVE API DEBUGGING - Deep Investigation
  const testDexatelAPI = async () => {
    setIsLoading(true);
    addTestResult('API Test', true, '🔍 Starting COMPREHENSIVE API Investigation...', {});

    try {
      // Run comprehensive debugging
      const results = await dexatelDebugger.runComprehensiveTest();
      const summary = dexatelDebugger.getSummary();

      // Add each test result
      results.forEach(result => {
        addTestResult(
          result.step,
          result.success,
          result.message,
          result.data
        );
      });

      // Add summary
      addTestResult(
        'Investigation Summary',
        summary.criticalIssues.length === 0,
        `Tests: ${summary.passed}/${summary.total} passed. ${summary.criticalIssues.length > 0 ? `Critical Issues: ${summary.criticalIssues.join(', ')}` : 'All systems operational!'}`,
        summary
      );

      // Show toast with summary
      toast({
        title: summary.criticalIssues.length === 0 ? "Investigation Complete" : "Issues Found",
        description: `${summary.passed}/${summary.total} tests passed. ${summary.criticalIssues.length > 0 ? `${summary.criticalIssues.length} critical issues found.` : 'All systems operational!'}`,
        variant: summary.criticalIssues.length === 0 ? "default" : "destructive"
      });

    } catch (error: any) {
      console.error('🚨 CRITICAL ERROR in investigation:', error);
      addTestResult('Investigation Error', false, `❌ INVESTIGATION FAILED: ${error.message}`, { error: error.toString() });

      toast({
        title: "Investigation Failed",
        description: "Failed to run comprehensive API investigation",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle auth success
  const handleAuthSuccess = (user: any, action: 'login' | 'signup') => {
    addTestResult('Modal Auth', true, `${action} successful via modal`, { user, action });
    setShowModal(false);
  };
  
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Phone Authentication Test
        </h1>
        <p className="text-gray-600">
          Test the phone-only authentication system for Algerian consumers
        </p>
      </div>
      
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              <span className="text-sm">Dexatel API:</span>
              <Badge variant={hasApiKey ? "default" : "destructive"}>
                {hasApiKey ? "Configured" : "Missing"}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="text-sm">Supabase:</span>
              <Badge variant={supabaseUrl ? "default" : "destructive"}>
                {supabaseUrl ? "Connected" : "Not Connected"}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              <span className="text-sm">Network:</span>
              <Badge variant={isOnline ? "default" : "destructive"}>
                {isOnline ? "Online" : "Offline"}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="text-sm">Auth Status:</span>
              <Badge variant={isAuthenticated ? "default" : "secondary"}>
                {isAuthenticated ? "Authenticated" : "Not Authenticated"}
              </Badge>
            </div>
          </div>
          
          {isAuthenticated && user && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Current User</span>
              </div>
              <div className="text-sm text-green-700 space-y-1">
                <div>ID: {user.id}</div>
                <div>Phone: {user.phone || profile?.phone || 'N/A'}</div>
                <div>Role: {profile?.role || 'N/A'}</div>
                <div>Verified: {profile?.phone_verified ? 'Yes' : 'No'}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Manual Testing */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Manual Testing
            </CardTitle>
            <CardDescription>
              Test individual components of the phone authentication system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="test-phone">Test Phone Number</Label>
              <Input
                id="test-phone"
                type="tel"
                value={testPhone}
                onChange={(e) => setTestPhone(e.target.value)}
                placeholder="+213555123456"
                className="font-mono"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="test-otp">OTP Code (for verification test)</Label>
              <Input
                id="test-otp"
                type="text"
                value={testOTP}
                onChange={(e) => setTestOTP(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="123456"
                className="font-mono text-center"
                maxLength={6}
              />
            </div>
            
            <div className="space-y-2">
              <Button
                onClick={testDexatelAPI}
                variant="destructive"
                size="sm"
                className="w-full"
                disabled={isLoading}
              >
                🔍 DEEP API INVESTIGATION
              </Button>

              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={testPhoneValidation}
                  variant="outline"
                  size="sm"
                >
                  Test Validation
                </Button>

                <Button
                  onClick={testPhoneExists}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                >
                  Check Exists
                </Button>

                <Button
                  onClick={testSendOTP}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                >
                  Send OTP
                </Button>

                <Button
                  onClick={testVerifyOTP}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                >
                  Verify OTP
                </Button>
              </div>
            </div>
            
            <div className="border-t pt-4 space-y-2">
              <Button
                onClick={() => setShowModal(true)}
                className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90"
              >
                Test Auth Modal
              </Button>
              
              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={() => triggerAuthModal('cart_add')}
                  variant="outline"
                  size="sm"
                >
                  Trigger Cart Add
                </Button>
                
                <Button
                  onClick={() => triggerAuthModal('checkout')}
                  variant="outline"
                  size="sm"
                >
                  Trigger Checkout
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Test Results
            </CardTitle>
            <CardDescription>
              Recent test results and system responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 text-center py-4">
                  No test results yet. Run some tests to see results here.
                </p>
              ) : (
                testResults.map((result) => (
                  <div
                    key={result.id}
                    className={`p-3 rounded-md border ${
                      result.success 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {result.success ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="font-medium text-sm">
                        {result.test}
                      </span>
                      <span className="text-xs text-gray-500 ml-auto">
                        {result.timestamp}
                      </span>
                    </div>
                    <p className={`text-sm ${
                      result.success ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {result.message}
                    </p>
                    {result.data && (
                      <details className="mt-2">
                        <summary className="text-xs text-gray-600 cursor-pointer">
                          View Details
                        </summary>
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                ))
              )}
            </div>
            
            {testResults.length > 0 && (
              <Button
                onClick={() => setTestResults([])}
                variant="outline"
                size="sm"
                className="w-full mt-4"
              >
                Clear Results
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Modals */}
      <EnhancedPhoneAuthModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSuccess={handleAuthSuccess}
      />
      
      <AutoAuthModal
        delay={10000} // Shorter delay for testing
        triggers={['cart_add', 'checkout']}
        minTimeOnSite={5000}
        oncePerSession={false} // Allow multiple shows for testing
      />
    </div>
  );
}
