import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  Filter,
  Grid3X3,
  List,
  ShoppingCart,
  Star,
  ChevronDown,
  ChevronUp,
  X,
  FileText,
  Package
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useFilter } from '@/contexts/FilterContext';
import { ProductFilterProvider, useProductFilter } from '@/contexts/ProductFilterContext';
import { CategoryNavigation } from '@/components/marketplace/CategoryNavigation';
import { SubcategoryNavigation } from '@/components/marketplace/SubcategoryNavigation';
import { AirbnbStyleProductCard } from '@/components/marketplace/AirbnbStyleProductCard';
import { QuotationRequestModal } from '@/components/marketplace/QuotationRequestModal';
import { AirbnbStyleFilterModal } from '@/components/marketplace/AirbnbStyleFilterModal';
import { ProductFilterModal } from '@/components/marketplace/ProductFilterModal';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';

// Categories for tabs
const categories = [
  { id: 'all', name: 'All Categories' },
  { id: 'tyres', name: 'Tyres & Related Products' },
  { id: 'brakes', name: 'Brake Parts & Systems' },
  { id: 'filters', name: 'Filters' },
  { id: 'oils-fluids', name: 'Oils & Fluids' },
  { id: 'engine', name: 'Engine' },
  { id: 'window-cleaning', name: 'Window Cleaning' },
  { id: 'glow-plug-ignition', name: 'Glow Plug & Ignition' },
  { id: 'wishbones-suspension', name: 'Wishbones & Suspension' },
  { id: 'electrical-systems', name: 'Electrical Systems' },
  { id: 'ignition', name: 'Ignition' },
  { id: 'exterior', name: 'Exterior' },
];

function WholesaleOffersContent() {
  const { t } = useTranslation();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popularity');
  const [activeTab, setActiveTab] = useState('all');

  // Fetch marketplace products from ALL users for each category
  const { products: tyreProducts, isLoading: tyresLoading } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading } = useMarketplaceProducts('brakes');
  const { products: filterProducts, isLoading: filtersLoading } = useMarketplaceProducts('filters');
  const { products: oilsFluidProducts, isLoading: oilsFluidLoading } = useMarketplaceProducts('oils-fluids');
  const { products: engineProducts, isLoading: engineLoading } = useMarketplaceProducts('engine');
  const { products: windowCleaningProducts, isLoading: windowCleaningLoading } = useMarketplaceProducts('window-cleaning');
  const { products: glowPlugIgnitionProducts, isLoading: glowPlugIgnitionLoading } = useMarketplaceProducts('glow-plug-ignition');
  const { products: wishbonesSuspensionProducts, isLoading: wishbonesSuspensionLoading } = useMarketplaceProducts('wishbones-suspension');
  const { products: electricalSystemsProducts, isLoading: electricalSystemsLoading } = useMarketplaceProducts('electrical-systems');

  const {
    tyresFilters,
    setTyresFilters,
    partsFilters,
    setPartsFilters,
    showTyresModal,
    setShowTyresModal,
    showPartsModal,
    setShowPartsModal,
    selectedCategory,
    setSelectedCategory,
    selectedSubcategory,
    setSelectedSubcategory
  } = useFilter();

  // Product filter context for the new filtering system
  const {
    showProductFilterModal,
    setShowProductFilterModal,
    productFilters,
    setProductFilters
  } = useProductFilter();

  // Local state for quotation modal
  const [showQuotationModal, setShowQuotationModal] = useState(false);

  // Filter products to show only wholesale products (products with wholesalePricingTiers)
  // AND only products with 'active' or 'out_of_stock' status (hide draft/discontinued)
  const wholesaleTyreProducts = tyreProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleBrakeProducts = brakeProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleFilterProducts = filterProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleOilsFluidProducts = oilsFluidProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleEngineProducts = engineProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleWindowCleaningProducts = windowCleaningProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleGlowPlugIgnitionProducts = glowPlugIgnitionProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleWishbonesSuspensionProducts = wishbonesSuspensionProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  const wholesaleElectricalSystemsProducts = electricalSystemsProducts.filter(product => {
    // Must have wholesale pricing tiers
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  // Get products based on selected category
  const getProductsForCategory = () => {
    if (selectedCategory === 'tyres') {
      return wholesaleTyreProducts;
    } else if (selectedCategory === 'brakes') {
      return wholesaleBrakeProducts;
    } else if (selectedCategory === 'filters') {
      return wholesaleFilterProducts;
    } else if (selectedCategory === 'oils-fluids') {
      return wholesaleOilsFluidProducts;
    } else if (selectedCategory === 'engine') {
      return wholesaleEngineProducts;
    } else if (selectedCategory === 'window-cleaning') {
      return wholesaleWindowCleaningProducts;
    } else if (selectedCategory === 'glow-plug-ignition') {
      return wholesaleGlowPlugIgnitionProducts;
    } else if (selectedCategory === 'wishbones-suspension') {
      return wholesaleWishbonesSuspensionProducts;
    } else if (selectedCategory === 'electrical-systems') {
      return wholesaleElectricalSystemsProducts;
    } else {
      // "All" category - combine products
      return [...wholesaleTyreProducts, ...wholesaleBrakeProducts, ...wholesaleFilterProducts, ...wholesaleOilsFluidProducts, ...wholesaleEngineProducts, ...wholesaleWindowCleaningProducts, ...wholesaleGlowPlugIgnitionProducts, ...wholesaleWishbonesSuspensionProducts, ...wholesaleElectricalSystemsProducts];
    }
  };

  const products = getProductsForCategory();
  const isLoading = tyresLoading || brakesLoading || filtersLoading || oilsFluidLoading || engineLoading || windowCleaningLoading || glowPlugIgnitionLoading || wishbonesSuspensionLoading || electricalSystemsLoading;

  // Filter products based on selected filters
  const filteredProducts = products.filter(product => {
    // Filter by subcategory if selected
    if (selectedSubcategory && product.subcategory !== selectedSubcategory) {
      return false;
    }

    // Apply tyres filters
    if (product.category === 'tyres' && selectedCategory === 'tyres') {
      const tyreProduct = product as TyreProduct;

      if (tyresFilters.width && tyreProduct.width?.toString() !== tyresFilters.width) {
        return false;
      }

      if (tyresFilters.aspectRatio && tyreProduct.aspectRatio?.toString() !== tyresFilters.aspectRatio) {
        return false;
      }

      if (tyresFilters.rimDiameter && tyreProduct.rimDiameter?.toString() !== tyresFilters.rimDiameter) {
        return false;
      }

      if (tyresFilters.season && tyreProduct.season !== tyresFilters.season) {
        return false;
      }
    }

    // Apply parts filters
    if (product.category === 'brakes' && selectedCategory === 'brakes') {
      // Apply vehicle compatibility filters if needed
      // This would be more complex in a real app
    }

    // Apply filter products filters
    if (product.category === 'filters' && selectedCategory === 'filters') {
      // Apply filter-specific filters if needed
      // This would be more complex in a real app
    }

    // Apply oils & fluids filters
    if (product.category === 'oils-fluids' && selectedCategory === 'oils-fluids') {
      // Apply oils & fluids specific filters if needed
      // This would be more complex in a real app
    }

    return true;
  });

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setSelectedSubcategory('');
    setActiveTab(categoryId);
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategory: string) => {
    setSelectedSubcategory(subcategory);
  };

  // Handle request quote button
  const handleRequestQuote = () => {
    setShowQuotationModal(true);
  };

  // Handle product filter changes
  const handleProductFilterChange = (filters: any) => {
    setProductFilters(filters);
  };

  // Handle filter submission
  const handleFilterSubmit = (filters: any) => {
    if (selectedCategory === 'tyres') {
      setTyresFilters({
        ...tyresFilters,
        ...filters
      });
    } else {
      setPartsFilters({
        ...partsFilters,
        ...filters
      });
    }
  };

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (sortBy === 'price-low') {
      return ((a.wholesalePricingTiers?.[0]?.price || 0) - (b.wholesalePricingTiers?.[0]?.price || 0));
    } else if (sortBy === 'price-high') {
      return ((b.wholesalePricingTiers?.[0]?.price || 0) - (a.wholesalePricingTiers?.[0]?.price || 0));
    } else if (sortBy === 'min-order') {
      return ((a.minimumOrderQuantity || 0) - (b.minimumOrderQuantity || 0));
    } else {
      // Default sort by popularity (random for mock data)
      return 0;
    }
  });

  return (
    <>
      {/* Optimized spacing to position content properly below header */}
      <div className="pt-6">
        {/* Category Navigation - Positioned with proper spacing from header */}
        <CategoryNavigation
          onCategorySelect={handleCategorySelect}
          selectedCategory={selectedCategory}
          section="wholesale"
          onFilterHint={() => {
            console.log('Filter hint triggered for category:', selectedCategory);
          }}
        />

        {/* Main Content - Remove header text and border line, optimize spacing */}
        <section className="pt-4 pb-8">
          <div className="container">
            <div className="flex flex-col space-y-6">
              {/* Subcategory Navigation - No border line above */}
              <SubcategoryNavigation
                selectedCategory={selectedCategory}
                onSubcategorySelect={handleSubcategorySelect}
                selectedSubcategory={selectedSubcategory}
                onRequestQuote={handleRequestQuote}
              />

              {/* Products Grid */}
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'
                  : 'space-y-4'
              }>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="aspect-square bg-muted rounded-xl mb-3"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                        <div className="h-4 bg-muted rounded w-1/3"></div>
                      </div>
                    </div>
                  ))
                ) : sortedProducts.length > 0 ? (
                  sortedProducts.map((product) => (
                    <AirbnbStyleProductCard
                      key={product.id}
                      product={product}
                      section="wholesale"
                    />
                  ))
                ) : (
                  <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                    <div className="bg-muted rounded-full p-3 mb-4">
                      <Package className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">No wholesale products found</h3>
                    <p className="text-muted-foreground max-w-md mb-6">
                      Try adjusting your filters to see more results
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Product Filter Modal - NEW SYSTEM */}
      <ProductFilterModal
        open={showProductFilterModal}
        onOpenChange={setShowProductFilterModal}
        onFiltersChange={handleProductFilterChange}
        initialFilters={productFilters}
        availableProducts={products}
      />

      {/* Quotation Request Modal */}
      <QuotationRequestModal
        isOpen={showQuotationModal}
        onClose={() => setShowQuotationModal(false)}
        subcategory={selectedSubcategory || (selectedCategory === 'tyres' ? 'Tyres' : 'Brake Parts')}
        section="wholesale"
      />
    </>
  );
}

// Main component that provides the ProductFilterProvider
export default function WholesaleOffers() {
  return (
    <ProductFilterProvider>
      <WholesaleOffersContent />
    </ProductFilterProvider>
  );
}
