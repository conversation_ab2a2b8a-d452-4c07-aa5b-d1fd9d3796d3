import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  Filter,
  Grid3X3,
  List,
  ShoppingCart,
  Star,
  ChevronDown,
  ChevronUp,
  X,
  FileText
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useFilter } from '@/contexts/FilterContext';
import { useProductFilter, ProductFilterProvider } from '@/contexts/ProductFilterContext';
import { CategoryNavigation } from '@/components/marketplace/CategoryNavigation';
import { SubcategoryNavigation } from '@/components/marketplace/SubcategoryNavigation';
import { AirbnbStyleProductCard } from '@/components/marketplace/AirbnbStyleProductCard';
import { QuotationRequestModal } from '@/components/marketplace/QuotationRequestModal';
import { ProductFilterModal } from '@/components/marketplace/ProductFilterModal';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';

// Internal component that uses the product filter context
function MyVehiclePartsContent() {
  const { t } = useTranslation();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popularity');

  // Fetch marketplace products from ALL users for each category
  const { products: tyreProducts, isLoading: tyresLoading } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading } = useMarketplaceProducts('brakes');
  const { products: filterProducts, isLoading: filtersLoading } = useMarketplaceProducts('filters');
  const { products: oilsFluidProducts, isLoading: oilsFluidLoading } = useMarketplaceProducts('oils-fluids');
  const { products: engineProducts, isLoading: engineLoading } = useMarketplaceProducts('engine');
  const { products: windowCleaningProducts, isLoading: windowCleaningLoading } = useMarketplaceProducts('window-cleaning');
  const { products: glowPlugIgnitionProducts, isLoading: glowPlugIgnitionLoading } = useMarketplaceProducts('glow-plug-ignition');
  const { products: wishbonesSuspensionProducts, isLoading: wishbonesSuspensionLoading } = useMarketplaceProducts('wishbones-suspension');
  const { products: electricalSystemsProducts, isLoading: electricalSystemsLoading } = useMarketplaceProducts('electrical-systems');

  const {
    selectedCategory,
    setSelectedCategory,
    selectedSubcategory,
    setSelectedSubcategory
  } = useFilter();

  // Product filter context for the new filtering system
  const {
    productFilters,
    setProductFilters,
    showProductFilterModal,
    setShowProductFilterModal
  } = useProductFilter();

  // Local state for quotation modal
  const [showQuotationModal, setShowQuotationModal] = useState(false);

  // Filter products to show only retail products (products with retailPrice)
  // AND only products with 'active' or 'out_of_stock' status (hide draft/discontinued)
  const retailTyreProducts = tyreProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailBrakeProducts = brakeProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailFilterProducts = filterProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailOilsFluidProducts = oilsFluidProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailEngineProducts = engineProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailWindowCleaningProducts = windowCleaningProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailGlowPlugIgnitionProducts = glowPlugIgnitionProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailWishbonesSuspensionProducts = wishbonesSuspensionProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  const retailElectricalSystemsProducts = electricalSystemsProducts.filter(product => {
    // Must have retail price
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  // Get products based on selected category
  const getProductsForCategory = () => {
    if (selectedCategory === 'tyres') {
      return retailTyreProducts;
    } else if (selectedCategory === 'brakes') {
      return retailBrakeProducts;
    } else if (selectedCategory === 'filters') {
      return retailFilterProducts;
    } else if (selectedCategory === 'oils-fluids') {
      return retailOilsFluidProducts;
    } else if (selectedCategory === 'engine') {
      return retailEngineProducts;
    } else if (selectedCategory === 'window-cleaning') {
      return retailWindowCleaningProducts;
    } else if (selectedCategory === 'glow-plug-ignition') {
      return retailGlowPlugIgnitionProducts;
    } else if (selectedCategory === 'wishbones-suspension') {
      return retailWishbonesSuspensionProducts;
    } else if (selectedCategory === 'electrical-systems') {
      return retailElectricalSystemsProducts;
    } else {
      // "All" category - combine products
      return [...retailTyreProducts, ...retailBrakeProducts, ...retailFilterProducts, ...retailOilsFluidProducts, ...retailEngineProducts, ...retailWindowCleaningProducts, ...retailGlowPlugIgnitionProducts, ...retailWishbonesSuspensionProducts, ...retailElectricalSystemsProducts];
    }
  };

  const products = getProductsForCategory();
  const isLoading = tyresLoading || brakesLoading || filtersLoading || oilsFluidLoading || engineLoading || windowCleaningLoading || glowPlugIgnitionLoading || wishbonesSuspensionLoading || electricalSystemsLoading;

  // DEBUG: Log product counts for investigation
  console.log('🔍 Marketplace Product Debug:', {
    totalTyreProducts: tyreProducts.length,
    totalBrakeProducts: brakeProducts.length,
    totalFilterProducts: filterProducts.length,
    totalOilsFluidProducts: oilsFluidProducts.length,
    totalEngineProducts: engineProducts.length,
    totalWindowCleaningProducts: windowCleaningProducts.length,
    totalGlowPlugIgnitionProducts: glowPlugIgnitionProducts.length,
    totalWishbonesSuspensionProducts: wishbonesSuspensionProducts.length,
    totalElectricalSystemsProducts: electricalSystemsProducts.length,
    retailTyreProducts: retailTyreProducts.length,
    retailBrakeProducts: retailBrakeProducts.length,
    retailFilterProducts: retailFilterProducts.length,
    retailOilsFluidProducts: retailOilsFluidProducts.length,
    retailEngineProducts: retailEngineProducts.length,
    retailWindowCleaningProducts: retailWindowCleaningProducts.length,
    retailGlowPlugIgnitionProducts: retailGlowPlugIgnitionProducts.length,
    retailWishbonesSuspensionProducts: retailWishbonesSuspensionProducts.length,
    retailElectricalSystemsProducts: retailElectricalSystemsProducts.length,
    combinedProducts: products.length,
    selectedCategory,
    selectedSubcategory
  });

  // Filter products based on product filters (NOT vehicle selection)
  const filteredProducts = products.filter(product => {
    // Filter by subcategory if selected
    if (selectedSubcategory && product.subcategory !== selectedSubcategory) {
      return false;
    }

    // Apply price range filter
    const price = product.retailPrice || 0;
    if (price < productFilters.priceRange[0] || price > productFilters.priceRange[1]) {
      return false;
    }

    // Apply brand filter
    if (productFilters.brands.length > 0 && !productFilters.brands.includes(product.brand || '')) {
      return false;
    }

    // Apply stock status filter
    if (productFilters.stockStatus.length > 0) {
      const stockStatus = product.stockQuantity > 10 ? 'In Stock' :
                         product.stockQuantity > 0 ? 'Low Stock' : 'Pre-order';
      if (!productFilters.stockStatus.includes(stockStatus)) {
        return false;
      }
    }

    // Apply category filter
    if (productFilters.categories.length > 0) {
      const categoryName = product.category === 'tyres' ? 'Tyres' :
                          product.category === 'brakes' ? 'Brake Parts' :
                          product.category === 'filters' ? 'Filters' :
                          product.category === 'oils-fluids' ? 'Oils & Fluids' :
                          product.category === 'engine' ? 'Engine' :
                          product.category === 'window-cleaning' ? 'Window Cleaning' :
                          product.category === 'glow-plug-ignition' ? 'Glow Plug & Ignition' :
                          product.category === 'wishbones-suspension' ? 'Wishbones & Suspension' :
                          product.category === 'electrical-systems' ? 'Electrical Systems' : 'Other';
      if (!productFilters.categories.includes(categoryName)) {
        return false;
      }
    }

    return true;
  });

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setSelectedSubcategory('');
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategory: string) => {
    setSelectedSubcategory(subcategory);
  };

  // Handle request quote button
  const handleRequestQuote = () => {
    setShowQuotationModal(true);
  };

  // Handle product filter changes
  const handleProductFilterChange = (filters: any) => {
    setProductFilters(filters);
  };

  // DEBUG: Log filtering results
  console.log('🔍 Marketplace Filter Debug:', {
    productsBeforeFiltering: products.length,
    productsAfterFiltering: filteredProducts.length,
    activeFilters: {
      subcategory: selectedSubcategory,
      priceRange: productFilters.priceRange,
      brands: productFilters.brands,
      stockStatus: productFilters.stockStatus,
      categories: productFilters.categories
    }
  });

  // Sort products based on product filter sortBy or local sortBy
  const currentSortBy = productFilters.sortBy !== 'popularity' ? productFilters.sortBy : sortBy;
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (currentSortBy === 'price-low') {
      return (a.retailPrice || 0) - (b.retailPrice || 0);
    } else if (currentSortBy === 'price-high') {
      return (b.retailPrice || 0) - (a.retailPrice || 0);
    } else if (currentSortBy === 'rating') {
      // Sort by rating (mock data doesn't have ratings, so random)
      return 0;
    } else if (currentSortBy === 'newest') {
      // Sort by newest (mock data doesn't have dates, so random)
      return 0;
    } else {
      // Default sort by popularity (random for mock data)
      return 0;
    }
  });

  return (
    <>
      {/* Optimized spacing to position content properly below header */}
      <div className="pt-6">
        {/* Category Navigation - Positioned with proper spacing from header */}
        <CategoryNavigation
          onCategorySelect={handleCategorySelect}
          selectedCategory={selectedCategory}
          section="retail"
          onFilterHint={() => {
            // Optional: Add any additional filter hint behavior
            console.log('Filter hint triggered for category:', selectedCategory);
          }}
        />

        {/* Main Content - Remove border line and optimize spacing */}
        <section className="pt-4 pb-8">
          <div className="container">
            <div className="flex flex-col space-y-6">
              {/* Subcategory Navigation - No border line above */}
              <SubcategoryNavigation
                selectedCategory={selectedCategory}
                onSubcategorySelect={handleSubcategorySelect}
                selectedSubcategory={selectedSubcategory}
                onRequestQuote={handleRequestQuote}
              />

              {/* Products Grid - 5 columns like Airbnb */}
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'
                  : 'space-y-4'
              }>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="aspect-square bg-muted rounded-xl mb-3"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                        <div className="h-4 bg-muted rounded w-1/3"></div>
                      </div>
                    </div>
                  ))
                ) : sortedProducts.length > 0 ? (
                  sortedProducts.map((product) => (
                    <AirbnbStyleProductCard
                      key={product.id}
                      product={product}
                      section="retail"
                    />
                  ))
                ) : (
                  <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                    <div className="bg-muted rounded-full p-3 mb-4">
                      <X className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">{t('marketplace.noProductsFound')}</h3>
                    <p className="text-muted-foreground max-w-md mb-6">
                      {t('marketplace.tryDifferentFilters')}
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Clear product filters
                        setProductFilters({
                          priceRange: [0, 10000],
                          brands: [],
                          stockStatus: [],
                          ratings: [],
                          categories: [],
                          sortBy: 'popularity'
                        });
                        setSelectedSubcategory('');
                      }}
                    >
                      {t('marketplace.clearFilters')}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Product Filter Modal - NEW SYSTEM */}
      <ProductFilterModal
        open={showProductFilterModal}
        onOpenChange={setShowProductFilterModal}
        onFiltersChange={handleProductFilterChange}
        initialFilters={productFilters}
        availableProducts={products}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />

      {/* Quotation Request Modal */}
      <QuotationRequestModal
        isOpen={showQuotationModal}
        onClose={() => setShowQuotationModal(false)}
        subcategory={selectedSubcategory || (selectedCategory === 'tyres' ? 'Tyres' : 'Brake Parts')}
        section="retail"
      />
    </>
  );
}

// Main component that provides the ProductFilterProvider
export default function MyVehicleParts() {
  return (
    <ProductFilterProvider>
      <MyVehiclePartsContent />
    </ProductFilterProvider>
  );
}
