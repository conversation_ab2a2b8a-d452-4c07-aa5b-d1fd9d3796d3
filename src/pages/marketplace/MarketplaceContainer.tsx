import React from 'react';
import { useLocation } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import MyVehicleParts from './MyVehicleParts';
import WholesaleOffers from './WholesaleOffers';
import MarketplaceHome from './MarketplaceHome';
import { FilterProvider } from '@/contexts/FilterContext';

/**
 * MarketplaceContainer component
 *
 * This component serves as a container for the marketplace sections.
 * It renders the appropriate content based on the current path.
 *
 * For the root path (/), it renders the MyVehicleParts component by default.
 * For /my-vehicle-parts, it renders the MyVehicleParts component.
 * For /wholesale-offers, it renders the WholesaleOffers component.
 */
export default function MarketplaceContainer() {
  const location = useLocation();
  const path = location.pathname;

  // Determine which content to render based on the path
  const renderContent = () => {
    if (path === '/') {
      // For the root path, render MyVehicleParts by default
      return <MyVehicleParts />;
    } else if (path === '/my-vehicle-parts') {
      return <MyVehicleParts />;
    } else if (path === '/wholesale-offers') {
      return <WholesaleOffers />;
    } else {
      // Fallback to MarketplaceHome for any other paths
      return <MarketplaceHome />;
    }
  };

  return (
    <FilterProvider>
      <MarketplaceLayout>
        {renderContent()}
      </MarketplaceLayout>
    </FilterProvider>
  );
}
