import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { getProductById } from '@/services/productService';
import { isValidProductId } from '@/utils/productUtils';

export default function ProductPageTest() {
  const { productId } = useParams<{ productId: string }>();
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      console.log(`[TEST] Starting fetch for: ${productId}`);
      
      if (!productId) {
        setError('No product ID provided');
        setLoading(false);
        return;
      }

      if (!isValidProductId(productId)) {
        setError('Invalid product ID format');
        setLoading(false);
        return;
      }

      try {
        setError(null);
        const foundProduct = await getProductById(productId);
        
        console.log(`[TEST] Product fetch result:`, foundProduct);
        
        if (foundProduct) {
          setProduct(foundProduct);
        } else {
          setError(`Product not found: ${productId}`);
        }
      } catch (error) {
        console.error('[TEST] Error:', error);
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <h1 className="text-2xl font-bold mb-4">Product Page Test</h1>
        
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold mb-2">Debug Info</h3>
            <p><strong>Product ID:</strong> {productId}</p>
            <p><strong>Loading:</strong> {loading ? 'true' : 'false'}</p>
            <p><strong>Error:</strong> {error || 'none'}</p>
            <p><strong>Product Found:</strong> {product ? 'yes' : 'no'}</p>
          </div>

          {loading && (
            <div className="p-4 bg-yellow-50 rounded-lg">
              <p>Loading product...</p>
            </div>
          )}

          {error && (
            <div className="p-4 bg-red-50 rounded-lg">
              <p className="text-red-800">Error: {error}</p>
            </div>
          )}

          {product && (
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-semibold mb-2">Product Found!</h3>
              <div className="space-y-2 text-sm">
                <p><strong>ID:</strong> {product.id}</p>
                <p><strong>Name:</strong> {product.name}</p>
                <p><strong>Category:</strong> {product.category}</p>
                <p><strong>Manufacturer:</strong> {product.manufacturer}</p>
                <p><strong>Stock:</strong> {product.stockQuantity}</p>
                <p><strong>Min Order Qty:</strong> {product.minimumOrderQuantity || 'not set'}</p>
                <p><strong>Marketplace Section:</strong> {product.marketplaceSection || 'not set'}</p>
                <p><strong>Wholesale Tiers:</strong> {product.wholesalePricingTiers ? product.wholesalePricingTiers.length : 0}</p>
                <p><strong>Retail Price:</strong> {product.retailPrice || 'not set'}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </MarketplaceLayout>
  );
}
