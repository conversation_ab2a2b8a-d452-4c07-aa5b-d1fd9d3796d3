import { useState, useCallback, useEffect, useRef } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { useTranslation } from 'react-i18next';
import { ProductsDataGrid } from '@/features/products/components/ProductsDataGrid';
import { CategoryHeader } from '@/features/products/components/CategoryHeader';
import { BarcodeScanner } from '@/features/products/components/BarcodeScanner';
import { ProductFormDialog } from '@/features/products/components/ProductFormDialog';
import { ProductEditDialog } from '@/features/products/components/ProductEditDialog';
import { ImportDialog } from '@/features/products/components/ImportDialog';
import { ExportDialog } from '@/features/products/components/ExportDialog';
import { QuickEditPanel } from '@/features/products/components/QuickEditPanel';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { TyreProduct } from '@/features/products/types/product.types';
import { useProducts } from '@/features/products/hooks/useProductsFactory';
import { getTableConfigForCategory } from '@/features/products/utils/tableConfigs';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Grid } from 'lucide-react';
import { toast } from 'sonner';
import { PhotoProvider } from 'react-photo-view';
import 'react-photo-view/dist/react-photo-view.css';
import { useParams, useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UserContext';


// Mock categories for now
const categories = [
  { id: 'tyres', name: 'Tyres & Related Products' },
  { id: 'all-other-categories', name: 'All Other Categories' },
  // Add more categories as they are implemented
];

export default function ProductsDataGridPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { category } = useParams<{ category: string }>();
  const [activeCategory, setActiveCategory] = useState<string>('tyres');
  const [isScannerOpen, setIsScannerOpen] = useState(false);

  // Create the ref outside the useEffect to persist across renders
  const isInitialMount = useRef(true);

  // Split into two separate useEffect hooks for clarity and reliability

  // First useEffect: Handle initial URL synchronization (runs only once)
  useEffect(() => {
    // On initial mount, sync state with URL parameter
    if (category && categories.some(c => c.id === category)) {
      // If URL has valid category, set it as active
      setActiveCategory(category);
    } else if (category) {
      // If URL has invalid category, redirect to tyres
      console.warn(`Invalid category: ${category}, redirecting to tyres`);
      navigate('/app/products-table/tyres', { replace: true });
    } else {
      // If no category in URL, add default category to URL
      navigate(`/app/products-table/${activeCategory}`, { replace: true });
    }

    // Add event listener to prevent tab switching issues
    const handleVisibilityChange = () => {
      // Reset the user interaction flag when the tab becomes visible again
      if (document.visibilityState === 'visible') {
        userClickedTabRef.current = false;

        // Clear any pending timers when visibility changes
        if (categoryChangeTimerRef.current !== null) {
          window.clearTimeout(categoryChangeTimerRef.current);
          categoryChangeTimerRef.current = null;
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      // Clear any pending timers when component unmounts
      if (categoryChangeTimerRef.current !== null) {
        window.clearTimeout(categoryChangeTimerRef.current);
        categoryChangeTimerRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array ensures this runs only once on mount

  // Track user interaction with tabs
  const userClickedTabRef = useRef(false);

  // Debounce timer for category changes
  const categoryChangeTimerRef = useRef<number | null>(null);

  // Second useEffect: Handle category changes after initial mount
  useEffect(() => {
    // Skip the first render
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Handle URL updates when tab selection changes
    if (category !== activeCategory) {
      // If activeCategory changed due to user clicking a tab, update URL
      if (userClickedTabRef.current) {
        // Clear any existing timer
        if (categoryChangeTimerRef.current !== null) {
          window.clearTimeout(categoryChangeTimerRef.current);
        }

        // Debounce the URL update to prevent rapid navigation
        categoryChangeTimerRef.current = window.setTimeout(() => {
          navigate(`/app/products-table/${activeCategory}`, { replace: true });
          userClickedTabRef.current = false;
          categoryChangeTimerRef.current = null;
        }, 100);
      }
      // If URL changed externally, update state
      else if (category && categories.some(c => c.id === category)) {
        // Clear any existing timer
        if (categoryChangeTimerRef.current !== null) {
          window.clearTimeout(categoryChangeTimerRef.current);
          categoryChangeTimerRef.current = null;
        }

        setActiveCategory(category);
      }
    }

    // Cleanup function to clear any pending timers
    return () => {
      if (categoryChangeTimerRef.current !== null) {
        window.clearTimeout(categoryChangeTimerRef.current);
        categoryChangeTimerRef.current = null;
      }
    };
  }, [activeCategory, category, categories, navigate]);

  // Get user role information
  const { userRole, isSupplier, isMerchant } = useUser();

  console.log(`ProductsDataGridPage using userRole=${userRole}, isSupplier=${isSupplier()}, isMerchant=${isMerchant()}`);

  // Get table configuration for the active category, filtered by user role
  const tableConfig = getTableConfigForCategory(activeCategory, userRole);

  // Fetch products for the active category
  const {
    products,
    isLoading,
    refetch,
    selectedProductIds,
    setSelectedProductIds,
    updateProduct,
    addProduct,
    deleteProduct,
    deleteProducts,
  } = useProducts(activeCategory);

  // State for dialogs
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<TyreProduct | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastEditedRowId, setLastEditedRowId] = useState<string | null>(null);
  const [partArticleNumberForNewProduct, setPartArticleNumberForNewProduct] = useState<string>('');
  const [isQuickEditOpen, setIsQuickEditOpen] = useState(false);
  const [quickEditProduct, setQuickEditProduct] = useState<TyreProduct | null>(null);

  // Handle toolbar actions
  const handleAddProduct = () => {
    setIsAddDialogOpen(true);
  };

  const handleImport = () => {
    setIsImportDialogOpen(true);
  };

  const handleExport = () => {
    setIsExportDialogOpen(true);
  };

  const handleScanBarcode = () => {
    setIsScannerOpen(true);
  };

  const handleBarcodeScan = (partArticleNumber: string) => {
    toast.success(`Scanned part article number: ${partArticleNumber}`);

    // Search for the product with this part article number
    const product = products.find(p => p.partArticleNumber === partArticleNumber);

    if (product) {
      toast.success(`Found product: ${product.name}`);

      // Highlight the row by setting it as the last edited row
      // This uses the same highlighting mechanism as cell editing
      const rowId = product.id;
      setLastEditedRowId(rowId);

      // Clear the highlight after 5 seconds
      setTimeout(() => {
        setLastEditedRowId(null);
      }, 5000);

      // Open the quick edit panel
      setQuickEditProduct(product as TyreProduct);
      setIsQuickEditOpen(true);
    } else {
      toast.error(`No product found with part article number: ${partArticleNumber}`);

      // Optionally, offer to create a new product with this part article number
      const shouldCreate = window.confirm(`Would you like to create a new product with part article number ${partArticleNumber}?`);

      if (shouldCreate) {
        // Pre-fill the part article number field in the add product dialog
        setPartArticleNumberForNewProduct(partArticleNumber);
        setIsAddDialogOpen(true);
      }
    }
  };

  const handleDeleteSelected = () => {
    if (selectedProductIds.length > 0) {
      setIsBulkDeleteDialogOpen(true);
    }
  };

  const handleRowClick = (product: any) => {
    // You can implement row click behavior if needed
    // For now, we'll just log the product
    console.log('Clicked product:', product);
  };

  const handleCellEdit = (rowId: string, columnId: string, value: any) => {
    // In a real app, this would update the data in your backend
    // Simulate a network delay to show the saving state
    console.log(`ProductsDataGridPage.handleCellEdit called: rowId=${rowId}, columnId=${columnId}, userRole=${userRole}`);

    return new Promise<void>((resolve, reject) => {
      setTimeout(() => {
        try {
          // Check role-specific permissions again as a safeguard
          if (
            (columnId === 'wholesalePricingTiers' && !isSupplier()) ||
            (columnId === 'retailPrice' && !isMerchant())
          ) {
            console.error(`Permission denied: ${userRole} cannot edit ${columnId}`);
            toast.error(`You don't have permission to edit this field as a ${userRole} user.`);
            reject(new Error(`Permission denied for ${userRole} to edit ${columnId}`));
            return;
          }

          // Update the product in the React Query cache
          updateProduct(rowId, { [columnId]: value });

          // Log the update
          console.log('Cell edit successful:', { rowId, columnId, value, userRole });

          // Show success message
          toast.success(`Updated ${columnId} for product ID ${rowId}`);

          // Resolve the promise to indicate the update is complete
          resolve();
        } catch (error) {
          console.error('Error in handleCellEdit:', error);
          reject(error);
        }
      }, 500); // Simulate a 500ms delay for the API call
    });
  };

  // Handle edit row
  const handleEditRow = useCallback((product: TyreProduct) => {
    setSelectedProduct(product);
    setIsEditDialogOpen(true);
  }, []);

  // Handle delete row
  const handleDeleteRow = useCallback((product: TyreProduct) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  }, []);

  // Handle save product (add)
  const handleSaveProduct = async (product: Partial<TyreProduct>) => {
    setIsSubmitting(true);
    try {
      await addProduct(product);
      toast.success('Product added successfully');
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error('Error adding product:', error);
      toast.error('Failed to add product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle import products
  const handleImportProducts = async (products: Partial<TyreProduct>[]) => {
    setIsSubmitting(true);
    try {
      // Import products one by one
      for (const product of products) {
        await addProduct(product);
      }
      toast.success(`${products.length} products imported successfully`);
      return Promise.resolve();
    } catch (error) {
      console.error('Error importing products:', error);
      toast.error('Failed to import products');
      return Promise.reject(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle update product (edit)
  const handleUpdateProduct = async (product: TyreProduct) => {
    setIsSubmitting(true);
    try {
      updateProduct(product.id, product);
      toast.success('Product updated successfully');
      setIsEditDialogOpen(false);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('Failed to update product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!selectedProduct) return;

    setIsSubmitting(true);
    try {
      await deleteProduct(selectedProduct.id);
      toast.success('Product deleted successfully');
      setIsDeleteDialogOpen(false);
      setSelectedProduct(null);
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle confirm bulk delete
  const handleConfirmBulkDelete = async () => {
    if (selectedProductIds.length === 0) return;

    setIsSubmitting(true);
    try {
      await deleteProducts(selectedProductIds);
      toast.success(`${selectedProductIds.length} products deleted successfully`);
      setIsBulkDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting products:', error);
      toast.error('Failed to delete products');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <PhotoProvider maskOpacity={0.8}>
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('productsDataGrid.productLibrary')}</h1>
            <p className="text-muted-foreground mt-1">
              {t('productsDataGrid.manageProductCatalog')}
            </p>
          </div>

          <div className="overflow-hidden">
            <Tabs
              defaultValue="tyres"
              value={activeCategory}
              // Use a custom handler to prevent unnecessary state updates
              onValueChange={(value) => {
                // Only update if the value is different to prevent unnecessary re-renders
                if (value !== activeCategory) {
                  // Clear any existing timer
                  if (categoryChangeTimerRef.current !== null) {
                    window.clearTimeout(categoryChangeTimerRef.current);
                  }

                  // Mark that this change was initiated by user clicking a tab
                  userClickedTabRef.current = true;

                  // Use a debounced update to prevent rapid state changes
                  categoryChangeTimerRef.current = window.setTimeout(() => {
                    setActiveCategory(value);
                    categoryChangeTimerRef.current = null;
                  }, 50);
                }
              }}
              className="w-full"
            >
              <div className="border-b border-[#e1e1e1] mb-2">
                <TabsList className="bg-transparent h-auto p-0 w-full flex justify-start gap-4 overflow-x-auto">
                  {categories.map(cat => (
                    <TabsTrigger
                      key={cat.id}
                      value={cat.id}
                      className="data-[state=active]:border-b-2 data-[state=active]:border-electric-orange data-[state=active]:bg-transparent data-[state=active]:shadow-none px-5 py-3 h-12 rounded-t-md data-[state=active]:text-electric-orange transition-all"
                      role="tab"
                      onClick={() => {
                        // Explicitly mark that this tab was clicked by the user
                        userClickedTabRef.current = true;
                      }}
                    >
                      <Grid className="w-4 h-4 mr-2" />
                      <span className="font-medium">{cat.name}</span>
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
            </Tabs>
          </div>

          <CategoryHeader
            tableConfig={tableConfig}
            selectedRows={selectedProductIds.length}
            onAddProduct={handleAddProduct}
            onImport={handleImport}
            onExport={handleExport}
            onRefresh={refetch}
            onScanBarcode={handleScanBarcode}
            onDeleteSelected={handleDeleteSelected}
          />

          <ProductsDataGrid
            data={products}
            tableConfig={tableConfig}
            isLoading={isLoading}
            onRowClick={handleRowClick}
            onCellEdit={handleCellEdit}
            onEditRow={handleEditRow}
            onDeleteRow={handleDeleteRow}
            onSelectionChange={(ids) => {
              // Update the selectedProductIds in the useProducts hook
              console.log('Selected IDs:', ids);
              // We need to update the selectedProductIds state in the useProducts hook
              // Since we're using the hook's selectedProductIds, we need to manually update it
              setSelectedProductIds(ids);
            }}
            highlightedRowId={lastEditedRowId}
          />

          {/* Dialogs */}
          <BarcodeScanner
            isOpen={isScannerOpen}
            onClose={() => setIsScannerOpen(false)}
            onScan={handleBarcodeScan}
          />

          {isAddDialogOpen && (
            <ProductFormDialog
              isOpen={true}
              onClose={() => setIsAddDialogOpen(false)}
              onSave={handleSaveProduct}
              categories={categories}
              initialBarcode={partArticleNumberForNewProduct}
              initialCategory={activeCategory}
            />
          )}

          {selectedProduct && (
            <ProductEditDialog
              isOpen={isEditDialogOpen}
              onClose={() => {
                setIsEditDialogOpen(false);
                setSelectedProduct(null);
              }}
              onSave={handleUpdateProduct}
              product={selectedProduct}
              categories={categories}
            />
          )}

          <ConfirmationDialog
            isOpen={isDeleteDialogOpen}
            onClose={() => {
              setIsDeleteDialogOpen(false);
              setSelectedProduct(null);
            }}
            onConfirm={handleConfirmDelete}
            title="Delete Product"
            description={`Are you sure you want to delete ${selectedProduct?.name || 'this product'}? This action cannot be undone.`}
            confirmText="Delete"
            variant="danger"
            isLoading={isSubmitting}
          />

          <ConfirmationDialog
            isOpen={isBulkDeleteDialogOpen}
            onClose={() => setIsBulkDeleteDialogOpen(false)}
            onConfirm={handleConfirmBulkDelete}
            title="Delete Selected Products"
            description={`Are you sure you want to delete ${selectedProductIds.length} selected products? This action cannot be undone.`}
            confirmText="Delete All"
            variant="danger"
            isLoading={isSubmitting}
          />

          <ImportDialog
            isOpen={isImportDialogOpen}
            onClose={() => setIsImportDialogOpen(false)}
            onImport={handleImportProducts}
            categoryId={activeCategory}
          />

          <ExportDialog
            isOpen={isExportDialogOpen}
            onClose={() => setIsExportDialogOpen(false)}
            products={products as TyreProduct[]}
            selectedProductIds={selectedProductIds}
            tableConfig={tableConfig}
          />

          {/* Quick Edit Panel */}
          {quickEditProduct && isQuickEditOpen && (
            <div className="fixed bottom-4 right-4 z-50">
              <QuickEditPanel
                product={quickEditProduct}
                onSave={(product) => {
                  updateProduct(product.id, product);
                  setQuickEditProduct(null);
                  setIsQuickEditOpen(false);
                  return Promise.resolve();
                }}
                onClose={() => {
                  setQuickEditProduct(null);
                  setIsQuickEditOpen(false);
                }}
              />
            </div>
          )}
        </div>
      </PhotoProvider>
    </AdminLayout>
  );
}
