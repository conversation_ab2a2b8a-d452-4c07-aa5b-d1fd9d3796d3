import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SmartMapSelector } from '@/components/location/SmartMapSelector';
import { GooglePlacesSearch } from '@/components/location/GooglePlacesSearch';
import { useLocation } from '@/contexts/LocationContext';
import { Badge } from '@/components/ui/badge';
import { MapPin, Key, Wifi, CheckCircle } from 'lucide-react';

export function GoogleMapsTest() {
  const { selectedLocation, getLocationString } = useLocation();
  const hasApiKey = !!import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-midnight-blue mb-2">
            🗺️ Google Maps Integration Test
          </h1>
          <p className="text-gray-600">
            Testing enhanced location selection with Google Maps + 100% accurate Algeria detection
          </p>
        </div>

        {/* API Key Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Key className="h-5 w-5" />
              <span>Google Maps API Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {hasApiKey ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-green-700">API Key Configured</span>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Enhanced Mode
                    </Badge>
                  </>
                ) : (
                  <>
                    <Key className="h-5 w-5 text-yellow-500" />
                    <span className="text-yellow-700">No API Key</span>
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      Fallback Mode
                    </Badge>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-1 text-sm text-gray-500">
                <Wifi className="h-4 w-4" />
                <span>Online</span>
              </div>
            </div>
            
            {!hasApiKey && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  <strong>To enable Google Maps:</strong> Add <code>VITE_GOOGLE_MAPS_API_KEY</code> to your .env file
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Google Places Search Test */}
        {hasApiKey && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>🔍 Google Places Search</CardTitle>
              <CardDescription>
                Search for locations in Algeria with rich autocomplete
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GooglePlacesSearch
                placeholder="Try searching for 'Tlemcen', 'Oran', or 'Constantine'..."
                onPlaceSelect={(place) => {
                  console.log('🗺️ Selected place:', place);
                }}
              />
            </CardContent>
          </Card>
        )}

        {/* Smart Map Selector Test */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>🗺️ Smart Map Selector</CardTitle>
            <CardDescription>
              Intelligent map with automatic Google Maps/OpenStreetMap selection
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SmartMapSelector
              height="400px"
              showCurrentLocationButton={true}
              showSearch={hasApiKey}
              onLocationSelect={(lat, lng) => {
                console.log('📍 Location selected:', { lat, lng });
              }}
            />
          </CardContent>
        </Card>

        {/* Selected Location Display */}
        {selectedLocation && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-[#fa7b00]" />
                <span>Selected Location</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Formatted Address:</label>
                  <p className="text-gray-900">{getLocationString()}</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Wilaya (French):</label>
                    <p className="text-gray-900">{selectedLocation.wilaya}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Wilaya (Arabic):</label>
                    <p className="text-gray-900 text-right">{selectedLocation.wilaya_ar}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Latitude:</label>
                    <p className="text-gray-900 font-mono">{selectedLocation.coordinates.lat.toFixed(6)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Longitude:</label>
                    <p className="text-gray-900 font-mono">{selectedLocation.coordinates.lng.toFixed(6)}</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-green-800">
                      ✅ 100% Accurate Wilaya Detection
                    </span>
                  </div>
                  <p className="text-xs text-green-700 mt-1">
                    Location validated against official Algeria administrative boundaries
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Features Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>🆚 Features Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Google Maps Column */}
              <div>
                <h3 className="font-semibold text-green-700 mb-3 flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4" />
                  <span>With Google Maps API</span>
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>Rich Arabic place names</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>Google Places search</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>Street-level accuracy</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>Familiar Google Maps UI</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>Landmarks & businesses</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>100% accurate wilaya detection</span>
                  </li>
                </ul>
              </div>

              {/* OpenStreetMap Column */}
              <div>
                <h3 className="font-semibold text-blue-700 mb-3 flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>OpenStreetMap Fallback</span>
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center space-x-2">
                    <span className="text-blue-500">📍</span>
                    <span>Basic map functionality</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-blue-500">📍</span>
                    <span>Click-based selection</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-blue-500">📍</span>
                    <span>No API key required</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-blue-500">📍</span>
                    <span>Always available</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-blue-500">📍</span>
                    <span>Open source maps</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-500">✅</span>
                    <span>100% accurate wilaya detection</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
