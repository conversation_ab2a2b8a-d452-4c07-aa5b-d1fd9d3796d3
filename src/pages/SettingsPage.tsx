import React, { useState } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ProfileSettings } from '@/components/settings/ProfileSettings';
import { SecuritySettings } from '@/components/settings/SecuritySettings';
import { NotificationSettings } from '@/components/settings/NotificationSettings';
import { AccountPreferences } from '@/components/settings/AccountPreferences';
import { useTranslation } from 'react-i18next';
import { useUser } from '@/contexts/UserContext';
import { 
  User, 
  Shield, 
  Bell, 
  Settings, 
  Building, 
  Store 
} from 'lucide-react';

export default function SettingsPage() {
  const { t } = useTranslation();
  const { isSupplier, isMerchant } = useUser();
  const [activeTab, setActiveTab] = useState('profile');
  
  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('settings.title')}</h1>
          <p className="text-muted-foreground mt-1">
            {t('settings.description')}
          </p>
        </div>
        
        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border p-4 sticky top-24">
              <div className="flex items-center gap-3 mb-6 pb-4 border-b">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-midnight-blue to-primary text-white font-bold flex items-center justify-center text-lg">
                  {isSupplier() ? <Building className="h-6 w-6" /> : isMerchant() ? <Store className="h-6 w-6" /> : <User className="h-6 w-6" />}
                </div>
                <div>
                  <h3 className="font-medium">
                    {isSupplier() ? t('roles.supplierAndManufacturer') : isMerchant() ? t('roles.merchantRetailer') : t('roles.user')}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.manageYourAccount')}
                  </p>
                </div>
              </div>
              
              <nav className="space-y-1">
                <button
                  className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${activeTab === 'profile' ? 'bg-primary/10 text-primary font-medium' : 'text-muted-foreground hover:bg-muted'}`}
                  onClick={() => setActiveTab('profile')}
                >
                  <User className="h-4 w-4" />
                  {t('settings.profile')}
                </button>
                
                <button
                  className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${activeTab === 'security' ? 'bg-primary/10 text-primary font-medium' : 'text-muted-foreground hover:bg-muted'}`}
                  onClick={() => setActiveTab('security')}
                >
                  <Shield className="h-4 w-4" />
                  {t('settings.security')}
                </button>
                
                <button
                  className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${activeTab === 'notifications' ? 'bg-primary/10 text-primary font-medium' : 'text-muted-foreground hover:bg-muted'}`}
                  onClick={() => setActiveTab('notifications')}
                >
                  <Bell className="h-4 w-4" />
                  {t('settings.notifications')}
                </button>
                
                <button
                  className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${activeTab === 'preferences' ? 'bg-primary/10 text-primary font-medium' : 'text-muted-foreground hover:bg-muted'}`}
                  onClick={() => setActiveTab('preferences')}
                >
                  <Settings className="h-4 w-4" />
                  {t('settings.preferences')}
                </button>
              </nav>
            </div>
          </div>
          
          <div className="flex-1">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              {activeTab === 'profile' && <ProfileSettings />}
              {activeTab === 'security' && <SecuritySettings />}
              {activeTab === 'notifications' && <NotificationSettings />}
              {activeTab === 'preferences' && <AccountPreferences />}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
