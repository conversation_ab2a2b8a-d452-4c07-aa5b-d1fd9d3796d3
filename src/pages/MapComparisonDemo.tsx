import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GoogleMapLocationSelector } from '@/components/location/GoogleMapLocationSelector';
import { MapLocationSelector } from '@/components/location/MapLocationSelector';
import { GooglePlacesSearch } from '@/components/location/GooglePlacesSearch';
import { useLocation } from '@/contexts/LocationContext';
import { Badge } from '@/components/ui/badge';
import { MapPin, Star, Zap, Globe } from 'lucide-react';

export function MapComparisonDemo() {
  const { selectedLocation, getLocationString } = useLocation();
  const hasApiKey = !!import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-midnight-blue mb-2">
            🗺️ Map Implementation Comparison
          </h1>
          <p className="text-gray-600 mb-4">
            See the difference between Google Maps and OpenStreetMap implementations
          </p>
          
          {hasApiKey ? (
            <Badge className="bg-green-100 text-green-800">
              <Star className="h-3 w-3 mr-1" />
              Google Maps API Enabled
            </Badge>
          ) : (
            <Badge className="bg-yellow-100 text-yellow-800">
              <Globe className="h-3 w-3 mr-1" />
              Using OpenStreetMap Fallback
            </Badge>
          )}
        </div>

        {/* Side by Side Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Google Maps Column */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-green-500" />
                <span>Google Maps (Enhanced)</span>
              </CardTitle>
              <CardDescription>
                Rich location data with Arabic support
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {hasApiKey ? (
                <>
                  {/* Google Places Search */}
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      🔍 Google Places Search
                    </label>
                    <GooglePlacesSearch
                      placeholder="Search for places in Algeria..."
                      onPlaceSelect={(place) => {
                        console.log('Google place selected:', place);
                      }}
                    />
                  </div>

                  {/* Google Map */}
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      🗺️ Interactive Google Map
                    </label>
                    <GoogleMapLocationSelector
                      height="300px"
                      showCurrentLocationButton={true}
                    />
                  </div>

                  {/* Features List */}
                  <div className="mt-4 space-y-2">
                    <h4 className="font-medium text-green-700">✅ Features:</h4>
                    <ul className="text-sm space-y-1 text-gray-600">
                      <li>• Rich Arabic place names</li>
                      <li>• Google Places autocomplete</li>
                      <li>• Street-level accuracy</li>
                      <li>• Landmarks & businesses</li>
                      <li>• Familiar Google Maps UI</li>
                      <li>• Drag & drop markers</li>
                    </ul>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 bg-yellow-50 rounded-lg border border-yellow-200">
                  <Zap className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                  <p className="text-yellow-800 font-medium mb-1">
                    Google Maps API Key Required
                  </p>
                  <p className="text-yellow-600 text-sm">
                    Add VITE_GOOGLE_MAPS_API_KEY to see enhanced features
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* OpenStreetMap Column */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-blue-500" />
                <span>OpenStreetMap (Standard)</span>
              </CardTitle>
              <CardDescription>
                Reliable fallback with basic functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* OpenStreetMap */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  🗺️ Interactive OpenStreetMap
                </label>
                <MapLocationSelector
                  height="300px"
                  showCurrentLocationButton={true}
                />
              </div>

              {/* Features List */}
              <div className="mt-4 space-y-2">
                <h4 className="font-medium text-blue-700">📍 Features:</h4>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Basic map functionality</li>
                  <li>• Click-based selection</li>
                  <li>• No API key required</li>
                  <li>• Always available</li>
                  <li>• Open source maps</li>
                  <li>• Drag & drop markers</li>
                </ul>
              </div>

              {/* Advantages */}
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <h5 className="font-medium text-blue-800 mb-1">💡 Advantages:</h5>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• No external dependencies</li>
                  <li>• Works offline (cached)</li>
                  <li>• Privacy-focused</li>
                  <li>• No usage limits</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Common Features */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-[#fa7b00]" />
              <span>Shared Features (Both Maps)</span>
            </CardTitle>
            <CardDescription>
              Core functionality available regardless of map provider
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl mb-2">🎯</div>
                <h3 className="font-medium text-green-800">100% Accurate</h3>
                <p className="text-sm text-green-600">Wilaya detection system</p>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl mb-2">📱</div>
                <h3 className="font-medium text-blue-800">Mobile Optimized</h3>
                <p className="text-sm text-blue-600">Touch-friendly interface</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl mb-2">🔄</div>
                <h3 className="font-medium text-purple-800">Smart Fallback</h3>
                <p className="text-sm text-purple-600">Always functional</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Selected Location Display */}
        {selectedLocation && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-[#fa7b00]" />
                <span>Selected Location (Both Maps)</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Location Details:</h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Address:</span> {getLocationString()}
                    </div>
                    <div>
                      <span className="font-medium">Wilaya (French):</span> {selectedLocation.wilaya}
                    </div>
                    <div>
                      <span className="font-medium">Wilaya (Arabic):</span> {selectedLocation.wilaya_ar}
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Coordinates:</h4>
                  <div className="space-y-2 text-sm font-mono">
                    <div>
                      <span className="font-medium">Latitude:</span> {selectedLocation.coordinates.lat.toFixed(6)}
                    </div>
                    <div>
                      <span className="font-medium">Longitude:</span> {selectedLocation.coordinates.lng.toFixed(6)}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-green-800">
                    ✅ Location validated with 100% accurate Algeria boundaries
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
