import React, { useState, useEffect } from 'react';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw,
  User,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { isFeatureEnabled, getFeatureFlags, logFeatureState } from '@/config/features';
import { createClient } from '@supabase/supabase-js';

export default function SupabaseTestPage() {
  const { isAuthenticated, user, profile } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [testResults, setTestResults] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  // Initialize Supabase client
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  useEffect(() => {
    checkConnection();
    logFeatureState();
  }, []);

  const checkConnection = async () => {
    setIsLoading(true);
    try {
      // Test basic connection
      const { data, error } = await supabase.from('products').select('count', { count: 'exact', head: true });
      
      if (error) {
        console.error('Supabase connection error:', error);
        setConnectionStatus('error');
        setTestResults(prev => ({ ...prev, connectionError: error.message }));
      } else {
        setConnectionStatus('connected');
        setTestResults(prev => ({ ...prev, connectionSuccess: true, productCount: data }));
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setConnectionStatus('error');
      setTestResults(prev => ({ ...prev, connectionError: error.message }));
    } finally {
      setIsLoading(false);
    }
  };

  const testDatabaseTables = async () => {
    setIsLoading(true);
    try {
      const tables = ['products', 'tyre_specifications', 'vehicle_compatibility', 'pricing_tiers'];
      const results = {};

      for (const table of tables) {
        try {
          const { data, error, count } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          
          results[table] = {
            exists: !error,
            count: count || 0,
            error: error?.message
          };
        } catch (err) {
          results[table] = {
            exists: false,
            error: err.message
          };
        }
      }

      setTestResults(prev => ({ ...prev, tables: results }));
      toast.success('Database tables tested successfully');
    } catch (error) {
      console.error('Table test failed:', error);
      toast.error('Failed to test database tables');
    } finally {
      setIsLoading(false);
    }
  };

  const testStorageBuckets = async () => {
    setIsLoading(true);
    try {
      const { data: buckets, error } = await supabase.storage.listBuckets();
      
      if (error) {
        setTestResults(prev => ({ ...prev, storageError: error.message }));
        toast.error('Failed to test storage buckets');
      } else {
        setTestResults(prev => ({ ...prev, buckets }));
        toast.success('Storage buckets tested successfully');
      }
    } catch (error) {
      console.error('Storage test failed:', error);
      toast.error('Storage test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const featureFlags = getFeatureFlags();

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Supabase Connection Test</h1>
          <p className="text-gray-600 mb-6">
            Test and verify Supabase backend connection, authentication, and database access.
          </p>
        </div>

        {/* Connection Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Connection Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              {connectionStatus === 'checking' && (
                <>
                  <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
                  <span>Checking connection...</span>
                </>
              )}
              {connectionStatus === 'connected' && (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-green-800">Connected to Supabase</span>
                </>
              )}
              {connectionStatus === 'error' && (
                <>
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <span className="text-red-800">Connection failed</span>
                </>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Supabase URL:</strong>
                <br />
                <code className="text-xs bg-gray-100 px-2 py-1 rounded">{supabaseUrl}</code>
              </div>
              <div>
                <strong>Anon Key:</strong>
                <br />
                <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                  {supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'Not set'}
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Authentication Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Authentication Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant={isAuthenticated ? "default" : "secondary"}>
                  {isAuthenticated ? "Authenticated" : "Not Authenticated"}
                </Badge>
              </div>
              
              {user && (
                <div className="text-sm space-y-1">
                  <div><strong>User ID:</strong> {user.id}</div>
                  <div><strong>Email:</strong> {user.email}</div>
                  {profile && <div><strong>Role:</strong> {profile.role}</div>}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Feature Flags */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Feature Flags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Use Supabase Backend:</strong>
                <Badge variant={featureFlags.useSupabaseBackend ? "default" : "secondary"} className="ml-2">
                  {featureFlags.useSupabaseBackend ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div>
                <strong>Enable Image Upload:</strong>
                <Badge variant={featureFlags.enableImageUpload ? "default" : "secondary"} className="ml-2">
                  {featureFlags.enableImageUpload ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div>
                <strong>Enable Real-time Updates:</strong>
                <Badge variant={featureFlags.enableRealTimeUpdates ? "default" : "secondary"} className="ml-2">
                  {featureFlags.enableRealTimeUpdates ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div>
                <strong>Migration Tools:</strong>
                <Badge variant={featureFlags.enableMigrationTools ? "default" : "secondary"} className="ml-2">
                  {featureFlags.enableMigrationTools ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Actions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button 
                onClick={checkConnection}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
                Test Connection
              </Button>
              
              <Button 
                onClick={testDatabaseTables}
                disabled={isLoading}
                variant="outline"
              >
                Test Database Tables
              </Button>
              
              <Button 
                onClick={testStorageBuckets}
                disabled={isLoading}
                variant="outline"
              >
                Test Storage Buckets
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </MarketplaceLayout>
  );
}
