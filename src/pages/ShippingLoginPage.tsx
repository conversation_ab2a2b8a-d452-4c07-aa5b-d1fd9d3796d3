import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import {
  Truck,
  Shield,
  Loader2,
  AlertCircle,
  Eye,
  EyeOff,
  Building2
} from 'lucide-react';
import {
  authenticateShippingCompany,
  getCurrentShippingCompanySession
} from '@/services/shippingCompanyAuthService';

export default function ShippingLoginPage() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [loginCode, setLoginCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showCode, setShowCode] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if already authenticated
  useEffect(() => {
    const session = getCurrentShippingCompanySession();
    if (session) {
      console.log('🚚 Already authenticated, redirecting to dashboard...');
      navigate('/shipping/dashboard');
    }
  }, [navigate]);

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!loginCode.trim()) {
      setError('Please enter your login code');
      return;
    }

    try {
      setIsLoading(true);
      console.log('🚚 [LOGIN] Attempting authentication...');

      const result = await authenticateShippingCompany(loginCode.trim());

      if (result.success && result.company) {
        toast({
          title: 'Login Successful',
          description: `Welcome back, ${result.company.company_name}!`,
        });

        console.log('✅ [LOGIN] Authentication successful, redirecting...');
        navigate('/shipping/dashboard');
      } else {
        setError(result.error || 'Authentication failed');
        toast({
          title: 'Login Failed',
          description: result.error || 'Invalid login code',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('❌ [LOGIN] Authentication error:', error);
      setError('An unexpected error occurred. Please try again.');
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="w-20 h-20 bg-[#fa7b00] rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <Truck className="h-10 w-10 text-white" />
          </motion.div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AROUZ MARKET
          </h1>
          <p className="text-gray-600">
            Shipping Company Portal
          </p>
        </div>

        {/* Login Card */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center pb-4">
            <CardTitle className="flex items-center justify-center gap-2 text-xl">
              <Shield className="h-6 w-6 text-[#fa7b00]" />
              Secure Access
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Enter your company login code to access the shipping portal
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-6">
              {/* Login Code Input */}
              <div className="space-y-2">
                <Label htmlFor="loginCode" className="text-sm font-medium">
                  Company Login Code
                </Label>
                <div className="relative">
                  <Input
                    id="loginCode"
                    type={showCode ? 'text' : 'password'}
                    value={loginCode}
                    onChange={(e) => setLoginCode(e.target.value)}
                    placeholder="Enter your login code"
                    className="pr-10 text-lg tracking-wider focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                    disabled={isLoading}
                    autoComplete="off"
                  />
                  <button
                    type="button"
                    onClick={() => setShowCode(!showCode)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    disabled={isLoading}
                  >
                    {showCode ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-500">
                  Contact AROUZ MARKET support if you don't have a login code
                </p>
              </div>

              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-start space-x-3"
                >
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-red-800 font-medium text-sm">Authentication Failed</p>
                    <p className="text-red-700 text-sm mt-1">{error}</p>
                  </div>
                </motion.div>
              )}

              {/* Login Button */}
              <Button
                type="submit"
                disabled={isLoading || !loginCode.trim()}
                className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white py-3 text-lg font-medium"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Authenticating...
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-5 w-5" />
                    Access Portal
                  </>
                )}
              </Button>
            </form>

            {/* Footer */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                  <Building2 className="h-4 w-4" />
                  <span>Authorized Shipping Partners Only</span>
                </div>
                <p className="text-xs text-gray-500">
                  This portal is exclusively for AROUZ MARKET shipping partners.
                  Unauthorized access is prohibited.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
          className="mt-8 text-center"
        >
          <div className="bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <h3 className="font-semibold text-gray-900 mb-2">Need Help?</h3>
            <p className="text-sm text-gray-600 mb-3">
              Contact AROUZ MARKET support for assistance with:
            </p>
            <ul className="text-xs text-gray-500 space-y-1">
              <li>• Login code issues</li>
              <li>• Account activation</li>
              <li>• Technical support</li>
            </ul>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
