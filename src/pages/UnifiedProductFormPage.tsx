import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { ScanBarcode, ArrowLeft, Loader2 } from 'lucide-react';
import { BarcodeScanner } from '@/features/products/components/BarcodeScanner';
import { toast } from 'sonner';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { useAllProducts } from '@/features/products/hooks/useAllProducts';
import { UnifiedProductFormSelector } from '@/features/products/components/ProductFormSelector';

export default function UnifiedProductFormPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [productFormData, setProductFormData] = useState<Partial<TyreProduct | BrakeProduct> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);

  // Fetch all products to find the one we need to edit
  const { allProducts, isLoading: isLoadingProducts } = useAllProducts();

  // Determine if we're in edit mode
  const isEditMode = !!id && id !== 'new';

  // Find the product to edit from all products
  useEffect(() => {
    if (isEditMode && allProducts.length > 0) {
      const productToEdit = allProducts.find(product => product.id === id);
      if (productToEdit) {
        setProductFormData(productToEdit);
        setIsFormOpen(true);
      } else {
        toast.error(t('products.productNotFound', 'Product not found'));
        navigate('/app/products');
      }
    } else if (!isEditMode) {
      // For new products, just open the form with default values
      setIsFormOpen(true);
    }
  }, [isEditMode, id, allProducts, navigate, t]);

  // Handle barcode scan for new products
  const handleBarcodeScan = (partArticleNumber: string) => {
    setIsScannerOpen(false);

    // Check if a product with this part article number already exists
    const existingProduct = allProducts.find(p => p.partArticleNumber === partArticleNumber);

    if (existingProduct) {
      toast.info(t('products.barcodeExists', 'A product with this part article number already exists'));
      navigate(`/app/products/edit/${existingProduct.id}`);
      return;
    }

    // Set the part article number in the form data
    setProductFormData(prev => ({ ...prev, partArticleNumber }));
    toast.success(t('products.barcodeScanned', 'Part article number scanned successfully'));
  };

  // Handle form submission
  const handleSaveProduct = async (product: TyreProduct | BrakeProduct) => {
    setIsSubmitting(true);

    try {
      // In a real app, this would be an API call
      console.log('Saving product:', product);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));

      // Update localStorage based on product category
      const storageKey = `products-${product.category}`;
      const storedDataStr = localStorage.getItem(storageKey);
      let storedData: (TyreProduct | BrakeProduct)[] = [];

      if (storedDataStr) {
        storedData = JSON.parse(storedDataStr);
      }

      if (isEditMode) {
        // Update existing product
        storedData = storedData.map(p => p.id === product.id ? product : p);
      } else {
        // Add new product
        storedData.push(product);
      }

      // Save back to localStorage
      localStorage.setItem(storageKey, JSON.stringify(storedData));

      // Show success message
      toast.success(
        isEditMode
          ? t('products.productUpdated', 'Product updated successfully!')
          : t('products.productCreated', 'Product created successfully!')
      );

      // Redirect to products page or data grid based on category
      navigate(`/app/products-table/${product.category}`);
    } catch (error) {
      console.error('Error saving product:', error);
      toast.error(t('products.errorSaving', 'Error saving product'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate('/app/products');
  };

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(-1)}
              className="rounded-full"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {isEditMode ? t('products.editProduct') : t('products.addProduct')}
              </h1>
              <p className="text-muted-foreground mt-1">
                {isEditMode
                  ? t('products.editProductDescription', 'Make changes to the product information')
                  : t('products.addProductDescription', 'Fill in the details to create a new product')}
              </p>
            </div>
          </div>

          {!isEditMode && (
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => setIsScannerOpen(true)}
            >
              <ScanBarcode className="h-4 w-4" />
              {t('products.scanBarcode')}
            </Button>
          )}
        </div>

        {(isLoadingProducts || isLoading) && (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-lg">{t('common.loading', 'Loading...')}</span>
          </div>
        )}

        {isFormOpen && (
          <UnifiedProductFormSelector
            isOpen={true}
            onClose={handleCancel}
            onSave={handleSaveProduct}
            initialProduct={productFormData}
            isEditMode={isEditMode}
            categories={[
              { id: 'tyres', name: 'Tyres & Related Products' },
              { id: 'brakes', name: 'Brake Parts & Systems' },
            ]}
          />
        )}
      </div>

      <BarcodeScanner
        isOpen={isScannerOpen}
        onClose={() => setIsScannerOpen(false)}
        onScan={handleBarcodeScan}
      />
    </AdminLayout>
  );
}
