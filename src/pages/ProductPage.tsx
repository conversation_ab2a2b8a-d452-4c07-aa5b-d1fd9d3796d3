import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { FilterProvider } from '@/contexts/FilterContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Heart,
  ShoppingCart,
  Star,
  Plus,
  Minus,
  Truck,
  Shield,
  CheckCircle,
  AlertCircle,
  Package,
  Clock,
  ArrowLeft,
  Share2,
  MessageSquare,
  Info,
  Loader2
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { toggleWishlist, isProductInWishlist } from '@/services/wishlistService';
import { getProductReviewStats } from '@/services/reviewsService';
import { ProductReviews } from '@/components/marketplace/ProductReviews';

import {
  getProductMarketplaceSection,
  getCategoryDisplayName,
  getProductDisplayPrice,
  isValidProductId,
  getProductSpecifications,
  getVehicleCompatibility,
  getProductDescription
} from '@/utils/productUtils';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';
import { toast } from 'sonner';
import { useCart } from '@/contexts/CartContext';
import { calculatePriceForQuantity, getTierForQuantity, formatTierDisplay } from '@/utils/pricingTierUtils';

export default function ProductPage() {
  const { productId } = useParams<{ productId: string }>();
  const { t } = useTranslation();
  const { toast } = useToast();
  const { addItem } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isWishlistLoading, setIsWishlistLoading] = useState(false);
  const [reviewStats, setReviewStats] = useState({ averageRating: 0, totalReviews: 0 });
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const [pendingWishlistAction, setPendingWishlistAction] = useState(false);

  // Use IDENTICAL React Query hooks as marketplace cards
  const { products: tyreProducts, isLoading: tyresLoading, isError: tyresError } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading, isError: brakesError } = useMarketplaceProducts('brakes');
  const { products: filterProducts, isLoading: filtersLoading, isError: filtersError } = useMarketplaceProducts('filters');
  const { products: oilsFluidProducts, isLoading: oilsFluidLoading, isError: oilsFluidError } = useMarketplaceProducts('oils-fluids');
  const { products: engineProducts, isLoading: engineLoading, isError: engineError } = useMarketplaceProducts('engine');
  const { products: windowCleaningProducts, isLoading: windowCleaningLoading, isError: windowCleaningError } = useMarketplaceProducts('window-cleaning');
  const { products: glowPlugIgnitionProducts, isLoading: glowPlugIgnitionLoading, isError: glowPlugIgnitionError } = useMarketplaceProducts('glow-plug-ignition');
  const { products: wishbonesSuspensionProducts, isLoading: wishbonesSuspensionLoading, isError: wishbonesSuspensionError } = useMarketplaceProducts('wishbones-suspension');
  const { products: electricalSystemsProducts, isLoading: electricalSystemsLoading, isError: electricalSystemsError } = useMarketplaceProducts('electrical-systems');

  // Apply COMBINED filtering logic for BOTH retail AND wholesale products

  const marketplaceTyreProducts = tyreProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;


    return passes;
  });

  const marketplaceBrakeProducts = brakeProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;


    return passes;
  });

  const marketplaceFilterProducts = filterProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  const marketplaceOilsFluidProducts = oilsFluidProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  const marketplaceEngineProducts = engineProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  const marketplaceWindowCleaningProducts = windowCleaningProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  const marketplaceGlowPlugIgnitionProducts = glowPlugIgnitionProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  const marketplaceWishbonesSuspensionProducts = wishbonesSuspensionProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  const marketplaceElectricalSystemsProducts = electricalSystemsProducts.filter(product => {
    // Must have EITHER retail price OR wholesale pricing tiers
    const hasRetailPrice = product.retailPrice && product.retailPrice > 0;
    const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
    const hasValidPricing = hasRetailPrice || hasWholesalePricing;

    // Must have visible status (active or out_of_stock only)
    const isVisible = product.status === 'active' || product.status === 'out_of_stock';
    const passes = hasValidPricing && isVisible;

    return passes;
  });

  // Combine all products (BOTH retail AND wholesale) from ALL categories
  const allProducts = [...marketplaceTyreProducts, ...marketplaceBrakeProducts, ...marketplaceFilterProducts, ...marketplaceOilsFluidProducts, ...marketplaceEngineProducts, ...marketplaceWindowCleaningProducts, ...marketplaceGlowPlugIgnitionProducts, ...marketplaceWishbonesSuspensionProducts, ...marketplaceElectricalSystemsProducts];
  const loading = tyresLoading || brakesLoading || filtersLoading || oilsFluidLoading || engineLoading || windowCleaningLoading || glowPlugIgnitionLoading || wishbonesSuspensionLoading || electricalSystemsLoading;
  const hasError = tyresError || brakesError || filtersError || oilsFluidError || engineError || windowCleaningError || glowPlugIgnitionError || wishbonesSuspensionError || electricalSystemsError;

  // Find the specific product (IDENTICAL to marketplace card filtering)
  const product = allProducts.find(p => p.id === productId) || null;
  const error = hasError ? 'Failed to load marketplace data' : (!product && !loading ? 'Product not found' : null);

  // Check consumer authentication and load wishlist/review data
  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        // Check authentication
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        setIsConsumerAuthenticated(!!isAuth);

        if (product) {
          // Load wishlist status if authenticated
          if (isAuth) {
            const result = await isProductInWishlist(product.id);
            if (result.success) {
              setIsWishlisted(result.isInWishlist || false);
            }
          }

          // Load review statistics
          const reviewResult = await getProductReviewStats(product.id);
          if (reviewResult.success && reviewResult.stats) {
            setReviewStats({
              averageRating: reviewResult.stats.average_rating,
              totalReviews: reviewResult.stats.total_reviews
            });
          }
        }
      } catch (error) {
        console.error('Error loading product data:', error);
      }
    };

    checkAuthAndLoadData();
  }, [product]);

  // Listen for consumer authentication success to retry wishlist action
  useEffect(() => {
    const handleConsumerAuthSuccess = async () => {
      if (pendingWishlistAction && isConsumerAuthenticated) {
        setPendingWishlistAction(false);
        // Retry the wishlist action after successful authentication
        await performWishlistToggle();
      }
    };

    window.addEventListener('consumer-auth-success', handleConsumerAuthSuccess);

    return () => {
      window.removeEventListener('consumer-auth-success', handleConsumerAuthSuccess);
    };
  }, [pendingWishlistAction, isConsumerAuthenticated]);

  // Set initial quantity when product is found (IDENTICAL to marketplace logic)
  useEffect(() => {
    if (product) {
      // Calculate effective minimum order quantity
      const marketplaceSection = getProductMarketplaceSection(product);
      const effectiveMinQuantity = product.minimumOrderQuantity ||
        (marketplaceSection === 'wholesale' && product.wholesalePricingTiers?.[0]?.minQuantity) ||
        1;

      setQuantity(effectiveMinQuantity);
    }
  }, [product]);



  // If loading, show enhanced loading state
  if (loading) {
    return (
      <FilterProvider>
        <MarketplaceLayout>
        <div className="container py-8">
          {/* Loading skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Image skeleton */}
            <div className="space-y-4">
              <Skeleton className="aspect-square w-full rounded-lg" />
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <Skeleton key={i} className="w-16 h-16 rounded-md" />
                ))}
              </div>
            </div>

            {/* Product info skeleton */}
            <div className="space-y-6">
              <div className="space-y-2">
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-6 w-1/2" />
              </div>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-8 w-24" />
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>

          <div className="text-center">
            <Loader2 className="h-8 w-8 mx-auto mb-4 text-[#fa7b00] animate-spin" />
            <p className="text-lg text-gray-600">Loading product details...</p>
          </div>
        </div>
      </MarketplaceLayout>
      </FilterProvider>
    );
  }

  // If error or product not found, show enhanced error state with suggestions
  if (error || !product) {
    // Get suggested products synchronously for immediate display
    const getSuggestedProducts = () => {
      try {
        // Use a simpler approach for suggested products
        const sampleProducts = [
          { id: 'TYR-100000-RETAIL', name: 'Michelin Pilot Sport 4 195/65R15', manufacturer: 'Michelin' },
          { id: 'TYR-100001-WHOLESALE', name: 'Bridgestone Potenza RE980AS 205/55R16', manufacturer: 'Bridgestone' },
          { id: 'BRK-100000-RETAIL', name: 'Brembo Brake Discs Front', manufacturer: 'Brembo' },
          { id: 'BRK-100001-WHOLESALE', name: 'ATE Brake Pads Rear', manufacturer: 'ATE' },
          { id: 'FLT-100000-RETAIL', name: 'Mann Oil Filter W 712/75', manufacturer: 'Mann' },
          { id: 'PROD-455146-WHOLESALE', name: 'Universal Air Filter', manufacturer: 'Generic' },
          { id: 'TYR-100002-RETAIL', name: 'Continental ExtremeContact DWS06 225/45R17', manufacturer: 'Continental' },
          { id: 'BRK-100002-RETAIL', name: 'TRW Brake Caliper Front', manufacturer: 'TRW' }
        ];

        if (productId?.startsWith('TYR-')) {
          return sampleProducts.filter(p => p.id.startsWith('TYR-'));
        } else if (productId?.startsWith('BRK-')) {
          return sampleProducts.filter(p => p.id.startsWith('BRK-'));
        } else if (productId?.startsWith('FLT-')) {
          return sampleProducts.filter(p => p.id.startsWith('FLT-'));
        } else if (productId?.startsWith('PROD-')) {
          return sampleProducts.filter(p => p.id.startsWith('PROD-'));
        }

        return sampleProducts;
      } catch (error) {
        console.error('Error getting suggested products:', error);
        return [];
      }
    };

    const suggestedProducts = getSuggestedProducts();

    return (
      <FilterProvider>
        <MarketplaceLayout>
        <div className="container py-8">
          <div className="max-w-4xl mx-auto">
            {/* Error Header */}
            <div className="text-center mb-8">
              <AlertCircle className="h-16 w-16 mx-auto mb-6 text-red-400" />
              <h1 className="text-3xl font-bold mb-4 text-gray-900">Product Not Found</h1>
              <p className="text-gray-600 mb-6 leading-relaxed max-w-2xl mx-auto">
                {error || `The product with ID "${productId}" could not be found. It may have been removed, the link may be incorrect, or this product ID doesn't exist in our system.`}
              </p>
            </div>

            {/* Debug Information */}
            {productId && (
              <div className="mb-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-sm font-semibold text-blue-900 mb-2">Debug Information</h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <p><strong>Requested Product ID:</strong> {productId}</p>
                  <p><strong>ID Format Valid:</strong> {isValidProductId(productId) ? '✅ Yes' : '❌ No'}</p>
                  <p><strong>Expected Format:</strong> TYR-XXXXXX-WHOLESALE, BRK-XXXXXX-RETAIL, FLT-XXXXXX-WHOLESALE, or PROD-XXXXXX-WHOLESALE</p>
                  <p><strong>Available ID Range:</strong> 100000-999999 (for testing)</p>
                </div>
              </div>
            )}

            {/* Suggested Products */}
            {suggestedProducts.length > 0 && (
              <div className="mb-8">
                <h2 className="text-xl font-bold mb-4 text-gray-900">Similar Products You Might Like</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {suggestedProducts.map((product) => (
                    <Card key={product.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden">
                          <img
                            src="/placeholder.svg"
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <h3 className="font-semibold text-sm mb-2 line-clamp-2">{product.name}</h3>
                        <p className="text-xs text-gray-600 mb-2">{product.manufacturer}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs">
                            {product.id}
                          </Badge>
                          <Button
                            size="sm"
                            onClick={() => window.location.href = `/${product.id}`}
                            className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-xs"
                          >
                            View
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="text-center">
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={() => window.history.back()}
                  variant="outline"
                  className="flex items-center"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Go Back
                </Button>
                <Button
                  onClick={() => window.location.href = '/'}
                  className="bg-[#fa7b00] hover:bg-[#fa7b00]/90"
                >
                  Browse All Products
                </Button>
                <Button
                  onClick={() => window.location.href = '/product-debug'}
                  variant="outline"
                >
                  View Available Products
                </Button>
              </div>
            </div>
          </div>
        </div>
      </MarketplaceLayout>
      </FilterProvider>
    );
  }

  // Get product details with enhanced debugging
  const marketplaceSection = getProductMarketplaceSection(product);
  const categoryDisplayName = getCategoryDisplayName(product.category);
  const specifications = getProductSpecifications(product);
  const vehicleCompatibility = getVehicleCompatibility(product);
  const productDescription = getProductDescription(product);

  // Calculate accurate price based on current quantity for wholesale products
  const getCurrentPrice = (): number => {
    if (marketplaceSection === 'wholesale' && product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0) {
      return calculatePriceForQuantity(quantity, product.wholesalePricingTiers);
    }
    return getProductDisplayPrice(product, marketplaceSection);
  };

  const currentPrice = getCurrentPrice();
  const currentTier = marketplaceSection === 'wholesale' && product.wholesalePricingTiers
    ? getTierForQuantity(quantity, product.wholesalePricingTiers)
    : null;

  // Calculate effective minimum order quantity - derive from wholesale tiers if undefined
  const effectiveMinimumOrderQuantity = product.minimumOrderQuantity ||
    (marketplaceSection === 'wholesale' && product.wholesalePricingTiers?.[0]?.minQuantity) ||
    1;





  // Get product images
  const productImages = [
    product.primaryImage || '/placeholder.svg',
    ...(product.additionalImages || [])
  ].filter(Boolean);

  // Handle quantity changes
  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    const minQuantity = effectiveMinimumOrderQuantity;
    const maxQuantity = product.stockQuantity;

    if (newQuantity >= minQuantity && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    } else if (newQuantity < minQuantity) {
      toast.error(`Minimum order quantity is ${minQuantity}`);
    } else if (newQuantity > maxQuantity) {
      toast.error(`Only ${maxQuantity} units available in stock`);
    }
  };

  // Handle direct quantity input
  const handleQuantityInput = (value: string) => {
    const newQuantity = parseInt(value) || 1;
    const minQuantity = effectiveMinimumOrderQuantity;
    const maxQuantity = product.stockQuantity;

    if (newQuantity >= minQuantity && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    }
  };

  // Helper function to convert product ID string to number for cart
  const getCartItemId = (productId: string): number => {
    // Simple hash function to convert string ID to number
    let hash = 0;
    for (let i = 0; i < productId.length; i++) {
      const char = productId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  };

  // Handle add to cart with loading state (UNIFIED for both retail and wholesale)
  const handleAddToCart = async () => {
    if (isAddingToCart) return;

    setIsAddingToCart(true);
    try {
      // Log product data before adding to cart
      console.log('🛒 [PRODUCT_PAGE] Adding product to cart:', {
        id: product.id,
        name: product.name,
        supplierName: product.supplierName,
        supplierAccountId: product.supplierAccountId,
        shippingOrigin: (product as any).shippingOrigin,
        rawProduct: product
      });

      // Add to cart using accurate current price with complete metadata
      const cartItem = {
        id: getCartItemId(product.id),
        name: product.name,
        price: currentPrice,
        quantity: quantity,
        image: product.primaryImage || '/placeholder.svg',
        // Complete product metadata for proper persistence
        category: product.category,
        subcategory: product.subcategory,
        manufacturer: product.manufacturer,
        brand: (product as any).brand, // Some products might have brand field
        partArticleNumber: product.partArticleNumber,
        supplierName: product.supplierName,
        supplierAccountId: product.supplierAccountId, // CRITICAL: Supplier account ID for shipping companies
        originalProductId: product.id,
        // Shipping & Location Information
        shippingOrigin: (product as any).shippingOrigin || 'Location TBD', // CRITICAL: Supplier location for shipping origins
        // Wholesale pricing support
        marketplaceSection: marketplaceSection,
        wholesalePricingTiers: product.wholesalePricingTiers,
        retailPrice: product.retailPrice
      };

      console.log('🛒 [PRODUCT_PAGE] Final cart item being added:', cartItem);
      addItem(cartItem);

      // Show success message with appropriate context
      const sectionText = marketplaceSection === 'wholesale' ? 'wholesale' : 'retail';
      const tierInfo = currentTier ? ` (${currentPrice.toFixed(2)} DZD per piece)` : '';
      toast.success(`Added ${quantity} ${product.name} to cart`, {
        description: `${sectionText}${tierInfo} • Total: ${(currentPrice * quantity).toFixed(2)} DZD`
      });
    } catch (error) {
      toast.error('Failed to add to cart. Please try again.');
    } finally {
      setIsAddingToCart(false);
    }
  };

  // Perform wishlist toggle action (extracted for reuse after authentication)
  const performWishlistToggle = async () => {
    setIsWishlistLoading(true);

    try {
      const result = await toggleWishlist({
        product_id: product.id,
        product_name: product.name,
        product_image: product.primaryImage,
        product_price: getCurrentPrice(),
        product_manufacturer: product.manufacturer || (product as any).brand
      });

      if (result.success) {
        const newWishlistState = result.action === 'added';
        setIsWishlisted(newWishlistState);

        toast.success(
          newWishlistState ? t('marketplace.addedToWishlist') : t('marketplace.removedFromWishlist'),
          {
            description: newWishlistState
              ? t('marketplace.addedToWishlistDescription')
              : t('marketplace.removedFromWishlistDescription')
          }
        );

        // Dispatch event to update wishlist count in header
        window.dispatchEvent(new CustomEvent('wishlist:updated'));
      } else {
        toast.error(t('marketplace.wishlistError'), {
          description: result.error || t('marketplace.wishlistErrorDescription')
        });
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      toast.error(t('marketplace.wishlistError'), {
        description: t('marketplace.wishlistErrorDescription')
      });
    } finally {
      setIsWishlistLoading(false);
    }
  };

  // Handle wishlist toggle with authentication check
  const handleWishlistToggle = async () => {
    // Check if user is authenticated
    if (!isConsumerAuthenticated) {
      // Set pending action and trigger auth modal for seamless experience
      setPendingWishlistAction(true);
      const authModal = document.getElementById('auth-modal-trigger');
      if (authModal) {
        authModal.click();
      }
      return;
    }

    // User is authenticated, perform the action immediately
    await performWishlistToggle();
  };

  // Get availability status
  const getAvailabilityStatus = () => {
    if (product.stockQuantity > 10) {
      return { status: 'In Stock', color: 'text-green-600', icon: CheckCircle };
    } else if (product.stockQuantity > 0) {
      return { status: 'Low Stock', color: 'text-yellow-600', icon: AlertCircle };
    } else {
      return { status: 'Out of Stock', color: 'text-red-600', icon: AlertCircle };
    }
  };

  const availability = getAvailabilityStatus();
  const AvailabilityIcon = availability.icon;



  return (
    <FilterProvider>
      <MarketplaceLayout>
      <div className="container py-8">


        {/* Breadcrumb Navigation */}
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <button
            onClick={() => window.history.back()}
            className="flex items-center hover:text-[#fa7b00] transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </button>
          <span>/</span>
          <span className="capitalize">{categoryDisplayName}</span>
          <span>/</span>
          <span className="text-gray-900 font-medium truncate">{product.name}</span>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
              <img
                src={productImages[selectedImageIndex]}
                alt={product.name}
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />

              {/* Image navigation arrows */}
              {productImages.length > 1 && (
                <>
                  <button
                    onClick={() => setSelectedImageIndex(
                      selectedImageIndex === 0 ? productImages.length - 1 : selectedImageIndex - 1
                    )}
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all opacity-0 group-hover:opacity-100"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setSelectedImageIndex(
                      selectedImageIndex === productImages.length - 1 ? 0 : selectedImageIndex + 1
                    )}
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all opacity-0 group-hover:opacity-100"
                  >
                    <ArrowLeft className="h-4 w-4 rotate-180" />
                  </button>
                </>
              )}

              {/* Image counter */}
              {productImages.length > 1 && (
                <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  {selectedImageIndex + 1} / {productImages.length}
                </div>
              )}
            </div>

            {/* Thumbnail images */}
            {productImages.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {productImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 transition-all ${
                      selectedImageIndex === index
                        ? 'border-[#fa7b00] ring-2 ring-[#fa7b00]/20'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder.svg';
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Overview */}
          <div className="space-y-6">
            {/* Category and Brand */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-sm bg-[#ffd9a3] text-[#071c44]">
                  {categoryDisplayName}
                </Badge>
                {marketplaceSection === 'wholesale' && (
                  <Badge variant="outline" className="text-xs border-[#fa7b00] text-[#fa7b00]">
                    Wholesale
                  </Badge>
                )}
                {marketplaceSection === 'retail' && (
                  <Badge variant="outline" className="text-xs border-blue-500 text-blue-500">
                    Retail
                  </Badge>
                )}
              </div>
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">{product.name}</h1>
              <div className="flex items-center space-x-4">
                <p className="text-lg text-gray-600">by <span className="font-medium text-gray-900">{product.manufacturer}</span></p>
                {product.partArticleNumber && (
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <Package className="h-4 w-4" />
                    <span>Part #{product.partArticleNumber}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Rating and Reviews */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-5 w-5 ${
                      star <= Math.round(reviewStats.averageRating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'fill-gray-200 text-gray-200'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">
                {reviewStats.totalReviews > 0
                  ? `${reviewStats.averageRating.toFixed(1)} out of 5`
                  : 'No reviews yet'
                }
              </span>
              {reviewStats.totalReviews > 0 && (
                <>
                  <span className="text-sm text-gray-400">•</span>
                  <button
                    className="text-sm text-[#fa7b00] hover:underline"
                    onClick={() => {
                      const reviewsSection = document.getElementById('reviews-section');
                      if (reviewsSection) {
                        reviewsSection.scrollIntoView({ behavior: 'smooth' });
                      }
                    }}
                  >
                    {reviewStats.totalReviews} review{reviewStats.totalReviews !== 1 ? 's' : ''}
                  </button>
                </>
              )}
            </div>

            {/* Enhanced Pricing Display */}
            <div className="space-y-3">
              {marketplaceSection === 'wholesale' && product.wholesalePricingTiers ? (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900">Wholesale Pricing Tiers</h3>
                  <div className="grid gap-2">
                    {product.wholesalePricingTiers
                      .sort((a, b) => a.minQuantity - b.minQuantity)
                      .map((tier, index) => {
                        const isCurrentTier = currentTier && tier.minQuantity === currentTier.minQuantity;
                        return (
                          <div
                            key={index}
                            className={`flex justify-between items-center p-3 rounded-lg border transition-all ${
                              isCurrentTier
                                ? 'bg-[#fa7b00]/10 border-[#fa7b00] ring-2 ring-[#fa7b00]/20'
                                : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                            }`}
                          >
                            <span className={`text-sm font-medium ${isCurrentTier ? 'text-[#fa7b00]' : 'text-gray-700'}`}>
                              {formatTierDisplay(tier)}
                            </span>
                            <div className="text-right">
                              {isCurrentTier && (
                                <div className="text-xs text-[#fa7b00] font-medium mb-1">
                                  ← Current selection
                                </div>
                              )}
                              <span className={`text-lg font-bold ${isCurrentTier ? 'text-[#fa7b00]' : 'text-gray-900'}`}>
                                {tier.price.toFixed(2)} DZD per piece
                              </span>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                  <p className="text-sm text-gray-600">
                    <Info className="h-4 w-4 inline mr-1" />
                    Minimum order: {effectiveMinimumOrderQuantity} units
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="text-4xl font-bold text-[#fa7b00]">
                    {currentPrice.toFixed(2)} DZD per piece
                  </div>
                  <p className="text-sm text-gray-600">
                    {marketplaceSection === 'wholesale' && currentTier
                      ? `Current tier: ${formatTierDisplay(currentTier)}`
                      : 'Price per unit • Retail pricing'
                    }
                  </p>
                </div>
              )}
            </div>

            {/* Availability Status */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <AvailabilityIcon className={`h-5 w-5 ${availability.color}`} />
                <span className={`font-medium ${availability.color}`}>
                  {availability.status}
                </span>
              </div>
              {product.stockQuantity > 0 && (
                <span className="text-sm text-gray-600">
                  {product.stockQuantity} units available
                </span>
              )}
            </div>

            {/* Quantity Selector and Actions */}
            {product.stockQuantity > 0 ? (
              <div className="space-y-4">
                {/* Quantity Selector */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Quantity {effectiveMinimumOrderQuantity > 1 && (
                      <span className="text-gray-500">(Min: {effectiveMinimumOrderQuantity})</span>
                    )}
                  </label>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center border-2 border-gray-200 rounded-lg overflow-hidden">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleQuantityChange(-1)}
                        disabled={quantity <= effectiveMinimumOrderQuantity}
                        className="h-10 w-10 p-0 hover:bg-gray-100"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <input
                        type="number"
                        value={quantity}
                        onChange={(e) => handleQuantityInput(e.target.value)}
                        className="w-16 h-10 text-center border-0 focus:ring-0 focus:outline-none"
                        min={effectiveMinimumOrderQuantity}
                        max={product.stockQuantity}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleQuantityChange(1)}
                        disabled={quantity >= product.stockQuantity}
                        className="h-10 w-10 p-0 hover:bg-gray-100"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Total Price Display */}
                    <div className="text-lg font-semibold text-gray-900">
                      Total: {(currentPrice * quantity).toFixed(2)} DZD
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <div className="flex space-x-3">
                    {/* UNIFIED Add to Cart button for both retail and wholesale */}
                    <Button
                      onClick={handleAddToCart}
                      disabled={isAddingToCart}
                      className="flex-1 bg-[#fa7b00] hover:bg-[#fa7b00]/90 h-12 text-base font-medium"
                    >
                      {isAddingToCart ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Adding to Cart...
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="h-5 w-5 mr-2" />
                          Add to Cart
                          {marketplaceSection === 'wholesale' && (
                            <span className="ml-1 text-sm opacity-90">
                              ({quantity} units)
                            </span>
                          )}
                        </>
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      onClick={handleWishlistToggle}
                      disabled={isWishlistLoading}
                      className={`h-12 w-12 p-0 ${
                        isWishlisted ? 'text-red-500 border-red-500 bg-red-50' : 'hover:bg-gray-50'
                      } ${isWishlistLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <Heart className={`h-5 w-5 ${
                        isWishlisted ? 'fill-current' : ''
                      } ${isWishlistLoading ? 'animate-pulse' : ''}`} />
                    </Button>
                  </div>

                  {/* Secondary Actions */}
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      {marketplaceSection === 'wholesale' ? 'Request Quote' : 'Ask Question'}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-2 text-red-700">
                  <AlertCircle className="h-5 w-5" />
                  <span className="font-medium">Out of Stock</span>
                </div>
                <p className="text-sm text-red-600 mt-1">
                  This product is currently unavailable. Contact us for restock information.
                </p>
                <Button variant="outline" size="sm" className="mt-3 border-red-300 text-red-700 hover:bg-red-50">
                  Notify When Available
                </Button>
              </div>
            )}

            {/* Additional Product Information */}
            <div className="space-y-3 pt-4 border-t border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">
                Product Information
              </h3>

              {/* SKU */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">SKU:</span>
                <span className="font-medium text-gray-900">{product.sku}</span>
              </div>

              {/* Shipping Info */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 flex items-center">
                  <Truck className="h-4 w-4 mr-1" />
                  Ships from:
                </span>
                <span className="font-medium text-gray-900">
                  {product.shippingOrigin || 'Multiple locations'}
                </span>
              </div>

              {/* Lead Time */}
              {product.estimatedLeadTime && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    Delivery time:
                  </span>
                  <span className="font-medium text-gray-900">{product.estimatedLeadTime}</span>
                </div>
              )}

              {/* Warranty Info */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 flex items-center">
                  <Shield className="h-4 w-4 mr-1" />
                  Warranty:
                </span>
                <span className="font-medium text-gray-900">2-year manufacturer warranty</span>
              </div>

              {/* Supplier Information */}
              {product.supplierName && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Supplier:</span>
                  <span className="font-medium text-gray-900">{product.supplierName}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Product Specifications */}
        <Card className="mb-8 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <Package className="h-6 w-6 mr-2 text-[#fa7b00]" />
              Product Specifications
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-hidden">
              {Object.entries(specifications).map(([key, value], index) => (
                <div
                  key={key}
                  className={`flex justify-between items-center py-4 px-6 border-b border-gray-100 last:border-b-0 ${
                    index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'
                  } hover:bg-[#ffd9a3]/20 transition-colors`}
                >
                  <span className="font-medium text-gray-700 text-sm uppercase tracking-wide">
                    {key}
                  </span>
                  <span className="text-gray-900 font-semibold text-right">
                    {value}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Product Description */}
        <Card className="mb-8 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <Info className="h-6 w-6 mr-2 text-[#fa7b00]" />
              Product Description
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed text-base">
                  {productDescription}
                </p>
              </div>

              {/* Key Features */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Key Features</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">High-quality construction for durability and performance</span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">Meets or exceeds OEM specifications</span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">Easy installation with included hardware</span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">Backed by manufacturer warranty</span>
                  </div>
                  {product.certifications && product.certifications.length > 0 && (
                    <div className="flex items-start space-x-3 md:col-span-2">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">
                        <strong>Certified:</strong> {product.certifications.join(', ')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shipping & Packaging Details */}
        {(product.availableShippingMethods || product.packagingDetails) && (
          <Card className="mb-8 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <Truck className="h-6 w-6 mr-2 text-[#fa7b00]" />
                Shipping & Packaging
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {product.availableShippingMethods && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Available Shipping Methods</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {product.availableShippingMethods.map((method, index) => (
                        <div key={index} className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-100">
                          <Truck className="h-5 w-5 text-blue-600 flex-shrink-0" />
                          <span className="text-gray-800 font-medium">{method}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {product.packagingDetails && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">Packaging Details</h3>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <p className="text-gray-700">{product.packagingDetails}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Certifications */}
        {product.certifications && product.certifications.length > 0 && (
          <Card className="mb-8 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <Shield className="h-6 w-6 mr-2 text-[#fa7b00]" />
                Certifications & Standards
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {product.certifications.map((cert, index) => (
                  <div key={index} className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-100">
                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                    <span className="text-gray-800 font-medium">{cert}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Vehicle Compatibility */}
        <Card className="mb-8 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <CheckCircle className="h-6 w-6 mr-2 text-[#fa7b00]" />
              Vehicle Compatibility
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              <p className="text-gray-600 text-base">
                This product is compatible with the following vehicles. Always verify compatibility with your specific vehicle before purchasing.
              </p>

              {vehicleCompatibility.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {vehicleCompatibility.map((vehicle, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-100"
                    >
                      <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                      <span className="text-gray-800 font-medium">{vehicle}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6 bg-gray-50 rounded-lg text-center">
                  <Package className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p className="text-gray-600">
                    Universal compatibility - suitable for most vehicles in this category.
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Contact us for specific vehicle compatibility verification.
                  </p>
                </div>
              )}

              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm text-blue-800 font-medium">
                      Important Compatibility Notice
                    </p>
                    <p className="text-sm text-blue-700 mt-1">
                      Always verify compatibility with your specific vehicle model, year, and engine type before purchasing.
                      If you're unsure about compatibility, please contact our support team for assistance.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Reviews */}
        <Card id="reviews-section" className="mb-8 shadow-sm">
          <CardContent className="p-6">
            <ProductReviews
              productId={product.id}
              productName={product.name}
            />
          </CardContent>
        </Card>
      </div>
    </MarketplaceLayout>
    </FilterProvider>
  );
}
