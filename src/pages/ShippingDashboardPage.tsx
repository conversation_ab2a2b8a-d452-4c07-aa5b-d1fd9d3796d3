import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Truck,
  Package,
  Clock,
  CheckCircle,
  MapPin,
  Phone,
  Search,
  Filter,
  Eye,
  LogOut,
  RefreshCw,
  Building2,
  DollarSign,
  TrendingUp,
  Users
} from 'lucide-react';
import {
  getCurrentShippingCompanySession,
  logoutShippingCompany
} from '@/services/shippingCompanyAuthService';
import {
  getShippingCompanyOrders,
  getShippingCompanyStats,
  ShippingOrderWithDetails,
  ShippingCompanyStats
} from '@/services/shippingCompanyOrderService';
import { StatsCard } from '@/components/dashboard/StatsCard';

export default function ShippingDashboardPage() {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Authentication state
  const [companySession, setCompanySession] = useState(getCurrentShippingCompanySession());

  // Data state
  const [orders, setOrders] = useState<ShippingOrderWithDetails[]>([]);
  const [stats, setStats] = useState<ShippingCompanyStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [shipmentStatusFilter, setShipmentStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const ITEMS_PER_PAGE = 10;

  // Check authentication
  useEffect(() => {
    if (!companySession) {
      console.log('🚚 No valid session, redirecting to login...');
      navigate('/shipping/login');
      return;
    }
    console.log('🚚 Authenticated as:', companySession.company_name);
  }, [companySession, navigate]);

  // Load data
  const loadData = async (showRefreshIndicator = false) => {
    if (!companySession) return;

    try {
      if (showRefreshIndicator) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      console.log('📊 [SHIPPING_DASHBOARD] Loading orders and stats...');

      // Load statistics
      const statsResult = await getShippingCompanyStats();
      if (statsResult.success) {
        setStats(statsResult.stats || null);
      }

      // Load orders with filters
      const filters = {
        status: statusFilter === 'all' ? undefined : statusFilter,
        shipment_status: shipmentStatusFilter === 'all' ? undefined : shipmentStatusFilter,
        search: searchTerm || undefined,
        limit: ITEMS_PER_PAGE,
        offset: (currentPage - 1) * ITEMS_PER_PAGE
      };

      const ordersResult = await getShippingCompanyOrders(filters);
      if (ordersResult.success) {
        setOrders(ordersResult.orders || []);
        setTotalCount(ordersResult.total_count || 0);
      } else {
        toast({
          title: 'Failed to load orders',
          description: ordersResult.error || 'Please try again',
          variant: 'destructive'
        });
      }

    } catch (error) {
      console.error('❌ [SHIPPING_DASHBOARD] Error loading data:', error);
      toast({
        title: 'Failed to load data',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (companySession) {
      loadData();
    }
  }, [companySession, currentPage, statusFilter, shipmentStatusFilter]);

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        loadData();
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Handle logout
  const handleLogout = async () => {
    try {
      await logoutShippingCompany();
    } catch (error) {
      console.error('Error during logout:', error);
      navigate('/shipping/login');
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'unassigned': return 'secondary';
      case 'assigned': return 'default';
      case 'picked_up': return 'outline';
      case 'in_transit': return 'default';
      case 'delivered': return 'default';
      default: return 'secondary';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'unassigned': return Clock;
      case 'assigned': return Package;
      case 'picked_up': return Truck;
      case 'in_transit': return Truck;
      case 'delivered': return CheckCircle;
      default: return Clock;
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  if (!companySession) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-[#fa7b00] rounded-lg flex items-center justify-center">
                <Truck className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {companySession.company_name}
                </h1>
                <p className="text-gray-600">Shipping Portal</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={() => loadData(true)}
                disabled={isRefreshing}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Statistics Cards */}
        {isLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
        ) : stats ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Total Orders"
              value={stats.total_assigned_orders}
              icon={<Package className="h-6 w-6" />}
              description="Assigned to you"
            />
            <StatsCard
              title="Pending Pickup"
              value={stats.pending_pickup}
              icon={<Clock className="h-6 w-6" />}
              description="Ready for pickup"
            />
            <StatsCard
              title="In Transit"
              value={stats.in_transit}
              icon={<Truck className="h-6 w-6" />}
              description="On the way"
            />
            <StatsCard
              title="Delivered Today"
              value={stats.delivered_today}
              icon={<CheckCircle className="h-6 w-6" />}
              description="Completed deliveries"
            />
          </div>
        ) : null}

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by order number, customer name, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full md:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Order status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Orders</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-48">
                <Select value={shipmentStatusFilter} onValueChange={setShipmentStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Shipment status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Shipments</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="picked_up">Picked Up</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Assigned Orders ({totalCount})
              </span>
              {totalPages > 1 && (
                <span className="text-sm text-gray-500">
                  Page {currentPage} of {totalPages}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-24" />
                ))}
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' || shipmentStatusFilter !== 'all'
                    ? 'Try adjusting your filters or search terms'
                    : 'Assigned orders will appear here'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {orders.map((order) => {
                  const StatusIcon = getStatusIcon(order.shipment_status || 'unassigned');
                  
                  return (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <StatusIcon className="h-6 w-6 text-[#fa7b00]" />
                          <div>
                            <h3 className="font-semibold text-lg">{order.order_number}</h3>
                            <p className="text-sm text-gray-600">
                              {formatDate(order.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge variant={getStatusBadgeVariant(order.shipment_status || 'unassigned')}>
                            {(order.shipment_status || 'unassigned').replace('_', ' ').toUpperCase()}
                          </Badge>
                          <Button
                            onClick={() => navigate(`/shipping/orders/${order.id}`)}
                            size="sm"
                            variant="outline"
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {order.consumer_name || 'Consumer'} - {order.consumer_phone}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{order.delivery_wilaya}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {order.supplier_contacts.length} supplier{order.supplier_contacts.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-600">
                            Items: {order.order_items.length}
                          </span>
                          {order.tracking_number && (
                            <span className="text-sm text-gray-600">
                              Tracking: {order.tracking_number}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          Total: {formatCurrency(order.total_amount)}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-6">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  variant="outline"
                  size="sm"
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
