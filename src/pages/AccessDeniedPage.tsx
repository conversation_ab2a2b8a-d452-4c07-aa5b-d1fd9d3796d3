import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Shield, AlertTriangle, ArrowLeft, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function AccessDeniedPage() {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();
  
  // Get the reason for access denial from location state
  const { from, reason, requiredRoles, currentRole } = location.state || {
    from: '/app/dashboard',
    reason: 'unknown',
    requiredRoles: [],
    currentRole: 'unknown'
  };
  
  // Get appropriate messages based on the reason
  const getMessages = () => {
    switch (reason) {
      case 'insufficient_permissions':
        const roleText = requiredRoles && requiredRoles.length > 0
          ? `This page requires ${requiredRoles.join(' or ')} access. Your current role is: ${currentRole}.`
          : 'You do not have the required permissions to access this page.';

        return {
          title: t('access.insufficientPermissionsTitle', 'Insufficient Permissions'),
          description: t('access.insufficientPermissionsDescription', roleText + ' Please contact your administrator if you believe this is an error.'),
          icon: <Shield className="h-12 w-12 text-red-500" />,
        };
      case 'ip_restriction':
        return {
          title: t('access.ipRestrictionTitle', 'IP Address Restricted'),
          description: t('access.ipRestrictionDescription', 'Your current IP address is not authorized to access this resource. Please contact your administrator for assistance.'),
          icon: <AlertTriangle className="h-12 w-12 text-amber-500" />,
        };
      case 'session_expired':
        return {
          title: t('access.sessionExpiredTitle', 'Session Expired'),
          description: t('access.sessionExpiredDescription', 'Your session has expired. Please log in again to continue.'),
          icon: <LogOut className="h-12 w-12 text-blue-500" />,
        };
      default:
        return {
          title: t('access.accessDeniedTitle', 'Access Denied'),
          description: t('access.accessDeniedDescription', 'You do not have permission to access this resource. Please contact your administrator if you believe this is an error.'),
          icon: <Shield className="h-12 w-12 text-red-500" />,
        };
    }
  };
  
  const { title, description, icon } = getMessages();
  
  // Handle going back to previous page
  const handleGoBack = () => {
    navigate(-1);
  };
  
  // Handle going to dashboard
  const handleGoToDashboard = () => {
    navigate('/app/dashboard');
  };
  
  // Handle logout
  const handleLogout = () => {
    logout('/partners');
  };
  
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6 sm:p-8">
          <div className="flex flex-col items-center text-center">
            {icon}
            <h1 className="mt-4 text-2xl font-bold text-gray-900">{title}</h1>
            <p className="mt-2 text-sm text-gray-600">{description}</p>
            
            <div className="mt-6 flex flex-col sm:flex-row gap-3 w-full">
              <Button 
                variant="outline" 
                className="flex-1 gap-2" 
                onClick={handleGoBack}
              >
                <ArrowLeft className="h-4 w-4" />
                {t('access.goBack', 'Go Back')}
              </Button>
              
              <Button 
                variant="default" 
                className="flex-1 bg-electric-orange hover:bg-electric-orange/90"
                onClick={handleGoToDashboard}
              >
                {t('access.goToDashboard', 'Go to Dashboard')}
              </Button>
            </div>
            
            <Button 
              variant="ghost" 
              className="mt-4 text-gray-600 hover:text-gray-900"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4 mr-2" />
              {t('access.logout', 'Logout')}
            </Button>
            
            <p className="mt-6 text-xs text-gray-500">
              {t('access.contactSupport', 'If you continue to experience issues, please contact support.')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
