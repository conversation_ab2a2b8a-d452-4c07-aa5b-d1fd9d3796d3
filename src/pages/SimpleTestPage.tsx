import React from 'react';
import { useParams } from 'react-router-dom';

export default function SimpleTestPage() {
  const { orderId } = useParams<{ orderId: string }>();
  
  console.log('🔍 [SIMPLE_TEST] Component mounted with orderId:', orderId);
  
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Simple Test Page</h1>
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <p className="text-gray-600 mb-2">Order ID: {orderId}</p>
          <p className="text-gray-600 mb-2">Component Status: Mounted Successfully</p>
          <p className="text-gray-600 mb-2">Check browser console for logs</p>
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
            <p className="text-green-800">✅ If you can see this page, the routing is working correctly!</p>
          </div>
        </div>
      </div>
    </div>
  );
}
