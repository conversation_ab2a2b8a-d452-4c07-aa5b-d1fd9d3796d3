
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { ProductTable } from '@/components/products/ProductTable';
import { CategorySelector } from '@/components/categories/CategorySelector';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Category } from '@/types';
import { toast } from 'sonner';

// Mock categories data
const mockCategories: Category[] = [
  {
    id: 'tyres',
    name: 'Tyres & Related Products',
    description: 'Tires, wheel covers, and related accessories',
    slug: 'tyres-related-products',
    productCount: 100,
    isDefault: false
  },
  {
    id: 'brakes',
    name: 'Brake Parts & Systems',
    description: 'Brake discs, pads, calipers, and related brake system components',
    slug: 'brake-parts-systems',
    productCount: 100,
    isDefault: false
  }
];

// Define all core columns for tyres products
const tyresCoreColumns = [
  "id", "name", "sku", "barcode", "category", "subcategory",
  "manufacturer", "supplierName", "description", "primaryImage", "additionalImages"
];

// Define core columns for brake products
const brakesCoreColumns = [
  "id", "name", "sku", "barcode", "category", "subcategory",
  "manufacturer", "supplierName", "description", "primaryImage", "additionalImages"
];

// Default columns for each category
const categoryColumns = {
  "all": ["id", "name", "sku", "barcode", "category", "subcategory", "manufacturer",
          "supplierName", "description", "primaryImage", "additionalImages",
          "retailPrice", "stockQuantity", "status", "actions"],
  "tyres": tyresCoreColumns.concat(["status", "actions"]), // Include status and actions
  "brakes": brakesCoreColumns.concat(["status", "actions"]) // Include status and actions
};

export default function CategoryProductsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(categoryColumns["all"]);
  const [categoryId, setCategoryId] = useState<string>("all");

  useEffect(() => {
    if (slug) {
      const category = categories.find(c => c.slug === slug);
      if (category) {
        setCurrentCategory(category);
        setCategoryId(category.id);
        // Update visible columns when category changes
        const columns = categoryColumns[category.id as keyof typeof categoryColumns] || categoryColumns["all"];
        setVisibleColumns(columns);
        console.log('Setting category ID to:', category.id);
      } else {
        // Category not found
        toast.error(t('categories.notFound'));
        navigate('/app/supplier/products-table');
      }
    } else {
      // No slug, set to all
      setCategoryId("all");
      setVisibleColumns(categoryColumns["all"]);
    }
  }, [slug, categories, navigate, t]);

  // Log to help with debugging
  console.log('CategoryProductsPage - Active Category ID:', categoryId);

  const handleAddCategory = (newCategory: Omit<Category, 'id'>) => {
    // In a real app, this would be an API call
    const categoryWithId = {
      ...newCategory,
      id: `category-${Date.now()}`,
      productCount: 0
    };

    setCategories([...categories, categoryWithId]);
    toast.success(t('categories.addedSuccessfully'));
  };

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {currentCategory
                ? currentCategory.name
                : t('products.productLibrary')}
            </h1>
            <p className="text-muted-foreground mt-1">
              {currentCategory
                ? currentCategory.description
                : t('products.libraryDescription')}
            </p>
          </div>
          <Button
            className="gap-2 bg-electric-orange hover:bg-electric-orange/90"
            onClick={() => navigate('/app/supplier/products/new')}
          >
            <Plus className="h-4 w-4" />
            {t('products.addProduct')}
          </Button>
        </div>

        <CategorySelector
          categories={categories}
          currentCategory={slug}
          onAddCategory={handleAddCategory}
        />

        <ProductTable
          activeCategory={categoryId}
          visibleColumns={visibleColumns}
        />
      </div>
    </AdminLayout>
  );
}
