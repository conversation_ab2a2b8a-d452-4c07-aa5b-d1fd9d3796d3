import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '@/contexts/UserContext';
import { Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getUserProfile } from '@/services/authService';
import { supabase } from '@/services/authService';

const AuthCallbackPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setUserRole, setSupplierRole, setMerchantRole } = useUser();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Error getting session:', sessionError);
          setStatus('error');
          setMessage(t('auth.verificationFailed'));
          return;
        }

        // Check if this is a duplicate verification attempt
        const urlParams = new URLSearchParams(window.location.search);
        const isDuplicate = urlParams.get('duplicate') === 'true';

        if (isDuplicate) {
          setStatus('error');
          setMessage(t('auth.alreadyVerified'));
          return;
        }

        if (!session) {
          // Try to exchange the auth code for a session
          const { data, error } = await supabase.auth.exchangeCodeForSession(
            window.location.hash.substring(1)
          );

          if (error) {
            console.error('Error exchanging code for session:', error);

            // Check if the error is due to the link being invalid or expired
            if (error.message.includes('invalid') || error.message.includes('expired')) {
              setStatus('error');
              setMessage(t('auth.linkExpired'));
              return;
            }

            // Check if the error is due to the user already being verified
            if (error.message.includes('already confirmed') || error.message.includes('already verified')) {
              setStatus('error');
              setMessage(t('auth.alreadyVerified'));
              return;
            }

            setStatus('error');
            setMessage(t('auth.verificationFailed'));
            return;
          }

          if (!data.session) {
            setStatus('error');
            setMessage(t('auth.verificationFailed'));
            return;
          }
        }

        // At this point we should have a valid session
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          setStatus('error');
          setMessage(t('auth.userNotFound'));
          return;
        }

        // Get the user's profile to determine their role
        // The profile might not be immediately available after confirmation
        // so we'll try to get it from the user metadata first
        let role: string | null = null;

        // Try to get role from user metadata
        if (user.user_metadata && user.user_metadata.role) {
          role = user.user_metadata.role as string;
          console.log('Got role from user metadata:', role);
        }

        // If not found in metadata, try to get from the profile
        if (!role) {
          const { profile, error: profileError } = await getUserProfile(user.id);

          if (profileError) {
            console.error('Error getting user profile:', profileError);
          }

          if (profile) {
            role = profile.role;
            console.log('Got role from profile:', role);
          }
        }

        // If we still don't have a role, use a default or show an error
        if (!role) {
          console.error('Could not determine user role');
          // Default to supplier if we can't determine the role
          // This is a fallback to ensure the user can access the dashboard
          role = 'supplier';
        }

        // Set the user's role
        setUserRole(role);

        // Set role-specific flags
        if (role === 'supplier') {
          setSupplierRole(true);
        } else if (role === 'merchant') {
          setMerchantRole(true);
        }

        // Update verification status
        setStatus('success');
        setMessage(t('auth.verificationSuccess'));

        // Redirect to the dashboard after a short delay
        setTimeout(() => {
          navigate('/app/dashboard');
        }, 1500);
      } catch (error) {
        console.error('Error in auth callback:', error);
        setStatus('error');
        setMessage(t('auth.unexpectedError'));
      }
    };

    handleAuthCallback();
  }, [navigate, setUserRole, setSupplierRole, setMerchantRole, t]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-xl shadow-lg">
        <div className="flex flex-col items-center justify-center text-center">
          <img
            src="/images/AROUZ Header Logo.png"
            alt="AROUZ MARKET"
            className="h-16 mb-6"
          />

          {status === 'loading' && (
            <>
              <Loader2 className="h-12 w-12 text-[#fa7b00] animate-spin mb-4" />
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                {t('auth.verifyingAccount')}
              </h1>
              <p className="text-gray-600">
                {t('auth.pleaseWait')}
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                {t('auth.accountVerified')}
              </h1>
              <p className="text-gray-600 mb-4">
                {t('auth.redirectingToDashboard')}
              </p>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-[#fa7b00] h-2.5 rounded-full animate-progress"></div>
              </div>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                {t('auth.verificationFailed')}
              </h1>
              <p className="text-gray-600 mb-6">
                {message || t('auth.unexpectedError')}
              </p>
              <button
                onClick={() => navigate('/partners')}
                className="px-6 py-2 bg-[#fa7b00] text-white rounded-lg hover:bg-[#fa7b00]/90 transition-colors"
              >
                {t('auth.returnToPartners')}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthCallbackPage;
