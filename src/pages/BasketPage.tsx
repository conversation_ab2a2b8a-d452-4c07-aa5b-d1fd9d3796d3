import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ShoppingCart,
  User,
  MapPin,
  CreditCard,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Lock,
  Truck,
  Store
} from 'lucide-react';

import { useCart } from '@/contexts/CartContext';
import { useConsumerAuth } from '@/components/auth/ConsumerAuthGuard';
import { useAuth } from '@/contexts/AuthContext';
import { useLocation } from '@/contexts/LocationContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';

// Import step components
import { AuthenticationStep } from '@/components/checkout/AuthenticationStep';
import { DeliveryAddressStep } from '@/components/checkout/DeliveryAddressStep';
import { PaymentMethodStep } from '@/components/checkout/PaymentMethodStep';
import { ShippingSelectionStep } from '@/components/checkout/ShippingSelectionStep';
import { OrderConfirmationStep } from '@/components/checkout/OrderConfirmationStep';
import { EnhancedOrderSummary } from '@/components/checkout/EnhancedOrderSummary';
import { createOrder } from '@/services/orderService';
import { useToast } from '@/components/ui/use-toast';

// Types
export type PaymentMethod = 'cash_on_delivery' | 'store_pickup';

export interface CheckoutData {
  deliveryAddress: string;
  deliveryCoordinates: { lat: number; lng: number } | null;
  deliveryWilaya: string;
  googleMapsUrl: string;
  paymentMethod: PaymentMethod | null;
  specialInstructions: string;
  // NEW: Shipping information
  shippingInfo?: {
    company_id: string;
    company_name: string;
    shipping_cost: number;
    shipping_method: string;
    estimated_delivery: string;
    shipping_notes?: string;
  };
}

const STEPS = [
  { id: 1, name: 'Authentication', icon: User, description: 'Verify your phone number' },
  { id: 2, name: 'Delivery Address', icon: MapPin, description: 'Confirm delivery location' },
  { id: 3, name: 'Payment Method', icon: CreditCard, description: 'Choose payment option' },
  { id: 4, name: 'Shipping Method', icon: Truck, description: 'Select shipping company' },
  { id: 5, name: 'Confirmation', icon: CheckCircle, description: 'Review and confirm order' }
];

export function BasketPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { items: cartItems, totalItems, totalPrice, clearCart } = useCart();
  const { isAuthenticated, isLoading: authLoading } = useConsumerAuth();
  const { user, profile } = useAuth();
  const { selectedLocation } = useLocation();

  // Checkout state
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [checkoutData, setCheckoutData] = useState<CheckoutData>({
    deliveryAddress: '',
    deliveryCoordinates: null,
    deliveryWilaya: '',
    googleMapsUrl: '',
    paymentMethod: null,
    specialInstructions: ''
  });

  // Redirect if cart is empty
  useEffect(() => {
    if (totalItems === 0) {
      navigate('/my-vehicle-parts');
    }
  }, [totalItems, navigate]);

  // Handle authentication state changes - STRICT VALIDATION
  useEffect(() => {
    if (!isAuthenticated && currentStep > 1) {
      // CRITICAL: Reset to step 1 if user logs out during checkout
      setCurrentStep(1);
      toast({
        title: t('checkout.authRequired'),
        description: t('checkout.pleaseSignIn'),
        variant: 'destructive',
        duration: 3000,
      });
    }
  }, [isAuthenticated, currentStep, toast, t]);

  // ONLY auto-advance if user is already authenticated when they first visit
  useEffect(() => {
    if (isAuthenticated && currentStep === 1) {
      // Brief delay to show authentication success, then auto-advance
      const timer = setTimeout(() => {
        setCurrentStep(2);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated]); // Remove currentStep dependency to prevent loops

  // Auto-fill delivery address if location is selected
  useEffect(() => {
    if (selectedLocation && !checkoutData.deliveryAddress) {
      setCheckoutData(prev => ({
        ...prev,
        deliveryAddress: selectedLocation.formatted_address,
        deliveryCoordinates: selectedLocation.coordinates,
        deliveryWilaya: selectedLocation.wilaya,
        googleMapsUrl: `https://maps.google.com/?q=${selectedLocation.coordinates.lat},${selectedLocation.coordinates.lng}`
      }));
    }
  }, [selectedLocation, checkoutData.deliveryAddress]);

  // STRICT STEP VALIDATION - No automatic progression
  const handleStepComplete = (stepData: Partial<CheckoutData>) => {
    // CRITICAL: Validate authentication for ALL steps
    if (!isAuthenticated) {
      toast({
        title: t('checkout.authRequired'),
        description: t('checkout.pleaseSignInFirst'),
        variant: 'destructive',
        duration: 3000,
      });
      setCurrentStep(1);
      return;
    }

    // Validate step-specific requirements
    if (!validateStepRequirements(currentStep, stepData)) {
      return; // Don't proceed if validation fails
    }

    // Update checkout data
    setCheckoutData(prev => ({ ...prev, ...stepData }));

    // Manual progression only - user must explicitly complete each step
    if (currentStep < 5) {
      setCurrentStep(prev => prev + 1);
    }
  };

  // Validate step requirements before allowing progression
  const validateStepRequirements = (step: number, stepData: Partial<CheckoutData>): boolean => {
    switch (step) {
      case 1:
        if (!isAuthenticated) {
          toast({
            title: t('checkout.authRequired'),
            description: t('checkout.completeAuthentication'),
            variant: 'destructive',
          });
          return false;
        }
        return true;

      case 2:
        const hasDeliveryData = stepData.deliveryAddress && stepData.googleMapsUrl;
        if (!hasDeliveryData) {
          toast({
            title: t('checkout.deliveryRequired'),
            description: t('checkout.selectDeliveryLocation'),
            variant: 'destructive',
          });
          return false;
        }
        return true;

      case 3:
        if (!stepData.paymentMethod) {
          toast({
            title: t('checkout.paymentRequired'),
            description: t('checkout.selectPaymentMethod'),
            variant: 'destructive',
          });
          return false;
        }
        return true;

      case 4:
        if (!stepData.shippingInfo) {
          toast({
            title: 'Shipping Required',
            description: 'Please select a shipping method',
            variant: 'destructive',
          });
          return false;
        }
        return true;

      default:
        return true;
    }
  };

  // STRICT NAVIGATION - Block unauthorized access
  const handleNext = () => {
    // CRITICAL: Block progression if not authenticated
    if (!isAuthenticated) {
      toast({
        title: t('checkout.authRequired'),
        description: t('checkout.signInToContinue'),
        variant: 'destructive',
        duration: 3000,
      });
      setCurrentStep(1);
      return;
    }

    // Validate current step before allowing progression
    const currentStepValid = validateCurrentStep();
    if (!currentStepValid) {
      return; // Don't proceed if current step is invalid
    }

    if (currentStep < 5) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleBackStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  // Validate current step completion
  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        return isAuthenticated;
      case 2:
        return !!(checkoutData.deliveryAddress && checkoutData.googleMapsUrl);
      case 3:
        return !!checkoutData.paymentMethod;
      case 4:
        return !!checkoutData.shippingInfo;
      default:
        return true;
    }
  };

  const handleOrderSubmit = async () => {
    console.log('🚀 [BASKET_PAGE] Order submission started...');
    setIsProcessing(true);
    try {
      console.log('🚀 [BASKET_PAGE] Creating order with data:', checkoutData);
      console.log('🚀 [BASKET_PAGE] Cart items:', cartItems);
      console.log('🚀 [BASKET_PAGE] Current authentication state:', {
        isAuthenticated,
        user: user?.id,
        profile: profile?.id
      });

      // Create order using the order service
      console.log('🚀 [BASKET_PAGE] Calling createOrder service...');
      const result = await createOrder(checkoutData, cartItems);
      console.log('🚀 [BASKET_PAGE] Order service result:', result);

      if (result.success && result.order) {
        console.log('✅ [BASKET_PAGE] Order created successfully:', result.order);
        // Show success message
        toast({
          title: t('checkout.orderCreated'),
          description: `${t('checkout.orderNumber')}: ${result.order.order_number}`,
          duration: 5000,
        });

        // Clear cart and redirect to order success page
        clearCart();
        navigate(`/order-success?orderId=${result.order.id}`);
      } else {
        console.error('❌ [BASKET_PAGE] Order creation failed:', result.error);
        // Show error message
        toast({
          title: t('checkout.orderFailed'),
          description: result.error || t('checkout.orderFailedDescription'),
          variant: 'destructive',
          duration: 5000,
        });
      }
    } catch (error) {
      console.error('❌ [BASKET_PAGE] Unexpected error creating order:', error);
      toast({
        title: t('checkout.orderFailed'),
        description: t('checkout.unexpectedError'),
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      console.log('🏁 [BASKET_PAGE] Order submission completed, setting isProcessing to false');
      setIsProcessing(false);
    }
  };

  const getStepProgress = () => {
    return (currentStep / STEPS.length) * 100;
  };

  const isStepCompleted = (stepId: number) => {
    switch (stepId) {
      case 1:
        return isAuthenticated;
      case 2:
        return checkoutData.deliveryAddress && checkoutData.googleMapsUrl;
      case 3:
        return checkoutData.paymentMethod !== null;
      case 4:
        return checkoutData.shippingInfo !== undefined;
      case 5:
        return false; // Final step
      default:
        return false;
    }
  };

  const canProceedToStep = (stepId: number) => {
    // CRITICAL: Always require authentication for steps 2-4
    if (stepId > 1 && !isAuthenticated) {
      return false;
    }

    if (stepId === 1) return true;
    return isStepCompleted(stepId - 1);
  };

  if (totalItems === 0) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Professional Header */}
      <div className="bg-gradient-to-r from-white to-gray-50 border-b border-gray-200 sticky top-0 z-40 shadow-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(-1)}
                className="text-gray-600 hover:text-[#fa7b00] hover:bg-orange-50 transition-all duration-200 rounded-lg px-4 py-2"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('common.back')}
              </Button>
              <div className="border-l border-gray-300 pl-6">
                <h1 className="text-3xl font-bold text-[#071c44] tracking-tight">
                  {t('checkout.title')}
                </h1>
                <p className="text-sm text-gray-600 mt-1 font-medium">
                  {totalItems} {totalItems === 1 ? t('marketplace.item') : t('marketplace.items')} •
                  <span className="text-[#fa7b00] font-semibold ml-1">
                    {new Intl.NumberFormat('fr-DZ', {
                      style: 'currency',
                      currency: 'DZD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(totalPrice)}
                  </span>
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 bg-green-50 px-4 py-2 rounded-full border border-green-200">
              <Lock className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-700 font-semibold">
                {t('checkout.secureCheckout')}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Professional Progress Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between relative">
              {/* Progress Line */}
              <div className="absolute top-6 left-0 right-0 h-1 bg-gray-200 rounded-full">
                <div
                  className="h-full bg-gradient-to-r from-[#fa7b00] to-[#ff9500] rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${((currentStep - 1) / (STEPS.length - 1)) * 100}%` }}
                />
              </div>

              {/* Steps */}
              {STEPS.map((step, index) => {
                const isCompleted = isStepCompleted(step.id);
                const isCurrent = currentStep === step.id;
                const isPending = currentStep < step.id;

                return (
                  <div key={step.id} className="relative flex flex-col items-center z-10">
                    {/* Step Circle */}
                    <div className={`
                      w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ease-out transform
                      ${isCompleted
                        ? 'bg-gradient-to-r from-[#fa7b00] to-[#ff9500] text-white shadow-lg scale-110'
                        : isCurrent
                        ? 'bg-white border-4 border-[#fa7b00] text-[#fa7b00] shadow-lg scale-110'
                        : 'bg-white border-2 border-gray-300 text-gray-400 hover:border-gray-400'
                      }
                    `}>
                      {isCompleted ? (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <CheckCircle className="h-6 w-6" />
                        </motion.div>
                      ) : (
                        <span className={`text-lg font-bold ${isCurrent ? 'text-[#fa7b00]' : ''}`}>
                          {step.id}
                        </span>
                      )}
                    </div>

                    {/* Step Info */}
                    <div className="mt-3 text-center max-w-[120px]">
                      <p className={`text-sm font-semibold transition-colors duration-200 ${
                        isCompleted || isCurrent ? 'text-[#fa7b00]' : 'text-gray-500'
                      }`}>
                        {step.name}
                      </p>
                      <p className={`text-xs mt-1 hidden sm:block transition-colors duration-200 ${
                        isCompleted || isCurrent ? 'text-gray-700' : 'text-gray-400'
                      }`}>
                        {step.description}
                      </p>
                    </div>

                    {/* Active Step Indicator */}
                    {isCurrent && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute -bottom-2 w-2 h-2 bg-[#fa7b00] rounded-full"
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Steps */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {currentStep === 1 && (
                  <AuthenticationStep
                    onComplete={() => handleStepComplete({})}
                    onBack={handleBackStep}
                  />
                )}
                {currentStep === 2 && (
                  <DeliveryAddressStep
                    data={checkoutData}
                    onComplete={handleStepComplete}
                    onBack={handleBackStep}
                  />
                )}
                {currentStep === 3 && (
                  <PaymentMethodStep
                    data={checkoutData}
                    onComplete={handleStepComplete}
                    onBack={handleBackStep}
                  />
                )}
                {currentStep === 4 && (
                  <ShippingSelectionStep
                    data={checkoutData}
                    orderValue={totalPrice}
                    onComplete={handleStepComplete}
                    onBack={handleBackStep}
                  />
                )}
                {currentStep === 5 && (
                  <OrderConfirmationStep
                    data={checkoutData}
                    cartItems={cartItems}
                    onConfirm={handleOrderSubmit}
                    onBack={handleBackStep}
                    isProcessing={isProcessing}
                  />
                )}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <EnhancedOrderSummary
              cartItems={cartItems}
              checkoutData={checkoutData}
              totalPrice={totalPrice}
              currentStep={currentStep}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
