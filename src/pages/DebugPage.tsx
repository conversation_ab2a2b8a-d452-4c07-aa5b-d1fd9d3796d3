import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCart } from '@/contexts/CartContext';
import { fetchMarketplaceProductsByCategory } from '@/services/productService';

export function DebugPage() {
  const { items: cartItems } = useCart();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchTestProducts = async () => {
    setLoading(true);
    try {
      console.log('🧪 [DEBUG] Fetching marketplace products...');
      const tyreProducts = await fetchMarketplaceProductsByCategory('tyres');
      console.log('🧪 [DEBUG] Fetched products:', tyreProducts);
      setProducts(tyreProducts);
    } catch (error) {
      console.error('🧪 [DEBUG] Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const testCartData = () => {
    console.log('🧪 [DEBUG] Current cart items:', cartItems);
    
    // Test supplier grouping
    const itemsBySupplier = cartItems.reduce((acc, item) => {
      const supplierKey = item.supplierName || 'Unknown Supplier';
      if (!acc[supplierKey]) {
        acc[supplierKey] = {
          items: [],
          location: item.shippingOrigin || 'Location TBD'
        };
      }
      acc[supplierKey].items.push(item);
      return acc;
    }, {} as Record<string, { items: any[]; location: string }>);

    console.log('🧪 [DEBUG] Grouped by supplier:', itemsBySupplier);
  };

  const testLocalStorage = () => {
    console.log('🧪 [DEBUG] LocalStorage data:');
    console.log('Cart:', localStorage.getItem('arouz_cart'));
    console.log('Phone Session:', localStorage.getItem('phone_auth_session'));
    console.log('Consumer Profile:', localStorage.getItem('consumer_profile'));
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold text-red-600">🐛 DEBUG PAGE</h1>
      
      {/* Product Fetching Test */}
      <Card>
        <CardHeader>
          <CardTitle>📦 Product Fetching Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={fetchTestProducts} disabled={loading}>
            {loading ? 'Loading...' : 'Fetch Test Products'}
          </Button>
          
          {products.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Fetched Products:</h3>
              {products.slice(0, 3).map((product, index) => (
                <div key={index} className="p-3 bg-gray-100 rounded text-sm">
                  <div><strong>ID:</strong> {product.id}</div>
                  <div><strong>Name:</strong> {product.name}</div>
                  <div><strong>Supplier:</strong> {product.supplierName}</div>
                  <div><strong>Supplier Account ID:</strong> {product.supplierAccountId}</div>
                  <div><strong>Shipping Origin:</strong> {product.shippingOrigin}</div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cart Data Test */}
      <Card>
        <CardHeader>
          <CardTitle>🛒 Cart Data Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testCartData}>Test Cart Data in Console</Button>
          
          <div className="space-y-2">
            <h3 className="font-semibold">Current Cart Items ({cartItems.length}):</h3>
            {cartItems.map((item, index) => (
              <div key={index} className="p-3 bg-gray-100 rounded text-sm">
                <div><strong>Name:</strong> {item.name}</div>
                <div><strong>Supplier:</strong> {item.supplierName}</div>
                <div><strong>Supplier Account ID:</strong> {item.supplierAccountId}</div>
                <div><strong>Shipping Origin:</strong> {item.shippingOrigin}</div>
                <div><strong>Original Product ID:</strong> {item.originalProductId}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* LocalStorage Test */}
      <Card>
        <CardHeader>
          <CardTitle>💾 LocalStorage Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testLocalStorage}>Test LocalStorage in Console</Button>
          
          <div className="space-y-2 text-sm">
            <div><strong>Cart Items:</strong> {localStorage.getItem('arouz_cart')?.length || 0} characters</div>
            <div><strong>Phone Session:</strong> {localStorage.getItem('phone_auth_session') ? 'Present' : 'Missing'}</div>
            <div><strong>Consumer Profile:</strong> {localStorage.getItem('consumer_profile') ? 'Present' : 'Missing'}</div>
          </div>
        </CardContent>
      </Card>

      {/* Database Test */}
      <Card>
        <CardHeader>
          <CardTitle>🗄️ Database Connection Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={() => {
            console.log('🧪 [DEBUG] Testing database connection...');
            import('@/services/authService').then(({ supabase }) => {
              supabase.from('products').select('id, name, supplier_name, shipping_origin').limit(5)
                .then(({ data, error }) => {
                  if (error) {
                    console.error('🧪 [DEBUG] Database error:', error);
                  } else {
                    console.log('🧪 [DEBUG] Database products:', data);
                  }
                });
            });
          }}>
            Test Database Connection
          </Button>
        </CardContent>
      </Card>

      {/* Order Creation Test */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Order Creation Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={() => {
            console.log('🧪 [DEBUG] Testing order creation data transformation...');
            
            const transformedItems = cartItems.map(item => ({
              product_id: item.originalProductId || item.id.toString(),
              product_name: item.name,
              quantity: item.quantity,
              unit_price: item.price,
              total_price: item.price * item.quantity,
              supplier_name: item.supplierName || 'Unknown Supplier',
              supplier_wilaya: item.shippingOrigin || '',
              supplier_account_id: item.supplierAccountId || '',
              marketplace_section: item.marketplaceSection || 'retail'
            }));
            
            console.log('🧪 [DEBUG] Transformed order items:', transformedItems);
          }}>
            Test Order Data Transformation
          </Button>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800">📋 Debug Instructions</CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <ol className="list-decimal list-inside space-y-2">
            <li>Open browser console (F12)</li>
            <li>Click "Fetch Test Products" to see if products have shipping origins</li>
            <li>Add products to cart from marketplace</li>
            <li>Click "Test Cart Data" to verify cart items have shipping origins</li>
            <li>Go to checkout and check if shipping origins display correctly</li>
            <li>Use the debug section in checkout for detailed analysis</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
