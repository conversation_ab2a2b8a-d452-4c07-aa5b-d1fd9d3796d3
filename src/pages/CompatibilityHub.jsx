import React, { useState, useEffect } from 'react';
import VehicleTypeSelector from '../components/compatibility/VehicleTypeSelector';
import SearchFilters from '../components/compatibility/SearchFilters';
import VehicleGrid from '../components/compatibility/VehicleGrid';
import MarketDemandModal from '../components/compatibility/MarketDemandModal';
import { fetchMarketDemand } from '../services/vehicleService';
import {
  fetchCarBrands,
  fetchCarModels,
  fetchCarEngines,
  getVehicleByIdentifiers,
  parseUniqueId,
  fetchAllVehicles
} from '../services/vehicleDataService';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Button } from '@/components/ui/button';

const CompatibilityHub = () => {
  const [selectedType, setSelectedType] = useState('car');
  const [filters, setFilters] = useState({
    brand: '',
    model: '',
    engineType: ''
  });
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showMarketDemand, setShowMarketDemand] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [marketDemandData, setMarketDemandData] = useState([]);

  // State for dropdown options
  const [brandOptions, setBrandOptions] = useState([]);
  const [modelOptions, setModelOptions] = useState([]);
  const [engineOptions, setEngineOptions] = useState([]);

  // Fetch brands when component mounts or vehicle type changes
  useEffect(() => {
    const getBrands = async () => {
      setLoading(true);
      try {
        if (selectedType === 'car') {
          const brands = await fetchCarBrands();
          setBrandOptions(brands);
        } else {
          // For other vehicle types, we'll use the old implementation
          // This would be replaced with similar functions for motorcycles and trucks
          setBrandOptions([]);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
        setBrandOptions([]);
      } finally {
        setLoading(false);
      }
    };

    getBrands();

    // Reset filters when changing vehicle type
    setFilters({
      brand: '',
      model: '',
      engineType: ''
    });
  }, [selectedType]);

  // Fetch models when brand changes
  useEffect(() => {
    const getModels = async () => {
      if (!filters.brand) {
        setModelOptions([]);
        return;
      }

      setLoading(true);
      try {
        if (selectedType === 'car') {
          const models = await fetchCarModels(filters.brand);
          console.log(`CompatibilityHub: Received ${models.length} models for ${filters.brand}`);

          // Log all model names to verify what's being set in the dropdown
          if (models.length > 0) {
            const uniqueModelNames = [...new Set(models.map(m => m.model))];
            console.log(`CompatibilityHub: Unique model names (${uniqueModelNames.length}):`, uniqueModelNames.join(', '));
          } else {
            console.warn(`CompatibilityHub: No models received for ${filters.brand}`);
          }

          setModelOptions(models);
        } else {
          // For other vehicle types
          setModelOptions([]);
        }
      } catch (error) {
        console.error('Error fetching models:', error);
        setModelOptions([]);
      } finally {
        setLoading(false);
      }
    };

    getModels();
  }, [selectedType, filters.brand]);

  // Fetch engines when model changes
  useEffect(() => {
    const getEngines = async () => {
      if (!filters.brand || !filters.model) {
        setEngineOptions([]);
        return;
      }

      setLoading(true);
      try {
        if (selectedType === 'car') {
          // Use the uniqueId directly from the model filter
          const uniqueId = filters.model;

          console.log('CompatibilityHub: Fetching engines for model with uniqueId:', uniqueId);

          // Parse the uniqueId to get model and generation information
          const { brand: parsedBrand, model: modelName, generationCode } = parseUniqueId(uniqueId);

          // Use the parsed brand if available, otherwise use the selected brand
          const brandToUse = parsedBrand || filters.brand;

          console.log('CompatibilityHub: Using brand:', brandToUse, 'model:', modelName, 'generation:', generationCode);

          const engines = await fetchCarEngines(brandToUse, modelName, generationCode);
          console.log(`CompatibilityHub: Received ${engines.length} engines for ${brandToUse} ${modelName} ${generationCode}`);
          setEngineOptions(engines);
        } else {
          // For other vehicle types
          setEngineOptions([]);
        }
      } catch (error) {
        console.error('Error fetching engines:', error);
        setEngineOptions([]);
      } finally {
        setLoading(false);
      }
    };

    getEngines();
  }, [selectedType, filters.brand, filters.model]);

  // Fetch vehicles based on selected filters
  useEffect(() => {
    const getVehicles = async () => {
      setLoading(true);
      try {
        if (selectedType === 'car') {
          let results = [];

          // If all filters are selected, get the specific vehicle
          if (filters.brand && filters.model && filters.engineType) {
            console.log('Fetching specific vehicle with engine:', filters.engineType);
            console.log('Selected filters:', filters);

            // Parse the uniqueId to get model and generation information
            const { brand: parsedBrand, model, generationCode } = parseUniqueId(filters.model);

            // Use the parsed brand if available, otherwise use the selected brand
            const brandToUse = parsedBrand || filters.brand;

            console.log('Using brand:', brandToUse, 'for vehicle lookup');

            // First try to get the specific vehicle with the selected engine
            const vehicle = await getVehicleByIdentifiers(
              brandToUse,
              filters.model,
              filters.engineType
            );

            if (vehicle) {
              results = [vehicle];
              console.log('Found specific vehicle with engine:', vehicle);
              console.log('Engine details:', vehicle.engineDetails);
            } else {
              console.warn('No vehicle found with the selected engine, falling back to model search');
              console.log('Selected filters that failed:', filters);

              // If no specific vehicle is found, fall back to showing all vehicles for the model
              // Parse the uniqueId to get model and generation information
              const { brand: parsedBrand, model, generationCode } = parseUniqueId(filters.model);

              // Use the parsed brand if available, otherwise use the selected brand
              const brandToUse = parsedBrand || filters.brand;

              const modelVehicles = await fetchAllVehicles(brandToUse, filters.model);

              // Get all engines for this model/generation
              const engines = await fetchCarEngines(brandToUse, model, generationCode);

              // Find the selected engine in the engines list
              const selectedEngine = engines.find(engine => engine.fullName === filters.engineType);

              if (selectedEngine) {
                console.log('Found selected engine:', selectedEngine);

                // Filter vehicles that match the engine properties
                results = modelVehicles.filter(vehicle => {
                  if (!vehicle.engineDetails) return false;

                  // Match by engine code
                  if (selectedEngine.code && vehicle.engineDetails.code === selectedEngine.code) {
                    return true;
                  }

                  // Match by power values
                  if (selectedEngine.power_kW && selectedEngine.power_HP &&
                      vehicle.engineDetails.power_kW === selectedEngine.power_kW &&
                      vehicle.engineDetails.power_HP === selectedEngine.power_HP) {
                    return true;
                  }

                  // Match by fuel type
                  if (selectedEngine.fuel_type && vehicle.engineDetails.fuel_type === selectedEngine.fuel_type) {
                    // If we're matching just by fuel type, make sure we have some other matching property
                    if (vehicle.engineDetails.fullName.includes(selectedEngine.fullName) ||
                        selectedEngine.fullName.includes(vehicle.engineDetails.fullName)) {
                      return true;
                    }
                  }

                  return false;
                });

                if (results.length === 0) {
                  console.warn('No vehicles found matching the selected engine properties, showing all model vehicles');
                  results = modelVehicles;
                }
              } else {
                console.warn('Selected engine not found in engines list, showing all model vehicles');
                results = modelVehicles;
              }
            }
          }
          // If brand and model are selected, get all vehicles for that model
          else if (filters.brand && filters.model) {
            console.log('Fetching all vehicles for model:', filters.model);

            // Parse the uniqueId to get model and generation information
            const { brand: parsedBrand, model: modelName, generationCode } = parseUniqueId(filters.model);

            // Use the parsed brand if available, otherwise use the selected brand
            const brandToUse = parsedBrand || filters.brand;

            console.log('CompatibilityHub: Parsed model uniqueId for vehicle fetching:', {
              brand: brandToUse,
              model: modelName,
              generation: generationCode
            });

            results = await fetchAllVehicles(brandToUse, filters.model);
            console.log(`CompatibilityHub: Received ${results.length} vehicles for brand ${brandToUse}, model ${modelName}`);

            if (results.length === 0) {
              console.warn('No vehicles found for the selected model');
            }
          }
          // If only brand is selected, get all vehicles for that brand
          else if (filters.brand) {
            console.log('Fetching all vehicles for brand:', filters.brand);
            results = await fetchAllVehicles(filters.brand);

            // Sort results by model name for better organization
            results.sort((a, b) => a.model.localeCompare(b.model));

            if (results.length === 0) {
              console.warn('No vehicles found for the selected brand');
            }
          }
          // If no filters are selected, get all vehicles using the direct method
          else {
            console.log('Fetching all vehicles using direct method');

            // Use the direct method to ensure all variants are included
            results = await fetchAllVehicles();

            // Sort results by brand and model for better organization
            results.sort((a, b) => {
              const brandCompare = a.brand.localeCompare(b.brand);
              return brandCompare !== 0 ? brandCompare : a.model.localeCompare(b.model);
            });

            console.log(`Showing all ${results.length} vehicles`);
          }

          console.log(`Setting ${results.length} vehicles in state`);
          setVehicles(results);
        } else {
          // For other vehicle types
          setVehicles([]);
        }
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        setVehicles([]);
      } finally {
        setLoading(false);
      }
    };

    getVehicles();
  }, [selectedType, filters.brand, filters.model, filters.engineType]);

  const handleTypeChange = (type) => {
    // Don't do anything if the same type is selected
    if (type === selectedType) return;

    // Show loading state
    setLoading(true);

    // Reset filters when changing vehicle type
    setFilters({
      brand: '',
      model: '',
      engineType: ''
    });

    // Change the selected type
    setSelectedType(type);
  };

  const handleFilterChange = (filterName, value) => {
    // If changing brand, reset model and engineType
    if (filterName === 'brand') {
      setFilters({
        brand: value,
        model: '',
        engineType: ''
      });
    }
    // If changing model, reset engineType
    else if (filterName === 'model') {
      setFilters({
        ...filters,
        model: value,
        engineType: ''
      });
    } else {
      setFilters({
        ...filters,
        [filterName]: value
      });
    }
  };

  const handleMarketDemand = async (vehicleId) => {
    try {
      // Find the vehicle in our list
      const vehicle = vehicles.find(v => v.id === vehicleId);
      if (!vehicle) return;

      setSelectedVehicle(vehicle);
      setShowMarketDemand(true);
      setMarketDemandData([]); // Clear previous data while loading

      // Show loading state
      const loadingTimeout = setTimeout(() => {
        setMarketDemandData([
          { category: 'Loading...', volume: '-', trend: 'up', trendPercentage: 0 }
        ]);
      }, 200);

      // Fetch market demand data
      const demandData = await fetchMarketDemand(vehicleId);

      // Clear loading timeout
      clearTimeout(loadingTimeout);

      // Update with real data
      setMarketDemandData(demandData);
    } catch (error) {
      console.error('Error fetching market demand:', error);
      setMarketDemandData([
        { category: 'Error loading data', volume: '-', trend: 'down', trendPercentage: 0 }
      ]);
    }
  };

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold tracking-tight">Compatibility Hub</h1>
          <p className="text-muted-foreground">
            Find vehicles compatible with your products and explore market opportunities
          </p>
        </div>

        <div className="bg-white p-6 rounded-3xl border shadow-lg">
          <VehicleTypeSelector
            selectedType={selectedType}
            onTypeChange={handleTypeChange}
          />

          <div className="mt-8">
            <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-medium mb-2 md:mb-0">Find Your Vehicle</h3>
                {!loading && vehicles.length > 0 && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {!filters.brand && !filters.model && !filters.engineType
                      ? `Showing all ${vehicles.length} vehicles in the database`
                      : `Showing ${vehicles.length} vehicles matching your filters`}
                  </p>
                )}
              </div>
              <div className="flex gap-2">
                {(filters.brand || filters.model || filters.engineType) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFilters({ brand: '', model: '', engineType: '' })}
                    className="self-start md:self-auto rounded-full"
                  >
                    Reset Filters
                  </Button>
                )}

              </div>
            </div>
            <SearchFilters
              brands={brandOptions}
              models={modelOptions}
              engines={engineOptions}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        </div>

        <div>
          <VehicleGrid
            vehicles={vehicles}
            loading={loading}
            onMarketDemand={handleMarketDemand}
          />
        </div>

        {/* Market Demand Modal */}
        <MarketDemandModal
          open={showMarketDemand}
          onOpenChange={setShowMarketDemand}
          vehicle={selectedVehicle}
          demandData={marketDemandData}
        />
      </div>
    </AdminLayout>
  );
};

export default CompatibilityHub;
