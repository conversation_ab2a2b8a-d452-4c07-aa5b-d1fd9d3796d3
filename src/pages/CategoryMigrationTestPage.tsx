import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CategoryMigrationStatus } from '@/components/debug/CategoryMigrationStatus';
import { useLegacyCategories, useMigrationStatus } from '@/data/categoryDataSupabase';
import { CATEGORIES } from '@/data/categoryData';
import { Database, HardDrive, TestTube, CheckCircle, XCircle } from 'lucide-react';

export default function CategoryMigrationTestPage() {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  
  // Get data from both sources
  const { categories: supabaseCategories, isLoading, error } = useLegacyCategories();
  const { isMigrated, shouldUseFallback, categoriesCount } = useMigrationStatus();
  const localStorageCategories = CATEGORIES;

  const runTest = (testName: string, testFn: () => boolean) => {
    const result = testFn();
    setTestResults(prev => ({ ...prev, [testName]: result }));
    return result;
  };

  const runAllTests = () => {
    const tests = [
      {
        name: 'Categories Count Match',
        test: () => supabaseCategories.length === localStorageCategories.length
      },
      {
        name: 'All Categories Present',
        test: () => {
          const supabaseIds = new Set(supabaseCategories.map(cat => cat.id));
          return localStorageCategories.every(cat => supabaseIds.has(cat.id));
        }
      },
      {
        name: 'Subcategories Count Match',
        test: () => {
          const supabaseSubcategoriesCount = supabaseCategories.reduce(
            (total, cat) => total + cat.subcategories.length, 0
          );
          const localStorageSubcategoriesCount = localStorageCategories.reduce(
            (total, cat) => total + cat.subcategories.length, 0
          );
          return supabaseSubcategoriesCount === localStorageSubcategoriesCount;
        }
      },
      {
        name: 'Category Names Match',
        test: () => {
          return localStorageCategories.every(localCat => {
            const supabaseCat = supabaseCategories.find(cat => cat.id === localCat.id);
            return supabaseCat && supabaseCat.name === localCat.name;
          });
        }
      },
      {
        name: 'Display Names Match',
        test: () => {
          return localStorageCategories.every(localCat => {
            const supabaseCat = supabaseCategories.find(cat => cat.id === localCat.id);
            return supabaseCat && supabaseCat.displayName === localCat.displayName;
          });
        }
      }
    ];

    tests.forEach(({ name, test }) => runTest(name, test));
  };

  const getTestIcon = (testName: string) => {
    if (!(testName in testResults)) return <TestTube className="h-4 w-4 text-gray-400" />;
    return testResults[testName] 
      ? <CheckCircle className="h-4 w-4 text-green-500" />
      : <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getTestBadge = (testName: string) => {
    if (!(testName in testResults)) return <Badge variant="secondary">Not Run</Badge>;
    return testResults[testName]
      ? <Badge className="bg-green-100 text-green-800">Pass</Badge>
      : <Badge variant="destructive">Fail</Badge>;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Categories Migration Test</h1>
        <Badge className={isMigrated ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
          {isMigrated ? 'Migrated' : 'Not Migrated'}
        </Badge>
      </div>

      <Tabs defaultValue="status" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="status">Migration Status</TabsTrigger>
          <TabsTrigger value="comparison">Data Comparison</TabsTrigger>
          <TabsTrigger value="tests">Integrity Tests</TabsTrigger>
          <TabsTrigger value="raw">Raw Data</TabsTrigger>
        </TabsList>

        <TabsContent value="status" className="space-y-4">
          <CategoryMigrationStatus />
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            {/* Supabase Data */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-blue-500" />
                  Supabase Categories ({supabaseCategories.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div>Loading...</div>
                ) : error ? (
                  <div className="text-red-500">Error: {error.message}</div>
                ) : (
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {supabaseCategories.map(category => (
                      <div key={category.id} className="border rounded p-2">
                        <div className="font-medium">{category.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {category.subcategories.length} subcategories
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* localStorage Data */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="h-5 w-5 text-orange-500" />
                  localStorage Categories ({localStorageCategories.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {localStorageCategories.map(category => (
                    <div key={category.id} className="border rounded p-2">
                      <div className="font-medium">{category.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {category.subcategories.length} subcategories
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Data Integrity Tests</span>
                <Button onClick={runAllTests} disabled={isLoading}>
                  Run All Tests
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  'Categories Count Match',
                  'All Categories Present',
                  'Subcategories Count Match',
                  'Category Names Match',
                  'Display Names Match'
                ].map(testName => (
                  <div key={testName} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-2">
                      {getTestIcon(testName)}
                      <span>{testName}</span>
                    </div>
                    {getTestBadge(testName)}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="raw" className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Supabase Raw Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(supabaseCategories, null, 2)}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>localStorage Raw Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(localStorageCategories, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
