import React from 'react';
import { AdminPanelLayout } from '@/components/layout/AdminPanelLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Truck, Construction } from 'lucide-react';

export default function AdminShippingCompaniesPage() {
  return (
    <AdminPanelLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shipping Companies Management</h1>
          <p className="text-gray-600 mt-1">
            Manage delivery partners and their service areas
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Construction className="h-5 w-5 text-[#fa7b00]" />
              Coming Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Truck className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Shipping Companies Management
              </h3>
              <p className="text-gray-600">
                This section will allow you to manage shipping company partnerships, 
                coverage areas, and service configurations.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}
