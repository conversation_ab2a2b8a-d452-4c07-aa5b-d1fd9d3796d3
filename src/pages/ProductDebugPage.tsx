import React from 'react';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getAllProducts } from '@/utils/centralizedProductData';
import { Copy, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';

export default function ProductDebugPage() {
  const allProducts = getAllProducts();
  
  // Group products by category and marketplace section
  const groupedProducts = allProducts.reduce((acc, product) => {
    const category = product.category;
    const section = product.marketplaceSection || 'unknown';
    
    if (!acc[category]) {
      acc[category] = {};
    }
    if (!acc[category][section]) {
      acc[category][section] = [];
    }
    
    acc[category][section].push(product);
    return acc;
  }, {} as Record<string, Record<string, typeof allProducts>>);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`Copied "${text}" to clipboard`);
  };

  const openProduct = (productId: string) => {
    window.open(`/${productId}`, '_blank');
  };

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Product Debug Page</h1>
          <p className="text-gray-600 mb-4">
            This page shows all available product IDs in the system for testing purposes.
          </p>
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2">Available Product IDs</h3>
            <p className="text-sm text-blue-800">
              Total products: <strong>{allProducts.length}</strong> | 
              Categories: <strong>{Object.keys(groupedProducts).join(', ')}</strong>
            </p>
          </div>
        </div>

        {Object.entries(groupedProducts).map(([category, sections]) => (
          <div key={category} className="mb-8">
            <h2 className="text-2xl font-bold mb-4 capitalize">{category} Products</h2>
            
            {Object.entries(sections).map(([section, products]) => (
              <Card key={section} className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="capitalize">{section} {category}</span>
                    <Badge variant="outline">{products.length} products</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {products.slice(0, 12).map((product) => (
                      <div
                        key={product.id}
                        className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                      >
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary" className="text-xs font-mono">
                              {product.id}
                            </Badge>
                            <div className="flex space-x-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => copyToClipboard(product.id)}
                                className="h-6 w-6 p-0"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => openProduct(product.id)}
                                className="h-6 w-6 p-0"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <h4 className="text-sm font-medium line-clamp-2">{product.name}</h4>
                          <p className="text-xs text-gray-600">{product.manufacturer}</p>
                          <div className="text-xs text-gray-500">
                            Stock: {product.stockQuantity}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {products.length > 12 && (
                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-600">
                        Showing 12 of {products.length} products
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ))}

        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-bold mb-4">Quick Test Links</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {allProducts.slice(0, 8).map((product) => (
              <Button
                key={product.id}
                variant="outline"
                size="sm"
                onClick={() => openProduct(product.id)}
                className="text-xs"
              >
                {product.id}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </MarketplaceLayout>
  );
}
