import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Package,
  Truck,
  Clock,
  CheckCircle,
  AlertCircle,
  Search,
  RefreshCw,
  ExternalLink,
  Phone,
  MapPin,
  Calendar,
  Building2
} from 'lucide-react';
import {
  getSupplierShipments,
  SupplierShipment
} from '@/services/supplierShippingService';

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'unassigned': return AlertCircle;
    case 'assigned': return Clock;
    case 'picked_up': return Truck;
    case 'in_transit': return Truck;
    case 'delivered': return CheckCircle;
    default: return Package;
  }
};

const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'unassigned': return 'secondary';
    case 'assigned': return 'default';
    case 'picked_up': return 'default';
    case 'in_transit': return 'default';
    case 'delivered': return 'default';
    default: return 'secondary';
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function SupplierShipmentsPage() {
  const { t } = useTranslation();
  const { toast } = useToast();

  // State management
  const [shipments, setShipments] = useState<SupplierShipment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const ITEMS_PER_PAGE = 10;

  // Load shipments
  const loadShipments = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      console.log('📦 [SUPPLIER_SHIPMENTS_PAGE] Loading shipments...');

      // Load shipments with filters
      const filters = {
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchTerm || undefined,
        limit: ITEMS_PER_PAGE,
        offset: (currentPage - 1) * ITEMS_PER_PAGE
      };

      const result = await getSupplierShipments(filters);
      if (result.success) {
        setShipments(result.shipments || []);
        setTotalCount(result.total_count || 0);
      } else {
        toast({
          title: 'Failed to load shipments',
          description: result.error || 'Please try again',
          variant: 'destructive'
        });
      }

    } catch (error) {
      console.error('❌ [SUPPLIER_SHIPMENTS_PAGE] Error loading shipments:', error);
      toast({
        title: 'Failed to load shipments',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load and when filters change
  useEffect(() => {
    loadShipments();
  }, [currentPage, statusFilter]);

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        loadShipments();
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Shipments Management</h1>
            <p className="text-gray-600 mt-1">
              Track and manage shipments for your orders
            </p>
          </div>
          <Button
            onClick={() => loadShipments(true)}
            disabled={isRefreshing}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter Shipments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by order number, company, or tracking number..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="picked_up">Picked Up</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shipments List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5 text-[#fa7b00]" />
              Shipments ({totalCount})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-32" />
                ))}
              </div>
            ) : shipments.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No shipments found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your filters or search terms'
                    : 'Shipments for your orders will appear here'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {shipments.map((shipment) => {
                  const StatusIcon = getStatusIcon(shipment.status);
                  
                  return (
                    <div
                      key={shipment.id}
                      className="border rounded-lg p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <StatusIcon className="h-6 w-6 text-[#fa7b00]" />
                          <div>
                            <h3 className="font-semibold text-lg">
                              Order #{shipment.orders?.order_number || 'N/A'}
                            </h3>
                            <p className="text-gray-600 text-sm">
                              Shipment ID: {shipment.id.slice(0, 8)}...
                            </p>
                          </div>
                        </div>
                        <Badge variant={getStatusBadgeVariant(shipment.status)} className="text-sm px-3 py-1">
                          {shipment.status.charAt(0).toUpperCase() + shipment.status.slice(1).replace('_', ' ')}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        {shipment.company_name && (
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-gray-400" />
                            <span className="text-sm font-medium">{shipment.company_name}</span>
                          </div>
                        )}
                        
                        {shipment.tracking_number && (
                          <div className="flex items-center gap-2">
                            <Package className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">Tracking: {shipment.tracking_number}</span>
                          </div>
                        )}
                        
                        {shipment.estimated_delivery_date && (
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">
                              ETA: {new Date(shipment.estimated_delivery_date).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            Created: {formatDate(shipment.created_at)}
                          </span>
                        </div>
                      </div>

                      {shipment.driver_name && (
                        <div className="bg-gray-50 rounded-lg p-3 mb-4">
                          <h4 className="font-medium text-sm mb-2">Driver Information</h4>
                          <div className="flex items-center gap-4">
                            <span className="text-sm">{shipment.driver_name}</span>
                            {shipment.driver_phone && (
                              <div className="flex items-center gap-1">
                                <Phone className="h-3 w-3 text-gray-400" />
                                <span className="text-sm">{shipment.driver_phone}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-600">
                          Assigned by: {shipment.assigned_by === 'supplier' ? 'You' : shipment.assigned_by}
                          {shipment.assigned_at && (
                            <span className="ml-2">
                              on {formatDate(shipment.assigned_at)}
                            </span>
                          )}
                        </div>
                        
                        {shipment.tracking_url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(shipment.tracking_url, '_blank')}
                            className="flex items-center gap-2"
                          >
                            <ExternalLink className="h-4 w-4" />
                            Track Package
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6 pt-6 border-t">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, totalCount)} of {totalCount} shipments
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm px-3 py-1 bg-gray-100 rounded">
                    {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
