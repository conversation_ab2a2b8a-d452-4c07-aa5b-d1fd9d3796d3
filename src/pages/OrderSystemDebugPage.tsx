import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/services/authService';
import { getSupplierOrderById } from '@/services/supplierOrderService';
import { getShippingOrderById } from '@/services/shippingCompanyOrderService';

export default function OrderSystemDebugPage() {
  const { toast } = useToast();
  const [orderId, setOrderId] = useState('');
  const [debugResults, setDebugResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Test order access
  const testOrderAccess = async () => {
    if (!orderId.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an order ID',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    setDebugResults(null);

    try {
      console.log('🔍 [DEBUG] Testing order access for:', orderId);

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      console.log('🔍 [DEBUG] Current user:', user);

      if (userError || !user) {
        setDebugResults({
          error: 'Not authenticated',
          user_error: userError
        });
        return;
      }

      // Test direct database query
      console.log('🔍 [DEBUG] Testing direct database query...');
      const { data: directOrder, error: directError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (*)
        `)
        .eq('id', orderId)
        .single();

      console.log('🔍 [DEBUG] Direct query result:', { directOrder, directError });

      // Test supplier service
      console.log('🔍 [DEBUG] Testing supplier service...');
      const supplierResult = await getSupplierOrderById(orderId);
      console.log('🔍 [DEBUG] Supplier service result:', supplierResult);

      // Test shipping service
      console.log('🔍 [DEBUG] Testing shipping service...');
      let shippingResult = null;
      try {
        shippingResult = await getShippingOrderById(orderId);
        console.log('🔍 [DEBUG] Shipping service result:', shippingResult);
      } catch (error) {
        console.log('🔍 [DEBUG] Shipping service error (expected if not shipping company):', error);
      }

      // Test order items query
      console.log('🔍 [DEBUG] Testing order items query...');
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select('*')
        .eq('order_id', orderId);

      console.log('🔍 [DEBUG] Order items query result:', { orderItems, itemsError });

      setDebugResults({
        user: {
          id: user.id,
          email: user.email
        },
        direct_query: {
          order: directOrder,
          error: directError
        },
        supplier_service: supplierResult,
        shipping_service: shippingResult,
        order_items: {
          items: orderItems,
          error: itemsError
        }
      });

    } catch (error) {
      console.error('❌ [DEBUG] Error during testing:', error);
      setDebugResults({
        error: 'Exception during testing',
        exception: error
      });
    } finally {
      setIsLoading(false);
    }
  };

  // List all orders
  const listAllOrders = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 [DEBUG] Listing all orders...');

      const { data: orders, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          status,
          created_at,
          order_items (
            id,
            supplier_account_id,
            product_name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      console.log('🔍 [DEBUG] All orders result:', { orders, error });

      setDebugResults({
        all_orders: {
          orders,
          error,
          count: orders?.length || 0
        }
      });

    } catch (error) {
      console.error('❌ [DEBUG] Error listing orders:', error);
      setDebugResults({
        error: 'Exception during listing',
        exception: error
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Order System Debug Tool</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="orderId">Order ID</Label>
                <Input
                  id="orderId"
                  value={orderId}
                  onChange={(e) => setOrderId(e.target.value)}
                  placeholder="Enter order ID to test"
                />
              </div>
              <div className="flex items-end gap-2">
                <Button 
                  onClick={testOrderAccess}
                  disabled={isLoading}
                >
                  Test Order Access
                </Button>
                <Button 
                  onClick={listAllOrders}
                  disabled={isLoading}
                  variant="outline"
                >
                  List All Orders
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {debugResults && (
          <Card>
            <CardHeader>
              <CardTitle>Debug Results</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
                {JSON.stringify(debugResults, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
