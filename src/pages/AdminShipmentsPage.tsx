import React from 'react';
import { AdminPanelLayout } from '@/components/layout/AdminPanelLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Construction } from 'lucide-react';

export default function AdminShipmentsPage() {
  return (
    <AdminPanelLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shipments Tracking</h1>
          <p className="text-gray-600 mt-1">
            Track all deliveries and shipment status
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Construction className="h-5 w-5 text-[#fa7b00]" />
              Coming Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <MapPin className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Shipments Tracking
              </h3>
              <p className="text-gray-600">
                This section will provide real-time tracking of all shipments, 
                delivery status updates, and logistics coordination.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}
