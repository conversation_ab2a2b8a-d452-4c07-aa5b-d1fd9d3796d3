/**
 * OTP Test Page - For testing the new OTP implementation
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { sendOTP, verifyOTP } from '@/services/dexatelService';
import { useToast } from '@/components/ui/use-toast';

export default function OTPTestPage() {
  const [phone, setPhone] = useState('+213770882600');
  const [code, setCode] = useState('');
  const [messageId, setMessageId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSendOTP = async () => {
    setIsLoading(true);
    try {
      const result = await sendOTP(phone);

      if (result.success) {
        setMessageId(result.data!.id);
        toast({
          title: "OTP Sent",
          description: `Verification code sent to ${phone}`,
        });
      } else {
        toast({
          title: "Failed to Send",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send OTP",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!messageId || !code) {
      toast({
        title: "Missing Information",
        description: "Please send OTP first and enter the code",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await verifyOTP(messageId, code);

      if (result.success) {
        toast({
          title: "Verification Successful",
          description: "OTP verified successfully!",
        });
      } else {
        toast({
          title: "Verification Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify OTP",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">OTP Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Phone Number</label>
            <Input
              type="tel"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              placeholder="+213770882600"
            />
          </div>
          
          <Button 
            onClick={handleSendOTP} 
            disabled={isLoading || !phone}
            className="w-full"
          >
            {isLoading ? 'Sending...' : 'Send OTP'}
          </Button>
          
          {messageId && (
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-sm text-green-800">
                OTP sent! Message ID: <code className="font-mono">{messageId}</code>
              </p>
            </div>
          )}
          
          <div>
            <label className="block text-sm font-medium mb-2">Verification Code</label>
            <Input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="Enter 6-digit code"
              maxLength={6}
            />
          </div>
          
          <Button 
            onClick={handleVerifyOTP} 
            disabled={isLoading || !messageId || !code}
            className="w-full"
            variant="outline"
          >
            {isLoading ? 'Verifying...' : 'Verify OTP'}
          </Button>
          
          <div className="text-xs text-gray-500 space-y-1">
            <p>• This is a test page for the new OTP implementation</p>
            <p>• Check your phone for the SMS with the verification code</p>
            <p>• The code expires in 5 minutes</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
