import React from 'react';
import { Link } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getProductsForSection } from '@/utils/centralizedProductData';

export default function ProductTestPage() {
  // Get sample products from centralized data source
  const sampleWholesaleTyres = getProductsForSection('wholesale', 'tyres').slice(0, 3);
  const sampleRetailTyres = getProductsForSection('retail', 'tyres').slice(0, 3);
  const sampleWholesaleBrakes = getProductsForSection('wholesale', 'brakes').slice(0, 2);
  const sampleRetailBrakes = getProductsForSection('retail', 'brakes').slice(0, 2);

  const allSampleProducts = [
    ...sampleWholesaleTyres,
    ...sampleRetailTyres,
    ...sampleWholesaleBrakes,
    ...sampleRetailBrakes
  ];

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Product Page Testing</h1>
          <p className="text-gray-600 mb-6">
            Click on any product ID below to test the product page functionality.
            The URL structure follows the pattern: /{'{'}ProductID{'}'} (e.g., /TYR-100001-WHOLESALE)
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {allSampleProducts.map((product) => (
            <Card key={product.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{product.name}</CardTitle>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{product.category}</Badge>
                  <Badge variant="secondary">{product.id}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm text-gray-600">
                    <strong>Manufacturer:</strong> {product.manufacturer}
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>SKU:</strong> {product.sku}
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>Stock:</strong> {product.stockQuantity} units
                  </div>

                  {/* Pricing Information */}
                  <div className="space-y-1">
                    {product.retailPrice && (
                      <div className="text-sm">
                        <strong>Retail Price:</strong> ${product.retailPrice.toFixed(2)}
                      </div>
                    )}
                    {product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0 && (
                      <div className="text-sm">
                        <strong>Wholesale Price:</strong> ${product.wholesalePricingTiers[0].price.toFixed(2)}
                      </div>
                    )}
                  </div>

                  {/* Category-specific info */}
                  {product.category === 'tyres' && 'width' in product && (
                    <div className="text-sm text-gray-600">
                      <strong>Size:</strong> {product.width}/{product.aspectRatio}R{product.rimDiameter}
                    </div>
                  )}

                  <div className="pt-3">
                    <Button asChild className="w-full">
                      <Link to={`/${product.id}`}>
                        View Product Page
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 p-6 bg-blue-50 rounded-lg">
          <h2 className="text-xl font-bold mb-4">Testing Instructions</h2>
          <div className="space-y-2 text-sm">
            <p>• Click on any "View Product Page" button to test the product page</p>
            <p>• The URL will change to /{'{'}ProductID{'}'} format (e.g., /TYR-100001-WHOLESALE)</p>
            <p>• The product page should display all sections: Overview, Specifications, Description, and Vehicle Compatibility</p>
            <p>• Products with IDs ending in -WHOLESALE should show wholesale pricing</p>
            <p>• Products with IDs ending in -RETAIL should show retail pricing</p>
            <p>• Test the back navigation and ensure the product is found correctly</p>
            <p>• Each product ID is now unique across marketplace sections</p>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg font-bold mb-4">Direct Product Links for Testing</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button asChild variant="outline">
              <Link to="/TYR-100000-WHOLESALE">TYR-100000-WHOLESALE</Link>
            </Button>
            <Button asChild variant="outline">
              <Link to="/TYR-100000-RETAIL">TYR-100000-RETAIL</Link>
            </Button>
            <Button asChild variant="outline">
              <Link to="/BRK-100000-WHOLESALE">BRK-100000-WHOLESALE</Link>
            </Button>
            <Button asChild variant="outline">
              <Link to="/BRK-100000-RETAIL">BRK-100000-RETAIL</Link>
            </Button>
          </div>
        </div>
      </div>
    </MarketplaceLayout>
  );
}
