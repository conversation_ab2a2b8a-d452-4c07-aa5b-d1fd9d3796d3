import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { 
  authenticateConsumerSimplified, 
  updateConsumerPasscode, 
  hasPasscodeSet,
  getCurrentConsumerSession,
  logoutConsumer,
  validatePasscodeFormat
} from '@/services/simplifiedConsumerAuth';
import { Shield, Lock, User, LogOut, CheckCircle, AlertCircle } from 'lucide-react';

export default function ConsumerPasscodeTest() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // Authentication test state
  const [phone, setPhone] = useState('');
  const [fullName, setFullName] = useState('');
  const [passcode, setPasscode] = useState('');
  const [authResult, setAuthResult] = useState<any>(null);
  
  // Passcode update test state
  const [currentPasscode, setCurrentPasscode] = useState('');
  const [newPasscode, setNewPasscode] = useState('');
  const [updateResult, setUpdateResult] = useState<any>(null);
  
  // Status checks
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [hasPasscode, setHasPasscode] = useState<boolean | null>(null);

  const handleAuthTest = async () => {
    setIsLoading(true);
    setAuthResult(null);
    
    try {
      const fullPhone = `+213${phone.replace(/\D/g, '')}`;
      
      const request: any = {
        fullName: fullName.trim(),
        phone: fullPhone
      };
      
      if (passcode) {
        request.passcode = passcode;
      }
      
      console.log('🧪 Testing authentication with:', request);
      
      const result = await authenticateConsumerSimplified(request);
      setAuthResult(result);
      
      if (result.success) {
        toast({
          title: "Authentication Successful!",
          description: `Action: ${result.action}`,
        });
        
        // Refresh session info
        await checkSessionInfo();
      } else {
        toast({
          title: "Authentication Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Auth test error:', error);
      setAuthResult({ success: false, error: String(error) });
      toast({
        title: "Test Error",
        description: String(error),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasscodeUpdate = async () => {
    setIsLoading(true);
    setUpdateResult(null);
    
    try {
      const request: any = {
        newPasscode: newPasscode
      };
      
      if (currentPasscode) {
        request.currentPasscode = currentPasscode;
      }
      
      console.log('🧪 Testing passcode update');
      
      const result = await updateConsumerPasscode(request);
      setUpdateResult(result);
      
      if (result.success) {
        toast({
          title: "Passcode Updated!",
          description: "Your passcode has been updated successfully.",
        });
        
        // Clear form
        setCurrentPasscode('');
        setNewPasscode('');
        
        // Refresh status
        await checkPasscodeStatus();
      } else {
        toast({
          title: "Update Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Passcode update test error:', error);
      setUpdateResult({ success: false, error: String(error) });
      toast({
        title: "Test Error",
        description: String(error),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkSessionInfo = async () => {
    try {
      const session = getCurrentConsumerSession();
      setSessionInfo(session);
      
      if (session) {
        await checkPasscodeStatus();
      }
    } catch (error) {
      console.error('Session check error:', error);
      setSessionInfo({ error: String(error) });
    }
  };

  const checkPasscodeStatus = async () => {
    try {
      const hasPasscodeResult = await hasPasscodeSet();
      setHasPasscode(hasPasscodeResult);
    } catch (error) {
      console.error('Passcode status check error:', error);
      setHasPasscode(null);
    }
  };

  const handleLogout = () => {
    logoutConsumer();
    setSessionInfo(null);
    setHasPasscode(null);
    setAuthResult(null);
    setUpdateResult(null);
    
    toast({
      title: "Logged Out",
      description: "Consumer session cleared.",
    });
  };

  const validatePasscode = (value: string) => {
    const validation = validatePasscodeFormat(value);
    return validation;
  };

  React.useEffect(() => {
    checkSessionInfo();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Consumer Passcode System Test</h1>
        <p className="text-gray-600">Test the enhanced consumer authentication with passcode support</p>
      </div>

      {/* Session Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Current Session Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={checkSessionInfo} variant="outline" size="sm">
              Refresh Status
            </Button>
            {sessionInfo && (
              <Button onClick={handleLogout} variant="destructive" size="sm">
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            )}
          </div>
          
          {sessionInfo ? (
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="font-medium text-green-800">✅ Logged In</p>
              <p className="text-sm text-green-600">User ID: {sessionInfo.user?.id}</p>
              <p className="text-sm text-green-600">Phone: {sessionInfo.user?.phone}</p>
              <p className="text-sm text-green-600">Name: {sessionInfo.profile?.full_name}</p>
              <p className="text-sm text-green-600">
                Has Passcode: {hasPasscode === true ? '✅ Yes' : hasPasscode === false ? '❌ No' : '⏳ Checking...'}
              </p>
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-600">❌ Not logged in</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Authentication Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Authentication Test
          </CardTitle>
          <CardDescription>
            Test consumer authentication with optional passcode
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Ahmed Benali"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone (9 digits)</Label>
              <Input
                id="phone"
                value={phone}
                onChange={(e) => setPhone(e.target.value.replace(/\D/g, '').slice(0, 9))}
                placeholder="123456789"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="passcode">Passcode (Optional, 4-6 digits)</Label>
            <Input
              id="passcode"
              type="password"
              value={passcode}
              onChange={(e) => setPasscode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="1234"
            />
            {passcode && (
              <p className={`text-xs mt-1 ${validatePasscode(passcode).isValid ? 'text-green-600' : 'text-red-600'}`}>
                {validatePasscode(passcode).isValid ? '✅ Valid format' : `❌ ${validatePasscode(passcode).error}`}
              </p>
            )}
          </div>
          
          <Button 
            onClick={handleAuthTest} 
            disabled={isLoading || !fullName || !phone}
            className="w-full"
          >
            {isLoading ? 'Testing...' : 'Test Authentication'}
          </Button>
          
          {authResult && (
            <div className={`p-4 rounded-lg ${authResult.success ? 'bg-green-50' : 'bg-red-50'}`}>
              <div className="flex items-center gap-2 mb-2">
                {authResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                <span className={`font-medium ${authResult.success ? 'text-green-800' : 'text-red-800'}`}>
                  {authResult.success ? 'Success' : 'Failed'}
                </span>
              </div>
              <pre className="text-xs bg-white p-2 rounded border overflow-auto">
                {JSON.stringify(authResult, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Passcode Update Test */}
      {sessionInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Passcode Update Test
            </CardTitle>
            <CardDescription>
              Test updating passcode for logged-in user
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {hasPasscode && (
              <div>
                <Label htmlFor="currentPasscode">Current Passcode</Label>
                <Input
                  id="currentPasscode"
                  type="password"
                  value={currentPasscode}
                  onChange={(e) => setCurrentPasscode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="Current passcode"
                />
              </div>
            )}
            
            <div>
              <Label htmlFor="newPasscode">New Passcode (4-6 digits)</Label>
              <Input
                id="newPasscode"
                type="password"
                value={newPasscode}
                onChange={(e) => setNewPasscode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="New passcode"
              />
              {newPasscode && (
                <p className={`text-xs mt-1 ${validatePasscode(newPasscode).isValid ? 'text-green-600' : 'text-red-600'}`}>
                  {validatePasscode(newPasscode).isValid ? '✅ Valid format' : `❌ ${validatePasscode(newPasscode).error}`}
                </p>
              )}
            </div>
            
            <Button 
              onClick={handlePasscodeUpdate} 
              disabled={isLoading || !newPasscode || !validatePasscode(newPasscode).isValid}
              className="w-full"
            >
              {isLoading ? 'Updating...' : 'Update Passcode'}
            </Button>
            
            {updateResult && (
              <div className={`p-4 rounded-lg ${updateResult.success ? 'bg-green-50' : 'bg-red-50'}`}>
                <div className="flex items-center gap-2 mb-2">
                  {updateResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span className={`font-medium ${updateResult.success ? 'text-green-800' : 'text-red-800'}`}>
                    {updateResult.success ? 'Success' : 'Failed'}
                  </span>
                </div>
                <pre className="text-xs bg-white p-2 rounded border overflow-auto">
                  {JSON.stringify(updateResult, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
