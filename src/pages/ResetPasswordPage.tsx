import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ResetPasswordForm } from '@/components/auth/ResetPasswordForm';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/services/authService';

const ResetPasswordPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isValidSession, setIsValidSession] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkSession = async () => {
      try {
        // Check if there's a valid session for password reset
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error checking session:', error);
          setError(t('auth.resetPasswordFailed'));
          setIsLoading(false);
          return;
        }

        // If we have a valid session, allow password reset
        if (data.session) {
          setIsValidSession(true);
          setIsLoading(false);
          return;
        }

        // Try to exchange the hash for a session (for password reset flow)
        if (window.location.hash) {
          try {
            const { data: hashData, error: hashError } = await supabase.auth.exchangeCodeForSession(
              window.location.hash.substring(1)
            );

            if (hashError) {
              console.error('Error exchanging hash for session:', hashError);
              setError(t('auth.linkExpired'));
              setIsLoading(false);
              return;
            }

            if (hashData.session) {
              setIsValidSession(true);
              setIsLoading(false);
              return;
            }
          } catch (err) {
            console.error('Error processing hash:', err);
            setError(t('auth.unexpectedError'));
            setIsLoading(false);
            return;
          }
        }

        // If we get here, there's no valid session or hash
        setError(t('auth.invalidResetLink'));
        setIsLoading(false);
      } catch (err) {
        console.error('Unexpected error in reset password page:', err);
        setError(t('auth.unexpectedError'));
        setIsLoading(false);
      }
    };

    checkSession();
  }, [t]);

  const handleComplete = () => {
    // Redirect to partners page after successful password reset
    navigate('/partners');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-12 w-12 text-[#fa7b00] animate-spin mb-4" />
            <p className="text-gray-600">{t('auth.loading')}</p>
          </div>
        ) : isValidSession ? (
          <>
            <h1 className="text-2xl font-bold text-center mb-6">{t('auth.resetPassword')}</h1>
            <ResetPasswordForm onComplete={handleComplete} />
          </>
        ) : (
          <div className="text-center py-8">
            <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4">
              <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h1 className="text-xl font-semibold text-gray-800 mb-2">
              {t('auth.resetPasswordFailed')}
            </h1>
            <p className="text-gray-600 mb-6">
              {error || t('auth.unexpectedError')}
            </p>
            <button
              onClick={() => navigate('/partners')}
              className="px-6 py-2 bg-[#fa7b00] text-white rounded-lg hover:bg-[#fa7b00]/90 transition-colors"
            >
              {t('auth.returnToPartners')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResetPasswordPage;
