import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Plus, FileUp, FileDown, Grid, Settings, Filter } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Category } from '@/types';
import { toast } from 'sonner';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProductTanStackTable } from '@/components/products/ProductTanStackTable';

// Updated mock categories data without image
const mockCategories: Category[] = [
  {
    id: '1',
    name: 'Tyres & Related Products',
    description: 'Tires, wheel covers, and related accessories',
    slug: 'tyres-related-products',
    productCount: 24,
    isDefault: true
  }
];

// Define all columns for tyres category
const tyresCoreColumns = [
  "id", "name", "sku", "barcode", "category", "subcategory",
  "manufacturer", "supplierName", "description", "primaryImage", "additionalImages"
];

// Default columns for each category
const categoryColumns = {
  "all": ["id", "name", "sku", "partArticleNumber", "category", "subcategory", "manufacturer",
          "supplierName", "descriptionAndSpecifications", "primaryImage", "additionalImages",
          "price", "stockQuantity", "status", "actions"],
  "1": tyresCoreColumns.concat(["status", "actions"]) // Include status and actions
};

export default function ProductsDataTablePage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [visibleColumns, setVisibleColumns] = useState<string[]>(categoryColumns.all);

  // Update visible columns when category changes
  useEffect(() => {
    const newColumns = categoryColumns[activeCategory as keyof typeof categoryColumns] || categoryColumns.all;
    setVisibleColumns(newColumns);
  }, [activeCategory]);

  // Handle column visibility change
  const handleColumnVisibilityChange = (column: string, isVisible: boolean) => {
    if (isVisible) {
      setVisibleColumns(prev => [...prev, column]);
    } else {
      setVisibleColumns(prev => prev.filter(col => col !== column));
    }
  };

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('productsDataGrid.productLibrary')}</h1>
          <p className="text-muted-foreground mt-1">
            {t('productsDataGrid.manageProductCatalog')}
          </p>
        </div>

        <div className="overflow-hidden">
          <Tabs
            defaultValue="all"
            value={activeCategory}
            onValueChange={setActiveCategory}
            className="w-full"
          >
            <div className="border-b border-[#e1e1e1] mb-2">
              <TabsList className="bg-transparent h-auto p-0 w-full flex justify-start gap-4 overflow-x-auto">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-electric-orange data-[state=active]:bg-transparent data-[state=active]:shadow-none px-5 py-3 h-12 rounded-t-md data-[state=active]:text-electric-orange transition-all"
                >
                  <Grid className="w-4 h-4 mr-2" />
                  <span className="font-medium">{t('categories.allProducts')}</span>
                </TabsTrigger>

                {categories.map(category => (
                  <TabsTrigger
                    key={category.id}
                    value={category.id}
                    className="data-[state=active]:border-b-2 data-[state=active]:border-electric-orange data-[state=active]:bg-transparent data-[state=active]:shadow-none px-5 py-2 h-auto rounded-t-md data-[state=active]:text-electric-orange transition-all"
                  >
                    <span className="text-electric-orange font-medium">{category.name}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
          </Tabs>
        </div>

        {/* Removed the duplicate search and filter controls that were here */}

        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            className="gap-2 border-electric-orange text-electric-orange hover:bg-electric-orange/10"
            onClick={() => {}}
          >
            <FileUp className="h-4 w-4" />
            {t('products.import')}
          </Button>

          <Button
            variant="outline"
            className="gap-2 border-electric-orange text-electric-orange hover:bg-electric-orange/10"
            onClick={() => {}}
          >
            <FileDown className="h-4 w-4" />
            {t('products.export')}
          </Button>
        </div>

        <ProductTanStackTable
          activeCategory={activeCategory}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
        />
      </div>
    </AdminLayout>
  );
}
