
import { useParams, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { ProductForm } from '@/components/products/ProductForm';
import { Product } from '@/types';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { ScanBarcode } from 'lucide-react';
import { BarcodeScanner } from '@/components/products/BarcodeScanner';
import { useTranslation } from 'react-i18next';

// Mock data
const productData: Record<string, Product> = {
  '1': {
    id: '1',
    name: 'Premium Brake Pad Set',
    description: 'High-performance ceramic brake pads for optimal stopping power and reduced dust.',
    sku: 'BP-2023-001',
    partArticleNumber: '123456789012',
    category: 'brakes',
    price: 89.99,
    stockQuantity: 42,
    images: ['/placeholder.svg'],
    specifications: {
      'Material': 'Ceramic',
      'Position': 'Front',
      'Warranty': '2 years'
    },
    manufacturer: 'BrakeMaster',
    compatibleVehicles: ['Toyota Corolla 2018-2022', 'Honda Civic 2019-2023'],
    status: 'published',
    createdAt: new Date('2023-05-15'),
    updatedAt: new Date('2023-06-02')
  }
};

export default function ProductFormPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [productFormData, setProductFormData] = useState<Partial<Product> | undefined>(
    id && id !== 'new' ? productData[id] : undefined
  );

  // In a real app, you would fetch the product data from an API
  const productToEdit = id && id !== 'new' ? productData[id] : undefined;

  const isEditMode = !!productToEdit;

  const handleSubmit = (data: Partial<Product>) => {
    // In a real app, this would be an API call
    console.log('Submitting product data:', data);

    // Show success message
    toast.success(
      isEditMode
        ? t('products.productUpdated', 'Product updated successfully!')
        : t('products.productCreated', 'Product created successfully!')
    );

    // Redirect to products page
    navigate('/products');
  };

  const handleCancel = () => {
    navigate('/products');
  };

  const handleBarcodeScan = (partArticleNumber: string) => {
    console.log('Part article number scanned:', partArticleNumber);

    setProductFormData(prev => ({
      ...prev,
      partArticleNumber
    }));

    toast.success(t('products.barcodeScanned', 'Part article number scanned successfully!'));
  };

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {isEditMode ? t('products.editProduct') : t('products.addProduct')}
            </h1>
            <p className="text-muted-foreground mt-1">
              {isEditMode
                ? t('products.editProductDescription', 'Make changes to the product information')
                : t('products.addProductDescription', 'Fill in the details to create a new product')}
            </p>
          </div>

          {!isEditMode && (
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => setIsScannerOpen(true)}
            >
              <ScanBarcode className="h-4 w-4" />
              {t('products.scanBarcode')}
            </Button>
          )}
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <ProductForm
            initialData={productFormData || productToEdit}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      </div>

      <BarcodeScanner
        isOpen={isScannerOpen}
        onClose={() => setIsScannerOpen(false)}
        onScan={handleBarcodeScan}
      />
    </AdminLayout>
  );
}
