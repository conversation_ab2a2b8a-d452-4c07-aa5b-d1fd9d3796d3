
import { useState, useEffect, useMemo } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { EnhancedProductFilters } from '@/components/products/EnhancedProductFilters';
import { ProductsGrid } from '@/components/products/ProductsGrid';
import { BulkActionsBar } from '@/components/products/BulkActionsBar';
import { ShareProductsDialog } from '@/components/products/ShareProductsDialog';
import { ProductEditDialog } from '@/features/products/components/ProductEditDialog';
import { Button } from '@/components/ui/button';
import {
  Grid,
  List,
  Package,
  RefreshCw,
  Download,
  Upload,
  SlidersHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { PhotoProvider } from 'react-photo-view';
import 'react-photo-view/dist/react-photo-view.css';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProducts } from '@/features/products/hooks/useProductsFactory';
import { TyreProduct, BrakeProduct, ProductStatus } from '@/features/products/types/product.types';
import { toast } from 'sonner';

// Type for combined product data
type AnyProduct = TyreProduct | BrakeProduct;

// Product filters interface
interface ProductFilters {
  search?: string;
  category?: string;
  status?: ProductStatus;
  manufacturer?: string;
  priceRange?: [number, number];
  inStock?: boolean;
}

export default function ProductsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<string | null>(null);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [productToEdit, setProductToEdit] = useState<AnyProduct | null>(null);
  const [filters, setFilters] = useState<ProductFilters>({});
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);

  // Fetch products from all categories using Supabase-integrated hooks
  const tyresHook = useProducts('tyres');
  const brakesHook = useProducts('brakes');

  // Combine products from all categories
  const allProducts = useMemo(() => {
    const combined: AnyProduct[] = [
      ...tyresHook.products,
      ...brakesHook.products,
    ];
    return combined;
  }, [tyresHook.products, brakesHook.products]);

  // Check if any category is loading
  const isLoading = tyresHook.isLoading || brakesHook.isLoading;

  // Apply filters to products
  const filteredProducts = useMemo(() => {
    let filtered = allProducts;

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.sku.toLowerCase().includes(searchTerm) ||
        product.manufacturer?.toLowerCase().includes(searchTerm)
      );
    }

    // Category filter
    if (filters.category && filters.category !== 'all') {
      filtered = filtered.filter(product => product.category === filters.category);
    }

    // Status filter
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(product => product.status === filters.status);
    }

    // Manufacturer filter
    if (filters.manufacturer && filters.manufacturer !== 'all') {
      filtered = filtered.filter(product => product.manufacturer === filters.manufacturer);
    }

    // Price range filter
    if (filters.priceRange) {
      const [min, max] = filters.priceRange;
      filtered = filtered.filter(product => {
        const price = product.retailPrice || 0;
        return price >= min && price <= max;
      });
    }

    // In stock filter
    if (filters.inStock !== undefined) {
      filtered = filtered.filter(product => {
        return filters.inStock ? product.stockQuantity > 0 : true;
      });
    }

    return filtered;
  }, [allProducts, filters]);

  // Get unique manufacturers for filter dropdown
  const manufacturers = useMemo(() => {
    const uniqueManufacturers = new Set<string>();
    allProducts.forEach(product => {
      if (product.manufacturer) {
        uniqueManufacturers.add(product.manufacturer);
      }
    });
    return Array.from(uniqueManufacturers).sort();
  }, [allProducts]);

  // Get max price for price range filter
  const maxPrice = useMemo(() => {
    let max = 0;
    allProducts.forEach(product => {
      const price = product.retailPrice || 0;
      if (price > max) {
        max = price;
      }
    });
    return Math.ceil(max / 1000) * 1000; // Round up to nearest thousand
  }, [allProducts]);

  // Refetch all products
  const refetch = () => {
    tyresHook.refetch();
    brakesHook.refetch();
  };

  // Update filters
  const updateFilters = (newFilters: Partial<ProductFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({});
  };

  // Product selection handlers
  const toggleProductSelection = (productId: string) => {
    setSelectedProductIds(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const selectAllProducts = () => {
    setSelectedProductIds(filteredProducts.map(p => p.id));
  };

  const deselectAllProducts = () => {
    setSelectedProductIds([]);
  };

  // Handle edit product
  const handleEditProduct = (product: AnyProduct) => {
    setProductToEdit(product);
    setIsEditDialogOpen(true);
  };

  // Handle save edited product
  const handleSaveEditedProduct = async (updatedProduct: AnyProduct) => {
    try {
      // Find which category the product belongs to and update using appropriate hook
      if (updatedProduct.category === 'tyres') {
        await tyresHook.updateProduct(updatedProduct.id, updatedProduct);
      } else if (updatedProduct.category === 'brakes') {
        await brakesHook.updateProduct(updatedProduct.id, updatedProduct);
      }

      setIsEditDialogOpen(false);
      setProductToEdit(null);
      toast.success(t('products.updateSuccess'));
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error(t('products.updateError'));
    }
  };

  // Handle delete product
  const deleteProduct = async (productId: string) => {
    try {
      // Find which category the product belongs to and delete from appropriate hook
      const product = allProducts.find(p => p.id === productId);
      if (!product) return;

      if (product.category === 'tyres') {
        await tyresHook.deleteProduct(productId);
      } else if (product.category === 'brakes') {
        await brakesHook.deleteProduct(productId);
      }

      // Remove from selected products
      setSelectedProductIds(prev => prev.filter(id => id !== productId));
      toast.success(t('products.deleteSuccess'));
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error(t('products.deleteError'));
    }
  };

  // Handle bulk delete
  const deleteProducts = async (productIds: string[]) => {
    try {
      // Group products by category for efficient deletion
      const tyreIds = productIds.filter(id => {
        const product = allProducts.find(p => p.id === id);
        return product?.category === 'tyres';
      });

      const brakeIds = productIds.filter(id => {
        const product = allProducts.find(p => p.id === id);
        return product?.category === 'brakes';
      });

      // Delete from each category
      if (tyreIds.length > 0) {
        await tyresHook.deleteProducts(tyreIds);
      }
      if (brakeIds.length > 0) {
        await brakesHook.deleteProducts(brakeIds);
      }

      // Clear selection
      setSelectedProductIds([]);
      toast.success(t('products.bulkDeleteSuccess', { count: productIds.length }));
    } catch (error) {
      console.error('Error deleting products:', error);
      toast.error(t('products.bulkDeleteError'));
    }
  };

  // Handle status update with proper stock quantity logic
  const updateProductsStatus = async (productIds: string[], status: ProductStatus) => {
    try {
      // Group products by category for efficient updates
      const tyreIds = productIds.filter(id => {
        const product = allProducts.find(p => p.id === id);
        return product?.category === 'tyres';
      });

      const brakeIds = productIds.filter(id => {
        const product = allProducts.find(p => p.id === id);
        return product?.category === 'brakes';
      });

      // Update status for each category with proper stock logic
      if (tyreIds.length > 0) {
        await Promise.all(tyreIds.map(id => {
          const product = allProducts.find(p => p.id === id);
          const updates: Partial<AnyProduct> = { status };

          // Only set stock to 0 when status is 'out_of_stock'
          // For all other statuses (active, draft, discontinued), preserve existing stock
          if (status === 'out_of_stock') {
            updates.stockQuantity = 0;
          }

          return tyresHook.updateProduct(id, updates);
        }));
      }

      if (brakeIds.length > 0) {
        await Promise.all(brakeIds.map(id => {
          const product = allProducts.find(p => p.id === id);
          const updates: Partial<AnyProduct> = { status };

          // Only set stock to 0 when status is 'out_of_stock'
          // For all other statuses (active, draft, discontinued), preserve existing stock
          if (status === 'out_of_stock') {
            updates.stockQuantity = 0;
          }

          return brakesHook.updateProduct(id, updates);
        }));
      }

      toast.success(t('products.statusUpdateSuccess', { count: productIds.length }));
    } catch (error) {
      console.error('Error updating product status:', error);
      toast.error(t('products.statusUpdateError'));
    }
  };

  // Handle delete product
  const handleDeleteProduct = (productId: string) => {
    setProductToDelete(productId);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete product
  const confirmDeleteProduct = () => {
    if (productToDelete) {
      deleteProduct(productToDelete);
      setProductToDelete(null);
    }
    setIsDeleteDialogOpen(false);
  };

  // Handle duplicate product
  const handleDuplicateProduct = (product: AnyProduct) => {
    toast.info(t('products.duplicateNotImplemented'));
  };

  // Handle feature product
  const handleFeatureProduct = (product: AnyProduct) => {
    toast.info(t('products.featureNotImplemented'));
  };

  // Handle export products
  const handleExportProducts = () => {
    // If products are selected, export only those
    const productsToExport = selectedProductIds.length > 0
      ? filteredProducts.filter(p => selectedProductIds.includes(p.id))
      : filteredProducts;

    // Create CSV content
    const headers = ['ID', 'Name', 'SKU', 'Category', 'Price', 'Stock', 'Status', 'Manufacturer'];
    const csvContent = [
      headers.join(','),
      ...productsToExport.map(p => [
        p.id,
        `"${p.name.replace(/"/g, '""')}"`, // Escape quotes in CSV
        p.sku,
        p.category,
        p.retailPrice || 0,
        p.stockQuantity,
        p.status,
        p.manufacturer
      ].join(','))
    ].join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `products-export-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(t('products.exportSuccess', { count: productsToExport.length }));
  };

  // Handle share by email
  const handleShareByEmail = () => {
    setIsShareDialogOpen(true);
  };

  // Get selected products
  const selectedProducts = useMemo(() => {
    return filteredProducts.filter(p => selectedProductIds.includes(p.id));
  }, [filteredProducts, selectedProductIds]);

  return (
    <AdminLayout>
      <PhotoProvider>
        <div className="flex flex-col gap-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{t('products.products')}</h1>
              <p className="text-muted-foreground mt-1">
                {t('products.manageProductCatalog')}
              </p>
            </div>
            <div className="flex gap-3">
              <div className="flex items-center bg-white rounded-md border">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="icon"
                  onClick={() => setViewMode('grid')}
                  className={viewMode === 'grid' ? 'bg-midnight-blue text-white' : ''}
                >
                  <Grid className="h-4 w-4" />
                  <span className="sr-only">{t('products.gridView')}</span>
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="icon"
                  onClick={() => setViewMode('list')}
                  className={viewMode === 'list' ? 'bg-midnight-blue text-white' : ''}
                >
                  <List className="h-4 w-4" />
                  <span className="sr-only">{t('products.listView')}</span>
                </Button>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <SlidersHorizontal className="h-4 w-4" />
                    {t('products.actions')}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate('/app/products-table/tyres')}>
                    <Grid className="mr-2 h-4 w-4" />
                    {t('products.goToDataGrid')}
                  </DropdownMenuItem>

                  <DropdownMenuItem onClick={() => refetch()}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    {t('products.refreshProducts')}
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  <DropdownMenuItem onClick={handleExportProducts}>
                    <Download className="mr-2 h-4 w-4" />
                    {t('products.exportProducts')}
                  </DropdownMenuItem>

                  <DropdownMenuItem onClick={() => toast.info(t('products.importNotImplemented'))}>
                    <Upload className="mr-2 h-4 w-4" />
                    {t('products.importProducts')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>


            </div>
          </div>

          <EnhancedProductFilters
            filters={filters}
            onFilterChange={updateFilters}
            onResetFilters={resetFilters}
            manufacturers={manufacturers}
            maxPrice={maxPrice}
          />

          <ProductsGrid
            products={filteredProducts}
            isLoading={isLoading}
            viewMode={viewMode}
            selectedProductIds={selectedProductIds}
            onSelectProduct={toggleProductSelection}
            onEditProduct={handleEditProduct}
            onDeleteProduct={handleDeleteProduct}
            onDuplicateProduct={handleDuplicateProduct}
            onFeatureProduct={handleFeatureProduct}
          />

          {/* Delete Confirmation Dialog */}
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('products.confirmDelete')}</DialogTitle>
                <DialogDescription>
                  {t('products.deleteConfirmation')}
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                  {t('actions.cancel')}
                </Button>
                <Button variant="destructive" onClick={confirmDeleteProduct}>
                  {t('products.delete')}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Share Products Dialog */}
          <ShareProductsDialog
            isOpen={isShareDialogOpen}
            onClose={() => setIsShareDialogOpen(false)}
            products={selectedProducts}
          />

          {/* Edit Product Dialog */}
          {isEditDialogOpen && productToEdit && (
            <ProductEditDialog
              isOpen={isEditDialogOpen}
              onClose={() => {
                setIsEditDialogOpen(false);
                setProductToEdit(null);
              }}
              onSave={handleSaveEditedProduct}
              product={productToEdit as any}
              categories={[
                { id: 'tyres', name: t('categories.tyres') },
                { id: 'brakes', name: t('categories.brakes') },
              ]}
            />
          )}

          {/* Bulk Actions Bar */}
          {selectedProductIds.length > 0 && (
            <BulkActionsBar
              selectedCount={selectedProductIds.length}
              onClearSelection={deselectAllProducts}
              onDelete={() => deleteProducts(selectedProductIds)}
              onUpdateStatus={(status: ProductStatus) => updateProductsStatus(selectedProductIds, status)}
              onExport={handleExportProducts}
              onShareByEmail={handleShareByEmail}
            />
          )}
        </div>
      </PhotoProvider>
    </AdminLayout>
  );
}
