import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the types for our filters
export interface TyresFilters {
  width: string;
  aspectRatio: string;
  rimDiameter: string;
  season: string;
  subcategory: string;
}

export interface PartsFilters {
  brand: string;
  model: string;
  engine: string;
  subcategory: string;
}

// Define the context type
interface FilterContextType {
  // Tyres filters
  tyresFilters: TyresFilters;
  setTyresFilters: (filters: TyresFilters) => void;

  // Parts filters
  partsFilters: PartsFilters;
  setPartsFilters: (filters: PartsFilters) => void;

  // Active section
  activeSection: 'tyres' | 'parts';
  setActiveSection: (section: 'tyres' | 'parts') => void;

  // Category and subcategory
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  selectedSubcategory: string;
  setSelectedSubcategory: (subcategory: string) => void;

  // Filter counts
  tyresFilterCount: number;
  partsFilterCount: number;

  // Modal states
  showTyresModal: boolean;
  setShowTyresModal: (show: boolean) => void;
  showPartsModal: boolean;
  setShowPartsModal: (show: boolean) => void;



  // Helper functions
  resetTyresFilters: () => void;
  resetPartsFilters: () => void;
  resetAllFilters: () => void;
}

// Create the context with default values
const FilterContext = createContext<FilterContextType | undefined>(undefined);

// Provider component
export const FilterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Tyres filter state
  const [tyresFilters, setTyresFilters] = useState<TyresFilters>({
    width: '',
    aspectRatio: '',
    rimDiameter: '',
    season: '',
    subcategory: ''
  });

  // Vehicle parts filter state
  const [partsFilters, setPartsFilters] = useState<PartsFilters>({
    brand: '',
    model: '',
    engine: '',
    subcategory: ''
  });

  // Active section state
  const [activeSection, setActiveSection] = useState<'tyres' | 'parts'>('tyres');

  // Category and subcategory state
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('');

  // Modal states
  const [showTyresModal, setShowTyresModal] = useState(false);
  const [showPartsModal, setShowPartsModal] = useState(false);

  // Count of applied filters - show "1" when any filter is applied, "0" when all are empty
  const tyresFilterCount = Object.values(tyresFilters).some(value => value !== '') ? 1 : 0;
  const partsFilterCount = Object.values(partsFilters).some(value => value !== '') ? 1 : 0;

  // Helper functions
  const resetTyresFilters = () => {
    setTyresFilters({
      width: '',
      aspectRatio: '',
      rimDiameter: '',
      season: '',
      subcategory: ''
    });
  };

  const resetPartsFilters = () => {
    setPartsFilters({
      brand: '',
      model: '',
      engine: '',
      subcategory: ''
    });
  };

  const resetAllFilters = () => {
    resetTyresFilters();
    resetPartsFilters();
    setSelectedCategory('all');
    setSelectedSubcategory('');
  };

  // Create the context value
  const value = {
    tyresFilters,
    setTyresFilters,
    partsFilters,
    setPartsFilters,
    activeSection,
    setActiveSection,
    selectedCategory,
    setSelectedCategory,
    selectedSubcategory,
    setSelectedSubcategory,
    tyresFilterCount,
    partsFilterCount,
    showTyresModal,
    setShowTyresModal,
    showPartsModal,
    setShowPartsModal,
    resetTyresFilters,
    resetPartsFilters,
    resetAllFilters
  };

  return (
    <FilterContext.Provider value={value}>
      {children}
    </FilterContext.Provider>
  );
};

// Custom hook to use the filter context
export const useFilter = () => {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilter must be used within a FilterProvider');
  }
  return context;
};
