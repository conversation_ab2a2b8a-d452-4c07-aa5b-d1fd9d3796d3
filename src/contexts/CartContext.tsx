import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { PricingTier } from '@/features/products/types/product.types';
import { calculatePriceForQuantity, getTierForQuantity } from '@/utils/pricingTierUtils';

// Define cart item type with complete product metadata
export interface CartItem {
  id: number;
  name: string;
  price: number; // This will be the base/initial price, but actual price is calculated dynamically for wholesale
  quantity: number;
  image?: string;
  // Additional product metadata for proper persistence
  category: string;
  subcategory?: string;
  manufacturer?: string;
  brand?: string;
  partArticleNumber?: string;
  supplierName?: string;
  supplierAccountId?: string; // CRITICAL: Supplier account ID for shipping companies
  originalProductId: string; // Store the original string ID from product
  // Shipping & Location Information
  shippingOrigin?: string; // CRITICAL: Supplier location (wilaya) for shipping origins display
  // Wholesale pricing support
  marketplaceSection?: 'wholesale' | 'retail';
  wholesalePricingTiers?: PricingTier[];
  retailPrice?: number;
}

// Define cart context type
interface CartContextType {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (itemId: number) => void;
  updateQuantity: (itemId: number, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  totalPrice: number;
  // Helper functions for dynamic pricing
  getItemPrice: (item: CartItem) => number;
  getItemTier: (item: CartItem) => PricingTier | null;
  getItemTotal: (item: CartItem) => number;
}

// localStorage key for cart persistence
const CART_STORAGE_KEY = 'arouz_market_cart';

// Helper functions for localStorage operations
const loadCartFromStorage = (): CartItem[] => {
  try {
    const storedCart = localStorage.getItem(CART_STORAGE_KEY);
    if (storedCart) {
      const parsedCart = JSON.parse(storedCart);
      // Validate that the stored data has the expected structure
      if (Array.isArray(parsedCart)) {
        return parsedCart.filter(item =>
          item &&
          typeof item.id === 'number' &&
          typeof item.name === 'string' &&
          typeof item.price === 'number' &&
          typeof item.quantity === 'number'
        );
      }
    }
  } catch (error) {
    console.error('Error loading cart from localStorage:', error);
  }
  return [];
};

const saveCartToStorage = (items: CartItem[]) => {
  try {
    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items));
  } catch (error) {
    console.error('Error saving cart to localStorage:', error);
  }
};

// Create context with default values
const CartContext = createContext<CartContextType>({
  items: [],
  addItem: () => {},
  removeItem: () => {},
  updateQuantity: () => {},
  clearCart: () => {},
  totalItems: 0,
  totalPrice: 0,
  getItemPrice: () => 0,
  getItemTier: () => null,
  getItemTotal: () => 0,
});

// Create provider component
export function CartProvider({ children }: { children: ReactNode }) {
  // Initialize cart from localStorage, no mock data
  const [items, setItems] = useState<CartItem[]>([]);

  // Load cart from localStorage on component mount
  useEffect(() => {
    const storedItems = loadCartFromStorage();
    if (storedItems.length > 0) {
      setItems(storedItems);
      console.log('🛒 [CART_PERSISTENCE] Loaded cart from localStorage:', storedItems);
    }
  }, []);

  // Save cart to localStorage whenever items change
  useEffect(() => {
    if (items.length > 0) {
      saveCartToStorage(items);
      console.log('🛒 [CART_PERSISTENCE] Saved cart to localStorage:', items);
    } else {
      // Clear localStorage when cart is empty
      localStorage.removeItem(CART_STORAGE_KEY);
      console.log('🛒 [CART_PERSISTENCE] Cleared cart from localStorage');
    }
  }, [items]);

  // Helper function to get current price for an item (considering wholesale tiers)
  const getItemPrice = (item: CartItem): number => {
    if (item.marketplaceSection === 'wholesale' && item.wholesalePricingTiers) {
      return calculatePriceForQuantity(item.quantity, item.wholesalePricingTiers);
    }
    return item.retailPrice || item.price;
  };

  // Helper function to get current tier for an item
  const getItemTier = (item: CartItem): PricingTier | null => {
    if (item.marketplaceSection === 'wholesale' && item.wholesalePricingTiers) {
      return getTierForQuantity(item.quantity, item.wholesalePricingTiers);
    }
    return null;
  };

  // Helper function to get total price for an item
  const getItemTotal = (item: CartItem): number => {
    return getItemPrice(item) * item.quantity;
  };

  // Calculate total items
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);

  // Calculate total price using dynamic pricing
  const totalPrice = items.reduce((sum, item) => sum + getItemTotal(item), 0);

  // Add item to cart with enhanced logging
  const addItem = (newItem: CartItem) => {
    console.log('🛒 [CART_ADD_ITEM] Adding item to cart:', newItem);

    setItems(prevItems => {
      const existingItemIndex = prevItems.findIndex(item => item.id === newItem.id);

      if (existingItemIndex >= 0) {
        // Item exists, update quantity
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + newItem.quantity
        };
        console.log('🛒 [CART_ADD_ITEM] Updated existing item quantity:', updatedItems[existingItemIndex]);
        return updatedItems;
      } else {
        // Item doesn't exist, add it
        const newItems = [...prevItems, newItem];
        console.log('🛒 [CART_ADD_ITEM] Added new item to cart. Total items:', newItems.length);
        return newItems;
      }
    });
  };

  // Remove item from cart
  const removeItem = (itemId: number) => {
    setItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  // Update item quantity
  const updateQuantity = (itemId: number, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    setItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  // Clear cart
  const clearCart = () => {
    setItems([]);
  };

  return (
    <CartContext.Provider value={{
      items,
      addItem,
      removeItem,
      updateQuantity,
      clearCart,
      totalItems,
      totalPrice,
      getItemPrice,
      getItemTier,
      getItemTotal
    }}>
      {children}
    </CartContext.Provider>
  );
}

// Custom hook to use cart context
export function useCart() {
  return useContext(CartContext);
}
