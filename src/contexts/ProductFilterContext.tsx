import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the types for product filters (NOT vehicle selection)
export interface ProductFilters {
  priceRange: [number, number];
  brands: string[];
  stockStatus: string[];
  ratings: number[];
  categories: string[];
  sortBy: string;
}

// Define the context type
interface ProductFilterContextType {
  // Product filters
  productFilters: ProductFilters;
  setProductFilters: (filters: ProductFilters) => void;
  
  // Modal state
  showProductFilterModal: boolean;
  setShowProductFilterModal: (show: boolean) => void;
  
  // Filter count for badge display
  activeFilterCount: number;
  
  // Helper functions
  resetProductFilters: () => void;
  applyProductFilters: (filters: Partial<ProductFilters>) => void;
}

// Default filter values
const defaultFilters: ProductFilters = {
  priceRange: [0, 10000],
  brands: [],
  stockStatus: [],
  ratings: [],
  categories: [],
  sortBy: 'popularity'
};

// Create the context
const ProductFilterContext = createContext<ProductFilterContextType | undefined>(undefined);

// Provider component
export const ProductFilterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Product filter state
  const [productFilters, setProductFilters] = useState<ProductFilters>(defaultFilters);
  
  // Modal state
  const [showProductFilterModal, setShowProductFilterModal] = useState(false);

  // Calculate active filter count
  const activeFilterCount = 
    (productFilters.priceRange[0] > 0 || productFilters.priceRange[1] < 10000 ? 1 : 0) +
    productFilters.brands.length +
    productFilters.stockStatus.length +
    productFilters.ratings.length +
    productFilters.categories.length +
    (productFilters.sortBy !== 'popularity' ? 1 : 0);

  // Helper function to reset all filters
  const resetProductFilters = () => {
    setProductFilters(defaultFilters);
  };

  // Helper function to apply partial filters
  const applyProductFilters = (filters: Partial<ProductFilters>) => {
    setProductFilters(prev => ({
      ...prev,
      ...filters
    }));
  };

  // Create the context value
  const value = {
    productFilters,
    setProductFilters,
    showProductFilterModal,
    setShowProductFilterModal,
    activeFilterCount,
    resetProductFilters,
    applyProductFilters
  };

  return (
    <ProductFilterContext.Provider value={value}>
      {children}
    </ProductFilterContext.Provider>
  );
};

// Custom hook to use the product filter context
export const useProductFilter = () => {
  const context = useContext(ProductFilterContext);
  if (context === undefined) {
    throw new Error('useProductFilter must be used within a ProductFilterProvider');
  }
  return context;
};
