import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the user role type
export type UserRole = 'supplier' | 'merchant' | 'consumer' | 'distribution' | 'admin';

// Define the user context interface
interface UserContextType {
  userRole: UserRole;
  setUserRole: (role: UserRole) => void;
  isSupplier: () => boolean;
  isMerchant: () => boolean;
  isConsumer: () => boolean;
  isDistribution: () => boolean;
  isAdmin: () => boolean;
  isRoleSwitcherVisible: boolean;
  setIsRoleSwitcherVisible: (visible: boolean) => void;

  // Multi-role support
  supplierRole: boolean;
  merchantRole: boolean;
  setSupplierRole: (active: boolean) => void;
  setMerchantRole: (active: boolean) => void;
  hasAnyRole: () => boolean;
}

// Create the context with default values
const UserContext = createContext<UserContextType>({
  userRole: 'supplier', // Default role
  setUserRole: () => {},
  isSupplier: () => false,
  isMerchant: () => false,
  isConsumer: () => false,
  isDistribution: () => false,
  isAdmin: () => false,
  isRoleSwitcherVisible: false,
  setIsRoleSwitcherVisible: () => {},

  // Multi-role support
  supplierRole: false,
  merchantRole: false,
  setSupplierRole: () => {},
  setMerchantRole: () => {},
  hasAnyRole: () => false,
});



// Provider component
export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [userRole, setUserRole] = useState<UserRole>('supplier');
  const [isRoleSwitcherVisible, setIsRoleSwitcherVisible] = useState(false);

  // Multi-role support - track which roles are active
  const [supplierRole, setSupplierRole] = useState<boolean>(false);
  const [merchantRole, setMerchantRole] = useState<boolean>(false);

  // Helper functions to check roles
  const isSupplier = () => userRole === 'supplier';
  const isMerchant = () => userRole === 'merchant';
  const isConsumer = () => userRole === 'consumer';
  const isDistribution = () => userRole === 'distribution';
  const isAdmin = () => userRole === 'admin';

  // Check if user has any role active
  const hasAnyRole = () => supplierRole || merchantRole;

  // On component mount, try to get the role from localStorage or Supabase
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        console.log('Fetching user role...');

        // First try to get the role from localStorage for faster loading
        const storedRole = localStorage.getItem('userRole') as UserRole;
        if (storedRole && ['supplier', 'merchant', 'consumer', 'distribution', 'admin'].includes(storedRole)) {
          console.log('Using role from localStorage:', storedRole);
          setUserRole(storedRole);
        }

        // Check for multi-role support in localStorage
        const storedSupplierRole = localStorage.getItem('supplierRole');
        if (storedSupplierRole === 'true') {
          setSupplierRole(true);
        }

        const storedMerchantRole = localStorage.getItem('merchantRole');
        if (storedMerchantRole === 'true') {
          setMerchantRole(true);
        }

        // Role switcher disabled for production
        setIsRoleSwitcherVisible(false);


      } catch (error) {
        console.error('Error in fetchUserRole:', error);
        // Set default role to ensure the app doesn't break
        setUserRole('supplier');
      }
    };

    fetchUserRole();
  }, []);

  // When role changes, store it in localStorage
  useEffect(() => {
    console.log('Updating user role to:', userRole);
    localStorage.setItem('userRole', userRole);
  }, [userRole]);

  // Update localStorage when supplier role changes
  useEffect(() => {
    localStorage.setItem('supplierRole', supplierRole.toString());
    console.log('Supplier role updated:', supplierRole);
  }, [supplierRole]);

  // Update localStorage when merchant role changes
  useEffect(() => {
    localStorage.setItem('merchantRole', merchantRole.toString());
    console.log('Merchant role updated:', merchantRole);
  }, [merchantRole]);

  return (
    <UserContext.Provider
      value={{
        userRole,
        setUserRole,
        isSupplier,
        isMerchant,
        isConsumer,
        isDistribution,
        isAdmin,
        isRoleSwitcherVisible,
        setIsRoleSwitcherVisible,

        // Multi-role support
        supplierRole,
        merchantRole,
        setSupplierRole,
        setMerchantRole,
        hasAnyRole,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
