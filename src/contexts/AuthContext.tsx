import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import {
  getCurrentUser,
  getCurrentSession,
  signOut,
  getUserProfile,
  UserProfile
} from '@/services/authService';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate, useLocation } from 'react-router-dom';
import { UserRole } from '@/contexts/UserContext';

// Constants - DISABLED AUTOMATIC LOGOUT
// Users will only be logged out when they manually choose to log out
const SESSION_TIMEOUT_MS = 365 * 24 * 60 * 60 * 1000; // 1 year - effectively permanent session
const INACTIVITY_TIMEOUT_MS = 365 * 24 * 60 * 60 * 1000; // 1 year - no inactivity timeout
const ACTIVITY_EVENTS = ['mousedown', 'keydown', 'touchstart', 'scroll', 'mousemove'];

// Define the auth context interface
interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasRole: (role: UserRole) => boolean;
  logout: (redirectUrl?: string) => Promise<void>;
  refreshSession: () => Promise<void>;
  lastActivity: number;
  updateLastActivity: () => void;
  rememberMe: boolean;
  setRememberMe: (value: boolean) => void;
  loginAttempts: number;
  incrementLoginAttempt: () => void;
  resetLoginAttempts: () => void;
  isRateLimited: boolean;
}

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  isAuthenticated: false,
  isLoading: true,
  hasRole: () => false,
  logout: async () => {},
  refreshSession: async () => {},
  lastActivity: Date.now(),
  updateLastActivity: () => {},
  rememberMe: false,
  setRememberMe: () => {},
  loginAttempts: 0,
  incrementLoginAttempt: () => {},
  resetLoginAttempts: () => {},
  isRateLimited: false,
});

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastActivity, setLastActivity] = useState(() => {
    // Try to get the last activity time from localStorage
    const storedActivity = localStorage.getItem('lastActivity');
    // If it exists and is not too old (within 24 hours), use it
    if (storedActivity) {
      const parsedTime = parseInt(storedActivity, 10);
      const now = Date.now();
      // Only use stored time if it's within the last 24 hours
      if (now - parsedTime < 24 * 60 * 60 * 1000) {
        return parsedTime;
      }
    }
    // Otherwise use current time
    return Date.now();
  });
  const [sessionExpiryTime, setSessionExpiryTime] = useState<number | null>(null);
  const [rememberMe, setRememberMe] = useState(() => {
    return localStorage.getItem('rememberMe') === 'true';
  });
  const [loginAttempts, setLoginAttempts] = useState(() => {
    const storedAttempts = localStorage.getItem('loginAttempts');
    return storedAttempts ? parseInt(storedAttempts, 10) : 0;
  });
  const [lastLoginAttempt, setLastLoginAttempt] = useState(() => {
    const storedTime = localStorage.getItem('lastLoginAttempt');
    return storedTime ? parseInt(storedTime, 10) : 0;
  });

  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();

  // DISABLED: Rate limiting completely removed - unlimited attempts allowed
  const isRateLimited = false;

  // Increment login attempt counter
  const incrementLoginAttempt = () => {
    const newAttempts = loginAttempts + 1;
    setLoginAttempts(newAttempts);
    setLastLoginAttempt(Date.now());
    localStorage.setItem('loginAttempts', newAttempts.toString());
    localStorage.setItem('lastLoginAttempt', Date.now().toString());
  };

  // Reset login attempts
  const resetLoginAttempts = () => {
    setLoginAttempts(0);
    localStorage.removeItem('loginAttempts');
    localStorage.removeItem('lastLoginAttempt');
  };

  // Update last activity timestamp
  const updateLastActivity = () => {
    const now = Date.now();
    setLastActivity(now);
    localStorage.setItem('lastActivity', now.toString());

    // Log activity update (only in development mode to avoid console spam)
    if (import.meta.env.DEV) {
      console.log('User activity updated at:', new Date(now).toLocaleTimeString());
    }
  };

  // Check if user has a specific role
  const hasRole = (role: UserRole): boolean => {
    if (!user || !profile) return false;
    return profile.role === role;
  };

  // Logout function
  const logout = async (redirectUrl: string = '/partners') => {
    try {
      await signOut();
      setUser(null);
      setProfile(null);

      // Clear session data
      localStorage.removeItem('lastActivity');
      if (!rememberMe) {
        localStorage.removeItem('rememberMe');
      }

      // Log the logout action
      console.log('User logged out successfully');

      // Always redirect to partners page after logout
      navigate('/partners');

      toast({
        title: 'Logged out successfully',
        description: 'You have been logged out of your account.',
      });
    } catch (error) {
      console.error('Error during logout:', error);
      toast({
        title: 'Logout failed',
        description: 'There was a problem logging you out. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Refresh user session
  const refreshSession = async () => {
    setIsLoading(true);
    try {
      console.log('Refreshing user session...');
      const { data: userData } = await getCurrentUser();

      if (userData?.user) {
        console.log('User found:', userData.user.id);

        // Get user profile first before setting any state
        const { profile: userProfile } = await getUserProfile(userData.user.id);

        // Batch state updates to prevent flickering
        if (userProfile) {
          console.log('User profile found:', userProfile.role);
          // Set both user and profile in a single batch update
          setUser(userData.user);
          setProfile(userProfile);
        } else {
          console.warn('No user profile found for user:', userData.user.id);
          setUser(userData.user);
          setProfile(null);
        }

        // DISABLED: Do not use Supabase session expiry time
        // We want permanent sessions, so we ignore Supabase's JWT expiry
        // and rely only on our custom timeout logic (which is set to 1 year)
        setSessionExpiryTime(null);
        console.log('Session expiry checking disabled - using permanent session mode');

        // Reset login attempts on successful session refresh
        resetLoginAttempts();

        // Update last activity
        updateLastActivity();

        return true;
      } else {
        console.log('No user found in session');
        // Batch clear both user and profile
        setUser(null);
        setProfile(null);
        return false;
      }
    } catch (error) {
      console.error('Error refreshing session:', error);
      // Clear state on error
      setUser(null);
      setProfile(null);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      await refreshSession();
    };

    initAuth();
  }, []);

  // Set up activity tracking with throttling to prevent excessive updates
  useEffect(() => {
    let lastUpdateTime = Date.now();
    let activityTimeout: number | null = null;

    // Throttled activity handler - only updates once every 5 minutes
    // This prevents excessive state updates while still tracking activity
    const handleActivity = () => {
      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdateTime;

      // Clear any pending timeout
      if (activityTimeout !== null) {
        window.clearTimeout(activityTimeout);
        activityTimeout = null;
      }

      // Only update if it's been more than 5 minutes since the last update
      // This significantly reduces the number of state updates
      if (timeSinceLastUpdate > 5 * 60 * 1000) {
        updateLastActivity();
        lastUpdateTime = now;
      } else {
        // Schedule an update in the future if no more activity occurs
        activityTimeout = window.setTimeout(() => {
          updateLastActivity();
          lastUpdateTime = Date.now();
          activityTimeout = null;
        }, 5 * 60 * 1000);
      }
    };

    // Add event listeners for user activity
    ACTIVITY_EVENTS.forEach(event => {
      window.addEventListener(event, handleActivity, { passive: true });
    });

    // Initial activity update
    updateLastActivity();

    return () => {
      // Clean up event listeners and any pending timeout
      ACTIVITY_EVENTS.forEach(event => {
        window.removeEventListener(event, handleActivity);
      });

      if (activityTimeout !== null) {
        window.clearTimeout(activityTimeout);
      }
    };
  }, []);

  // Set up session timeout checking
  useEffect(() => {
    if (!user) return;

    const checkSessionTimeout = () => {
      const now = Date.now();

      // DEBUG: Confirm automatic logout is disabled
      console.log('🔒 Session timeout check running - automatic logout DISABLED', {
        sessionExpiryTime: sessionExpiryTime,
        rememberMe: rememberMe,
        lastActivity: new Date(lastActivity).toLocaleString(),
        inactivityTimeoutMs: INACTIVITY_TIMEOUT_MS
      });

      // For debugging - log remaining session time (should be null now)
      if (sessionExpiryTime) {
        const remainingSessionTime = sessionExpiryTime - now;
        if (remainingSessionTime > 0) {
          const remainingMinutes = Math.floor(remainingSessionTime / (60 * 1000));
          if (remainingMinutes % 30 === 0) { // Log every 30 minutes
            console.log(`Session expires in ${remainingMinutes} minutes`);
          }
        }
      } else {
        console.log('✅ Session expiry time is NULL - automatic logout successfully disabled');
      }

      // DISABLED: Absolute session timeout checking
      // We want permanent sessions, so we never check Supabase JWT expiry
      // Users will only be logged out manually or due to inactivity (if rememberMe is false)
      if (false) { // This block is intentionally disabled
        console.log('Absolute session timeout checking is disabled');
        return;
      }

      // Check for inactivity timeout (unless remember me is enabled)
      if (!rememberMe && now - lastActivity > INACTIVITY_TIMEOUT_MS) {
        const inactiveTime = Math.floor((now - lastActivity) / (60 * 1000));
        console.log(`Inactivity timeout reached. User inactive for ${inactiveTime} minutes.`);
        console.log('Last activity:', new Date(lastActivity).toLocaleString());

        toast({
          title: 'Session timeout',
          description: 'You have been logged out due to inactivity.',
        });
        logout();
        return;
      }
    };

    // Check session timeout every 5 minutes instead of every minute
    // This reduces unnecessary checks while still maintaining security
    const intervalId = setInterval(checkSessionTimeout, 5 * 60 * 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [user, sessionExpiryTime, lastActivity, rememberMe]);

  // Update localStorage when rememberMe changes
  useEffect(() => {
    localStorage.setItem('rememberMe', rememberMe.toString());
  }, [rememberMe]);

  // Provide the auth context
  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isAuthenticated: !!user,
        isLoading,
        hasRole,
        logout,
        refreshSession,
        lastActivity,
        updateLastActivity,
        rememberMe,
        setRememberMe,
        loginAttempts,
        incrementLoginAttempt,
        resetLoginAttempts,
        isRateLimited,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
