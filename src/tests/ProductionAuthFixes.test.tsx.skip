/**
 * Production Authentication Fixes Test Suite
 * 
 * Tests for critical production fixes:
 * 1. AutoAuthModal only appears on consumer routes (not admin/shipping)
 * 2. Existing consumers can login with phone only (no OTP)
 * 
 * PRODUCTION CRITICAL: These tests ensure no breaking changes
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Import components and utilities
import { AutoAuthModal } from '@/components/auth/AutoAuthModal';
import { EnhancedPhoneAuthModal } from '@/components/auth/EnhancedPhoneAuthModal';
import { 
  isAdminOrBusinessRoute, 
  isConsumerMarketplaceRoute, 
  getRouteType 
} from '@/utils/routeUtils';
import { loginExistingConsumer, checkPhoneExists } from '@/services/phoneAuthService';

// Mock dependencies
vi.mock('@/services/phoneAuthService', () => ({
  loginExistingConsumer: vi.fn(),
  checkPhoneExists: vi.fn(),
  sendPhoneOTP: vi.fn(),
  verifyPhoneOTP: vi.fn(),
  validateAlgerianPhone: vi.fn()
}));

vi.mock('@/components/auth/ConsumerAuthGuard', () => ({
  useConsumerAuth: () => ({
    isAuthenticated: false,
    isLoading: false
  })
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Production Authentication Fixes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: { pathname: '/' },
      writable: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Fix 1: AutoAuthModal Route Detection', () => {
    it('should identify admin routes correctly', () => {
      const adminRoutes = [
        '/admin',
        '/admin/dashboard',
        '/admin/orders',
        '/app',
        '/app/dashboard',
        '/app/products',
        '/shipping',
        '/shipping/dashboard'
      ];

      adminRoutes.forEach(route => {
        expect(isAdminOrBusinessRoute(route)).toBe(true);
        expect(isConsumerMarketplaceRoute(route)).toBe(false);
        expect(getRouteType(route)).toMatch(/admin|business|shipping/);
      });
    });

    it('should identify consumer routes correctly', () => {
      const consumerRoutes = [
        '/',
        '/my-vehicle-parts',
        '/wholesale-offers',
        '/wishlist',
        '/basket',
        '/TYR-123456' // Product ID route
      ];

      consumerRoutes.forEach(route => {
        expect(isConsumerMarketplaceRoute(route)).toBe(true);
        expect(isAdminOrBusinessRoute(route)).toBe(false);
        expect(getRouteType(route)).toBe('consumer');
      });
    });

    it('should NOT render AutoAuthModal on admin routes', () => {
      // Mock admin route
      Object.defineProperty(window, 'location', {
        value: { pathname: '/admin/dashboard' },
        writable: true
      });

      render(
        <TestWrapper>
          <AutoAuthModal />
        </TestWrapper>
      );

      // Modal should not be present in DOM
      expect(screen.queryByText('Consumer Registration')).not.toBeInTheDocument();
    });

    it('should render AutoAuthModal on consumer routes', async () => {
      // Mock consumer route
      Object.defineProperty(window, 'location', {
        value: { pathname: '/' },
        writable: true
      });

      render(
        <TestWrapper>
          <AutoAuthModal delay={100} minTimeOnSite={0} />
        </TestWrapper>
      );

      // Wait for modal to appear (after delay)
      await waitFor(() => {
        expect(screen.queryByText('Consumer Registration')).toBeInTheDocument();
      }, { timeout: 200 });
    });
  });

  describe('Fix 2: Phone-Only Login for Existing Users', () => {
    it('should login existing consumer without OTP', async () => {
      const mockUser = { id: 'user123', phone: '+213555123456' };
      const mockProfile = { id: 'user123', full_name: 'Ahmed B' };

      // Mock existing user
      vi.mocked(checkPhoneExists).mockResolvedValue({ exists: true });
      vi.mocked(loginExistingConsumer).mockResolvedValue({
        success: true,
        user: mockUser,
        profile: mockProfile,
        action: 'login'
      });

      const onSuccess = vi.fn();
      const onClose = vi.fn();

      render(
        <TestWrapper>
          <EnhancedPhoneAuthModal
            isOpen={true}
            onClose={onClose}
            onSuccess={onSuccess}
          />
        </TestWrapper>
      );

      // Enter phone number
      const phoneInput = screen.getByPlaceholderText('XXX XXX XXX');
      fireEvent.change(phoneInput, { target: { value: '555123456' } });

      // Click continue button
      const continueButton = screen.getByText('Continue');
      fireEvent.click(continueButton);

      // Should login without OTP
      await waitFor(() => {
        expect(loginExistingConsumer).toHaveBeenCalledWith('+213555123456', undefined);
        expect(onSuccess).toHaveBeenCalledWith(mockUser, 'login');
        expect(onClose).toHaveBeenCalled();
      });
    });

    it('should require OTP for new users', async () => {
      // Mock new user
      vi.mocked(checkPhoneExists).mockResolvedValue({ exists: false });

      render(
        <TestWrapper>
          <EnhancedPhoneAuthModal
            isOpen={true}
            onClose={vi.fn()}
            onSuccess={vi.fn()}
          />
        </TestWrapper>
      );

      // Enter phone and name for new user
      const phoneInput = screen.getByPlaceholderText('XXX XXX XXX');
      const nameInput = screen.getByPlaceholderText(/Enter first and last name/);
      
      fireEvent.change(phoneInput, { target: { value: '555123456' } });
      fireEvent.change(nameInput, { target: { value: 'Ahmed Benali' } });

      // Click continue button
      const continueButton = screen.getByText('Continue');
      fireEvent.click(continueButton);

      // Should proceed to OTP step for new users
      await waitFor(() => {
        expect(screen.getByText('Enter Verification Code')).toBeInTheDocument();
      });
    });

    it('should handle phone-only login errors gracefully', async () => {
      // Mock existing user but login fails
      vi.mocked(checkPhoneExists).mockResolvedValue({ exists: true });
      vi.mocked(loginExistingConsumer).mockResolvedValue({
        success: false,
        error: 'Network error'
      });

      render(
        <TestWrapper>
          <EnhancedPhoneAuthModal
            isOpen={true}
            onClose={vi.fn()}
            onSuccess={vi.fn()}
          />
        </TestWrapper>
      );

      // Enter phone number and name
      const phoneInput = screen.getByPlaceholderText('XXX XXX XXX');
      const nameInput = screen.getByPlaceholderText(/Enter first and last name/);
      
      fireEvent.change(phoneInput, { target: { value: '555123456' } });
      fireEvent.change(nameInput, { target: { value: 'Ahmed Benali' } });

      // Click continue button
      const continueButton = screen.getByText('Continue');
      fireEvent.click(continueButton);

      // Should fallback to OTP flow
      await waitFor(() => {
        expect(screen.getByText('Enter Verification Code')).toBeInTheDocument();
      });
    });
  });

  describe('Security and Edge Cases', () => {
    it('should prevent duplicate consumer creation', async () => {
      const phoneNumber = '+213555123456';
      
      // Mock phone exists check
      vi.mocked(checkPhoneExists).mockResolvedValue({ exists: true });
      
      const result = await checkPhoneExists(phoneNumber);
      expect(result.exists).toBe(true);
      
      // Should not create new user if phone exists
      expect(vi.mocked(checkPhoneExists)).toHaveBeenCalledWith(phoneNumber);
    });

    it('should validate Algerian phone number format', () => {
      const validPhones = ['555123456', '661234567', '770123456'];
      const invalidPhones = ['123', '12345678901', 'abc123456'];

      // This would be tested in the actual validation function
      validPhones.forEach(phone => {
        expect(phone).toMatch(/^\d{9}$/);
      });

      invalidPhones.forEach(phone => {
        expect(phone).not.toMatch(/^\d{9}$/);
      });
    });

    it('should handle localStorage session persistence', () => {
      const mockSession = {
        user: { id: 'user123', phone: '+213555123456' },
        profile: { full_name: 'Ahmed B' },
        authenticated_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      };

      // Test localStorage operations
      localStorage.setItem('phone_auth_session', JSON.stringify(mockSession));
      const stored = localStorage.getItem('phone_auth_session');
      
      expect(stored).toBeTruthy();
      expect(JSON.parse(stored!)).toEqual(mockSession);
      
      // Cleanup
      localStorage.removeItem('phone_auth_session');
    });
  });
});
