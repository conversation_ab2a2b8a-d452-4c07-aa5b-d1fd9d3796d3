import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../services/authService';

// Types
export interface Category {
  id: string;
  name: string;
  display_name: string;
  description: string | null;
  id_prefix: string;
  created_at: string;
  created_by: string | null;
  is_active: boolean;
  sort_order: number;
}

export interface Subcategory {
  id: string;
  name: string;
  display_name: string;
  category_id: string;
  created_at: string;
  created_by: string | null;
  is_active: boolean;
  sort_order: number;
}

// Fetch all active categories
export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async (): Promise<Category[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Fetch all active subcategories
export const useSubcategories = () => {
  return useQuery({
    queryKey: ['subcategories'],
    queryFn: async (): Promise<Subcategory[]> => {
      const { data, error } = await supabase
        .from('subcategories')
        .select('*')
        .eq('is_active', true)
        .order('category_id, sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching subcategories:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Fetch subcategories for a specific category
export const useSubcategoriesByCategory = (categoryId: string) => {
  return useQuery({
    queryKey: ['subcategories', categoryId],
    queryFn: async (): Promise<Subcategory[]> => {
      const { data, error } = await supabase
        .from('subcategories')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching subcategories for category:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get category by ID
export const useCategoryById = (categoryId: string) => {
  return useQuery({
    queryKey: ['category', categoryId],
    queryFn: async (): Promise<Category | null> => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('id', categoryId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        console.error('Error fetching category:', error);
        throw error;
      }

      return data;
    },
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get subcategory by ID
export const useSubcategoryById = (subcategoryId: string) => {
  return useQuery({
    queryKey: ['subcategory', subcategoryId],
    queryFn: async (): Promise<Subcategory | null> => {
      const { data, error } = await supabase
        .from('subcategories')
        .select('*')
        .eq('id', subcategoryId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        console.error('Error fetching subcategory:', error);
        throw error;
      }

      return data;
    },
    enabled: !!subcategoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Utility functions
export const useCategoryUtils = () => {
  const queryClient = useQueryClient();

  const invalidateCategories = () => {
    queryClient.invalidateQueries({ queryKey: ['categories'] });
    queryClient.invalidateQueries({ queryKey: ['subcategories'] });
  };

  const getCachedCategories = (): Category[] => {
    return queryClient.getQueryData(['categories']) || [];
  };

  const getCachedSubcategories = (): Subcategory[] => {
    return queryClient.getQueryData(['subcategories']) || [];
  };

  const getCachedCategoryById = (categoryId: string): Category | undefined => {
    const categories = getCachedCategories();
    return categories.find(cat => cat.id === categoryId);
  };

  const getCachedSubcategoryById = (subcategoryId: string): Subcategory | undefined => {
    const subcategories = getCachedSubcategories();
    return subcategories.find(sub => sub.id === subcategoryId);
  };

  const getCachedSubcategoriesByCategory = (categoryId: string): Subcategory[] => {
    const subcategories = getCachedSubcategories();
    return subcategories.filter(sub => sub.category_id === categoryId);
  };

  return {
    invalidateCategories,
    getCachedCategories,
    getCachedSubcategories,
    getCachedCategoryById,
    getCachedSubcategoryById,
    getCachedSubcategoriesByCategory,
  };
};
