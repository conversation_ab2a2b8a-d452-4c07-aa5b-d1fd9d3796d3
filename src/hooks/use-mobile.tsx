import * as React from "react"

const MO<PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  // Initialize with the current window width to avoid undefined on first render
  const [isMobile, setIsMobile] = React.useState<boolean>(
    typeof window !== 'undefined' ? window.innerWidth < MOBILE_BREAKPOINT : false
  )

  React.useEffect(() => {
    // Function to update the state based on window width
    const updateSize = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }

    // Add event listener for window resize
    window.addEventListener('resize', updateSize)

    // Initial check
    updateSize()

    // Cleanup
    return () => window.removeEventListener('resize', updateSize)
  }, [])

  // Log for debugging
  console.log('useIsMobile hook:', isMobile)

  return isMobile
}
