/**
 * Dashboard Data Hooks
 * 
 * React hooks for fetching real-time dashboard data with proper error handling,
 * loading states, and automatic refetching for both Suppliers and Merchants
 */

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@/contexts/UserContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  getSupplierDashboardData,
  getMerchantDashboardData,
  getDashboardMetrics,
  SupplierDashboardData,
  MerchantDashboardData,
  DashboardMetrics
} from '@/services/dashboardService';
import { useToast } from '@/components/ui/use-toast';
import { useEffect } from 'react';

/**
 * Hook for fetching basic dashboard metrics
 */
export function useDashboardMetrics() {
  const { toast } = useToast();

  return useQuery<DashboardMetrics>({
    queryKey: ['dashboard-metrics'],
    queryFn: getDashboardMetrics,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      console.error('Error fetching dashboard metrics:', error);
      toast({
        title: 'Error Loading Dashboard',
        description: 'Failed to load dashboard metrics. Please try again.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook for fetching supplier dashboard data
 */
export function useSupplierDashboard() {
  const { userRole } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const query = useQuery<SupplierDashboardData>({
    queryKey: ['supplier-dashboard'],
    queryFn: getSupplierDashboardData,
    enabled: userRole === 'supplier',
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      console.error('Error fetching supplier dashboard:', error);
      toast({
        title: 'Error Loading Supplier Dashboard',
        description: 'Failed to load supplier dashboard data. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Refresh function for manual updates
  const refresh = () => {
    queryClient.invalidateQueries({ queryKey: ['supplier-dashboard'] });
    queryClient.invalidateQueries({ queryKey: ['dashboard-metrics'] });
  };

  return {
    ...query,
    refresh,
  };
}

/**
 * Hook for fetching merchant dashboard data
 */
export function useMerchantDashboard() {
  const { userRole } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const query = useQuery<MerchantDashboardData>({
    queryKey: ['merchant-dashboard'],
    queryFn: getMerchantDashboardData,
    enabled: userRole === 'merchant',
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      console.error('Error fetching merchant dashboard:', error);
      toast({
        title: 'Error Loading Merchant Dashboard',
        description: 'Failed to load merchant dashboard data. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Refresh function for manual updates
  const refresh = () => {
    queryClient.invalidateQueries({ queryKey: ['merchant-dashboard'] });
    queryClient.invalidateQueries({ queryKey: ['dashboard-metrics'] });
  };

  return {
    ...query,
    refresh,
  };
}

/**
 * Universal dashboard hook that returns appropriate data based on user role
 */
export function useDashboard() {
  const { userRole } = useUser();
  const { isAuthenticated } = useAuth();
  const supplierData = useSupplierDashboard();
  const merchantData = useMerchantDashboard();

  // Return appropriate data based on user role
  if (!isAuthenticated) {
    return {
      data: null,
      isLoading: false,
      isError: true,
      error: new Error('User not authenticated'),
      refresh: () => {},
    };
  }

  if (userRole === 'supplier') {
    return supplierData;
  } else if (userRole === 'merchant') {
    return merchantData;
  } else {
    return {
      data: null,
      isLoading: false,
      isError: true,
      error: new Error('Invalid user role'),
      refresh: () => {},
    };
  }
}

/**
 * Hook for real-time dashboard updates
 * Automatically refreshes data when certain events occur
 */
export function useRealtimeDashboard() {
  const dashboard = useDashboard();
  const queryClient = useQueryClient();

  // Set up real-time subscriptions and performance monitoring
  useEffect(() => {
    let refreshTimer: NodeJS.Timeout;
    let performanceStartTime = Date.now();

    // Listen for order status changes, new orders, etc.
    // This would integrate with Supabase real-time subscriptions

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Refresh data when user returns to the tab
        performanceStartTime = Date.now();
        dashboard.refresh();
      }
    };

    const handleFocus = () => {
      // Refresh data when window gains focus
      if (Date.now() - performanceStartTime > 30000) { // Only if more than 30 seconds
        dashboard.refresh();
        performanceStartTime = Date.now();
      }
    };

    const handleOnline = () => {
      // Refresh data when connection is restored
      dashboard.refresh();
    };

    // Set up periodic refresh for critical data
    refreshTimer = setInterval(() => {
      if (document.visibilityState === 'visible') {
        dashboard.refresh();
      }
    }, 5 * 60 * 1000); // Every 5 minutes

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('online', handleOnline);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('online', handleOnline);
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  }, [dashboard.refresh]);

  return dashboard;
}

/**
 * Hook for dashboard data with optimistic updates
 */
export function useDashboardWithOptimisticUpdates() {
  const dashboard = useDashboard();
  const queryClient = useQueryClient();

  const updateOptimistically = (updates: Partial<SupplierDashboardData | MerchantDashboardData>) => {
    const queryKey = dashboard.data && 'ordersAwaitingShipment' in dashboard.data 
      ? ['supplier-dashboard'] 
      : ['merchant-dashboard'];

    queryClient.setQueryData(queryKey, (oldData: any) => {
      if (!oldData) return oldData;
      return { ...oldData, ...updates };
    });
  };

  return {
    ...dashboard,
    updateOptimistically,
  };
}

/**
 * Hook for dashboard performance metrics
 */
export function useDashboardPerformance() {
  const { userRole } = useUser();
  
  return useQuery({
    queryKey: ['dashboard-performance', userRole],
    queryFn: async () => {
      // Track dashboard load times, data freshness, etc.
      return {
        loadTime: Date.now(),
        dataFreshness: new Date().toISOString(),
        cacheHitRate: 0.85, // Example metric
      };
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook for dashboard error recovery
 */
export function useDashboardErrorRecovery() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const recoverFromError = async () => {
    try {
      // Clear all dashboard-related cache
      await queryClient.invalidateQueries({ queryKey: ['supplier-dashboard'] });
      await queryClient.invalidateQueries({ queryKey: ['merchant-dashboard'] });
      await queryClient.invalidateQueries({ queryKey: ['dashboard-metrics'] });
      
      toast({
        title: 'Dashboard Refreshed',
        description: 'Dashboard data has been refreshed successfully.',
      });
    } catch (error) {
      console.error('Error recovering dashboard:', error);
      toast({
        title: 'Recovery Failed',
        description: 'Failed to recover dashboard data. Please refresh the page.',
        variant: 'destructive',
      });
    }
  };

  return { recoverFromError };
}

/**
 * Hook for dashboard data export
 */
export function useDashboardExport() {
  const dashboard = useDashboard();

  const exportData = (format: 'json' | 'csv' = 'json') => {
    if (!dashboard.data) {
      throw new Error('No dashboard data available for export');
    }

    if (format === 'json') {
      const dataStr = JSON.stringify(dashboard.data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
    }
    // CSV export would be implemented here
  };

  return { exportData };
}
