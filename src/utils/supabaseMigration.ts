/**
 * Supabase Migration Utility
 * 
 * This utility migrates existing localStorage product data to the new Supabase backend
 * while preserving all data integrity and user account isolation.
 */

import { createClient } from '@supabase/supabase-js';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { createProduct } from '@/services/productService';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export interface MigrationResult {
  category: string;
  totalProducts: number;
  migratedProducts: number;
  failedProducts: number;
  errors: string[];
  skippedProducts: number;
}

export interface MigrationSummary {
  totalCategories: number;
  totalProducts: number;
  totalMigrated: number;
  totalFailed: number;
  totalSkipped: number;
  results: MigrationResult[];
  duration: number;
}

/**
 * Check if user is authenticated
 */
async function checkAuthentication(): Promise<boolean> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    return !error && !!user;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
}

/**
 * Get current user ID
 */
async function getCurrentUserId(): Promise<string | null> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      return null;
    }
    return user.id;
  } catch (error) {
    console.error('Error getting user ID:', error);
    return null;
  }
}

/**
 * Check if a product already exists in Supabase
 */
async function productExists(productId: string, userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('id')
      .eq('id', productId)
      .eq('user_id', userId)
      .single();

    return !error && !!data;
  } catch (error) {
    return false;
  }
}

/**
 * Migrate products from a specific category
 */
export async function migrateCategoryProducts(categoryId: string): Promise<MigrationResult> {
  const result: MigrationResult = {
    category: categoryId,
    totalProducts: 0,
    migratedProducts: 0,
    failedProducts: 0,
    errors: [],
    skippedProducts: 0
  };

  try {
    // Check authentication
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
      result.errors.push('User not authenticated');
      return result;
    }

    const userId = await getCurrentUserId();
    if (!userId) {
      result.errors.push('Could not get user ID');
      return result;
    }

    // Get products from localStorage
    const storageKey = `products-${categoryId}`;
    const storedData = localStorage.getItem(storageKey);

    if (!storedData) {
      result.errors.push(`No products found in localStorage for category: ${categoryId}`);
      return result;
    }

    let products: (TyreProduct | BrakeProduct)[] = [];
    
    try {
      products = JSON.parse(storedData);
      
      // Convert string dates back to Date objects
      products = products.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt),
        inventoryUpdateDate: item.inventoryUpdateDate ? new Date(item.inventoryUpdateDate) : new Date()
      }));
    } catch (parseError) {
      result.errors.push(`Error parsing localStorage data: ${parseError}`);
      return result;
    }

    result.totalProducts = products.length;

    // Migrate each product
    for (const product of products) {
      try {
        // Check if product already exists
        const exists = await productExists(product.id, userId);
        if (exists) {
          result.skippedProducts++;
          console.log(`Product ${product.id} already exists, skipping...`);
          continue;
        }

        // Ensure the product has the user_id field for the migration
        const productWithUserId = {
          ...product,
          // Ensure marketplace section is set based on product characteristics
          marketplaceSection: product.marketplaceSection || 
            (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0 ? 'wholesale' : 'retail')
        };

        // Create product in Supabase
        await createProduct(productWithUserId);
        result.migratedProducts++;
        
        console.log(`Successfully migrated product: ${product.id}`);
        
      } catch (productError) {
        result.failedProducts++;
        const errorMessage = `Failed to migrate product ${product.id}: ${productError}`;
        result.errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    console.log(`Migration completed for category ${categoryId}:`, {
      total: result.totalProducts,
      migrated: result.migratedProducts,
      failed: result.failedProducts,
      skipped: result.skippedProducts
    });

  } catch (error) {
    result.errors.push(`Category migration error: ${error}`);
    console.error(`Error migrating category ${categoryId}:`, error);
  }

  return result;
}

/**
 * Migrate all products from localStorage to Supabase
 */
export async function migrateAllProducts(): Promise<MigrationSummary> {
  const startTime = Date.now();
  const categories = ['tyres', 'brakes']; // Add more categories as needed
  
  const summary: MigrationSummary = {
    totalCategories: categories.length,
    totalProducts: 0,
    totalMigrated: 0,
    totalFailed: 0,
    totalSkipped: 0,
    results: [],
    duration: 0
  };

  console.log('Starting migration of all products to Supabase...');

  // Check authentication first
  const isAuthenticated = await checkAuthentication();
  if (!isAuthenticated) {
    throw new Error('User must be authenticated to perform migration');
  }

  // Migrate each category
  for (const category of categories) {
    console.log(`Migrating category: ${category}`);
    
    try {
      const result = await migrateCategoryProducts(category);
      summary.results.push(result);
      
      summary.totalProducts += result.totalProducts;
      summary.totalMigrated += result.migratedProducts;
      summary.totalFailed += result.failedProducts;
      summary.totalSkipped += result.skippedProducts;
      
    } catch (error) {
      console.error(`Error migrating category ${category}:`, error);
      summary.results.push({
        category,
        totalProducts: 0,
        migratedProducts: 0,
        failedProducts: 0,
        errors: [`Category migration failed: ${error}`],
        skippedProducts: 0
      });
    }
  }

  summary.duration = Date.now() - startTime;

  console.log('Migration summary:', summary);
  return summary;
}

/**
 * Check migration status for all categories
 */
export async function checkMigrationStatus(): Promise<{
  isAuthenticated: boolean;
  localStorageData: { [category: string]: number };
  supabaseData: { [category: string]: number };
  needsMigration: boolean;
}> {
  const status = {
    isAuthenticated: false,
    localStorageData: {} as { [category: string]: number },
    supabaseData: {} as { [category: string]: number },
    needsMigration: false
  };

  // Check authentication
  status.isAuthenticated = await checkAuthentication();
  
  if (!status.isAuthenticated) {
    return status;
  }

  const userId = await getCurrentUserId();
  if (!userId) {
    status.isAuthenticated = false;
    return status;
  }

  const categories = ['tyres', 'brakes'];

  // Check localStorage data
  for (const category of categories) {
    const storageKey = `products-${category}`;
    const storedData = localStorage.getItem(storageKey);
    
    if (storedData) {
      try {
        const products = JSON.parse(storedData);
        status.localStorageData[category] = products.length;
      } catch (error) {
        status.localStorageData[category] = 0;
      }
    } else {
      status.localStorageData[category] = 0;
    }
  }

  // Check Supabase data
  for (const category of categories) {
    try {
      const { count, error } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('category', category);

      status.supabaseData[category] = count || 0;
    } catch (error) {
      console.error(`Error checking Supabase data for ${category}:`, error);
      status.supabaseData[category] = 0;
    }
  }

  // Determine if migration is needed
  status.needsMigration = categories.some(category => 
    status.localStorageData[category] > 0 && 
    status.localStorageData[category] > status.supabaseData[category]
  );

  return status;
}

/**
 * Clear localStorage data after successful migration
 */
export async function clearLocalStorageAfterMigration(): Promise<void> {
  const categories = ['tyres', 'brakes'];
  
  // Verify that data exists in Supabase before clearing localStorage
  const status = await checkMigrationStatus();
  
  if (!status.isAuthenticated) {
    throw new Error('User not authenticated');
  }

  let canClear = true;
  
  // Check if all data has been migrated
  for (const category of categories) {
    if (status.localStorageData[category] > status.supabaseData[category]) {
      canClear = false;
      break;
    }
  }

  if (!canClear) {
    throw new Error('Cannot clear localStorage: migration appears incomplete');
  }

  // Clear localStorage data
  for (const category of categories) {
    const storageKey = `products-${category}`;
    localStorage.removeItem(storageKey);
    console.log(`Cleared localStorage for category: ${category}`);
  }

  console.log('localStorage cleared successfully after migration verification');
}

/**
 * Rollback migration (delete all products from Supabase for current user)
 * WARNING: This is destructive and should only be used for testing
 */
export async function rollbackMigration(): Promise<void> {
  const isAuthenticated = await checkAuthentication();
  if (!isAuthenticated) {
    throw new Error('User not authenticated');
  }

  const userId = await getCurrentUserId();
  if (!userId) {
    throw new Error('Could not get user ID');
  }

  console.warn('Rolling back migration - deleting all products from Supabase...');

  try {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    console.log('Migration rollback completed successfully');
  } catch (error) {
    console.error('Error during rollback:', error);
    throw error;
  }
}
