import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { getAllProducts } from '@/utils/centralizedProductData';
import { extractMarketplaceSectionFromId, isValidProductId as validateProductId } from '@/features/products/utils/idGenerator';

/**
 * Utility functions for product operations
 */

// Get all products from centralized data source
const allProducts = getAllProducts();

/**
 * Parse the Description and Specifications field into separate sections
 * @param descriptionAndSpecifications The combined field content
 * @returns Object with separate description and specifications
 */
export const parseDescriptionAndSpecifications = (descriptionAndSpecifications: string) => {
  if (!descriptionAndSpecifications) {
    return {
      description: '',
      specifications: {}
    };
  }

  // Try to split by common separators or patterns
  const content = descriptionAndSpecifications.trim();

  // Look for "Part 1" and "Part 2" markers (from our template)
  const part1Match = content.match(/Part 1[:\-\s]*Product Description[:\-\s]*(.*?)(?=Part 2|$)/is);
  const part2Match = content.match(/Part 2[:\-\s]*Specifications[:\-\s]*(.*?)$/is);

  if (part1Match && part2Match) {
    // Template format found
    const description = part1Match[1].trim();
    const specsText = part2Match[1].trim();

    // Parse specifications from text format
    const specifications: Record<string, string> = {};
    const specLines = specsText.split('\n').filter(line => line.trim());

    specLines.forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim().replace(/^-\s*/, '');
        const value = line.substring(colonIndex + 1).trim();
        if (key && value) {
          specifications[key] = value;
        }
      }
    });

    return { description, specifications };
  }

  // Fallback: treat entire content as description
  return {
    description: content,
    specifications: {}
  };
};

/**
 * Find a product by its ID
 * @param productId The product ID to search for
 * @returns The product if found, null otherwise
 */
export const findProductById = (productId: string): TyreProduct | BrakeProduct | null => {
  return allProducts.find(product => product.id === productId) || null;
};

/**
 * Determine which marketplace section a product belongs to based on its properties
 * @param product The product to check
 * @returns 'wholesale' if it has wholesale pricing tiers, 'retail' if it has retail price
 */
export const getProductMarketplaceSection = (product: TyreProduct | BrakeProduct): 'wholesale' | 'retail' => {
  console.log('🔍 [MARKETPLACE_SECTION] Analyzing product:', {
    id: product.id,
    name: product.name,
    hasMarketplaceSectionProperty: 'marketplaceSection' in product,
    marketplaceSectionValue: product.marketplaceSection
  });

  // PRIORITY 1: Extract marketplace section from the product ID suffix (most reliable)
  const sectionFromId = extractMarketplaceSectionFromId(product.id);
  console.log('🔍 [MARKETPLACE_SECTION] ID suffix extraction:', {
    productId: product.id,
    endsWithWholesale: product.id.endsWith('-WHOLESALE'),
    endsWithRetail: product.id.endsWith('-RETAIL'),
    extractedSection: sectionFromId
  });

  if (sectionFromId) {
    console.log('✅ [MARKETPLACE_SECTION] Using ID suffix (PRIORITY 1):', sectionFromId);
    return sectionFromId;
  }

  // PRIORITY 2: Check pricing structure (more reliable than database field)
  const hasWholesalePricing = product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0;
  const hasRetailPrice = !!product.retailPrice;

  console.log('🔍 [MARKETPLACE_SECTION] Pricing structure analysis:', {
    hasWholesalePricing,
    wholesalePricingTiersCount: product.wholesalePricingTiers?.length || 0,
    hasRetailPrice,
    retailPriceValue: product.retailPrice
  });

  if (hasWholesalePricing && !hasRetailPrice) {
    console.log('✅ [MARKETPLACE_SECTION] Using pricing structure (PRIORITY 2): wholesale (has wholesale tiers, no retail price)');
    return 'wholesale';
  }

  if (hasRetailPrice && !hasWholesalePricing) {
    console.log('✅ [MARKETPLACE_SECTION] Using pricing structure (PRIORITY 2): retail (has retail price, no wholesale tiers)');
    return 'retail';
  }

  // PRIORITY 3: Enhanced ID logic - If product ID contains WHOLESALE, prioritize wholesale
  if (product.id.includes('WHOLESALE')) {
    console.log('✅ [MARKETPLACE_SECTION] ID contains WHOLESALE (PRIORITY 3), forcing wholesale section');
    return 'wholesale';
  }

  // PRIORITY 4: Check database marketplaceSection property (lowest priority due to potential data inconsistency)
  if ('marketplaceSection' in product && product.marketplaceSection) {
    console.log('✅ [MARKETPLACE_SECTION] Using product.marketplaceSection (PRIORITY 4):', product.marketplaceSection);
    return product.marketplaceSection;
  }

  // Default fallback
  console.log('⚠️ [MARKETPLACE_SECTION] Using default fallback: retail');
  return 'retail';
};

/**
 * Get the display name for a category
 * @param categoryId The category ID from the product
 * @returns The display name for the marketplace
 */
export const getCategoryDisplayName = (categoryId: string): string => {
  const categoryMap: Record<string, string> = {
    'tyres': 'Tyres',
    'brakes': 'Brake Parts',
  };

  return categoryMap[categoryId] || categoryId;
};

/**
 * Get the primary price for display based on marketplace section
 * @param product The product
 * @param section The marketplace section
 * @returns The price to display
 */
export const getProductDisplayPrice = (product: TyreProduct | BrakeProduct, section: 'wholesale' | 'retail'): number => {
  console.log('💰 [DISPLAY_PRICE] Calculating price for:', {
    productId: product.id,
    section,
    hasWholesalePricingTiers: !!(product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0),
    wholesalePricingTiersCount: product.wholesalePricingTiers?.length || 0,
    hasRetailPrice: !!product.retailPrice,
    retailPriceValue: product.retailPrice
  });

  if (section === 'wholesale' && product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0) {
    // Return the first tier price for wholesale
    const firstTierPrice = product.wholesalePricingTiers[0].price;
    console.log('✅ [DISPLAY_PRICE] Using wholesale first tier price:', firstTierPrice);
    return firstTierPrice;
  }

  if (section === 'retail' && product.retailPrice) {
    console.log('✅ [DISPLAY_PRICE] Using retail price:', product.retailPrice);
    return product.retailPrice;
  }

  // Fallback: try to get any available price
  if (product.retailPrice) {
    console.log('⚠️ [DISPLAY_PRICE] Fallback to retail price:', product.retailPrice);
    return product.retailPrice;
  }

  if (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0) {
    const fallbackPrice = product.wholesalePricingTiers[0].price;
    console.log('⚠️ [DISPLAY_PRICE] Fallback to wholesale first tier:', fallbackPrice);
    return fallbackPrice;
  }

  console.log('❌ [DISPLAY_PRICE] No price found, returning 0');
  return 0;
};

/**
 * Check if a product ID has the correct format
 * @param productId The product ID to validate
 * @returns True if the format is valid
 */
export const isValidProductId = (productId: string): boolean => {
  return validateProductId(productId);
};

/**
 * Get product specifications for the specifications table
 * @param product The product
 * @returns Object with specification key-value pairs
 */
export const getProductSpecifications = (product: TyreProduct | BrakeProduct): Record<string, string> => {
  // First, try to get specifications from the parsed Description and Specifications field
  const parsed = parseDescriptionAndSpecifications(product.descriptionAndSpecifications || '');

  const baseSpecs: Record<string, string> = {
    'Part #': product.partArticleNumber || product.sku || 'N/A',
    'SKU': product.sku || 'N/A',
    'Product ID': product.id,
    'Manufacturer': product.manufacturer || 'N/A',
    'Stock Quantity': product.stockQuantity?.toString() || '0',
  };

  // Add parsed specifications from the Description and Specifications field
  const combinedSpecs = { ...baseSpecs, ...parsed.specifications };

  // Add category-specific specifications for tyres
  if (product.category === 'tyres' && 'width' in product) {
    const tyreProduct = product as TyreProduct;
    const tyreSpecs = {
      'Width': tyreProduct.width ? `${tyreProduct.width}mm` : 'N/A',
      'Aspect Ratio': tyreProduct.aspectRatio ? `${tyreProduct.aspectRatio}%` : 'N/A',
      'Rim Diameter': tyreProduct.rimDiameter ? `${tyreProduct.rimDiameter}"` : 'N/A',
      'Load Index': tyreProduct.loadIndex?.toString() || 'N/A',
      'Speed Rating': tyreProduct.speedRating || 'N/A',
      'Season': tyreProduct.season || 'N/A',
      'Tread Life': tyreProduct.treadLife || 'N/A',
      'Traction Rating': tyreProduct.tractionRating || 'N/A',
      'Temperature Rating': tyreProduct.temperatureRating || 'N/A',
    };

    return { ...combinedSpecs, ...tyreSpecs };
  }

  // Add certifications if available
  if (product.certifications && product.certifications.length > 0) {
    combinedSpecs['Certifications'] = product.certifications.join(', ');
  }

  // Add shipping information
  if (product.shippingOrigin) {
    combinedSpecs['Ships From'] = product.shippingOrigin;
  }

  if (product.estimatedLeadTime) {
    combinedSpecs['Lead Time'] = product.estimatedLeadTime;
  }

  return combinedSpecs;
};

/**
 * Get the product description from the parsed Description and Specifications field
 * @param product The product
 * @returns The product description text
 */
export const getProductDescription = (product: TyreProduct | BrakeProduct): string => {
  const parsed = parseDescriptionAndSpecifications(product.descriptionAndSpecifications || '');

  if (parsed.description) {
    return parsed.description;
  }

  // Fallback to a generated description
  return `High-quality ${product.name} from ${product.manufacturer}. This product is designed to meet the highest standards of performance and reliability, ensuring optimal functionality for your vehicle.`;
};

/**
 * Get vehicle compatibility information
 * @param product The product
 * @returns Array of compatible vehicle strings
 */
export const getVehicleCompatibility = (product: TyreProduct | BrakeProduct): string[] => {
  if ('vehicleTypeCompatibility' in product && product.vehicleTypeCompatibility) {
    return product.vehicleTypeCompatibility.map(vehicle => vehicle.displayName);
  }

  // Fallback for products without specific compatibility data
  return ['Universal Compatibility - Check with your vehicle specifications'];
};
