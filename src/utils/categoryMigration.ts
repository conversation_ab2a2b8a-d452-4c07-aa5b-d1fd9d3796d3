// =====================================================
// AROUZ MARKET - Category Migration Utilities
// Purpose: Seamless migration from localStorage to Supabase
// =====================================================

import { supabase } from '@/services/authService';
import { CATEGORIES } from '@/data/categoryData';

// Migration status checker
export const checkMigrationStatus = async (): Promise<{
  isMigrated: boolean;
  categoriesCount: number;
  subcategoriesCount: number;
  error?: string;
}> => {
  try {
    // Check if categories table exists and has data
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id')
      .limit(1);

    if (categoriesError) {
      return {
        isMigrated: false,
        categoriesCount: 0,
        subcategoriesCount: 0,
        error: categoriesError.message,
      };
    }

    // Check if subcategories table exists and has data
    const { data: subcategories, error: subcategoriesError } = await supabase
      .from('subcategories')
      .select('id')
      .limit(1);

    if (subcategoriesError) {
      return {
        isMigrated: false,
        categoriesCount: 0,
        subcategoriesCount: 0,
        error: subcategoriesError.message,
      };
    }

    // Get actual counts
    const { count: categoriesCount } = await supabase
      .from('categories')
      .select('*', { count: 'exact', head: true });

    const { count: subcategoriesCount } = await supabase
      .from('subcategories')
      .select('*', { count: 'exact', head: true });

    const isMigrated = (categoriesCount || 0) > 0 && (subcategoriesCount || 0) > 0;

    return {
      isMigrated,
      categoriesCount: categoriesCount || 0,
      subcategoriesCount: subcategoriesCount || 0,
    };
  } catch (error) {
    console.error('Error checking migration status:', error);
    return {
      isMigrated: false,
      categoriesCount: 0,
      subcategoriesCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Verify data integrity after migration
export const verifyMigrationIntegrity = async (): Promise<{
  isValid: boolean;
  issues: string[];
  summary: {
    categoriesInSupabase: number;
    subcategoriesInSupabase: number;
    categoriesInLocalStorage: number;
    subcategoriesInLocalStorage: number;
  };
}> => {
  const issues: string[] = [];

  try {
    // Get Supabase data
    const { data: supabaseCategories } = await supabase
      .from('categories')
      .select('*')
      .eq('is_active', true);

    const { data: supabaseSubcategories } = await supabase
      .from('subcategories')
      .select('*')
      .eq('is_active', true);

    // Get localStorage data
    const localStorageCategories = CATEGORIES;
    const localStorageSubcategories = localStorageCategories.flatMap(cat => cat.subcategories);

    // Check category counts
    const supabaseCategoriesCount = supabaseCategories?.length || 0;
    const localStorageCategoriesCount = localStorageCategories.length;

    if (supabaseCategoriesCount !== localStorageCategoriesCount) {
      issues.push(`Category count mismatch: Supabase has ${supabaseCategoriesCount}, localStorage has ${localStorageCategoriesCount}`);
    }

    // Check subcategory counts
    const supabaseSubcategoriesCount = supabaseSubcategories?.length || 0;
    const localStorageSubcategoriesCount = localStorageSubcategories.length;

    if (supabaseSubcategoriesCount !== localStorageSubcategoriesCount) {
      issues.push(`Subcategory count mismatch: Supabase has ${supabaseSubcategoriesCount}, localStorage has ${localStorageSubcategoriesCount}`);
    }

    // Check for missing categories
    const supabaseCategoryIds = new Set(supabaseCategories?.map(cat => cat.id) || []);
    const localStorageCategoryIds = new Set(localStorageCategories.map(cat => cat.id));

    for (const localId of localStorageCategoryIds) {
      if (!supabaseCategoryIds.has(localId)) {
        issues.push(`Missing category in Supabase: ${localId}`);
      }
    }

    // Check for missing subcategories (handle unique IDs with category prefixes)
    const supabaseSubcategoryIds = new Set(supabaseSubcategories?.map(sub => sub.id) || []);
    const localStorageSubcategoryIds = new Set(localStorageSubcategories.map(sub => sub.id));

    // Create a mapping of localStorage subcategories by category for better matching
    const localSubcategoriesByCategory = new Map<string, Set<string>>();
    localStorageSubcategories.forEach(sub => {
      if (!localSubcategoriesByCategory.has(sub.categoryId)) {
        localSubcategoriesByCategory.set(sub.categoryId, new Set());
      }
      localSubcategoriesByCategory.get(sub.categoryId)!.add(sub.id);
    });

    // Create a mapping of Supabase subcategories by category
    const supabaseSubcategoriesByCategory = new Map<string, Set<string>>();
    supabaseSubcategories?.forEach(sub => {
      if (!supabaseSubcategoriesByCategory.has(sub.category_id)) {
        supabaseSubcategoriesByCategory.set(sub.category_id, new Set());
      }
      supabaseSubcategoriesByCategory.get(sub.category_id)!.add(sub.id);
    });

    // Check for missing subcategories with smart matching
    for (const localId of localStorageSubcategoryIds) {
      const localSub = localStorageSubcategories.find(sub => sub.id === localId);
      if (!localSub) continue;

      const categoryId = localSub.categoryId;
      const supabaseSubsForCategory = supabaseSubcategoriesByCategory.get(categoryId) || new Set();

      // Check if the exact ID exists
      if (supabaseSubsForCategory.has(localId)) {
        continue; // Found exact match
      }

      // Check if the prefixed ID exists (for duplicates that were fixed)
      const prefixedId = `${categoryId}-${localId}`;
      if (supabaseSubsForCategory.has(prefixedId)) {
        continue; // Found prefixed match
      }

      // If neither exact nor prefixed ID found, it's truly missing
      issues.push(`Missing subcategory in Supabase: ${localId} (category: ${categoryId})`);
    }

    return {
      isValid: issues.length === 0,
      issues,
      summary: {
        categoriesInSupabase: supabaseCategoriesCount,
        subcategoriesInSupabase: supabaseSubcategoriesCount,
        categoriesInLocalStorage: localStorageCategoriesCount,
        subcategoriesInLocalStorage: localStorageSubcategoriesCount,
        migrationNote: issues.length === 0 ? 'Perfect sync achieved with unique subcategory IDs' : 'Issues found in migration',
      },
    };
  } catch (error) {
    console.error('Error verifying migration integrity:', error);
    return {
      isValid: false,
      issues: [`Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      summary: {
        categoriesInSupabase: 0,
        subcategoriesInSupabase: 0,
        categoriesInLocalStorage: CATEGORIES.length,
        subcategoriesInLocalStorage: CATEGORIES.flatMap(cat => cat.subcategories).length,
      },
    };
  }
};

// Get migration instructions for manual execution
export const getMigrationInstructions = (): string => {
  return `
🎯 AROUZ MARKET - Categories Migration Instructions

STEP 1: Run SQL Migrations in Supabase
1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Execute these files in order:
   - supabase/migrations/20241220120000_create_categories_system.sql
   - supabase/migrations/20241220120001_insert_subcategories.sql
   - supabase/migrations/20241220120002_insert_remaining_subcategories.sql

STEP 2: Verify Migration
1. Check that categories table has 8 rows
2. Check that subcategories table has 100+ rows
3. Verify all data is properly inserted

STEP 3: Test Application
1. Refresh your application
2. Test product creation modal
3. Verify category/subcategory dropdowns work
4. Test marketplace navigation

✅ The application will automatically detect the migration and switch to Supabase data!
❌ If migration fails, the app will fallback to localStorage data seamlessly.

🚀 Zero downtime, 100% backward compatibility guaranteed!
`;
};

// Console logger for migration status
export const logMigrationStatus = async (): Promise<void> => {
  console.log('🔍 Checking AROUZ MARKET Categories Migration Status...');
  
  const status = await checkMigrationStatus();
  
  if (status.isMigrated) {
    console.log('✅ Migration Status: COMPLETED');
    console.log(`📊 Categories: ${status.categoriesCount}`);
    console.log(`📊 Subcategories: ${status.subcategoriesCount}`);
    console.log('🚀 Using Supabase categories system');
  } else {
    console.log('⚠️ Migration Status: NOT COMPLETED');
    console.log('📦 Using localStorage fallback');
    if (status.error) {
      console.log(`❌ Error: ${status.error}`);
    }
  }
  
  // Verify integrity if migrated
  if (status.isMigrated) {
    const integrity = await verifyMigrationIntegrity();
    if (integrity.isValid) {
      console.log('✅ Data Integrity: VERIFIED');
    } else {
      console.log('⚠️ Data Integrity: ISSUES FOUND');
      integrity.issues.forEach(issue => console.log(`   - ${issue}`));
    }
  }
};
