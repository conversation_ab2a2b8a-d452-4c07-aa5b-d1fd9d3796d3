/**
 * Product ID Migration Utility
 * 
 * This utility migrates existing products from the old ID format (TYR-100001)
 * to the new format with marketplace section suffixes (TYR-100001-WHOLESALE/RETAIL).
 */

import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { isValidProductId } from '@/features/products/utils/idGenerator';

/**
 * Migrate a single product ID to the new format
 * @param product The product to migrate
 * @param defaultMarketplaceSection The default marketplace section to assign
 * @returns The product with updated ID
 */
export const migrateProductId = (
  product: TyreProduct | BrakeProduct,
  defaultMarketplaceSection: 'wholesale' | 'retail' = 'retail'
): TyreProduct | BrakeProduct => {
  // If the product already has the new format, return as-is
  if (isValidProductId(product.id) && (product.id.endsWith('-WHOLESALE') || product.id.endsWith('-RETAIL'))) {
    return product;
  }

  // Determine marketplace section based on product properties or default
  let marketplaceSection = defaultMarketplaceSection;

  // Check if product has marketplace section property
  if ('marketplaceSection' in product && product.marketplaceSection) {
    marketplaceSection = product.marketplaceSection;
  } else {
    // Determine based on pricing structure
    if (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0 && !product.retailPrice) {
      marketplaceSection = 'wholesale';
    } else if (product.retailPrice && !product.wholesalePricingTiers) {
      marketplaceSection = 'retail';
    }
  }

  // Create the new ID with suffix
  const suffix = marketplaceSection === 'wholesale' ? 'WHOLESALE' : 'RETAIL';
  const newId = `${product.id}-${suffix}`;

  // Return the product with updated ID and marketplace section
  return {
    ...product,
    id: newId,
    marketplaceSection,
  };
};

/**
 * Migrate all products in a category from localStorage
 * @param categoryId The category to migrate ('tyres' or 'brakes')
 * @param defaultMarketplaceSection The default marketplace section for products without clear indicators
 * @returns The number of products migrated
 */
export const migrateProductsInCategory = (
  categoryId: string,
  defaultMarketplaceSection: 'wholesale' | 'retail' = 'retail'
): number => {
  const storageKey = `products-${categoryId}`;
  const storedData = localStorage.getItem(storageKey);

  if (!storedData) {
    console.log(`No products found in localStorage for category: ${categoryId}`);
    return 0;
  }

  try {
    let products: (TyreProduct | BrakeProduct)[] = JSON.parse(storedData);
    
    // Convert string dates back to Date objects
    products = products.map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      inventoryUpdateDate: item.inventoryUpdateDate ? new Date(item.inventoryUpdateDate) : undefined
    }));

    let migratedCount = 0;
    const migratedProducts = products.map(product => {
      const originalId = product.id;
      const migratedProduct = migrateProductId(product, defaultMarketplaceSection);
      
      if (migratedProduct.id !== originalId) {
        migratedCount++;
        console.log(`Migrated product ID: ${originalId} → ${migratedProduct.id}`);
      }
      
      return migratedProduct;
    });

    // Save the migrated products back to localStorage
    localStorage.setItem(storageKey, JSON.stringify(migratedProducts));
    
    console.log(`Migration completed for category ${categoryId}: ${migratedCount} products migrated`);
    return migratedCount;
  } catch (error) {
    console.error(`Error migrating products in category ${categoryId}:`, error);
    return 0;
  }
};

/**
 * Migrate all products across all categories
 * @param supplierMarketplaceSection The marketplace section to assign to supplier-like products
 * @param merchantMarketplaceSection The marketplace section to assign to merchant-like products
 * @returns Object with migration results for each category
 */
export const migrateAllProducts = (
  supplierMarketplaceSection: 'wholesale' | 'retail' = 'wholesale',
  merchantMarketplaceSection: 'wholesale' | 'retail' = 'retail'
): { tyres: number; brakes: number; total: number } => {
  console.log('Starting product ID migration for all categories...');

  // For this migration, we'll assign wholesale to products that look like supplier products
  // and retail to products that look like merchant products
  const tyresMigrated = migrateProductsInCategory('tyres', supplierMarketplaceSection);
  const brakesMigrated = migrateProductsInCategory('brakes', supplierMarketplaceSection);

  const total = tyresMigrated + brakesMigrated;

  console.log('Migration summary:', {
    tyres: tyresMigrated,
    brakes: brakesMigrated,
    total
  });

  return {
    tyres: tyresMigrated,
    brakes: brakesMigrated,
    total
  };
};

/**
 * Smart migration that assigns marketplace sections based on product characteristics
 * @param categoryId The category to migrate
 * @returns The number of products migrated
 */
export const smartMigrateProductsInCategory = (categoryId: string): number => {
  const storageKey = `products-${categoryId}`;
  const storedData = localStorage.getItem(storageKey);

  if (!storedData) {
    console.log(`No products found in localStorage for category: ${categoryId}`);
    return 0;
  }

  try {
    let products: (TyreProduct | BrakeProduct)[] = JSON.parse(storedData);
    
    // Convert string dates back to Date objects
    products = products.map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      inventoryUpdateDate: item.inventoryUpdateDate ? new Date(item.inventoryUpdateDate) : undefined
    }));

    let migratedCount = 0;
    const migratedProducts = products.map((product, index) => {
      const originalId = product.id;
      
      // Skip if already migrated
      if (originalId.endsWith('-WHOLESALE') || originalId.endsWith('-RETAIL')) {
        return product;
      }

      // Smart assignment: alternate between wholesale and retail, or use product characteristics
      let marketplaceSection: 'wholesale' | 'retail';
      
      // Check if product has clear indicators
      if (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0 && !product.retailPrice) {
        marketplaceSection = 'wholesale';
      } else if (product.retailPrice && !product.wholesalePricingTiers) {
        marketplaceSection = 'retail';
      } else {
        // For products with both or neither, alternate based on index
        // First half wholesale, second half retail
        marketplaceSection = index < products.length / 2 ? 'wholesale' : 'retail';
      }

      const migratedProduct = migrateProductId(product, marketplaceSection);
      
      if (migratedProduct.id !== originalId) {
        migratedCount++;
        console.log(`Smart migrated product ID: ${originalId} → ${migratedProduct.id} (${marketplaceSection})`);
      }
      
      return migratedProduct;
    });

    // Save the migrated products back to localStorage
    localStorage.setItem(storageKey, JSON.stringify(migratedProducts));
    
    console.log(`Smart migration completed for category ${categoryId}: ${migratedCount} products migrated`);
    return migratedCount;
  } catch (error) {
    console.error(`Error in smart migration for category ${categoryId}:`, error);
    return 0;
  }
};

/**
 * Smart migration for all categories
 * @returns Object with migration results for each category
 */
export const smartMigrateAllProducts = (): { tyres: number; brakes: number; total: number } => {
  console.log('Starting smart product ID migration for all categories...');

  const tyresMigrated = smartMigrateProductsInCategory('tyres');
  const brakesMigrated = smartMigrateProductsInCategory('brakes');

  const total = tyresMigrated + brakesMigrated;

  console.log('Smart migration summary:', {
    tyres: tyresMigrated,
    brakes: brakesMigrated,
    total
  });

  return {
    tyres: tyresMigrated,
    brakes: brakesMigrated,
    total
  };
};

/**
 * Check migration status for all categories
 * @returns Object with migration status for each category
 */
export const checkMigrationStatus = (): {
  tyres: { total: number; migrated: number; needsMigration: number };
  brakes: { total: number; migrated: number; needsMigration: number };
} => {
  const checkCategory = (categoryId: string) => {
    const storageKey = `products-${categoryId}`;
    const storedData = localStorage.getItem(storageKey);

    if (!storedData) {
      return { total: 0, migrated: 0, needsMigration: 0 };
    }

    try {
      const products: (TyreProduct | BrakeProduct)[] = JSON.parse(storedData);
      const total = products.length;
      const migrated = products.filter(p => 
        p.id.endsWith('-WHOLESALE') || p.id.endsWith('-RETAIL')
      ).length;
      const needsMigration = total - migrated;

      return { total, migrated, needsMigration };
    } catch (error) {
      console.error(`Error checking migration status for ${categoryId}:`, error);
      return { total: 0, migrated: 0, needsMigration: 0 };
    }
  };

  return {
    tyres: checkCategory('tyres'),
    brakes: checkCategory('brakes'),
  };
};
