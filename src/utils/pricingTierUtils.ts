import { PricingTier } from '@/features/products/types/product.types';

/**
 * Validation result for pricing tiers
 */
export interface TierValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates that pricing tiers follow non-overlapping structure
 * Rules:
 * 1. First tier must start at 1
 * 2. Each tier must start exactly where the previous tier ends + 1
 * 3. No gaps between tiers allowed
 * 4. No overlapping ranges allowed
 * 5. Prices should generally decrease as quantity increases (warning only)
 */
export const validatePricingTiers = (tiers: PricingTier[]): TierValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!tiers || tiers.length === 0) {
    return { isValid: true, errors, warnings };
  }

  // Sort tiers by minQuantity to validate structure
  const sortedTiers = [...tiers].sort((a, b) => a.minQuantity - b.minQuantity);

  // Check first tier starts at 1
  if (sortedTiers[0].minQuantity !== 1) {
    errors.push('First pricing tier must start at quantity 1');
  }

  // Check for gaps and overlaps
  for (let i = 0; i < sortedTiers.length - 1; i++) {
    const currentTier = sortedTiers[i];
    const nextTier = sortedTiers[i + 1];

    // Current tier must have maxQuantity defined (except for last tier)
    if (currentTier.maxQuantity === undefined) {
      errors.push(`Tier ${i + 1} (${currentTier.minQuantity}+) must have a maximum quantity since it's not the last tier`);
      continue;
    }

    // Next tier must start exactly where current tier ends + 1
    const expectedNextStart = currentTier.maxQuantity + 1;
    if (nextTier.minQuantity !== expectedNextStart) {
      if (nextTier.minQuantity < expectedNextStart) {
        errors.push(`Tier ${i + 2} overlaps with tier ${i + 1}. Tier ${i + 2} should start at ${expectedNextStart}, not ${nextTier.minQuantity}`);
      } else {
        errors.push(`Gap between tier ${i + 1} and tier ${i + 2}. Tier ${i + 2} should start at ${expectedNextStart}, not ${nextTier.minQuantity}`);
      }
    }
  }

  // Check for invalid quantity ranges within tiers
  for (let i = 0; i < sortedTiers.length; i++) {
    const tier = sortedTiers[i];
    
    if (tier.minQuantity <= 0) {
      errors.push(`Tier ${i + 1}: Minimum quantity must be greater than 0`);
    }

    if (tier.maxQuantity !== undefined && tier.maxQuantity <= tier.minQuantity) {
      errors.push(`Tier ${i + 1}: Maximum quantity (${tier.maxQuantity}) must be greater than minimum quantity (${tier.minQuantity})`);
    }

    if (tier.price <= 0) {
      errors.push(`Tier ${i + 1}: Price must be greater than 0`);
    }
  }

  // Check for price progression (warning only)
  for (let i = 0; i < sortedTiers.length - 1; i++) {
    const currentTier = sortedTiers[i];
    const nextTier = sortedTiers[i + 1];

    if (nextTier.price >= currentTier.price) {
      warnings.push(`Tier ${i + 2} price (${nextTier.price}) should typically be lower than tier ${i + 1} price (${currentTier.price}) for volume discounts`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Auto-suggests the next tier based on existing tiers
 */
export const suggestNextTier = (existingTiers: PricingTier[]): Partial<PricingTier> => {
  if (!existingTiers || existingTiers.length === 0) {
    return { minQuantity: 1, price: 0 };
  }

  const sortedTiers = [...existingTiers].sort((a, b) => a.minQuantity - b.minQuantity);
  const lastTier = sortedTiers[sortedTiers.length - 1];

  if (lastTier.maxQuantity === undefined) {
    // Last tier is open-ended, suggest a specific range
    const suggestedMin = lastTier.minQuantity + 50; // Reasonable gap
    return {
      minQuantity: suggestedMin,
      maxQuantity: suggestedMin + 49,
      price: Math.max(0, lastTier.price * 0.9) // 10% discount
    };
  } else {
    // Last tier has a max, suggest next tier
    return {
      minQuantity: lastTier.maxQuantity + 1,
      price: Math.max(0, lastTier.price * 0.9) // 10% discount
    };
  }
};

/**
 * Calculates the correct price for a given quantity based on pricing tiers
 */
export const calculatePriceForQuantity = (quantity: number, tiers: PricingTier[]): number => {
  if (!tiers || tiers.length === 0) {
    return 0;
  }

  // Find the applicable tier
  const applicableTier = tiers.find(tier => {
    const meetsMin = quantity >= tier.minQuantity;
    const meetsMax = tier.maxQuantity === undefined || quantity <= tier.maxQuantity;
    return meetsMin && meetsMax;
  });

  return applicableTier ? applicableTier.price : tiers[0].price;
};

/**
 * Gets the tier that applies to a given quantity
 */
export const getTierForQuantity = (quantity: number, tiers: PricingTier[]): PricingTier | null => {
  if (!tiers || tiers.length === 0) {
    return null;
  }

  return tiers.find(tier => {
    const meetsMin = quantity >= tier.minQuantity;
    const meetsMax = tier.maxQuantity === undefined || quantity <= tier.maxQuantity;
    return meetsMin && meetsMax;
  }) || null;
};

/**
 * Formats a pricing tier for display
 */
export const formatTierDisplay = (tier: PricingTier): string => {
  if (tier.maxQuantity === undefined) {
    return `From ${tier.minQuantity} pieces and above → ${tier.price.toFixed(2)} DZD per piece`;
  } else {
    return `From ${tier.minQuantity} to ${tier.maxQuantity} pieces → ${tier.price.toFixed(2)} DZD per piece`;
  }
};

/**
 * Formats all pricing tiers for display
 */
export const formatAllTiersDisplay = (tiers: PricingTier[]): string[] => {
  if (!tiers || tiers.length === 0) {
    return [];
  }

  const sortedTiers = [...tiers].sort((a, b) => a.minQuantity - b.minQuantity);
  return sortedTiers.map(formatTierDisplay);
};
