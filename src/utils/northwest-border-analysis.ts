/**
 * NORTHWEST BORDER ANALYSIS UTILITY
 * 
 * This utility analyzes the specific coordinate 34.834096, -1.670609
 * to determine which wilaya it should actually belong to based on
 * distances to wilaya centers and boundary analysis.
 */

// Wilaya centers for northwest region
const NORTHWEST_WILAYAS = {
  "13": { name: "<PERSON><PERSON><PERSON><PERSON>", center: { lat: 34.8786, lng: -1.3150 } },
  "22": { name: "<PERSON><PERSON>", center: { lat: 35.1977, lng: -0.6388 } },
  "45": { name: "<PERSON><PERSON><PERSON>", center: { lat: 33.2667, lng: -0.3167 } },
  "20": { name: "<PERSON><PERSON><PERSON>", center: { lat: 34.8370, lng: 0.1514 } },
  "14": { name: "Tiar<PERSON>", center: { lat: 35.3712, lng: 1.3170 } }
};

/**
 * Calculate distance between two coordinates using Haversine formula
 */
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Analyze the problematic coordinate to determine correct wilaya
 */
export function analyzeNorthwestCoordinate(lat: number, lng: number) {
  console.log(`🔍 ANALYZING COORDINATE: ${lat}, ${lng}`);
  console.log(`📍 This is the coordinate that was incorrectly detected as Tlemcen`);
  
  const distances: Array<{
    wilaya: string;
    distance: number;
    code: string;
  }> = [];

  // Calculate distances to all northwest wilayas
  for (const [code, wilaya] of Object.entries(NORTHWEST_WILAYAS)) {
    const distance = calculateDistance(lat, lng, wilaya.center.lat, wilaya.center.lng);
    distances.push({
      wilaya: wilaya.name,
      distance: distance,
      code: code
    });
  }

  // Sort by distance
  distances.sort((a, b) => a.distance - b.distance);

  console.log(`\n📊 DISTANCE ANALYSIS:`);
  distances.forEach((item, index) => {
    const rank = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
    console.log(`   ${rank} ${item.wilaya}: ${item.distance.toFixed(2)} km`);
  });

  const closest = distances[0];
  console.log(`\n🎯 CONCLUSION: Closest wilaya is ${closest.wilaya} (${closest.distance.toFixed(2)} km)`);
  
  // Special analysis for this coordinate
  console.log(`\n🔍 DETAILED ANALYSIS:`);
  console.log(`   - Longitude ${lng} is significantly WEST of Tlemcen center (${NORTHWEST_WILAYAS["13"].center.lng})`);
  console.log(`   - Difference: ${(lng - NORTHWEST_WILAYAS["13"].center.lng).toFixed(3)} degrees west`);
  console.log(`   - This suggests the coordinate is NOT in Tlemcen wilaya`);
  
  if (closest.wilaya !== "Tlemcen") {
    console.log(`   ✅ Distance analysis confirms: Should be ${closest.wilaya}, NOT Tlemcen`);
  } else {
    console.log(`   ⚠️ Distance analysis suggests Tlemcen, but longitude suggests otherwise`);
  }

  return {
    coordinates: { lat, lng },
    closestWilaya: closest.wilaya,
    closestDistance: closest.distance,
    allDistances: distances,
    recommendation: closest.wilaya !== "Tlemcen" ? closest.wilaya : "Needs further investigation"
  };
}

/**
 * Test the specific problematic coordinate
 */
export function testProblematicCoordinate() {
  const PROBLEMATIC_COORD = { lat: 34.834096, lng: -1.670609 };
  
  console.log(`🚨 TESTING PROBLEMATIC COORDINATE`);
  console.log(`📍 Coordinate: ${PROBLEMATIC_COORD.lat}, ${PROBLEMATIC_COORD.lng}`);
  console.log(`🐛 Issue: Currently detected as Tlemcen (INCORRECT)`);
  
  return analyzeNorthwestCoordinate(PROBLEMATIC_COORD.lat, PROBLEMATIC_COORD.lng);
}

/**
 * Comprehensive northwest border testing
 */
export function testNorthwestBorders() {
  console.log(`🧪 COMPREHENSIVE NORTHWEST BORDER TESTING`);
  
  const testCoordinates = [
    { name: "Problematic coordinate", lat: 34.834096, lng: -1.670609 },
    { name: "Tlemcen center", lat: 34.8786, lng: -1.3150 },
    { name: "Sidi Bel Abbès center", lat: 35.1977, lng: -0.6388 },
    { name: "Naâma center", lat: 33.2667, lng: -0.3167 },
    { name: "West of Tlemcen", lat: 34.8, lng: -2.0 },
    { name: "Border area 1", lat: 34.5, lng: -1.5 },
    { name: "Border area 2", lat: 35.0, lng: -1.0 }
  ];

  const results = testCoordinates.map(coord => ({
    name: coord.name,
    analysis: analyzeNorthwestCoordinate(coord.lat, coord.lng)
  }));

  console.log(`\n📋 SUMMARY OF NORTHWEST BORDER TESTS:`);
  results.forEach(result => {
    console.log(`   ${result.name}: Should be ${result.analysis.recommendation}`);
  });

  return results;
}
