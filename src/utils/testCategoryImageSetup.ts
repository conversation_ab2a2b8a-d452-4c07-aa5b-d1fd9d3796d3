/**
 * Test Category Image Setup
 * Use this to verify that the category-images bucket is working correctly
 */

import { getCategoryImageUrl, getSubcategoryImageUrl } from '@/data/categoryData';
import { getFallbackImageUrl } from '@/services/categoryImageService';

/**
 * Test all category image URLs
 */
export const testCategoryImageUrls = () => {
  console.log('=== TESTING CATEGORY IMAGE URLS ===');
  
  // Test category URLs
  console.log('\nCATEGORY URLS:');
  console.log('Tyres:', getCategoryImageUrl('tyres'));
  console.log('Brakes:', getCategoryImageUrl('brakes'));
  
  // Test subcategory URLs (sample)
  console.log('\nSUBCATEGORY URLS (sample):');
  console.log('Tyres subcategory:', getSubcategoryImageUrl('tyres'));
  console.log('Brake Pads:', getSubcategoryImageUrl('brake-pads'));
  console.log('Brake Discs:', getSubcategoryImageUrl('brake-discs'));
  
  // Test fallback URLs
  console.log('\nFALLBACK URLS:');
  console.log('Category fallback:', getFallbackImageUrl('category'));
  console.log('Subcategory fallback:', getFallbackImageUrl('subcategory'));
};

/**
 * Test image loading in the browser
 */
export const testImageLoading = async () => {
  console.log('=== TESTING IMAGE LOADING ===');
  
  const testUrls = [
    { type: 'category', id: 'tyres', url: getCategoryImageUrl('tyres') },
    { type: 'category', id: 'brakes', url: getCategoryImageUrl('brakes') },
    { type: 'subcategory', id: 'brake-pads', url: getSubcategoryImageUrl('brake-pads') },
    { type: 'fallback-category', id: 'fallback', url: getFallbackImageUrl('category') },
    { type: 'fallback-subcategory', id: 'fallback', url: getFallbackImageUrl('subcategory') }
  ];
  
  for (const test of testUrls) {
    try {
      const response = await fetch(test.url, { method: 'HEAD' });
      const status = response.ok ? '✅ LOADED' : '❌ FAILED';
      console.log(`${status} ${test.type} (${test.id}): ${response.status} - ${test.url}`);
    } catch (error) {
      console.log(`❌ ERROR ${test.type} (${test.id}): ${error} - ${test.url}`);
    }
  }
};

/**
 * Test bucket access and permissions
 */
export const testBucketAccess = async () => {
  console.log('=== TESTING BUCKET ACCESS ===');
  
  try {
    // Test if we can access the bucket by trying to load a category image
    const testUrl = getCategoryImageUrl('tyres');
    const response = await fetch(testUrl, { method: 'HEAD' });
    
    if (response.ok) {
      console.log('✅ Bucket is accessible and public');
      console.log('✅ RLS policies are working correctly');
    } else if (response.status === 404) {
      console.log('⚠️  Bucket exists but image not found (expected if no images uploaded yet)');
      console.log('✅ This means the bucket setup is working correctly');
    } else {
      console.log('❌ Bucket access issue:', response.status, response.statusText);
    }
  } catch (error) {
    console.log('❌ Bucket access error:', error);
  }
};

/**
 * Run all tests
 */
export const runAllTests = async () => {
  console.log('🧪 CATEGORY IMAGE SETUP TESTS');
  console.log('==============================');
  
  testCategoryImageUrls();
  await testImageLoading();
  await testBucketAccess();
  
  console.log('\n=== TEST COMPLETE ===');
  console.log('If you see ✅ or ⚠️ messages, the setup is working correctly.');
  console.log('❌ messages indicate issues that need to be resolved.');
};

/**
 * Quick test function for browser console
 */
export const quickTest = () => {
  console.log('🚀 Quick Category Image Test');
  testCategoryImageUrls();
  console.log('\nTo run full tests including network checks, use: runAllTests()');
};

// Export for easy browser console access
if (typeof window !== 'undefined') {
  (window as any).testCategoryImages = {
    quickTest,
    runAllTests,
    testCategoryImageUrls,
    testImageLoading,
    testBucketAccess
  };
}
