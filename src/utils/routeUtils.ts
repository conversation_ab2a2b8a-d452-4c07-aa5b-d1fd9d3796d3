/**
 * Route Utilities for AROUZ MARKET
 * 
 * Production-ready utilities for route detection and navigation control
 * Used to prevent consumer modals from appearing on admin/business pages
 */

/**
 * Admin and business route patterns that should NOT show consumer authentication modals
 */
const ADMIN_ROUTE_PATTERNS = [
  // Main admin panel routes
  '/admin',
  '/admin/',
  '/admin/login',
  '/admin/dashboard',
  '/admin/orders',
  '/admin/shipping-companies',
  '/admin/shipments',
  '/admin/users',
  '/admin/analytics',
  '/admin/settings',
  
  // Business dashboard routes (suppliers/merchants)
  '/app',
  '/app/',
  '/app/dashboard',
  '/app/products',
  '/app/products-table',
  '/app/orders',
  '/app/settings',
  '/app/profile',
  '/app/shipping',
  
  // Shipping company routes
  '/shipping',
  '/shipping/',
  '/shipping/login',
  '/shipping/dashboard',
  '/shipping/orders',
  '/shipping/shipments',
  '/shipping/tracking',
  '/shipping/analytics',
  '/shipping/settings',
  
  // Authentication routes (don't show consumer modal on auth pages)
  '/login',
  '/signup',
  '/auth',
  '/register',
  '/forgot-password',
  '/reset-password',

  // Partner registration page (business authentication only)
  '/partners',

  // API and system routes
  '/api',
  '/health',
  '/status'
];

/**
 * Consumer marketplace routes where AutoAuthModal SHOULD appear
 */
const CONSUMER_ROUTE_PATTERNS = [
  '/', // Homepage/marketplace
  '/my-vehicle-parts',
  '/wholesale-offers',
  '/wishlist',
  '/my-reviews',
  '/my-orders',
  '/basket',
  '/checkout',
  '/product',
  '/category',
  '/search',
  '/about',
  '/contact',
  '/help',
  '/faq'
];

/**
 * Check if current route is an admin/business route where consumer modals should NOT appear
 * @param pathname - Current window.location.pathname
 * @returns true if route is admin/business, false if consumer marketplace
 */
export function isAdminOrBusinessRoute(pathname: string): boolean {
  // Normalize pathname
  const normalizedPath = pathname.toLowerCase().trim();
  
  // Check exact matches and prefix matches
  return ADMIN_ROUTE_PATTERNS.some(pattern => {
    // Exact match
    if (normalizedPath === pattern) {
      return true;
    }
    
    // Prefix match (for routes like /app/products/123)
    if (pattern.endsWith('/') && normalizedPath.startsWith(pattern)) {
      return true;
    }
    
    // Prefix match without trailing slash
    if (!pattern.endsWith('/') && normalizedPath.startsWith(pattern + '/')) {
      return true;
    }
    
    return false;
  });
}

/**
 * Check if current route is a consumer marketplace route where modals SHOULD appear
 * @param pathname - Current window.location.pathname
 * @returns true if route is consumer marketplace, false otherwise
 */
export function isConsumerMarketplaceRoute(pathname: string): boolean {
  // If it's an admin route, definitely not consumer
  if (isAdminOrBusinessRoute(pathname)) {
    return false;
  }
  
  const normalizedPath = pathname.toLowerCase().trim();
  
  // Check if it matches consumer patterns
  const isConsumerRoute = CONSUMER_ROUTE_PATTERNS.some(pattern => {
    if (normalizedPath === pattern) {
      return true;
    }
    
    if (pattern.endsWith('/') && normalizedPath.startsWith(pattern)) {
      return true;
    }
    
    if (!pattern.endsWith('/') && normalizedPath.startsWith(pattern + '/')) {
      return true;
    }
    
    return false;
  });
  
  // Also check for dynamic product routes (/:productId)
  if (!isConsumerRoute) {
    // Product ID routes (like /TYR-123456)
    const productIdPattern = /^\/[A-Z]{3,4}-[A-Z0-9]+$/i;
    if (productIdPattern.test(normalizedPath)) {
      return true;
    }
  }
  
  return isConsumerRoute;
}

/**
 * Get the current route type for debugging and logging
 * @param pathname - Current window.location.pathname
 * @returns Route type classification
 */
export function getRouteType(pathname: string): 'admin' | 'business' | 'shipping' | 'consumer' | 'auth' | 'api' | 'unknown' {
  const normalizedPath = pathname.toLowerCase().trim();
  
  if (normalizedPath.startsWith('/admin')) {
    return 'admin';
  }
  
  if (normalizedPath.startsWith('/app')) {
    return 'business';
  }
  
  if (normalizedPath.startsWith('/shipping')) {
    return 'shipping';
  }
  
  if (normalizedPath.startsWith('/login') || normalizedPath.startsWith('/signup') || 
      normalizedPath.startsWith('/auth') || normalizedPath.startsWith('/register')) {
    return 'auth';
  }
  
  if (normalizedPath.startsWith('/api')) {
    return 'api';
  }
  
  if (isConsumerMarketplaceRoute(pathname)) {
    return 'consumer';
  }
  
  return 'unknown';
}

/**
 * Hook to get current route information
 * @returns Object with route detection utilities
 */
export function useRouteInfo() {
  const pathname = window.location.pathname;
  
  return {
    pathname,
    isAdminRoute: isAdminOrBusinessRoute(pathname),
    isConsumerRoute: isConsumerMarketplaceRoute(pathname),
    routeType: getRouteType(pathname),
    shouldShowConsumerModal: isConsumerMarketplaceRoute(pathname)
  };
}

/**
 * Debug function to log route information (for development)
 * @param pathname - Current pathname
 */
export function debugRouteInfo(pathname: string) {
  console.log('🛣️ Route Debug Info:', {
    pathname,
    isAdmin: isAdminOrBusinessRoute(pathname),
    isConsumer: isConsumerMarketplaceRoute(pathname),
    routeType: getRouteType(pathname),
    shouldShowModal: isConsumerMarketplaceRoute(pathname)
  });
}
