/**
 * AROUZ MARKET - Production Monitoring & Analytics
 * 
 * This module provides comprehensive monitoring, error tracking, and analytics
 * for production deployment without external dependencies.
 */

// Environment configuration
const isProduction = import.meta.env.VITE_APP_ENV === 'production';
const enableErrorTracking = import.meta.env.VITE_ENABLE_ERROR_TRACKING === 'true';
const enablePerformanceMonitoring = import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true';
const enableUserAnalytics = import.meta.env.VITE_ENABLE_USER_ANALYTICS === 'true';

// Error tracking interface
interface ErrorReport {
  message: string;
  stack?: string;
  url: string;
  userAgent: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  buildVersion: string;
  errorType: 'javascript' | 'network' | 'performance' | 'user';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

// Performance metrics interface
interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
  firstInputDelay?: number;
  timestamp: number;
  url: string;
  sessionId: string;
  userAgent: string;
}

// User analytics interface
interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  sessionId: string;
  userId?: string;
  url: string;
  userAgent: string;
  customData?: Record<string, any>;
}

class MonitoringService {
  private sessionId: string;
  private buildVersion: string;
  private errorQueue: ErrorReport[] = [];
  private performanceQueue: PerformanceMetrics[] = [];
  private analyticsQueue: AnalyticsEvent[] = [];
  private flushInterval: number = 30000; // 30 seconds

  constructor() {
    this.sessionId = this.generateSessionId();
    this.buildVersion = this.getBuildVersion();
    
    if (isProduction) {
      this.initializeMonitoring();
    }
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getBuildVersion(): string {
    return import.meta.env.VITE_BUILD_VERSION || 'unknown';
  }

  private initializeMonitoring(): void {
    // Global error handler
    if (enableErrorTracking) {
      window.addEventListener('error', (event) => {
        this.reportError({
          message: event.message,
          stack: event.error?.stack,
          url: event.filename || window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          sessionId: this.sessionId,
          buildVersion: this.buildVersion,
          errorType: 'javascript',
          severity: 'high',
          context: {
            lineno: event.lineno,
            colno: event.colno
          }
        });
      });

      // Unhandled promise rejection handler
      window.addEventListener('unhandledrejection', (event) => {
        this.reportError({
          message: `Unhandled Promise Rejection: ${event.reason}`,
          stack: event.reason?.stack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          sessionId: this.sessionId,
          buildVersion: this.buildVersion,
          errorType: 'javascript',
          severity: 'critical',
          context: {
            reason: event.reason
          }
        });
      });
    }

    // Performance monitoring
    if (enablePerformanceMonitoring) {
      this.initializePerformanceMonitoring();
    }

    // Periodic flush
    setInterval(() => {
      this.flushQueues();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flushQueues();
    });
  }

  private initializePerformanceMonitoring(): void {
    // Wait for page load to complete
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics: PerformanceMetrics = {
            pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
            timestamp: Date.now(),
            url: window.location.href,
            sessionId: this.sessionId,
            userAgent: navigator.userAgent
          };

          // Add Web Vitals if available
          if ('PerformanceObserver' in window) {
            this.observeWebVitals(metrics);
          }

          this.reportPerformance(metrics);
        }
      }, 1000);
    });
  }

  private observeWebVitals(metrics: PerformanceMetrics): void {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      if (entries.length > 0) {
        metrics.firstContentfulPaint = entries[0].startTime;
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      if (entries.length > 0) {
        metrics.largestContentfulPaint = entries[entries.length - 1].startTime;
      }
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    new PerformanceObserver((list) => {
      let clsValue = 0;
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      metrics.cumulativeLayoutShift = clsValue;
    }).observe({ entryTypes: ['layout-shift'] });

    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      if (entries.length > 0) {
        metrics.firstInputDelay = (entries[0] as any).processingStart - entries[0].startTime;
      }
    }).observe({ entryTypes: ['first-input'] });
  }

  public reportError(error: Partial<ErrorReport>): void {
    if (!enableErrorTracking) return;

    const fullError: ErrorReport = {
      message: error.message || 'Unknown error',
      stack: error.stack,
      url: error.url || window.location.href,
      userAgent: error.userAgent || navigator.userAgent,
      timestamp: error.timestamp || Date.now(),
      sessionId: error.sessionId || this.sessionId,
      buildVersion: error.buildVersion || this.buildVersion,
      errorType: error.errorType || 'javascript',
      severity: error.severity || 'medium',
      context: error.context
    };

    this.errorQueue.push(fullError);

    // Immediate flush for critical errors
    if (fullError.severity === 'critical') {
      this.flushQueues();
    }
  }

  public reportPerformance(metrics: PerformanceMetrics): void {
    if (!enablePerformanceMonitoring) return;
    this.performanceQueue.push(metrics);
  }

  public trackEvent(event: Partial<AnalyticsEvent>): void {
    if (!enableUserAnalytics) return;

    const fullEvent: AnalyticsEvent = {
      event: event.event || 'unknown',
      category: event.category || 'general',
      action: event.action || 'unknown',
      label: event.label,
      value: event.value,
      timestamp: event.timestamp || Date.now(),
      sessionId: event.sessionId || this.sessionId,
      userId: event.userId,
      url: event.url || window.location.href,
      userAgent: event.userAgent || navigator.userAgent,
      customData: event.customData
    };

    this.analyticsQueue.push(fullEvent);
  }

  private async flushQueues(): Promise<void> {
    if (!isProduction) return;

    const promises: Promise<void>[] = [];

    // Flush errors
    if (this.errorQueue.length > 0) {
      promises.push(this.sendErrors([...this.errorQueue]));
      this.errorQueue = [];
    }

    // Flush performance metrics
    if (this.performanceQueue.length > 0) {
      promises.push(this.sendPerformanceMetrics([...this.performanceQueue]));
      this.performanceQueue = [];
    }

    // Flush analytics
    if (this.analyticsQueue.length > 0) {
      promises.push(this.sendAnalytics([...this.analyticsQueue]));
      this.analyticsQueue = [];
    }

    try {
      await Promise.all(promises);
    } catch (error) {
      console.error('Failed to flush monitoring data:', error);
    }
  }

  private async sendErrors(errors: ErrorReport[]): Promise<void> {
    try {
      // Store in localStorage as fallback
      const existingErrors = JSON.parse(localStorage.getItem('arouz_errors') || '[]');
      const allErrors = [...existingErrors, ...errors].slice(-100); // Keep last 100 errors
      localStorage.setItem('arouz_errors', JSON.stringify(allErrors));

      // In a real implementation, you would send to your monitoring service
      console.warn('Errors logged:', errors.length);
    } catch (error) {
      console.error('Failed to send error reports:', error);
    }
  }

  private async sendPerformanceMetrics(metrics: PerformanceMetrics[]): Promise<void> {
    try {
      // Store in localStorage as fallback
      const existingMetrics = JSON.parse(localStorage.getItem('arouz_performance') || '[]');
      const allMetrics = [...existingMetrics, ...metrics].slice(-50); // Keep last 50 metrics
      localStorage.setItem('arouz_performance', JSON.stringify(allMetrics));

      console.info('Performance metrics logged:', metrics.length);
    } catch (error) {
      console.error('Failed to send performance metrics:', error);
    }
  }

  private async sendAnalytics(events: AnalyticsEvent[]): Promise<void> {
    try {
      // Store in localStorage as fallback
      const existingEvents = JSON.parse(localStorage.getItem('arouz_analytics') || '[]');
      const allEvents = [...existingEvents, ...events].slice(-200); // Keep last 200 events
      localStorage.setItem('arouz_analytics', JSON.stringify(allEvents));

      console.info('Analytics events logged:', events.length);
    } catch (error) {
      console.error('Failed to send analytics:', error);
    }
  }

  // Public methods for manual tracking
  public trackPageView(page: string): void {
    this.trackEvent({
      event: 'page_view',
      category: 'navigation',
      action: 'view',
      label: page
    });
  }

  public trackUserAction(action: string, category: string = 'user', label?: string, value?: number): void {
    this.trackEvent({
      event: 'user_action',
      category,
      action,
      label,
      value
    });
  }

  public trackError(error: Error, context?: Record<string, any>): void {
    this.reportError({
      message: error.message,
      stack: error.stack,
      errorType: 'javascript',
      severity: 'medium',
      context
    });
  }
}

// Create singleton instance
export const monitoring = new MonitoringService();

// Export convenience functions
export const trackPageView = (page: string) => monitoring.trackPageView(page);
export const trackUserAction = (action: string, category?: string, label?: string, value?: number) => 
  monitoring.trackUserAction(action, category, label, value);
export const trackError = (error: Error, context?: Record<string, any>) => 
  monitoring.trackError(error, context);
export const reportError = (error: Partial<ErrorReport>) => monitoring.reportError(error);
export const reportPerformance = (metrics: PerformanceMetrics) => monitoring.reportPerformance(metrics);

// Initialize monitoring
if (isProduction) {
  console.info('AROUZ MARKET Monitoring initialized');
}
