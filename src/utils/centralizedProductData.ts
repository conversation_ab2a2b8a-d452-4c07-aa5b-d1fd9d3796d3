import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { generateProductIdForIndex } from '@/features/products/utils/idGenerator';

/**
 * Centralized Product Data Source
 *
 * This file provides a single source of truth for all product data used across
 * the application, ensuring consistency between marketplace displays and product lookups.
 */

// Helper function to generate random pricing tiers
const generatePricingTiers = () => {
  const basePrice = 50 + Math.random() * 150;
  return [
    { minQuantity: 1, maxQuantity: 10, price: basePrice },
    { minQuantity: 11, maxQuantity: 50, price: basePrice * 0.9 },
    { minQuantity: 51, price: basePrice * 0.8 }
  ];
};

// Helper function to generate random status
const generateStatus = () => {
  const statuses = ['active', 'draft', 'pending_approval', 'out_of_stock', 'discontinued'];
  const weights = [0.7, 0.1, 0.1, 0.05, 0.05];
  const random = Math.random();
  let sum = 0;
  for (let i = 0; i < weights.length; i++) {
    sum += weights[i];
    if (random < sum) return statuses[i];
  }
  return 'active';
};

/**
 * Generate consistent tyre products for both wholesale and retail
 */
const generateCentralizedTyreProducts = (count: number): { wholesale: TyreProduct[], retail: TyreProduct[] } => {
  const manufacturers = ['Michelin', 'Bridgestone', 'Continental', 'Pirelli', 'Goodyear'];
  const models = ['Pilot Sport 4', 'Potenza RE980AS', 'ExtremeContact DWS06', 'P Zero', 'Eagle F1'];
  const widths = [185, 195, 205, 215, 225, 235, 245, 255];
  const aspectRatios = [35, 40, 45, 50, 55, 60, 65];
  const rimDiameters = [15, 16, 17, 18, 19, 20];
  const speedRatings = ['H', 'V', 'W', 'Y', 'Z'];
  const seasons = ['Summer', 'Winter', 'All-Season'] as const;

  const wholesale: TyreProduct[] = [];
  const retail: TyreProduct[] = [];

  for (let i = 0; i < count; i++) {
    const manufacturer = manufacturers[i % manufacturers.length];
    const model = models[i % models.length];
    const width = widths[i % widths.length];
    const aspectRatio = aspectRatios[i % aspectRatios.length];
    const rimDiameter = rimDiameters[i % rimDiameters.length];
    const speedRating = speedRatings[i % speedRatings.length];
    const loadIndex = 75 + (i % 30);
    const season = seasons[i % seasons.length];
    const stockQuantity = 10 + (i % 90);
    const status = generateStatus();

    // Base product data
    const baseProduct = {
      name: `${manufacturer} ${model} ${width}/${aspectRatio}R${rimDiameter}`,
      sku: `${manufacturer.substring(0, 3).toUpperCase()}-${width}${aspectRatio}R${rimDiameter}-${loadIndex}${speedRating}`,
      partArticleNumber: `${Math.floor(Math.random() * 10000000000000)}`,
      category: 'tyres' as const,
      subcategory: 'Tyres',
      descriptionAndSpecifications: `Part 1 - Product Description:
High-performance ${season.toLowerCase()} tyre designed for optimal performance and safety. Features advanced compound technology for superior grip and durability. The ${manufacturer} ${model} delivers excellent handling characteristics and reliable performance across various driving conditions.

Part 2 - Specifications:
- Width: ${width}mm
- Aspect Ratio: ${aspectRatio}%
- Rim Diameter: ${rimDiameter}"
- Load Index: ${loadIndex}
- Speed Rating: ${speedRating}
- Season: ${season}
- Tread Pattern: Asymmetric
- Compound Type: Advanced Silica
- DOT Compliance: Yes
- ECE Marking: E11`,
      primaryImage: '/placeholder.svg',
      additionalImages: ['/placeholder.svg'],
      manufacturer,
      supplierName: 'Global Tyre Distributors',
      stockQuantity,
      minimumOrderQuantity: 2,
      quotationRequestEnabled: i % 2 === 0,
      width,
      aspectRatio,
      rimDiameter,
      loadIndex,
      speedRating,
      season,
      vehicleTypeCompatibility: [],
      treadLife: `${30000 + (i * 1000)} miles`,
      tractionRating: ['A', 'AA', 'B'][i % 3],
      temperatureRating: ['A', 'B', 'C'][i % 3],
      certifications: ['DOT', 'ECE', 'ISO 9001'],
      shippingOrigin: ['China', 'Japan', 'Germany'][i % 3],
      estimatedLeadTime: `${3 + (i % 10)} days`,
      packagingDetails: 'Individual cardboard boxes',
      inventoryUpdateDate: new Date(),
      status,
      adminNotes: undefined,
      createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)),
      updatedAt: new Date(),
    };

    // Wholesale version
    const wholesaleProduct: TyreProduct = {
      ...baseProduct,
      id: generateProductIdForIndex('tyres', i, 'wholesale'),
      wholesalePricingTiers: generatePricingTiers(),
      retailPrice: undefined,
      marketplaceSection: 'wholesale' as const,
    };

    // Retail version
    const retailProduct: TyreProduct = {
      ...baseProduct,
      id: generateProductIdForIndex('tyres', i, 'retail'),
      wholesalePricingTiers: undefined,
      retailPrice: 80 + (i * 5),
      marketplaceSection: 'retail' as const,
    };

    wholesale.push(wholesaleProduct);
    retail.push(retailProduct);
  }

  return { wholesale, retail };
};

/**
 * Generate consistent brake products for both wholesale and retail
 */
const generateCentralizedBrakeProducts = (count: number): { wholesale: BrakeProduct[], retail: BrakeProduct[] } => {
  const manufacturers = ['Brembo', 'ATE', 'TRW', 'Bosch', 'Ferodo'];
  const subcategories = ['Brake Discs', 'Brake Pads', 'Brake Caliper', 'Brake Shoes', 'Brake Drums'];

  const wholesale: BrakeProduct[] = [];
  const retail: BrakeProduct[] = [];

  for (let i = 0; i < count; i++) {
    const manufacturer = manufacturers[i % manufacturers.length];
    const subcategory = subcategories[i % subcategories.length];
    const stockQuantity = 5 + (i % 95);
    const status = generateStatus();

    // Base product data
    const baseProduct = {
      name: `${manufacturer} ${subcategory} ${i % 2 === 0 ? 'Front' : 'Rear'}`,
      sku: `${manufacturer.substring(0, 3).toUpperCase()}-${subcategory.substring(0, 2).toUpperCase()}-${100000 + i}`,
      partArticleNumber: `${Math.floor(Math.random() * 10000000000000)}`,
      category: 'brakes' as const,
      subcategory,
      descriptionAndSpecifications: `Part 1 - Product Description:
High-quality ${subcategory.toLowerCase()} engineered for reliable braking performance and safety. Manufactured by ${manufacturer} to OEM standards with premium materials for extended service life and consistent performance. Designed for optimal heat dissipation and fade resistance.

Part 2 - Specifications:
- Part Number: ${manufacturer.substring(0, 3).toUpperCase()}-${subcategory.substring(0, 2).toUpperCase()}-${100000 + i}
- Brand: ${manufacturer}
- Component Type: ${subcategory}
- Material: High-carbon cast iron
- Surface Treatment: Coated
- Position: ${i % 2 === 0 ? 'Front' : 'Rear'} Axle
- Certification: ECE R90, ISO 9001
- Installation: Professional installation recommended
- Warranty: 2-year manufacturer warranty`,
      primaryImage: '/placeholder.svg',
      additionalImages: ['/placeholder.svg'],
      manufacturer,
      supplierName: 'Global Brake Parts Distributors',
      stockQuantity,
      minimumOrderQuantity: 1,
      quotationRequestEnabled: i % 3 === 0,
      vehicleTypeCompatibility: [],
      certifications: ['ECE R90', 'ISO 9001', 'TÜV'],
      shippingOrigin: ['Germany', 'Italy', 'Japan'][i % 3],
      estimatedLeadTime: `${2 + (i % 8)} days`,
      availableShippingMethods: ['Standard', 'Express'],
      inventoryUpdateDate: new Date(),
      status,
      adminNotes: undefined,
      createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)),
      updatedAt: new Date(),
    };

    // Wholesale version
    const wholesaleProduct: BrakeProduct = {
      ...baseProduct,
      id: generateProductIdForIndex('brakes', i, 'wholesale'),
      wholesalePricingTiers: generatePricingTiers(),
      retailPrice: undefined,
      marketplaceSection: 'wholesale' as const,
    };

    // Retail version
    const retailProduct: BrakeProduct = {
      ...baseProduct,
      id: generateProductIdForIndex('brakes', i, 'retail'),
      wholesalePricingTiers: undefined,
      retailPrice: 40 + (i * 3),
      marketplaceSection: 'retail' as const,
    };

    wholesale.push(wholesaleProduct);
    retail.push(retailProduct);
  }

  return { wholesale, retail };
};

// Generate centralized product data
const PRODUCT_COUNT = 50; // 50 of each type for testing

const tyreData = generateCentralizedTyreProducts(PRODUCT_COUNT);
const brakeData = generateCentralizedBrakeProducts(PRODUCT_COUNT);

// Export centralized product collections
export const CENTRALIZED_PRODUCTS = {
  wholesale: {
    tyres: tyreData.wholesale,
    brakes: brakeData.wholesale,
    all: [...tyreData.wholesale, ...brakeData.wholesale],
  },
  retail: {
    tyres: tyreData.retail,
    brakes: brakeData.retail,
    all: [...tyreData.retail, ...brakeData.retail],
  },
  // Combined for lookup
  all: [
    ...tyreData.wholesale,
    ...tyreData.retail,
    ...brakeData.wholesale,
    ...brakeData.retail,
  ],
};

/**
 * Get products for a specific marketplace section and category
 */
export const getProductsForSection = (
  section: 'wholesale' | 'retail',
  category: 'tyres' | 'brakes' | 'all' = 'all'
): (TyreProduct | BrakeProduct)[] => {
  return CENTRALIZED_PRODUCTS[section][category];
};

/**
 * Get all products for lookup purposes
 */
export const getAllProducts = (): (TyreProduct | BrakeProduct)[] => {
  return CENTRALIZED_PRODUCTS.all;
};
