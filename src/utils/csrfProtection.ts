/**
 * CSRF Protection Utilities
 * 
 * This module provides utilities for Cross-Site Request Forgery (CSRF) protection.
 * It generates and validates CSRF tokens for form submissions and API requests.
 */

// Generate a random string for CSRF token
const generateRandomString = (length: number): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;
  
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  
  return result;
};

// Generate a timestamp for token expiration
const generateTimestamp = (expiryMinutes: number = 30): number => {
  return Date.now() + (expiryMinutes * 60 * 1000);
};

/**
 * Generate a CSRF token
 * 
 * @param expiryMinutes Minutes until the token expires (default: 30)
 * @returns CSRF token string
 */
export const generateCsrfToken = (expiryMinutes: number = 30): string => {
  // Generate a random token
  const randomPart = generateRandomString(32);
  const timestamp = generateTimestamp(expiryMinutes);
  
  // Combine random part and timestamp
  const token = `${randomPart}.${timestamp}`;
  
  // Store the token in localStorage (in a real app, this would be a secure HTTP-only cookie)
  localStorage.setItem('csrfToken', token);
  
  return token;
};

/**
 * Validate a CSRF token
 * 
 * @param token The token to validate
 * @returns Boolean indicating if the token is valid
 */
export const validateCsrfToken = (token: string): boolean => {
  // Get the stored token
  const storedToken = localStorage.getItem('csrfToken');
  
  // If no token is stored, validation fails
  if (!storedToken) {
    return false;
  }
  
  // Check if the tokens match
  if (token !== storedToken) {
    return false;
  }
  
  // Check if the token has expired
  const parts = storedToken.split('.');
  if (parts.length !== 2) {
    return false;
  }
  
  const timestamp = parseInt(parts[1], 10);
  if (isNaN(timestamp) || timestamp < Date.now()) {
    // Token has expired, remove it
    localStorage.removeItem('csrfToken');
    return false;
  }
  
  return true;
};

/**
 * Get the current CSRF token or generate a new one
 * 
 * @returns CSRF token string
 */
export const getCsrfToken = (): string => {
  const storedToken = localStorage.getItem('csrfToken');
  
  // If no token exists or it's expired, generate a new one
  if (!storedToken) {
    return generateCsrfToken();
  }
  
  // Check if the token has expired
  const parts = storedToken.split('.');
  if (parts.length !== 2) {
    return generateCsrfToken();
  }
  
  const timestamp = parseInt(parts[1], 10);
  if (isNaN(timestamp) || timestamp < Date.now()) {
    return generateCsrfToken();
  }
  
  return storedToken;
};

/**
 * Add CSRF token to a form
 * 
 * @param formElement The form element to add the token to
 */
export const addCsrfTokenToForm = (formElement: HTMLFormElement): void => {
  // Remove any existing CSRF token input
  const existingInput = formElement.querySelector('input[name="_csrf"]');
  if (existingInput) {
    existingInput.remove();
  }
  
  // Create a new hidden input for the CSRF token
  const input = document.createElement('input');
  input.type = 'hidden';
  input.name = '_csrf';
  input.value = getCsrfToken();
  
  // Add the input to the form
  formElement.appendChild(input);
};

/**
 * Add CSRF token to fetch/axios headers
 * 
 * @param headers Headers object to add the token to
 * @returns Updated headers object
 */
export const addCsrfTokenToHeaders = (headers: Record<string, string> = {}): Record<string, string> => {
  return {
    ...headers,
    'X-CSRF-Token': getCsrfToken(),
  };
};

/**
 * CSRF protection middleware for API requests
 * 
 * This is a higher-order function that wraps API calls with CSRF protection
 * 
 * @param apiCall The API call function to wrap
 * @returns Wrapped API call function with CSRF protection
 */
export const withCsrfProtection = <T extends (...args: any[]) => Promise<any>>(
  apiCall: T
): T => {
  return (async (...args: Parameters<T>) => {
    // Get the last argument which should be the options object
    const lastArgIndex = args.length - 1;
    const options = args[lastArgIndex] || {};
    
    // Add CSRF token to headers
    const headers = options.headers || {};
    const updatedHeaders = addCsrfTokenToHeaders(headers);
    
    // Update the options with the new headers
    const updatedOptions = {
      ...options,
      headers: updatedHeaders,
    };
    
    // Update the arguments with the new options
    const updatedArgs = [...args.slice(0, lastArgIndex), updatedOptions];
    
    // Call the original API function with the updated arguments
    return apiCall(...updatedArgs as Parameters<T>);
  }) as T;
};
