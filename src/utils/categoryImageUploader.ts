/**
 * Category Image Upload Utility
 * Helper script for uploading category and subcategory images to Supabase Storage
 */

import { uploadCategoryImage, type CategoryImageUpload } from '@/services/categoryImageService';
import { CATEGORIES } from '@/data/categoryData';

/**
 * Upload images for all categories and subcategories
 * This is a utility function for bulk uploading placeholder images
 */
export const uploadAllCategoryImages = async () => {
  const uploads: CategoryImageUpload[] = [];
  
  // Add category uploads
  CATEGORIES.filter(cat => cat.id !== 'all').forEach(category => {
    // You would replace this with actual image files
    // For now, this is just a structure example
    console.log(`Category: ${category.id} - ${category.name}`);
  });

  // Add subcategory uploads
  CATEGORIES.forEach(category => {
    category.subcategories.forEach(subcategory => {
      console.log(`Subcategory: ${subcategory.id} - ${subcategory.name} (Category: ${category.id})`);
    });
  });

  console.log('Category and subcategory structure ready for image uploads');
  console.log('Total categories (excluding "all"):', CATEGORIES.filter(cat => cat.id !== 'all').length);
  console.log('Total subcategories:', CATEGORIES.reduce((total, cat) => total + cat.subcategories.length, 0));
};

/**
 * Generate a list of all categories and subcategories for image preparation
 */
export const generateImageList = () => {
  const imageList: Array<{
    type: 'category' | 'subcategory';
    id: string;
    name: string;
    displayName: string;
    categoryId?: string;
    suggestedFileName: string;
  }> = [];

  // Categories
  CATEGORIES.filter(cat => cat.id !== 'all').forEach(category => {
    imageList.push({
      type: 'category',
      id: category.id,
      name: category.name,
      displayName: category.displayName,
      suggestedFileName: `category_${category.id}.png`
    });
  });

  // Subcategories
  CATEGORIES.forEach(category => {
    category.subcategories.forEach(subcategory => {
      imageList.push({
        type: 'subcategory',
        id: subcategory.id,
        name: subcategory.name,
        displayName: subcategory.displayName,
        categoryId: category.id,
        suggestedFileName: `subcategory_${subcategory.id}.png`
      });
    });
  });

  return imageList;
};

/**
 * Print the image list for manual preparation
 */
export const printImageList = () => {
  const imageList = generateImageList();
  
  console.log('=== CATEGORY IMAGES NEEDED ===');
  console.log('');
  
  console.log('CATEGORIES:');
  imageList.filter(item => item.type === 'category').forEach(item => {
    console.log(`- ${item.displayName} (${item.id}) -> ${item.suggestedFileName}`);
  });
  
  console.log('');
  console.log('SUBCATEGORIES:');
  imageList.filter(item => item.type === 'subcategory').forEach(item => {
    console.log(`- ${item.displayName} (${item.id}) -> ${item.suggestedFileName}`);
  });
  
  console.log('');
  console.log(`Total images needed: ${imageList.length}`);
  console.log('- Categories:', imageList.filter(item => item.type === 'category').length);
  console.log('- Subcategories:', imageList.filter(item => item.type === 'subcategory').length);
  
  return imageList;
};

/**
 * Upload a single category image
 */
export const uploadSingleCategoryImage = async (
  file: File,
  categoryId: string,
  type: 'category' | 'subcategory'
) => {
  try {
    const result = await uploadCategoryImage({
      file,
      categoryId,
      type
    });
    
    if (result.success) {
      console.log(`✅ Successfully uploaded ${type} image for ${categoryId}`);
      console.log(`URL: ${result.url}`);
    } else {
      console.error(`❌ Failed to upload ${type} image for ${categoryId}:`, result.error);
    }
    
    return result;
  } catch (error) {
    console.error(`❌ Error uploading ${type} image for ${categoryId}:`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Validate that all required images are uploaded
 */
export const validateCategoryImages = async () => {
  const imageList = generateImageList();
  const results = {
    categories: { uploaded: 0, missing: 0, total: 0 },
    subcategories: { uploaded: 0, missing: 0, total: 0 }
  };
  
  // This would check if images exist in Supabase Storage
  // For now, just return the structure
  console.log('Image validation would check:', imageList.length, 'images');
  
  return results;
};

// Export the image list for external use
export const CATEGORY_IMAGE_LIST = generateImageList();
