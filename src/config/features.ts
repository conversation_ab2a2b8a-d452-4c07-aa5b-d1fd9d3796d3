/**
 * Feature Flags Configuration
 * 
 * This file manages feature flags for the AROUZ MARKET application,
 * allowing for gradual rollout of new features and easy switching between
 * different implementations.
 */

// Environment variables for feature flags
const VITE_USE_SUPABASE_BACKEND = import.meta.env.VITE_USE_SUPABASE_BACKEND === 'true';
const VITE_ENABLE_PRODUCT_FEATURES = import.meta.env.VITE_ENABLE_PRODUCT_FEATURES !== 'false';
const VITE_ENABLE_REAL_TIME_UPDATES = import.meta.env.VITE_ENABLE_REAL_TIME_UPDATES === 'true';
const VITE_ENABLE_IMAGE_UPLOAD = import.meta.env.VITE_ENABLE_IMAGE_UPLOAD === 'true';

export interface FeatureFlags {
  // Backend Configuration
  useSupabaseBackend: boolean;
  useLocalStorage: boolean;
  
  // Product Features
  enableProductFeatures: boolean;
  enableProductCreation: boolean;
  enableProductEditing: boolean;
  enableProductDeletion: boolean;
  enableBulkOperations: boolean;
  
  // Real-time Features
  enableRealTimeUpdates: boolean;
  enableLiveSync: boolean;
  
  // Storage Features
  enableImageUpload: boolean;
  enableFileStorage: boolean;
  
  // Migration Features
  enableMigrationTools: boolean;
  showMigrationStatus: boolean;
  
  // Development Features
  enableDebugMode: boolean;
  showPerformanceMetrics: boolean;
  enableTestingTools: boolean;
}

// Default feature flags
const defaultFeatures: FeatureFlags = {
  // Backend Configuration
  useSupabaseBackend: VITE_USE_SUPABASE_BACKEND,
  useLocalStorage: !VITE_USE_SUPABASE_BACKEND,
  
  // Product Features
  enableProductFeatures: VITE_ENABLE_PRODUCT_FEATURES,
  enableProductCreation: VITE_ENABLE_PRODUCT_FEATURES,
  enableProductEditing: VITE_ENABLE_PRODUCT_FEATURES,
  enableProductDeletion: VITE_ENABLE_PRODUCT_FEATURES,
  enableBulkOperations: VITE_ENABLE_PRODUCT_FEATURES,
  
  // Real-time Features
  enableRealTimeUpdates: VITE_ENABLE_REAL_TIME_UPDATES && VITE_USE_SUPABASE_BACKEND,
  enableLiveSync: VITE_ENABLE_REAL_TIME_UPDATES && VITE_USE_SUPABASE_BACKEND,
  
  // Storage Features
  enableImageUpload: VITE_ENABLE_IMAGE_UPLOAD && VITE_USE_SUPABASE_BACKEND,
  enableFileStorage: VITE_ENABLE_IMAGE_UPLOAD && VITE_USE_SUPABASE_BACKEND,
  
  // Migration Features
  enableMigrationTools: true, // Always enabled for development
  showMigrationStatus: true,
  
  // Development Features
  enableDebugMode: import.meta.env.DEV,
  showPerformanceMetrics: import.meta.env.DEV,
  enableTestingTools: import.meta.env.DEV,
};

// Runtime feature flags (can be modified during runtime)
let runtimeFeatures: FeatureFlags = { ...defaultFeatures };

/**
 * Get current feature flags
 */
export function getFeatureFlags(): FeatureFlags {
  return { ...runtimeFeatures };
}

/**
 * Check if a specific feature is enabled
 */
export function isFeatureEnabled(feature: keyof FeatureFlags): boolean {
  return runtimeFeatures[feature];
}

/**
 * Enable a feature at runtime
 */
export function enableFeature(feature: keyof FeatureFlags): void {
  runtimeFeatures[feature] = true;
  console.log(`Feature enabled: ${feature}`);
}

/**
 * Disable a feature at runtime
 */
export function disableFeature(feature: keyof FeatureFlags): void {
  runtimeFeatures[feature] = false;
  console.log(`Feature disabled: ${feature}`);
}

/**
 * Toggle a feature at runtime
 */
export function toggleFeature(feature: keyof FeatureFlags): boolean {
  runtimeFeatures[feature] = !runtimeFeatures[feature];
  console.log(`Feature toggled: ${feature} = ${runtimeFeatures[feature]}`);
  return runtimeFeatures[feature];
}

/**
 * Update multiple features at once
 */
export function updateFeatures(updates: Partial<FeatureFlags>): void {
  runtimeFeatures = { ...runtimeFeatures, ...updates };
  console.log('Features updated:', updates);
}

/**
 * Reset features to default values
 */
export function resetFeatures(): void {
  runtimeFeatures = { ...defaultFeatures };
  console.log('Features reset to defaults');
}

/**
 * Get backend type based on feature flags
 */
export function getBackendType(): 'supabase' | 'localStorage' {
  return isFeatureEnabled('useSupabaseBackend') ? 'supabase' : 'localStorage';
}

/**
 * Check if migration is needed
 */
export function shouldShowMigrationPrompt(): boolean {
  return (
    isFeatureEnabled('enableMigrationTools') &&
    isFeatureEnabled('showMigrationStatus') &&
    !isFeatureEnabled('useSupabaseBackend')
  );
}

/**
 * Development helper to log current feature state
 */
export function logFeatureState(): void {
  if (import.meta.env.DEV) {
    console.group('🚀 Feature Flags Status');
    console.log('Backend Type:', getBackendType());
    console.log('Environment:', import.meta.env.MODE);
    console.table(runtimeFeatures);
    console.groupEnd();
  }
}

/**
 * Feature flag presets for different environments
 */
export const featurePresets = {
  development: {
    useSupabaseBackend: false,
    useLocalStorage: true,
    enableProductFeatures: true,
    enableRealTimeUpdates: false,
    enableImageUpload: false,
    enableMigrationTools: true,
    enableDebugMode: true,
    showPerformanceMetrics: true,
    enableTestingTools: true,
  } as Partial<FeatureFlags>,
  
  testing: {
    useSupabaseBackend: true,
    useLocalStorage: false,
    enableProductFeatures: true,
    enableRealTimeUpdates: true,
    enableImageUpload: true,
    enableMigrationTools: true,
    enableDebugMode: true,
    showPerformanceMetrics: false,
    enableTestingTools: true,
  } as Partial<FeatureFlags>,
  
  production: {
    useSupabaseBackend: true,
    useLocalStorage: false,
    enableProductFeatures: true,
    enableRealTimeUpdates: true,
    enableImageUpload: true,
    enableMigrationTools: false,
    enableDebugMode: false,
    showPerformanceMetrics: false,
    enableTestingTools: false,
  } as Partial<FeatureFlags>,
};

/**
 * Apply a feature preset
 */
export function applyFeaturePreset(preset: keyof typeof featurePresets): void {
  const presetConfig = featurePresets[preset];
  updateFeatures(presetConfig);
  console.log(`Applied ${preset} feature preset`);
}

/**
 * Initialize features based on environment
 */
export function initializeFeatures(): void {
  const env = import.meta.env.MODE;
  
  // Apply environment-specific presets
  if (env === 'development') {
    // Keep default development settings
  } else if (env === 'test') {
    applyFeaturePreset('testing');
  } else if (env === 'production') {
    applyFeaturePreset('production');
  }
  
  // Log feature state in development
  if (import.meta.env.DEV) {
    logFeatureState();
  }
}

// Initialize features on module load
initializeFeatures();

// Export commonly used feature checks
export const features = {
  get useSupabase() { return isFeatureEnabled('useSupabaseBackend'); },
  get useLocalStorage() { return isFeatureEnabled('useLocalStorage'); },
  get enableProducts() { return isFeatureEnabled('enableProductFeatures'); },
  get enableRealTime() { return isFeatureEnabled('enableRealTimeUpdates'); },
  get enableImages() { return isFeatureEnabled('enableImageUpload'); },
  get enableMigration() { return isFeatureEnabled('enableMigrationTools'); },
  get isDebug() { return isFeatureEnabled('enableDebugMode'); },
};
