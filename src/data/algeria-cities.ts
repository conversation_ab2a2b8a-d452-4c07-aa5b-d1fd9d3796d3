/**
 * Comprehensive Algerian administrative divisions data structure
 * Following the official hierarchy: Country → Wilayas → Daïras → Communes
 * Updated with complete 58 wilayas and all administrative divisions
 */

export interface AlgerianLocation {
  code: string;
  name: string;
  name_ar: string;
  name_fr: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface AlgerianWilaya extends AlgerianLocation {
  dairas: AlgerianDaira[];
}

export interface AlgerianDaira extends AlgerianLocation {
  wilaya_code: string;
  communes: AlgerianCommune[];
}

export interface AlgerianCommune extends AlgerianLocation {
  wilaya_code: string;
  daira_code: string;
}

export interface FormattedAddress {
  street?: string;
  commune: string;
  commune_ar?: string;
  commune_fr?: string;
  daira: string;
  daira_ar?: string;
  daira_fr?: string;
  wilaya: string;
  wilaya_ar?: string;
  wilaya_fr?: string;
  country: string;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface SearchResult {
  type: 'wilaya' | 'daira' | 'commune';
  location: AlgerianWilaya | AlgerianDaira | AlgerianCommune;
  hierarchy: string; // e.g., "Commune, Daïra, Wilaya"
}

// Legacy interface for backward compatibility
export interface AlgerianCity extends AlgerianLocation {}

// Import complete administrative data
import { loadCompleteAlgerianData, searchAllAdministrativeDivisions } from './algeria-data-loader';

/**
 * Comprehensive list of Algerian wilayas with coordinates and administrative data
 * This will be populated with complete data including all 58 wilayas
 */
let ALGERIAN_WILAYAS: AlgerianWilaya[] = [
  {
    code: "01",
    name: "Adrar",
    name_ar: "أدرار",
    name_fr: "Adrar",
    coordinates: { lat: 27.8702, lng: -0.2841 },
    dairas: []
  },
  {
    code: "02",
    name: "Chlef",
    name_ar: "الشلف",
    name_fr: "Chlef",
    coordinates: { lat: 36.1654, lng: 1.3347 },
    dairas: []
  },
  {
    code: "03",
    name: "Laghouat",
    name_ar: "الأغواط",
    name_fr: "Laghouat",
    coordinates: { lat: 33.8000, lng: 2.8667 },
    dairas: []
  },
  {
    code: "04",
    name: "Oum El Bouaghi",
    name_ar: "أم البواقي",
    name_fr: "Oum El Bouaghi",
    coordinates: { lat: 35.8753, lng: 7.1135 },
    dairas: []
  },
  {
    code: "05",
    name: "Batna",
    name_ar: "باتنة",
    name_fr: "Batna",
    coordinates: { lat: 35.5559, lng: 6.1740 },
    dairas: []
  },
  {
    code: "06",
    name: "Béjaïa",
    name_ar: "بجاية",
    name_fr: "Béjaïa",
    coordinates: { lat: 36.7525, lng: 5.0689 },
    dairas: []
  },
  {
    code: "07",
    name: "Biskra",
    name_ar: "بسكرة",
    name_fr: "Biskra",
    coordinates: { lat: 34.8361, lng: 5.7253 },
    dairas: []
  },
  {
    code: "08",
    name: "Béchar",
    name_ar: "بشار",
    name_fr: "Béchar",
    coordinates: { lat: 31.6167, lng: -2.2167 },
    dairas: []
  },
  {
    code: "09",
    name: "Blida",
    name_ar: "البليدة",
    name_fr: "Blida",
    coordinates: { lat: 36.7763, lng: 3.0586 },
    dairas: []
  },
  {
    code: "10",
    name: "Bouira",
    name_ar: "البويرة",
    name_fr: "Bouira",
    coordinates: { lat: 35.3667, lng: 1.0333 },
    dairas: []
  },
  {
    code: "11",
    name: "Tamanrasset",
    name_ar: "تمنراست",
    name_fr: "Tamanrasset",
    coordinates: { lat: 22.7833, lng: 5.5167 },
    dairas: []
  },
  {
    code: "12",
    name: "Tébessa",
    name_ar: "تبسة",
    name_fr: "Tébessa",
    coordinates: { lat: 35.4000, lng: 8.1222 },
    dairas: []
  },
  {
    code: "13",
    name: "Tlemcen",
    name_ar: "تلمسان",
    name_fr: "Tlemcen",
    coordinates: { lat: 34.8833, lng: -1.3167 },
    dairas: []
  },
  {
    code: "14",
    name: "Tiaret",
    name_ar: "تيارت",
    name_fr: "Tiaret",
    coordinates: { lat: 35.3667, lng: 1.3167 },
    dairas: []
  },
  {
    code: "15",
    name: "Tizi Ouzou",
    name_ar: "تيزي وزو",
    name_fr: "Tizi Ouzou",
    coordinates: { lat: 36.7167, lng: 4.0500 },
    dairas: []
  },
  {
    code: "16",
    name: "Algiers",
    name_ar: "الجزائر",
    name_fr: "Alger",
    coordinates: { lat: 36.7631, lng: 3.0506 },
    dairas: []
  },
  {
    code: "17",
    name: "Djelfa",
    name_ar: "الجلفة",
    name_fr: "Djelfa",
    coordinates: { lat: 34.6711, lng: 3.2631 },
    dairas: []
  },
  {
    code: "18",
    name: "Jijel",
    name_ar: "جيجل",
    name_fr: "Jijel",
    coordinates: { lat: 36.8167, lng: 5.7667 },
    dairas: []
  },
  {
    code: "19",
    name: "Sétif",
    name_ar: "سطيف",
    name_fr: "Sétif",
    coordinates: { lat: 36.1900, lng: 5.4133 },
    dairas: []
  },
  {
    code: "20",
    name: "Saïda",
    name_ar: "سعيدة",
    name_fr: "Saïda",
    coordinates: { lat: 34.8500, lng: 0.1500 },
    dairas: []
  },
  {
    code: "21",
    name: "Skikda",
    name_ar: "سكيكدة",
    name_fr: "Skikda",
    coordinates: { lat: 36.8667, lng: 6.9167 },
    dairas: []
  },
  {
    code: "22",
    name: "Sidi Bel Abbès",
    name_ar: "سيدي بلعباس",
    name_fr: "Sidi Bel Abbès",
    coordinates: { lat: 35.1833, lng: -0.6333 },
    dairas: []
  },
  {
    code: "23",
    name: "Annaba",
    name_ar: "عنابة",
    name_fr: "Annaba",
    coordinates: { lat: 36.9000, lng: 7.7667 },
    dairas: []
  },
  {
    code: "24",
    name: "Guelma",
    name_ar: "قالمة",
    name_fr: "Guelma",
    coordinates: { lat: 36.4667, lng: 7.4333 },
    dairas: []
  },
  {
    code: "25",
    name: "Constantine",
    name_ar: "قسنطينة",
    name_fr: "Constantine",
    coordinates: { lat: 36.3650, lng: 6.6147 },
    dairas: []
  },
  {
    code: "26",
    name: "Médéa",
    name_ar: "المدية",
    name_fr: "Médéa",
    coordinates: { lat: 36.2667, lng: 2.7500 },
    dairas: []
  },
  {
    code: "27",
    name: "Mostaganem",
    name_ar: "مستغانم",
    name_fr: "Mostaganem",
    coordinates: { lat: 35.9314, lng: 0.0892 },
    dairas: []
  },
  {
    code: "28",
    name: "M'Sila",
    name_ar: "المسيلة",
    name_fr: "M'Sila",
    coordinates: { lat: 35.7056, lng: 4.5417 },
    dairas: []
  },
  {
    code: "29",
    name: "Mascara",
    name_ar: "معسكر",
    name_fr: "Mascara",
    coordinates: { lat: 35.3967, lng: 0.1406 },
    dairas: []
  },
  {
    code: "30",
    name: "Ouargla",
    name_ar: "ورقلة",
    name_fr: "Ouargla",
    coordinates: { lat: 31.9500, lng: 5.3333 },
    dairas: []
  },
  {
    code: "31",
    name: "Oran",
    name_ar: "وهران",
    name_fr: "Oran",
    coordinates: { lat: 35.6969, lng: -0.6331 },
    dairas: []
  },
  {
    code: "32",
    name: "El Bayadh",
    name_ar: "البيض",
    name_fr: "El Bayadh",
    coordinates: { lat: 33.6833, lng: 1.0000 },
    dairas: []
  },
  {
    code: "33",
    name: "Illizi",
    name_ar: "إليزي",
    name_fr: "Illizi",
    coordinates: { lat: 26.5000, lng: 8.4667 },
    dairas: []
  },
  {
    code: "34",
    name: "Bordj Bou Arréridj",
    name_ar: "برج بوعريريج",
    name_fr: "Bordj Bou Arréridj",
    coordinates: { lat: 36.0667, lng: 4.7667 },
    dairas: []
  },
  {
    code: "35",
    name: "Boumerdès",
    name_ar: "بومرداس",
    name_fr: "Boumerdès",
    coordinates: { lat: 36.7667, lng: 3.4833 },
    dairas: []
  },
  {
    code: "36",
    name: "El Tarf",
    name_ar: "الطارف",
    name_fr: "El Tarf",
    coordinates: { lat: 36.7667, lng: 8.3167 },
    dairas: []
  },
  {
    code: "37",
    name: "Tindouf",
    name_ar: "تندوف",
    name_fr: "Tindouf",
    coordinates: { lat: 27.6833, lng: -8.1667 },
    dairas: []
  },
  {
    code: "38",
    name: "Tissemsilt",
    name_ar: "تيسمسيلت",
    name_fr: "Tissemsilt",
    coordinates: { lat: 35.6000, lng: 1.8167 },
    dairas: []
  },
  {
    code: "39",
    name: "El Oued",
    name_ar: "الوادي",
    name_fr: "El Oued",
    coordinates: { lat: 33.3667, lng: 6.8667 },
    dairas: []
  },
  {
    code: "40",
    name: "Khenchela",
    name_ar: "خنشلة",
    name_fr: "Khenchela",
    coordinates: { lat: 35.4333, lng: 7.1500 },
    dairas: []
  },
  {
    code: "41",
    name: "Souk Ahras",
    name_ar: "سوق أهراس",
    name_fr: "Souk Ahras",
    coordinates: { lat: 36.2833, lng: 7.9500 },
    dairas: []
  },
  {
    code: "42",
    name: "Tipaza",
    name_ar: "تيبازة",
    name_fr: "Tipaza",
    coordinates: { lat: 36.5833, lng: 2.4500 },
    dairas: []
  },
  {
    code: "43",
    name: "Mila",
    name_ar: "ميلة",
    name_fr: "Mila",
    coordinates: { lat: 36.4500, lng: 6.2667 },
    dairas: []
  },
  {
    code: "44",
    name: "Aïn Defla",
    name_ar: "عين الدفلى",
    name_fr: "Aïn Defla",
    coordinates: { lat: 36.2667, lng: 1.9667 },
    dairas: []
  },
  {
    code: "45",
    name: "Naâma",
    name_ar: "النعامة",
    name_fr: "Naâma",
    coordinates: { lat: 33.2667, lng: -0.3000 },
    dairas: []
  },
  {
    code: "46",
    name: "Aïn Témouchent",
    name_ar: "عين تيموشنت",
    name_fr: "Aïn Témouchent",
    coordinates: { lat: 35.2833, lng: -1.0333 },
    dairas: []
  },
  {
    code: "47",
    name: "Ghardaïa",
    name_ar: "غرداية",
    name_fr: "Ghardaïa",
    coordinates: { lat: 32.4833, lng: 3.6667 },
    dairas: []
  },
  {
    code: "48",
    name: "Relizane",
    name_ar: "غليزان",
    name_fr: "Relizane",
    coordinates: { lat: 35.7333, lng: 0.5500 },
    dairas: []
  },
  // New wilayas created in December 2019 (49-58)
  {
    code: "49",
    name: "Timimoun",
    name_ar: "تيميمون",
    name_fr: "Timimoun",
    coordinates: { lat: 29.2631, lng: 0.2298 },
    dairas: []
  },
  {
    code: "50",
    name: "Bordj Badji Mokhtar",
    name_ar: "برج باجي مختار",
    name_fr: "Bordj Badji Mokhtar",
    coordinates: { lat: 21.3167, lng: 0.9500 },
    dairas: []
  },
  {
    code: "51",
    name: "Ouled Djellal",
    name_ar: "أولاد جلال",
    name_fr: "Ouled Djellal",
    coordinates: { lat: 34.3833, lng: 5.0833 },
    dairas: []
  },
  {
    code: "52",
    name: "Béni Abbès",
    name_ar: "بني عباس",
    name_fr: "Béni Abbès",
    coordinates: { lat: 30.1333, lng: -2.1667 },
    dairas: []
  },
  {
    code: "53",
    name: "In Salah",
    name_ar: "عين صالح",
    name_fr: "In Salah",
    coordinates: { lat: 27.2167, lng: 2.4667 },
    dairas: []
  },
  {
    code: "54",
    name: "In Guezzam",
    name_ar: "عين قزام",
    name_fr: "In Guezzam",
    coordinates: { lat: 19.5667, lng: 5.7667 },
    dairas: []
  },
  {
    code: "55",
    name: "Touggourt",
    name_ar: "تقرت",
    name_fr: "Touggourt",
    coordinates: { lat: 33.1167, lng: 6.0667 },
    dairas: []
  },
  {
    code: "56",
    name: "Djanet",
    name_ar: "جانت",
    name_fr: "Djanet",
    coordinates: { lat: 24.5500, lng: 9.4833 },
    dairas: []
  },
  {
    code: "57",
    name: "El Meghaier",
    name_ar: "المغير",
    name_fr: "El Meghaier",
    coordinates: { lat: 33.9500, lng: 5.9167 },
    dairas: []
  },
  {
    code: "58",
    name: "El Menia",
    name_ar: "المنيعة",
    name_fr: "El Menia",
    coordinates: { lat: 30.5833, lng: 2.8833 },
    dairas: []
  },
  {
    code: "06",
    name: "Béjaïa",
    name_ar: "بجاية",
    name_fr: "Béjaïa",
    coordinates: { lat: 36.7525, lng: 5.0689 },
    dairas: []
  },
  {
    code: "07",
    name: "Biskra",
    name_ar: "بسكرة",
    name_fr: "Biskra",
    coordinates: { lat: 34.8481, lng: 5.7281 },
    dairas: []
  },
  {
    code: "08",
    name: "Béchar",
    name_ar: "بشار",
    name_fr: "Béchar",
    coordinates: { lat: 31.6177, lng: -2.2286 },
    dairas: []
  },
  {
    code: "09",
    name: "Blida",
    name_ar: "البليدة",
    name_fr: "Blida",
    coordinates: { lat: 36.4203, lng: 2.8277 },
    dairas: []
  },
  {
    code: "10",
    name: "Bouira",
    name_ar: "البويرة",
    name_fr: "Bouira",
    coordinates: { lat: 36.3737, lng: 3.9030 },
    dairas: []
  },
  {
    code: "11",
    name: "Tamanrasset",
    name_ar: "تمنراست",
    name_fr: "Tamanrasset",
    coordinates: { lat: 22.7851, lng: 5.5281 },
    dairas: []
  },
  {
    code: "12",
    name: "Tébessa",
    name_ar: "تبسة",
    name_fr: "Tébessa",
    coordinates: { lat: 35.4020, lng: 8.1207 },
    dairas: []
  },
  {
    code: "13",
    name: "Tlemcen",
    name_ar: "تلمسان",
    name_fr: "Tlemcen",
    coordinates: { lat: 34.8786, lng: -1.3150 },
    dairas: []
  },
  {
    code: "14",
    name: "Tiaret",
    name_ar: "تيارت",
    name_fr: "Tiaret",
    coordinates: { lat: 35.3712, lng: 1.3170 },
    dairas: []
  },
  {
    code: "15",
    name: "Tizi Ouzou",
    name_ar: "تيزي وزو",
    name_fr: "Tizi Ouzou",
    coordinates: { lat: 36.7118, lng: 4.0435 },
    dairas: []
  },
  {
    code: "16",
    name: "Alger",
    name_ar: "الجزائر",
    name_fr: "Alger",
    coordinates: { lat: 36.7538, lng: 3.0588 },
    dairas: []
  },
  {
    code: "17",
    name: "Djelfa",
    name_ar: "الجلفة",
    name_fr: "Djelfa",
    coordinates: { lat: 34.6714, lng: 3.2631 },
    dairas: []
  },
  {
    code: "18",
    name: "Jijel",
    name_ar: "جيجل",
    name_fr: "Jijel",
    coordinates: { lat: 36.8190, lng: 5.7667 },
    dairas: []
  },
  {
    code: "19",
    name: "Sétif",
    name_ar: "سطيف",
    name_fr: "Sétif",
    coordinates: { lat: 36.1900, lng: 5.4133 },
    dairas: []
  },
  {
    code: "20",
    name: "Saïda",
    name_ar: "سعيدة",
    name_fr: "Saïda",
    coordinates: { lat: 34.8370, lng: 0.1514 },
    dairas: []
  },
  {
    code: "21",
    name: "Skikda",
    name_ar: "سكيكدة",
    name_fr: "Skikda",
    coordinates: { lat: 36.8761, lng: 6.9093 },
    dairas: []
  },
  {
    code: "22",
    name: "Sidi Bel Abbès",
    name_ar: "سيدي بلعباس",
    name_fr: "Sidi Bel Abbès",
    coordinates: { lat: 35.1977, lng: -0.6388 },
    dairas: []
  },
  {
    code: "23",
    name: "Annaba",
    name_ar: "عنابة",
    name_fr: "Annaba",
    coordinates: { lat: 36.9000, lng: 7.7667 },
    dairas: []
  },
  {
    code: "24",
    name: "Guelma",
    name_ar: "قالمة",
    name_fr: "Guelma",
    coordinates: { lat: 36.4612, lng: 7.4286 },
    dairas: []
  },
  {
    code: "25",
    name: "Constantine",
    name_ar: "قسنطينة",
    name_fr: "Constantine",
    coordinates: { lat: 36.3650, lng: 6.6147 },
    dairas: []
  },
  {
    code: "26",
    name: "Médéa",
    name_ar: "المدية",
    name_fr: "Médéa",
    coordinates: { lat: 36.2640, lng: 2.7540 },
    dairas: []
  },
  {
    code: "27",
    name: "Mostaganem",
    name_ar: "مستغانم",
    name_fr: "Mostaganem",
    coordinates: { lat: 35.9315, lng: 0.0890 },
    dairas: []
  },
  {
    code: "28",
    name: "M'Sila",
    name_ar: "المسيلة",
    name_fr: "M'Sila",
    coordinates: { lat: 35.7056, lng: 4.5415 },
    dairas: []
  },
  {
    code: "29",
    name: "Mascara",
    name_ar: "معسكر",
    name_fr: "Mascara",
    coordinates: { lat: 35.3968, lng: 0.1405 },
    dairas: []
  },
  {
    code: "30",
    name: "Ouargla",
    name_ar: "ورقلة",
    name_fr: "Ouargla",
    coordinates: { lat: 31.9539, lng: 5.3249 },
    dairas: []
  },
  {
    code: "31",
    name: "Oran",
    name_ar: "وهران",
    name_fr: "Oran",
    coordinates: { lat: 35.6969, lng: -0.6331 },
    dairas: []
  },
  {
    code: "32",
    name: "El Bayadh",
    name_ar: "البيض",
    name_fr: "El Bayadh",
    coordinates: { lat: 33.6815, lng: 1.0199 },
    dairas: []
  },
  {
    code: "33",
    name: "Illizi",
    name_ar: "إليزي",
    name_fr: "Illizi",
    coordinates: { lat: 26.4840, lng: 8.4777 },
    dairas: []
  },
  {
    code: "34",
    name: "Bordj Bou Arréridj",
    name_ar: "برج بوعريريج",
    name_fr: "Bordj Bou Arréridj",
    coordinates: { lat: 36.0731, lng: 4.7617 },
    dairas: []
  },
  {
    code: "35",
    name: "Boumerdès",
    name_ar: "بومرداس",
    name_fr: "Boumerdès",
    coordinates: { lat: 36.7667, lng: 3.4667 },
    dairas: []
  },
  {
    code: "36",
    name: "El Tarf",
    name_ar: "الطارف",
    name_fr: "El Tarf",
    coordinates: { lat: 36.7672, lng: 8.3137 },
    dairas: []
  },
  {
    code: "37",
    name: "Tindouf",
    name_ar: "تندوف",
    name_fr: "Tindouf",
    coordinates: { lat: 27.6710, lng: -8.1479 },
    dairas: []
  },
  {
    code: "38",
    name: "Tissemsilt",
    name_ar: "تيسمسيلت",
    name_fr: "Tissemsilt",
    coordinates: { lat: 35.6075, lng: 1.8113 },
    dairas: []
  },
  {
    code: "39",
    name: "El Oued",
    name_ar: "الوادي",
    name_fr: "El Oued",
    coordinates: { lat: 33.3565, lng: 6.8531 },
    dairas: []
  },
  {
    code: "40",
    name: "Khenchela",
    name_ar: "خنشلة",
    name_fr: "Khenchela",
    coordinates: { lat: 35.4361, lng: 7.1433 },
    dairas: []
  },
  {
    code: "41",
    name: "Souk Ahras",
    name_ar: "سوق أهراس",
    name_fr: "Souk Ahras",
    coordinates: { lat: 36.2863, lng: 7.9511 },
    dairas: []
  },
  {
    code: "42",
    name: "Tipaza",
    name_ar: "تيبازة",
    name_fr: "Tipaza",
    coordinates: { lat: 36.5931, lng: 2.4474 },
    dairas: []
  },
  {
    code: "43",
    name: "Mila",
    name_ar: "ميلة",
    name_fr: "Mila",
    coordinates: { lat: 36.4504, lng: 6.2648 },
    dairas: []
  },
  {
    code: "44",
    name: "Aïn Defla",
    name_ar: "عين الدفلى",
    name_fr: "Aïn Defla",
    coordinates: { lat: 36.2639, lng: 1.9681 },
    dairas: []
  },
  {
    code: "45",
    name: "Naâma",
    name_ar: "النعامة",
    name_fr: "Naâma",
    coordinates: { lat: 33.2667, lng: -0.3167 },
    dairas: []
  },
  {
    code: "46",
    name: "Aïn Témouchent",
    name_ar: "عين تموشنت",
    name_fr: "Aïn Témouchent",
    coordinates: { lat: 35.2981, lng: -1.0372 },
    dairas: []
  },
  {
    code: "47",
    name: "Ghardaïa",
    name_ar: "غرداية",
    name_fr: "Ghardaïa",
    coordinates: { lat: 32.4840, lng: 3.6736 },
    dairas: []
  },
  {
    code: "48",
    name: "Relizane",
    name_ar: "غليزان",
    name_fr: "Relizane",
    coordinates: { lat: 35.7370, lng: 0.5559 },
    dairas: []
  },
  {
    code: "49",
    name: "Timimoun",
    name_ar: "تيميمون",
    name_fr: "Timimoun",
    coordinates: { lat: 29.2631, lng: 0.2411 },
    dairas: []
  },
  {
    code: "50",
    name: "Bordj Badji Mokhtar",
    name_ar: "برج باجي مختار",
    name_fr: "Bordj Badji Mokhtar",
    coordinates: { lat: 21.3167, lng: 0.9333 },
    dairas: []
  },
  {
    code: "51",
    name: "Ouled Djellal",
    name_ar: "أولاد جلال",
    name_fr: "Ouled Djellal",
    coordinates: { lat: 34.4167, lng: 5.0833 },
    dairas: []
  },
  {
    code: "52",
    name: "Béni Abbès",
    name_ar: "بني عباس",
    name_fr: "Béni Abbès",
    coordinates: { lat: 30.1333, lng: -2.1667 },
    dairas: []
  },
  {
    code: "53",
    name: "In Salah",
    name_ar: "عين صالح",
    name_fr: "In Salah",
    coordinates: { lat: 27.1930, lng: 2.4608 },
    dairas: []
  },
  {
    code: "54",
    name: "In Guezzam",
    name_ar: "عين قزام",
    name_fr: "In Guezzam",
    coordinates: { lat: 19.5667, lng: 5.7667 },
    dairas: []
  },
  {
    code: "55",
    name: "Touggourt",
    name_ar: "تقرت",
    name_fr: "Touggourt",
    coordinates: { lat: 33.1067, lng: 6.0581 },
    dairas: []
  },
  {
    code: "56",
    name: "Djanet",
    name_ar: "جانت",
    name_fr: "Djanet",
    coordinates: { lat: 24.5542, lng: 9.4840 },
    dairas: []
  },
  {
    code: "57",
    name: "El Meghaier",
    name_ar: "المغير",
    name_fr: "El Meghaier",
    coordinates: { lat: 33.9500, lng: 5.9167 },
    dairas: []
  },
  {
    code: "58",
    name: "El Menia",
    name_ar: "المنيعة",
    name_fr: "El Menia",
    coordinates: { lat: 30.5833, lng: 2.8833 },
    dairas: []
  }
];

// Data loading state
let isDataLoaded = false;
let isDataLoading = false;

/**
 * Initialize complete administrative data
 */
export async function initializeCompleteData(): Promise<void> {
  if (isDataLoaded || isDataLoading) return;

  isDataLoading = true;
  try {
    const completeData = await loadCompleteAlgerianData();
    ALGERIAN_WILAYAS.splice(0, ALGERIAN_WILAYAS.length, ...completeData);
    isDataLoaded = true;
    console.log(`Loaded complete Algerian administrative data: ${ALGERIAN_WILAYAS.length} wilayas`);
  } catch (error) {
    console.error('Failed to load complete administrative data:', error);
  } finally {
    isDataLoading = false;
  }
}

/**
 * Get current wilayas data (may be basic or complete depending on loading state)
 */
export function getCurrentWilayasData(): AlgerianWilaya[] {
  return ALGERIAN_WILAYAS;
}

/**
 * Check if complete data is loaded
 */
export function isCompleteDataLoaded(): boolean {
  return isDataLoaded;
}

// Legacy export for backward compatibility
export const ALGERIAN_CITIES: AlgerianCity[] = ALGERIAN_WILAYAS;

/**
 * Location utility functions for Algerian administrative divisions
 */

/**
 * Get a formatted wilaya string with code
 * @param code The wilaya code
 * @returns Formatted wilaya string (e.g., "16 - Alger")
 */
export const getFormattedWilaya = (code: string | undefined): string => {
  if (!code) return '';

  const wilaya = ALGERIAN_WILAYAS.find(w => w.code === code);
  if (!wilaya) return '';

  return `${wilaya.code} - ${wilaya.name}`;
};

/**
 * Get a wilaya by its code
 * @param code The wilaya code
 * @returns The wilaya object or undefined if not found
 */
export const getWilayaByCode = (code: string | undefined): AlgerianWilaya | undefined => {
  if (!code) return undefined;
  return ALGERIAN_WILAYAS.find(w => w.code === code);
};

/**
 * Search wilayas by name (supports Arabic, French, and Latin names)
 * @param query The search query
 * @param limit Maximum number of results
 * @returns Array of matching wilayas
 */
export const searchWilayas = (query: string, limit: number = 10): AlgerianWilaya[] => {
  if (!query.trim()) return ALGERIAN_WILAYAS.slice(0, limit);

  const searchTerm = query.toLowerCase().trim();

  return ALGERIAN_WILAYAS
    .filter(wilaya =>
      wilaya.name.toLowerCase().includes(searchTerm) ||
      wilaya.name_ar.includes(searchTerm) ||
      wilaya.name_fr.toLowerCase().includes(searchTerm) ||
      wilaya.code.includes(searchTerm)
    )
    .slice(0, limit);
};

/**
 * Enhanced search function for all administrative levels
 * @param query The search query
 * @param limit Maximum number of results
 * @returns Array of search results with hierarchy information
 */
export const searchAllAdministrativeLocations = (query: string, limit: number = 10): SearchResult[] => {
  if (!query.trim()) return [];

  // Use the complete data search if available
  if (isDataLoaded) {
    return searchAllAdministrativeDivisions(ALGERIAN_WILAYAS, query, limit);
  }

  // Fallback to wilaya-only search
  const wilayas = searchWilayas(query, limit);
  return wilayas.map(wilaya => ({
    type: 'wilaya' as const,
    location: wilaya,
    hierarchy: `${wilaya.name}, Algeria`
  }));
};

/**
 * Find location by coordinates with enhanced accuracy
 * @param lat Latitude
 * @param lng Longitude
 * @returns Most accurate location match
 */
export const findLocationByCoordinates = (lat: number, lng: number): SearchResult | null => {
  let bestMatch: SearchResult | null = null;
  let minDistance = Infinity;

  ALGERIAN_WILAYAS.forEach(wilaya => {
    // Check wilaya distance
    if (wilaya.coordinates) {
      const distance = calculateDistance(lat, lng, wilaya.coordinates.lat, wilaya.coordinates.lng);
      if (distance < minDistance) {
        minDistance = distance;
        bestMatch = {
          type: 'wilaya',
          location: wilaya,
          hierarchy: `${wilaya.name}, Algeria`
        };
      }
    }

    // Check dairas if available
    wilaya.dairas?.forEach(daira => {
      if (daira.coordinates) {
        const distance = calculateDistance(lat, lng, daira.coordinates.lat, daira.coordinates.lng);
        if (distance < minDistance) {
          minDistance = distance;
          bestMatch = {
            type: 'daira',
            location: daira,
            hierarchy: `${daira.name}, ${wilaya.name}`
          };
        }
      }

      // Check communes if available
      daira.communes?.forEach(commune => {
        if (commune.coordinates) {
          const distance = calculateDistance(lat, lng, commune.coordinates.lat, commune.coordinates.lng);
          if (distance < minDistance) {
            minDistance = distance;
            bestMatch = {
              type: 'commune',
              location: commune,
              hierarchy: `${commune.name}, ${daira.name}, ${wilaya.name}`
            };
          }
        }
      });
    });
  });

  return bestMatch;
};

/**
 * Find the nearest wilaya to given coordinates
 * @param lat Latitude
 * @param lng Longitude
 * @returns The nearest wilaya
 */
export const findNearestWilaya = (lat: number, lng: number): AlgerianWilaya | undefined => {
  let nearestWilaya: AlgerianWilaya | undefined;
  let minDistance = Infinity;

  for (const wilaya of ALGERIAN_WILAYAS) {
    if (!wilaya.coordinates) continue;

    const distance = calculateDistance(
      lat, lng,
      wilaya.coordinates.lat, wilaya.coordinates.lng
    );

    if (distance < minDistance) {
      minDistance = distance;
      nearestWilaya = wilaya;
    }
  }

  return nearestWilaya;
};

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param lat1 First latitude
 * @param lng1 First longitude
 * @param lat2 Second latitude
 * @param lng2 Second longitude
 * @returns Distance in kilometers
 */
export const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Format coordinates to Algerian address with enhanced accuracy
 * @param lat Latitude
 * @param lng Longitude
 * @param street Optional street/area name
 * @returns Formatted address with proper hierarchy
 */
export const formatAlgerianAddress = (
  lat: number,
  lng: number,
  street?: string
): FormattedAddress | null => {
  const locationMatch = findLocationByCoordinates(lat, lng);
  if (!locationMatch) return null;

  const location = locationMatch.location;

  // Get wilaya information
  let wilaya: AlgerianWilaya;
  let daira: AlgerianDaira | undefined;
  let commune: AlgerianCommune | undefined;

  if (locationMatch.type === 'wilaya') {
    wilaya = location as AlgerianWilaya;
  } else if (locationMatch.type === 'daira') {
    daira = location as AlgerianDaira;
    wilaya = ALGERIAN_WILAYAS.find(w => w.code === daira!.wilaya_code)!;
  } else {
    commune = location as AlgerianCommune;
    wilaya = ALGERIAN_WILAYAS.find(w => w.code === commune!.wilaya_code)!;
    daira = wilaya.dairas.find(d => d.code === commune!.daira_code);
  }

  return {
    street: street || '',
    commune: commune?.name || daira?.name || wilaya.name,
    commune_ar: commune?.name_ar || daira?.name_ar || wilaya.name_ar,
    commune_fr: commune?.name_fr || daira?.name_fr || wilaya.name_fr,
    daira: daira?.name || wilaya.name,
    daira_ar: daira?.name_ar || wilaya.name_ar,
    daira_fr: daira?.name_fr || wilaya.name_fr,
    wilaya: wilaya.name,
    wilaya_ar: wilaya.name_ar,
    wilaya_fr: wilaya.name_fr,
    country: 'Algeria',
    coordinates: { lat, lng }
  };
};

/**
 * Generate a human-readable address string with proper hierarchy
 * @param address FormattedAddress object
 * @returns Formatted address string
 */
export const generateAddressString = (address: FormattedAddress): string => {
  const parts = [];

  if (address.street) parts.push(address.street);

  // Add commune if different from daira and wilaya
  if (address.commune &&
      address.commune !== address.daira &&
      address.commune !== address.wilaya) {
    parts.push(address.commune);
  }

  // Add daira if different from wilaya
  if (address.daira && address.daira !== address.wilaya) {
    parts.push(address.daira);
  }

  parts.push(address.wilaya);
  parts.push(address.country);

  return parts.join(', ');
};

/**
 * Generate address string with Arabic names
 * @param address FormattedAddress object
 * @returns Formatted address string in Arabic
 */
export const generateAddressStringArabic = (address: FormattedAddress): string => {
  const parts = [];

  if (address.street) parts.push(address.street);

  if (address.commune_ar &&
      address.commune_ar !== address.daira_ar &&
      address.commune_ar !== address.wilaya_ar) {
    parts.push(address.commune_ar);
  }

  if (address.daira_ar && address.daira_ar !== address.wilaya_ar) {
    parts.push(address.daira_ar);
  }

  parts.push(address.wilaya_ar);
  parts.push('الجزائر'); // Algeria in Arabic

  return parts.join('، ');
};

// Legacy functions for backward compatibility
export const getFormattedCity = getFormattedWilaya;
export const getCityByCode = getWilayaByCode;
