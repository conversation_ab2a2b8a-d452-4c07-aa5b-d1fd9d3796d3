// =====================================================
// AROUZ MARKET - Supabase Categories Compatibility Layer
// Purpose: Maintain exact same interface as localStorage system
// =====================================================

import { useCategories, useSubcategories, useCategoryUtils } from '../hooks/useCategories';

// Legacy interface compatibility
export interface LegacyCategory {
  id: string;
  name: string;
  displayName: string;
  description: string;
  subcategories: LegacySubcategory[];
}

export interface LegacySubcategory {
  id: string;
  name: string;
  displayName: string;
}

// Hook to get categories in legacy format
export const useLegacyCategories = () => {
  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const { data: subcategories = [], isLoading: subcategoriesLoading, error: subcategoriesError } = useSubcategories();

  const isLoading = categoriesLoading || subcategoriesLoading;
  const error = categoriesError || subcategoriesError;

  // Transform Supabase data to legacy format
  const legacyCategories: LegacyCategory[] = categories.map(category => ({
    id: category.id,
    name: category.name,
    displayName: category.display_name,
    description: category.description || '',
    subcategories: subcategories
      .filter(sub => sub.category_id === category.id)
      .map(sub => ({
        id: sub.id,
        name: sub.name,
        displayName: sub.display_name,
      }))
      .sort((a, b) => a.name.localeCompare(b.name))
  }));

  return {
    categories: legacyCategories,
    isLoading,
    error,
  };
};

// Get category by ID (legacy format)
export const useLegacyCategoryById = (categoryId: string) => {
  const { categories, isLoading, error } = useLegacyCategories();
  
  const category = categories.find(cat => cat.id === categoryId);
  
  return {
    category: category || null,
    isLoading,
    error,
  };
};

// Get subcategories for a category (legacy format)
export const useLegacySubcategoriesByCategory = (categoryId: string) => {
  const { categories, isLoading, error } = useLegacyCategories();
  
  const category = categories.find(cat => cat.id === categoryId);
  const subcategories = category?.subcategories || [];
  
  return {
    subcategories,
    isLoading,
    error,
  };
};

// Static fallback data (for emergency fallback only)
export const FALLBACK_CATEGORIES: LegacyCategory[] = [
  {
    id: 'brakes',
    name: 'Brake Parts & Systems',
    displayName: 'Brake Parts',
    description: 'Brake discs, pads, calipers, and related brake system components',
    subcategories: [
      { id: 'brake-disc', name: 'Brake Disc', displayName: 'Brake Disc' },
      { id: 'brake-pad-set', name: 'Brake Pad Set', displayName: 'Brake Pad Set' },
      { id: 'brake-caliper', name: 'Brake Caliper', displayName: 'Brake Caliper' },
    ]
  },
  {
    id: 'filters',
    name: 'Filters & Filtration',
    displayName: 'Filters',
    description: 'Air filters, oil filters, fuel filters, and all filtration systems',
    subcategories: [
      { id: 'air-filter', name: 'Air Filter', displayName: 'Air Filter' },
      { id: 'oil-filter', name: 'Oil Filter', displayName: 'Oil Filter' },
      { id: 'fuel-filter', name: 'Fuel Filter', displayName: 'Fuel Filter' },
    ]
  }
];

// Utility functions for backward compatibility (non-hook versions)
export const getCategoryDisplayName = (categoryId: string): string => {
  // Fallback to static data for now - these should be replaced with proper context usage
  const category = FALLBACK_CATEGORIES.find(cat => cat.id === categoryId);
  return category?.display_name || categoryId;
};

export const getCategoryPrefix = (categoryId: string): string => {
  // Fallback to static data for now - these should be replaced with proper context usage
  const category = FALLBACK_CATEGORIES.find(cat => cat.id === categoryId);
  return category?.id_prefix || 'PROD';
};

export const getSubcategoryDisplayName = (subcategoryId: string): string => {
  // Fallback to static data for now - these should be replaced with proper context usage
  const subcategory = FALLBACK_CATEGORIES
    .flatMap(cat => cat.subcategories || [])
    .find(sub => sub.id === subcategoryId);
  return subcategory?.display_name || subcategoryId;
};

// Export for backward compatibility
export const categoryData = FALLBACK_CATEGORIES;

// Migration status checker
export const useMigrationStatus = () => {
  const { categories, isLoading, error } = useLegacyCategories();
  
  const isMigrated = !isLoading && !error && categories.length > 0;
  const shouldUseFallback = error || (!isLoading && categories.length === 0);
  
  return {
    isMigrated,
    shouldUseFallback,
    isLoading,
    error,
    categoriesCount: categories.length,
  };
};
