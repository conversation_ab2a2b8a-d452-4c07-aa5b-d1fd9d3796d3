// Sample car data for the compatibility hub
export const cars: Record<string, {
  models: Array<{
    name: string;
    generation?: string;
    productionYears?: string;
    bodyType?: string;
    engines?: Array<{
      code?: string;
      fuel_type?: string;
      power_kW?: number;
      power_HP?: number;
      fullName?: string;
      productionYears?: string;
    }>;
  }>;
}> = {
  "Renault": {
    models: [
      {
        name: "Clio",
        generation: "V",
        productionYears: "2019-Present",
        bodyType: "Hatchback",
        engines: [
          {
            code: "H4D",
            fuel_type: "Petrol",
            power_kW: 74,
            power_HP: 100,
            fullName: "1.0 TCe (100 HP)",
            productionYears: "2019-Present"
          },
          {
            code: "H4D",
            fuel_type: "Petrol",
            power_kW: 67,
            power_HP: 90,
            fullName: "1.0 TCe (90 HP)",
            productionYears: "2019-Present"
          },
          {
            code: "K9K",
            fuel_type: "Diesel",
            power_kW: 63,
            power_HP: 85,
            fullName: "1.5 dCi (85 HP)",
            productionYears: "2019-Present"
          }
        ]
      },
      {
        name: "Megan<PERSON>",
        generation: "IV",
        productionYears: "2016-Present",
        bodyType: "Hatchback",
        engines: [
          {
            code: "H5H",
            fuel_type: "Petrol",
            power_kW: 103,
            power_HP: 140,
            fullName: "1.3 TCe (140 HP)",
            productionYears: "2018-Present"
          },
          {
            code: "K9K",
            fuel_type: "Diesel",
            power_kW: 85,
            power_HP: 115,
            fullName: "1.5 Blue dCi (115 HP)",
            productionYears: "2018-Present"
          }
        ]
      }
    ]
  },
  "Dacia": {
    models: [
      {
        name: "Sandero",
        generation: "III",
        productionYears: "2020-Present",
        bodyType: "Hatchback",
        engines: [
          {
            code: "H4D",
            fuel_type: "Petrol",
            power_kW: 67,
            power_HP: 90,
            fullName: "1.0 TCe (90 HP)",
            productionYears: "2020-Present"
          },
          {
            code: "D4F",
            fuel_type: "Petrol/LPG",
            power_kW: 74,
            power_HP: 100,
            fullName: "1.0 ECO-G (100 HP)",
            productionYears: "2020-Present"
          }
        ]
      },
      {
        name: "Duster",
        generation: "II",
        productionYears: "2018-Present",
        bodyType: "SUV",
        engines: [
          {
            code: "H5H",
            fuel_type: "Petrol",
            power_kW: 96,
            power_HP: 130,
            fullName: "1.3 TCe (130 HP)",
            productionYears: "2018-Present"
          },
          {
            code: "K9K",
            fuel_type: "Diesel",
            power_kW: 85,
            power_HP: 115,
            fullName: "1.5 dCi (115 HP)",
            productionYears: "2018-Present"
          }
        ]
      }
    ]
  },
  "Peugeot": {
    models: [
      {
        name: "208",
        generation: "II",
        productionYears: "2019-Present",
        bodyType: "Hatchback",
        engines: [
          {
            code: "EB2",
            fuel_type: "Petrol",
            power_kW: 74,
            power_HP: 100,
            fullName: "1.2 PureTech (100 HP)",
            productionYears: "2019-Present"
          },
          {
            code: "DV5",
            fuel_type: "Diesel",
            power_kW: 74,
            power_HP: 100,
            fullName: "1.5 BlueHDi (100 HP)",
            productionYears: "2019-Present"
          }
        ]
      },
      {
        name: "3008",
        generation: "II",
        productionYears: "2016-Present",
        bodyType: "SUV",
        engines: [
          {
            code: "EP6",
            fuel_type: "Petrol",
            power_kW: 96,
            power_HP: 130,
            fullName: "1.2 PureTech (130 HP)",
            productionYears: "2016-Present"
          },
          {
            code: "DW10",
            fuel_type: "Diesel",
            power_kW: 96,
            power_HP: 130,
            fullName: "1.5 BlueHDi (130 HP)",
            productionYears: "2018-Present"
          }
        ]
      }
    ]
  }
};
