/**
 * Algeria Administrative Data Loader
 * Loads complete administrative divisions from external source
 */

import { AlgerianWilaya, AlgerianDaira, AlgerianCommune, SearchResult } from './algeria-cities';

interface RawAlgerianData {
  id: number;
  commune_name_ascii: string;
  commune_name: string;
  daira_name_ascii: string;
  daira_name: string;
  wilaya_code: string;
  wilaya_name_ascii: string;
  wilaya_name: string;
}

/**
 * Load complete Algerian administrative data from external source
 */
export async function loadCompleteAlgerianData(): Promise<AlgerianWilaya[]> {
  try {
    // Load from the GitHub repository with complete data
    const response = await fetch('https://raw.githubusercontent.com/othmanus/algeria-cities/master/json/algeria_cities.json');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.status}`);
    }
    
    const rawData: RawAlgerianData[] = await response.json();
    
    // Transform raw data into structured format
    return transformRawDataToStructured(rawData);
  } catch (error) {
    console.error('Error loading Algerian administrative data:', error);
    // Return fallback data if external source fails
    return getFallbackWilayasData();
  }
}

/**
 * Transform raw data into structured wilaya/daira/commune hierarchy
 */
function transformRawDataToStructured(rawData: RawAlgerianData[]): AlgerianWilaya[] {
  const wilayasMap = new Map<string, AlgerianWilaya>();
  const dairasMap = new Map<string, AlgerianDaira>();
  
  // Process each raw entry
  rawData.forEach(entry => {
    const wilayaKey = entry.wilaya_code;
    const dairaKey = `${entry.wilaya_code}-${entry.daira_name_ascii}`;
    
    // Create or get wilaya
    if (!wilayasMap.has(wilayaKey)) {
      wilayasMap.set(wilayaKey, {
        code: entry.wilaya_code,
        name: entry.wilaya_name_ascii,
        name_ar: entry.wilaya_name,
        name_fr: entry.wilaya_name_ascii,
        coordinates: getWilayaCoordinates(entry.wilaya_code),
        dairas: []
      });
    }
    
    // Create or get daira
    if (!dairasMap.has(dairaKey)) {
      const daira: AlgerianDaira = {
        code: `${entry.wilaya_code}${String(dairasMap.size + 1).padStart(2, '0')}`,
        name: entry.daira_name_ascii,
        name_ar: entry.daira_name,
        name_fr: entry.daira_name_ascii,
        wilaya_code: entry.wilaya_code,
        coordinates: getDairaCoordinates(entry.wilaya_code, entry.daira_name_ascii),
        communes: []
      };
      
      dairasMap.set(dairaKey, daira);
      wilayasMap.get(wilayaKey)!.dairas.push(daira);
    }
    
    // Create commune
    const commune: AlgerianCommune = {
      code: `${entry.wilaya_code}${String(dairasMap.get(dairaKey)!.communes.length + 1).padStart(4, '0')}`,
      name: entry.commune_name_ascii,
      name_ar: entry.commune_name,
      name_fr: entry.commune_name_ascii,
      wilaya_code: entry.wilaya_code,
      daira_code: dairasMap.get(dairaKey)!.code,
      coordinates: getCommuneCoordinates(entry.wilaya_code, entry.commune_name_ascii)
    };
    
    dairasMap.get(dairaKey)!.communes.push(commune);
  });
  
  // Convert map to array and sort by wilaya code
  return Array.from(wilayasMap.values()).sort((a, b) => a.code.localeCompare(b.code));
}

/**
 * Get coordinates for wilaya (using known coordinates)
 */
function getWilayaCoordinates(wilayaCode: string): { lat: number; lng: number } {
  const coordinates: Record<string, { lat: number; lng: number }> = {
    "01": { lat: 27.8702, lng: -0.2841 }, // Adrar
    "02": { lat: 36.1654, lng: 1.3347 },  // Chlef
    "03": { lat: 33.8000, lng: 2.8667 },  // Laghouat
    "04": { lat: 35.8753, lng: 7.1135 },  // Oum El Bouaghi
    "05": { lat: 35.5559, lng: 6.1740 },  // Batna
    "06": { lat: 36.7525, lng: 5.0689 },  // Béjaïa
    "07": { lat: 34.8361, lng: 5.7253 },  // Biskra
    "08": { lat: 31.6167, lng: -2.2167 }, // Béchar
    "09": { lat: 36.7763, lng: 3.0586 },  // Blida
    "10": { lat: 35.3667, lng: 1.0333 },  // Bouira
    "11": { lat: 36.4667, lng: 2.8333 },  // Tamanrasset
    "12": { lat: 35.4000, lng: 1.3167 },  // Tébessa
    "13": { lat: 35.4056, lng: 8.1222 },  // Tlemcen
    "14": { lat: 35.8833, lng: -1.3167 }, // Tiaret
    "15": { lat: 36.4167, lng: 1.4667 },  // Tizi Ouzou
    "16": { lat: 36.7631, lng: 3.0506 },  // Algiers
    "17": { lat: 35.6911, lng: 0.6417 },  // Djelfa
    "18": { lat: 35.9297, lng: 5.7314 },  // Jijel
    "19": { lat: 36.3650, lng: 6.6147 },  // Sétif
    "20": { lat: 36.1889, lng: 5.4139 },  // Saïda
    "21": { lat: 36.4611, lng: -1.0544 }, // Skikda
    "22": { lat: 35.1681, lng: -0.6406 }, // Sidi Bel Abbès
    "23": { lat: 36.7069, lng: 7.7336 },  // Annaba
    "24": { lat: 35.2028, lng: 6.1444 },  // Guelma
    "25": { lat: 34.3667, lng: 2.8833 },  // Constantine
    "26": { lat: 35.8714, lng: 6.2675 },  // Médéa
    "27": { lat: 35.5225, lng: 0.1408 },  // Mostaganem
    "28": { lat: 35.9314, lng: -0.7031 }, // M'Sila
    "29": { lat: 35.6969, lng: 5.2625 },  // Mascara
    "30": { lat: 31.0500, lng: 5.7000 },  // Ouargla
    "31": { lat: 35.6969, lng: -0.6331 }, // Oran
    "32": { lat: 34.8611, lng: 3.8167 },  // El Bayadh
    "33": { lat: 36.4667, lng: 4.0667 },  // Illizi
    "34": { lat: 36.2667, lng: 9.2333 },  // Bordj Bou Arréridj
    "35": { lat: 36.6167, lng: 8.1167 },  // Boumerdès
    "36": { lat: 22.7833, lng: 5.5167 },  // El Tarf
    "37": { lat: 36.4667, lng: 7.7667 },  // Tindouf
    "38": { lat: 27.6833, lng: -8.1667 }, // Tissemsilt
    "39": { lat: 35.6000, lng: 1.8667 },  // El Oued
    "40": { lat: 33.5000, lng: 6.8667 },  // Khenchela
    "41": { lat: 35.4056, lng: 7.7667 },  // Souk Ahras
    "42": { lat: 36.1167, lng: 7.3333 },  // Tipaza
    "43": { lat: 36.5833, lng: 2.3333 },  // Mila
    "44": { lat: 36.4667, lng: 6.8667 },  // Aïn Defla
    "45": { lat: 33.2667, lng: -0.3000 }, // Naâma
    "46": { lat: 35.2833, lng: -1.4667 }, // Aïn Témouchent
    "47": { lat: 32.4833, lng: 3.6667 },  // Ghardaïa
    "48": { lat: 35.7333, lng: 0.5500 },  // Relizane
    // New wilayas (49-58)
    "49": { lat: 29.2631, lng: 0.2298 },  // Timimoun
    "50": { lat: 21.3167, lng: 0.9500 },  // Bordj Badji Mokhtar
    "51": { lat: 34.3833, lng: 5.0833 },  // Ouled Djellal
    "52": { lat: 30.1333, lng: -2.1667 }, // Béni Abbès
    "53": { lat: 27.2167, lng: 2.4667 },  // In Salah
    "54": { lat: 19.5667, lng: 5.7667 },  // In Guezzam
    "55": { lat: 33.1167, lng: 6.0667 },  // Touggourt
    "56": { lat: 24.5500, lng: 9.4833 },  // Djanet
    "57": { lat: 33.9500, lng: 5.9167 },  // El Meghaier
    "58": { lat: 30.5833, lng: 2.8833 }   // El Menia
  };
  
  return coordinates[wilayaCode] || { lat: 36.7631, lng: 3.0506 }; // Default to Algiers
}

/**
 * Get approximate coordinates for daira (offset from wilaya)
 */
function getDairaCoordinates(wilayaCode: string, dairaName: string): { lat: number; lng: number } {
  const wilayaCoords = getWilayaCoordinates(wilayaCode);
  // Add small random offset for daira
  const hash = dairaName.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
  const latOffset = (hash % 100 - 50) / 1000; // ±0.05 degrees
  const lngOffset = ((hash * 7) % 100 - 50) / 1000; // ±0.05 degrees
  
  return {
    lat: wilayaCoords.lat + latOffset,
    lng: wilayaCoords.lng + lngOffset
  };
}

/**
 * Get approximate coordinates for commune (offset from daira)
 */
function getCommuneCoordinates(wilayaCode: string, communeName: string): { lat: number; lng: number } {
  const wilayaCoords = getWilayaCoordinates(wilayaCode);
  // Add small random offset for commune
  const hash = communeName.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
  const latOffset = (hash % 200 - 100) / 2000; // ±0.05 degrees
  const lngOffset = ((hash * 13) % 200 - 100) / 2000; // ±0.05 degrees
  
  return {
    lat: wilayaCoords.lat + latOffset,
    lng: wilayaCoords.lng + lngOffset
  };
}

/**
 * Fallback data with essential wilayas if external source fails
 */
function getFallbackWilayasData(): AlgerianWilaya[] {
  return [
    {
      code: "01",
      name: "Adrar",
      name_ar: "أدرار",
      name_fr: "Adrar",
      coordinates: { lat: 27.8702, lng: -0.2841 },
      dairas: []
    },
    {
      code: "16",
      name: "Algiers",
      name_ar: "الجزائر",
      name_fr: "Alger",
      coordinates: { lat: 36.7631, lng: 3.0506 },
      dairas: []
    }
    // Add more essential wilayas as needed
  ];
}

/**
 * Search all administrative divisions
 */
export function searchAllAdministrativeDivisions(
  wilayas: AlgerianWilaya[],
  query: string,
  limit: number = 10
): SearchResult[] {
  if (!query.trim()) return [];
  
  const searchTerm = query.toLowerCase().trim();
  const results: SearchResult[] = [];
  
  wilayas.forEach(wilaya => {
    // Search wilayas
    if (
      wilaya.name.toLowerCase().includes(searchTerm) ||
      wilaya.name_ar.includes(searchTerm) ||
      wilaya.name_fr.toLowerCase().includes(searchTerm) ||
      wilaya.code.includes(searchTerm)
    ) {
      results.push({
        type: 'wilaya',
        location: wilaya,
        hierarchy: `${wilaya.name}, Algeria`
      });
    }
    
    // Search dairas
    wilaya.dairas.forEach(daira => {
      if (
        daira.name.toLowerCase().includes(searchTerm) ||
        daira.name_ar.includes(searchTerm) ||
        daira.name_fr.toLowerCase().includes(searchTerm)
      ) {
        results.push({
          type: 'daira',
          location: daira,
          hierarchy: `${daira.name}, ${wilaya.name}`
        });
      }
      
      // Search communes
      daira.communes.forEach(commune => {
        if (
          commune.name.toLowerCase().includes(searchTerm) ||
          commune.name_ar.includes(searchTerm) ||
          commune.name_fr.toLowerCase().includes(searchTerm)
        ) {
          results.push({
            type: 'commune',
            location: commune,
            hierarchy: `${commune.name}, ${daira.name}, ${wilaya.name}`
          });
        }
      });
    });
  });
  
  return results.slice(0, limit);
}
