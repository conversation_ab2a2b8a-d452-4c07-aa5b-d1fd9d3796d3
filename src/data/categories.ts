/**
 * Centralized Categories Configuration
 * 
 * This file contains all category and subcategory data used throughout the application.
 * It ensures consistency across all components and provides a single source of truth.
 */

export interface CategoryData {
  id: string;
  name: string;
  prefix: string;
  subcategories: SubcategoryData[];
}

export interface SubcategoryData {
  id: string;
  name: string;
}

/**
 * Complete categories configuration
 */
export const CATEGORIES: CategoryData[] = [
  {
    id: 'tyres',
    name: 'Tyres & Related Products',
    prefix: 'TYR',
    subcategories: [
      { id: 'tyres', name: 'Tyres' },
      { id: 'wheels', name: 'Wheels' },
      { id: 'rims', name: 'Rims' },
      { id: 'wheel-accessories', name: 'Wheel Accessories' },
      { id: 'tyre-accessories', name: 'Tyre Accessories' },
      { id: 'valve-stems', name: 'Valve Stems' },
      { id: 'wheel-weights', name: 'Wheel Weights' },
      { id: 'tyre-pressure-monitoring', name: 'Tyre Pressure Monitoring' },
      { id: 'wheel-spacers', name: 'Wheel Spacers' },
      { id: 'lug-nuts', name: 'Lug Nuts' },
      { id: 'wheel-bolts', name: 'Wheel Bolts' },
      { id: 'hub-caps', name: 'Hub Caps' },
      { id: 'wheel-covers', name: 'Wheel Covers' },
      { id: 'tyre-chains', name: 'Tyre Chains' },
      { id: 'run-flat-inserts', name: 'Run Flat Inserts' },
      { id: 'wheel-alignment', name: 'Wheel Alignment' },
      { id: 'balancing-weights', name: 'Balancing Weights' },
      { id: 'tyre-repair', name: 'Tyre Repair' }
    ]
  },
  {
    id: 'brakes',
    name: 'Brake Parts & Systems',
    prefix: 'BRK',
    subcategories: [
      { id: 'brake-discs', name: 'Brake Discs' },
      { id: 'brake-pads', name: 'Brake Pads' },
      { id: 'brake-shoes', name: 'Brake Shoes' },
      { id: 'brake-drums', name: 'Brake Drums' },
      { id: 'brake-calipers', name: 'Brake Calipers' },
      { id: 'brake-cylinders', name: 'Brake Cylinders' },
      { id: 'brake-hoses', name: 'Brake Hoses' },
      { id: 'brake-lines', name: 'Brake Lines' },
      { id: 'brake-fluid', name: 'Brake Fluid' },
      { id: 'brake-boosters', name: 'Brake Boosters' },
      { id: 'master-cylinders', name: 'Master Cylinders' },
      { id: 'brake-sensors', name: 'Brake Sensors' },
      { id: 'abs-components', name: 'ABS Components' },
      { id: 'parking-brake', name: 'Parking Brake' },
      { id: 'brake-hardware', name: 'Brake Hardware' },
      { id: 'brake-tools', name: 'Brake Tools' },
      { id: 'performance-brakes', name: 'Performance Brakes' },
      { id: 'brake-maintenance', name: 'Brake Maintenance' }
    ]
  },
  {
    id: 'filters',
    name: 'Filters & Filtration',
    prefix: 'FLT',
    subcategories: [
      { id: 'air-filters', name: 'Air Filters' },
      { id: 'oil-filters', name: 'Oil Filters' },
      { id: 'fuel-filters', name: 'Fuel Filters' },
      { id: 'cabin-filters', name: 'Cabin Filters' },
      { id: 'hydraulic-filters', name: 'Hydraulic Filters' },
      { id: 'transmission-filters', name: 'Transmission Filters' },
      { id: 'coolant-filters', name: 'Coolant Filters' },
      { id: 'power-steering-filters', name: 'Power Steering Filters' },
      { id: 'differential-filters', name: 'Differential Filters' },
      { id: 'breather-filters', name: 'Breather Filters' },
      { id: 'intake-filters', name: 'Intake Filters' },
      { id: 'exhaust-filters', name: 'Exhaust Filters' },
      { id: 'particulate-filters', name: 'Particulate Filters' },
      { id: 'adblue-filters', name: 'AdBlue Filters' },
      { id: 'vacuum-filters', name: 'Vacuum Filters' },
      { id: 'filter-housings', name: 'Filter Housings' },
      { id: 'filter-elements', name: 'Filter Elements' },
      { id: 'filter-accessories', name: 'Filter Accessories' }
    ]
  },
  {
    id: 'oils-fluids',
    name: 'Oils & Fluids',
    prefix: 'PROD',
    subcategories: [
      { id: 'engine-lubrication-oil', name: 'Engine Lubrication Oil' },
      { id: 'hydraulic-system-oil', name: 'Hydraulic System Oil' },
      { id: 'automatic-transmission-fluid-atf', name: 'Automatic Transmission Fluid (ATF)' },
      { id: 'coolant-antifreeze-liquid', name: 'Coolant & Antifreeze Liquid' },
      { id: 'brake-system-fluid', name: 'Brake System Fluid' },
      { id: 'power-steering-hydraulic-fluid', name: 'Power Steering Hydraulic Fluid' },
      { id: 'transmission-gear-oil', name: 'Transmission Gear Oil' },
      { id: 'windshield-washer-solution', name: 'Windshield Washer Solution' },
      { id: 'distilled-water-automotive', name: 'Distilled Water for Automotive Use' },
      { id: 'diesel-exhaust-fluid-adblue', name: 'Diesel Exhaust Fluid (AdBlue Equivalent)' },
      { id: 'oil-treatment-performance-enhancer', name: 'Oil Treatment & Performance Enhancer' },
      { id: 'soot-particle-filter-cleaner', name: 'Soot & Particle Filter Cleaner' },
      { id: 'transmission-treatment-additives', name: 'Transmission Treatment Additives' }
    ]
  }
];

/**
 * Get category data by ID
 */
export const getCategoryById = (categoryId: string): CategoryData | undefined => {
  return CATEGORIES.find(cat => cat.id === categoryId);
};

/**
 * Get category prefix by ID
 */
export const getCategoryPrefix = (categoryId: string): string => {
  const category = getCategoryById(categoryId);
  return category?.prefix || 'PROD';
};

/**
 * Get all categories except tyres (for unified table)
 */
export const getAllOtherCategories = (): CategoryData[] => {
  return CATEGORIES.filter(cat => cat.id !== 'tyres');
};

/**
 * Get all subcategories for a category
 */
export const getSubcategoriesForCategory = (categoryId: string): SubcategoryData[] => {
  const category = getCategoryById(categoryId);
  return category?.subcategories || [];
};

/**
 * Get category name by ID
 */
export const getCategoryName = (categoryId: string): string => {
  const category = getCategoryById(categoryId);
  return category?.name || categoryId;
};

/**
 * Check if a category exists
 */
export const categoryExists = (categoryId: string): boolean => {
  return CATEGORIES.some(cat => cat.id === categoryId);
};

/**
 * Get all category IDs
 */
export const getAllCategoryIds = (): string[] => {
  return CATEGORIES.map(cat => cat.id);
};

/**
 * Get categories for unified table (all except tyres)
 */
export const getUnifiedTableCategories = (): string[] => {
  return getAllOtherCategories().map(cat => cat.id);
};
