
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { ArrowRight, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

export function Hero() {
  const { t } = useTranslation();

  return (
    <div className="relative overflow-hidden bg-white">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

      <div className="container relative py-16 md:py-24 h-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-0 items-stretch h-full">
          <div className="flex flex-col gap-6">
            {/* Pre-title */}
            <div className="inline-flex items-center rounded-full border border-electric-orange/30 bg-electric-orange/10 px-3 py-1 text-sm text-electric-orange">
              <span className="font-medium">
                {t('public.prelaunchAnnouncement')}
              </span>
            </div>

            {/* Main Title */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-midnight-blue">
              {t('public.heroTitle')}
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-muted-foreground max-w-md">
              {t('public.heroSubtitle')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mt-4">
              <Button size="lg" className="bg-electric-orange hover:bg-electric-orange/90 text-white" asChild>
                <Link to="/#waitlist">
                  {t('public.getUpdates')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>

              <Button size="lg" variant="outline" className="border-electric-orange text-electric-orange hover:bg-electric-orange/10" asChild>
                <Link to="/features">
                  {t('public.explorePlatform')}
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  {t('public.statAlgerian')}
                </span>
                <span className="text-2xl font-bold text-midnight-blue">
                  100%
                </span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  {t('public.statCategories')}
                </span>
                <span className="text-2xl font-bold text-midnight-blue">
                  50+
                </span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  {t('public.statAIPowered')}
                </span>
                <span className="text-2xl font-bold text-midnight-blue">
                  {t('public.statYes')}
                </span>
              </div>

              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">
                  {t('public.statRealTime')}
                </span>
                <span className="text-2xl font-bold text-midnight-blue">
                  {t('public.statSync')}
                </span>
              </div>
            </div>
          </div>

          {/* Hero Image - Dashboard Image covering exactly half of the section */}
          <div className="relative h-[400px] lg:h-full w-full lg:min-h-[600px] mt-8 lg:mt-0">
            <img
              src="/images/HeroSection.png"
              alt="AROUZ MARKET Dashboard"
              className="w-full h-full object-cover rounded-lg lg:rounded-none"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
