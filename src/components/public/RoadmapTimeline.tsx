import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { CheckCircle2, Circle } from 'lucide-react';

interface TimelineItem {
  id: string;
  quarter: string;
  year: string;
  title: string;
  description: string;
  isCompleted?: boolean;
  isCurrent?: boolean;
}

export function RoadmapTimeline() {
  const { t } = useTranslation();
  
  const timelineItems: TimelineItem[] = [
    {
      id: 'q1-2025',
      quarter: 'Q1',
      year: '2025',
      title: t('public.roadmapQ1Title'),
      description: t('public.roadmapQ1Description'),
      isCompleted: false,
      isCurrent: false,
    },
    {
      id: 'q2-2025',
      quarter: 'Q2',
      year: '2025',
      title: t('public.roadmapQ2Title'),
      description: t('public.roadmapQ2Description'),
      isCompleted: false,
      isCurrent: false,
    },
    {
      id: 'q3-2025',
      quarter: 'Q3',
      year: '2025',
      title: t('public.roadmapQ3Title'),
      description: t('public.roadmapQ3Description'),
      isCompleted: false,
      isCurrent: true,
    },
    {
      id: 'q4-2025',
      quarter: 'Q4',
      year: '2025',
      title: t('public.roadmapQ4Title'),
      description: t('public.roadmapQ4Description'),
      isCompleted: false,
      isCurrent: false,
    },
  ];
  
  return (
    <div className="relative">
      {/* Timeline Line */}
      <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-px bg-border transform md:-translate-x-px" />
      
      {/* Timeline Items */}
      <div className="relative space-y-12">
        {timelineItems.map((item, index) => (
          <div 
            key={item.id}
            className={cn(
              "relative flex flex-col md:flex-row md:items-center",
              index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
            )}
          >
            {/* Timeline Marker */}
            <div className="absolute left-4 md:left-1/2 transform -translate-x-1/2 flex items-center justify-center">
              {item.isCompleted ? (
                <CheckCircle2 className="h-8 w-8 text-green-500 bg-white rounded-full" />
              ) : item.isCurrent ? (
                <div className="h-8 w-8 rounded-full border-2 border-electric-orange bg-white flex items-center justify-center">
                  <div className="h-4 w-4 rounded-full bg-electric-orange animate-pulse" />
                </div>
              ) : (
                <Circle className="h-8 w-8 text-muted-foreground bg-white rounded-full" />
              )}
            </div>
            
            {/* Content */}
            <div className={cn(
              "ml-12 md:ml-0 md:w-1/2",
              index % 2 === 0 ? "md:pr-12" : "md:pl-12",
              "pb-12 md:pb-0"
            )}>
              <div className={cn(
                "rounded-lg border p-6",
                item.isCurrent ? "border-electric-orange bg-electric-orange/5" : "border-border bg-background"
              )}>
                <div className="flex items-center gap-2 mb-2">
                  <div className={cn(
                    "flex items-center justify-center rounded-full px-3 py-1 text-sm font-medium",
                    item.isCurrent ? "bg-electric-orange/20 text-electric-orange" : "bg-muted text-muted-foreground"
                  )}>
                    {item.quarter} {item.year}
                  </div>
                  
                  {item.isCurrent && (
                    <div className="rounded-full bg-electric-orange/20 px-3 py-1 text-xs font-medium text-electric-orange">
                      {t('public.current')}
                    </div>
                  )}
                </div>
                
                <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                <p className="text-muted-foreground">{item.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
