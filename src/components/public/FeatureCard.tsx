import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
  iconClassName?: string;
}

export function FeatureCard({
  icon: Icon,
  title,
  description,
  className,
  iconClassName,
}: FeatureCardProps) {
  return (
    <div className={cn(
      "group relative overflow-hidden rounded-lg border border-border/50 bg-background p-6 transition-all hover:border-electric-orange/50 hover:shadow-md",
      className
    )}>
      <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted/50 group-hover:bg-electric-orange/10">
        <Icon className={cn("h-6 w-6 text-electric-orange", iconClassName)} />
      </div>
      
      <h3 className="mb-2 text-xl font-semibold">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
}
