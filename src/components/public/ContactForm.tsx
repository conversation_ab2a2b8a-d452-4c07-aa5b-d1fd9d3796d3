import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

export function ContactForm() {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.message) {
      toast.error(t('public.pleaseCompleteForm'));
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    try {
      // In a real implementation, this would be an API call to your backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(t('public.messageSent'));
      setFormData({
        name: '',
        email: '',
        phone: '',
        message: '',
      });
    } catch (error) {
      toast.error(t('public.messageError'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">{t('public.fullName')}</Label>
          <Input
            id="name"
            name="name"
            type="text"
            placeholder={t('public.enterName')}
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="email">{t('public.email')}</Label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder={t('public.enterEmail')}
            value={formData.email}
            onChange={handleChange}
            required
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="phone">{t('public.phone')}</Label>
        <Input
          id="phone"
          name="phone"
          type="tel"
          placeholder={t('public.enterPhone')}
          value={formData.phone}
          onChange={handleChange}
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="message">{t('public.message')}</Label>
        <Textarea
          id="message"
          name="message"
          placeholder={t('public.enterMessage')}
          value={formData.message}
          onChange={handleChange}
          rows={5}
          required
        />
      </div>
      
      <Button 
        type="submit" 
        className={cn(
          "w-full md:w-auto bg-electric-orange hover:bg-electric-orange/90 text-white",
          isSubmitting && "opacity-70 cursor-not-allowed"
        )}
        disabled={isSubmitting}
      >
        {isSubmitting ? t('public.sending') : t('public.sendMessage')}
      </Button>
    </form>
  );
}
