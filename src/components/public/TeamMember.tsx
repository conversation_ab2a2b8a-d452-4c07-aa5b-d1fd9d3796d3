import React from 'react';
import { cn } from '@/lib/utils';
import { Linkedin, Github } from 'lucide-react';

interface TeamMemberProps {
  name: string;
  role: string;
  bio: string;
  imageSrc?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  className?: string;
}

export function TeamMember({
  name,
  role,
  bio,
  imageSrc,
  linkedinUrl,
  githubUrl,
  className,
}: TeamMemberProps) {
  return (
    <div className={cn(
      "group relative overflow-hidden rounded-lg border border-border/50 bg-background p-6 transition-all hover:border-electric-orange/50 hover:shadow-md",
      className
    )}>
      <div className="flex flex-col sm:flex-row gap-6 items-start sm:items-center">
        {/* Profile Image */}
        <div className="relative w-24 h-24 rounded-full overflow-hidden border-2 border-muted bg-muted/50 flex-shrink-0">
          {imageSrc ? (
            <img 
              src={imageSrc} 
              alt={name} 
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-electric-orange/10">
              <span className="text-2xl font-bold text-electric-orange">
                {name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="flex-1">
          <h3 className="text-xl font-semibold">{name}</h3>
          <p className="text-electric-orange font-medium mb-2">{role}</p>
          <p className="text-muted-foreground mb-4">{bio}</p>
          
          {/* Social Links */}
          <div className="flex gap-3">
            {linkedinUrl && (
              <a 
                href={linkedinUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-electric-orange transition-colors"
              >
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </a>
            )}
            
            {githubUrl && (
              <a 
                href={githubUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-electric-orange transition-colors"
              >
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
