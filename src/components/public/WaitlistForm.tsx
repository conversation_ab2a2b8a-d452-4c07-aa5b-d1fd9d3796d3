import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

export function WaitlistForm() {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [userType, setUserType] = useState('supplier');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !name) {
      toast.error(t('public.pleaseCompleteForm'));
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    try {
      // In a real implementation, this would be an API call to your backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(t('public.waitlistSuccess'));
      setEmail('');
      setName('');
    } catch (error) {
      toast.error(t('public.waitlistError'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="w-full max-w-md mx-auto" id="waitlist">
      <div className="rounded-lg border border-border/50 bg-background p-6 shadow-sm">
        <h3 className="text-2xl font-bold mb-2 text-midnight-blue">
          {t('public.joinWaitlist')}
        </h3>
        <p className="text-muted-foreground mb-6">
          {t('public.waitlistDescription')}
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t('public.fullName')}</Label>
            <Input
              id="name"
              type="text"
              placeholder={t('public.enterName')}
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">{t('public.email')}</Label>
            <Input
              id="email"
              type="email"
              placeholder={t('public.enterEmail')}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label>{t('public.iAm')}</Label>
            <RadioGroup 
              value={userType} 
              onValueChange={setUserType}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="supplier" id="supplier" />
                <Label htmlFor="supplier" className="font-normal cursor-pointer">
                  {t('public.supplier')}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="merchant" id="merchant" />
                <Label htmlFor="merchant" className="font-normal cursor-pointer">
                  {t('public.merchant')}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="consumer" id="consumer" />
                <Label htmlFor="consumer" className="font-normal cursor-pointer">
                  {t('public.consumer')}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="other" id="other" />
                <Label htmlFor="other" className="font-normal cursor-pointer">
                  {t('public.other')}
                </Label>
              </div>
            </RadioGroup>
          </div>
          
          <Button 
            type="submit" 
            className={cn(
              "w-full bg-electric-orange hover:bg-electric-orange/90 text-white",
              isSubmitting && "opacity-70 cursor-not-allowed"
            )}
            disabled={isSubmitting}
          >
            {isSubmitting ? t('public.submitting') : t('public.joinWaitlist')}
          </Button>
        </form>
      </div>
    </div>
  );
}
