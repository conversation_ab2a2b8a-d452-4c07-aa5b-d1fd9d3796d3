
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { CategoryManagementModal } from './CategoryManagementModal';
import { Category } from '@/types';

interface CategorySelectorProps {
  categories: Category[];
  currentCategory: string;
  onAddCategory: (category: Omit<Category, 'id'>) => void;
  buttonId?: string;
}

export function CategorySelector({
  categories,
  currentCategory,
  onAddCategory,
  buttonId
}: CategorySelectorProps) {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleAddCategory = (category: Omit<Category, 'id'>) => {
    onAddCategory(category);
    setIsModalOpen(false);
  };

  return (
    <>
      <Button
        id={buttonId}
        variant="ghost"
        className="gap-2 hover:bg-muted/50"
        onClick={handleOpenModal}
        style={{ unicodeBidi: 'isolate' }}
      >
        <Plus className="h-4 w-4 flex-shrink-0" />
        <span style={{ unicodeBidi: 'isolate' }}>{t('categories.addCategory')}</span>
      </Button>
      
      <CategoryManagementModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleAddCategory}
      />
    </>
  );
}
