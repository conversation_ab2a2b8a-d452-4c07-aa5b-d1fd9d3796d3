/**
 * Auto Authentication Modal
 *
 * Automatically appears for unauthenticated users with smart timing
 * Provides seamless phone-only authentication experience
 *
 * PRODUCTION SECURITY: Only appears on consumer marketplace routes
 * Prevents modal from showing on admin/business/shipping dashboards
 */

import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useConsumerAuth } from './ConsumerAuthGuard';
import { EnhancedPhoneAuthModal } from './EnhancedPhoneAuthModal';
import { isConsumerMarketplaceRoute, debugRouteInfo } from '@/utils/routeUtils';

interface AutoAuthModalProps {
  /** Delay before showing modal (in milliseconds) */
  delay?: number;
  /** Show modal on specific user actions */
  triggers?: ('cart_add' | 'checkout' | 'profile_access' | 'idle_time')[];
  /** Minimum time user must be on site before showing modal */
  minTimeOnSite?: number;
  /** Don't show modal more than once per session */
  oncePerSession?: boolean;
}

export function AutoAuthModal({
  delay = 30000, // 30 seconds default
  triggers = ['cart_add', 'idle_time'],
  minTimeOnSite = 15000, // 15 seconds
  oncePerSession = true
}: AutoAuthModalProps) {
  const { isAuthenticated, isLoading } = useConsumerAuth();
  const location = useLocation();
  const [showModal, setShowModal] = useState(false);
  const [hasShownThisSession, setHasShownThisSession] = useState(false);
  const [timeOnSite, setTimeOnSite] = useState(0);
  const [lastActivity, setLastActivity] = useState(Date.now());

  // PRODUCTION SECURITY: Check if current route allows consumer modals
  const shouldShowOnCurrentRoute = isConsumerMarketplaceRoute(location.pathname);

  // Debug route info in development
  useEffect(() => {
    if (import.meta.env.DEV) {
      debugRouteInfo(location.pathname);
    }
  }, [location.pathname]);
  
  // Track time on site
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeOnSite(prev => prev + 1000);
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  // Track user activity
  useEffect(() => {
    const updateActivity = () => {
      setLastActivity(Date.now());
    };
    
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });
    
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, []);
  
  // Check for idle time trigger
  useEffect(() => {
    if (!triggers.includes('idle_time') || isAuthenticated || isLoading) return;
    
    const checkIdle = () => {
      const now = Date.now();
      const idleTime = now - lastActivity;
      const idleThreshold = 45000; // 45 seconds of inactivity
      
      if (
        idleTime >= idleThreshold &&
        timeOnSite >= minTimeOnSite &&
        !hasShownThisSession &&
        !showModal
      ) {
        setShowModal(true);
        if (oncePerSession) {
          setHasShownThisSession(true);
        }
      }
    };
    
    const timer = setInterval(checkIdle, 5000); // Check every 5 seconds
    return () => clearInterval(timer);
  }, [
    triggers, 
    isAuthenticated, 
    isLoading, 
    lastActivity, 
    timeOnSite, 
    minTimeOnSite, 
    hasShownThisSession, 
    showModal, 
    oncePerSession
  ]);
  
  // Auto-show modal after delay (ONLY on consumer routes)
  useEffect(() => {
    if (isAuthenticated || isLoading || hasShownThisSession || !shouldShowOnCurrentRoute) return;

    const timer = setTimeout(() => {
      if (timeOnSite >= minTimeOnSite && !showModal && shouldShowOnCurrentRoute) {
        console.log('🔐 AutoAuthModal: Showing modal on consumer route:', location.pathname);
        setShowModal(true);
        if (oncePerSession) {
          setHasShownThisSession(true);
        }
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [
    isAuthenticated,
    isLoading,
    delay,
    timeOnSite,
    minTimeOnSite,
    hasShownThisSession,
    showModal,
    oncePerSession,
    shouldShowOnCurrentRoute,
    location.pathname
  ]);
  
  // Listen for cart add events (ONLY on consumer routes)
  useEffect(() => {
    if (!triggers.includes('cart_add') || isAuthenticated || isLoading || !shouldShowOnCurrentRoute) return;

    const handleCartAdd = () => {
      if (timeOnSite >= minTimeOnSite && !hasShownThisSession && !showModal && shouldShowOnCurrentRoute) {
        console.log('🛒 AutoAuthModal: Showing modal on cart add event:', location.pathname);
        setShowModal(true);
        if (oncePerSession) {
          setHasShownThisSession(true);
        }
      }
    };

    // Listen for custom cart events
    window.addEventListener('cart:add', handleCartAdd);
    window.addEventListener('cart:update', handleCartAdd);

    return () => {
      window.removeEventListener('cart:add', handleCartAdd);
      window.removeEventListener('cart:update', handleCartAdd);
    };
  }, [
    triggers,
    isAuthenticated,
    isLoading,
    timeOnSite,
    minTimeOnSite,
    hasShownThisSession,
    showModal,
    oncePerSession,
    shouldShowOnCurrentRoute,
    location.pathname
  ]);
  
  // Listen for checkout attempts
  useEffect(() => {
    console.log('🎯 AutoAuthModal checkout listener setup:', {
      includesCheckout: triggers.includes('checkout'),
      isAuthenticated,
      isLoading,
      hasShownThisSession,
      showModal
    });

    if (!triggers.includes('checkout') || isAuthenticated || isLoading) {
      console.log('🚫 Checkout listener not active - conditions not met');
      return;
    }

    const handleCheckout = (event: Event) => {
      console.log('🎯 Checkout attempt event received:', event);
      console.log('🔍 Modal state check:', { hasShownThisSession, showModal });

      if (!hasShownThisSession && !showModal) {
        console.log('✅ Showing auth modal for checkout');
        setShowModal(true);
        if (oncePerSession) {
          setHasShownThisSession(true);
        }
      } else {
        console.log('❌ Modal not shown - already shown this session or currently showing');
      }
    };

    const handleDirectModalShow = (event: Event) => {
      console.log('🎯 Direct modal show event received:', event);
      if (!showModal) {
        console.log('✅ Showing auth modal directly');
        setShowModal(true);
      }
    };

    window.addEventListener('checkout:attempt', handleCheckout);
    window.addEventListener('auth:modal:show', handleDirectModalShow);

    return () => {
      window.removeEventListener('checkout:attempt', handleCheckout);
      window.removeEventListener('auth:modal:show', handleDirectModalShow);
    };
  }, [
    triggers,
    isAuthenticated,
    isLoading,
    hasShownThisSession,
    showModal,
    oncePerSession
  ]);
  
  // Listen for profile access attempts
  useEffect(() => {
    if (!triggers.includes('profile_access') || isAuthenticated || isLoading) return;
    
    const handleProfileAccess = () => {
      if (!hasShownThisSession && !showModal) {
        setShowModal(true);
        if (oncePerSession) {
          setHasShownThisSession(true);
        }
      }
    };
    
    window.addEventListener('profile:access', handleProfileAccess);
    
    return () => {
      window.removeEventListener('profile:access', handleProfileAccess);
    };
  }, [
    triggers, 
    isAuthenticated, 
    isLoading, 
    hasShownThisSession, 
    showModal, 
    oncePerSession
  ]);
  
  // Reset session state when authentication state changes
  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      // User logged out, reset session state
      console.log('🔄 User logged out, resetting AutoAuthModal session state');
      setHasShownThisSession(false);
    }
  }, [isAuthenticated, isLoading]);

  // Handle successful authentication
  const handleAuthSuccess = (user: any, action: 'login' | 'signup') => {
    console.log('✅ Auth success in AutoAuthModal:', { user, action });
    setShowModal(false);

    // Dispatch success event for other components to listen
    window.dispatchEvent(new CustomEvent('auth:success', {
      detail: { user, action }
    }));
  };
  
  // Handle modal close
  const handleClose = () => {
    setShowModal(false);
    
    // Mark as shown if user manually closes
    if (oncePerSession) {
      setHasShownThisSession(true);
    }
  };
  
  // Don't render if user is authenticated, loading, or not on consumer route
  if (isAuthenticated || isLoading || !shouldShowOnCurrentRoute) {
    if (import.meta.env.DEV && !shouldShowOnCurrentRoute) {
      console.log('🚫 AutoAuthModal: Not rendering on non-consumer route:', location.pathname);
    }
    return null;
  }
  
  return (
    <EnhancedPhoneAuthModal
      isOpen={showModal}
      onClose={handleClose}
      onSuccess={handleAuthSuccess}
    />
  );
}

// Helper function to trigger authentication modal manually
export function triggerAuthModal(trigger: 'cart_add' | 'checkout' | 'profile_access') {
  window.dispatchEvent(new CustomEvent(`${trigger.replace('_', ':')}`, {
    detail: { timestamp: Date.now() }
  }));
}

// Export for use in other components
export { triggerAuthModal as showAuthModal };
