import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useUser } from '@/contexts/UserContext';
import { UserRole } from '@/contexts/UserContext';
import { Loader2 } from 'lucide-react';
import { logAuditEvent } from '@/services/auditService';

interface SecureRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  allowedIPs?: string[];
}

/**
 * SecureRoute component
 * 
 * A secure route component that:
 * 1. Checks if the user is authenticated
 * 2. Verifies the user has the required role(s)
 * 3. Optionally checks IP address restrictions
 * 4. Redirects unauthenticated users to login with return URL
 * 5. Logs access attempts for audit purposes
 */
export function SecureRoute({
  children,
  requiredRoles = [],
  allowedIPs = []
}: SecureRouteProps) {
  const { isAuthenticated, isLoading, user, hasRole, updateLastActivity } = useAuth();
  const { userRole, isSupplier, isMerchant, isConsumer, isDistribution, isAdmin } = useUser();
  const location = useLocation();

  // Enhanced role checking that works in both development and production
  const hasRequiredRole = (role: UserRole): boolean => {
    // In development mode, fall back to UserContext if AuthContext doesn't have profile
    if (import.meta.env.DEV && (!user || !hasRole(role))) {
      console.log('DEV MODE: Falling back to UserContext for role check:', role);
      switch (role) {
        case 'supplier': return isSupplier();
        case 'merchant': return isMerchant();
        case 'consumer': return isConsumer();
        case 'distribution': return isDistribution();
        case 'admin': return isAdmin();
        default: return false;
      }
    }

    // Use AuthContext role checking (production mode or when profile is available)
    return hasRole(role);
  };

  // Update last activity timestamp when accessing protected routes
  useEffect(() => {
    if (isAuthenticated) {
      updateLastActivity();
    }
  }, [isAuthenticated, location.pathname, updateLastActivity]);

  // Log access attempt for audit purposes
  useEffect(() => {
    if (user) {
      logAuditEvent({
        userId: user.id,
        action: 'page_access',
        resource: location.pathname,
        details: {
          method: 'GET',
          timestamp: new Date().toISOString(),
        },
      });
    }
  }, [user, location.pathname]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Verifying authentication...</span>
      </div>
    );
  }

  // Debug authentication state
  console.log('🔍 [SECURE_ROUTE] Authentication check:', {
    path: location.pathname,
    isAuthenticated,
    isLoading,
    user: user ? { id: user.id, email: user.email } : null,
    userRole,
    requiredRoles
  });

  // Always redirect unauthenticated users to partners page, regardless of environment
  // This ensures proper authentication flow in both development and production
  if (!isAuthenticated) {
    // Log unauthorized access attempt
    console.warn(`🚫 [SECURE_ROUTE] Unauthorized access attempt to ${location.pathname} - redirecting to partners page`);

    return (
      <Navigate
        to={`/partners?returnUrl=${encodeURIComponent(location.pathname)}`}
        replace
      />
    );
  }

  // Check role-based access if required roles are specified
  if (requiredRoles.length > 0) {
    const hasAnyRequiredRole = requiredRoles.some(role => hasRequiredRole(role));

    console.log('🔍 [SECURE_ROUTE] Role check:', {
      requiredRoles,
      currentRole: userRole,
      hasAnyRequiredRole,
      roleChecks: requiredRoles.map(role => ({
        role,
        hasRole: hasRequiredRole(role)
      }))
    });

    if (!hasAnyRequiredRole) {
      // Log unauthorized role access
      console.warn(`🚫 [SECURE_ROUTE] User lacks required role(s) for ${location.pathname}. Required: ${requiredRoles.join(', ')}, Current: ${userRole}`);

      // Only log audit event if we have a user (in production mode)
      if (user) {
        logAuditEvent({
          userId: user.id,
          action: 'access_denied',
          resource: location.pathname,
          details: {
            reason: 'insufficient_permissions',
            requiredRoles,
            currentRole: userRole,
            timestamp: new Date().toISOString(),
          },
        });
      }

      return (
        <Navigate
          to="/app/access-denied"
          replace
          state={{
            from: location.pathname,
            reason: 'insufficient_permissions',
            requiredRoles,
            currentRole: userRole
          }}
        />
      );
    }
  }

  // Check IP restrictions if enabled
  if (allowedIPs.length > 0) {
    // In a real implementation, you would get the client IP from the request
    // For this example, we'll use a placeholder
    const clientIP = '127.0.0.1'; // This would be replaced with actual IP detection
    
    if (!allowedIPs.includes(clientIP)) {
      // Log IP restriction violation
      console.warn(`IP restriction violation for ${location.pathname} from ${clientIP}`);
      logAuditEvent({
        userId: user.id,
        action: 'access_denied',
        resource: location.pathname,
        details: {
          reason: 'ip_restriction',
          clientIP,
          timestamp: new Date().toISOString(),
        },
      });
      
      return (
        <Navigate 
          to="/app/access-denied" 
          replace 
          state={{ 
            from: location.pathname,
            reason: 'ip_restriction' 
          }} 
        />
      );
    }
  }

  // All checks passed, render the protected content
  return <>{children}</>;
}
