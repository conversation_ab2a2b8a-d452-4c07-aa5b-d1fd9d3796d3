/**
 * Enhanced Phone Authentication Modal with Consumer Name Collection
 * 
 * CRITICAL: This modal collects consumer full name during authentication
 * to ensure proper order confirmation display and database storage
 */

import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Loader2,
  Shield,
  AlertCircle,
  User,
  Lock,
  Eye,
  EyeOff
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import {
  authenticateConsumerSimplified,
  validateAlgerianPhone
} from '@/services/simplifiedConsumerAuth';

interface EnhancedPhoneAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (user: any, action: 'login' | 'signup') => void;
}

type AuthStep = 'simplified_auth';

export function EnhancedPhoneAuthModal({ isOpen, onClose, onSuccess }: EnhancedPhoneAuthModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // State
  const [step, setStep] = useState<AuthStep>('simplified_auth');
  const [phone, setPhone] = useState('');
  const [fullName, setFullName] = useState(''); // CRITICAL: Consumer name collection
  const [passcode, setPasscode] = useState(''); // Optional 4-6 digit passcode
  const [showPasscode, setShowPasscode] = useState(false); // Toggle passcode visibility
  const [isLoading, setIsLoading] = useState(false);
  const [requiresPasscode, setRequiresPasscode] = useState(false); // For existing users with passcode

  // Refs
  const phoneInputRef = useRef<HTMLInputElement>(null);
  const nameInputRef = useRef<HTMLInputElement>(null);
  const passcodeInputRef = useRef<HTMLInputElement>(null);
  
  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('simplified_auth');
      setPhone('');
      setFullName('');
      setPasscode('');
      setShowPasscode(false);
      setRequiresPasscode(false);
      setIsLoading(false);
    }
  }, [isOpen]);
  
  // Focus management - Focus on name field first for CONSUMER
  useEffect(() => {
    if (isOpen && step === 'simplified_auth' && nameInputRef.current) {
      setTimeout(() => nameInputRef.current?.focus(), 100);
    }
  }, [isOpen, step]);
  
  // Removed countdown timers - not needed for simplified authentication
  
  // Format phone number input
  const formatPhoneInput = (value: string) => {
    const digits = value.replace(/\D/g, '');
    const limited = digits.slice(0, 9);
    
    if (limited.length <= 3) return limited;
    if (limited.length <= 6) return `${limited.slice(0, 3)} ${limited.slice(3)}`;
    return `${limited.slice(0, 3)} ${limited.slice(3, 6)} ${limited.slice(6)}`;
  };
  
  // Handle phone input change
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneInput(e.target.value);
    setPhone(formatted);
  };
  
  // Handle name input change
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFullName(e.target.value);
  };

  // Format passcode input (4-6 digits only)
  const formatPasscodeInput = (value: string) => {
    const digits = value.replace(/\D/g, '');
    return digits.slice(0, 6); // Max 6 digits
  };

  // Handle passcode input change
  const handlePasscodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPasscodeInput(e.target.value);
    setPasscode(formatted);
  };

  // Removed OTP input handler - not needed for simplified authentication
  
  // Validate phone number
  const isPhoneValid = () => {
    const digits = phone.replace(/\D/g, '');
    return digits.length === 9;
  };
  
  // Validate full name - REQUIRED: First and Last name
  const isNameValid = () => {
    const trimmedName = fullName.trim();
    const nameParts = trimmedName.split(/\s+/);
    return trimmedName.length >= 3 && nameParts.length >= 2 && nameParts[0].length >= 1 && nameParts[1].length >= 1;
  };

  // Validate passcode format (4-6 digits)
  const isPasscodeValid = () => {
    if (!passcode) return true; // Passcode is optional
    return passcode.length >= 4 && passcode.length <= 6 && /^\d+$/.test(passcode);
  };

  // Check if form is valid for submission
  const isFormValid = () => {
    const basicValid = isPhoneValid() && isNameValid();

    // If user requires passcode (existing user with passcode), passcode is mandatory
    if (requiresPasscode) {
      return basicValid && passcode.length >= 4 && passcode.length <= 6;
    }

    // For new users or existing users without passcode, passcode is optional but must be valid if provided
    return basicValid && isPasscodeValid();
  };

  // Get validation error message for name
  const getNameValidationError = () => {
    const trimmedName = fullName.trim();
    const nameParts = trimmedName.split(/\s+/);

    if (trimmedName.length === 0) {
      return "Full name is required";
    }
    if (trimmedName.length < 3) {
      return "Name must be at least 3 characters";
    }
    if (nameParts.length < 2) {
      return "Please enter both first and last name";
    }
    if (nameParts[0].length < 1 || nameParts[1].length < 1) {
      return "Both first and last name must be at least 1 character";
    }
    return null;
  };

  // Convert full name to privacy-protected format (First Name + Last Initial)
  const convertToPrivacyProtectedName = (fullName: string): { displayName: string; fullName: string } => {
    const trimmedName = fullName.trim();
    const nameParts = trimmedName.split(/\s+/);

    if (nameParts.length >= 2) {
      const firstName = nameParts[0];
      const lastNameInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();
      const displayName = `${firstName} ${lastNameInitial}`;

      return {
        displayName, // Privacy-protected version for suppliers/merchants/shipping
        fullName: trimmedName // Original full name for internal use
      };
    }

    return {
      displayName: trimmedName,
      fullName: trimmedName
    };
  };
  
  // Removed time formatting - not needed for simplified authentication
  
  // Use the service function for checking phone existence
  
  // SIMPLIFIED AUTHENTICATION: No SMS verification required, with optional passcode
  const handleSimplifiedAuth = async () => {
    if (!isFormValid() || isLoading) return;

    setIsLoading(true);

    try {
      const fullPhone = `+213${phone.replace(/\D/g, '')}`;

      console.log('🚀 Starting simplified consumer authentication with passcode support');

      const authRequest: any = {
        fullName: fullName.trim(),
        phone: fullPhone
      };

      // Include passcode if provided
      if (passcode) {
        authRequest.passcode = passcode;
      }

      const result = await authenticateConsumerSimplified(authRequest);

      if (result.success) {
        console.log('✅ Simplified authentication successful:', result.action);

        toast({
          title: result.action === 'signup' ? "Account Created!" : "Welcome Back!",
          description: result.action === 'signup'
            ? "Your account has been created successfully!"
            : "You have been logged in successfully!",
        });

        // Trigger auth success event
        window.dispatchEvent(new CustomEvent('auth:success', {
          detail: { user: result.user, action: result.action }
        }));

        // Call success callback
        onSuccess?.(result.user!, result.action!);

        // Close modal
        onClose();
      } else if (result.requiresPasscode) {
        // User has a passcode set and needs to enter it
        console.log('🔐 User requires passcode for authentication');
        setRequiresPasscode(true);

        // Focus on passcode input
        setTimeout(() => passcodeInputRef.current?.focus(), 100);

        toast({
          title: "Passcode Required",
          description: "Please enter your 4-6 digit passcode to continue.",
        });
      } else {
        console.error('❌ Simplified authentication failed:', result.error);

        toast({
          title: "Authentication Failed",
          description: result.error || "Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('❌ Error in simplified authentication:', error);
      toast({
        title: "Error",
        description: "Failed to process authentication. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Removed OTP verification - using simplified authentication instead
  
  // Removed unused functions - using simplified authentication service instead
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 'simplified_auth') {
      handleSimplifiedAuth();
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-[#fa7b00]" />
            Consumer Registration
          </DialogTitle>
          <DialogDescription>
            Enter your phone number to login, or add your name if you're new
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <AnimatePresence mode="wait">
            {step === 'simplified_auth' ? (
              <motion.div
                key="simplified-auth-step"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.15 }}
                className="space-y-4"
              >
                {/* CONSUMER FULL NAME FIELD - AT THE TOP */}
                <div className="space-y-2">
                  <Label htmlFor="fullName" className="text-sm font-medium">
                    Full Name <span className="text-gray-400">(Required for new accounts)</span>
                  </Label>
                  <Input
                    id="fullName"
                    ref={nameInputRef}
                    type="text"
                    value={fullName}
                    onChange={handleNameChange}
                    placeholder="Enter first and last name (e.g., Ahmed Benali)"
                    className={cn(
                      "text-lg focus:ring-[#fa7b00] focus:border-[#fa7b00]",
                      !isNameValid() && fullName.length > 0 && "border-red-500 focus:border-red-500 focus:ring-red-500"
                    )}
                    autoComplete="name"
                    maxLength={50}
                  />
                  {!isNameValid() && fullName.length > 0 && (
                    <p className="text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {getNameValidationError()}
                    </p>
                  )}
                  <p className="text-xs text-green-600">
                    ✅ <strong>Instant Registration:</strong> No verification codes required
                  </p>
                  <p className="text-xs text-blue-600">
                    <Shield className="h-3 w-3 inline mr-1" />
                    Privacy: Only first name + last initial will be visible to suppliers
                  </p>
                </div>

                {/* PHONE NUMBER FIELD - BELOW NAME */}
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                      +213
                    </div>
                    <Input
                      id="phone"
                      ref={phoneInputRef}
                      type="tel"
                      value={phone}
                      onChange={handlePhoneChange}
                      placeholder="XXX XXX XXX"
                      className="pl-12 text-lg tracking-wider focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                      inputMode="numeric"
                      autoComplete="tel"
                      maxLength={11}
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Enter your 9-digit Algerian mobile number
                  </p>
                </div>

                {/* PASSCODE FIELD - Optional for new users, required for existing users with passcode */}
                <div className="space-y-2">
                  <Label htmlFor="passcode" className="text-sm font-medium flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Passcode
                    {requiresPasscode ? (
                      <span className="text-red-500">(Required)</span>
                    ) : (
                      <span className="text-gray-400">(Optional - 4-6 digits for enhanced security)</span>
                    )}
                  </Label>
                  <div className="relative">
                    <Input
                      id="passcode"
                      ref={passcodeInputRef}
                      type={showPasscode ? "text" : "password"}
                      value={passcode}
                      onChange={handlePasscodeChange}
                      placeholder={requiresPasscode ? "Enter your passcode" : "Set a 4-6 digit passcode (optional)"}
                      className={cn(
                        "text-lg tracking-wider focus:ring-[#fa7b00] focus:border-[#fa7b00] pr-10",
                        !isPasscodeValid() && passcode.length > 0 && "border-red-500 focus:border-red-500 focus:ring-red-500",
                        requiresPasscode && "border-orange-300 bg-orange-50"
                      )}
                      inputMode="numeric"
                      autoComplete="off"
                      maxLength={6}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPasscode(!showPasscode)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPasscode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  {!isPasscodeValid() && passcode.length > 0 && (
                    <p className="text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      Passcode must be 4-6 digits
                    </p>
                  )}
                  {requiresPasscode ? (
                    <p className="text-xs text-orange-600">
                      <Shield className="h-3 w-3 inline mr-1" />
                      This account has a passcode set. Please enter it to continue.
                    </p>
                  ) : (
                    <p className="text-xs text-blue-600">
                      <Shield className="h-3 w-3 inline mr-1" />
                      Optional: Set a passcode for enhanced account security
                    </p>
                  )}
                </div>

                <Button
                  type="submit"
                  disabled={!isFormValid() || isLoading}
                  className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <User className="mr-2 h-4 w-4" />
                      Continue
                    </>
                  )}
                </Button>
              </motion.div>
            ) : null}
          </AnimatePresence>
        </form>
        
        <div className="text-center text-xs text-gray-500">
          <p>By continuing, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
