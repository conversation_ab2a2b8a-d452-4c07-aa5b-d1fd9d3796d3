/**
 * Enhanced Phone Authentication Modal with Consumer Name Collection
 * 
 * CRITICAL: This modal collects consumer full name during authentication
 * to ensure proper order confirmation display and database storage
 */

import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Loader2, 
  Phone, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  ArrowLeft,
  Clock,
  User
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { sendPhoneOTP, verifyPhoneOTP, validateAlgerianPhone, loginExistingConsumer, checkPhoneExists } from '@/services/phoneAuthService';

interface EnhancedPhoneAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (user: any, action: 'login' | 'signup') => void;
}

type AuthStep = 'phone' | 'otp' | 'existing_login';

export function EnhancedPhoneAuthModal({ isOpen, onClose, onSuccess }: EnhancedPhoneAuthModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // State
  const [step, setStep] = useState<AuthStep>('phone');
  const [phone, setPhone] = useState('');
  const [fullName, setFullName] = useState(''); // CRITICAL: Consumer name collection
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(60);
  const [rateLimitInfo, setRateLimitInfo] = useState<any>(null);
  const [isNewUser, setIsNewUser] = useState(false); // Track if user is new
  const [existingUserName, setExistingUserName] = useState(''); // Store existing user's name
  
  // Refs
  const phoneInputRef = useRef<HTMLInputElement>(null);
  const nameInputRef = useRef<HTMLInputElement>(null);
  const otpInputRef = useRef<HTMLInputElement>(null);
  
  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('phone');
      setPhone('');
      setFullName('');
      setOtp('');
      setIsLoading(false);
      setSessionId('');
      setTimeLeft(300);
      setCanResend(false);
      setResendCountdown(60);
      setRateLimitInfo(null);
      setIsNewUser(false);
      setExistingUserName('');
    }
  }, [isOpen]);
  
  // Focus management - Focus on name field first for CONSUMER
  useEffect(() => {
    if (isOpen && step === 'phone' && nameInputRef.current) {
      setTimeout(() => nameInputRef.current?.focus(), 100);
    } else if (isOpen && step === 'otp' && otpInputRef.current) {
      setTimeout(() => otpInputRef.current?.focus(), 100);
    }
  }, [isOpen, step]);
  
  // Countdown timers
  useEffect(() => {
    if (step === 'otp' && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      setStep('phone');
      toast({
        title: "Code Expired",
        description: "Your verification code has expired. Please request a new one.",
        variant: "destructive"
      });
    }
  }, [step, timeLeft, toast]);
  
  // Resend countdown
  useEffect(() => {
    if (step === 'otp' && !canResend && resendCountdown > 0) {
      const timer = setTimeout(() => setResendCountdown(resendCountdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (resendCountdown === 0) {
      setCanResend(true);
    }
  }, [step, canResend, resendCountdown]);
  
  // Format phone number input
  const formatPhoneInput = (value: string) => {
    const digits = value.replace(/\D/g, '');
    const limited = digits.slice(0, 9);
    
    if (limited.length <= 3) return limited;
    if (limited.length <= 6) return `${limited.slice(0, 3)} ${limited.slice(3)}`;
    return `${limited.slice(0, 3)} ${limited.slice(3, 6)} ${limited.slice(6)}`;
  };
  
  // Handle phone input change
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneInput(e.target.value);
    setPhone(formatted);
  };
  
  // Handle name input change
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFullName(e.target.value);
  };
  
  // Handle OTP input change
  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };
  
  // Validate phone number
  const isPhoneValid = () => {
    const digits = phone.replace(/\D/g, '');
    return digits.length === 9;
  };
  
  // Validate full name - REQUIRED: First and Last name
  const isNameValid = () => {
    const trimmedName = fullName.trim();
    const nameParts = trimmedName.split(/\s+/);
    return trimmedName.length >= 3 && nameParts.length >= 2 && nameParts[0].length >= 1 && nameParts[1].length >= 1;
  };

  // Get validation error message for name
  const getNameValidationError = () => {
    const trimmedName = fullName.trim();
    const nameParts = trimmedName.split(/\s+/);

    if (trimmedName.length === 0) {
      return "Full name is required";
    }
    if (trimmedName.length < 3) {
      return "Name must be at least 3 characters";
    }
    if (nameParts.length < 2) {
      return "Please enter both first and last name";
    }
    if (nameParts[0].length < 1 || nameParts[1].length < 1) {
      return "Both first and last name must be at least 1 character";
    }
    return null;
  };

  // Convert full name to privacy-protected format (First Name + Last Initial)
  const convertToPrivacyProtectedName = (fullName: string): { displayName: string; fullName: string } => {
    const trimmedName = fullName.trim();
    const nameParts = trimmedName.split(/\s+/);

    if (nameParts.length >= 2) {
      const firstName = nameParts[0];
      const lastNameInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();
      const displayName = `${firstName} ${lastNameInitial}`;

      return {
        displayName, // Privacy-protected version for suppliers/merchants/shipping
        fullName: trimmedName // Original full name for internal use
      };
    }

    return {
      displayName: trimmedName,
      fullName: trimmedName
    };
  };
  
  // Format time display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Use the service function for checking phone existence
  
  // PRODUCTION FEATURE: Smart authentication - OTP only for new users
  const handleSendOTP = async () => {
    if (!isPhoneValid() || isLoading) return;

    setIsLoading(true);

    try {
      const fullPhone = `+213${phone.replace(/\D/g, '')}`;

      // STEP 1: Check if consumer already exists
      console.log('🔍 Checking if consumer exists for phone:', fullPhone);
      const phoneExistsResult = await checkPhoneExists(fullPhone);

      if (phoneExistsResult.exists) {
        // EXISTING USER: Login without OTP (saves SMS costs!)
        console.log('✅ Existing consumer found - attempting phone-only login');

        const loginResult = await loginExistingConsumer(fullPhone, fullName.trim() || undefined);

        if (loginResult.success) {
          console.log('🎉 Phone-only login successful!');

          toast({
            title: "Welcome Back!",
            description: "You have been logged in successfully.",
          });

          // Trigger auth success event
          window.dispatchEvent(new CustomEvent('auth:success', {
            detail: { user: loginResult.user, action: loginResult.action }
          }));

          // Call success callback
          onSuccess?.(loginResult.user!, loginResult.action!);

          // Close modal
          onClose();
          return;
        } else if (loginResult.error === 'CONSUMER_NOT_FOUND') {
          // Phone exists but not as consumer - treat as new user
          console.log('📱 Phone exists but not as consumer - treating as new user');
          setIsNewUser(true);
        } else {
          // Other error - show message and continue with OTP flow
          console.error('❌ Phone-only login failed:', loginResult.error);
          toast({
            title: "Login Issue",
            description: "We'll send you a verification code instead.",
            variant: "default"
          });
          setIsNewUser(false);
        }
      } else {
        // NEW USER: Require OTP verification
        console.log('🆕 New consumer - OTP verification required');
        setIsNewUser(true);
      }

      // Validate name for new users or if phone-only login failed
      if (!isNameValid()) {
        toast({
          title: "Name Required",
          description: getNameValidationError(),
          variant: "destructive"
        });
        setIsLoading(false);
        return;
      }

      // STEP 2: Send OTP for new users or failed phone-only login
      console.log('📱 Sending OTP verification code');
      const result = await sendPhoneOTP(fullPhone);

      if (result.success) {
        setSessionId(result.verificationId!);
        setTimeLeft(300);
        setCanResend(false);
        setResendCountdown(60);
        setRateLimitInfo(result.rateLimitInfo);

        // Go to OTP step
        setStep('otp');

        toast({
          title: "Code Sent",
          description: `Verification code sent to ${fullPhone}`,
        });
      } else {
        setRateLimitInfo(result.rateLimitInfo);
        toast({
          title: "Failed to Send Code",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('❌ Error in handleSendOTP:', error);
      toast({
        title: "Error",
        description: "Failed to process authentication. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Step 2: Verify OTP with name storage
  const handleVerifyOTP = async () => {
    if (otp.length !== 6 || isLoading) return;
    
    setIsLoading(true);
    
    try {
      const result = await verifyPhoneOTP(sessionId, otp);
      
      if (result.success) {
        // Update profile with privacy-protected name for ALL users (new and existing)
        if (fullName.trim()) {
          const { displayName, fullName: originalFullName } = convertToPrivacyProtectedName(fullName.trim());
          console.log('🔄 Updating consumer profile with privacy-protected name:');
          console.log('   Original Name:', originalFullName);
          console.log('   Privacy-Protected Name:', displayName);

          await updateConsumerProfile(result.user.id, displayName, originalFullName);

          // CRITICAL: Update localStorage session with latest profile data
          await updateLocalStorageSession(result.user.id, displayName, originalFullName);
        }

        toast({
          title: result.action === 'signup' ? "Account Created" : "Welcome Back",
          description: result.action === 'signup'
            ? `Welcome ${fullName || 'to AROUZ MARKET'}! Your account has been created.`
            : "You have been logged in successfully!",
        });

        // Trigger auth success event for consumer auth state update
        window.dispatchEvent(new CustomEvent('auth:success', {
          detail: { user: result.user, action: result.action }
        }));

        // Call success callback
        onSuccess?.(result.user, result.action!);

        // Close modal
        onClose();
      } else {
        toast({
          title: "Verification Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify code. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Update consumer profile with privacy-protected name
  const updateConsumerProfile = async (userId: string, displayName: string, originalFullName: string) => {
    try {
      const { supabase } = await import('@/services/authService');
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: displayName, // Privacy-protected name (e.g., "Yousef P")
          original_full_name: originalFullName, // Store original for internal use
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('❌ Error updating consumer profile:', error);
        throw error;
      } else {
        console.log('✅ Consumer profile updated in database:');
        console.log('   Display Name (Privacy-Protected):', displayName);
        console.log('   Original Full Name (Internal):', originalFullName);
      }
    } catch (error) {
      console.error('❌ Error updating consumer profile:', error);
      throw error;
    }
  };

  // Update localStorage session with latest profile data
  const updateLocalStorageSession = async (userId: string, displayName: string, originalFullName: string) => {
    try {
      console.log('🔄 Updating localStorage session with latest profile data...');

      // Get current session
      const phoneSession = localStorage.getItem('phone_auth_session');
      if (!phoneSession) {
        console.log('❌ No phone session found in localStorage');
        return;
      }

      const session = JSON.parse(phoneSession);

      // Update the profile data in the session
      const updatedSession = {
        ...session,
        profile: {
          ...session.profile,
          full_name: displayName, // Privacy-protected name
          original_full_name: originalFullName, // Original name for internal use
          updated_at: new Date().toISOString()
        }
      };

      // Save updated session back to localStorage
      localStorage.setItem('phone_auth_session', JSON.stringify(updatedSession));

      console.log('✅ localStorage session updated with:');
      console.log('   full_name (Privacy-Protected):', displayName);
      console.log('   original_full_name (Internal):', originalFullName);

    } catch (error) {
      console.error('❌ Error updating localStorage session:', error);
    }
  };
  
  // Handle resend OTP
  const handleResendOTP = async () => {
    if (!canResend || isLoading) return;
    
    setCanResend(false);
    setResendCountdown(60);
    
    const fullPhone = `+213${phone.replace(/\D/g, '')}`;
    const result = await sendPhoneOTP(fullPhone);
    
    if (result.success) {
      setSessionId(result.verificationId!);
      setTimeLeft(300);
      setRateLimitInfo(result.rateLimitInfo);
      
      toast({
        title: "Code Resent",
        description: `New verification code sent to ${fullPhone}`,
      });
    } else {
      setCanResend(true);
      setResendCountdown(0);
      setRateLimitInfo(result.rateLimitInfo);
      
      toast({
        title: "Failed to Resend",
        description: result.error,
        variant: "destructive"
      });
    }
  };
  
  // Handle back navigation
  const handleBack = () => {
    if (step === 'otp') {
      setStep('phone');
      setOtp('');
    }
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 'phone') {
      handleSendOTP();
    } else if (step === 'otp') {
      handleVerifyOTP();
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {step === 'phone' ? (
              <>
                <User className="h-5 w-5 text-[#fa7b00]" />
                Consumer Registration
              </>
            ) : (
              <>
                <Shield className="h-5 w-5 text-[#fa7b00]" />
                Enter Verification Code
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {step === 'phone'
              ? "Enter your phone number to login, or add your name if you're new"
              : `Enter the 6-digit code sent to +213${phone.replace(/\D/g, '')}`
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <AnimatePresence mode="wait">
            {step === 'phone' ? (
              <motion.div
                key="phone-step"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.15 }}
                className="space-y-4"
              >
                {/* CONSUMER FULL NAME FIELD - AT THE TOP */}
                <div className="space-y-2">
                  <Label htmlFor="fullName" className="text-sm font-medium">
                    Full Name <span className="text-gray-400">(Required for new accounts)</span>
                  </Label>
                  <Input
                    id="fullName"
                    ref={nameInputRef}
                    type="text"
                    value={fullName}
                    onChange={handleNameChange}
                    placeholder="Enter first and last name (e.g., Ahmed Benali)"
                    className={cn(
                      "text-lg focus:ring-[#fa7b00] focus:border-[#fa7b00]",
                      !isNameValid() && fullName.length > 0 && "border-red-500 focus:border-red-500 focus:ring-red-500"
                    )}
                    autoComplete="name"
                    maxLength={50}
                  />
                  {!isNameValid() && fullName.length > 0 && (
                    <p className="text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {getNameValidationError()}
                    </p>
                  )}
                  <p className="text-xs text-green-600">
                    💡 <strong>Existing users:</strong> Just enter your phone number to login instantly
                  </p>
                  <p className="text-xs text-blue-600">
                    <Shield className="h-3 w-3 inline mr-1" />
                    Privacy: Only first name + last initial will be visible to suppliers
                  </p>
                </div>

                {/* PHONE NUMBER FIELD - BELOW NAME */}
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                      +213
                    </div>
                    <Input
                      id="phone"
                      ref={phoneInputRef}
                      type="tel"
                      value={phone}
                      onChange={handlePhoneChange}
                      placeholder="XXX XXX XXX"
                      className="pl-12 text-lg tracking-wider focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                      inputMode="numeric"
                      autoComplete="tel"
                      maxLength={11}
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Enter your 9-digit Algerian mobile number
                  </p>
                </div>
                
                {rateLimitInfo && rateLimitInfo.remainingAttempts < 3 && (
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <p className="text-sm text-yellow-800">
                      {rateLimitInfo.remainingAttempts} attempts remaining
                    </p>
                  </div>
                )}
                
                <Button
                  type="submit"
                  disabled={!isPhoneValid() || isLoading}
                  className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Continue'
                  )}
                </Button>
              </motion.div>
            ) : (
              <motion.div
                key="otp-step"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.15 }}
                className="space-y-4"
              >
                <div className="space-y-2">
                  <Label htmlFor="otp">Verification Code</Label>
                  <Input
                    id="otp"
                    ref={otpInputRef}
                    type="text"
                    value={otp}
                    onChange={handleOtpChange}
                    placeholder="123456"
                    className="text-center text-xl tracking-widest focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                    inputMode="numeric"
                    maxLength={6}
                    autoComplete="one-time-code"
                  />
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1 text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>Expires in {formatTime(timeLeft)}</span>
                  </div>
                  
                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    onClick={handleResendOTP}
                    disabled={!canResend || isLoading}
                    className="text-[#fa7b00] hover:text-[#fa7b00]/80 p-0 h-auto"
                  >
                    {canResend ? 'Resend Code' : `Resend in ${resendCountdown}s`}
                  </Button>
                </div>
                
                <div className="space-y-3">
                  <Button
                    type="submit"
                    disabled={otp.length !== 6 || isLoading}
                    className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      'Verify Code'
                    )}
                  </Button>
                  
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleBack}
                    className="w-full"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Change Phone Number
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </form>
        
        <div className="text-center text-xs text-gray-500">
          <p>By continuing, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
