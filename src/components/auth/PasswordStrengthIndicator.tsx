import React from 'react';
import { cn } from '@/lib/utils';
import { checkPasswordStrength } from '@/services/authService';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

export function PasswordStrengthIndicator({ password, className }: PasswordStrengthIndicatorProps) {
  const { score, errors, isValid } = checkPasswordStrength(password);
  
  // Don't show anything if password is empty
  if (!password) {
    return null;
  }
  
  const getStrengthLabel = () => {
    if (score === 0) return 'Very Weak';
    if (score === 1) return 'Weak';
    if (score === 2) return 'Fair';
    if (score === 3) return 'Good';
    return 'Strong';
  };
  
  const getStrengthColor = () => {
    if (score === 0) return 'bg-red-500';
    if (score === 1) return 'bg-orange-500';
    if (score === 2) return 'bg-yellow-500';
    if (score === 3) return 'bg-green-400';
    return 'bg-green-600';
  };
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex gap-1 h-1.5">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "h-full flex-1 rounded-full transition-colors",
              i < score ? getStrengthColor() : "bg-gray-200"
            )}
          />
        ))}
      </div>
      
      <div className="flex justify-between items-center text-xs">
        <span className={cn(
          "font-medium",
          score === 0 ? "text-red-500" : 
          score === 1 ? "text-orange-500" : 
          score === 2 ? "text-yellow-500" : 
          score === 3 ? "text-green-500" : 
          "text-green-600"
        )}>
          {getStrengthLabel()}
        </span>
        
        {errors.length > 0 && (
          <span className="text-muted-foreground">
            {errors.length} {errors.length === 1 ? 'issue' : 'issues'}
          </span>
        )}
      </div>
      
      {errors.length > 0 && (
        <ul className="text-xs text-muted-foreground space-y-1 list-disc pl-4">
          {errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      )}
    </div>
  );
}
