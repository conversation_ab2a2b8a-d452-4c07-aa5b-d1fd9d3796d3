/**
 * Phone Authentication Modal
 * 
 * Production-ready phone-only authentication modal for Algerian consumers
 * Features exceptional UI/UX with ≤75ms transitions and comprehensive error handling
 */

import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Loader2, 
  Phone, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  ArrowLeft,
  Clock
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { sendPhoneOTP, verifyPhoneOTP, validateAlgerianPhone } from '@/services/phoneAuthService';

interface PhoneAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (user: any, action: 'login' | 'signup') => void;
}

type AuthStep = 'phone' | 'otp';

export function PhoneAuthModal({ isOpen, onClose, onSuccess }: PhoneAuthModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // State
  const [step, setStep] = useState<AuthStep>('phone');
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(60);
  const [rateLimitInfo, setRateLimitInfo] = useState<any>(null);
  
  // Refs
  const phoneInputRef = useRef<HTMLInputElement>(null);
  const otpInputRef = useRef<HTMLInputElement>(null);
  
  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('phone');
      setPhone('');
      setOtp('');
      setIsLoading(false);
      setSessionId('');
      setTimeLeft(300);
      setCanResend(false);
      setResendCountdown(60);
      setRateLimitInfo(null);
    }
  }, [isOpen]);
  
  // Focus management
  useEffect(() => {
    if (isOpen && step === 'phone' && phoneInputRef.current) {
      setTimeout(() => phoneInputRef.current?.focus(), 100);
    } else if (isOpen && step === 'otp' && otpInputRef.current) {
      setTimeout(() => otpInputRef.current?.focus(), 100);
    }
  }, [isOpen, step]);
  
  // Countdown timers
  useEffect(() => {
    if (step === 'otp' && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      setStep('phone');
      toast({
        title: "Code Expired",
        description: "Your verification code has expired. Please request a new one.",
        variant: "destructive"
      });
    }
  }, [step, timeLeft, toast]);
  
  // Resend countdown
  useEffect(() => {
    if (step === 'otp' && !canResend && resendCountdown > 0) {
      const timer = setTimeout(() => setResendCountdown(resendCountdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (resendCountdown === 0) {
      setCanResend(true);
    }
  }, [step, canResend, resendCountdown]);
  
  // Format phone number input
  const formatPhoneInput = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Limit to 9 digits (Algerian mobile format)
    const limited = digits.slice(0, 9);
    
    // Format as XXX XXX XXX
    if (limited.length <= 3) return limited;
    if (limited.length <= 6) return `${limited.slice(0, 3)} ${limited.slice(3)}`;
    return `${limited.slice(0, 3)} ${limited.slice(3, 6)} ${limited.slice(6)}`;
  };
  
  // Handle phone input change
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneInput(e.target.value);
    setPhone(formatted);
  };
  
  // Handle OTP input change
  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };
  
  // Validate phone number
  const isPhoneValid = () => {
    const digits = phone.replace(/\D/g, '');
    return digits.length === 9;
  };
  
  // Format time display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Step 1: Send OTP
  const handleSendOTP = async () => {
    if (!isPhoneValid() || isLoading) return;
    
    setIsLoading(true);
    
    try {
      const fullPhone = `+213${phone.replace(/\D/g, '')}`;
      const result = await sendPhoneOTP(fullPhone);
      
      if (result.success) {
        setSessionId(result.verificationId!);
        setStep('otp');
        setTimeLeft(300);
        setCanResend(false);
        setResendCountdown(60);
        setRateLimitInfo(result.rateLimitInfo);
        
        toast({
          title: "Code Sent",
          description: `Verification code sent to ${fullPhone}`,
        });
      } else {
        setRateLimitInfo(result.rateLimitInfo);
        toast({
          title: "Failed to Send Code",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send verification code. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Step 2: Verify OTP
  const handleVerifyOTP = async () => {
    if (otp.length !== 6 || isLoading) return;
    
    setIsLoading(true);
    
    try {
      const result = await verifyPhoneOTP(sessionId, otp);
      
      if (result.success) {
        toast({
          title: result.action === 'signup' ? "Account Created" : "Welcome Back",
          description: result.action === 'signup'
            ? "Your account has been created successfully!"
            : "You have been logged in successfully!",
        });

        // Trigger auth success event for consumer auth state update
        window.dispatchEvent(new CustomEvent('auth:success', {
          detail: { user: result.user, action: result.action }
        }));

        // Call success callback
        onSuccess?.(result.user, result.action!);

        // Close modal
        onClose();
      } else {
        toast({
          title: "Verification Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to verify code. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle resend OTP
  const handleResendOTP = async () => {
    if (!canResend || isLoading) return;
    
    setCanResend(false);
    setResendCountdown(60);
    
    const fullPhone = `+213${phone.replace(/\D/g, '')}`;
    const result = await sendPhoneOTP(fullPhone);
    
    if (result.success) {
      setSessionId(result.verificationId!);
      setTimeLeft(300);
      setRateLimitInfo(result.rateLimitInfo);
      
      toast({
        title: "Code Resent",
        description: `New verification code sent to ${fullPhone}`,
      });
    } else {
      setCanResend(true);
      setResendCountdown(0);
      setRateLimitInfo(result.rateLimitInfo);
      
      toast({
        title: "Failed to Resend",
        description: result.error,
        variant: "destructive"
      });
    }
  };
  
  // Handle back to phone step
  const handleBackToPhone = () => {
    setStep('phone');
    setOtp('');
    setSessionId('');
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 'phone') {
      handleSendOTP();
    } else {
      handleVerifyOTP();
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {step === 'phone' ? (
              <>
                <Phone className="h-5 w-5 text-[#fa7b00]" />
                Phone Verification
              </>
            ) : (
              <>
                <Shield className="h-5 w-5 text-[#fa7b00]" />
                Enter Verification Code
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {step === 'phone' 
              ? "Enter your Algerian mobile number to get started"
              : `Enter the 6-digit code sent to +213${phone.replace(/\D/g, '')}`
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <AnimatePresence mode="wait">
            {step === 'phone' ? (
              <motion.div
                key="phone-step"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.15 }}
                className="space-y-4"
              >
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                      +213
                    </div>
                    <Input
                      id="phone"
                      ref={phoneInputRef}
                      type="tel"
                      value={phone}
                      onChange={handlePhoneChange}
                      placeholder="XXX XXX XXX"
                      className="pl-12 text-lg tracking-wider focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                      inputMode="numeric"
                      autoComplete="tel"
                      maxLength={11} // Formatted length
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Enter your 9-digit Algerian mobile number
                  </p>
                </div>
                
                {rateLimitInfo && rateLimitInfo.remainingAttempts < 3 && (
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <p className="text-sm text-yellow-800">
                      {rateLimitInfo.remainingAttempts} attempts remaining
                    </p>
                  </div>
                )}
                
                <Button
                  type="submit"
                  disabled={!isPhoneValid() || isLoading}
                  className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending Code...
                    </>
                  ) : (
                    'Send Verification Code'
                  )}
                </Button>
              </motion.div>
            ) : (
              <motion.div
                key="otp-step"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.15 }}
                className="space-y-4"
              >
                <div className="space-y-2">
                  <Label htmlFor="otp">Verification Code</Label>
                  <Input
                    id="otp"
                    ref={otpInputRef}
                    type="text"
                    value={otp}
                    onChange={handleOtpChange}
                    placeholder="123456"
                    className="text-center text-xl tracking-widest focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                    inputMode="numeric"
                    maxLength={6}
                    autoComplete="one-time-code"
                  />
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1 text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>Expires in {formatTime(timeLeft)}</span>
                  </div>
                  
                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    onClick={handleResendOTP}
                    disabled={!canResend || isLoading}
                    className="text-[#fa7b00] hover:text-[#fa7b00]/80 p-0 h-auto"
                  >
                    {canResend ? 'Resend Code' : `Resend in ${resendCountdown}s`}
                  </Button>
                </div>
                
                <div className="space-y-3">
                  <Button
                    type="submit"
                    disabled={otp.length !== 6 || isLoading}
                    className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      'Verify Code'
                    )}
                  </Button>
                  
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={handleBackToPhone}
                    className="w-full"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Change Phone Number
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </form>
        
        <div className="text-center text-xs text-gray-500">
          <p>By continuing, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
