/**
 * Consumer Authentication Guard
 * 
 * Protects consumer-specific features and shows authentication prompts
 * 🎯 CRITICAL: Preserves existing authentication system - NO CHANGES to auth flow
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Heart, Star, User, ShoppingCart } from 'lucide-react';

interface ConsumerAuthGuardProps {
  children: React.ReactNode;
  action: 'wishlist' | 'review' | 'profile' | 'general';
  fallback?: React.ReactNode;
  showModal?: boolean;
  onAuthRequired?: () => void;
}

export function ConsumerAuthGuard({ 
  children, 
  action, 
  fallback, 
  showModal = true,
  onAuthRequired 
}: ConsumerAuthGuardProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Check consumer authentication
  useEffect(() => {
    const checkAuth = () => {
      try {
        const phoneSession = localStorage.getItem('phone_auth_session');
        if (phoneSession) {
          const session = JSON.parse(phoneSession);
          const isConsumer = session.profile?.role === 'consumer';
          setIsAuthenticated(!!isConsumer);
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error checking consumer auth:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    // Listen for auth changes
    const handleAuthChange = () => checkAuth();
    window.addEventListener('auth:success', handleAuthChange);
    window.addEventListener('storage', handleAuthChange);

    return () => {
      window.removeEventListener('auth:success', handleAuthChange);
      window.removeEventListener('storage', handleAuthChange);
    };
  }, []);

  // Handle authentication requirement
  const handleAuthRequired = () => {
    if (onAuthRequired) {
      onAuthRequired();
      return;
    }

    if (showModal) {
      setShowAuthModal(true);
    } else {
      // Show toast notification
      const messages = {
        wishlist: {
          title: t('auth.loginRequired'),
          description: t('auth.loginRequiredDescription')
        },
        review: {
          title: t('auth.loginRequired'),
          description: 'Please log in to write and manage reviews'
        },
        profile: {
          title: t('auth.loginRequired'),
          description: 'Please log in to access your profile'
        },
        general: {
          title: t('auth.loginRequired'),
          description: t('auth.loginRequiredDescription')
        }
      };

      toast.error(messages[action].title, {
        description: messages[action].description,
        action: {
          label: t('auth.login'),
          onClick: () => {
            const authModal = document.getElementById('auth-modal-trigger');
            if (authModal) {
              authModal.click();
            }
          }
        }
      });
    }
  };

  // Handle login button click
  const handleLoginClick = () => {
    setShowAuthModal(false);
    const authModal = document.getElementById('auth-modal-trigger');
    if (authModal) {
      authModal.click();
    }
  };

  // Get action-specific content
  const getActionContent = () => {
    const icons = {
      wishlist: <Heart className="h-12 w-12 text-[#fa7b00] mx-auto mb-4" />,
      review: <Star className="h-12 w-12 text-[#fa7b00] mx-auto mb-4" />,
      profile: <User className="h-12 w-12 text-[#fa7b00] mx-auto mb-4" />,
      general: <ShoppingCart className="h-12 w-12 text-[#fa7b00] mx-auto mb-4" />
    };

    const titles = {
      wishlist: 'Access Your Wishlist',
      review: 'Write Product Reviews',
      profile: 'Access Your Profile',
      general: 'Sign In Required'
    };

    const descriptions = {
      wishlist: 'Save your favorite products and shop them later. Sign in to access your personal wishlist.',
      review: 'Share your experience and help other customers make informed decisions. Sign in to write reviews.',
      profile: 'Manage your account, view your orders, and access personalized features.',
      general: 'Please sign in to access this feature and enjoy a personalized shopping experience.'
    };

    return {
      icon: icons[action],
      title: titles[action],
      description: descriptions[action]
    };
  };

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 rounded h-8 w-full" />;
  }

  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Show fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Show authentication modal if requested
  const actionContent = getActionContent();

  return (
    <>
      {/* Trigger element that shows auth modal */}
      <div onClick={handleAuthRequired} className="cursor-pointer">
        {children}
      </div>

      {/* Authentication Modal */}
      <Dialog open={showAuthModal} onOpenChange={setShowAuthModal}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader className="text-center">
            {actionContent.icon}
            <DialogTitle className="text-xl font-semibold">
              {actionContent.title}
            </DialogTitle>
            <DialogDescription className="text-center mt-2">
              {actionContent.description}
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col gap-3 mt-6">
            <Button
              onClick={handleLoginClick}
              className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
            >
              {t('auth.loginOrSignUp')}
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowAuthModal(false)}
              className="w-full"
            >
              {t('actions.cancel')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

/**
 * Hook for checking consumer authentication status
 */
export function useConsumerAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      try {
        const phoneSession = localStorage.getItem('phone_auth_session');
        if (phoneSession) {
          const session = JSON.parse(phoneSession);
          const isConsumer = session.profile?.role === 'consumer';
          setIsAuthenticated(!!isConsumer);
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error checking consumer auth:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    // Listen for auth changes
    const handleAuthChange = () => checkAuth();
    window.addEventListener('auth:success', handleAuthChange);
    window.addEventListener('storage', handleAuthChange);

    return () => {
      window.removeEventListener('auth:success', handleAuthChange);
      window.removeEventListener('storage', handleAuthChange);
    };
  }, []);

  return { isAuthenticated, isLoading };
}
