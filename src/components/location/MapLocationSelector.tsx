import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents, useMap } from 'react-leaflet';
import { LatLng, Icon, DivIcon } from 'leaflet';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useLocation } from '@/contexts/LocationContext';
import { findNearestWilaya } from '@/data/algeria-cities';
import { findAccurateWilayaByCoordinates } from '@/data/algeria-accurate-location';
import { findAccuratePreciseWilayaByCoordinates } from '@/data/algeria-precise-location';
import { cn } from '@/lib/utils';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in React-Leaflet
delete (Icon.Default.prototype as any)._getIconUrl;
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icon - proper pin shape
const createCustomIcon = () => new DivIcon({
  html: `<div class="relative">
    <svg width="24" height="36" viewBox="0 0 24 36" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"
            fill="#fa7b00" stroke="#ffffff" stroke-width="2"/>
    </svg>
  </div>`,
  className: 'custom-marker',
  iconSize: [24, 36],
  iconAnchor: [12, 36], // Anchor at the bottom point of the pin
});

interface MapLocationSelectorProps {
  onLocationSelect?: (lat: number, lng: number) => void;
  initialCenter?: { lat: number; lng: number };
  height?: string;
  className?: string;
  showCurrentLocationButton?: boolean;
}

// Component to handle map clicks and marker updates
function MapClickHandler({ onLocationSelect }: { onLocationSelect?: (lat: number, lng: number) => void }) {
  const { setLocationFromCoordinates } = useLocation();
  
  useMapEvents({
    click: (e) => {
      const { lat, lng } = e.latlng;
      setLocationFromCoordinates(lat, lng);
      onLocationSelect?.(lat, lng);
    },
  });
  
  return null;
}

// Component to update map view when location changes
function MapUpdater({ center }: { center: { lat: number; lng: number } }) {
  const map = useMap();
  
  useEffect(() => {
    map.setView([center.lat, center.lng], map.getZoom());
  }, [center, map]);
  
  return null;
}

export function MapLocationSelector({
  onLocationSelect,
  initialCenter = { lat: 36.7538, lng: 3.0588 }, // Algiers as default
  height = '300px',
  className,
  showCurrentLocationButton = true
}: MapLocationSelectorProps) {
  const { 
    selectedLocation, 
    isLocationLoading, 
    locationError,
    requestCurrentLocation,
    setLocationFromCoordinates 
  } = useLocation();
  
  const [mapCenter, setMapCenter] = useState(initialCenter);
  const [isMapReady, setIsMapReady] = useState(false);
  const mapRef = useRef<any>(null);

  // Update map center when selected location changes
  useEffect(() => {
    if (selectedLocation?.coordinates) {
      setMapCenter(selectedLocation.coordinates);
    }
  }, [selectedLocation]);

  const handleCurrentLocationClick = async () => {
    try {
      await requestCurrentLocation();
    } catch (error) {
      console.error('Error requesting current location:', error);
    }
  };

  const markerPosition = selectedLocation?.coordinates 
    ? [selectedLocation.coordinates.lat, selectedLocation.coordinates.lng] as [number, number]
    : null;

  return (
    <div className={cn("relative", className)}>
      {/* Map Container */}
      <div 
        className="relative rounded-lg overflow-hidden border border-gray-200"
        style={{ height }}
      >
        <MapContainer
          ref={mapRef}
          center={[mapCenter.lat, mapCenter.lng]}
          zoom={13}
          style={{ height: '100%', width: '100%' }}
          zoomControl={true}
          scrollWheelZoom={true}
          whenReady={() => setIsMapReady(true)}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          {/* Map click handler */}
          <MapClickHandler onLocationSelect={onLocationSelect} />
          
          {/* Map updater */}
          <MapUpdater center={mapCenter} />
          
          {/* Location marker */}
          {markerPosition && (
            <Marker 
              position={markerPosition}
              icon={createCustomIcon()}
              draggable={true}
              eventHandlers={{
                dragend: (e) => {
                  const marker = e.target;
                  const position = marker.getLatLng();
                  setLocationFromCoordinates(position.lat, position.lng);
                  onLocationSelect?.(position.lat, position.lng);
                }
              }}
            />
          )}
        </MapContainer>

        {/* Loading overlay */}
        {isLocationLoading && (
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-[1000]">
            <div className="flex items-center gap-2 text-[#fa7b00]">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span className="text-sm font-medium">Getting your location...</span>
            </div>
          </div>
        )}

        {/* Current location button */}
        {showCurrentLocationButton && (
          <Button
            onClick={handleCurrentLocationClick}
            disabled={isLocationLoading}
            size="sm"
            className="absolute top-3 right-3 z-[1000] bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-sm"
            variant="outline"
          >
            <Crosshair className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Error message */}
      {locationError && (
        <div className="mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
          {locationError}
        </div>
      )}


    </div>
  );
}
