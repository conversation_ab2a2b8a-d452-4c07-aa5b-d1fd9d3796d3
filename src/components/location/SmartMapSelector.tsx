import React, { useState, useEffect } from 'react';
import { MapPin, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { GoogleMapLocationSelector } from './GoogleMapLocationSelector';
import { MapLocationSelector } from './MapLocationSelector';
import { GooglePlacesSearch } from './GooglePlacesSearch';
import { useLocation } from '@/contexts/LocationContext';
import { cn } from '@/lib/utils';

interface SmartMapSelectorProps {
  onLocationSelect?: (lat: number, lng: number) => void;
  initialCenter?: { lat: number; lng: number };
  height?: string;
  className?: string;
  showCurrentLocationButton?: boolean;
  showSearch?: boolean;
}

type MapProvider = 'google' | 'openstreetmap' | 'loading' | 'error';

export function SmartMapSelector({
  onLocationSelect,
  initialCenter = { lat: 36.7538, lng: 3.0588 }, // Algiers as default
  height = '300px',
  className,
  showCurrentLocationButton = true,
  showSearch = true
}: SmartMapSelectorProps) {
  const [mapProvider, setMapProvider] = useState<MapProvider>('loading');
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { setLocationFromCoordinates } = useLocation();

  // Check Google Maps availability
  useEffect(() => {
    const checkGoogleMaps = async () => {
      try {
        // Check if Google Maps API key is available
        const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

        console.log('🗺️ SmartMapSelector: Checking Google Maps availability...');
        console.log('🗺️ SmartMapSelector: API Key exists:', !!apiKey);

        if (!apiKey) {
          console.log('🗺️ SmartMapSelector: No API key found, using OpenStreetMap');
          setMapProvider('openstreetmap');
          return;
        }

        console.log('🗺️ SmartMapSelector: API key found:', apiKey.substring(0, 10) + '...');
        console.log('🗺️ SmartMapSelector: Setting provider to Google Maps');
        setMapProvider('google');

      } catch (error) {
        console.error('🗺️ SmartMapSelector: Error checking Google Maps:', error);
        setMapProvider('openstreetmap');
      }
    };

    checkGoogleMaps();
  }, []);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Loading state
  if (mapProvider === 'loading') {
    return (
      <div className={cn("relative", className)}>
        <div 
          className="relative rounded-lg overflow-hidden border border-gray-200 bg-gray-50 flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#fa7b00] mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading enhanced map...</p>
            <p className="text-xs text-gray-500 mt-1">Checking Google Maps availability</p>
          </div>
        </div>
      </div>
    );
  }

  // Offline state
  if (!isOnline) {
    return (
      <div className={cn("relative", className)}>
        <div 
          className="relative rounded-lg overflow-hidden border border-gray-200 bg-yellow-50 flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center p-4">
            <WifiOff className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-yellow-800 mb-1">No Internet Connection</p>
            <p className="text-xs text-yellow-600">Map features require internet access</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative space-y-3", className)}>
      {/* Google Places Search (only for Google Maps) */}
      {showSearch && mapProvider === 'google' && (
        <div>
          <GooglePlacesSearch
            placeholder="🔍 Search for places in Algeria..."
            onPlaceSelect={(place) => {
              console.log('🗺️ SmartMapSelector: Place selected from search:', place);

              // Extract coordinates from the place
              if (place.geometry?.location) {
                const lat = typeof place.geometry.location.lat === 'function'
                  ? place.geometry.location.lat()
                  : place.geometry.location.lat;
                const lng = typeof place.geometry.location.lng === 'function'
                  ? place.geometry.location.lng()
                  : place.geometry.location.lng;

                console.log('🗺️ SmartMapSelector: Updating location context with search result:', { lat, lng });

                // Update the location context which will trigger updates everywhere
                setLocationFromCoordinates(lat, lng);

                // Also call the optional callback
                onLocationSelect?.(lat, lng);
              }
            }}
          />
        </div>
      )}

      {/* Simplified Map Provider Indicator - Only show if not Google Maps */}
      {mapProvider !== 'google' && (
        <div className="flex items-center justify-between text-xs text-gray-500 px-1">
          <div className="flex items-center space-x-2">
            <MapPin className="h-3 w-3 text-blue-500" />
            <span>OpenStreetMap (Standard)</span>

            {/* Force Google Maps button */}
            {import.meta.env.VITE_GOOGLE_MAPS_API_KEY && (
              <button
                onClick={() => {
                  console.log('🗺️ Switching to Google Maps...');
                  setMapProvider('google');
                }}
                className="text-[#fa7b00] hover:text-[#e56b00] underline text-xs"
              >
                Use Google Maps
              </button>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <div className={cn(
              "w-2 h-2 rounded-full",
              isOnline ? "bg-green-500" : "bg-red-500"
            )} />
            <span>{isOnline ? "Online" : "Offline"}</span>
          </div>
        </div>
      )}

      {/* Map Component */}
      <div className="relative">
        {mapProvider === 'google' ? (
          <GoogleMapLocationSelector
            onLocationSelect={onLocationSelect}
            initialCenter={initialCenter}
            height={height}
            showCurrentLocationButton={showCurrentLocationButton}
          />
        ) : (
          <MapLocationSelector
            onLocationSelect={onLocationSelect}
            initialCenter={initialCenter}
            height={height}
            showCurrentLocationButton={showCurrentLocationButton}
          />
        )}
      </div>


    </div>
  );
}
