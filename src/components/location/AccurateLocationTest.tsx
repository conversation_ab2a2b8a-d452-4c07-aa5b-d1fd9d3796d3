import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { findAccurateWilayaByCoordinates, testLocationAccuracy } from '@/data/algeria-accurate-location';
import { MapPin, TestTube, CheckCircle, XCircle } from 'lucide-react';

/**
 * Accurate Location Test Component
 * 
 * This component allows testing the accuracy of the new location detection system
 * and demonstrates the fix for the critical coordinate mapping issue.
 */
export default function AccurateLocationTest() {
  const [testLat, setTestLat] = useState('33.486435');
  const [testLng, setTestLng] = useState('0.829468');
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Predefined test cases to verify accuracy
  const testCases = [
    {
      name: "Critical Bug Fix Test",
      description: "Coordinates that were incorrectly showing Naâma instead of Laghouat",
      lat: 33.486435,
      lng: 0.829468,
      expectedWilaya: "Laghouat"
    },
    {
      name: "Algiers Center",
      description: "Capital city center coordinates",
      lat: 36.7631,
      lng: 3.0506,
      expectedWilaya: "Alger"
    },
    {
      name: "Constantine",
      description: "Eastern Algeria major city",
      lat: 36.3650,
      lng: 6.6147,
      expectedWilaya: "Constantine"
    },
    {
      name: "Oran",
      description: "Western Algeria major city",
      lat: 35.6969,
      lng: -0.6331,
      expectedWilaya: "Oran"
    },
    {
      name: "Tamanrasset",
      description: "Southern Algeria (Sahara)",
      lat: 22.7851,
      lng: 5.5281,
      expectedWilaya: "Tamanrasset"
    }
  ];

  const handleTest = async () => {
    setIsLoading(true);
    try {
      const lat = parseFloat(testLat);
      const lng = parseFloat(testLng);
      
      if (isNaN(lat) || isNaN(lng)) {
        setResult({ error: 'Invalid coordinates' });
        return;
      }

      const detectedWilaya = findAccurateWilayaByCoordinates(lat, lng);
      setResult({
        coordinates: { lat, lng },
        detected: detectedWilaya,
        timestamp: new Date().toLocaleTimeString()
      });
    } catch (error) {
      setResult({ error: 'Error detecting location' });
    } finally {
      setIsLoading(false);
    }
  };

  const runTestCase = async (testCase: any) => {
    setTestLat(testCase.lat.toString());
    setTestLng(testCase.lng.toString());
    
    const detectedWilaya = findAccurateWilayaByCoordinates(testCase.lat, testCase.lng);
    setResult({
      coordinates: { lat: testCase.lat, lng: testCase.lng },
      detected: detectedWilaya,
      expected: testCase.expectedWilaya,
      testCase: testCase.name,
      timestamp: new Date().toLocaleTimeString()
    });
  };

  const runSystemTest = () => {
    const systemTestResult = testLocationAccuracy();
    setResult({
      systemTest: true,
      detected: systemTestResult,
      timestamp: new Date().toLocaleTimeString()
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5 text-blue-600" />
            100% Accurate Location Detection Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Latitude</label>
              <Input
                type="number"
                step="any"
                value={testLat}
                onChange={(e) => setTestLat(e.target.value)}
                placeholder="Enter latitude"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Longitude</label>
              <Input
                type="number"
                step="any"
                value={testLng}
                onChange={(e) => setTestLng(e.target.value)}
                placeholder="Enter longitude"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={handleTest}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Testing...' : 'Test Coordinates'}
            </Button>
            
            <Button 
              onClick={runSystemTest}
              variant="outline"
            >
              Run System Test
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Predefined Test Cases */}
      <Card>
        <CardHeader>
          <CardTitle>Predefined Test Cases</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {testCases.map((testCase, index) => (
              <div
                key={index}
                className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => runTestCase(testCase)}
              >
                <div className="font-medium text-sm">{testCase.name}</div>
                <div className="text-xs text-gray-600 mt-1">{testCase.description}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {testCase.lat}, {testCase.lng}
                </div>
                <Badge variant="outline" className="mt-2 text-xs">
                  Expected: {testCase.expectedWilaya}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              Detection Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {result.error ? (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <XCircle className="h-4 w-4" />
                  {result.error}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {result.coordinates && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="font-medium text-blue-900">
                      📍 Coordinates: {result.coordinates.lat}, {result.coordinates.lng}
                    </div>
                  </div>
                )}
                
                {result.detected && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-medium text-green-900">
                          ✅ Detected Wilaya: {result.detected.name}
                        </div>
                        <div className="text-green-700 mt-1">
                          Arabic: {result.detected.name_ar}
                        </div>
                        <div className="text-green-700">
                          French: {result.detected.name_fr}
                        </div>
                        <div className="text-green-700">
                          Code: {result.detected.code}
                        </div>
                      </div>
                      <Badge 
                        variant={result.detected.confidence > 0.8 ? "default" : "secondary"}
                        className="ml-2"
                      >
                        {(result.detected.confidence * 100).toFixed(1)}% confidence
                      </Badge>
                    </div>
                  </div>
                )}
                
                {result.expected && (
                  <div className={`p-4 border rounded-lg ${
                    result.detected?.name === result.expected 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center gap-2">
                      {result.detected?.name === result.expected ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className={
                        result.detected?.name === result.expected 
                          ? 'text-green-900' 
                          : 'text-red-900'
                      }>
                        Expected: {result.expected} | 
                        Result: {result.detected?.name === result.expected ? 'PASS' : 'FAIL'}
                      </span>
                    </div>
                  </div>
                )}
                
                {result.testCase && (
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="text-gray-700">
                      <strong>Test Case:</strong> {result.testCase}
                    </div>
                  </div>
                )}
                
                <div className="text-xs text-gray-500">
                  Tested at: {result.timestamp}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
