import React, { useState, useRef, useEffect } from 'react';
import { Search, MapPin, Loader2, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useLocation } from '@/contexts/LocationContext';
import { cn } from '@/lib/utils';

interface GooglePlacesSearchProps {
  onPlaceSelect?: (place: google.maps.places.PlaceResult) => void;
  placeholder?: string;
  className?: string;
  countryRestriction?: string;
}

interface PlacePrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

export function GooglePlacesSearch({
  onPlaceSelect,
  placeholder = "Search for a location in Algeria...",
  className,
  countryRestriction = 'dz' // Algeria
}: GooglePlacesSearchProps) {
  const [query, setQuery] = useState('');
  const [predictions, setPredictions] = useState<PlacePrediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);
  const { setLocationFromCoordinates } = useLocation();

  // Initialize Google Places services
  useEffect(() => {
    if (window.google && window.google.maps && window.google.maps.places) {
      autocompleteService.current = new google.maps.places.AutocompleteService();
      
      // Create a dummy div for PlacesService (required by Google Maps API)
      const dummyDiv = document.createElement('div');
      placesService.current = new google.maps.places.PlacesService(dummyDiv);
    }
  }, []);

  // Debounced search function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.length > 1 && autocompleteService.current) { // Reduced from 2 to 1 for better UX
        searchPlaces(query);
      } else {
        setPredictions([]);
        setShowSuggestions(false);
      }
    }, 200); // Reduced from 300ms to 200ms for faster response

    return () => clearTimeout(timeoutId);
  }, [query]);

  const searchPlaces = async (searchQuery: string) => {
    if (!autocompleteService.current) return;

    setIsLoading(true);
    
    try {
      const request: google.maps.places.AutocompletionRequest = {
        input: searchQuery,
        componentRestrictions: { country: countryRestriction },
        types: ['geocode', 'establishment'], // Include both geographical locations and establishments
        language: 'ar' // Arabic language support
      };

      autocompleteService.current.getPlacePredictions(
        request,
        (predictions, status) => {
          setIsLoading(false);
          
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setPredictions(predictions.map(p => ({
              place_id: p.place_id,
              description: p.description,
              structured_formatting: p.structured_formatting
            })));
            setShowSuggestions(true);
            setSelectedIndex(-1);
          } else {
            setPredictions([]);
            setShowSuggestions(false);
          }
        }
      );
    } catch (error) {
      console.error('Error searching places:', error);
      setIsLoading(false);
      setPredictions([]);
      setShowSuggestions(false);
    }
  };

  const selectPlace = async (placeId: string) => {
    if (!placesService.current) return;

    setIsLoading(true);
    setShowSuggestions(false);

    try {
      const request: google.maps.places.PlaceDetailsRequest = {
        placeId,
        fields: ['geometry', 'name', 'formatted_address', 'address_components', 'place_id']
      };

      placesService.current.getDetails(request, (place, status) => {
        setIsLoading(false);
        
        if (status === google.maps.places.PlacesServiceStatus.OK && place && place.geometry?.location) {
          const lat = place.geometry.location.lat();
          const lng = place.geometry.location.lng();

          console.log('🗺️ GooglePlacesSearch: Place details received:', {
            name: place.name,
            formatted_address: place.formatted_address,
            coordinates: { lat, lng }
          });

          // Update the query with the selected place name
          setQuery(place.name || place.formatted_address || '');

          // Update location context with coordinates (this will trigger updates everywhere)
          setLocationFromCoordinates(lat, lng);

          // Call the callback if provided
          onPlaceSelect?.(place);
        }
      });
    } catch (error) {
      console.error('Error getting place details:', error);
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || predictions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < predictions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < predictions.length) {
          selectPlace(predictions[selectedIndex].place_id);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const clearSearch = () => {
    setQuery('');
    setPredictions([]);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  return (
    <div className={cn("relative", className)}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (predictions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          placeholder={placeholder}
          className="pl-10 pr-10"
        />
        
        {/* Loading or Clear Button */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          ) : query ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="h-auto p-0 hover:bg-transparent"
            >
              <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            </Button>
          ) : null}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && predictions.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {predictions.map((prediction, index) => (
            <button
              key={prediction.place_id}
              onClick={() => selectPlace(prediction.place_id)}
              className={cn(
                "w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors",
                selectedIndex === index && "bg-gray-50"
              )}
            >
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-900 truncate">
                    {prediction.structured_formatting.main_text}
                  </div>
                  <div className="text-sm text-gray-500 truncate">
                    {prediction.structured_formatting.secondary_text}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
