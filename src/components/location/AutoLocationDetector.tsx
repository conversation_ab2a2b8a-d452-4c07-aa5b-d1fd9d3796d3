import React, { useEffect } from 'react';
import { useLocation } from '@/contexts/LocationContext';

/**
 * AutoLocationDetector Component
 * 
 * Automatically attempts to detect user's location on first app visit.
 * This component should be placed at the root level of the app to ensure
 * it runs immediately when the app loads, regardless of which page the user lands on.
 * 
 * Features:
 * - Only triggers on first visit to avoid repeated permission requests
 * - Fails silently without showing error messages to the user
 * - Respects user's previous permission decisions
 * - Integrates seamlessly with existing LocationContext
 */
export function AutoLocationDetector() {
  const { 
    isFirstVisit, 
    autoLocationAttempted, 
    hasLocationPermission, 
    attemptAutoLocation 
  } = useLocation();

  useEffect(() => {
    // Only attempt auto location if:
    // 1. This is the first visit
    // 2. Auto location hasn't been attempted yet
    // 3. User hasn't already granted permission
    if (isFirstVisit && !autoLocationAttempted && !hasLocationPermission) {
      // Small delay to ensure the app has fully loaded
      const timer = setTimeout(() => {
        attemptAutoLocation();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isFirstVisit, autoLocationAttempted, hasLocationPermission, attemptAutoLocation]);

  // This component doesn't render anything visible
  return null;
}

export default AutoLocationDetector;
