import React, { useState, useRef } from 'react';
import { MapPin, ChevronDown, Search } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useClickAway } from 'react-use';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useLocation } from '@/contexts/LocationContext';
import { SmartMapSelector } from './SmartMapSelector';
import { searchAllAdministrativeLocations, SearchResult } from '@/data/algeria-cities';

interface EnhancedDeliveryLocationButtonProps {
  className?: string;
}

export function EnhancedDeliveryLocationButton({ className }: EnhancedDeliveryLocationButtonProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const buttonRef = useRef<HTMLDivElement>(null);
  
  const { 
    selectedLocation, 
    getLocationString, 
    setLocationFromCoordinates,
    clearLocation 
  } = useLocation();

  // Close dropdown when clicking outside
  useClickAway(buttonRef, () => {
    if (isOpen) {
      setIsOpen(false);
      setSearchQuery('');
      setSearchResults([]);
    }
  });

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (value.trim()) {
      const results = searchAllAdministrativeLocations(value, 8);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  };

  const handleLocationSelect = (result: SearchResult) => {
    const location = result.location;
    if (location.coordinates) {
      setLocationFromCoordinates(location.coordinates.lat, location.coordinates.lng, location.name);
    }
    setSearchQuery('');
    setSearchResults([]);
  };

  const handleApply = () => {
    setIsOpen(false);
    setSearchQuery('');
    setSearchResults([]);
  };

  const handleCancel = () => {
    setIsOpen(false);
    setSearchQuery('');
    setSearchResults([]);
  };

  const locationText = getLocationString() || t('marketplace.selectLocation');
  const isLocationSelected = !!selectedLocation;

  return (
    <div className={cn("relative", className)} ref={buttonRef}>
      {/* Delivery Location Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center bg-white hover:bg-gray-50 rounded-full border border-gray-200 py-2 px-3.5 shadow-sm transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <MapPin className="h-5 w-5 text-[#fa7b00] mr-2 flex-shrink-0" />
        <div className="flex flex-col items-start">
          <span className="text-xs text-gray-500 leading-tight">
            {t('marketplace.deliverTo')}
          </span>
          <span className={cn(
            "text-sm font-medium truncate max-w-[120px]",
            isLocationSelected ? "text-gray-900" : "text-gray-500"
          )}>
            {locationText}
          </span>
        </div>
        <ChevronDown
          className={cn(
            "ml-1.5 h-4 w-4 text-gray-500 transition-transform",
            isOpen && "transform rotate-180"
          )}
        />
      </button>

      {/* Enhanced Dropdown with Map */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.15 }}
            className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
          >
            <div className="p-4">
              {/* Header */}
              <h3 className="font-medium text-gray-900 mb-3">
                {t('marketplace.chooseYourLocation')}
              </h3>



              {/* Search Input */}
              <div className="mb-4">
                <label htmlFor="location-search" className="block text-sm text-gray-700 mb-1">
                  {t('marketplace.enterDeliveryLocation')}
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="location-search"
                    type="text"
                    value={searchQuery}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    placeholder={t('marketplace.enterLocation')}
                    className="pl-9 focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                  />
                </div>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <div className="mt-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                    {searchResults.map((result, index) => (
                      <button
                        key={`${result.type}-${result.location.code}-${index}`}
                        onClick={() => handleLocationSelect(result)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-50 text-sm border-b border-gray-100 last:border-b-0"
                      >
                        <div className="font-medium">{result.location.name}</div>
                        <div className="text-xs text-gray-500">
                          {result.location.name_ar} • {result.hierarchy}
                        </div>
                        <div className="text-xs text-blue-600 capitalize">
                          {result.type}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Interactive Map */}
              <div className="mb-4">
                <SmartMapSelector
                  height="200px"
                  showCurrentLocationButton={true}
                  showSearch={false}
                  className="rounded-md overflow-hidden"
                />
              </div>

              {/* Selected Location Display */}
              {selectedLocation && (
                <div className="mb-4 p-4 bg-gradient-to-r from-[#fa7b00]/10 to-[#ffd9a3]/20 border border-[#fa7b00]/20 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4 text-[#fa7b00]" />
                        <span className="text-sm font-semibold text-[#fa7b00]">
                          Selected Location
                        </span>
                      </div>
                      <div className="text-base font-medium text-gray-900 leading-relaxed">
                        {getLocationString()}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearLocation}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 ml-2"
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="text-gray-700 hover:bg-gray-100"
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  onClick={handleApply}
                  disabled={!selectedLocation}
                  className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
                >
                  {t('common.apply')}
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
