import React, { useState, useRef, useEffect } from 'react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from './scroll-area';

interface EnhancedDropdownProps {
  options: {
    value: string;
    label: string;
    [key: string]: any;
  }[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  disabled?: boolean;
  className?: string;
  renderOption?: (option: any) => React.ReactNode;
  groupBy?: (option: any) => string;
  emptyMessage?: string;
  maxHeight?: string;
}

const EnhancedDropdown: React.FC<EnhancedDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  searchPlaceholder = 'Search...',
  disabled = false,
  className,
  renderOption,
  groupBy,
  emptyMessage = 'No results found.',
  maxHeight = '250px'
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus the search input when the dropdown opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Filter options based on search query
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group options if groupBy function is provided
  const groupedOptions = groupBy
    ? filteredOptions.reduce((groups, option) => {
        const groupName = groupBy(option);
        if (!groups[groupName]) {
          groups[groupName] = [];
        }
        groups[groupName].push(option);
        return groups;
      }, {} as Record<string, typeof options>)
    : { '': filteredOptions };

  // Get the selected option's label
  const selectedOption = options.find(option => option.value === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-full justify-between border",
            "bg-white hover:bg-gray-50",
            "rounded-lg shadow-sm",
            "text-left font-normal",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
        >
          {selectedOption ? (
            renderOption ? (
              renderOption(selectedOption)
            ) : (
              selectedOption.label
            )
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 rounded-lg shadow-lg border w-full min-w-[240px] max-h-[80vh]"
        align="start"
        side="bottom"
        avoidCollisions={true}
        collisionPadding={20}
        sticky="always"
        sideOffset={4}
      >
        <div className="rounded-lg flex flex-col h-full">
          <div className="border-b">
            <CommandInput
              placeholder={searchPlaceholder}
              className="h-10 rounded-t-lg"
              value={searchQuery}
              onValueChange={setSearchQuery}
              ref={inputRef}
            />
          </div>
          <div className="overflow-y-auto" style={{ maxHeight: maxHeight }}>
            <Command className="h-full">
              <CommandList>
                <CommandEmpty>{emptyMessage}</CommandEmpty>
                {Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
                  <CommandGroup key={groupName} heading={groupName !== '' ? groupName : undefined}>
                    {groupOptions.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        onSelect={() => {
                          onChange(option.value);
                          setOpen(false);
                          setSearchQuery("");
                        }}
                        className="py-1.5 px-3 cursor-pointer text-sm"
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center">
                            {renderOption ? renderOption(option) : option.label}
                          </div>
                          {value === option.value && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                ))}
              </CommandList>
            </Command>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default EnhancedDropdown;
