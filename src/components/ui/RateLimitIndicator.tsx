import React from 'react';
import { Clock, AlertTriangle, Shield } from 'lucide-react';

interface RateLimitIndicatorProps {
  remainingAttempts: number;
  maxAttempts: number;
  countdown?: number;
  type?: 'warning' | 'blocked' | 'info';
  message?: string;
}

export function RateLimitIndicator({ 
  remainingAttempts, 
  maxAttempts, 
  countdown = 0,
  type = 'info',
  message 
}: RateLimitIndicatorProps) {
  const getStyles = () => {
    switch (type) {
      case 'warning':
        return {
          container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          icon: 'text-yellow-600',
          progress: 'bg-yellow-200'
        };
      case 'blocked':
        return {
          container: 'bg-red-50 border-red-200 text-red-800',
          icon: 'text-red-600',
          progress: 'bg-red-200'
        };
      default:
        return {
          container: 'bg-blue-50 border-blue-200 text-blue-800',
          icon: 'text-blue-600',
          progress: 'bg-blue-200'
        };
    }
  };

  const styles = getStyles();
  const progressPercentage = (remainingAttempts / maxAttempts) * 100;

  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  };

  const getIcon = () => {
    if (countdown > 0) return <Clock className="h-4 w-4 flex-shrink-0" />;
    if (type === 'blocked') return <AlertTriangle className="h-4 w-4 flex-shrink-0" />;
    return <Shield className="h-4 w-4 flex-shrink-0" />;
  };

  return (
    <div className={`flex items-start gap-3 p-4 border rounded-lg ${styles.container}`}>
      <div className={styles.icon}>
        {getIcon()}
      </div>
      
      <div className="flex-1 space-y-2">
        <div className="flex items-center justify-between">
          <p className="font-medium text-sm">
            {type === 'blocked' ? 'Rate Limit Exceeded' : 
             type === 'warning' ? 'Rate Limit Warning' : 
             'Rate Limit Protection'}
          </p>
          {countdown > 0 && (
            <span className="text-xs font-mono bg-white/50 px-2 py-1 rounded">
              {formatTime(countdown)}
            </span>
          )}
        </div>
        
        {message && (
          <p className="text-sm">{message}</p>
        )}
        
        {countdown > 0 ? (
          <p className="text-xs">
            {type === 'blocked' ? 
              `Please wait ${formatTime(countdown)} before trying again.` :
              `Next attempt available in ${formatTime(countdown)}.`
            }
          </p>
        ) : (
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>Attempts remaining</span>
              <span className="font-medium">{remainingAttempts}/{maxAttempts}</span>
            </div>
            <div className="w-full bg-white/50 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${styles.progress}`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>
        )}
        
        {remainingAttempts === 0 && countdown === 0 && (
          <p className="text-xs font-medium">
            All attempts used. Rate limit will reset automatically.
          </p>
        )}
      </div>
    </div>
  );
}

export default RateLimitIndicator;
