import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X, Search, Check, HelpCircle, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { fetchCarBrands, fetchCarModels, fetchCarEngines } from '@/services/vehicleDataService';
import { cn } from '@/lib/utils';
import { useFilter } from '@/contexts/FilterContext';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";



interface AirbnbStyleFilterModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: 'tyres' | 'parts';
  onSubmit: (filters: any) => void;
  initialFilters?: any;
}

export function AirbnbStyleFilterModal({
  open,
  onOpenChange,
  type,
  onSubmit,
  initialFilters = {}
}: AirbnbStyleFilterModalProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const { selectedCategory } = useFilter();

  // Tyres state
  const [width, setWidth] = useState(initialFilters.width || '');
  const [aspectRatio, setAspectRatio] = useState(initialFilters.aspectRatio || '');
  const [rimDiameter, setRimDiameter] = useState(initialFilters.rimDiameter || '');
  const [season, setSeason] = useState(initialFilters.season || '');

  // Vehicle parts state
  const [brand, setBrand] = useState(initialFilters.brand || '');
  const [model, setModel] = useState(initialFilters.model || '');
  const [engine, setEngine] = useState(initialFilters.engine || '');

  // Active section for the Airbnb-style filter
  const [activeSection, setActiveSection] = useState<'brand' | 'model' | 'engine'>(
    brand ? (model ? (engine ? 'engine' : 'model') : 'brand') : 'brand'
  );

  // Search state
  const [brandSearch, setBrandSearch] = useState('');
  const [modelSearch, setModelSearch] = useState('');
  const [engineSearch, setEngineSearch] = useState('');

  // Options for dropdowns
  const [brandOptions, setBrandOptions] = useState<any[]>([]);
  const [modelOptions, setModelOptions] = useState<any[]>([]);
  const [engineOptions, setEngineOptions] = useState<any[]>([]);

  // Fetch brands on initial load
  useEffect(() => {
    const fetchBrands = async () => {
      setLoading(true);
      try {
        const brands = await fetchCarBrands();
        setBrandOptions(brands);
      } catch (error) {
        console.error('Error fetching brands:', error);
      } finally {
        setLoading(false);
      }
    };

    if (type === 'parts' && open) {
      fetchBrands();
    }
  }, [type, open]);

  // Fetch models when brand changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!brand) {
        setModelOptions([]);
        return;
      }

      setLoading(true);
      try {
        const models = await fetchCarModels(brand);
        setModelOptions(models);

        // Auto-expand model section when brand is selected
        if (activeSection === 'brand') {
          setActiveSection('model');
        }
      } catch (error) {
        console.error('Error fetching models:', error);
      } finally {
        setLoading(false);
      }
    };

    if (type === 'parts') {
      fetchModels();
    }
  }, [brand, type, activeSection]);

  // Fetch engines when model changes
  useEffect(() => {
    const fetchEngines = async () => {
      if (!brand || !model) {
        setEngineOptions([]);
        return;
      }

      setLoading(true);
      try {
        // Parse the uniqueId to get model and generation information
        const parts = model.split('|');
        const modelName = parts[1];
        const generationCode = parts[2];

        const engines = await fetchCarEngines(brand, modelName, generationCode);
        setEngineOptions(engines);

        // Auto-expand engine section when model is selected
        if (activeSection === 'model') {
          setActiveSection('engine');
        }
      } catch (error) {
        console.error('Error fetching engines:', error);
      } finally {
        setLoading(false);
      }
    };

    if (type === 'parts') {
      fetchEngines();
    }
  }, [brand, model, type, activeSection]);

  const handleSubmit = () => {
    if (type === 'tyres') {
      onSubmit({
        width,
        aspectRatio,
        rimDiameter,
        season
      });
    } else {
      onSubmit({
        brand,
        model,
        engine
      });
    }
    onOpenChange(false);
  };

  // Filter options based on search
  const filteredBrands = brandOptions.filter(
    brand => brand.name.toLowerCase().includes(brandSearch.toLowerCase())
  );

  const filteredModels = modelOptions.filter(
    model => model.model.toLowerCase().includes(modelSearch.toLowerCase())
  );

  const filteredEngines = engineOptions.filter(
    engine => engine.fullName.toLowerCase().includes(engineSearch.toLowerCase())
  );

  // Group models by name
  const groupedModels = filteredModels.reduce((acc, model) => {
    if (!acc[model.model]) {
      acc[model.model] = [];
    }
    acc[model.model].push(model);
    return acc;
  }, {});

  // Group engines by fuel type
  const groupedEngines = filteredEngines.reduce((acc, engine) => {
    const fuelType = engine.fuel_type || 'Unknown';
    if (!acc[fuelType]) {
      acc[fuelType] = [];
    }
    acc[fuelType].push(engine);
    return acc;
  }, {});

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto p-0 gap-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-xl font-semibold">
            {type === 'tyres' ? t('marketplace.tyreSelectorTitle') : t('marketplace.vehicleSelectorTitle')}
          </DialogTitle>
        </DialogHeader>

        <div className="p-6">

          {type === 'tyres' ? (
            // Tyres filter with Airbnb-style UI
            <div className="space-y-6">
              {/* Tyre Parameters Reference Guide */}
              <div className="bg-[#f8f9fa] border border-[#e9ecef] rounded-xl p-4 mb-6">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Info className="h-5 w-5 text-[#fa7b00] mr-2" />
                    <h3 className="text-lg font-medium text-[#071c44]">{t('marketplace.tyreParametersGuide')}</h3>
                  </div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="text-[#fa7b00] border-[#fa7b00] hover:bg-[#ffd9a3]/20">
                        <HelpCircle className="h-4 w-4 mr-1" />
                        {t('marketplace.learnMore')}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[350px] p-4" side="top">
                      <div className="space-y-2">
                        <h4 className="font-medium text-[#071c44]">{t('marketplace.tyreParametersExplained')}</h4>
                        <p className="text-sm text-gray-600">{t('marketplace.tyreParametersDescription')}</p>
                        <ul className="text-sm space-y-1 text-gray-600">
                          <li><span className="font-medium">225</span> - {t('marketplace.tyreWidthDescription')}</li>
                          <li><span className="font-medium">45</span> - {t('marketplace.aspectRatioDescription')}</li>
                          <li><span className="font-medium">R</span> - {t('marketplace.constructionTypeDescription')}</li>
                          <li><span className="font-medium">18</span> - {t('marketplace.rimDiameterDescription')}</li>
                          <li><span className="font-medium">91</span> - {t('marketplace.loadIndexDescription')}</li>
                          <li><span className="font-medium">Y</span> - {t('marketplace.speedRatingDescription')}</li>
                        </ul>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="relative">
                  <img
                    src="/images/Parameters_Explained.png"
                    alt="Tyre Parameters Explained"
                    className="w-full max-w-[500px] mx-auto rounded-lg shadow-sm"
                  />
                  <p className="text-sm text-gray-600 mt-3 text-center">
                    {t('marketplace.tyreParametersHelp')}
                  </p>
                </div>
              </div>

              {/* Width Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all",
                  activeSection === 'brand' ? "border-black shadow-md" : "border-gray-200"
                )}
                onClick={() => setActiveSection('brand')}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.tyreWidth')}
                  </h3>

                  {activeSection === 'brand' ? (
                    <div className="mt-4 grid grid-cols-3 gap-2 max-h-[300px] overflow-y-auto">
                      {[155, 165, 175, 185, 195, 205, 215, 225, 235, 245, 255, 265, 275, 285, 295, 305, 315, 325].map((w) => (
                        <div
                          key={w}
                          className={cn(
                            "flex items-center justify-center p-3 rounded-lg cursor-pointer border",
                            width === w.toString()
                              ? "bg-gray-100 border-black"
                              : "border-gray-200 hover:border-gray-300"
                          )}
                          onClick={() => setWidth(w.toString())}
                        >
                          <span className="font-medium">{w} mm</span>
                          {width === w.toString() && (
                            <Check className="ml-2 h-4 w-4 text-black" />
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 mt-1">
                      {width ? `${width} mm` : t('marketplace.selectWidth')}
                    </p>
                  )}
                </div>
              </div>

              {/* Aspect Ratio Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all",
                  activeSection === 'model' ? "border-black shadow-md" : "border-gray-200"
                )}
                onClick={() => setActiveSection('model')}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.aspectRatio')}
                  </h3>

                  {activeSection === 'model' ? (
                    <div className="mt-4 grid grid-cols-3 gap-2 max-h-[300px] overflow-y-auto">
                      {[30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85].map((ratio) => (
                        <div
                          key={ratio}
                          className={cn(
                            "flex items-center justify-center p-3 rounded-lg cursor-pointer border",
                            aspectRatio === ratio.toString()
                              ? "bg-gray-100 border-black"
                              : "border-gray-200 hover:border-gray-300"
                          )}
                          onClick={() => setAspectRatio(ratio.toString())}
                        >
                          <span className="font-medium">{ratio}%</span>
                          {aspectRatio === ratio.toString() && (
                            <Check className="ml-2 h-4 w-4 text-black" />
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 mt-1">
                      {aspectRatio ? `${aspectRatio}%` : t('marketplace.selectRatio')}
                    </p>
                  )}
                </div>
              </div>

              {/* Rim Diameter Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all",
                  activeSection === 'engine' ? "border-black shadow-md" : "border-gray-200"
                )}
                onClick={() => setActiveSection('engine')}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.rimDiameter')}
                  </h3>

                  {activeSection === 'engine' ? (
                    <div className="mt-4 grid grid-cols-3 gap-2 max-h-[300px] overflow-y-auto">
                      {[13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24].map((d) => (
                        <div
                          key={d}
                          className={cn(
                            "flex items-center justify-center p-3 rounded-lg cursor-pointer border",
                            rimDiameter === d.toString()
                              ? "bg-gray-100 border-black"
                              : "border-gray-200 hover:border-gray-300"
                          )}
                          onClick={() => setRimDiameter(d.toString())}
                        >
                          <span className="font-medium">{d}"</span>
                          {rimDiameter === d.toString() && (
                            <Check className="ml-2 h-4 w-4 text-black" />
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 mt-1">
                      {rimDiameter ? `${rimDiameter}"` : t('marketplace.selectDiameter')}
                    </p>
                  )}
                </div>
              </div>

              {/* Season Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all"
                )}
                onClick={() => {}}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.season')}
                  </h3>

                  <div className="mt-4 grid grid-cols-3 gap-2">
                    <div
                      className={cn(
                        "flex items-center justify-center p-3 rounded-lg cursor-pointer border",
                        season === 'summer'
                          ? "bg-gray-100 border-black"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => setSeason('summer')}
                    >
                      <span className="font-medium">{t('marketplace.summer')}</span>
                      {season === 'summer' && (
                        <Check className="ml-2 h-4 w-4 text-black" />
                      )}
                    </div>

                    <div
                      className={cn(
                        "flex items-center justify-center p-3 rounded-lg cursor-pointer border",
                        season === 'winter'
                          ? "bg-gray-100 border-black"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => setSeason('winter')}
                    >
                      <span className="font-medium">{t('marketplace.winter')}</span>
                      {season === 'winter' && (
                        <Check className="ml-2 h-4 w-4 text-black" />
                      )}
                    </div>

                    <div
                      className={cn(
                        "flex items-center justify-center p-3 rounded-lg cursor-pointer border",
                        season === 'all-season'
                          ? "bg-gray-100 border-black"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => setSeason('all-season')}
                    >
                      <span className="font-medium">{t('marketplace.allSeason')}</span>
                      {season === 'all-season' && (
                        <Check className="ml-2 h-4 w-4 text-black" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Vehicle parts filter with Airbnb-style UI
            <div className="space-y-6">
              {/* Brand Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all",
                  activeSection === 'brand' ? "border-black shadow-md" : "border-gray-200"
                )}
                onClick={() => setActiveSection('brand')}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.selectBrand')}
                  </h3>

                  {activeSection === 'brand' ? (
                    <div className="mt-2">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          value={brandSearch}
                          onChange={(e) => setBrandSearch(e.target.value)}
                          placeholder={t('marketplace.searchBrands')}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full"
                        />
                      </div>

                      <div className="mt-4 max-h-[300px] overflow-y-auto">
                        {filteredBrands.map((brandOption) => (
                          <div
                            key={brandOption.name}
                            className={cn(
                              "flex items-center p-3 rounded-lg cursor-pointer",
                              brand === brandOption.name ? "bg-gray-100" : "hover:bg-gray-50"
                            )}
                            onClick={() => setBrand(brandOption.name)}
                          >
                            <div className="flex-shrink-0 w-8 h-8 rounded-md bg-gray-100 flex items-center justify-center mr-3">
                              {brandOption.image_url ? (
                                <img
                                  src={brandOption.image_url}
                                  alt={brandOption.name}
                                  className="w-6 h-6 object-contain"
                                />
                              ) : (
                                <span className="text-sm font-medium text-gray-500">
                                  {brandOption.name.charAt(0)}
                                </span>
                              )}
                            </div>
                            <span className="font-medium">{brandOption.name}</span>
                            {brand === brandOption.name && (
                              <Check className="ml-auto h-4 w-4 text-black" />
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500 mt-1">
                      {brand || t('marketplace.noBrandSelected')}
                    </p>
                  )}
                </div>
              </div>

              {/* Model Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all",
                  activeSection === 'model' ? "border-black shadow-md" : "border-gray-200",
                  !brand && "opacity-50 pointer-events-none"
                )}
                onClick={() => brand && setActiveSection('model')}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.selectModel')}
                  </h3>

                  {activeSection === 'model' && brand ? (
                    <div className="mt-2">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          value={modelSearch}
                          onChange={(e) => setModelSearch(e.target.value)}
                          placeholder={t('marketplace.searchModels')}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full"
                        />
                      </div>

                      <div className="mt-4 max-h-[300px] overflow-y-auto">
                        {Object.entries(groupedModels).map(([modelName, models]) => (
                          <div key={modelName} className="mb-4">
                            <h4 className="text-sm font-semibold text-gray-500 px-3 mb-1">{modelName}</h4>
                            {(models as any[]).map((modelOption) => (
                              <div
                                key={modelOption.uniqueId}
                                className={cn(
                                  "flex items-center p-3 rounded-lg cursor-pointer",
                                  model === modelOption.uniqueId ? "bg-gray-100" : "hover:bg-gray-50"
                                )}
                                onClick={() => setModel(modelOption.uniqueId)}
                              >
                                <div className="flex flex-col">
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium">{modelOption.model}</span>
                                    <span className="text-xs bg-gray-100 px-1 rounded">{modelOption.generation}</span>
                                    <span className="text-xs text-blue-600">{modelOption.bodyType}</span>
                                  </div>
                                  <span className="text-xs text-muted-foreground">{modelOption.dateRange}</span>
                                </div>
                                {model === modelOption.uniqueId && (
                                  <Check className="ml-auto h-4 w-4 text-black" />
                                )}
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500 mt-1">
                      {model ? modelOptions.find(m => m.uniqueId === model)?.fullName : t('marketplace.noModelSelected')}
                    </p>
                  )}
                </div>
              </div>

              {/* Engine Section */}
              <div
                className={cn(
                  "border rounded-xl overflow-hidden transition-all",
                  activeSection === 'engine' ? "border-black shadow-md" : "border-gray-200",
                  (!brand || !model) && "opacity-50 pointer-events-none"
                )}
                onClick={() => (brand && model) && setActiveSection('engine')}
              >
                <div className="p-4">
                  <h3 className="text-lg font-medium">
                    {t('marketplace.selectEngine')}
                  </h3>

                  {activeSection === 'engine' && brand && model ? (
                    <div className="mt-2">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          value={engineSearch}
                          onChange={(e) => setEngineSearch(e.target.value)}
                          placeholder={t('marketplace.searchEngines')}
                          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full"
                        />
                      </div>

                      <div className="mt-4 max-h-[300px] overflow-y-auto">
                        {Object.entries(groupedEngines).map(([fuelType, engines]) => (
                          <div key={fuelType} className="mb-4">
                            <h4 className="text-sm font-semibold text-gray-500 px-3 mb-1">{fuelType}</h4>
                            {(engines as any[]).map((engineOption) => (
                              <div
                                key={engineOption.fullName}
                                className={cn(
                                  "flex items-center p-3 rounded-lg cursor-pointer",
                                  engine === engineOption.fullName ? "bg-gray-100" : "hover:bg-gray-50"
                                )}
                                onClick={() => setEngine(engineOption.fullName)}
                              >
                                <div className="flex flex-col">
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium text-sm">
                                      {engineOption.fullName.split('(')[0].trim()}
                                    </span>
                                    {engineOption.code && (
                                      <span className="text-xs bg-gray-100 px-1 rounded">{engineOption.code}</span>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2 text-xs">
                                    {(engineOption.power_kW || engineOption.power_HP) && (
                                      <span className="text-blue-600">
                                        {engineOption.power_kW} kW / {engineOption.power_HP} HP
                                      </span>
                                    )}
                                  </div>
                                </div>
                                {engine === engineOption.fullName && (
                                  <Check className="ml-auto h-4 w-4 text-black" />
                                )}
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500 mt-1">
                      {engine || t('marketplace.noEngineSelected')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="p-4 border-t mt-4 flex justify-between items-center">
          <Button variant="link" onClick={() => {
            // Clear all filters
            if (type === 'tyres') {
              setWidth('');
              setAspectRatio('');
              setRimDiameter('');
              setSeason('');
              // Submit empty filters to parent component
              onSubmit({
                width: '',
                aspectRatio: '',
                rimDiameter: '',
                season: ''
              });
            } else {
              setBrand('');
              setModel('');
              setEngine('');
              setActiveSection('brand');
              // Submit empty filters to parent component
              onSubmit({
                brand: '',
                model: '',
                engine: ''
              });
            }
            // Close the modal
            onOpenChange(false);
          }}>
            {t('actions.clearAll')}
          </Button>

          <Button
            onClick={handleSubmit}
            className="bg-[#fa7b00] hover:bg-[#e06e00] text-white px-6 py-2 rounded-lg"
          >
            {t('actions.search')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}