import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search, MapPin } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Avatar } from "@/components/ui/avatar";
import { fetchCarBrands, fetchCarModels, fetchCarEngines } from '@/services/vehicleDataService';
import { cn } from '@/lib/utils';

// Reusing the enhanced dropdown from compatibility hub
import EnhancedDropdown from "@/components/ui/enhanced-dropdown";

interface VehiclePartsSearchProps {
  isCompact?: boolean;
}

export function VehiclePartsSearch({ isCompact = false }: VehiclePartsSearchProps) {
  const { t } = useTranslation();
  const [location, setLocation] = useState('');
  const [filters, setFilters] = useState({
    brand: '',
    model: '',
    engineType: ''
  });

  // State for dropdown options
  const [brandOptions, setBrandOptions] = useState<any[]>([]);
  const [modelOptions, setModelOptions] = useState<any[]>([]);
  const [engineOptions, setEngineOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState({
    brands: false,
    models: false,
    engines: false
  });

  // Fetch car brands on component mount
  useEffect(() => {
    const loadBrands = async () => {
      setLoading(prev => ({ ...prev, brands: true }));
      try {
        const brands = await fetchCarBrands();
        setBrandOptions(brands);
      } catch (error) {
        console.error('Error fetching car brands:', error);
      } finally {
        setLoading(prev => ({ ...prev, brands: false }));
      }
    };

    loadBrands();
  }, []);

  // Fetch car models when brand changes
  useEffect(() => {
    const loadModels = async () => {
      if (!filters.brand) {
        setModelOptions([]);
        return;
      }

      setLoading(prev => ({ ...prev, models: true }));
      try {
        const models = await fetchCarModels(filters.brand);
        setModelOptions(models);
      } catch (error) {
        console.error('Error fetching car models:', error);
      } finally {
        setLoading(prev => ({ ...prev, models: false }));
      }
    };

    loadModels();
  }, [filters.brand]);

  // Fetch car engines when model changes
  useEffect(() => {
    const loadEngines = async () => {
      if (!filters.model) {
        setEngineOptions([]);
        return;
      }

      setLoading(prev => ({ ...prev, engines: true }));
      try {
        const { brand, model } = parseUniqueId(filters.model);
        const engines = await fetchCarEngines(brand, model);
        setEngineOptions(engines);
      } catch (error) {
        console.error('Error fetching car engines:', error);
      } finally {
        setLoading(prev => ({ ...prev, engines: false }));
      }
    };

    loadEngines();
  }, [filters.model]);

  // Helper function to parse uniqueId
  const parseUniqueId = (uniqueId: string) => {
    const parts = uniqueId.split('_');
    return {
      brand: parts[0],
      model: parts[1]
    };
  };

  const handleFilterChange = (filterName: string, value: string) => {
    // Reset dependent filters
    if (filterName === 'brand') {
      setFilters({
        brand: value,
        model: '',
        engineType: ''
      });
    } else if (filterName === 'model') {
      setFilters({
        ...filters,
        model: value,
        engineType: ''
      });
    } else {
      setFilters({
        ...filters,
        [filterName]: value
      });
    }
  };

  // Render functions for dropdown options
  const renderBrandOption = (option: any) => (
    <div className="flex items-center gap-2">
      {option.image_url ? (
        <Avatar className="h-6 w-6">
          <img src={option.image_url} alt={option.label} />
        </Avatar>
      ) : (
        <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
          {option.label.charAt(0)}
        </div>
      )}
      <span>{option.label}</span>
    </div>
  );

  const renderModelOption = (option: any) => (
    <div className="flex flex-col">
      <span className="font-medium">{option.model}</span>
      <span className="text-xs text-muted-foreground">
        {option.generation} {option.bodyType && `• ${option.bodyType}`}
        {option.dateRange && ` • ${option.dateRange}`}
      </span>
    </div>
  );

  const renderEngineOption = (option: any) => (
    <div className="flex flex-col">
      <span className="font-medium">{option.code}</span>
      <span className="text-xs text-muted-foreground">
        {option.power_kW && `${option.power_kW} kW`}
        {option.power_HP && ` (${option.power_HP} HP)`}
        {option.fuel_type && ` • ${option.fuel_type}`}
      </span>
    </div>
  );

  // Group functions for dropdown options
  const groupModelsByName = (option: any) => option.model;
  const groupEnginesByFuelType = (option: any) => option.fuel_type || 'Other';

  const handleSearch = () => {
    console.log('Searching for parts with filters:', filters, 'and location:', location);
    // In a real app, this would navigate to search results or trigger an API call
  };

  return (
    <div className={cn(
      "flex flex-col transition-all duration-300",
      isCompact ? "gap-3" : "gap-4"
    )}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
        {/* Brand Selection */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="brand-select"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            <div className="flex items-center">
              <div className={cn(
                "flex items-center justify-center rounded-full bg-[#fa7b00] text-white mr-2 font-bold transition-all duration-300",
                isCompact ? "w-5 h-5 text-xs" : "w-6 h-6 text-sm"
              )}>
                1
              </div>
              <span>{t('marketplace.selectBrand')}</span>
            </div>
          </Label>
          <EnhancedDropdown
            options={brandOptions.map(brand => ({
              value: brand.name,
              label: brand.name,
              image_url: brand.image_url
            }))}
            value={filters.brand}
            onChange={(value) => handleFilterChange('brand', value)}
            placeholder={t('marketplace.searchBrands')}
            searchPlaceholder={t('marketplace.searchForBrand')}
            renderOption={renderBrandOption}
            emptyMessage={t('marketplace.noBrandsFound')}
            maxHeight="300px"
            className={cn(
              "border-gray-300 focus:ring-[#fa7b00] focus:border-[#fa7b00] rounded-lg transition-all duration-300",
              isCompact ? "h-9 text-sm" : "h-10"
            )}
          />
        </div>

        {/* Model Selection */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="model-select"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            <div className="flex items-center">
              <div className={cn(
                "flex items-center justify-center rounded-full bg-gray-200 text-gray-700 mr-2 font-bold transition-all duration-300",
                isCompact ? "w-5 h-5 text-xs" : "w-6 h-6 text-sm"
              )}>
                2
              </div>
              <span>{t('marketplace.selectModel')}</span>
            </div>
          </Label>
          <EnhancedDropdown
            options={modelOptions.map(model => ({
              value: model.uniqueId,
              label: model.fullName || `${model.model} (${model.generation})`,
              model: model.model,
              generation: model.generation,
              bodyType: model.bodyType,
              dateRange: model.dateRange
            }))}
            value={filters.model}
            onChange={(value) => handleFilterChange('model', value)}
            placeholder={t('marketplace.searchModels')}
            searchPlaceholder={t('marketplace.searchForModel')}
            renderOption={renderModelOption}
            groupBy={groupModelsByName}
            emptyMessage={t('marketplace.noModelsFound')}
            disabled={!filters.brand}
            maxHeight="300px"
            className={cn(
              "border-gray-300 focus:ring-[#fa7b00] focus:border-[#fa7b00] rounded-lg transition-all duration-300",
              isCompact ? "h-9 text-sm" : "h-10"
            )}
          />
        </div>

        {/* Engine Selection */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="engine-select"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            <div className="flex items-center">
              <div className={cn(
                "flex items-center justify-center rounded-full bg-gray-200 text-gray-700 mr-2 font-bold transition-all duration-300",
                isCompact ? "w-5 h-5 text-xs" : "w-6 h-6 text-sm"
              )}>
                3
              </div>
              <span>{t('marketplace.selectEngine')}</span>
            </div>
          </Label>
          <EnhancedDropdown
            options={engineOptions.map(engine => ({
              value: engine.code,
              label: engine.fullName || engine.code,
              code: engine.code,
              fuel_type: engine.fuel_type,
              power_kW: engine.power_kW,
              power_HP: engine.power_HP
            }))}
            value={filters.engineType}
            onChange={(value) => handleFilterChange('engineType', value)}
            placeholder={t('marketplace.searchEngines')}
            searchPlaceholder={t('marketplace.searchForEngine')}
            renderOption={renderEngineOption}
            groupBy={groupEnginesByFuelType}
            emptyMessage={t('marketplace.noEnginesFound')}
            disabled={!filters.model}
            maxHeight="300px"
            className={cn(
              "border-gray-300 focus:ring-[#fa7b00] focus:border-[#fa7b00] rounded-lg transition-all duration-300",
              isCompact ? "h-9 text-sm" : "h-10"
            )}
          />
        </div>

        {/* Delivery Location */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="delivery-location"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            <div className="flex items-center">
              <div className={cn(
                "flex items-center justify-center rounded-full bg-gray-200 text-gray-700 mr-2 font-bold transition-all duration-300",
                isCompact ? "w-5 h-5 text-xs" : "w-6 h-6 text-sm"
              )}>
                4
              </div>
              <span>{t('marketplace.deliverTo')}</span>
            </div>
          </Label>
          <div className="relative">
            <MapPin
              className={cn(
                "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 transition-all duration-300",
                isCompact ? "h-4 w-4" : "h-5 w-5"
              )}
            />
            <Input
              id="delivery-location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder={t('marketplace.enterLocation')}
              className={cn(
                "pl-10 border-gray-300 focus:ring-[#fa7b00] focus:border-[#fa7b00] rounded-lg transition-all duration-300",
                isCompact ? "h-9 text-sm" : "h-10"
              )}
            />
          </div>
        </div>
      </div>

      {/* Search Button */}
      <div className="flex justify-center mt-2">
        <Button
          onClick={handleSearch}
          className={cn(
            "bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white font-medium rounded-full transition-all duration-300",
            isCompact
              ? "px-6 py-1.5 text-sm h-9"
              : "px-8 py-2 text-base h-10"
          )}
          disabled={!filters.brand || !filters.model}
        >
          <Search className={cn("mr-2 transition-all duration-300", isCompact ? "h-4 w-4" : "h-5 w-5")} />
          {t('marketplace.searchParts')}
        </Button>
      </div>
    </div>
  );
}
