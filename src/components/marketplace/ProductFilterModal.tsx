import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { X, ChevronDown, ChevronUp, Filter, Grid3X3, List } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

interface ProductFilters {
  priceRange: [number, number];
  brands: string[];
  stockStatus: string[];
  ratings: number[];
  categories: string[];
  sortBy: string;
}

interface ProductFilterModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFiltersChange: (filters: ProductFilters) => void;
  initialFilters?: Partial<ProductFilters>;
  availableProducts?: any[]; // For dynamic filter options
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
}

export function ProductFilterModal({
  open,
  onOpenChange,
  onFiltersChange,
  initialFilters = {},
  availableProducts = [],
  viewMode = 'grid',
  onViewModeChange
}: ProductFilterModalProps) {
  const { t } = useTranslation();

  // Filter state
  const [priceRange, setPriceRange] = useState<[number, number]>(
    initialFilters.priceRange || [0, 10000]
  );
  const [selectedBrands, setSelectedBrands] = useState<string[]>(
    initialFilters.brands || []
  );
  const [selectedStockStatus, setSelectedStockStatus] = useState<string[]>(
    initialFilters.stockStatus || []
  );
  const [selectedRatings, setSelectedRatings] = useState<number[]>(
    initialFilters.ratings || []
  );
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    initialFilters.categories || []
  );
  const [sortBy, setSortBy] = useState<string>(
    initialFilters.sortBy || 'popularity'
  );

  // Expandable sections state
  const [expandedSections, setExpandedSections] = useState({
    price: true,
    brands: true,
    stock: true,
    ratings: true,
    categories: false,
    sort: true
  });

  // Mock data for filters (in real app, this would come from API or props)
  const availableBrands = ['Michelin', 'Bridgestone', 'Continental', 'Pirelli', 'Goodyear', 'Dunlop'];
  const stockStatuses = ['In Stock', 'Low Stock', 'Pre-order'];
  const ratingOptions = [5, 4, 3, 2, 1];
  const categoryOptions = ['Tyres', 'Brake Parts', 'Engine Parts', 'Suspension'];
  const sortOptions = [
    { value: 'popularity', label: 'Most Popular' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'newest', label: 'Newest First' }
  ];

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleBrandToggle = (brand: string) => {
    setSelectedBrands(prev =>
      prev.includes(brand)
        ? prev.filter(b => b !== brand)
        : [...prev, brand]
    );
  };

  const handleStockStatusToggle = (status: string) => {
    setSelectedStockStatus(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleRatingToggle = (rating: number) => {
    setSelectedRatings(prev =>
      prev.includes(rating)
        ? prev.filter(r => r !== rating)
        : [...prev, rating]
    );
  };

  const handleCategoryToggle = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handleApplyFilters = () => {
    const filters: ProductFilters = {
      priceRange,
      brands: selectedBrands,
      stockStatus: selectedStockStatus,
      ratings: selectedRatings,
      categories: selectedCategories,
      sortBy
    };
    onFiltersChange(filters);
    onOpenChange(false);
  };

  const handleClearAll = () => {
    setPriceRange([0, 10000]);
    setSelectedBrands([]);
    setSelectedStockStatus([]);
    setSelectedRatings([]);
    setSelectedCategories([]);
    setSortBy('popularity');

    const clearedFilters: ProductFilters = {
      priceRange: [0, 10000],
      brands: [],
      stockStatus: [],
      ratings: [],
      categories: [],
      sortBy: 'popularity'
    };
    onFiltersChange(clearedFilters);
  };

  // Count active filters
  const activeFilterCount =
    (priceRange[0] > 0 || priceRange[1] < 10000 ? 1 : 0) +
    selectedBrands.length +
    selectedStockStatus.length +
    selectedRatings.length +
    selectedCategories.length +
    (sortBy !== 'popularity' ? 1 : 0);

  const FilterSection = ({
    title,
    sectionKey,
    children
  }: {
    title: string;
    sectionKey: keyof typeof expandedSections;
    children: React.ReactNode;
  }) => (
    <div className="border-b border-gray-200 pb-4">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="flex items-center justify-between w-full py-2 text-left"
      >
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {expandedSections[sectionKey] ? (
          <ChevronUp className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        )}
      </button>
      {expandedSections[sectionKey] && (
        <div className="mt-3 space-y-3">
          {children}
        </div>
      )}
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto p-0 gap-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold flex items-center">
              <Filter className="h-5 w-5 mr-2 text-[#fa7b00]" />
              {t('marketplace.productFilters')}
            </DialogTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="bg-[#fa7b00] text-white">
                {activeFilterCount} {t('marketplace.filtersActive')}
              </Badge>
            )}
          </div>
        </DialogHeader>

        <div className="p-6 space-y-6">
          {/* Price Range */}
          <FilterSection title={t('marketplace.priceRange')} sectionKey="price">
            <div className="space-y-4">
              <Slider
                value={priceRange}
                onValueChange={setPriceRange}
                max={10000}
                min={0}
                step={100}
                className="w-full"
              />
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>{priceRange[0]} DZD</span>
                <span>{priceRange[1]} DZD</span>
              </div>
            </div>
          </FilterSection>

          {/* Brands */}
          <FilterSection title={t('marketplace.brands')} sectionKey="brands">
            <div className="grid grid-cols-2 gap-2">
              {availableBrands.map(brand => (
                <div key={brand} className="flex items-center space-x-2">
                  <Checkbox
                    id={`brand-${brand}`}
                    checked={selectedBrands.includes(brand)}
                    onCheckedChange={() => handleBrandToggle(brand)}
                  />
                  <label
                    htmlFor={`brand-${brand}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {brand}
                  </label>
                </div>
              ))}
            </div>
          </FilterSection>

          {/* Stock Status */}
          <FilterSection title={t('marketplace.stockStatus')} sectionKey="stock">
            <div className="space-y-2">
              {stockStatuses.map(status => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={`stock-${status}`}
                    checked={selectedStockStatus.includes(status)}
                    onCheckedChange={() => handleStockStatusToggle(status)}
                  />
                  <label
                    htmlFor={`stock-${status}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {status}
                  </label>
                </div>
              ))}
            </div>
          </FilterSection>

          {/* Sort By */}
          <FilterSection title={t('marketplace.sortBy')} sectionKey="sort">
            <div className="space-y-2">
              {sortOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id={`sort-${option.value}`}
                    name="sortBy"
                    value={option.value}
                    checked={sortBy === option.value}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="text-[#fa7b00] focus:ring-[#fa7b00]"
                  />
                  <label
                    htmlFor={`sort-${option.value}`}
                    className="text-sm font-medium leading-none"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
          </FilterSection>

          {/* View Mode Toggle */}
          {onViewModeChange && (
            <FilterSection title={t('marketplace.viewMode')} sectionKey="view">
              <div className="flex items-center border rounded-md">
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10",
                    viewMode === 'grid' ? 'bg-muted' : ''
                  )}
                  onClick={() => onViewModeChange('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Separator orientation="vertical" className="h-6" />
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10",
                    viewMode === 'list' ? 'bg-muted' : ''
                  )}
                  onClick={() => onViewModeChange('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </FilterSection>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50 flex justify-between items-center">
          <Button variant="outline" onClick={handleClearAll}>
            {t('actions.clearAll')}
          </Button>
          <Button
            onClick={handleApplyFilters}
            className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
          >
            {t('actions.applyFilters')} ({activeFilterCount})
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
