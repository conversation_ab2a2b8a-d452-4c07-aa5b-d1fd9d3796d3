import React, { useState, useRef, useEffect } from 'react';
import { ShoppingCart, ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useCart } from '@/contexts/CartContext';
import { ShoppingCartBox } from './ShoppingCartBox';
import { useClickAway } from 'react-use';
import { useIsMobile } from '@/hooks/use-mobile';

export function ShoppingCartButton() {
  const { t } = useTranslation();
  const { totalItems, totalPrice } = useCart();
  const [isCartOpen, setIsCartOpen] = useState(false);
  const cartRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Close cart when clicking outside
  useClickAway(cartRef, () => {
    if (isCartOpen) {
      setIsCartOpen(false);
    }
  });

  // Format currency
  const formattedPrice = new Intl.NumberFormat('fr-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(totalPrice);

  return (
    <div className="relative select-none" ref={cartRef}>
      {/* Desktop Cart Button */}
      <button
        className={cn(
          "hidden md:flex items-center gap-3 bg-[#071c44] hover:bg-[#071c44]/90 text-white rounded-full py-3 px-6 transition-all shadow-md",
          isCartOpen && "bg-[#071c44]/90"
        )}
        onClick={() => setIsCartOpen(!isCartOpen)}
        draggable="false"
      >
        <div className="relative">
          <ShoppingCart className="h-7 w-7" />
          {totalItems > 0 && (
            <span className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-[#fa7b00] text-white text-[10px] font-bold flex items-center justify-center">
              {totalItems}
            </span>
          )}
        </div>
        <div className="flex flex-col items-start">
          <span className="text-sm font-medium">{totalItems} {totalItems === 1 ? t('marketplace.item') : t('marketplace.items')}</span>
          <span className="text-base font-bold leading-tight">{formattedPrice}</span>
        </div>
        <ChevronDown className={cn(
          "h-5 w-5 transition-transform ml-1",
          isCartOpen && "transform rotate-180"
        )} />
      </button>

      {/* Mobile Cart Button */}
      <button
        className={cn(
          "md:hidden flex items-center justify-center bg-[#071c44] hover:bg-[#071c44]/90 text-white rounded-full h-12 w-12 transition-all shadow-md",
          isCartOpen && "bg-[#071c44]/90"
        )}
        onClick={() => setIsCartOpen(!isCartOpen)}
        draggable="false"
      >
        <div className="relative">
          <ShoppingCart className="h-7 w-7" />
          {totalItems > 0 && (
            <span className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-[#fa7b00] text-white text-[10px] font-bold flex items-center justify-center">
              {totalItems}
            </span>
          )}
        </div>
      </button>

      {/* Cart Dropdown */}
      {isCartOpen && (
        <ShoppingCartBox onClose={() => setIsCartOpen(false)} />
      )}
    </div>
  );
}
