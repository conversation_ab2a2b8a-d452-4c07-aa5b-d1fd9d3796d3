import React from 'react';
import { useTranslation } from 'react-i18next';
import { AlertTriangle, Building, Store, LogOut, ArrowRight, X } from 'lucide-react';
import { UserRole } from '@/contexts/UserContext';
import { signOut, ConflictType } from '@/services/authService';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

interface RoleConflictDialogProps {
  isOpen: boolean;
  onClose: () => void;
  role: UserRole;
  conflictingRole: UserRole;
  conflictType?: ConflictType;
  onContinue: () => void;
  userEmail?: string;
}

export function RoleConflictDialog({
  isOpen,
  onClose,
  role,
  conflictingRole,
  conflictType = 'session_conflict',
  onContinue,
  userEmail
}: RoleConflictDialogProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);

  const handleLogoutAndContinue = async () => {
    try {
      setIsLoggingOut(true);

      // Log out from the conflicting role
      const { success, error } = await signOut(conflictingRole);

      if (success) {
        toast({
          title: t('auth.roleLogoutSuccess'),
          description: t('auth.roleLogoutSuccessDescription'),
        });

        // Close the dialog and continue with the new login
        onClose();
        onContinue();
      } else {
        toast({
          title: t('auth.logoutFailed'),
          description: error?.message || t('auth.logoutFailedDescription'),
          variant: "destructive",
        });
        onClose();
      }
    } catch (error) {
      console.error('Error during role logout:', error);
      toast({
        title: t('auth.logoutFailed'),
        description: t('auth.unexpectedError'),
        variant: "destructive",
      });
      onClose();
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Get role-specific icons and names
  const getRoleIcon = (roleType: UserRole) => {
    if (roleType === 'supplier') {
      return <Building className="h-5 w-5 text-[#fa7b00]" />;
    } else if (roleType === 'merchant') {
      return <Store className="h-5 w-5 text-[#071c44]" />;
    }
    return null;
  };

  const getRoleName = (roleType: UserRole) => {
    if (roleType === 'supplier') {
      return t('roles.manufacturer');
    } else if (roleType === 'merchant') {
      return t('roles.merchant');
    }
    return '';
  };

  // Get the appropriate title and description based on conflict type
  const getDialogContent = () => {
    if (conflictType === 'email_role_mismatch') {
      return {
        title: t('auth.emailRoleMismatchTitle'),
        description: conflictingRole === 'supplier'
          ? t('auth.emailRoleMismatchSupplier')
          : t('auth.emailRoleMismatchMerchant'),
        showLogoutButton: false,
        showSwitchButton: true
      };
    } else {
      return {
        title: t('auth.sessionConflictTitle'),
        description: conflictingRole === 'supplier'
          ? t('auth.sessionConflictSupplierActive')
          : t('auth.sessionConflictMerchantActive'),
        showLogoutButton: true,
        showSwitchButton: false
      };
    }
  };

  const dialogContent = getDialogContent();

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-lg">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-3 text-xl">
            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-amber-100">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
            {dialogContent.title}
          </AlertDialogTitle>
          <AlertDialogDescription className="pt-4">
            <div className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                {dialogContent.description}
              </p>

              {userEmail && conflictType === 'email_role_mismatch' && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 text-blue-800">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="font-medium text-sm">Email: {userEmail}</span>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-center gap-3 p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
                <div className="flex items-center gap-2">
                  {getRoleIcon(conflictingRole)}
                  <span className="font-semibold text-gray-800">{getRoleName(conflictingRole)}</span>
                </div>
                <ArrowRight className="h-5 w-5 text-gray-400" />
                <div className="flex items-center gap-2">
                  {getRoleIcon(role)}
                  <span className="font-semibold text-gray-800">{getRoleName(role)}</span>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="mt-6 gap-3">
          <AlertDialogCancel
            disabled={isLoggingOut}
            className="flex-1"
          >
            <X className="h-4 w-4 mr-2" />
            {conflictType === 'email_role_mismatch' ? t('auth.stayLoggedIn') : t('actions.cancel')}
          </AlertDialogCancel>

          {dialogContent.showLogoutButton && (
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleLogoutAndContinue();
              }}
              disabled={isLoggingOut}
              className="flex-1 bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white gap-2"
            >
              {isLoggingOut ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {t('auth.loggingOut')}
                </>
              ) : (
                <>
                  <LogOut className="h-4 w-4" />
                  {t('auth.logoutFromCurrentRole')}
                </>
              )}
            </AlertDialogAction>
          )}

          {dialogContent.showSwitchButton && (
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                onClose();
                // Trigger navigation to the correct login section
                const targetSection = conflictingRole === 'supplier' ? 'supplier' : 'merchant';
                const targetElement = document.querySelector(`[data-role="${targetSection}"]`);
                if (targetElement) {
                  targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
              }}
              className="flex-1 bg-[#071c44] hover:bg-[#071c44]/90 text-white gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              {t('auth.switchToCorrectLogin')}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
