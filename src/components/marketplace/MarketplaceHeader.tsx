import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { User, Menu, Car, Heart, HelpCircle, Search, X, Globe, Building, Store, LogOut } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslation } from 'react-i18next';
import { AirbnbStyleSearchBar } from './AirbnbStyleSearchBar';
import { AirbnbStyleFilterModal } from './AirbnbStyleFilterModal';
import { ShoppingCartButton } from './ShoppingCartButton';
import { EnhancedDeliveryLocationButton } from '../location/EnhancedDeliveryLocationButton';
import { UserProfileButton } from './UserProfileButton';
import { useIsMobile } from '@/hooks/use-mobile';
import { MobileBottomNavigation } from './MobileBottomNavigation';
import { EnhancedMobileDeliveryLocation } from '../location/EnhancedMobileDeliveryLocation';
import { TyreIcon } from './icons/TyreIcon';
import { useFilter } from '@/contexts/FilterContext';
import { useToast } from '@/hooks/use-toast';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";

export function MarketplaceHeader() {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const isMobile = useIsMobile();
  const { toast } = useToast();

  const [searchText, setSearchText] = useState('');
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);

  // Use the filter context instead of local state
  const {
    activeSection,
    setActiveSection,
    tyresFilters,
    setTyresFilters,
    partsFilters,
    setPartsFilters,
    tyresFilterCount,
    partsFilterCount,
    showTyresModal,
    setShowTyresModal,
    showPartsModal,
    setShowPartsModal
  } = useFilter();

  // Treat the root path (/) as the MyVehicleParts page
  const isVehiclePartsPage = location.pathname === '/my-vehicle-parts' || location.pathname === '/';
  const isHomePage = location.pathname === '/';
  const showSearchSection = isVehiclePartsPage;

  const toggleSection = (section: 'tyres' | 'parts') => {
    setActiveSection(section);
  };

  // Open tyre filter modal with coming soon message
  const handleOpenTyresModal = () => {
    toast({
      title: t('marketplace.comingSoon'),
      description: t('marketplace.underDevelopment'),
      duration: 3000,
    });
    // Keep the original functionality for now
    // setShowTyresModal(true);
  };

  // Open parts filter modal with coming soon message
  const handleOpenPartsModal = () => {
    toast({
      title: t('marketplace.comingSoon'),
      description: t('marketplace.underDevelopment'),
      duration: 3000,
    });
    // Keep the original functionality for now
    // setShowPartsModal(true);
  };

  // Handle search submission
  const handleSearch = () => {
    // Search across all products with both filters applied
    console.log('Search params:', {
      ...tyresFilters,
      ...partsFilters,
      searchText
    });

    // In a real implementation, this would navigate to search results page
    // or trigger an API call to fetch search results
    // For now, we'll just log the search parameters
  };

  // Handle form submission on Enter key
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  // Check consumer authentication
  useEffect(() => {
    const checkConsumerAuth = () => {
      try {
        const phoneSession = localStorage.getItem('phone_auth_session');
        if (phoneSession) {
          const session = JSON.parse(phoneSession);
          const isConsumer = session.profile?.role === 'consumer';
          setIsConsumerAuthenticated(!!isConsumer);
        } else {
          setIsConsumerAuthenticated(false);
        }
      } catch (error) {
        console.error('Error checking consumer auth:', error);
        setIsConsumerAuthenticated(false);
      }
    };

    checkConsumerAuth();

    // Listen for auth changes
    const handleAuthChange = () => checkConsumerAuth();
    window.addEventListener('auth:success', handleAuthChange);
    window.addEventListener('storage', handleAuthChange);

    return () => {
      window.removeEventListener('auth:success', handleAuthChange);
      window.removeEventListener('storage', handleAuthChange);
    };
  }, []);

  // Get consumer info from session
  const getConsumerInfo = () => {
    try {
      const phoneSession = localStorage.getItem('phone_auth_session');
      if (phoneSession) {
        const session = JSON.parse(phoneSession);
        return {
          name: session.profile?.full_name || 'Consumer',
          phone: session.profile?.phone || session.user?.phone || '',
          avatar: session.profile?.avatar_url
        };
      }
    } catch (error) {
      console.error('Error getting consumer info:', error);
    }
    return { name: 'Consumer', phone: '', avatar: null };
  };

  // Handle consumer logout
  const handleConsumerLogout = () => {
    try {
      localStorage.removeItem('phone_auth_session');
      setIsConsumerAuthenticated(false);

      // Dispatch auth change event
      window.dispatchEvent(new Event('auth:logout'));

      // Close the sheet
      const closeButton = document.querySelector('[data-radix-collection-item]');
      if (closeButton && 'click' in closeButton) {
        (closeButton as HTMLElement).click();
      }
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Debug log
  console.log('Render state:', { isMobile, isConsumerAuthenticated });

  return (
    <header className="sticky top-0 z-50 w-full shadow-md select-none">
      {/* Top Navigation Bar */}
      <div className="bg-[#fed7a1]">
        <div className="container mx-auto flex items-center justify-center h-8">
          <nav className="flex items-center justify-center w-full">
            <div className="flex items-center justify-center space-x-3">
              <Link
                to="/my-vehicle-parts"
                className={cn(
                  "font-medium text-[10px] md:text-xs text-[#fa7b00] hover:bg-[#fa7b00]/10 px-2 md:px-3 py-1 transition-colors uppercase tracking-wide rounded-sm whitespace-nowrap",
                  (location.pathname === '/my-vehicle-parts' || location.pathname === '/') ? "bg-[#fa7b00] text-white hover:bg-[#fa7b00]/90" : ""
                )}
              >
                {t('marketplace.myVehicleParts')}
              </Link>
              <Link
                to="/wholesale-offers"
                className={cn(
                  "font-medium text-[10px] md:text-xs text-[#fa7b00] hover:bg-[#fa7b00]/10 px-2 md:px-3 py-1 transition-colors uppercase tracking-wide rounded-sm whitespace-nowrap",
                  location.pathname === '/wholesale-offers' ? "bg-[#fa7b00] text-white hover:bg-[#fa7b00]/90" : ""
                )}
              >
                {t('marketplace.wholesaleOffers')}
              </Link>
              <Link
                to="/partners"
                className={cn(
                  "font-medium text-[10px] md:text-xs text-[#fa7b00] hover:bg-[#fa7b00]/10 px-2 md:px-3 py-1 transition-colors uppercase tracking-wide rounded-sm whitespace-nowrap",
                  location.pathname === '/partners' ? "bg-[#fa7b00] text-white hover:bg-[#fa7b00]/90" : ""
                )}
              >
                {t('marketplace.partners')}
              </Link>
            </div>
          </nav>
        </div>
      </div>

      {/* Main Header Section */}
      <div className="bg-[#fa7b00]">
        <div className="container mx-auto py-1 md:py-4">
          <div className="flex items-center justify-between">
            {/* Logo - Both Mobile and Desktop */}
            <Link
              to="/"
              className="flex items-center select-none flex-shrink-0"
              draggable="false"
            >
              <div
                className="relative overflow-visible flex items-center"
                style={{
                  pointerEvents: 'none',
                  userSelect: 'none',
                  marginLeft: '-0.25rem'
                }}
              >
                <div className="relative" style={{ height: '3rem', width: '5rem', overflow: 'visible' }}>
                  <img
                    src="/images/logohome.png"
                    alt="AROUZ MARKET Logo"
                    className="w-auto h-12 md:h-20 object-contain absolute top-1/2 left-0 transform -translate-y-1/2"
                    style={{
                      filter: 'drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.12))',
                      transform: 'translateY(-50%) scale(1.35)',
                      transformOrigin: 'left center',
                      maxHeight: '140%'
                  }}
                  draggable="false"
                  onDragStart={(e) => e.preventDefault()}
                />
                </div>
              </div>
            </Link>

            {/* Desktop Search Bar and User Actions */}
            <div className="hidden md:flex items-center justify-end flex-1 gap-3 ml-8">
              {/* Search Bar */}
              <div className="flex-1 max-w-3xl mx-auto">
                <AirbnbStyleSearchBar
                  onSearch={handleSearch}
                />
              </div>

              {/* Enhanced Delivery Location Button */}
              <div className="flex-shrink-0 ml-2">
                <EnhancedDeliveryLocationButton />
              </div>

              {/* Shopping Cart Button */}
              <div className="ml-2 flex-shrink-0">
                <ShoppingCartButton />
              </div>

              {/* User Profile Button */}
              <div className="ml-2 flex-shrink-0">
                <UserProfileButton />
              </div>
            </div>

            {/* Mobile Search Input and Menu */}
            <div className="flex md:hidden items-center flex-1 ml-2">
              {/* Mobile Search Input */}
              <div className="relative flex-1 mx-2">
                <form
                  className="flex items-center"
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleSearch();
                  }}
                >
                  <div className="relative flex-1">
                    <input
                      type="text"
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={t('marketplace.searchAllProducts')}
                      className="w-full pl-9 pr-12 py-2.5 text-sm bg-white border border-gray-200 text-gray-700 rounded-full focus:outline-none focus:ring-1 focus:ring-[#fa7b00] focus:border-[#fa7b00]"
                      aria-label={t('marketplace.searchAllProducts')}
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />

                    {/* Search Button (inside input) */}
                    <button
                      type="submit"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 flex items-center justify-center bg-[#fa7b00] text-white rounded-full hover:bg-[#fa7b00]/90 focus:outline-none focus:ring-1 focus:ring-[#fa7b00]"
                      aria-label={t('marketplace.search')}
                    >
                      <Search className="h-4 w-4" />
                      <span className="sr-only">{t('marketplace.search')}</span>
                    </button>
                  </div>
                </form>
              </div>

              {/* Mobile Menu Button */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-white hover:text-white/90 h-9 w-9 rounded-full
                      bg-white/15 hover:bg-white/25 border border-white/30 hover:border-white/50
                      shadow-sm transition-all duration-200
                      backdrop-blur-[2px] flex-shrink-0"
                  >
                    <Menu className="h-5 w-5 drop-shadow-sm" />
                    <span className="sr-only">Menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                  <div className="flex flex-col gap-6 mt-8">
                    <h3 className="text-lg font-bold text-[#fa7b00]">Menu</h3>
                    <nav className="flex flex-col gap-4">
                      <Link
                        to="/my-vehicle-parts"
                        className={cn(
                          "text-lg font-medium transition-colors hover:text-[#fa7b00] py-2",
                          (location.pathname === '/my-vehicle-parts' || location.pathname === '/') ? "text-[#fa7b00]" : ""
                        )}
                      >
                        {t('marketplace.myVehicleParts')}
                      </Link>
                      <Link
                        to="/wholesale-offers"
                        className={cn(
                          "text-lg font-medium transition-colors hover:text-[#fa7b00] py-2",
                          location.pathname === '/wholesale-offers' ? "text-[#fa7b00]" : ""
                        )}
                      >
                        {t('marketplace.wholesaleOffers')}
                      </Link>
                      <Link
                        to="/partners"
                        className={cn(
                          "text-lg font-medium transition-colors hover:text-[#fa7b00] py-2",
                          location.pathname === '/partners' ? "text-[#fa7b00]" : ""
                        )}
                      >
                        {t('marketplace.partners')}
                      </Link>
                      <div className="border-t border-gray-200 my-2"></div>

                      {/* Consumer Authentication Section */}
                      {isConsumerAuthenticated ? (
                        // Authenticated Consumer Menu
                        <div className="space-y-2">
                          <div className="flex items-center gap-3 py-2">
                            <div className="w-10 h-10 bg-[#fa7b00] rounded-full flex items-center justify-center text-white font-bold">
                              {getConsumerInfo().name.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">
                                {getConsumerInfo().name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {getConsumerInfo().phone}
                              </p>
                            </div>
                          </div>

                          <Link
                            to="/my-reviews"
                            className="text-lg font-medium transition-colors hover:text-[#fa7b00] py-2 flex items-center gap-2 w-full text-left"
                            onClick={() => {
                              // Close the sheet
                              const closeButton = document.querySelector('[data-radix-collection-item]');
                              if (closeButton && 'click' in closeButton) {
                                (closeButton as HTMLElement).click();
                              }
                            }}
                          >
                            <User className="h-5 w-5" />
                            {t('marketplace.myReviews')}
                          </Link>

                          <button
                            onClick={handleConsumerLogout}
                            className="text-lg font-medium transition-colors hover:text-red-600 py-2 flex items-center gap-2 w-full text-left text-red-500"
                          >
                            <LogOut className="h-5 w-5" />
                            {t('auth.logout')}
                          </button>
                        </div>
                      ) : (
                        // Unauthenticated Menu
                        <button
                          onClick={() => {
                            // Close the sheet and open the auth modal
                            const closeButton = document.querySelector('[data-radix-collection-item]');
                            if (closeButton && 'click' in closeButton) {
                              (closeButton as HTMLElement).click();
                              // Small delay to ensure sheet is closed before opening modal
                              setTimeout(() => {
                                const authModal = document.getElementById('auth-modal-trigger');
                                if (authModal) {
                                  authModal.click();
                                }
                              }, 300);
                            }
                          }}
                          className="text-lg font-medium transition-colors hover:text-[#fa7b00] py-2 flex items-center gap-2 w-full text-left"
                        >
                          <User className="h-5 w-5" />
                          {t('auth.loginOrSignUp')}
                        </button>
                      )}

                      <div className="border-t border-gray-200 my-2"></div>
                      <p className="text-sm text-gray-500 px-2 py-1">{t('auth.sellWithUs')}</p>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Link
                              to="/partners"
                              onClick={() => {
                                // Close the sheet
                                const closeButton = document.querySelector('[data-radix-collection-item]');
                                if (closeButton && 'click' in closeButton) {
                                  (closeButton as HTMLElement).click();
                                }
                              }}
                              className="text-lg font-medium bg-[#fa7b00] text-white hover:bg-[#fa7b00]/90 py-2 px-3 flex items-center gap-2 w-full text-left rounded-md my-2"
                            >
                              <Building className="h-5 w-5" />
                              {t('auth.becomeSupplier')}
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent side="left" className="max-w-[200px]">
                            {t('auth.supplierTooltip')}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Link
                              to="/partners"
                              onClick={() => {
                                // Close the sheet
                                const closeButton = document.querySelector('[data-radix-collection-item]');
                                if (closeButton && 'click' in closeButton) {
                                  (closeButton as HTMLElement).click();
                                }
                              }}
                              className="text-lg font-medium bg-[#fa7b00]/90 text-white hover:bg-[#fa7b00] py-2 px-3 flex items-center gap-2 w-full text-left rounded-md mb-2"
                            >
                              <Store className="h-5 w-5" />
                              {t('auth.joinAsMerchant')}
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent side="left" className="max-w-[200px]">
                            {t('auth.merchantTooltip')}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <Link
                        to="/wishlist"
                        className="text-lg font-medium transition-colors hover:text-[#fa7b00] py-2 flex items-center gap-2"
                      >
                        <Heart className="h-5 w-5" />
                        {t('marketplace.myWishlist')}
                      </Link>
                      <Link
                        to="/help"
                        className="text-lg font-medium transition-colors hover:text-[#fa7b00] py-2 flex items-center gap-2"
                      >
                        <HelpCircle className="h-5 w-5" />
                        {t('marketplace.helpCenter')}
                      </Link>
                      <div className="py-2">
                        <div className="text-lg font-medium text-gray-900 mb-2 flex items-center gap-2">
                          <Globe className="h-5 w-5" />
                          {t('marketplace.languages')}
                        </div>
                        <div className="flex flex-col gap-1 ml-7">
                          <button
                            className={cn(
                              "text-sm font-medium transition-colors hover:text-[#fa7b00] py-1 text-left",
                              i18n.language === 'ar' ? 'text-[#fa7b00]' : 'text-gray-600'
                            )}
                            onClick={() => {
                              i18n.changeLanguage('ar');
                              localStorage.setItem('i18nextLng', 'ar');
                            }}
                          >
                            🇩🇿 {t('language.arabic')}
                          </button>
                          <button
                            className={cn(
                              "text-sm font-medium transition-colors hover:text-[#fa7b00] py-1 text-left",
                              i18n.language === 'en' ? 'text-[#fa7b00]' : 'text-gray-600'
                            )}
                            onClick={() => {
                              i18n.changeLanguage('en');
                              localStorage.setItem('i18nextLng', 'en');
                            }}
                          >
                            🇺🇸 {t('language.english')}
                          </button>
                          <button
                            className={cn(
                              "text-sm font-medium transition-colors hover:text-[#fa7b00] py-1 text-left",
                              i18n.language === 'fr' ? 'text-[#fa7b00]' : 'text-gray-600'
                            )}
                            onClick={() => {
                              i18n.changeLanguage('fr');
                              localStorage.setItem('i18nextLng', 'fr');
                            }}
                          >
                            🇫🇷 {t('language.french')}
                          </button>
                        </div>
                      </div>
                    </nav>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>

        </div>
      </div>

      {/* Enhanced Mobile Delivery Location Button - Outside the orange header */}
      {isMobile && (
        <EnhancedMobileDeliveryLocation />
      )}

      {/* We've removed the mobile expanded search interface as it's no longer needed */}

      {/* Filter Modals - Centralized here for both desktop and mobile */}
      <AirbnbStyleFilterModal
        open={showTyresModal}
        onOpenChange={setShowTyresModal}
        type="tyres"
        onSubmit={(filters) => {
          setTyresFilters(filters);
          console.log('Tyres filters updated:', filters);
        }}
        initialFilters={tyresFilters}
      />

      <AirbnbStyleFilterModal
        open={showPartsModal}
        onOpenChange={setShowPartsModal}
        type="parts"
        onSubmit={(filters) => {
          setPartsFilters(filters);
          console.log('Parts filters updated:', filters);
        }}
        initialFilters={partsFilters}
      />

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <MobileBottomNavigation />
      )}
    </header>
  );
}
