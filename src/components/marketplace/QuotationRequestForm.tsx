import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useTranslation } from 'react-i18next';
import { X, FileText, Send, Car, Truck } from 'lucide-react';
import { useFilter } from '@/contexts/FilterContext';
import { toast } from 'sonner';

interface QuotationRequestFormProps {
  subcategory: string;
  section: 'retail' | 'wholesale';
}

export function QuotationRequestForm({ subcategory, section }: QuotationRequestFormProps) {
  const { t } = useTranslation();
  const { 
    setShowQuotationRequest,
    tyresFilters,
    partsFilters,
    activeSection
  } = useFilter();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    quantity: '1',
    details: '',
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // In a real app, this would send the data to an API
    setTimeout(() => {
      setIsSubmitting(false);
      toast.success(t('marketplace.quotationRequestSent'));
      setShowQuotationRequest(false);
    }, 1500);
  };
  
  // Get vehicle or tyre details from filters
  const getFilterDetails = () => {
    if (activeSection === 'tyres') {
      if (tyresFilters.width || tyresFilters.aspectRatio || tyresFilters.rimDiameter) {
        return `${tyresFilters.width}/${tyresFilters.aspectRatio}R${tyresFilters.rimDiameter} ${tyresFilters.season}`;
      }
      return '';
    } else {
      if (partsFilters.brand || partsFilters.model || partsFilters.engine) {
        return `${partsFilters.brand} ${partsFilters.model} ${partsFilters.engine}`;
      }
      return '';
    }
  };
  
  const filterDetails = getFilterDetails();

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden">
      <div className="bg-[#071c44] text-white p-4 flex justify-between items-center">
        <div className="flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          <h3 className="font-medium">
            {t('marketplace.requestQuotation')} - {subcategory}
          </h3>
        </div>
        <Button 
          variant="ghost" 
          size="icon" 
          className="text-white hover:bg-[#071c44]/80"
          onClick={() => setShowQuotationRequest(false)}
        >
          <X className="h-5 w-5" />
        </Button>
      </div>
      
      <form onSubmit={handleSubmit} className="p-4">
        <div className="bg-[#ffd9a3]/30 rounded-lg p-4 mb-4">
          <div className="flex items-start gap-3">
            {activeSection === 'tyres' ? (
              <Truck className="h-5 w-5 text-[#fa7b00] mt-0.5" />
            ) : (
              <Car className="h-5 w-5 text-[#fa7b00] mt-0.5" />
            )}
            <div>
              <p className="font-medium text-sm">
                {activeSection === 'tyres' 
                  ? t('marketplace.tyreSpecifications') 
                  : t('marketplace.vehicleDetails')}
              </p>
              <p className="text-sm text-muted-foreground">
                {filterDetails || t('marketplace.noDetailsSelected')}
              </p>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('marketplace.fullName')} *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">{t('marketplace.email')} *</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">{t('marketplace.phone')}</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="+213"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quantity">{t('marketplace.quantity')} *</Label>
              <Input
                id="quantity"
                name="quantity"
                type="number"
                min="1"
                value={formData.quantity}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="details">{t('marketplace.additionalDetails')}</Label>
            <Textarea
              id="details"
              name="details"
              value={formData.details}
              onChange={handleChange}
              placeholder={t('marketplace.detailsPlaceholder')}
              rows={4}
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('marketplace.submitting')}
              </span>
            ) : (
              <span className="flex items-center">
                <Send className="h-4 w-4 mr-2" />
                {t('marketplace.sendRequest')}
              </span>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
