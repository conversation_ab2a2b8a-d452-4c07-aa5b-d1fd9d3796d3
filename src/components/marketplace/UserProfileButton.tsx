import React, { useState, useEffect, useMemo } from 'react';
import { User, Menu, Heart, Globe, HelpCircle, Building, Store, LogOut, MessageSquare, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuGroup,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AuthModal } from './AuthModal';
import { RoleAuthModal, UserRole } from './RoleAuthModal';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { getWishlistCount } from '@/services/wishlistService';

export function UserProfileButton() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, profile, isAuthenticated, isLoading } = useAuth();

  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRoleAuthModalOpen, setIsRoleAuthModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>('consumer');
  const [authMode, setAuthMode] = useState<'signup' | 'login'>('signup');
  const [wishlistCount, setWishlistCount] = useState(0);
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const [isConsumerLoading, setIsConsumerLoading] = useState(true);

  // Check for consumer authentication (phone-based)
  useEffect(() => {
    const checkConsumerAuth = () => {
      setIsConsumerLoading(true);
      try {
        const phoneSession = localStorage.getItem('phone_auth_session');
        if (phoneSession) {
          const session = JSON.parse(phoneSession);
          const isConsumer = session.profile?.role === 'consumer';
          setIsConsumerAuthenticated(isConsumer);

          if (isConsumer) {
            // Load wishlist count for authenticated consumer
            loadWishlistCount();
          }
        } else {
          setIsConsumerAuthenticated(false);
          setWishlistCount(0);
        }
      } catch (error) {
        console.error('Error checking consumer auth:', error);
        setIsConsumerAuthenticated(false);
      } finally {
        setIsConsumerLoading(false);
      }
    };

    checkConsumerAuth();

    // Listen for auth changes and wishlist updates
    const handleAuthChange = () => checkConsumerAuth();
    const handleWishlistUpdate = () => {
      if (isConsumerAuthenticated) {
        loadWishlistCount();
      }
    };

    window.addEventListener('auth:success', handleAuthChange);
    window.addEventListener('storage', handleAuthChange);
    window.addEventListener('wishlist:updated', handleWishlistUpdate);

    return () => {
      window.removeEventListener('auth:success', handleAuthChange);
      window.removeEventListener('storage', handleAuthChange);
      window.removeEventListener('wishlist:updated', handleWishlistUpdate);
    };
  }, [user, profile]);

  // Load wishlist count
  const loadWishlistCount = async () => {
    try {
      const result = await getWishlistCount();
      if (result.success) {
        setWishlistCount(result.count || 0);
      }
    } catch (error) {
      console.error('Error loading wishlist count:', error);
    }
  };

  const openRoleAuthModal = (role: UserRole, mode?: 'signup' | 'login') => {
    setSelectedRole(role);
    setAuthMode(mode || 'signup');
    setIsRoleAuthModalOpen(true);
  };

  // CRITICAL FIX: Handle consumer logout using proper service function
  const handleConsumerLogout = () => {
    try {
      // Use the proper consumer logout function to ensure cache clearing
      const { logoutConsumer } = require('@/services/simplifiedConsumerAuth');
      logoutConsumer();

      setIsConsumerAuthenticated(false);
      setWishlistCount(0);
      toast.success(t('auth.loggedOut'), {
        description: t('auth.loggedOutDescription')
      });
      // Refresh the page to reset all state
      window.location.reload();
    } catch (error) {
      console.error('Error during consumer logout:', error);
    }
  };

  // Memoize consumer info to prevent flickering
  const consumerInfo = useMemo(() => {
    try {
      const phoneSession = localStorage.getItem('phone_auth_session');
      if (phoneSession) {
        const session = JSON.parse(phoneSession);
        return {
          name: session.profile?.full_name || 'Consumer',
          phone: session.profile?.phone || session.user?.phone || '',
          avatar: session.profile?.avatar_url
        };
      }
    } catch (error) {
      console.error('Error getting consumer info:', error);
    }
    return { name: 'Consumer', phone: '', avatar: null };
  }, [isConsumerAuthenticated]); // Only recalculate when auth status changes

  // Listen for custom events from mobile menu
  useEffect(() => {
    const handleRoleAuthEvent = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.role) {
        const role = customEvent.detail.role as UserRole;
        const mode = customEvent.detail.mode as 'signup' | 'login' | undefined;
        openRoleAuthModal(role, mode);
      }
    };

    document.addEventListener('open-role-auth-modal', handleRoleAuthEvent);

    return () => {
      document.removeEventListener('open-role-auth-modal', handleRoleAuthEvent);
    };
  }, []);

  return (
    <>
      {/* Hidden button for mobile menu to trigger auth modal */}
      <button
        id="auth-modal-trigger"
        className="hidden"
        onClick={() => setIsAuthModalOpen(true)}
      />

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="relative text-white hover:text-white/90 h-12 rounded-full px-3 flex-shrink-0 gap-2
              bg-white/15 hover:bg-white/25 border-2 border-white/40 hover:border-white/60
              shadow-sm transition-all duration-200
              backdrop-blur-[2px]"
          >
            {isConsumerLoading ? (
              <>
                <div className="h-6 w-6 rounded-full bg-white/20 animate-pulse" />
                <span className="hidden md:block text-sm font-medium drop-shadow-sm">
                  Loading...
                </span>
              </>
            ) : isConsumerAuthenticated ? (
              <>
                <Avatar className="h-6 w-6 border border-white/40">
                  <AvatarImage src={consumerInfo.avatar || ""} alt="Avatar" />
                  <AvatarFallback className="bg-[#fa7b00] text-white text-xs font-bold">
                    {consumerInfo.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="hidden md:block text-sm font-medium drop-shadow-sm">
                  {consumerInfo.name}
                </span>
              </>
            ) : (
              <>
                <Menu className="h-5 w-5 drop-shadow-sm" />
                <User className="h-5 w-5 drop-shadow-sm" />
              </>
            )}
            <span className="sr-only">{t('marketplace.account')}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64 mt-2 p-2">
          {isConsumerAuthenticated ? (
            // Authenticated Consumer Menu
            <>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {consumerInfo.name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {consumerInfo.phone}
                  </p>
                </div>
              </DropdownMenuLabel>

              <DropdownMenuSeparator />

              <DropdownMenuItem
                className="cursor-pointer py-2 px-3"
                onClick={() => navigate('/wishlist')}
              >
                <Heart className="mr-2 h-4 w-4" />
                {t('marketplace.myWishlist')}
                {wishlistCount > 0 && (
                  <span className="ml-auto bg-[#fa7b00] text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                    {wishlistCount}
                  </span>
                )}
              </DropdownMenuItem>

              <DropdownMenuItem
                className="cursor-pointer py-2 px-3"
                onClick={() => navigate('/my-reviews')}
              >
                <Star className="mr-2 h-4 w-4" />
                {t('marketplace.myReviews')}
              </DropdownMenuItem>

              <DropdownMenuItem className="cursor-pointer py-2 px-3">
                <MessageSquare className="mr-2 h-4 w-4" />
                {t('marketplace.messages')}
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem className="cursor-pointer py-2 px-3">
                <Globe className="mr-2 h-4 w-4" />
                {t('marketplace.languages')}
              </DropdownMenuItem>

              <DropdownMenuItem className="cursor-pointer py-2 px-3">
                <HelpCircle className="mr-2 h-4 w-4" />
                {t('marketplace.helpCenter')}
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem
                className="cursor-pointer py-2 px-3 text-destructive hover:text-destructive hover:bg-destructive/10"
                onClick={handleConsumerLogout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                {t('auth.logout')}
              </DropdownMenuItem>
            </>
          ) : (
            // Unauthenticated Menu
            <>
              <DropdownMenuItem
                className="cursor-pointer py-3 px-3 font-medium"
                onClick={() => setIsAuthModalOpen(true)}
              >
                {t('auth.loginOrSignUp')}
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuLabel className="text-xs text-gray-500 px-3 py-2">
                {t('auth.sellWithUs')}
              </DropdownMenuLabel>

              <DropdownMenuGroup>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuItem
                        className="cursor-pointer py-2 px-3 bg-[#fa7b00] text-white hover:bg-[#fa7b00]/90 hover:text-white mb-1 rounded-md"
                        asChild
                      >
                        <Link to="/partners">
                          <Building className="mr-2 h-4 w-4" />
                          {t('auth.becomeSupplier')}
                        </Link>
                      </DropdownMenuItem>
                    </TooltipTrigger>
                    <TooltipContent side="left" className="max-w-[200px]">
                      {t('auth.supplierTooltip')}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuItem
                        className="cursor-pointer py-2 px-3 bg-[#fa7b00]/90 text-white hover:bg-[#fa7b00] hover:text-white rounded-md"
                        asChild
                      >
                        <Link to="/partners">
                          <Store className="mr-2 h-4 w-4" />
                          {t('auth.joinAsMerchant')}
                        </Link>
                      </DropdownMenuItem>
                    </TooltipTrigger>
                    <TooltipContent side="left" className="max-w-[200px]">
                      {t('auth.merchantTooltip')}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </DropdownMenuGroup>

              <DropdownMenuSeparator className="my-2" />

              <DropdownMenuItem className="cursor-pointer py-2 px-3">
                <Heart className="mr-2 h-4 w-4" />
                {t('marketplace.myWishlist')}
              </DropdownMenuItem>

              <DropdownMenuItem className="cursor-pointer py-2 px-3">
                <Globe className="mr-2 h-4 w-4" />
                {t('marketplace.languages')}
              </DropdownMenuItem>

              <DropdownMenuItem className="cursor-pointer py-2 px-3">
                <HelpCircle className="mr-2 h-4 w-4" />
                {t('marketplace.helpCenter')}
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Consumer Authentication Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      {/* Hidden trigger for auth modal (used by wishlist functionality) */}
      <button
        id="auth-modal-trigger"
        className="hidden"
        onClick={() => setIsAuthModalOpen(true)}
      />

      {/* Role-specific Authentication Modal */}
      <RoleAuthModal
        isOpen={isRoleAuthModalOpen}
        onClose={() => setIsRoleAuthModalOpen(false)}
        role={selectedRole}
        initialMode={authMode}
      />
    </>
  );
}
