import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ShoppingCart, X, Trash, Plus, Minus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useCart } from '@/contexts/CartContext';

interface ShoppingCartBoxProps {
  onClose: () => void;
}

export function ShoppingCartBox({ onClose }: ShoppingCartBoxProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    items: cartItems,
    totalItems,
    totalPrice: subtotal,
    removeItem,
    updateQuantity,
    getItemPrice,
    getItemTier,
    getItemTotal
  } = useCart();

  // Handle product name click - navigate to product page and close cart
  const handleProductClick = (productId: string) => {
    onClose(); // Close the cart dropdown first
    navigate(`/${productId}`); // Navigate to product page
  };

  return (
    <div className="w-full md:absolute md:top-full md:right-0 md:mt-3 md:w-96 bg-white rounded-lg md:shadow-xl md:border md:border-gray-200 md:z-50 md:transform md:translate-x-0">
      <div className="p-5 flex items-center justify-between border-b border-gray-200 bg-[#071c44]/5">
        <h3 className="font-medium flex items-center text-[#071c44]">
          <ShoppingCart className="h-5 w-5 mr-2 text-[#fa7b00]" />
          {t('marketplace.cart')}
          <span className="ml-2 text-sm text-[#071c44]/70">
            ({totalItems} {totalItems === 1 ? t('marketplace.item') : t('marketplace.items')})
          </span>
        </h3>
        <Button variant="ghost" size="icon" onClick={onClose} className="hover:bg-[#071c44]/10 text-[#071c44]">
          <X className="h-5 w-5" />
          <span className="sr-only">{t('marketplace.close')}</span>
        </Button>
      </div>

      {cartItems.length > 0 ? (
        <>
          <div className="px-5">
            <ScrollArea className="h-[240px]">
              <div className="space-y-5 pr-4">
                {cartItems.map((item, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="h-20 w-20 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                      <img
                        src={item.image || '/placeholder.svg'}
                        alt={item.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      {/* Clickable product name */}
                      <button
                        onClick={() => handleProductClick(item.originalProductId)}
                        className="text-base font-medium truncate text-gray-800 hover:text-[#fa7b00] transition-colors duration-200 cursor-pointer text-left w-full"
                        title="View product details"
                      >
                        {item.name}
                      </button>

                      {/* Show tier information for wholesale items */}
                      {item.marketplaceSection === 'wholesale' && getItemTier(item) && (
                        <div className="text-xs text-orange-600 mt-1">
                          Tier {getItemTier(item)?.minQuantity}+: {getItemPrice(item).toFixed(2)} DZD/piece
                        </div>
                      )}

                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6 rounded-full p-0"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                            <span className="sr-only">Decrease</span>
                          </Button>
                          <span className="text-sm font-medium">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-6 w-6 rounded-full p-0"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                            <span className="sr-only">Increase</span>
                          </Button>
                        </div>
                        <div className="text-right">
                          {/* Show unit price for wholesale items */}
                          {item.marketplaceSection === 'wholesale' && (
                            <div className="text-xs text-gray-500">
                              {getItemPrice(item).toFixed(2)} DZD each
                            </div>
                          )}
                          <div className="font-bold text-base text-gray-900">
                            {new Intl.NumberFormat('fr-DZ', {
                              style: 'currency',
                              currency: 'DZD',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0
                            }).format(getItemTotal(item))}
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 hover:bg-gray-100"
                      onClick={() => removeItem(item.id)}
                    >
                      <Trash className="h-4 w-4 text-gray-500" />
                      <span className="sr-only">{t('marketplace.removeItem')}</span>
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          <div className="p-5 border-t border-gray-200 bg-[#071c44]/5">
            <div className="flex items-center justify-between mb-5">
              <span className="font-medium text-[#071c44]">{t('marketplace.subtotal')}</span>
              <span className="font-bold text-lg text-[#071c44]">
                {new Intl.NumberFormat('fr-DZ', {
                  style: 'currency',
                  currency: 'DZD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                }).format(subtotal)}
              </span>
            </div>
            <Button
              onClick={() => {
                onClose(); // Close cart dropdown
                navigate('/basket'); // Navigate to checkout
              }}
              className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white py-6 text-base font-medium shadow-md"
            >
              {t('marketplace.checkout')}
            </Button>
          </div>
        </>
      ) : (
        <div className="py-10 px-6 flex flex-col items-center justify-center text-center">
          <div className="h-20 w-20 rounded-full bg-[#071c44]/10 flex items-center justify-center mb-5">
            <ShoppingCart className="h-10 w-10 text-[#071c44]/60" />
          </div>
          <h4 className="font-medium text-lg text-[#071c44] mb-2">{t('marketplace.cartEmpty')}</h4>
          <p className="text-base text-[#071c44]/70 mb-6">
            {t('marketplace.addItemsToCart')}
          </p>
          <Button
            className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white py-6 px-6 text-base font-medium shadow-md"
            onClick={onClose}
          >
            {t('marketplace.continueShopping')}
          </Button>
        </div>
      )}
    </div>
  );
}
