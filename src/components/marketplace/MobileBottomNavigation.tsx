import React, { useState, useEffect } from 'react';
import { Heart, User, ShoppingCart, Car } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { TyreIcon } from './icons/TyreIcon';
import { useCart } from '@/contexts/CartContext';
import { useFilter } from '@/contexts/FilterContext';
import { ShoppingCartBox } from './ShoppingCartBox';
import { AuthModal } from './AuthModal';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getWishlistCount } from '@/services/wishlistService';
import { useToast } from '@/hooks/use-toast';

interface MobileBottomNavigationProps {
  // No props needed anymore as we're using the filter context
}

export function MobileBottomNavigation({}: MobileBottomNavigationProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { totalItems } = useCart();
  const { toast } = useToast();
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);

  // Use the filter context
  const {
    activeSection,
    setActiveSection,
    tyresFilterCount,
    partsFilterCount,
    setShowTyresModal,
    setShowPartsModal
  } = useFilter();

  // Helper functions
  const toggleSection = (section: 'tyres' | 'parts') => {
    setActiveSection(section);
  };

  const handleOpenTyresModal = () => {
    toast({
      title: t('marketplace.comingSoon'),
      description: t('marketplace.underDevelopment'),
      duration: 3000,
    });
    // Keep the original functionality for now
    // setShowTyresModal(true);
  };

  const handleOpenPartsModal = () => {
    toast({
      title: t('marketplace.comingSoon'),
      description: t('marketplace.underDevelopment'),
      duration: 3000,
    });
    // Keep the original functionality for now
    // setShowPartsModal(true);
  };

  // Check for consumer authentication
  useEffect(() => {
    const checkConsumerAuth = () => {
      try {
        const phoneSession = localStorage.getItem('phone_auth_session');
        if (phoneSession) {
          const session = JSON.parse(phoneSession);
          const isConsumer = session.profile?.role === 'consumer';
          setIsConsumerAuthenticated(isConsumer);

          if (isConsumer) {
            loadWishlistCount();
          }
        } else {
          setIsConsumerAuthenticated(false);
          setWishlistCount(0);
        }
      } catch (error) {
        console.error('Error checking consumer auth:', error);
        setIsConsumerAuthenticated(false);
      }
    };

    checkConsumerAuth();

    // Listen for auth changes and wishlist updates
    const handleAuthChange = () => checkConsumerAuth();
    const handleWishlistUpdate = () => {
      if (isConsumerAuthenticated) {
        loadWishlistCount();
      }
    };

    window.addEventListener('auth:success', handleAuthChange);
    window.addEventListener('storage', handleAuthChange);
    window.addEventListener('wishlist:updated', handleWishlistUpdate);

    return () => {
      window.removeEventListener('auth:success', handleAuthChange);
      window.removeEventListener('storage', handleAuthChange);
      window.removeEventListener('wishlist:updated', handleWishlistUpdate);
    };
  }, []);

  // Load wishlist count
  const loadWishlistCount = async () => {
    try {
      const result = await getWishlistCount();
      if (result.success) {
        setWishlistCount(result.count || 0);
      }
    } catch (error) {
      console.error('Error loading wishlist count:', error);
    }
  };

  // Get consumer info from session
  const getConsumerInfo = () => {
    try {
      const phoneSession = localStorage.getItem('phone_auth_session');
      if (phoneSession) {
        const session = JSON.parse(phoneSession);
        return {
          name: session.profile?.full_name || 'Consumer',
          phone: session.profile?.phone || session.user?.phone || '',
          avatar: session.profile?.avatar_url
        };
      }
    } catch (error) {
      console.error('Error getting consumer info:', error);
    }
    return { name: 'Consumer', phone: '', avatar: null };
  };

  // Handle wishlist click
  const handleWishlistClick = () => {
    if (isConsumerAuthenticated) {
      navigate('/wishlist');
    } else {
      setIsAuthModalOpen(true);
    }
  };

  // Handle profile click
  const handleProfileClick = () => {
    if (isConsumerAuthenticated) {
      navigate('/my-reviews');
    } else {
      setIsAuthModalOpen(true);
    }
  };

  // Add padding to the bottom of the page to account for the bottom navigation
  useEffect(() => {
    // Add padding to the bottom of the page
    document.body.style.paddingBottom = '64px'; // 16px (height) + extra padding

    // Clean up when component unmounts
    return () => {
      document.body.style.paddingBottom = '0';
    };
  }, []);

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 h-16 flex items-center justify-between px-2 z-50">
        {/* Wishlist Button */}
        <button
          className="flex flex-col items-center justify-center w-1/5 h-full relative"
          onClick={handleWishlistClick}
        >
          <Heart className={cn(
            "h-6 w-6",
            isConsumerAuthenticated ? "text-[#fa7b00]" : "text-gray-600"
          )} />
          <span className={cn(
            "text-xs mt-1",
            isConsumerAuthenticated ? "text-[#fa7b00]" : "text-gray-600"
          )}>
            {t('marketplace.wishlist').charAt(0).toUpperCase() + t('marketplace.wishlist').slice(1)}
          </span>
          {isConsumerAuthenticated && wishlistCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-[#fa7b00] text-white text-[10px] font-bold flex items-center justify-center">
              {wishlistCount}
            </span>
          )}
        </button>

        {/* Tyres Toggle Button */}
        <button
          className={cn(
            "flex flex-col items-center justify-center w-1/5 h-full relative",
            activeSection === 'tyres' ? "text-[#fa7b00]" : "text-gray-600"
          )}
          onClick={() => {
            toggleSection('tyres');
            handleOpenTyresModal();
          }}
        >
          <TyreIcon className="h-6 w-6" />
          <span className="text-xs mt-1">{t('marketplace.tyres')}</span>
          {/* Badge for filter count - always visible */}
          <span className={`absolute top-0 right-1/4 ${tyresFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center`}>
            {tyresFilterCount}
          </span>
        </button>

        {/* Shopping Cart Button - Centerpiece */}
        <button
          className="flex flex-col items-center justify-center w-1/5 h-full relative"
          onClick={() => setIsCartOpen(!isCartOpen)}
        >
          <div className="bg-[#071c44] rounded-full p-3 -mt-6 shadow-lg relative">
            <ShoppingCart className="h-6 w-6 text-white" />
            {totalItems > 0 && (
              <span className="absolute -top-2 -right-2 h-5 w-5 rounded-full bg-[#fa7b00] text-white text-[10px] font-bold flex items-center justify-center">
                {totalItems}
              </span>
            )}
          </div>
          <span className="text-xs mt-1 text-gray-600">{t('marketplace.cart')}</span>
        </button>

        {/* All Other Parts Toggle Button */}
        <button
          className={cn(
            "flex flex-col items-center justify-center w-1/5 h-full relative",
            activeSection === 'parts' ? "text-[#fa7b00]" : "text-gray-600"
          )}
          onClick={() => {
            toggleSection('parts');
            handleOpenPartsModal();
          }}
        >
          <Car className="h-6 w-6" />
          <span className="text-xs mt-1">{t('marketplace.allOtherParts')}</span>
          {/* Badge for filter count - always visible */}
          <span className={`absolute top-0 right-1/4 ${partsFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center`}>
            {partsFilterCount}
          </span>
        </button>

        {/* Profile Button */}
        <button
          className="flex flex-col items-center justify-center w-1/5 h-full"
          onClick={handleProfileClick}
        >
          {isConsumerAuthenticated ? (
            <Avatar className="h-6 w-6 border border-[#fa7b00]/40">
              <AvatarImage src={getConsumerInfo().avatar || ""} alt="Avatar" />
              <AvatarFallback className="bg-[#fa7b00] text-white text-xs font-bold">
                {getConsumerInfo().name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          ) : (
            <User className="h-6 w-6 text-gray-600" />
          )}
          <span className={cn(
            "text-xs mt-1",
            isConsumerAuthenticated ? "text-[#fa7b00]" : "text-gray-600"
          )}>
            {isConsumerAuthenticated ? getConsumerInfo().name.split(' ')[0] : t('marketplace.profile')}
          </span>
        </button>
      </div>

      {/* Cart Dropdown */}
      {isCartOpen && (
        <div className="fixed bottom-16 left-0 right-0 z-50 px-4">
          <div className="bg-white rounded-lg shadow-xl border border-gray-200">
            <ShoppingCartBox onClose={() => setIsCartOpen(false)} />
          </div>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </>
  );
}
