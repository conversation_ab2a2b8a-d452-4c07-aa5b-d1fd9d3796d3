import React, { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Filter, ChevronLeft, ChevronRight, Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useFilter } from '@/contexts/FilterContext';
import { useProductFilter } from '@/contexts/ProductFilterContext';
import { CATEGORIES, getCategoryImageUrl } from '@/data/categoryData';
import { getFallbackImageUrl } from '@/services/categoryImageService';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CategoryNavigationProps {
  onCategorySelect: (categoryId: string) => void;
  selectedCategory: string;
  section: 'retail' | 'wholesale';
  onFilterHint?: () => void;
}

export function CategoryNavigation({
  onCategorySelect,
  selectedCategory,
  section,
  onFilterHint
}: CategoryNavigationProps) {
  const { t, i18n } = useTranslation();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [showFilterHint, setShowFilterHint] = useState(false);

  // Use product filter context for the new filtering system
  const {
    setShowProductFilterModal,
    activeFilterCount
  } = useProductFilter();

  // Use the centralized category data
  const categories = CATEGORIES;

  // Check scroll position to show/hide arrows
  const checkScroll = () => {
    if (!scrollContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10); // 10px buffer
  };

  // Scroll left/right
  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const scrollAmount = 300; // Adjust as needed
    const newScrollLeft = direction === 'left'
      ? scrollContainerRef.current.scrollLeft - scrollAmount
      : scrollContainerRef.current.scrollLeft + scrollAmount;

    scrollContainerRef.current.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    });
  };

  // Listen for scroll events
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScroll);
      // Initial check
      checkScroll();

      return () => scrollContainer.removeEventListener('scroll', checkScroll);
    }
  }, []);

  // Check scroll when window resizes
  useEffect(() => {
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  // Handle category selection with filter hint
  const handleCategorySelect = (categoryId: string) => {
    onCategorySelect(categoryId);

    // Show filter hint when selecting a specific category (not "all")
    if (categoryId !== 'all') {
      setShowFilterHint(true);
      onFilterHint?.();

      // Auto-hide hint after 3 seconds
      setTimeout(() => {
        setShowFilterHint(false);
      }, 3000);
    } else {
      setShowFilterHint(false);
    }
  };

  // Open the product filter modal (NOT vehicle selection)
  const handleOpenFilters = () => {
    setShowFilterHint(false);
    setShowProductFilterModal(true);
  };

  // Language selector functionality
  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
    localStorage.setItem('i18nextLng', language);
  };

  const getLanguageName = (code: string) => {
    switch (code) {
      case 'ar':
        return t('language.arabic');
      case 'en':
        return t('language.english');
      case 'fr':
        return t('language.french');
      default:
        return code;
    }
  };

  const getLanguageFlag = (code: string) => {
    switch (code) {
      case 'ar':
        return '🇩🇿'; // Algeria flag for Arabic
      case 'en':
        return '🇺🇸'; // US flag for English
      case 'fr':
        return '🇫🇷'; // France flag for French
      default:
        return '🌐';
    }
  };

  return (
    <div className="sticky top-0 z-40 bg-white">
      <div className="container">
        <div className="relative flex items-center h-20 md:h-20 py-2">
          {/* Left scroll arrow */}
          {showLeftArrow && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-0 z-10 h-8 w-8 bg-white/90 shadow-md hover:bg-white rounded-full border border-gray-200 hidden sm:flex"
              onClick={() => scroll('left')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}

          {/* Scrollable categories - Skeleton style design with rectangular shape */}
          <div
            ref={scrollContainerRef}
            className="flex items-center gap-3 md:gap-4 overflow-x-auto scrollbar-hide px-2 sm:px-10 flex-1"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {categories.map((category, index) => {
              // Smart text wrapping logic - based on character count (18+ chars including spaces)
              const shouldWrap = category.displayName.length >= 18;

              // Mobile: Only show first 3 categories initially to prevent overlap
              const isHiddenOnMobile = index >= 3;

              return (
                <button
                  key={category.id}
                  className={cn(
                    "flex flex-col items-center gap-1 md:gap-2 py-1 md:py-2 px-2 md:px-3 transition-all duration-75 min-w-[80px] md:min-w-[100px] group",
                    "hover:opacity-80 active:scale-95",
                    selectedCategory === category.id
                      ? "opacity-100"
                      : "opacity-70 hover:opacity-90",
                    // Hide categories beyond 3rd on mobile to prevent overlap
                    isHiddenOnMobile && "md:flex"
                  )}
                  onClick={() => handleCategorySelect(category.id)}
                >
                  {/* Image container - Rectangular/rounded rectangle shape - Increased size */}
                  <div className={cn(
                    "w-16 h-12 md:w-20 md:h-16 rounded-xl md:rounded-2xl bg-gray-200 flex items-center justify-center overflow-hidden transition-all duration-75",
                    selectedCategory === category.id
                      ? "ring-2 ring-gray-900 ring-offset-1 md:ring-offset-2"
                      : "group-hover:bg-gray-300"
                  )}>
                    <img
                      src={getCategoryImageUrl(category.id)}
                      alt={category.displayName}
                      className="w-10 h-8 md:w-14 md:h-12 object-contain"
                      onError={(e) => {
                        // Fallback to placeholder on error
                        const target = e.target as HTMLImageElement;
                        target.src = getFallbackImageUrl('category');
                      }}
                    />
                  </div>

                  {/* Category name - Smart text wrapping */}
                  <div className={cn(
                    "text-center transition-colors duration-75 flex items-center justify-center",
                    shouldWrap ? "h-6 md:h-8 leading-tight" : "h-3 md:h-4",
                    selectedCategory === category.id
                      ? "text-gray-900"
                      : "text-gray-600 group-hover:text-gray-900"
                  )}>
                    <span className={cn(
                      "font-medium",
                      shouldWrap
                        ? "text-[9px] md:text-[10px] leading-3 max-w-[60px] md:max-w-[80px]"
                        : "text-[10px] md:text-xs whitespace-nowrap"
                    )}>
                      {category.displayName}
                    </span>
                  </div>
                </button>
              );
            })}
          </div>

          {/* Right scroll arrow - Positioned to the left of Language/Filters buttons */}
          {showRightArrow && (
            <Button
              variant="ghost"
              size="icon"
              className="ml-2 z-10 h-8 w-8 bg-white/90 shadow-md hover:bg-white rounded-full border border-gray-200 hidden sm:flex flex-shrink-0"
              onClick={() => scroll('right')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}

          {/* Language Selector & Filters - Positioned at the far right */}
          <div className="flex flex-col gap-1 md:gap-2 ml-2 md:ml-4 flex-shrink-0">
            <TooltipProvider>
              {/* Language Selector - Top position */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className={cn(
                            "flex items-center gap-1 md:gap-2 px-2 md:px-3 py-1 md:py-1.5 h-6 md:h-8 rounded-md md:rounded-lg border transition-all",
                            "bg-white/95 backdrop-blur-sm shadow-sm hover:shadow-md hover:bg-white",
                            "border-gray-300 hover:border-[#fa7b00]/50"
                          )}
                        >
                          <Globe className="h-3 w-3 md:h-4 md:w-4 text-[#fa7b00]" />
                          {/* Mobile: Show language name instead of just flag */}
                          <span className="text-[10px] md:text-xs font-medium">
                            {getLanguageName(i18n.language)}
                          </span>
                          <span className="text-xs md:text-sm">
                            {getLanguageFlag(i18n.language)}
                          </span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="left">
                        <p>{t('marketplace.selectLanguage')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[150px]">
                  <DropdownMenuItem
                    onClick={() => changeLanguage('ar')}
                    className={cn(
                      "flex items-center gap-2 cursor-pointer",
                      i18n.language === 'ar' ? 'bg-[#fa7b00]/10 text-[#fa7b00]' : ''
                    )}
                  >
                    <span>🇩🇿</span>
                    {t('language.arabic')}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => changeLanguage('en')}
                    className={cn(
                      "flex items-center gap-2 cursor-pointer",
                      i18n.language === 'en' ? 'bg-[#fa7b00]/10 text-[#fa7b00]' : ''
                    )}
                  >
                    <span>🇺🇸</span>
                    {t('language.english')}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => changeLanguage('fr')}
                    className={cn(
                      "flex items-center gap-2 cursor-pointer",
                      i18n.language === 'fr' ? 'bg-[#fa7b00]/10 text-[#fa7b00]' : ''
                    )}
                  >
                    <span>🇫🇷</span>
                    {t('language.french')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Filters Button - Bottom position */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className={cn(
                      "flex items-center gap-1 md:gap-2 px-2 md:px-3 py-1 md:py-1.5 h-6 md:h-8 rounded-md md:rounded-lg border transition-all",
                      "bg-white/95 backdrop-blur-sm shadow-sm hover:shadow-md",
                      showFilterHint
                        ? "border-[#fa7b00] bg-[#fa7b00]/5 animate-pulse"
                        : "border-gray-300 bg-white hover:border-[#fa7b00]/50"
                    )}
                    onClick={handleOpenFilters}
                  >
                    <Filter className="h-3 w-3 md:h-4 md:w-4" />
                    <span className="text-[10px] md:text-xs font-medium">
                      {t('marketplace.filters')}
                    </span>
                    {activeFilterCount > 0 && (
                      <span className="flex h-3 w-3 md:h-4 md:w-4 items-center justify-center rounded-full bg-[#fa7b00] text-[8px] md:text-[10px] font-bold text-white">
                        {activeFilterCount}
                      </span>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>{t('marketplace.filters')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Filter hint tooltip */}
        {showFilterHint && selectedCategory !== 'all' && (
          <div className="absolute right-0 top-full mt-2 z-50">
            <div className="bg-[#071c44] text-white px-3 py-2 rounded-lg shadow-lg text-xs font-medium relative">
              <div className="absolute -top-1 right-4 w-2 h-2 bg-[#071c44] rotate-45" />
              {t('marketplace.useFiltersToRefine')}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
