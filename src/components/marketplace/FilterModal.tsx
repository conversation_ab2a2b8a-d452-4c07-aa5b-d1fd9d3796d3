import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import EnhancedDropdown from '@/components/ui/enhanced-dropdown';
import { fetchCarBrands, fetchCarModels, fetchCarEngines } from '@/services/vehicleDataService';

interface FilterModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: 'tyres' | 'parts';
  onSubmit: (filters: any) => void;
  initialFilters?: any;
}

export function FilterModal({ open, onOpenChange, type, onSubmit, initialFilters = {} }: FilterModalProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  // Tyres state
  const [width, setWidth] = useState(initialFilters.width || '');
  const [aspectRatio, setAspectRatio] = useState(initialFilters.aspectRatio || '');
  const [rimDiameter, setRimDiameter] = useState(initialFilters.rimDiameter || '');
  const [season, setSeason] = useState(initialFilters.season || '');

  // Vehicle parts state
  const [brand, setBrand] = useState(initialFilters.brand || '');
  const [model, setModel] = useState(initialFilters.model || '');
  const [engine, setEngine] = useState(initialFilters.engine || '');

  // Options for dropdowns
  const [brandOptions, setBrandOptions] = useState<any[]>([]);
  const [modelOptions, setModelOptions] = useState<any[]>([]);
  const [engineOptions, setEngineOptions] = useState<any[]>([]);

  // Fetch brands on initial load
  useEffect(() => {
    const fetchBrands = async () => {
      setLoading(true);
      try {
        const brands = await fetchCarBrands();
        setBrandOptions(brands);
      } catch (error) {
        console.error('Error fetching brands:', error);
      } finally {
        setLoading(false);
      }
    };

    if (type === 'parts' && open) {
      fetchBrands();
    }
  }, [type, open]);

  // Fetch models when brand changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!brand) {
        setModelOptions([]);
        return;
      }

      setLoading(true);
      try {
        const models = await fetchCarModels(brand);
        setModelOptions(models);
      } catch (error) {
        console.error('Error fetching models:', error);
      } finally {
        setLoading(false);
      }
    };

    if (type === 'parts') {
      fetchModels();
    }
  }, [brand, type]);

  // Fetch engines when model changes
  useEffect(() => {
    const fetchEngines = async () => {
      if (!brand || !model) {
        setEngineOptions([]);
        return;
      }

      setLoading(true);
      try {
        // Parse the uniqueId to get model and generation information
        const parts = model.split('_');
        const modelName = parts[0];
        const generationCode = parts.length > 1 ? parts[1] : '';

        const engines = await fetchCarEngines(brand, modelName, generationCode);
        setEngineOptions(engines);
      } catch (error) {
        console.error('Error fetching engines:', error);
      } finally {
        setLoading(false);
      }
    };

    if (type === 'parts') {
      fetchEngines();
    }
  }, [brand, model, type]);

  const handleSubmit = () => {
    if (type === 'tyres') {
      onSubmit({
        width,
        aspectRatio,
        rimDiameter,
        season
      });
    } else {
      onSubmit({
        brand,
        model,
        engine
      });
    }
    onOpenChange(false);
  };

  const renderBrandOption = (option: any) => (
    <div className="flex items-center">
      {option.image_url && (
        <img
          src={option.image_url}
          alt={option.label}
          className="w-6 h-6 mr-2 object-contain"
        />
      )}
      <span>{option.label}</span>
    </div>
  );

  const renderModelOption = (option: any) => (
    <div className="flex flex-col">
      <span className="font-medium">{option.model}</span>
      <span className="text-xs text-gray-500">
        {option.generation} {option.bodyType && `• ${option.bodyType}`} {option.productionYears && `• ${option.productionYears}`}
      </span>
    </div>
  );

  const renderEngineOption = (option: any) => (
    <div className="flex flex-col">
      <span className="font-medium">{option.fullName}</span>
      <span className="text-xs text-gray-500">
        {option.code && `${option.code} • `}
        {option.productionYears}
      </span>
    </div>
  );

  const groupModelsByName = (option: any) => option.model;
  const groupEnginesByFuelType = (option: any) => option.fuel_type || 'Unknown';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {type === 'tyres' ? t('marketplace.tyreSelectorTitle') : t('marketplace.vehicleSelectorTitle')}
          </DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="py-4">
          {type === 'tyres' ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">{t('marketplace.tyreWidth')}</label>
                <select
                  value={width}
                  onChange={(e) => setWidth(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">{t('marketplace.selectWidth')}</option>
                  {[155, 165, 175, 185, 195, 205, 215, 225, 235, 245, 255, 265, 275, 285, 295, 305, 315, 325].map((w) => (
                    <option key={w} value={w.toString()}>
                      {w} mm
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">{t('marketplace.aspectRatio')}</label>
                <select
                  value={aspectRatio}
                  onChange={(e) => setAspectRatio(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">{t('marketplace.selectRatio')}</option>
                  {[30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85].map((ratio) => (
                    <option key={ratio} value={ratio.toString()}>
                      {ratio}%
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">{t('marketplace.rimDiameter')}</label>
                <select
                  value={rimDiameter}
                  onChange={(e) => setRimDiameter(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">{t('marketplace.selectDiameter')}</option>
                  {[13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24].map((d) => (
                    <option key={d} value={d.toString()}>
                      {d}"
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">{t('marketplace.season')}</label>
                <select
                  value={season}
                  onChange={(e) => setSeason(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">{t('marketplace.selectSeason')}</option>
                  <option value="summer">{t('marketplace.summer')}</option>
                  <option value="winter">{t('marketplace.winter')}</option>
                  <option value="all-season">{t('marketplace.allSeason')}</option>
                </select>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-orange-500 text-white mr-2 text-xs">
                      1
                    </div>
                    <span>{t('marketplace.selectBrand')}</span>
                  </div>
                </label>
                <EnhancedDropdown
                  options={brandOptions.map(brand => ({
                    value: brand.name,
                    label: brand.name,
                    image_url: brand.image_url
                  }))}
                  value={brand}
                  onChange={setBrand}
                  placeholder={t('marketplace.searchBrands')}
                  searchPlaceholder={t('marketplace.searchBrands')}
                  renderOption={renderBrandOption}
                  emptyMessage={t('marketplace.noBrandsFound')}
                  maxHeight="200px"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2 text-xs">
                      2
                    </div>
                    <span>{t('marketplace.selectModel')}</span>
                  </div>
                </label>
                <EnhancedDropdown
                  options={modelOptions}
                  value={model}
                  onChange={setModel}
                  placeholder={t('marketplace.searchModels')}
                  searchPlaceholder={t('marketplace.searchModels')}
                  renderOption={renderModelOption}
                  groupBy={groupModelsByName}
                  emptyMessage={t('marketplace.noModelsFound')}
                  disabled={!brand}
                  maxHeight="200px"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2 text-xs">
                      3
                    </div>
                    <span>{t('marketplace.selectEngine')}</span>
                  </div>
                </label>
                <EnhancedDropdown
                  options={engineOptions}
                  value={engine}
                  onChange={setEngine}
                  placeholder={t('marketplace.searchEngines')}
                  searchPlaceholder={t('marketplace.searchEngines')}
                  renderOption={renderEngineOption}
                  groupBy={groupEnginesByFuelType}
                  emptyMessage={t('marketplace.noEnginesFound')}
                  disabled={!model}
                  maxHeight="200px"
                />
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('actions.cancel')}
          </Button>
          <Button onClick={handleSubmit}>
            {t('actions.save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
