/**
 * AuthModal - Enhanced Phone Authentication for Consumers
 *
 * This component uses the EnhancedPhoneAuthModal with full name collection
 * Provides phone-only authentication with consumer privacy protection
 * Enhanced with post-authentication action support for seamless UX
 */

import React, { useEffect } from 'react';
import { EnhancedPhoneAuthModal } from '@/components/auth/EnhancedPhoneAuthModal';
import { useAuth } from '@/contexts/AuthContext';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const { refreshSession } = useAuth();

  // Handle successful authentication
  const handleAuthSuccess = async (user: any, action: 'login' | 'signup') => {
    console.log(`🎉 Authentication successful: ${action}`, user);

    // Refresh the auth session
    await refreshSession();

    // Close the modal
    onClose();

    // Dispatch authentication success event for components to listen to
    window.dispatchEvent(new CustomEvent('consumer-auth-success', {
      detail: { user, action }
    }));
  };

  return (
    <EnhancedPhoneAuthModal
      isOpen={isOpen}
      onClose={onClose}
      onSuccess={handleAuthSuccess}
    />
  );
}
