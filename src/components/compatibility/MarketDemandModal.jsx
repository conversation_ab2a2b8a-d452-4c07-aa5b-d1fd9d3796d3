import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON><PERSON><PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { LineChart } from "lucide-react";
import { cn } from "@/lib/utils";

const MarketDemandModal = ({ open, onOpenChange, vehicle, demandData }) => {
  if (!vehicle) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <LineChart className="h-5 w-5 text-primary" />
            Market Demand for {vehicle.brand} {vehicle.model}
          </DialogTitle>
        </DialogHeader>

        <div className="mb-6 pb-4 border-b">
          <h4 className="text-lg font-semibold mb-1">{vehicle.brand} {vehicle.model}</h4>
          <p className="text-muted-foreground">Engine: {vehicle.engineType}</p>
        </div>

        <div>
          <h5 className="text-base font-medium mb-4">Top Searched Parts</h5>

          {demandData && demandData.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Part Category</TableHead>
                  <TableHead>Search Volume</TableHead>
                  <TableHead>Trend</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {demandData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.category}</TableCell>
                    <TableCell>{item.volume}</TableCell>
                    <TableCell className={cn(
                      item.trend === 'up' ? "text-green-600 font-medium" : "text-red-600 font-medium"
                    )}>
                      {item.trend === 'up' ? '↑' : '↓'} {item.trendPercentage}%
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center py-6 text-muted-foreground italic">
              No market demand data available for this vehicle.
            </p>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default MarketDemandModal;
