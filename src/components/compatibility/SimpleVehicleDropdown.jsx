import React, { useState, useRef, useEffect } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { Check, ChevronsUpDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";

const SimpleVehicleDropdown = ({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  disabled = false,
  className,
  renderOption,
  groupBy,
  searchPlaceholder = "Search...",
  emptyMessage = "No results found.",
  maxHeight = "300px",
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const inputRef = useRef(null);

  // Focus the search input when the dropdown opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Filter options based on search query
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group options if groupBy function is provided
  const groupedOptions = groupBy
    ? filteredOptions.reduce((groups, option) => {
        const groupKey = groupBy(option);
        if (!groups[groupKey]) groups[groupKey] = [];
        groups[groupKey].push(option);
        return groups;
      }, {})
    : { all: filteredOptions };

  // Find the selected option
  const selectedOption = options.find(option => option.value === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-full justify-between border-2 h-14 px-4 py-6",
            "bg-white hover:bg-gray-50",
            "rounded-full shadow-sm",
            "text-left font-normal",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
        >
          {selectedOption ? (
            renderOption ? (
              renderOption(selectedOption)
            ) : (
              selectedOption.label
            )
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 rounded-3xl shadow-xl border-none w-full min-w-[240px]"
        align="start"
        side="bottom"
        avoidCollisions={true}
        collisionPadding={20}
        sticky="always"
      >
        <div className="rounded-3xl overflow-hidden">
          <div className="border-b p-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                ref={inputRef}
                placeholder={searchPlaceholder}
                className="pl-9 h-10 rounded-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <ScrollArea className="w-full" style={{ maxHeight }}>
            {filteredOptions.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                {emptyMessage}
              </div>
            ) : (
              <div className="p-2">
                {Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
                  <div key={groupName} className="mb-2">
                    {groupName !== "all" && (
                      <div className="px-3 py-1.5 text-xs font-medium text-muted-foreground">
                        {groupName}
                      </div>
                    )}
                    <div>
                      {groupOptions.map((option) => (
                        <div
                          key={option.value}
                          className={cn(
                            "flex items-center justify-between w-full py-2 px-3 text-sm rounded-md cursor-pointer",
                            value === option.value ? "bg-accent text-accent-foreground" : "hover:bg-muted"
                          )}
                          onClick={() => {
                            onChange(option.value);
                            setOpen(false);
                            setSearchQuery("");
                          }}
                        >
                          <div className="flex items-center">
                            {renderOption ? renderOption(option) : option.label}
                          </div>
                          {value === option.value && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default SimpleVehicleDropdown;
