import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Car, Truck, Bus, Tractor, Bike } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VehicleTypeSelectorProps {
  selectedType: string;
  onTypeChange: (type: string) => void;
}

const VehicleTypeSelector: React.FC<VehicleTypeSelectorProps> = ({
  selectedType,
  onTypeChange
}) => {
  const vehicleTypes = [
    {
      id: 'car',
      name: 'Car',
      icon: Car
    },
    {
      id: 'truck',
      name: 'Truck',
      icon: Truck
    },
    {
      id: 'bus',
      name: 'Bus',
      icon: Bus
    },
    {
      id: 'tractor',
      name: 'Tractor',
      icon: Tractor
    },
    {
      id: 'motorcycle',
      name: 'Motorcycle',
      icon: Bike
    }
  ];

  return (
    <div className="flex flex-wrap gap-2">
      {vehicleTypes.map((type) => (
        <Button
          key={type.id}
          variant={selectedType === type.id ? 'default' : 'outline'}
          className={cn(
            "flex flex-col items-center justify-center h-20 w-24 gap-1",
            selectedType === type.id ? 'bg-primary text-primary-foreground' : 'bg-background'
          )}
          onClick={() => onTypeChange(type.id)}
        >
          <type.icon className="h-6 w-6" />
          <span className="text-xs font-medium">{type.name}</span>
        </Button>
      ))}
    </div>
  );
};

export default VehicleTypeSelector;
