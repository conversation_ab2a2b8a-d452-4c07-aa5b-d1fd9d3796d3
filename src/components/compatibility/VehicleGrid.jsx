import React from 'react';
import { <PERSON><PERSON><PERSON>, Car } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

// Loading skeleton for vehicle cards
const VehicleCardSkeleton = () => {
  return (
    <div className="h-full rounded-2xl overflow-hidden border border-gray-200 bg-white">
      <div className="p-6">
        <div className="flex flex-col h-full">
          <div className="flex items-start mb-5">
            <div className="flex-shrink-0 mr-4">
              <Skeleton className="w-12 h-12 rounded-xl" />
            </div>
            <div>
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>

          <div className="border-t border-gray-100 pt-5">
            <Skeleton className="h-12 w-full rounded-xl mb-5" />

            <div className="grid grid-cols-2 gap-6">
              <div className="flex flex-col bg-gray-50 rounded-xl p-3">
                <Skeleton className="h-3 w-20 mb-2" />
                <Skeleton className="h-6 w-8" />
              </div>

              <div className="flex flex-col bg-gray-50 rounded-xl p-3">
                <Skeleton className="h-3 w-20 mb-2" />
                <Skeleton className="h-6 w-8" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const VehicleCard = ({ vehicle, onMarketDemand }) => {
  return (
    <div className="h-full rounded-2xl overflow-hidden border border-gray-200 bg-white transition-all duration-300 hover:shadow-lg hover:translate-y-[-4px]">
      <div className="p-6">
        <div className="flex flex-col h-full">
          <div className="flex items-start mb-5">
            <div className="flex-shrink-0 mr-4">
              <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center">
                <Car className="h-6 w-6 text-gray-500" />
              </div>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-1">{vehicle.brand}</h3>
              <div className="flex items-center gap-2 mb-1">
                <p className="text-base text-gray-600">{vehicle.model}</p>
                {vehicle.generation && (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                    {vehicle.generation}
                  </span>
                )}
              </div>

              {/* Body type and year range */}
              <p className="text-sm text-gray-500 mb-2">
                {vehicle.bodyType && `${vehicle.bodyType} • `}
                {vehicle.yearFrom && vehicle.yearTo ?
                  `${vehicle.yearFrom}${vehicle.yearTo !== '...' ? '–' + vehicle.yearTo : '–present'}`
                  : ''}
              </p>

              {/* Engine details */}
              {vehicle.engineDetails ? (
                <div className="mt-1 bg-gray-50 px-2 py-1.5 rounded-lg">
                  <p className="text-sm text-gray-600 font-medium flex items-center">
                    <span className={`w-2 h-2 rounded-full mr-1.5 ${
                      vehicle.engineDetails.fuel_type === 'Petrol' ? 'bg-green-500' :
                      vehicle.engineDetails.fuel_type === 'Diesel' ? 'bg-blue-500' :
                      vehicle.engineDetails.fuel_type === 'Electric' ? 'bg-purple-500' :
                      vehicle.engineDetails.fuel_type === 'Hybrid' ? 'bg-teal-500' : 'bg-gray-500'
                    }`}></span>
                    {vehicle.engineDetails.fuel_type} Engine
                  </p>
                  {vehicle.engineDetails.power_kW && vehicle.engineDetails.power_HP && (
                    <p className="text-sm text-gray-500">
                      {vehicle.engineDetails.power_kW} kW / {vehicle.engineDetails.power_HP} HP
                    </p>
                  )}
                  {vehicle.engineDetails.code && vehicle.engineDetails.code !== 'Unknown' && (
                    <p className="text-xs text-gray-400 mt-0.5">
                      Code: {vehicle.engineDetails.code}
                    </p>
                  )}
                </div>
              ) : vehicle.engineType ? (
                <p className="text-sm text-gray-500">{vehicle.engineType}</p>
              ) : null}
            </div>
          </div>

          <div className="border-t border-gray-100 pt-5">
            <button
              type="button"
              className="w-full mb-5 py-3 px-4 rounded-xl bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-800 font-medium flex items-center justify-center gap-2 transition-colors"
              onClick={() => onMarketDemand(vehicle.id)}
            >
              <LineChart className="h-4 w-4 text-primary" /> Market Demand
            </button>

            <div className="grid grid-cols-2 gap-6">
              <div className="flex flex-col bg-gray-50 rounded-xl p-3">
                <span className="text-xs text-gray-500 mb-1">My Listings</span>
                <span className="text-lg font-bold text-gray-900">{vehicle.myProductCount}</span>
              </div>

              <div className="flex flex-col bg-gray-50 rounded-xl p-3">
                <span className="text-xs text-gray-500 mb-1">All Listings</span>
                <span className="text-lg font-bold text-gray-900">{vehicle.allProductCount}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const VehicleGrid = ({ vehicles, loading, onMarketDemand }) => {
  // State for pagination
  const [currentPage, setCurrentPage] = React.useState(1);
  const itemsPerPage = 18; // Increased from 12 to 18 for better display of all vehicles

  // Reset to page 1 when vehicles change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [vehicles.length]);

  // Calculate total pages
  const totalPages = Math.ceil(vehicles.length / itemsPerPage);

  // Get current page items
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return vehicles.slice(startIndex, endIndex);
  };

  // Generate pagination range with ellipsis for large page counts
  const getPaginationRange = () => {
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Show all pages if there are few
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Always show first page, last page, current page, and pages around current
    const pages = [1];

    // Calculate range around current page
    const leftBound = Math.max(2, currentPage - 1);
    const rightBound = Math.min(totalPages - 1, currentPage + 1);

    // Add ellipsis before left bound if needed
    if (leftBound > 2) {
      pages.push('...');
    }

    // Add pages in range
    for (let i = leftBound; i <= rightBound; i++) {
      pages.push(i);
    }

    // Add ellipsis after right bound if needed
    if (rightBound < totalPages - 1) {
      pages.push('...');
    }

    // Add last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  // Handle page change
  const handlePageChange = (page) => {
    if (page === '...') return; // Skip ellipsis

    setCurrentPage(page);
    // Scroll to top of grid
    window.scrollTo({ top: document.getElementById('vehicle-grid').offsetTop - 100, behavior: 'smooth' });
  };

  // Show skeleton loading state
  if (loading) {
    return (
      <div id="vehicle-grid" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array(9).fill(0).map((_, index) => (
          <div key={`skeleton-${index}`}>
            <VehicleCardSkeleton />
          </div>
        ))}
      </div>
    );
  }

  // Show empty state when no vehicles are found
  if (vehicles.length === 0) {
    return (
      <div id="vehicle-grid" className="text-center py-16 px-4 bg-white rounded-2xl border border-gray-200 shadow-sm">
        <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-6">
          <Car className="h-10 w-10 text-gray-400" />
        </div>
        <h3 className="text-xl font-medium mb-3 text-gray-900">No vehicles found</h3>
        <p className="text-gray-500 max-w-md mx-auto">
          Try adjusting your filters or selecting a different vehicle type to find compatible vehicles.
        </p>
      </div>
    );
  }

  // Get current page vehicles
  const currentVehicles = getCurrentPageItems();

  // Show vehicle grid with pagination
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <p className="text-sm text-muted-foreground">
          Showing {currentVehicles.length} of {vehicles.length} vehicles
          {totalPages > 1 && ` (Page ${currentPage} of ${totalPages})`}
        </p>
      </div>

      <div id="vehicle-grid" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {currentVehicles.map((vehicle) => (
          <div key={vehicle.id}>
            <VehicleCard
              vehicle={vehicle}
              onMarketDemand={onMarketDemand}
            />
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-10">
          <nav className="flex items-center gap-2">
            <button
              type="button"
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="h-10 w-10 rounded-full flex items-center justify-center border-2 border-gray-200 text-gray-600 disabled:opacity-40 disabled:cursor-not-allowed hover:border-gray-300 transition-colors"
            >
              &lt;
            </button>

            {getPaginationRange().map((page, index) => (
              <button
                key={`page-${index}-${page}`}
                type="button"
                onClick={() => handlePageChange(page)}
                disabled={page === '...'}
                className={`h-10 min-w-10 px-2 rounded-full flex items-center justify-center border-2 transition-colors ${
                  page === '...'
                    ? "border-transparent text-gray-600 cursor-default"
                    : currentPage === page
                      ? "border-primary bg-primary/5 text-primary font-medium"
                      : "border-gray-200 text-gray-600 hover:border-gray-300"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              type="button"
              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="h-10 w-10 rounded-full flex items-center justify-center border-2 border-gray-200 text-gray-600 disabled:opacity-40 disabled:cursor-not-allowed hover:border-gray-300 transition-colors"
            >
              &gt;
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default VehicleGrid;
