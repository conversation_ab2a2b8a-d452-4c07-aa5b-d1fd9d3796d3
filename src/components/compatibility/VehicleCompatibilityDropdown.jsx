import React, { useState } from 'react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "./command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

const VehicleCompatibilityDropdown = ({
  options,
  value,
  onChange,
  placeholder,
  disabled,
  emptyMessage = "No results found.",
  className,
  renderOption,
  groupBy,
  searchPlaceholder = "Search...",
  maxHeight = "300px",
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Filter options based on search query
  const filteredOptions = options.filter((option) => {
    if (!searchQuery) return true;
    return option.label.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Group options if groupBy function is provided
  const groupedOptions = groupBy
    ? filteredOptions.reduce((groups, option) => {
        const groupKey = groupBy(option);
        if (!groups[groupKey]) groups[groupKey] = [];
        groups[groupKey].push(option);
        return groups;
      }, {})
    : { all: filteredOptions };

  // Find the selected option
  const selectedOption = options.find((option) => option.value === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-full justify-between border-2 h-14 px-4 py-6",
            "bg-white hover:bg-gray-50",
            "rounded-full shadow-sm",
            "text-left font-normal",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
        >
          {selectedOption ? (
            renderOption ? (
              renderOption(selectedOption)
            ) : (
              selectedOption.label
            )
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 rounded-3xl shadow-xl border-none w-full min-w-[240px]"
        align="start"
        side="bottom"
        avoidCollisions={true}
        collisionPadding={20}
        sticky="always"
      >
        <Command className="rounded-3xl">
          <CommandInput
            placeholder={searchPlaceholder}
            className="h-12 rounded-t-3xl"
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList className={`overflow-y-auto`} style={{ maxHeight: maxHeight }}>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            {Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
              <CommandGroup key={groupName} heading={groupName !== "all" ? groupName : undefined}>
                {groupOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={() => {
                      onChange(option.value);
                      setOpen(false);
                      setSearchQuery("");
                    }}
                    className="py-2 px-3 cursor-pointer"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        {renderOption ? renderOption(option) : option.label}
                      </div>
                      {value === option.value && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default VehicleCompatibilityDropdown;
