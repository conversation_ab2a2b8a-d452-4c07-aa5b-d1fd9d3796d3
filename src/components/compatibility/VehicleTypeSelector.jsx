import React from 'react';
import { Car, Bike, Truck } from 'lucide-react';
import { cn } from '@/lib/utils';

const VehicleTypeSelector = ({ selectedType, onTypeChange }) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4 justify-center">
      <button
        type="button"
        className={cn(
          "flex flex-col items-center justify-center h-auto py-6 px-8 flex-1 max-w-[200px]",
          "rounded-2xl border-2 transition-all duration-200",
          "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20",
          selectedType === 'car'
            ? "border-primary bg-primary/5 text-primary"
            : "border-gray-200 bg-white text-gray-700 hover:border-gray-300"
        )}
        onClick={() => onTypeChange('car')}
      >
        <div className={cn(
          "flex items-center justify-center w-12 h-12 rounded-full mb-3",
          selectedType === 'car' ? "bg-primary/10" : "bg-gray-100"
        )}>
          <Car className={cn(
            "h-6 w-6",
            selectedType === 'car' ? "text-primary" : "text-gray-600"
          )} />
        </div>
        <span className="font-medium">Cars</span>
      </button>

      <button
        type="button"
        className={cn(
          "flex flex-col items-center justify-center h-auto py-6 px-8 flex-1 max-w-[200px]",
          "rounded-2xl border-2 transition-all duration-200",
          "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20",
          selectedType === 'motorcycle'
            ? "border-primary bg-primary/5 text-primary"
            : "border-gray-200 bg-white text-gray-700 hover:border-gray-300"
        )}
        onClick={() => onTypeChange('motorcycle')}
      >
        <div className={cn(
          "flex items-center justify-center w-12 h-12 rounded-full mb-3",
          selectedType === 'motorcycle' ? "bg-primary/10" : "bg-gray-100"
        )}>
          <Bike className={cn(
            "h-6 w-6",
            selectedType === 'motorcycle' ? "text-primary" : "text-gray-600"
          )} />
        </div>
        <span className="font-medium">Motorcycles</span>
      </button>

      <button
        type="button"
        className={cn(
          "flex flex-col items-center justify-center h-auto py-6 px-8 flex-1 max-w-[200px]",
          "rounded-2xl border-2 transition-all duration-200",
          "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20",
          selectedType === 'truck'
            ? "border-primary bg-primary/5 text-primary"
            : "border-gray-200 bg-white text-gray-700 hover:border-gray-300"
        )}
        onClick={() => onTypeChange('truck')}
      >
        <div className={cn(
          "flex items-center justify-center w-12 h-12 rounded-full mb-3",
          selectedType === 'truck' ? "bg-primary/10" : "bg-gray-100"
        )}>
          <Truck className={cn(
            "h-6 w-6",
            selectedType === 'truck' ? "text-primary" : "text-gray-600"
          )} />
        </div>
        <span className="font-medium">Trucks</span>
      </button>
    </div>
  );
};

export default VehicleTypeSelector;
