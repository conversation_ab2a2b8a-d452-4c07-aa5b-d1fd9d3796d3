import React from 'react';
import { Label } from "@/components/ui/label";
import { Avatar } from "@/components/ui/avatar";
import EnhancedDropdown from "@/components/ui/enhanced-dropdown";

const SearchFilters = ({ brands, models, engines, filters, onFilterChange }) => {
  // Get the selected brand from filters
  const selectedBrand = filters.brand;
  // Convert brands to the format expected by EnhancedDropdown
  const brandOptions = brands.map(brand => ({
    value: brand.name,
    label: brand.name,
    image_url: brand.image_url
  }));

  // Convert models to the format expected by EnhancedDropdown
  // First, ensure we have valid model data
  const validModels = models.filter(model =>
    model && model.uniqueId && model.model && model.generation && model.bodyType
  );

  if (validModels.length < models.length) {
    console.warn(`SearchFilters: Filtered out ${models.length - validModels.length} invalid models`);
  }

  const modelOptions = validModels.map(model => ({
    value: model.uniqueId, // Use the unique ID as the value
    label: model.fullName || `${model.model} (${model.generation})`,
    model: model.model,
    generation: model.generation,
    bodyType: model.bodyType,
    dateRange: model.dateRange
  }));

  console.log(`Rendering ${modelOptions.length} model options`);

  // Log unique model names to verify what's being displayed in the dropdown
  if (modelOptions.length > 0) {
    const uniqueModelNames = [...new Set(modelOptions.map(m => m.model))];
    console.log(`SearchFilters: Unique model names (${uniqueModelNames.length}):`, uniqueModelNames.join(', '));

    // Check if we have all the expected KIA models
    if (selectedBrand === 'KIA') {
      const expectedKiaModels = [
        'PICANTO', 'SPORTAGE', 'CERATO', 'SORENTO', 'CEE\'D', 'OPTIMA', 'CARENS', 'SOUL', 'BONGO',
        'STONIC', 'SELTOS', 'NIRO', 'PEGAS', 'PROCEED', 'MAGENTIS', 'XCEED', 'VENGA', 'PRIDE',
        'SHUMA / SEPHIA / MENTOR', 'SEDONA / CARNIVAL', 'PREGIO', 'BESTA', 'CLARUS', 'AVELLA',
        'RETONA', 'JOICE', 'ENTERPRISE', 'ELAN', 'POTENTIA', 'OPIRUS', 'QUORIS / K9 / K900',
        'STINGER', 'EV6', 'EV9', 'RIO'
      ];

      const missingModels = expectedKiaModels.filter(model => !uniqueModelNames.includes(model));
      if (missingModels.length > 0) {
        console.warn(`SearchFilters: Missing KIA models (${missingModels.length}):`, missingModels.join(', '));
      } else {
        console.log('SearchFilters: All expected KIA models are present');
      }
    }
  }

  // Convert engines to the format expected by EnhancedDropdown
  const engineOptions = engines.map(engine => ({
    value: engine.fullName,
    label: engine.fullName,
    code: engine.code,
    fuel_type: engine.fuel_type,
    power_kW: engine.power_kW,
    power_HP: engine.power_HP
  }));

  console.log(`Rendering ${engineOptions.length} engine options`);

  // Group model options by model name
  const groupModelsByName = (option) => {
    // Ensure we have a valid model name
    if (!option || !option.model) {
      console.warn('Invalid option in groupModelsByName:', option);
      return 'Unknown';
    }
    return option.model;
  };

  // Group engine options by fuel type
  const groupEnginesByFuelType = (option) => {
    return option.fuel_type || 'Other';
  };

  // Custom renderer for brand options
  const renderBrandOption = (option) => (
    <div className="flex items-center gap-2">
      <div className="flex-shrink-0 w-7 h-7 rounded-md bg-gray-100 flex items-center justify-center">
        <span className="text-sm font-medium text-gray-500">
          {option.label.charAt(0)}
        </span>
      </div>
      <span className="font-medium">{option.label}</span>
    </div>
  );

  // Custom renderer for model options
  const renderModelOption = (option) => (
    <div className="flex flex-col py-0.5">
      <div className="flex items-center gap-1">
        <span className="font-medium">{option.model}</span>
        <span className="text-xs bg-gray-100 px-1 rounded">{option.generation}</span>
        <span className="text-xs text-blue-600">{option.bodyType}</span>
      </div>
      <span className="text-xs text-muted-foreground">{option.dateRange}</span>
    </div>
  );

  // Custom renderer for engine options
  const renderEngineOption = (option) => {
    // Extract engine details from the label
    // Format: "1.4 Fire (843A1000) (70 kW / 95 HP) (10.2015–...)"
    const engineNameMatch = option.label.match(/^(.*?)\s*\(/);
    const engineName = engineNameMatch ? engineNameMatch[1].trim() : '';

    const codeMatch = option.label.match(/\(([^)]+)\)/);
    const engineCode = codeMatch ? codeMatch[1] : '';

    const powerMatch = option.label.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
    const power = powerMatch ? `${powerMatch[1]} kW / ${powerMatch[2]} HP` : '';

    const dateMatch = option.label.match(/\(([^)]+)\)$/);
    const dateRange = dateMatch ? dateMatch[1] : '';

    return (
      <div className="flex flex-col py-0.5">
        <div className="flex items-center gap-1">
          <span className="font-medium text-sm">{engineName}</span>
          <span className="text-xs bg-gray-100 px-1 rounded">{engineCode}</span>
        </div>
        <div className="flex items-center gap-2 text-xs">
          <span className="text-blue-600">{power}</span>
          <span className="text-gray-500">{dateRange}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="space-y-2">
        <Label htmlFor="brand-select">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-orange-500 text-white mr-2">
              1
            </div>
            <span>Select car brand</span>
          </div>
        </Label>
        <EnhancedDropdown
          options={brandOptions}
          value={filters.brand}
          onChange={(value) => onFilterChange('brand', value)}
          placeholder="Search brands..."
          searchPlaceholder="Search for a brand..."
          renderOption={renderBrandOption}
          emptyMessage="No brands found"
          maxHeight="200px"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="model-select">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2">
              2
            </div>
            <span>Select Model</span>
          </div>
        </Label>
        <EnhancedDropdown
          options={modelOptions}
          value={filters.model}
          onChange={(value) => onFilterChange('model', value)}
          placeholder="Search models..."
          searchPlaceholder="Search for a model..."
          renderOption={renderModelOption}
          groupBy={groupModelsByName}
          emptyMessage="No models found"
          disabled={!filters.brand}
          maxHeight="200px"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="engine-select">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2">
              3
            </div>
            <span>Select engine (type)</span>
          </div>
        </Label>
        <EnhancedDropdown
          options={engineOptions}
          value={filters.engineType}
          onChange={(value) => onFilterChange('engineType', value)}
          placeholder="Search engine types..."
          searchPlaceholder="Search for an engine..."
          renderOption={renderEngineOption}
          groupBy={groupEnginesByFuelType}
          emptyMessage="No engines found"
          disabled={!filters.model}
          maxHeight="200px"
        />
      </div>
    </div>
  );
};

export default SearchFilters;
