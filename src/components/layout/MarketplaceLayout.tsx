import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { MarketplaceHeader } from '@/components/marketplace/MarketplaceHeader';
import { useConsumerAuth } from '@/components/auth/ConsumerAuthGuard';
import { Truck } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface MarketplaceLayoutProps {
  children: React.ReactNode;
}

export function MarketplaceLayout({ children }: MarketplaceLayoutProps) {
  const { t } = useTranslation();
  const { isAuthenticated } = useConsumerAuth();

  // Handle Track Your Order button click
  const handleTrackOrderClick = (e: React.MouseEvent) => {
    e.preventDefault();

    if (isAuthenticated) {
      // User is authenticated, navigate to orders page
      window.location.href = '/my-orders';
    } else {
      // User is not authenticated, trigger auth modal
      const authModal = document.getElementById('auth-modal-trigger');
      if (authModal) {
        authModal.click();
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      {/* Marketplace Header */}
      <MarketplaceHeader />

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Exceptional AutoZone-Inspired Footer */}
      <footer className="bg-[#071c44] text-white">
        {/* Top Action Bar */}
        <div className="bg-[#071c44] border-b border-white/10">
          <div className="container py-4">
            <div className="flex flex-col md:flex-row items-center justify-center md:justify-start gap-4">
              {/* Track Your Order Button */}
              <button
                onClick={handleTrackOrderClick}
                className="flex items-center gap-3 bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-lg transition-all duration-200 font-medium text-sm group"
              >
                <Truck className="h-5 w-5 group-hover:scale-110 transition-transform" />
                {t('footer.trackYourOrder')}
              </button>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="container py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">

            {/* SHOP Section */}
            <div>
              <h3 className="text-white font-bold text-sm uppercase tracking-wide mb-4">
                {t('footer.shop')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/my-vehicle-parts" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('marketplace.myVehicleParts')}
                  </Link>
                </li>
                <li>
                  <Link to="/wholesale-offers" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('marketplace.wholesaleOffers')}
                  </Link>
                </li>
                <li>
                  <Link to="/partners" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('marketplace.partners')}
                  </Link>
                </li>
                <li>
                  <Link to="/basket" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.basket')}
                  </Link>
                </li>
              </ul>
            </div>

            {/* PRODUCTS Section */}
            <div>
              <h3 className="text-white font-bold text-sm uppercase tracking-wide mb-4">
                {t('footer.products')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/my-vehicle-parts?category=tyres" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.tyres')}
                  </Link>
                </li>
                <li>
                  <Link to="/my-vehicle-parts?category=oils" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.oilsFluids')}
                  </Link>
                </li>
                <li>
                  <Link to="/my-vehicle-parts?category=batteries" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.batteries')}
                  </Link>
                </li>
                <li>
                  <Link to="/my-vehicle-parts?category=filters" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.filters')}
                  </Link>
                </li>
              </ul>
            </div>

            {/* HELP Section */}
            <div>
              <h3 className="text-white font-bold text-sm uppercase tracking-wide mb-4">
                {t('footer.help')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/my-orders" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.trackMyOrder')}
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.contactUs')}
                  </Link>
                </li>
                <li>
                  <Link to="/my-orders" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.myAccount')}
                  </Link>
                </li>
                <li>
                  <Link to="/help" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.faq')}
                  </Link>
                </li>
              </ul>
            </div>

            {/* ABOUT US Section */}
            <div>
              <h3 className="text-white font-bold text-sm uppercase tracking-wide mb-4">
                {t('footer.aboutUs')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/about" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.aboutArouz')}
                  </Link>
                </li>
                <li>
                  <Link to="/careers" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.careers')}
                  </Link>
                </li>
                <li>
                  <Link to="/investor-relations" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.investorRelations')}
                  </Link>
                </li>
                <li>
                  <Link to="/corporate-responsibility" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.corporateResponsibility')}
                  </Link>
                </li>
              </ul>
            </div>

            {/* SERVICES Section */}
            <div>
              <h3 className="text-white font-bold text-sm uppercase tracking-wide mb-4">
                {t('footer.services')}
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/delivery-info" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.deliveryInfo')}
                  </Link>
                </li>
                <li>
                  <Link to="/return-policy" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.returnPolicy')}
                  </Link>
                </li>
                <li>
                  <Link to="/warranty" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.warranty')}
                  </Link>
                </li>
                <li>
                  <Link to="/installation-services" className="text-gray-300 hover:text-white text-sm transition-colors">
                    {t('footer.installationServices')}
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Social Media Section */}
          <div className="mt-12 pt-8 border-t border-white/10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">

              {/* Logo and Description */}
              <div className="flex flex-col items-center md:items-start">
                <Link to="/" className="flex items-center mb-4">
                  <div className="relative" style={{ height: '3rem', width: '5rem', overflow: 'visible' }}>
                    <img
                      src="/images/logohome.png"
                      alt="AROUZ MARKET Logo"
                      className="w-auto h-12 md:h-20 object-contain absolute top-1/2 left-0 transform -translate-y-1/2"
                      style={{
                        filter: 'drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.12))',
                        transform: 'translateY(-50%) scale(1.35)',
                        transformOrigin: 'left center',
                        maxHeight: '140%'
                      }}
                      draggable="false"
                      onDragStart={(e) => e.preventDefault()}
                    />
                  </div>
                </Link>
              </div>

              {/* Social Media Icons */}
              <div className="flex items-center gap-4">
                <span className="text-gray-300 text-sm font-medium mr-2">{t('footer.followUs')}:</span>

                {/* Facebook */}
                <a
                  href="https://www.facebook.com/share/14GSivw2XzW/?mibextid=wwXIfr"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white/10 hover:bg-[#1877f2] text-white p-3 rounded-full transition-all duration-200 group"
                >
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>

                {/* LinkedIn */}
                <a
                  href="https://www.linkedin.com/company/arouzmarket/about/?viewAsMember=true"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white/10 hover:bg-[#0077b5] text-white p-3 rounded-full transition-all duration-200 group"
                >
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Copyright Bar */}
        <div className="bg-[#051426] border-t border-white/10">
          <div className="container py-4">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4 text-sm text-gray-400">
              <div className="flex flex-wrap items-center gap-4">
                <span>© {new Date().getFullYear()} AROUZ MARKET. {t('public.allRightsReserved')}</span>
                <Link to="/privacy-policy" className="hover:text-white transition-colors">
                  {t('footer.privacyPolicy')}
                </Link>
                <Link to="/terms-of-service" className="hover:text-white transition-colors">
                  {t('footer.termsOfService')}
                </Link>
                <Link to="/sitemap" className="hover:text-white transition-colors">
                  {t('footer.sitemap')}
                </Link>
              </div>
              <div className="text-gray-500 text-xs">
                {t('footer.poweredBy')} AROUZ MARKET
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
