
import React, { useState, useEffect } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const isMobile = useIsMobile();
  const [isSidebarOpen, setSidebarOpen] = useState(!isMobile);

  // Update sidebar state when screen size changes
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  const handleToggleSidebar = (isOpen: boolean) => {
    setSidebarOpen(isOpen);
  };

  return (
    <div className={cn(
      "flex min-h-screen bg-background",
      isRTL ? 'rtl' : 'ltr'
    )}>
      <Sidebar onToggle={handleToggleSidebar} />
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 ease-in-out",
        isMobile
          ? "ml-0 mr-0"
          : isRTL
            ? isSidebarOpen ? "mr-[280px]" : "mr-[70px]"
            : isSidebarOpen ? "ml-[280px]" : "ml-[70px]",
        "w-full p-0" // Added p-0 to ensure no padding
      )}>
        <Header />
        <main className="flex-1 p-4 md:p-6 lg:p-8 overflow-x-hidden bg-muted/10">
          <div className="max-w-7xl mx-auto w-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
