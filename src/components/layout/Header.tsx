
import { Bell, Search, Menu, ChevronDown, LogOut, Building, Store, User, Settings, Clock, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { LanguageSelector } from '@/components/common/LanguageSelector';
import { RoleIndicator } from '@/components/common/RoleIndicator';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useUser } from '@/contexts/UserContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuGroup
} from '@/components/ui/dropdown-menu';
import { useState, useEffect } from 'react';
import { LogoutConfirmationModal } from '@/components/common/LogoutConfirmationModal';
import { Link } from 'react-router-dom';
import { getUserProfile, UserProfile } from '@/services/authService';
import { getCurrentUser } from '@/services/authService';
import { format } from 'date-fns';

export function Header() {
  const { t, i18n } = useTranslation();
  const { userRole, isSupplier, isMerchant, supplierRole, merchantRole } = useUser();
  const isRTL = i18n.language === 'ar';
  const isMobile = useIsMobile();
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [logoutRole, setLogoutRole] = useState<'supplier' | 'merchant' | undefined>(undefined);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if user has multiple roles active
  const hasMultipleRoles = supplierRole && merchantRole;

  // Load user profile
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setLoading(true);
        const { data } = await getCurrentUser();

        if (data.user) {
          const { profile } = await getUserProfile(data.user.id);
          setUserProfile(profile);
        } else {
          // No user found, clear profile
          setUserProfile(null);
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
        setUserProfile(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, []);

  return (
    <header className={cn(
      "sticky top-0 z-30 flex h-20 items-center gap-4 border-b bg-background/95 backdrop-blur-sm",
      "px-4 md:px-6 lg:px-8",
      "shadow-sm"
    )}>
      {/* Mobile menu button is now in the Sidebar component */}

      <div className="hidden md:flex w-full max-w-sm relative">
        <Search className={cn(
          "absolute top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground",
          isRTL ? "right-3" : "left-3"
        )} />
        <Input
          type="search"
          placeholder={t('products.search')}
          className={cn(
            "w-full bg-muted/50 border-none rounded-full",
            "focus-visible:ring-1 focus-visible:ring-primary/50",
            isRTL ? "pr-9 pl-3" : "pl-9 pr-3"
          )}
          style={{ unicodeBidi: 'isolate' }}
        />
      </div>

      <div className="flex flex-1 items-center justify-end gap-3">
        <RoleIndicator />

        <LanguageSelector />

        <Button variant="ghost" size="icon" className="relative hidden sm:flex">
          <Bell className="h-5 w-5" />
          <span className="sr-only" style={{ unicodeBidi: 'isolate' }}>{t('notifications')}</span>
          <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-racing-red animate-pulse" />
        </Button>

        <div className={cn(
          "flex items-center gap-3",
          "sm:border-l sm:border-r-0 sm:pl-4 sm:pr-0",
          isRTL && "sm:border-r sm:border-l-0 sm:pr-4 sm:pl-0"
        )}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 cursor-pointer">
                <Avatar className="h-10 w-10 border-2 border-electric-orange/20 shadow-sm">
                  <AvatarImage src={userProfile?.avatar_url || ""} alt="Avatar" />
                  <AvatarFallback className="bg-gradient-to-br from-midnight-blue to-primary text-white font-bold">
                    {userProfile?.full_name
                      ? userProfile.full_name.charAt(0).toUpperCase()
                      : isSupplier()
                        ? 'S'
                        : isMerchant()
                          ? 'M'
                          : 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:flex flex-col">
                  <span className="text-sm font-semibold text-primary" style={{ unicodeBidi: 'isolate' }}>
                    {isSupplier()
                      ? userProfile?.company_name || t('roles.supplierAndManufacturer')
                      : isMerchant()
                        ? userProfile?.store_name || t('roles.merchantRetailer')
                        : userProfile?.full_name || t('roles.user')}
                  </span>
                  <span className="text-xs text-muted-foreground" style={{ unicodeBidi: 'isolate' }}>
                    {userProfile?.email || 'No email'}
                  </span>
                </div>
                <ChevronDown className="h-4 w-4 text-muted-foreground hidden md:block" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {userProfile?.full_name || t('settings.yourAccount')}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {userProfile?.email || 'No email'}
                  </p>
                  {userProfile?.last_login && (
                    <p className="text-xs text-muted-foreground flex items-center mt-1">
                      <Clock className="h-3 w-3 mr-1" />
                      {t('settings.lastLogin')}: {format(new Date(userProfile.last_login), 'PPp')}
                    </p>
                  )}
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                  <Link to="/app/settings" className="flex items-center gap-2 cursor-pointer">
                    <User className="h-4 w-4" />
                    {t('settings.profile')}
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <Link to="/app/settings" className="flex items-center gap-2 cursor-pointer">
                    <Settings className="h-4 w-4" />
                    {t('settings.accountSettings')}
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuGroup>

              <DropdownMenuSeparator />

              {/* Show role-specific logout options if user has multiple roles */}
              {hasMultipleRoles && (
                <>
                  <DropdownMenuItem
                    className="flex items-center gap-2 cursor-pointer text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={() => {
                      setLogoutRole('supplier');
                      setIsLogoutModalOpen(true);
                    }}
                  >
                    <Building className="h-4 w-4" />
                    {t('auth.logoutFromSupplier')}
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    className="flex items-center gap-2 cursor-pointer text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={() => {
                      setLogoutRole('merchant');
                      setIsLogoutModalOpen(true);
                    }}
                  >
                    <Store className="h-4 w-4" />
                    {t('auth.logoutFromMerchant')}
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />
                </>
              )}

              {/* Complete logout option */}
              <DropdownMenuItem
                className="flex items-center gap-2 cursor-pointer text-destructive hover:text-destructive hover:bg-destructive/10"
                onClick={() => {
                  setLogoutRole(undefined);
                  setIsLogoutModalOpen(true);
                }}
              >
                <LogOut className="h-4 w-4" />
                {t('navigation.logout')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Logout confirmation modal */}
          <LogoutConfirmationModal
            isOpen={isLogoutModalOpen}
            onClose={() => setIsLogoutModalOpen(false)}
            role={logoutRole}
          />
        </div>
      </div>
    </header>
  );
}
