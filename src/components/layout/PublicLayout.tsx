import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

interface PublicLayoutProps {
  children: React.ReactNode;
}

export function PublicLayout({ children }: PublicLayoutProps) {
  const { t } = useTranslation();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 py-2 shadow-sm">
        <div className="container flex h-16 items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <img
              src="/images/AROUZ Header Logo.png"
              alt="AROUZ MARKET Logo"
              className="h-16 w-auto transition-transform hover:scale-105 object-contain"
            />
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <Link
              to="/"
              className={cn(
                "text-sm font-medium transition-colors hover:text-electric-orange",
                isActive('/') ? "text-electric-orange" : "text-foreground"
              )}
            >
              {t('public.home')}
            </Link>
            <Link
              to="/about"
              className={cn(
                "text-sm font-medium transition-colors hover:text-electric-orange",
                isActive('/about') ? "text-electric-orange" : "text-foreground"
              )}
            >
              {t('public.about')}
            </Link>
            <Link
              to="/features"
              className={cn(
                "text-sm font-medium transition-colors hover:text-electric-orange",
                isActive('/features') ? "text-electric-orange" : "text-foreground"
              )}
            >
              {t('public.features')}
            </Link>
            <Link
              to="/roadmap"
              className={cn(
                "text-sm font-medium transition-colors hover:text-electric-orange",
                isActive('/roadmap') ? "text-electric-orange" : "text-foreground"
              )}
            >
              {t('public.roadmap')}
            </Link>
            <Link
              to="/contact"
              className={cn(
                "text-sm font-medium transition-colors hover:text-electric-orange",
                isActive('/contact') ? "text-electric-orange" : "text-foreground"
              )}
            >
              {t('public.contact')}
            </Link>
          </nav>

          {/* CTA Button */}
          <div>
            <Button
              className="bg-electric-orange hover:bg-electric-orange/90 text-white"
              asChild
            >
              <Link to="/#waitlist">
                {t('public.joinWaitlist')}
              </Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-muted/40">
        <div className="container py-12 md:py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <Link to="/" className="flex items-center">
                <img
                  src="/images/AROUZ Header Logo.png"
                  alt="AROUZ MARKET Logo"
                  className="h-14 w-auto object-contain"
                />
              </Link>
              <p className="mt-4 text-muted-foreground max-w-md">
                {t('public.footerTagline')}
              </p>
              <p className="mt-4 text-sm text-muted-foreground">
                © {new Date().getFullYear()} AROUZ MARKET. {t('public.allRightsReserved')}
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">{t('public.navigation')}</h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/" className="text-sm text-muted-foreground hover:text-electric-orange">
                    {t('public.home')}
                  </Link>
                </li>
                <li>
                  <Link to="/about" className="text-sm text-muted-foreground hover:text-electric-orange">
                    {t('public.about')}
                  </Link>
                </li>
                <li>
                  <Link to="/features" className="text-sm text-muted-foreground hover:text-electric-orange">
                    {t('public.features')}
                  </Link>
                </li>
                <li>
                  <Link to="/roadmap" className="text-sm text-muted-foreground hover:text-electric-orange">
                    {t('public.roadmap')}
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="text-sm text-muted-foreground hover:text-electric-orange">
                    {t('public.contact')}
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">{t('public.contact')}</h3>
              <ul className="space-y-2">
                <li className="text-sm text-muted-foreground">
                  <span className="font-medium">Email:</span> <EMAIL>
                </li>
                <li className="text-sm text-muted-foreground">
                  <span className="font-medium">{t('public.address')}:</span> Algiers, Algeria
                </li>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
