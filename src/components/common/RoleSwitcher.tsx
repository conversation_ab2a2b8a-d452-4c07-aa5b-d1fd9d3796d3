import React from 'react';
import { useUser, UserRole } from '@/contexts/UserContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Building, Store, User, Truck, ChevronDown } from 'lucide-react';

export function RoleSwitcher() {
  const { userRole, setUserRole, isRoleSwitcherVisible } = useUser();

  if (!isRoleSwitcherVisible) {
    return null;
  }

  const roleOptions: { role: UserRole; label: string; icon: React.ReactNode }[] = [
    { role: 'supplier', label: 'Supplier', icon: <Building className="h-4 w-4 mr-2" /> },
    { role: 'merchant', label: 'Merchant', icon: <Store className="h-4 w-4 mr-2" /> },
    { role: 'consumer', label: 'Consumer', icon: <User className="h-4 w-4 mr-2" /> },
    { role: 'distribution', label: 'Distribution', icon: <Truck className="h-4 w-4 mr-2" /> },
  ];

  const currentRoleOption = roleOptions.find(option => option.role === userRole);

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white shadow-lg rounded-lg p-2 border border-gray-200">
      <div className="text-xs text-gray-500 mb-1">Development Mode</div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            {currentRoleOption?.icon}
            {currentRoleOption?.label}
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {roleOptions.map(option => (
            <DropdownMenuItem
              key={option.role}
              onClick={() => setUserRole(option.role)}
              className="flex items-center cursor-pointer"
            >
              {option.icon}
              {option.label}
              {option.role === userRole && (
                <span className="ml-2 h-2 w-2 rounded-full bg-green-500" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
