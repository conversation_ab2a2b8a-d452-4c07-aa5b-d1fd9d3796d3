import React, { useState, useEffect } from 'react';
import { useUser, UserRole } from '@/contexts/UserContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { 
  Building, 
  Store, 
  User, 
  Truck, 
  ChevronDown, 
  Eye, 
  EyeOff,
  Info
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export function GlobalRoleSwitcher() {
  const { userRole, setUserRole, isRoleSwitcherVisible } = useUser();
  const [isMinimized, setIsMinimized] = useState(() => {
    const storedState = localStorage.getItem('roleSwitcherMinimized');
    return storedState ? JSON.parse(storedState) : false;
  });

  // Only show in development mode
  if (!import.meta.env.DEV || !isRoleSwitcherVisible) {
    return null;
  }

  const roleOptions: { 
    role: UserRole; 
    label: string; 
    icon: React.ReactNode;
    description: string;
  }[] = [
    { 
      role: 'supplier', 
      label: 'Supplier', 
      icon: <Building className="h-4 w-4 mr-2" />,
      description: 'Access wholesale pricing tiers and B2B features'
    },
    { 
      role: 'merchant', 
      label: 'Merchant', 
      icon: <Store className="h-4 w-4 mr-2" />,
      description: 'Access retail pricing and B2C features'
    },
    { 
      role: 'consumer', 
      label: 'Consumer', 
      icon: <User className="h-4 w-4 mr-2" />,
      description: 'Standard customer account features'
    },
    { 
      role: 'distribution', 
      label: 'Distribution', 
      icon: <Truck className="h-4 w-4 mr-2" />,
      description: 'Distribution network features'
    },
  ];

  const currentRoleOption = roleOptions.find(option => option.role === userRole);

  // Toggle minimized state
  const toggleMinimized = () => {
    const newState = !isMinimized;
    setIsMinimized(newState);
    localStorage.setItem('roleSwitcherMinimized', JSON.stringify(newState));
  };

  return (
    <div className={`fixed bottom-4 right-4 z-[9999] bg-white shadow-lg rounded-lg ${isMinimized ? 'p-2' : 'p-3'} border border-gray-200 transition-all duration-300`}>
      {!isMinimized && (
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-green-500 mr-2 animate-pulse"></div>
            <div className="text-xs text-gray-500 font-medium">Development Mode</div>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6" 
                  onClick={toggleMinimized}
                >
                  <EyeOff className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Minimize role switcher</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}
      
      <div className="flex items-center gap-2">
        {isMinimized ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6" 
                  onClick={toggleMinimized}
                >
                  <Eye className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Expand role switcher</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : null}
        
        <DropdownMenu>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    className={`flex items-center gap-2 ${isMinimized ? 'h-6 px-2 text-xs' : ''}`}
                    size={isMinimized ? "sm" : "default"}
                  >
                    {currentRoleOption?.icon}
                    {!isMinimized && (
                      <>
                        {currentRoleOption?.label}
                        <ChevronDown className="h-4 w-4" />
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Switch between user roles for testing</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <DropdownMenuContent align="end" className="w-64">
            <div className="px-2 py-1.5 text-xs text-muted-foreground">
              Switch user role for testing
            </div>
            <DropdownMenuSeparator />
            {roleOptions.map(option => (
              <DropdownMenuItem
                key={option.role}
                onClick={() => setUserRole(option.role)}
                className="flex items-start gap-2 p-2 cursor-pointer"
              >
                <div className="flex-shrink-0 mt-0.5">
                  {option.icon}
                </div>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {option.label}
                    {option.role === userRole && (
                      <span className="ml-2 h-2 w-2 rounded-full bg-green-500 inline-block"></span>
                    )}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {option.description}
                  </span>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
