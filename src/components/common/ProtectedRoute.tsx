import React from 'react';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * ProtectedRoute component
 *
 * During pre-launch phase, this component can either allow or restrict access
 * to product routes based on development needs.
 *
 * Later, this can be updated to check for authentication and permissions.
 */
export function ProtectedRoute({ children }: ProtectedRouteProps) {
  // Check if product features should be enabled based on environment variable
  // This can be controlled via .env files or deployment configuration
  const enableProductFeatures = import.meta.env.VITE_ENABLE_PRODUCT_FEATURES === 'true';

  if (enableProductFeatures) {
    // During development/demos, allow access to product features
    return <>{children}</>;
  } else {
    // In pre-launch phase, redirect all product route access to home page
    return <Navigate to="/" replace />;
  }

  // After launch, this can be updated to:
  // return isAuthenticated ? children : <Navigate to="/login" replace />;
}
