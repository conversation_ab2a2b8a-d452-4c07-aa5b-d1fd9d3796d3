
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Briefcase, ShoppingBag, User, Truck, Factory } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

type AdminRole = 'manufacturer' | 'merchant' | 'consumer' | 'distribution';

interface AdminRoleSelectorProps {
  currentRole?: AdminRole;
  onRoleChange?: (role: AdminRole) => void;
}

export function AdminRoleSelector({ 
  currentRole = 'manufacturer', 
  onRoleChange 
}: AdminRoleSelectorProps) {
  const { t } = useTranslation();

  const handleRoleChange = (role: AdminRole) => {
    if (onRoleChange) {
      onRoleChange(role);
    }
  };

  const roles = [
    { 
      id: 'manufacturer' as AdminRole, 
      label: t('roles.manufacturer'), 
      icon: Factory,
      description: t('roles.manufacturerDescription')
    },
    { 
      id: 'merchant' as AdminRole, 
      label: t('roles.merchant'), 
      icon: ShoppingBag,
      description: t('roles.merchantDescription')
    },
    { 
      id: 'consumer' as AdminRole, 
      label: t('roles.consumer'), 
      icon: User,
      description: t('roles.consumerDescription')
    },
    { 
      id: 'distribution' as AdminRole, 
      label: t('roles.distribution'), 
      icon: Truck,
      description: t('roles.distributionDescription')
    }
  ];

  const currentRoleData = roles.find(role => role.id === currentRole) || roles[0];
  const CurrentRoleIcon = currentRoleData.icon;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={cn(
            "flex items-center gap-2 border-electric-orange/20 bg-electric-orange/5",
            "hover:bg-electric-orange/10 hover:text-electric-orange"
          )}
          style={{ unicodeBidi: 'isolate' }}
        >
          <CurrentRoleIcon className="h-4 w-4 text-electric-orange flex-shrink-0" />
          <span className="hidden sm:inline-block" style={{ unicodeBidi: 'isolate' }}>{currentRoleData.label}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {roles.map((role) => {
          const RoleIcon = role.icon;
          const isActive = role.id === currentRole;
          
          return (
            <DropdownMenuItem 
              key={role.id}
              onClick={() => handleRoleChange(role.id)}
              className={cn(
                "flex items-start gap-2 p-3",
                isActive && "bg-electric-orange/5"
              )}
            >
              <RoleIcon className={cn(
                "h-4 w-4 mt-0.5 flex-shrink-0",
                isActive ? "text-electric-orange" : "text-gray-500"
              )} />
              <div className="flex flex-col">
                <span className={cn(
                  "font-medium",
                  isActive && "text-electric-orange"
                )}
                  style={{ unicodeBidi: 'isolate' }}
                >
                  {role.label}
                </span>
                <span className="text-xs text-muted-foreground" style={{ unicodeBidi: 'isolate' }}>
                  {role.description}
                </span>
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
