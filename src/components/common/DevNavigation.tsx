import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  ChevronUp,
  Home,
  LayoutDashboard,
  Database,
  Settings,
  Info,
  FileText,
  Clock,
  Phone
} from 'lucide-react';

/**
 * Development Navigation Component
 *
 * This component provides quick navigation between public website and product features
 * during development. It should be removed or disabled in production.
 */
export function DevNavigation() {
  const [isOpen, setIsOpen] = useState(false);

  // Only show in development mode
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="flex flex-col items-end">
        {isOpen && (
          <div className="mb-2 bg-white rounded-lg shadow-lg border border-border p-4 w-64">
            <div className="mb-4">
              <h3 className="font-medium text-sm text-muted-foreground mb-2">PUBLIC WEBSITE</h3>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/">
                    <Home className="h-4 w-4 mr-2" />
                    Home
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/about">
                    <Info className="h-4 w-4 mr-2" />
                    About
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/features">
                    <FileText className="h-4 w-4 mr-2" />
                    Features
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/roadmap">
                    <Clock className="h-4 w-4 mr-2" />
                    Roadmap
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/contact">
                    <Phone className="h-4 w-4 mr-2" />
                    Contact
                  </Link>
                </Button>
              </div>
            </div>

            <div>
              <h3 className="font-medium text-sm text-muted-foreground mb-2">ADMIN PANEL</h3>
              <div className="grid grid-cols-1 gap-2">
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/app/dashboard">
                    <LayoutDashboard className="h-4 w-4 mr-2" />
                    Dashboard
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/app/products-table">
                    <Database className="h-4 w-4 mr-2" />
                    Products Data Grid
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="justify-start">
                  <Link to="/app/products">
                    <Database className="h-4 w-4 mr-2" />
                    Products List
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        )}

        <Button
          onClick={() => setIsOpen(!isOpen)}
          className="bg-midnight-blue hover:bg-midnight-blue/90 text-white"
        >
          {isOpen ? (
            <>
              <ChevronDown className="h-4 w-4 mr-2" />
              Close Dev Menu
            </>
          ) : (
            <>
              <ChevronUp className="h-4 w-4 mr-2" />
              Dev Menu
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
