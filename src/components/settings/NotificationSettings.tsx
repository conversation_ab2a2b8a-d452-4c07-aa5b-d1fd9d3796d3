import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Switch } from '@/components/ui/switch';
import { Loader2, Bell, Mail, MessageSquare, ShoppingCart, Tag, AlertCircle } from 'lucide-react';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  email: boolean;
  push: boolean;
  sms: boolean;
  icon: React.ReactNode;
}

export function NotificationSettings() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  
  // Notification settings
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'order_updates',
      title: t('settings.orderUpdates'),
      description: t('settings.orderUpdatesDescription'),
      email: true,
      push: true,
      sms: false,
      icon: <ShoppingCart className="h-5 w-5 text-gray-500" />,
    },
    {
      id: 'price_alerts',
      title: t('settings.priceAlerts'),
      description: t('settings.priceAlertsDescription'),
      email: true,
      push: false,
      sms: false,
      icon: <Tag className="h-5 w-5 text-gray-500" />,
    },
    {
      id: 'inventory_alerts',
      title: t('settings.inventoryAlerts'),
      description: t('settings.inventoryAlertsDescription'),
      email: true,
      push: true,
      sms: true,
      icon: <AlertCircle className="h-5 w-5 text-gray-500" />,
    },
    {
      id: 'messages',
      title: t('settings.messages'),
      description: t('settings.messagesDescription'),
      email: true,
      push: true,
      sms: false,
      icon: <MessageSquare className="h-5 w-5 text-gray-500" />,
    },
  ]);
  
  // Update notification setting
  const updateSetting = (id: string, channel: 'email' | 'push' | 'sms', value: boolean) => {
    setSettings(settings.map(setting => 
      setting.id === id ? { ...setting, [channel]: value } : setting
    ));
  };
  
  // Save notification settings
  const saveSettings = async () => {
    setIsSaving(true);
    
    // This would be implemented with actual API call
    // For now, we'll just simulate it
    setTimeout(() => {
      setIsSaving(false);
      
      toast({
        title: t('settings.notificationSettingsSaved'),
        description: t('settings.notificationSettingsSavedDescription'),
      });
    }, 1000);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t('settings.notificationSettings')}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {t('settings.notificationSettingsDescription')}
        </p>
      </div>
      
      <Separator />
      
      <div className="space-y-6">
        {/* Channel headers */}
        <div className="grid grid-cols-4 gap-4 pb-2">
          <div></div>
          <div className="text-sm font-medium text-center">
            <div className="flex items-center justify-center gap-2">
              <Mail className="h-4 w-4" />
              {t('settings.email')}
            </div>
          </div>
          <div className="text-sm font-medium text-center">
            <div className="flex items-center justify-center gap-2">
              <Bell className="h-4 w-4" />
              {t('settings.push')}
            </div>
          </div>
          <div className="text-sm font-medium text-center">
            <div className="flex items-center justify-center gap-2">
              <MessageSquare className="h-4 w-4" />
              {t('settings.sms')}
            </div>
          </div>
        </div>
        
        {/* Notification settings */}
        {settings.map((setting) => (
          <div key={setting.id} className="grid grid-cols-4 gap-4 py-4 border-t">
            <div className="flex items-start gap-3">
              <div className="mt-0.5">{setting.icon}</div>
              <div>
                <h4 className="text-sm font-medium">{setting.title}</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  {setting.description}
                </p>
              </div>
            </div>
            <div className="flex justify-center items-center">
              <Switch
                checked={setting.email}
                onCheckedChange={(checked) => updateSetting(setting.id, 'email', checked)}
              />
            </div>
            <div className="flex justify-center items-center">
              <Switch
                checked={setting.push}
                onCheckedChange={(checked) => updateSetting(setting.id, 'push', checked)}
              />
            </div>
            <div className="flex justify-center items-center">
              <Switch
                checked={setting.sms}
                onCheckedChange={(checked) => updateSetting(setting.id, 'sms', checked)}
              />
            </div>
          </div>
        ))}
      </div>
      
      <div className="pt-4">
        <Button 
          onClick={saveSettings} 
          disabled={isSaving}
          className="bg-electric-orange hover:bg-electric-orange/90"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('settings.saving')}
            </>
          ) : (
            t('settings.saveChanges')
          )}
        </Button>
      </div>
    </div>
  );
}
