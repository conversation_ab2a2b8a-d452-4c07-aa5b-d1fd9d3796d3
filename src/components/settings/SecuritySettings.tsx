import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Switch } from '@/components/ui/switch';
import { Loader2, Eye, EyeOff, Lock, Shield, AlertTriangle } from 'lucide-react';
import { updatePassword, checkPasswordStrength, sendEmailVerification } from '@/services/authService';
import { PasswordStrengthIndicator } from '@/components/auth/PasswordStrengthIndicator';

export function SecuritySettings() {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  // Password change states
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  
  // Two-factor authentication states
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [isEnablingTwoFactor, setIsEnablingTwoFactor] = useState(false);
  
  // Email verification states
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  
  // Handle password change
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate passwords
    if (newPassword !== confirmPassword) {
      toast({
        title: t('settings.passwordMismatch'),
        description: t('settings.passwordMismatchDescription'),
        variant: 'destructive',
      });
      return;
    }
    
    // Check password strength
    const passwordCheck = checkPasswordStrength(newPassword);
    if (!passwordCheck.isValid) {
      toast({
        title: t('settings.passwordTooWeak'),
        description: t('settings.passwordRequirements'),
        variant: 'destructive',
      });
      return;
    }
    
    setIsChangingPassword(true);
    try {
      const { success, error } = await updatePassword(newPassword);
      
      if (error) {
        console.error('Error updating password:', error);
        toast({
          title: t('settings.passwordChangeFailed'),
          description: error.message,
          variant: 'destructive',
        });
        return;
      }
      
      if (success) {
        toast({
          title: t('settings.passwordChanged'),
          description: t('settings.passwordChangedDescription'),
        });
        
        // Reset form
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (error) {
      console.error('Unexpected error updating password:', error);
      toast({
        title: t('settings.passwordChangeFailed'),
        description: t('settings.unexpectedError'),
        variant: 'destructive',
      });
    } finally {
      setIsChangingPassword(false);
    }
  };
  
  // Handle two-factor authentication toggle
  const handleTwoFactorToggle = async (enabled: boolean) => {
    setIsEnablingTwoFactor(true);
    
    // This would be implemented with actual two-factor authentication setup
    // For now, we'll just simulate it
    setTimeout(() => {
      setTwoFactorEnabled(enabled);
      setIsEnablingTwoFactor(false);
      
      toast({
        title: enabled ? t('settings.twoFactorEnabled') : t('settings.twoFactorDisabled'),
        description: enabled ? t('settings.twoFactorEnabledDescription') : t('settings.twoFactorDisabledDescription'),
      });
    }, 1000);
  };
  
  // Handle email verification
  const handleVerifyEmail = async () => {
    setIsVerifyingEmail(true);
    try {
      const { success, error } = await sendEmailVerification('<EMAIL>'); // Would use actual email
      
      if (error) {
        console.error('Error sending verification email:', error);
        toast({
          title: t('settings.verificationEmailFailed'),
          description: error.message,
          variant: 'destructive',
        });
        return;
      }
      
      if (success) {
        toast({
          title: t('settings.verificationEmailSent'),
          description: t('settings.verificationEmailSentDescription'),
        });
      }
    } catch (error) {
      console.error('Unexpected error sending verification email:', error);
      toast({
        title: t('settings.verificationEmailFailed'),
        description: t('settings.unexpectedError'),
        variant: 'destructive',
      });
    } finally {
      setIsVerifyingEmail(false);
    }
  };
  
  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium">{t('settings.securitySettings')}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {t('settings.securitySettingsDescription')}
        </p>
      </div>
      
      <Separator />
      
      {/* Password Change Section */}
      <div className="space-y-4">
        <div>
          <h4 className="text-md font-medium">{t('settings.changePassword')}</h4>
          <p className="text-sm text-muted-foreground mt-1">
            {t('settings.changePasswordDescription')}
          </p>
        </div>
        
        <form onSubmit={handlePasswordChange} className="space-y-4 max-w-md">
          <div className="space-y-1.5">
            <Label htmlFor="currentPassword">{t('settings.currentPassword')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                <Lock className="h-4 w-4" />
              </div>
              <Input
                id="currentPassword"
                type={showCurrentPassword ? 'text' : 'password'}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="pl-10 pr-10"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>
          
          <div className="space-y-1.5">
            <Label htmlFor="newPassword">{t('settings.newPassword')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                <Lock className="h-4 w-4" />
              </div>
              <Input
                id="newPassword"
                type={showNewPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="pl-10 pr-10"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                onClick={() => setShowNewPassword(!showNewPassword)}
              >
                {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            
            {newPassword && <PasswordStrengthIndicator password={newPassword} className="mt-2" />}
          </div>
          
          <div className="space-y-1.5">
            <Label htmlFor="confirmPassword">{t('settings.confirmPassword')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                <Lock className="h-4 w-4" />
              </div>
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="pl-10 pr-10"
                required
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {newPassword && confirmPassword && newPassword !== confirmPassword && (
              <p className="text-xs text-red-500 mt-1">
                {t('settings.passwordMismatchDescription')}
              </p>
            )}
          </div>
          
          <Button 
            type="submit" 
            disabled={isChangingPassword || !currentPassword || !newPassword || !confirmPassword}
            className="bg-electric-orange hover:bg-electric-orange/90"
          >
            {isChangingPassword ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('settings.changing')}
              </>
            ) : (
              t('settings.updatePassword')
            )}
          </Button>
        </form>
      </div>
      
      <Separator />
      
      {/* Two-Factor Authentication Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-md font-medium">{t('settings.twoFactorAuthentication')}</h4>
            <p className="text-sm text-muted-foreground mt-1">
              {t('settings.twoFactorAuthenticationDescription')}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {isEnablingTwoFactor ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Switch
                checked={twoFactorEnabled}
                onCheckedChange={handleTwoFactorToggle}
              />
            )}
          </div>
        </div>
      </div>
      
      <Separator />
      
      {/* Email Verification Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-md font-medium">{t('settings.emailVerification')}</h4>
            <p className="text-sm text-muted-foreground mt-1">
              {t('settings.emailVerificationDescription')}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleVerifyEmail}
            disabled={isVerifyingEmail}
            className="gap-2"
          >
            {isVerifyingEmail ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Shield className="h-4 w-4" />
            )}
            {t('settings.verifyEmail')}
          </Button>
        </div>
      </div>
      
      <Separator />
      
      {/* Account Activity Section */}
      <div className="space-y-4">
        <div>
          <h4 className="text-md font-medium">{t('settings.accountActivity')}</h4>
          <p className="text-sm text-muted-foreground mt-1">
            {t('settings.accountActivityDescription')}
          </p>
        </div>
        
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 flex gap-3 items-start max-w-md">
          <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
          <div>
            <h5 className="text-sm font-medium text-amber-800">{t('settings.securityTip')}</h5>
            <p className="text-xs text-amber-700 mt-1">
              {t('settings.securityTipDescription')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
