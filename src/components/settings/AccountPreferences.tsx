import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Globe, Clock, Monitor } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "lucide-react";

export function AccountPreferences() {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  // Preferences state
  const [language, setLanguage] = useState(i18n.language || 'en');
  const [timezone, setTimezone] = useState('Africa/Algiers');
  const [theme, setTheme] = useState('light');
  const [dateFormat, setDateFormat] = useState('DD/MM/YYYY');
  const [autoTranslate, setAutoTranslate] = useState(true);

  // Save preferences
  const savePreferences = async () => {
    setIsSaving(true);

    // Change language if needed
    if (language !== i18n.language) {
      await i18n.changeLanguage(language);
    }

    // This would be implemented with actual API call for other preferences
    // For now, we'll just simulate it
    setTimeout(() => {
      setIsSaving(false);

      toast({
        title: t('settings.preferencesSaved'),
        description: t('settings.preferencesSavedDescription'),
      });
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t('settings.accountPreferences')}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {t('settings.accountPreferencesDescription')}
        </p>
      </div>

      <Separator />

      <div className="space-y-6 max-w-md">
        {/* Language Preference */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <Globe className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <Label htmlFor="language" className="text-sm font-medium">
                {t('settings.language')}
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                {t('settings.languageDescription')}
              </p>
            </div>
          </div>

          <Select value={language} onValueChange={setLanguage}>
            <SelectTrigger id="language" className="w-full">
              <SelectValue placeholder={t('settings.selectLanguage')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="fr">Français</SelectItem>
              <SelectItem value="ar">العربية</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Timezone Preference */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <Clock className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <Label htmlFor="timezone" className="text-sm font-medium">
                {t('settings.timezone')}
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                {t('settings.timezoneDescription')}
              </p>
            </div>
          </div>

          <Select value={timezone} onValueChange={setTimezone}>
            <SelectTrigger id="timezone" className="w-full">
              <SelectValue placeholder={t('settings.selectTimezone')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Africa/Algiers">Africa/Algiers (GMT+1)</SelectItem>
              <SelectItem value="Europe/Paris">Europe/Paris (GMT+2)</SelectItem>
              <SelectItem value="Europe/London">Europe/London (GMT+0)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Date Format Preference */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <Label className="text-sm font-medium">
                {t('settings.dateFormat')}
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                {t('settings.dateFormatDescription')}
              </p>
            </div>
          </div>

          <RadioGroup value={dateFormat} onValueChange={setDateFormat} className="flex flex-col space-y-1">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="DD/MM/YYYY" id="date-format-1" />
              <Label htmlFor="date-format-1" className="text-sm">DD/MM/YYYY</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="MM/DD/YYYY" id="date-format-2" />
              <Label htmlFor="date-format-2" className="text-sm">MM/DD/YYYY</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="YYYY-MM-DD" id="date-format-3" />
              <Label htmlFor="date-format-3" className="text-sm">YYYY-MM-DD</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Theme Preference */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <Monitor className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <Label className="text-sm font-medium">
                {t('settings.theme')}
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                {t('settings.themeDescription')}
              </p>
            </div>
          </div>

          <RadioGroup value={theme} onValueChange={setTheme} className="flex flex-col space-y-1">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="light" id="theme-light" />
              <Label htmlFor="theme-light" className="text-sm">{t('settings.lightTheme')}</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="dark" id="theme-dark" />
              <Label htmlFor="theme-dark" className="text-sm">{t('settings.darkTheme')}</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="system" id="theme-system" />
              <Label htmlFor="theme-system" className="text-sm">{t('settings.systemTheme')}</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Auto-translate Preference */}
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <Globe className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <Label className="text-sm font-medium">
                  {t('settings.autoTranslate')}
                </Label>
                <p className="text-xs text-muted-foreground mt-1">
                  {t('settings.autoTranslateDescription')}
                </p>
              </div>
            </div>
            <Switch
              checked={autoTranslate}
              onCheckedChange={setAutoTranslate}
            />
          </div>
        </div>
      </div>

      <div className="pt-4">
        <Button
          onClick={savePreferences}
          disabled={isSaving}
          className="bg-electric-orange hover:bg-electric-orange/90"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('settings.saving')}
            </>
          ) : (
            t('settings.saveChanges')
          )}
        </Button>
      </div>
    </div>
  );
}
