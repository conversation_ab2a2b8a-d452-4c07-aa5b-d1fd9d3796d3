import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Loader2, Upload, User, Building, Store, MapPin, Mail, Phone } from 'lucide-react';
import { useUser } from '@/contexts/UserContext';
import { getUserProfile, updateUserProfile, UserProfile } from '@/services/authService';
import { getCurrentUser } from '@/services/authService';

export function ProfileSettings() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { isSupplier, isMerchant } = useUser();
  
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  
  // Form fields
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [storeName, setStoreName] = useState('');
  const [storeAddress, setStoreAddress] = useState('');
  
  // Load user profile
  useEffect(() => {
    const loadProfile = async () => {
      setIsLoading(true);
      try {
        const { data } = await getCurrentUser();
        if (data.user) {
          const { profile, error } = await getUserProfile(data.user.id);
          if (error) {
            console.error('Error loading profile:', error);
            toast({
              title: t('settings.errorLoadingProfile'),
              description: error.message,
              variant: 'destructive',
            });
            return;
          }
          
          if (profile) {
            setProfile(profile);
            setFullName(profile.full_name || '');
            setEmail(profile.email || '');
            setPhone(profile.phone || '');
            setCompanyName(profile.company_name || '');
            setStoreName(profile.store_name || '');
            setStoreAddress(profile.store_address || '');
          }
        }
      } catch (error) {
        console.error('Error loading user:', error);
        toast({
          title: t('settings.errorLoadingProfile'),
          description: t('settings.unexpectedError'),
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadProfile();
  }, [t, toast]);
  
  // Save profile changes
  const handleSave = async () => {
    if (!profile) return;
    
    setIsSaving(true);
    try {
      const updatedProfile: Partial<UserProfile> = {
        full_name: fullName,
        company_name: isSupplier() ? companyName : undefined,
        store_name: isMerchant() ? storeName : undefined,
        store_address: isMerchant() ? storeAddress : undefined,
      };
      
      const { success, error } = await updateUserProfile(profile.id, updatedProfile);
      
      if (error) {
        console.error('Error updating profile:', error);
        toast({
          title: t('settings.errorSavingProfile'),
          description: error.message,
          variant: 'destructive',
        });
        return;
      }
      
      if (success) {
        toast({
          title: t('settings.profileSaved'),
          description: t('settings.profileSavedDescription'),
        });
      }
    } catch (error) {
      console.error('Unexpected error updating profile:', error);
      toast({
        title: t('settings.errorSavingProfile'),
        description: t('settings.unexpectedError'),
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{t('settings.profileInformation')}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {t('settings.profileInformationDescription')}
        </p>
      </div>
      
      <Separator />
      
      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/3">
          <div className="flex flex-col items-center gap-4">
            <Avatar className="h-24 w-24 border-2 border-electric-orange/20 shadow-sm">
              <AvatarImage src="" alt="Profile" />
              <AvatarFallback className="bg-gradient-to-br from-midnight-blue to-primary text-white text-xl font-bold">
                {fullName ? fullName.charAt(0).toUpperCase() : 'U'}
              </AvatarFallback>
            </Avatar>
            
            <Button variant="outline" size="sm" className="gap-2">
              <Upload className="h-4 w-4" />
              {t('settings.uploadPhoto')}
            </Button>
            
            <p className="text-xs text-center text-muted-foreground max-w-[200px]">
              {t('settings.photoRequirements')}
            </p>
          </div>
        </div>
        
        <div className="md:w-2/3 space-y-4">
          <div className="space-y-1.5">
            <Label htmlFor="fullName">{t('settings.fullName')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                <User className="h-4 w-4" />
              </div>
              <Input
                id="fullName"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          {isSupplier() && (
            <div className="space-y-1.5">
              <Label htmlFor="companyName">{t('settings.companyName')}</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                  <Building className="h-4 w-4" />
                </div>
                <Input
                  id="companyName"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          )}
          
          {isMerchant() && (
            <>
              <div className="space-y-1.5">
                <Label htmlFor="storeName">{t('settings.storeName')}</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <Store className="h-4 w-4" />
                  </div>
                  <Input
                    id="storeName"
                    value={storeName}
                    onChange={(e) => setStoreName(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-1.5">
                <Label htmlFor="storeAddress">{t('settings.storeAddress')}</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <MapPin className="h-4 w-4" />
                  </div>
                  <Input
                    id="storeAddress"
                    value={storeAddress}
                    onChange={(e) => setStoreAddress(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </>
          )}
          
          <div className="space-y-1.5">
            <Label htmlFor="email">{t('settings.email')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                <Mail className="h-4 w-4" />
              </div>
              <Input
                id="email"
                value={email}
                readOnly
                className="pl-10 bg-gray-50"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              {t('settings.emailChangeDescription')}
            </p>
          </div>
          
          <div className="space-y-1.5">
            <Label htmlFor="phone">{t('settings.phone')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                <Phone className="h-4 w-4" />
              </div>
              <Input
                id="phone"
                value={phone}
                readOnly
                className="pl-10 bg-gray-50"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              {t('settings.phoneChangeDescription')}
            </p>
          </div>
          
          <div className="pt-4">
            <Button 
              onClick={handleSave} 
              disabled={isSaving}
              className="bg-electric-orange hover:bg-electric-orange/90"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('settings.saving')}
                </>
              ) : (
                t('settings.saveChanges')
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
