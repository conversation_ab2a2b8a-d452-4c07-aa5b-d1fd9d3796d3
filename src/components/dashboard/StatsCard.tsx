
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    positive: boolean;
  };
  className?: string;
}

export function StatsCard({ title, value, icon, description, trend, className }: StatsCardProps) {
  return (
    <Card className={cn("stats-card group", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <div className="h-9 w-9 rounded-full bg-soft-white flex items-center justify-center text-electric-orange group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
      </CardHeader>
      <Card<PERSON>ontent>
        <div className="text-2xl font-bold text-graphite">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
        {trend && (
          <div className="flex items-center gap-1 text-xs mt-2 font-medium">
            <div className={cn(
              "flex items-center gap-0.5 px-1.5 py-0.5 rounded-full",
              trend.positive ? "bg-service-blue/10 text-service-blue" : "bg-racing-red/10 text-racing-red"
            )}>
              {trend.positive ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span>{trend.positive ? '+' : '-'}{Math.abs(trend.value)}%</span>
            </div>
            <span className="text-muted-foreground ml-1">from last month</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
