/**
 * Production Dashboard Component
 * 
 * Main dashboard component that routes to appropriate dashboard
 * based on user role with real data integration and error handling
 */

import React from 'react';
import { useUser } from '@/contexts/UserContext';
import { useAuth } from '@/contexts/AuthContext';
import { SupplierDashboard } from './SupplierDashboard';
import { MerchantDashboard } from './MerchantDashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, UserX, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export function ProductionDashboard() {
  const { userRole, isSupplier, isMerchant } = useUser();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Handle unauthenticated users
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <CardTitle className="text-xl text-gray-900">
              Authentication Required
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-6">
              Please log in to access your dashboard.
            </p>
            <Button 
              onClick={() => navigate('/partners')}
              className="w-full bg-[#fa7b00] hover:bg-[#e56f00]"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle invalid user roles
  if (!isSupplier() && !isMerchant()) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <CardTitle className="text-xl text-gray-900">
              Invalid User Role
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-6">
              Your account role ({userRole}) does not have access to this dashboard.
              Only Suppliers and Merchants can access the dashboard.
            </p>
            <div className="space-y-3">
              <Button 
                onClick={() => navigate('/partners')}
                variant="outline"
                className="w-full"
              >
                Switch Account
              </Button>
              <Button 
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render appropriate dashboard based on user role
  if (isSupplier()) {
    return <SupplierDashboard />;
  }

  if (isMerchant()) {
    return <MerchantDashboard />;
  }

  // Fallback (should never reach here)
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <CardTitle className="text-xl text-gray-900">
            Dashboard Error
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-600 mb-6">
            Unable to determine the appropriate dashboard for your account.
            Please contact support if this issue persists.
          </p>
          <Button 
            onClick={() => window.location.reload()}
            variant="outline"
            className="w-full"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Page
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
