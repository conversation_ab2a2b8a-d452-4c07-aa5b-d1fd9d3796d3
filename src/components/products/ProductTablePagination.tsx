
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
} from 'lucide-react';

interface ProductTablePaginationProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

export function ProductTablePagination({
  currentPage,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
}: ProductTablePaginationProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const totalPages = Math.ceil(totalItems / pageSize);
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);
  
  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };
  
  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };
  
  const handleFirst = () => {
    onPageChange(1);
  };
  
  const handleLast = () => {
    onPageChange(totalPages);
  };
  
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;
    
    if (totalPages <= maxPagesToShow) {
      // Show all pages if total is less than maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Calculate start and end of middle pages
      let middleStart = Math.max(2, currentPage - 1);
      let middleEnd = Math.min(totalPages - 1, currentPage + 1);
      
      // Ensure we show maxPagesToShow - 2 middle pages (accounting for first and last)
      const middlePagesToShow = maxPagesToShow - 2;
      
      // Adjust if needed to show correct number of pages
      if (middleEnd - middleStart + 1 < middlePagesToShow) {
        if (middleStart === 2) {
          middleEnd = Math.min(middleEnd + (middlePagesToShow - (middleEnd - middleStart + 1)), totalPages - 1);
        } else if (middleEnd === totalPages - 1) {
          middleStart = Math.max(middleStart - (middlePagesToShow - (middleEnd - middleStart + 1)), 2);
        }
      }
      
      // Add ellipsis after first page if needed
      if (middleStart > 2) {
        pages.push('...');
      }
      
      // Add middle pages
      for (let i = middleStart; i <= middleEnd; i++) {
        pages.push(i);
      }
      
      // Add ellipsis before last page if needed
      if (middleEnd < totalPages - 1) {
        pages.push('...');
      }
      
      // Always show last page
      pages.push(totalPages);
    }
    
    return pages;
  };
  
  return (
    <div className="flex items-center justify-between px-4 py-2 border-t">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <div>
          {t('products.showing')} {startItem}-{endItem} {t('products.of')} {totalItems}
        </div>
        <div className="flex items-center gap-1">
          <span>{t('products.itemsPerPage')}:</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(parseInt(value))}
          >
            <SelectTrigger className="h-8 w-16">
              <SelectValue>{pageSize}</SelectValue>
            </SelectTrigger>
            <SelectContent align={isRTL ? "end" : "start"}>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            onClick={handleFirst}
            disabled={currentPage === 1}
            className="h-8 w-8 rounded-r-none rounded-l-md rtl:rounded-l-none rtl:rounded-r-md"
          >
            {isRTL ? <ChevronsRight className="h-4 w-4" /> : <ChevronsLeft className="h-4 w-4" />}
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className="h-8 w-8 rounded-none border-l-0 rtl:border-l rtl:border-r-0"
          >
            {isRTL ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
          
          {getPageNumbers().map((page, index) => {
            if (page === '...') {
              return (
                <span key={`ellipsis-${index}`} className="h-8 w-8 flex items-center justify-center">
                  ...
                </span>
              );
            }
            
            return (
              <Button
                key={`page-${page}`}
                variant={currentPage === page ? "default" : "outline"}
                size="icon"
                onClick={() => typeof page === 'number' && onPageChange(page)}
                className={`h-8 w-8 rounded-none ${
                  currentPage === page 
                    ? "bg-electric-orange hover:bg-electric-orange/90 text-white" 
                    : "border-l-0 rtl:border-l rtl:border-r-0"
                }`}
              >
                {page}
              </Button>
            );
          })}
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className="h-8 w-8 rounded-none border-l-0 rtl:border-l rtl:border-r-0"
          >
            {isRTL ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleLast}
            disabled={currentPage === totalPages}
            className="h-8 w-8 rounded-l-none rounded-r-md rtl:rounded-r-none rtl:rounded-l-md border-l-0 rtl:border-l rtl:border-r-0"
          >
            {isRTL ? <ChevronsLeft className="h-4 w-4" /> : <ChevronsRight className="h-4 w-4" />}
          </Button>
        </div>
      </div>
    </div>
  );
}
