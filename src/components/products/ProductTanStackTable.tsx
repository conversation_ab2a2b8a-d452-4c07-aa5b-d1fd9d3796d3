
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  sortingFns,
  FilterFn,
  SortingFn,
  ColumnFiltersState,
  Row,
  RowSelectionState,
  SortingState,
  VisibilityState,
  ColumnDef,
  createColumnHelper,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Product } from '@/types';
import { toast } from 'sonner';
import { generateProductId } from '@/features/products/utils/idGenerator';
import { useUser } from '@/contexts/UserContext';
import {
  Barcode,
  ScanBarcode,
  Check,
  ChevronDown,
  ChevronRight,
  ChevronsUpDown,
  Edit,
  MoreHorizontal,
  Plus,
  PlusCircle,
  Trash,
  X,
  Eye,
  PencilLine,
  Image,
  FileText,
  Tag,
  Truck,
  Upload,
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Filter,
  EyeOff,
  Settings,
  LayoutGrid,
  Save,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

// Mock data (in real app would come from API)
import { allProducts } from '@/data/products';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';

type ProductTableProps = {
  activeCategory: string;
  visibleColumns: string[];
  onColumnVisibilityChange?: (columnId: string, isVisible: boolean) => void;
  defaultData?: Product[];
};

// Define custom filter functions
const fuzzyFilter: FilterFn<Product> = (row, columnId, value, addMeta) => {
  const itemValue = row.getValue(columnId);

  // Return true if the filter value is empty
  if (!value) return true;

  if (typeof itemValue === 'string') {
    return itemValue.toLowerCase().includes(String(value).toLowerCase());
  }

  return false;
};

// Custom sorting functions
const numericSort: SortingFn<Product> = (rowA, rowB, columnId) => {
  const valueA = rowA.getValue(columnId) as number;
  const valueB = rowB.getValue(columnId) as number;

  return valueA - valueB;
};

// Create a column helper to ensure type safety
const columnHelper = createColumnHelper<Product>();

// Define column types for the product table
const getProductColumns = (t: (key: string) => string): ColumnDef<Product, any>[] => [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected()
            ? true
            : table.getIsSomePageRowsSelected()
            ? 'indeterminate'
            : false
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="rounded-sm"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="rounded-sm"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50,
  }),
  columnHelper.accessor('id', {
    header: t('products.id'),
    cell: ({ row }) => (
      <div className="font-mono text-sm">{row.getValue('id')}</div>
    ),
    enableHiding: true,
  }),
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Tag className="h-4 w-4 text-muted-foreground" />
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.name')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="max-w-60 truncate font-medium">
        {row.getValue('name') || (
          <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
        )}
      </div>
    ),
    enableSorting: true,
    size: 250,
  }),
  columnHelper.accessor('sku', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.sku')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue('sku') || (
          <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
        )}
      </div>
    ),
    enableSorting: true,
    size: 150,
  }),
  columnHelper.accessor('partArticleNumber', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Barcode className="h-4 w-4 text-muted-foreground" />
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.barcodeEan')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Barcode className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-mono">
          {row.getValue('partArticleNumber') || (
            <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
          )}
        </span>
      </div>
    ),
    enableSorting: true,
    size: 150,
  }),
  columnHelper.accessor('category', {
    header: t('products.category'),
    cell: ({ row }) => (
      <Badge variant="outline" className="bg-[#f0f7ff] text-[#0073ea] border-[#0073ea]/30">
        Tyres & Related Products
      </Badge>
    ),
    filterFn: "equalsString",
    size: 150,
  }),
  columnHelper.accessor('subcategory', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.subcategory')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div>
        {row.getValue('subcategory') || (
          <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
        )}
      </div>
    ),
    enableSorting: true,
    filterFn: "equalsString",
    size: 150,
  }),
  columnHelper.accessor('manufacturer', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Tag className="h-4 w-4 text-muted-foreground" />
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.manufacturer')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        {row.original.manufacturerLogo && (
          <div className="h-6 w-6 rounded-full overflow-hidden">
            <img
              src={row.original.manufacturerLogo}
              className="h-full w-full object-contain"
              alt={String(row.getValue('manufacturer'))}
            />
          </div>
        )}
        <span>
          {row.getValue('manufacturer') || (
            <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
          )}
        </span>
      </div>
    ),
    enableSorting: true,
    size: 150,
  }),
  columnHelper.accessor('supplierName', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Truck className="h-4 w-4 text-muted-foreground" />
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.supplierName')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div>
        {row.getValue('supplierName') || (
          <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
        )}
      </div>
    ),
    enableSorting: true,
    size: 150,
  }),
  columnHelper.accessor('descriptionAndSpecifications', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <FileText className="h-4 w-4 text-muted-foreground" />
        <span>{t('products.description')}</span>
      </div>
    ),
    cell: ({ row }) => {
      const description = row.getValue('descriptionAndSpecifications') as string;
      return (
        <div className="max-w-60 truncate">
          {description ? (
            description.length > 50 ? `${description.substring(0, 50)}...` : description
          ) : (
            <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>
          )}
        </div>
      );
    },
    enableSorting: false,
    size: 250,
  }),
  columnHelper.accessor('primaryImage', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Image className="h-4 w-4 text-muted-foreground" />
        <span>{t('products.primaryImage')}</span>
      </div>
    ),
    cell: ({ row }) => (
      <div className="h-10 w-10 rounded-md border bg-white overflow-hidden flex items-center justify-center">
        <img
          src={row.getValue('primaryImage') || row.original.images?.[0] || "/placeholder.svg"}
          className="h-full w-full object-contain"
          alt={String(row.getValue('name'))}
        />
      </div>
    ),
    enableSorting: false,
    size: 80,
  }),
  columnHelper.accessor(
    row => row.additionalImages,
    {
      id: 'additionalImages',
      header: ({ column }) => (
        <div className="flex items-center gap-1">
          <Image className="h-4 w-4 text-muted-foreground" />
          <span>{t('products.additionalImages')}</span>
        </div>
      ),
      cell: ({ row }) => {
        const additionalImages = row.original.additionalImages || [];
        return (
          <div className="flex gap-1">
            {additionalImages.length > 0 ? (
              <>
                <div className="h-8 w-8 rounded-md border bg-white overflow-hidden flex items-center justify-center">
                  <img
                    src={additionalImages[0]}
                    className="h-full w-full object-contain"
                    alt={`${String(row.getValue('name'))} additional`}
                  />
                </div>
                {additionalImages.length > 1 && (
                  <Badge variant="outline" className="bg-muted">+{additionalImages.length - 1}</Badge>
                )}
              </>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
      enableSorting: false,
      size: 100,
    }
  ),
  columnHelper.accessor('price', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent text-right"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.price')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('price'));
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);

      return <div className="text-right font-medium">{formatted}</div>;
    },
    enableSorting: true,
    sortingFn: numericSort,
    size: 100,
  }),
  columnHelper.accessor('stockQuantity', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent text-right"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.stock')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const quantity = parseInt(row.getValue('stockQuantity'));

      return (
        <div className={cn(
          "text-right font-medium",
          quantity === 0 ? "text-red-500" : quantity <= 10 ? "text-amber-500" : ""
        )}>
          {quantity}
        </div>
      );
    },
    enableSorting: true,
    sortingFn: numericSort,
    size: 100,
  }),
  columnHelper.accessor('status', {
    header: ({ column }) => (
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="-ml-3 h-8 px-1 data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t('products.status')}
          <ChevronsUpDown className="ml-1 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string;

      return (
        <Badge variant={
          status === 'published' ? 'default' :
          status === 'draft' ? 'secondary' :
          'destructive'
        }>
          {status === 'published' ? t('products.published') :
           status === 'draft' ? t('products.draft') :
           t('products.outOfStock')}
        </Badge>
      );
    },
    enableSorting: true,
    filterFn: "equalsString",
    size: 120,
  }),
  columnHelper.display({
    id: 'actions',
    header: t('products.actions'),
    cell: ({ row }) => (
      <div className="flex justify-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">{t('products.openMenu')}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => console.log('Edit', row.original)}>
              <PencilLine className="mr-2 h-4 w-4" />
              <span>{t('products.edit')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => console.log('View', row.original)}>
              <Eye className="mr-2 h-4 w-4" />
              <span>{t('products.view')}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => console.log('Duplicate', row.original)}>
              <Plus className="mr-2 h-4 w-4" />
              <span>{t('products.duplicate')}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => console.log('Delete', row.original)}
              className="text-red-600 focus:bg-red-50 focus:text-red-600"
            >
              <Trash className="mr-2 h-4 w-4" />
              <span>{t('products.delete')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
    size: 80,
  }),
];

// The main component
export function ProductTanStackTable({
  activeCategory,
  visibleColumns,
  onColumnVisibilityChange,
  defaultData = allProducts
}: ProductTableProps) {
  const { t, i18n } = useTranslation();
  const { userRole, isSupplier, isMerchant } = useUser();
  const [data, setData] = useState<Product[]>(defaultData);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [newBarcodeValue, setNewBarcodeValue] = useState<string>('');
  const [manageColumnsOpen, setManageColumnsOpen] = useState<boolean>(false);

  // Filter products based on active category
  const filteredData = useMemo(() => {
    return activeCategory === 'all'
      ? data
      : data.filter(product => product.category === activeCategory);
  }, [data, activeCategory]);

  // Define columns
  const columns = useMemo(
    () => getProductColumns(t),
    [t]
  );

  // Set up the table instance
  const table = useReactTable({
    data: filteredData,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter,
    },
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      globalFilter,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
  });

  // Update column visibility based on props
  useEffect(() => {
    if (visibleColumns && visibleColumns.length) {
      // Convert the array of visible column ids to an object for the table
      const visibilitySettings: VisibilityState = {};

      // First, set all columns to hidden
      columns.forEach(column => {
        if (column.id) {
          visibilitySettings[column.id] = false;
        }
      });

      // Then, set the specified columns to visible
      visibleColumns.forEach(columnId => {
        visibilitySettings[columnId] = true;
      });

      // Always show the selection and actions columns
      visibilitySettings.select = true;
      visibilitySettings.actions = true;

      setColumnVisibility(visibilitySettings);
    }
  }, [visibleColumns, columns]);

  const handleAddProduct = () => {
    if (!newBarcodeValue.trim()) {
      toast.error(t('products.barcodeRequired'));
      return;
    }

    // Check if barcode already exists
    if (data.some(p => p.barcode === newBarcodeValue)) {
      toast.error(t('products.barcodeExists'));
      return;
    }

    // Determine the category for the new product
    const productCategory = activeCategory !== 'all' ? activeCategory : 'tyres'; // Default to tyres

    // Determine marketplace section based on user role
    const marketplaceSection = isSupplier() ? 'wholesale' : 'retail';

    // Create a new product with minimal details and a category-specific ID
    const newProduct: Product = {
      id: generateProductId(productCategory, marketplaceSection),
      name: '',
      sku: '',
      description: '',
      category: productCategory,
      subcategory: productCategory === 'tyres' ? 'Tyres' : productCategory === 'brakes' ? 'Brake Pads' : '',
      partArticleNumber: newBarcodeValue,
      price: 0,
      stockQuantity: 0,
      images: ['/placeholder.svg'],
      primaryImage: '/placeholder.svg',
      additionalImages: [],
      specifications: {},
      manufacturer: '',
      manufacturerLogo: '',
      supplierName: '',
      compatibleVehicles: [],
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setData([...data, newProduct]);
    setNewBarcodeValue('');
    toast.success(t('products.productCreated'));
  };

  // Create a reference to the scroll container
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Set up virtualization
  const { rows } = table.getRowModel();

  const virtualizer = useVirtualizer({
    getScrollElement: () => tableContainerRef.current,
    count: rows.length,
    estimateSize: () => 45, // approximate row height
    overscan: 10,
  });

  const virtualRows = virtualizer.getVirtualItems();
  const totalSize = virtualizer.getTotalSize();
  const paddingTop = virtualRows.length > 0 ? virtualRows[0].start || 0 : 0;
  const paddingBottom = virtualRows.length > 0
    ? totalSize - (virtualRows[virtualRows.length - 1].end || 0)
    : 0;

  // Function to get column label by ID
  const getColumnLabel = (columnId: string) => {
    // Find the column by ID or accessorKey
    const column = columns.find(col => {
      return col.id === columnId;
    });

    if (!column) return columnId;

    if (typeof column.header === 'string') {
      return column.header;
    } else if ('accessorKey' in column) {
      // Try to find a translation for the column
      const translationKey = `products.${column.accessorKey}`;
      return t(translationKey);
    } else if (column.id) {
      // Try using the ID if it's a meaningful name
      const translationKey = `products.${column.id}`;
      return t(translationKey);
    }

    return columnId;
  };

  return (
    <div className="space-y-4">
      {/* Column Management Dialog */}
      <Dialog open={manageColumnsOpen} onOpenChange={setManageColumnsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('products.manageColumns')}</DialogTitle>
            <DialogDescription>
              {t('products.selectColumnsDescription')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="grid gap-2 max-h-[60vh] overflow-y-auto px-1">
              {columns
                .filter(column => column.id !== 'select' && column.id !== 'actions')
                .map(column => {
                  const columnId = column.id;
                  return (
                    <div key={columnId} className="flex items-center justify-between py-1">
                      <div className="flex items-center">
                        <Checkbox
                          id={`column-${columnId}`}
                          checked={table.getColumn(columnId)?.getIsVisible()}
                          onCheckedChange={(value) => {
                            table.getColumn(columnId)?.toggleVisibility(!!value);
                            if (onColumnVisibilityChange) {
                              onColumnVisibilityChange(columnId, !!value);
                            }
                          }}
                        />
                        <label
                          htmlFor={`column-${columnId}`}
                          className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {getColumnLabel(columnId)}
                        </label>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button
              type="button"
              variant="outline"
              onClick={() => setManageColumnsOpen(false)}
            >
              {t('common.close')}
            </Button>
            <Button
              type="button"
              onClick={() => {
                // Save column configuration logic would go here
                toast.success(t('products.columnLayoutSaved'));
                setManageColumnsOpen(false);
              }}
              className="gap-2 bg-electric-orange hover:bg-electric-orange/90"
            >
              <Save className="h-4 w-4" />
              {t('common.saveChanges')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Table Toolbar */}
      <div className="flex flex-wrap justify-between gap-3 items-center pb-4">
        <div className="flex flex-wrap gap-3 items-center">
          <Input
            placeholder={t('products.searchProducts')}
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            className="w-[200px] md:w-[250px]"
          />

          <Button
            variant="outline"
            size="sm"
            className="h-9 border-dashed gap-2"
            onClick={() => setManageColumnsOpen(true)}
          >
            <Settings className="h-4 w-4" />
            {t('products.manageColumns')}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="gap-2 h-9"
              >
                <Filter className="h-4 w-4" />
                {t('products.filter')}
                {table.getState().columnFilters.length > 0 && (
                  <Badge variant="secondary" className="ml-1 rounded-full px-1">
                    {table.getState().columnFilters.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <div className="p-2">
                <p className="text-sm font-medium mb-2">{t('products.status')}</p>
                <div className="flex flex-wrap gap-2">
                  {['published', 'draft', 'out_of_stock'].map((status) => (
                    <Badge
                      key={status}
                      variant={
                        table.getColumn('status')?.getFilterValue() === status
                          ? 'default'
                          : 'outline'
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        if (table.getColumn('status')?.getFilterValue() === status) {
                          table.getColumn('status')?.setFilterValue(undefined);
                        } else {
                          table.getColumn('status')?.setFilterValue(status);
                        }
                      }}
                    >
                      {status === 'published' ? t('products.published') :
                       status === 'draft' ? t('products.draft') :
                       t('products.outOfStock')}
                    </Badge>
                  ))}
                </div>
              </div>
              <DropdownMenuSeparator />
              <div className="p-2">
                <p className="text-sm font-medium mb-2">{t('products.subcategory')}</p>
                <div className="flex flex-wrap gap-2 max-h-[200px] overflow-y-auto">
                  {activeCategory === 'tyres' ? (
                    ['Tyres', 'Wheel Covers', 'Tire Bag Sets', 'Wheel Chocks', 'Lug Wrenches', 'Snow Chains'].map((subcategory) => (
                      <Badge
                        key={subcategory}
                        variant={
                          table.getColumn('subcategory')?.getFilterValue() === subcategory
                            ? 'default'
                            : 'outline'
                        }
                        className="cursor-pointer"
                        onClick={() => {
                          if (table.getColumn('subcategory')?.getFilterValue() === subcategory) {
                            table.getColumn('subcategory')?.setFilterValue(undefined);
                          } else {
                            table.getColumn('subcategory')?.setFilterValue(subcategory);
                          }
                        }}
                      >
                        {subcategory}
                      </Badge>
                    ))
                  ) : activeCategory === 'brakes' ? (
                    ['Brake Pads', 'Brake Discs', 'Brake Caliper', 'ABS Sensor', 'Brake Fluid', 'Brake Hose'].map((subcategory) => (
                      <Badge
                        key={subcategory}
                        variant={
                          table.getColumn('subcategory')?.getFilterValue() === subcategory
                            ? 'default'
                            : 'outline'
                        }
                        className="cursor-pointer"
                        onClick={() => {
                          if (table.getColumn('subcategory')?.getFilterValue() === subcategory) {
                            table.getColumn('subcategory')?.setFilterValue(undefined);
                          } else {
                            table.getColumn('subcategory')?.setFilterValue(subcategory);
                          }
                        }}
                      >
                        {subcategory}
                      </Badge>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground">No subcategories available</div>
                  )}
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  table.resetColumnFilters();
                  setGlobalFilter('');
                }}
                className="justify-center text-center"
              >
                {t('products.clearFilters')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="border rounded-md bg-white">
        <div className="overflow-auto" style={{ height: 'calc(100vh - 330px)' }} ref={tableContainerRef}>
          <Table className="relative w-full">
            <TableHeader className="sticky top-0 z-20 bg-[#f5f6f8] shadow-sm">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow
                  key={headerGroup.id}
                  className="border-b-0 hover:bg-transparent"
                >
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        style={{ width: header.getSize() }}
                        className="font-medium py-3 px-3 border-r border-[#e1e1e1] last:border-r-0"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {paddingTop > 0 && (
                <tr>
                  <td style={{ height: `${paddingTop}px` }} />
                </tr>
              )}
              {virtualRows.map(virtualRow => {
                const row = rows[virtualRow.index] as Row<Product>;
                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={cn(
                      "group border-b hover:bg-[#f0f3ff]",
                      virtualRow.index % 2 === 0 ? "bg-white" : "bg-[#f9fafc]"
                    )}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell
                        key={cell.id}
                        className="py-2 px-3 border-r border-[#e1e1e1] last:border-r-0"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })}
              {paddingBottom > 0 && (
                <tr>
                  <td style={{ height: `${paddingBottom}px` }} />
                </tr>
              )}
              {rows.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    <div className="flex flex-col items-center justify-center py-6">
                      <div className="h-12 w-12 rounded-full bg-muted/30 flex items-center justify-center mb-3">
                        <Barcode className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <p className="text-lg font-medium mb-1">{t('products.noProductsFound')}</p>
                      <p className="text-muted-foreground mb-4">{t('products.enterBarcodeToAdd')}</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length > 0 && (
            <span>
              {table.getFilteredSelectedRowModel().rows.length} of{" "}
              {table.getFilteredRowModel().rows.length} row(s) selected.
            </span>
          )}
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">{t('products.rowsPerPage')}</p>
            <select
              value={table.getState().pagination.pageSize}
              onChange={e => {
                table.setPageSize(Number(e.target.value))
              }}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              {[10, 20, 30, 40, 50].map(pageSize => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            {t('products.pageXofY', {
              current: table.getState().pagination.pageIndex + 1,
              total: table.getPageCount(),
            })}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">{t('products.goToFirstPage')}</span>
              <ChevronRight className="h-4 w-4 rotate-180" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">{t('products.goToPreviousPage')}</span>
              <ChevronRight className="h-4 w-4 rotate-180" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">{t('products.goToNextPage')}</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">{t('products.goToLastPage')}</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
