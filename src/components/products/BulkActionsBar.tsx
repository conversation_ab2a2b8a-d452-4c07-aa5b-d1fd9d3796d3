import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Trash, 
  CheckCircle, 
  Clock, 
  XCircle, 
  Mail, 
  Download, 
  ShoppingCart,
  X,
  AlertTriangle,
  Archive
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { ProductStatus } from '@/features/products/types/product.types';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface BulkActionsBarProps {
  selectedCount: number;
  onClearSelection: () => void;
  onDelete: () => void;
  onUpdateStatus: (status: ProductStatus) => void;
  onExport: () => void;
  onShareByEmail?: () => void;
}

export function BulkActionsBar({
  selectedCount,
  onClearSelection,
  onDelete,
  onUpdateStatus,
  onExport,
  onShareByEmail
}: BulkActionsBarProps) {
  const { t } = useTranslation();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  const handleDelete = () => {
    setIsDeleteDialogOpen(false);
    onDelete();
  };
  
  const handleShareByEmail = () => {
    if (onShareByEmail) {
      onShareByEmail();
    } else {
      toast.info('Email sharing feature coming soon');
    }
  };
  
  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t shadow-lg z-50 py-3 px-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="font-medium">
            {t('products.selectedItems', { count: selectedCount })}
          </span>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0" 
            onClick={onClearSelection}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">{t('actions.clearSelection')}</span>
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                {t('products.setStatus')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onUpdateStatus('active')}>
                <CheckCircle className="mr-2 h-4 w-4 text-algerian-green" />
                <span>{t('products.markAsActive')}</span>
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onUpdateStatus('draft')}>
                <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{t('products.markAsDraft')}</span>
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onUpdateStatus('out_of_stock')}>
                <XCircle className="mr-2 h-4 w-4 text-destructive" />
                <span>{t('products.markAsOutOfStock')}</span>
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onUpdateStatus('discontinued')}>
                <Archive className="mr-2 h-4 w-4 text-purple-500" />
                <span>{t('products.markAsDiscontinued')}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-9"
            onClick={onExport}
          >
            <Download className="mr-2 h-4 w-4" />
            {t('products.export')}
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-9"
            onClick={handleShareByEmail}
          >
            <Mail className="mr-2 h-4 w-4" />
            {t('products.shareByEmail')}
          </Button>
          
          <Button 
            variant="destructive" 
            size="sm" 
            className="h-9"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash className="mr-2 h-4 w-4" />
            {t('products.delete')}
          </Button>
        </div>
      </div>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              {t('products.confirmDelete')}
            </DialogTitle>
            <DialogDescription>
              {t('products.deleteSelectedConfirmation', { count: selectedCount })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              {t('actions.cancel')}
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
            >
              {t('products.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
