
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Product } from '@/types';
import { toast } from 'sonner';
import { generateProductId } from '@/features/products/utils/idGenerator';
import { useUser } from '@/contexts/UserContext';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { getCategoryDisplayName } from '@/features/products/utils/categoryMapping';
import { ProductFormSelector, ProductEditSelector } from '@/features/products/components/ProductFormSelector';
import {
  Barcode,
  ScanBarcode,
  Check,
  CheckCircle2,
  Clock,
  Copy,
  Edit,
  MoreHorizontal,
  Plus,
  Trash,
  X,
  XCircle,
  Eye,
  PencilLine,
  Image,
  FileText,
  Tag,
  Truck,
  Upload,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { BarcodeScanner } from './BarcodeScanner';
import { EditableCell } from './EditableCell';
import { Textarea } from '@/components/ui/textarea';

// Mock data (in real app would come from API)
import { allProducts } from '@/data/products';

interface ProductTableProps {
  activeCategory: string;
  visibleColumns: string[];
}

export function ProductTable({ activeCategory, visibleColumns }: ProductTableProps) {
  const { t, i18n } = useTranslation();
  const { userRole, isSupplier, isMerchant } = useUser();
  const isRTL = i18n.language === 'ar';

  // State for product data
  const [products, setProducts] = useState<Product[]>(allProducts);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [newBarcodeValue, setNewBarcodeValue] = useState<string>('');
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingCell, setEditingCell] = useState<{productId: string, field: string} | null>(null);
  const [uploadingImage, setUploadingImage] = useState<{productId: string, field: string} | null>(null);

  // Filter products based on active category
  const filteredProducts = activeCategory === 'all'
    ? products
    : products.filter(product => product.category === activeCategory);

  console.log('Active Category:', activeCategory);
  console.log('Filtered Products:', filteredProducts.length);
  console.log('All Products:', products.length);
  console.log('Products with category:', products.map(p => ({ id: p.id, category: p.category })));

  // Handle new product creation from barcode
  const handleAddProduct = () => {
    if (!newBarcodeValue.trim()) {
      toast.error(t('products.barcodeRequired'));
      return;
    }

    // Check if part article number already exists
    if (products.some(p => p.partArticleNumber === newBarcodeValue)) {
      toast.error(t('products.barcodeExists'));
      return;
    }

    // Open the add product dialog with the barcode value
    setIsAddDialogOpen(true);
  };

  // Handle barcode entry via keyboard
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddProduct();
    }
  };

  // Handle barcode scan result
  const handleBarcodeScan = (barcode: string) => {
    setNewBarcodeValue(barcode);
    toast.success(t('products.barcodeScanned'));
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(product => product.id));
    }
  };

  // Handle select product
  const handleSelectProduct = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    } else {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };

  // Handle edit product
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setIsEditDialogOpen(true);
  };

  // Handle save product
  const handleSaveProduct = (updatedProduct: Product) => {
    const updatedProducts = products.map(p =>
      p.id === updatedProduct.id ? updatedProduct : p
    );
    setProducts(updatedProducts);
    setIsEditDialogOpen(false);
    setEditingProduct(null);
    toast.success(t('products.productUpdated'));
  };

  // Handle cell edit
  const handleCellEdit = (productId: string, field: string) => {
    setEditingCell({ productId, field });
  };

  // Handle cell value save
  const handleCellSave = (productId: string, field: string, value: string | number) => {
    const updatedProducts = products.map(product => {
      if (product.id === productId) {
        return {
          ...product,
          [field]: value,
          updatedAt: new Date()
        };
      }
      return product;
    });

    setProducts(updatedProducts);
    setEditingCell(null);
    toast.success(t('products.fieldUpdated'));
  };

  // Handle image upload
  const handleImageUpload = (productId: string, field: string, imageUrl: string) => {
    // Simulate image upload - in a real app this would be an actual file upload
    const demoImages = [
      '/placeholder.svg',
      'https://images.unsplash.com/photo-1619252584172-a83a949b6efd',
      'https://images.unsplash.com/photo-1551816230-ef5d99c0f3b6',
      'https://images.unsplash.com/photo-1641919505555-1089aaf96cae'
    ];

    const randomImage = demoImages[Math.floor(Math.random() * demoImages.length)];

    const updatedProducts = products.map(product => {
      if (product.id === productId) {
        if (field === 'primaryImage') {
          return {
            ...product,
            primaryImage: randomImage,
            updatedAt: new Date()
          };
        } else if (field === 'additionalImages') {
          const currentImages = [...(product.additionalImages || [])];
          currentImages.push(randomImage);
          return {
            ...product,
            additionalImages: currentImages,
            updatedAt: new Date()
          };
        }
      }
      return product;
    });

    setProducts(updatedProducts);
    setUploadingImage(null);
    toast.success(t('products.imageUploaded'));
  };

  // Handle remove additional image
  const handleRemoveAdditionalImage = (productId: string, index: number) => {
    const updatedProducts = products.map(product => {
      if (product.id === productId && product.additionalImages) {
        const updatedImages = [...product.additionalImages];
        updatedImages.splice(index, 1);
        return {
          ...product,
          additionalImages: updatedImages,
          updatedAt: new Date()
        };
      }
      return product;
    });

    setProducts(updatedProducts);
    toast.success(t('products.imageRemoved'));
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(i18n.language, {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
    }).format(new Date(date));
  };

  // Function to check if a column should be displayed
  const isColumnVisible = (columnId: string) => {
    return visibleColumns.includes(columnId);
  };

  // Render editable cell based on field type
  const renderEditableCell = (product: Product, field: string) => {
    const isEditing = editingCell?.productId === product.id && editingCell?.field === field;
    const isUploading = uploadingImage?.productId === product.id && uploadingImage?.field === field;

    if (isEditing) {
      if (field === 'description') {
        return (
          <Textarea
            value={product[field as keyof Product] as string || ''}
            onChange={(e) => {
              const updatedProducts = products.map(p => {
                if (p.id === product.id) {
                  return { ...p, [field]: e.target.value };
                }
                return p;
              });
              setProducts(updatedProducts);
            }}
            onBlur={() => handleCellSave(product.id, field, product[field as keyof Product] as string || '')}
            className="w-full h-24 min-h-[80px]"
            autoFocus
          />
        );
      }

      if (field === 'primaryImage') {
        return (
          <div className="flex flex-col gap-2 p-2 border rounded-md bg-white">
            <div className="h-24 w-full flex items-center justify-center border-2 border-dashed border-muted-foreground/20 rounded-md p-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 flex gap-1"
                onClick={() => {
                  setEditingCell(null);
                  setUploadingImage({ productId: product.id, field });
                  // Simulate upload delay
                  setTimeout(() => handleImageUpload(product.id, field, ''), 800);
                }}
              >
                <Upload className="h-4 w-4" />
                {t('products.uploadImage')}
              </Button>
            </div>
            <div className="flex justify-end gap-1 mt-1">
              <Button
                variant="outline"
                size="sm"
                className="h-7 w-7 p-0 rounded-full"
                onClick={() => handleCellSave(product.id, field, product[field as keyof Product] as string || '')}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        );
      }

      if (field === 'additionalImages') {
        return (
          <div className="flex flex-col gap-2 p-2 border rounded-md bg-white min-w-[200px]">
            <div className="flex flex-wrap gap-2">
              {product.additionalImages && product.additionalImages.length > 0 ? (
                product.additionalImages.map((img, idx) => (
                  <div key={idx} className="relative h-16 w-16 border rounded-md overflow-hidden group">
                    <img src={img} alt="" className="h-full w-full object-contain" />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="h-5 w-5 p-0 absolute top-0 right-0 opacity-0 group-hover:opacity-100 rounded-full"
                      onClick={() => handleRemoveAdditionalImage(product.id, idx)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))
              ) : (
                <div className="text-muted-foreground italic text-sm">{t('products.noAdditionalImages')}</div>
              )}
            </div>
            <div className="h-16 w-full flex items-center justify-center border-2 border-dashed border-muted-foreground/20 rounded-md p-2">
              <Button
                variant="outline"
                size="sm"
                className="h-7 flex gap-1 text-xs"
                onClick={() => {
                  setEditingCell(null);
                  setUploadingImage({ productId: product.id, field });
                  // Simulate upload delay
                  setTimeout(() => handleImageUpload(product.id, field, ''), 800);
                }}
              >
                <Plus className="h-3 w-3" />
                {t('products.addImage')}
              </Button>
            </div>
            <div className="flex justify-end gap-1 mt-1">
              <Button
                variant="outline"
                size="sm"
                className="h-7 w-7 p-0 rounded-full"
                onClick={() => setEditingCell(null)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        );
      }

      return (
        <div className="min-w-[120px]">
          <EditableCell
            value={product[field as keyof Product] as string | number || ''}
            onSave={(value) => handleCellSave(product.id, field, value)}
            inputType={
              field === 'price' || field === 'stockQuantity' ? 'number' :
              field === 'status' ? 'select' : 'text'
            }
            options={
              field === 'status' ? [
                { value: 'draft', label: t('products.draft') },
                { value: 'published', label: t('products.published') },
                { value: 'out_of_stock', label: t('products.outOfStock') }
              ] :
              field === 'subcategory' ? [
                { value: 'Tyres', label: 'Tyres' },
                { value: 'Wheel Covers', label: 'Wheel Covers' },
                { value: 'Tire Bag Sets', label: 'Tire Bag Sets' },
                { value: 'Wheel Accessories', label: 'Wheel Accessories' }
              ] : undefined
            }
          />
        </div>
      );
    }

    if (isUploading) {
      return (
        <div className="flex items-center justify-center p-2">
          <div className="h-4 w-4 rounded-full border-2 border-electric-orange border-t-transparent animate-spin"></div>
          <span className="ml-2 text-sm">{t('products.uploading')}</span>
        </div>
      );
    }

    // Make all cells clickable for editing, including category
    if (field === 'category') {
      return (
        <div
          className="cursor-pointer hover:bg-gray-50 p-1 rounded"
          onClick={() => handleCellEdit(product.id, field)}
        >
          <Badge variant="outline" className="bg-[#f0f7ff] text-[#0073ea] border-[#0073ea]/30">
            Tyres & Related Products
          </Badge>
        </div>
      );
    }

    // Render based on field type when not editing
    switch (field) {
      case 'primaryImage':
        return (
          <div
            className="h-10 w-10 rounded-md border bg-white overflow-hidden flex items-center justify-center cursor-pointer hover:bg-gray-50"
            onClick={() => handleCellEdit(product.id, field)}
          >
            <img
              src={product.primaryImage || product.images[0] || "/placeholder.svg"}
              className="h-full w-full object-contain"
              alt={product.name}
            />
          </div>
        );

      case 'additionalImages':
        return (
          <div className="flex gap-1">
            {product.additionalImages && product.additionalImages.length > 0 ? (
              <>
                <div className="h-8 w-8 rounded-md border bg-white overflow-hidden flex items-center justify-center">
                  <img
                    src={product.additionalImages[0]}
                    className="h-full w-full object-contain"
                    alt={`${product.name} additional`}
                  />
                </div>
                {product.additionalImages.length > 1 && (
                  <Badge variant="outline" className="bg-muted">+{product.additionalImages.length - 1}</Badge>
                )}
              </>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => handleCellEdit(product.id, field)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}
          </div>
        );

      case 'description':
        return (
          <div
            className="max-w-xs truncate cursor-pointer hover:text-blue-600"
            onClick={() => handleCellEdit(product.id, field)}
            title={product.description || t('products.clickToEdit')}
          >
            {product.description ?
              (product.description.length > 50 ?
                `${product.description.substring(0, 50)}...` :
                product.description) :
              <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>}
          </div>
        );

      case 'manufacturer':
        return (
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
            onClick={() => handleCellEdit(product.id, field)}
          >
            {product.manufacturerLogo && (
              <div className="h-6 w-6 rounded-full overflow-hidden">
                <img
                  src={product.manufacturerLogo}
                  className="h-full w-full object-contain"
                  alt={product.manufacturer}
                />
              </div>
            )}
            <span>{product.manufacturer || <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>}</span>
          </div>
        );

      case 'barcode':
        return (
          <div
            className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
            onClick={() => handleCellEdit(product.id, field)}
          >
            <Barcode className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-mono">{product[field] || <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>}</span>
          </div>
        );

      case 'status':
        return (
          <div onClick={() => handleCellEdit(product.id, field)} className="cursor-pointer">
            <Badge variant={
              product.status === 'published' ? 'default' :
              product.status === 'draft' ? 'secondary' :
              'destructive'
            }>
              {product.status === 'published' ? t('products.published') :
               product.status === 'draft' ? t('products.draft') :
               t('products.outOfStock')}
            </Badge>
          </div>
        );

      default:
        return (
          <div
            className="cursor-pointer hover:bg-gray-50 p-1 rounded"
            onClick={() => handleCellEdit(product.id, field)}
          >
            {product[field as keyof Product] ?
              String(product[field as keyof Product]) :
              <span className="text-muted-foreground italic">{t('products.clickToEdit')}</span>}
          </div>
        );
    }
  };

  // Get column icon by field name
  const getColumnIcon = (columnId: string) => {
    switch (columnId) {
      case 'id': return null;
      case 'name': return <Tag className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'sku': return <Copy className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'partArticleNumber': return <Barcode className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'description': return <FileText className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'primaryImage': return <Image className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'additionalImages': return <Image className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'manufacturer': return <Tag className="h-4 w-4 mr-2 text-muted-foreground" />;
      case 'supplierName': return <Truck className="h-4 w-4 mr-2 text-muted-foreground" />;
      default: return null;
    }
  };

  return (
    <div className="space-y-4">
      {/* Barcode Scanner Dialog */}
      <BarcodeScanner
        isOpen={isScannerOpen}
        onClose={() => setIsScannerOpen(false)}
        onScan={handleBarcodeScan}
      />

      {/* This section is now handled by the ProductEditSelector component at the end of the file */}

      {/* Product Information Section */}
      <div className="bg-white border border-border rounded-md p-4 shadow-sm">

        <div className="border border-border rounded-md overflow-hidden">
          <div className="overflow-x-auto">
            <Table className="monday-style-table">
              <TableHeader className="bg-[#f5f6f8] sticky top-0 z-10">
                <TableRow className="border-b-0 hover:bg-transparent">
                  <TableHead className="w-[50px] py-3 px-3 border-r border-[#e1e1e1]">
                    <Checkbox
                      checked={filteredProducts.length > 0 && selectedProducts.length === filteredProducts.length}
                      onCheckedChange={handleSelectAll}
                      className="rounded-sm"
                    />
                  </TableHead>

                  {isColumnVisible('id') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[80px]">
                      {t('products.id')}
                    </TableHead>
                  )}

                  {isColumnVisible('primaryImage') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[80px]">
                      {getColumnIcon('primaryImage')}
                      {t('products.primaryImage')}
                    </TableHead>
                  )}

                  {isColumnVisible('name') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[250px]">
                      {getColumnIcon('name')}
                      {t('products.name')}
                    </TableHead>
                  )}

                  {isColumnVisible('sku') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {getColumnIcon('sku')}
                      {t('products.sku')}
                    </TableHead>
                  )}

                  {isColumnVisible('partArticleNumber') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {getColumnIcon('partArticleNumber')}
                      {t('products.barcodeEan')}
                    </TableHead>
                  )}

                  {isColumnVisible('category') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {t('products.category')}
                    </TableHead>
                  )}

                  {isColumnVisible('subcategory') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {t('products.subcategory')}
                    </TableHead>
                  )}

                  {isColumnVisible('manufacturer') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {getColumnIcon('manufacturer')}
                      {t('products.manufacturer')}
                    </TableHead>
                  )}

                  {isColumnVisible('supplierName') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {getColumnIcon('supplierName')}
                      {t('products.supplierName')}
                    </TableHead>
                  )}

                  {isColumnVisible('description') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[250px]">
                      {getColumnIcon('description')}
                      {t('products.description')}
                    </TableHead>
                  )}

                  {isColumnVisible('additionalImages') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {getColumnIcon('additionalImages')}
                      {t('products.additionalImages')}
                    </TableHead>
                  )}

                  {isColumnVisible('price') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {t('products.price')}
                    </TableHead>
                  )}

                  {isColumnVisible('stockQuantity') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {t('products.stock')}
                    </TableHead>
                  )}

                  {isColumnVisible('status') && (
                    <TableHead className="font-medium py-3 px-4 border-r border-[#e1e1e1] min-w-[150px]">
                      {t('products.status')}
                    </TableHead>
                  )}

                  {isColumnVisible('actions') && (
                    <TableHead className="w-[80px] py-3 px-3 font-medium text-center">
                      {t('products.actions')}
                    </TableHead>
                  )}
                </TableRow>
              </TableHeader>

              <TableBody>
                {filteredProducts.map((product, index) => (
                  <TableRow
                    key={product.id}
                    className={cn(
                      "group border-b hover:bg-[#f0f3ff]",
                      index % 2 === 0 ? "bg-white" : "bg-[#f9fafc]"
                    )}
                  >
                    <TableCell className="py-2 px-3 border-r border-[#e1e1e1]">
                      <Checkbox
                        checked={selectedProducts.includes(product.id)}
                        onCheckedChange={() => handleSelectProduct(product.id)}
                        className="rounded-sm"
                      />
                    </TableCell>

                    {isColumnVisible('id') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1] font-mono text-sm">
                        {product.id}
                      </TableCell>
                    )}

                    {isColumnVisible('primaryImage') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'primaryImage')}
                      </TableCell>
                    )}

                    {isColumnVisible('name') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1] font-medium">
                        {renderEditableCell(product, 'name')}
                      </TableCell>
                    )}

                    {isColumnVisible('sku') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1] font-mono text-sm">
                        {renderEditableCell(product, 'sku')}
                      </TableCell>
                    )}

                    {isColumnVisible('barcode') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'barcode')}
                      </TableCell>
                    )}

                    {isColumnVisible('category') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'category')}
                      </TableCell>
                    )}

                    {isColumnVisible('subcategory') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'subcategory')}
                      </TableCell>
                    )}

                    {isColumnVisible('manufacturer') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'manufacturer')}
                      </TableCell>
                    )}

                    {isColumnVisible('supplierName') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'supplierName')}
                      </TableCell>
                    )}

                    {isColumnVisible('description') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'description')}
                      </TableCell>
                    )}

                    {isColumnVisible('additionalImages') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'additionalImages')}
                      </TableCell>
                    )}

                    {isColumnVisible('price') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'price')}
                      </TableCell>
                    )}

                    {isColumnVisible('stockQuantity') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'stockQuantity')}
                      </TableCell>
                    )}

                    {isColumnVisible('status') && (
                      <TableCell className="py-2 px-4 border-r border-[#e1e1e1]">
                        {renderEditableCell(product, 'status')}
                      </TableCell>
                    )}

                    {isColumnVisible('actions') && (
                      <TableCell className="py-2 px-3 text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 rounded-full bg-transparent hover:bg-[#dcdfec] focus:bg-[#dcdfec]"
                          onClick={() => handleEditProduct(product)}
                        >
                          <PencilLine className="h-4 w-4 text-[#676879]" />
                          <span className="sr-only">{t('products.edit')}</span>
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                ))}

                {/* Add new product row */}
                <TableRow className="group border-b hover:bg-[#f0f3ff] bg-white">
                  <TableCell className="py-2 px-3 border-r border-[#e1e1e1]">
                    <div className="w-4 h-4" />
                  </TableCell>

                  <TableCell colSpan={visibleColumns.length} className="py-3 px-4 border-r border-[#e1e1e1]">
                    <div className="flex items-center gap-2">
                      <div className="relative flex-1">
                        <Input
                          placeholder={t('products.enterBarcode')}
                          value={newBarcodeValue}
                          onChange={(e) => setNewBarcodeValue(e.target.value)}
                          onKeyDown={handleKeyDown}
                          className="pl-9 pr-9 h-9 text-sm border-[#c3c6d4] focus-visible:ring-[#0073ea]"
                        />
                        <Barcode className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7"
                          onClick={() => setIsScannerOpen(true)}
                        >
                          <ScanBarcode className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </div>
                      <Button
                        variant={newBarcodeValue ? "default" : "ghost"}
                        size="sm"
                        className={cn(
                          "h-9",
                          newBarcodeValue ? "bg-[#0073ea] hover:bg-[#0060b9]" : "opacity-50"
                        )}
                        onClick={handleAddProduct}
                        disabled={!newBarcodeValue}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        {t('products.add')}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>

                {filteredProducts.length === 0 && newBarcodeValue === '' && (
                  <TableRow>
                    <TableCell colSpan={visibleColumns.length + 1} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center py-6">
                        <div className="h-12 w-12 rounded-full bg-muted/30 flex items-center justify-center mb-3">
                          <Barcode className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <p className="text-lg font-medium mb-1">{t('products.noProductsFound')}</p>
                        <p className="text-muted-foreground mb-4">{t('products.enterBarcodeToAdd')}</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Replace the JSX-style tag with a standard style tag */}
      <style>
        {`
        .monday-style-table {
          border-collapse: separate;
          border-spacing: 0;
        }

        .monday-style-table th {
          font-weight: 500;
          color: #676879;
          text-transform: capitalize;
        }

        .monday-style-table td, .monday-style-table th {
          text-align: left;
          vertical-align: middle;
        }
        `}
      </style>

      {/* Barcode scanner dialog */}
      {isScannerOpen && (
        <BarcodeScanner
          onScan={handleBarcodeScan}
          onClose={() => setIsScannerOpen(false)}
        />
      )}

      {/* Add product dialog */}
      {isAddDialogOpen && (
        <ProductFormSelector
          isOpen={isAddDialogOpen}
          onClose={() => setIsAddDialogOpen(false)}
          onSave={async (product) => {
            // Determine marketplace section based on user role
            const marketplaceSection = isSupplier() ? 'wholesale' : 'retail';

            const newProduct = {
              ...product,
              id: product.id || generateProductId(product.category || activeCategory, marketplaceSection),
              createdAt: new Date(),
              updatedAt: new Date(),
            };
            setProducts([...products, newProduct as any]);
            setIsAddDialogOpen(false);
            toast.success(t('products.productCreated'));
          }}
          categories={[
            { id: 'tyres', name: getCategoryDisplayName('tyres') },
            { id: 'brakes', name: getCategoryDisplayName('brakes') },
          ]}
          initialBarcode={newBarcodeValue}
          categoryId={activeCategory !== 'all' ? activeCategory : 'tyres'}
        />
      )}

      {/* Edit product dialog */}
      {isEditDialogOpen && editingProduct && (
        <ProductEditSelector
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          product={editingProduct as any}
          onSave={async (updatedProduct) => {
            const updatedProducts = products.map(p =>
              p.id === updatedProduct.id ? {...p, ...updatedProduct as any} : p
            );
            setProducts(updatedProducts);
            setIsEditDialogOpen(false);
            setEditingProduct(null);
            toast.success(t('products.productUpdated'));
          }}
          categories={[
            { id: 'tyres', name: getCategoryDisplayName('tyres') },
            { id: 'brakes', name: getCategoryDisplayName('brakes') },
          ]}
        />
      )}
    </div>
  );
}
