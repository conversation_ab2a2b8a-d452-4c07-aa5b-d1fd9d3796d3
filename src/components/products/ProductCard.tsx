
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from '@/components/ui/card';
import { Product } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, Trash, Copy, Star, MoreHorizontal } from 'lucide-react';
import { 
  DropdownMenu, 
  DropdownMenuTrigger, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { useTranslation } from 'react-i18next';

interface ProductCardProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (productId: string) => void;
  onDuplicate: (product: Product) => void;
}

export function ProductCard({ product, onEdit, onDelete, onDuplicate }: ProductCardProps) {
  const { t } = useTranslation();
  
  const statusColor = {
    'draft': 'bg-muted text-muted-foreground',
    'published': 'bg-algerian-green text-white',
    'out_of_stock': 'bg-destructive text-destructive-foreground'
  };

  return (
    <Card className="card-hover relative overflow-hidden">
      <div className="absolute right-2 top-2 z-10 rtl:right-auto rtl:left-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 bg-white/80 text-midnight-blue">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(product)}>
              <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
              <span>{t('products.edit')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onDuplicate(product)}>
              <Copy className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
              <span>{t('products.duplicate')}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => onDelete(product.id)}
              className="text-destructive focus:text-destructive"
            >
              <Trash className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
              <span>{t('products.delete')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <div className="relative aspect-square overflow-hidden">
        <img 
          src={product.images[0] || '/placeholder.svg'} 
          alt={product.name}
          className="object-cover w-full h-full transition-transform duration-300 hover:scale-105"
        />
        <Badge 
          className={`absolute bottom-2 left-2 rtl:left-auto rtl:right-2 ${statusColor[product.status]}`}
        >
          {t(`products.${product.status.replace('_', '')}`)}
        </Badge>
      </div>
      
      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-medium line-clamp-1">{product.name}</h3>
            <p className="text-sm text-muted-foreground">{product.category}</p>
          </div>
          <div className="text-right rtl:text-left">
            <div className="text-lg font-bold">${product.price.toFixed(2)}</div>
            <div className="text-xs text-muted-foreground">SKU: {product.sku}</div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-4 pt-2">
        <p className="text-sm line-clamp-2">{product.description}</p>
        <div className="flex justify-between items-center mt-2">
          <span className="text-sm">
            {t('products.stock')}: <span className={product.stockQuantity > 0 ? 'text-algerian-green' : 'text-destructive'}>
              {product.stockQuantity}
            </span>
          </span>
          <Badge variant="outline" className="ml-2 rtl:ml-0 rtl:mr-2">{product.manufacturer}</Badge>
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0 flex justify-between">
        <Button variant="outline" size="sm" onClick={() => onEdit(product)}>
          <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
          {t('actions.edit')}
        </Button>
        <Button variant="ghost" size="sm" className="text-electric-orange">
          <Star className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
          {t('products.feature')}
        </Button>
      </CardFooter>
    </Card>
  );
}
