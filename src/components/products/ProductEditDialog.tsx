
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Product } from '@/types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ImagePlus, Upload, X } from 'lucide-react';

interface ProductEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  onSave: (product: Product) => void;
}

export function ProductEditDialog({
  isOpen,
  onClose,
  product,
  onSave,
}: ProductEditDialogProps) {
  const { t } = useTranslation();
  const [editedProduct, setEditedProduct] = useState<Product>({ ...product });

  const handleInputChange = (field: keyof Product, value: any) => {
    setEditedProduct((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(editedProduct);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {t('products.editProduct')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 py-4">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="mb-6 bg-[#f5f6f8]">
              <TabsTrigger value="basic" className="data-[state=active]:bg-white">
                {t('products.basicInfo')}
              </TabsTrigger>
              <TabsTrigger value="images" className="data-[state=active]:bg-white">
                {t('products.images')}
              </TabsTrigger>
              <TabsTrigger value="description" className="data-[state=active]:bg-white">
                {t('products.description')}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="id">{t('products.id')}</Label>
                    <Input
                      id="id"
                      value={editedProduct.id}
                      readOnly
                      className="bg-[#f5f6f8]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">{t('products.name')}</Label>
                    <Input
                      id="name"
                      value={editedProduct.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder={t('products.enterName')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sku">{t('products.sku')}</Label>
                    <Input
                      id="sku"
                      value={editedProduct.sku}
                      onChange={(e) => handleInputChange('sku', e.target.value)}
                      placeholder={t('products.enterSku')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="barcode">{t('products.barcodeEan')}</Label>
                    <Input
                      id="barcode"
                      value={editedProduct.barcode}
                      onChange={(e) => handleInputChange('barcode', e.target.value)}
                      placeholder={t('products.enterBarcode')}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="category">{t('products.category')}</Label>
                    <Input
                      id="category"
                      value="Tyres & Related Products"
                      readOnly
                      className="bg-[#f5f6f8]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subcategory">{t('products.subcategory')}</Label>
                    <Select
                      value={editedProduct.subcategory}
                      onValueChange={(value) => handleInputChange('subcategory', value)}
                    >
                      <SelectTrigger id="subcategory">
                        <SelectValue placeholder={t('products.selectSubcategory')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Tyres">Tyres</SelectItem>
                        <SelectItem value="Wheel Covers">Wheel Covers</SelectItem>
                        <SelectItem value="Tire Bag Sets">Tire Bag Sets</SelectItem>
                        <SelectItem value="Wheel Accessories">Wheel Accessories</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="manufacturer">{t('products.manufacturer')}</Label>
                    <Input
                      id="manufacturer"
                      value={editedProduct.manufacturer}
                      onChange={(e) => handleInputChange('manufacturer', e.target.value)}
                      placeholder={t('products.enterManufacturer')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="supplierName">{t('products.supplierName')}</Label>
                    <Input
                      id="supplierName"
                      value={editedProduct.supplierName || ''}
                      onChange={(e) => handleInputChange('supplierName', e.target.value)}
                      placeholder={t('products.enterSupplier')}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="images" className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label className="block mb-2">{t('products.primaryImage')}</Label>
                  <div className="border-2 border-dashed border-[#c3c6d4] rounded-md p-6 text-center">
                    {editedProduct.images && editedProduct.images.length > 0 ? (
                      <div className="relative w-full h-[200px] bg-[#f5f6f8] rounded-md overflow-hidden">
                        <img
                          src={editedProduct.images[0]}
                          alt={editedProduct.name}
                          className="w-full h-full object-contain"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-8 w-8 rounded-full"
                          onClick={() => {
                            const newImages = [...editedProduct.images];
                            newImages.splice(0, 1);
                            handleInputChange('images', newImages);
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-6">
                        <div className="h-12 w-12 rounded-full bg-[#f5f6f8] flex items-center justify-center mb-3">
                          <ImagePlus className="h-6 w-6 text-[#676879]" />
                        </div>
                        <p className="text-[#676879] mb-2">{t('products.dragAndDrop')}</p>
                        <Button type="button" variant="outline" size="sm">
                          <Upload className="h-4 w-4 mr-2" />
                          {t('products.uploadImage')}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <Label className="block mb-2">{t('products.additionalImages')}</Label>
                  <div className="grid grid-cols-3 gap-4">
                    {Array(3).fill(0).map((_, index) => (
                      <div key={index} className="border-2 border-dashed border-[#c3c6d4] rounded-md p-4 text-center h-[150px]">
                        <div className="flex flex-col items-center justify-center h-full">
                          <ImagePlus className="h-6 w-6 text-[#676879] mb-2" />
                          <Button type="button" variant="outline" size="sm">
                            {t('products.addImage')}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="description" className="space-y-6">
              <div className="space-y-3">
                <Label htmlFor="description">{t('products.description')}</Label>
                <Textarea
                  id="description"
                  value={editedProduct.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('products.enterDescription')}
                  className="min-h-[200px]"
                />
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('actions.cancel')}
            </Button>
            <Button type="submit" className="bg-[#0073ea] hover:bg-[#0060b9]">
              {t('actions.save')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
