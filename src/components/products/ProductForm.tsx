
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Product } from '@/types';
import { useState } from 'react';
import { Plus, MinusCircle, Upload, Barcode, ImagePlus } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ProductFormProps {
  initialData?: Partial<Product>;
  onSubmit: (data: Partial<Product>) => void;
  onCancel: () => void;
}

export function ProductForm({ initialData, onSubmit, onCancel }: ProductFormProps) {
  const [formData, setFormData] = useState<Partial<Product>>(initialData || {
    name: '',
    description: '',
    sku: '',
    partArticleNumber: '',
    category: '',
    price: 0,
    stockQuantity: 0,
    images: [],
    specifications: {},
    manufacturer: '',
    compatibleVehicles: [],
    status: 'draft'
  });

  const [specs, setSpecs] = useState<Array<{ key: string; value: string }>>(
    Object.entries(formData.specifications || {}).map(([key, value]) => ({ key, value }))
  );

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSpecChange = (index: number, field: 'key' | 'value', value: string) => {
    const newSpecs = [...specs];
    newSpecs[index][field] = value;
    setSpecs(newSpecs);

    // Update specifications object in formData
    const specsObject = newSpecs.reduce((acc, { key, value }) => {
      if (key) acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    setFormData(prev => ({ ...prev, specifications: specsObject }));
  };

  const addSpecification = () => {
    setSpecs([...specs, { key: '', value: '' }]);
  };

  const removeSpecification = (index: number) => {
    const newSpecs = specs.filter((_, i) => i !== index);
    setSpecs(newSpecs);

    // Update specifications object in formData
    const specsObject = newSpecs.reduce((acc, { key, value }) => {
      if (key) acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    setFormData(prev => ({ ...prev, specifications: specsObject }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
          <TabsTrigger value="specs">Specifications</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name</Label>
                <Input
                  id="name"
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter product name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter product description"
                  className="min-h-[120px]"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category || ''}
                  onValueChange={(value) => handleInputChange('category', value)}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="engine">Engine Parts</SelectItem>
                    <SelectItem value="brakes">Brake System</SelectItem>
                    <SelectItem value="suspension">Suspension</SelectItem>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="body">Body Parts</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sku">SKU</Label>
                  <Input
                    id="sku"
                    value={formData.sku || ''}
                    onChange={(e) => handleInputChange('sku', e.target.value)}
                    placeholder="Enter SKU"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="partArticleNumber">Part Article Number</Label>
                  <div className="relative">
                    <Input
                      id="partArticleNumber"
                      value={formData.partArticleNumber || ''}
                      onChange={(e) => handleInputChange('partArticleNumber', e.target.value)}
                      placeholder="Enter part article number"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full"
                    >
                      <Barcode className="h-4 w-4" />
                      <span className="sr-only">Generate Part Article Number</span>
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="manufacturer">Manufacturer</Label>
                <Input
                  id="manufacturer"
                  value={formData.manufacturer || ''}
                  onChange={(e) => handleInputChange('manufacturer', e.target.value)}
                  placeholder="Enter manufacturer"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">Price (USD)</Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price || ''}
                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value))}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status || 'draft'}
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="images" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Product Images</h3>
              <Button type="button" variant="outline" size="sm">
                <ImagePlus className="h-4 w-4 mr-2" />
                AI Enhancement
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Image upload placeholder */}
              <Card className="aspect-square flex flex-col items-center justify-center border-dashed cursor-pointer hover:bg-muted/50 transition-colors">
                <CardContent className="flex flex-col items-center justify-center h-full py-6">
                  <div className="h-12 w-12 rounded-full bg-muted/30 flex items-center justify-center mb-2">
                    <Upload className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <p className="text-sm font-medium">Upload Image</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    PNG, JPG or WEBP (max. 2MB)
                  </p>
                  <Input
                    type="file"
                    className="sr-only"
                    accept="image/*"
                    onChange={(e) => {
                      // Handle image upload here
                      console.log(e.target.files);
                    }}
                  />
                </CardContent>
              </Card>

              {/* Sample images - replace with actual uploaded images in real app */}
              {[1, 2, 3].map((index) => (
                <div key={index} className="relative aspect-square bg-muted rounded-lg overflow-hidden group">
                  <img
                    src="/placeholder.svg"
                    alt={`Product image ${index}`}
                    className="object-cover w-full h-full"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button variant="secondary" size="icon" className="h-8 w-8">
                      <MinusCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="specs" className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Product Specifications</h3>
              <Button
                type="button"
                onClick={addSpecification}
                variant="outline"
                size="sm"
                className="gap-1"
              >
                <Plus className="h-4 w-4" /> Add Specification
              </Button>
            </div>

            {specs.map((spec, index) => (
              <div key={index} className="grid grid-cols-5 gap-4 items-start">
                <div className="col-span-2">
                  <Input
                    value={spec.key}
                    onChange={(e) => handleSpecChange(index, 'key', e.target.value)}
                    placeholder="Specification Name"
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    value={spec.value}
                    onChange={(e) => handleSpecChange(index, 'value', e.target.value)}
                    placeholder="Specification Value"
                  />
                </div>
                <div className="col-span-1">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeSpecification(index)}
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <MinusCircle className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            {specs.length === 0 && (
              <div className="flex flex-col items-center justify-center p-8 bg-muted/20 rounded-lg">
                <p className="text-muted-foreground">No specifications added yet.</p>
                <Button
                  type="button"
                  onClick={addSpecification}
                  variant="outline"
                  size="sm"
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Specification
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="stockQuantity">Stock Quantity</Label>
                <Input
                  id="stockQuantity"
                  type="number"
                  value={formData.stockQuantity || 0}
                  onChange={(e) => handleInputChange('stockQuantity', parseInt(e.target.value))}
                  placeholder="0"
                  min="0"
                />
              </div>

              <div className="space-y-2">
                <Label>Compatible Vehicles</Label>
                <div className="p-4 border rounded-md bg-muted/20">
                  <p className="text-sm text-muted-foreground mb-2">
                    Add vehicle compatibility information for this product.
                  </p>
                  <Button type="button" variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" /> Add Vehicle
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Bulk Upload</Label>
                <div className="p-4 border rounded-md bg-muted/20">
                  <p className="text-sm text-muted-foreground mb-2">
                    Import products via CSV file or connect to your inventory system.
                  </p>
                  <div className="flex gap-2">
                    <Button type="button" variant="outline" size="sm">
                      <Upload className="h-4 w-4 mr-2" /> Import CSV
                    </Button>
                    <Button type="button" variant="outline" size="sm">
                      API Connect
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-4 mt-8">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" className="bg-electric-orange hover:bg-electric-orange/90">
          Save Product
        </Button>
      </div>
    </form>
  );
}
