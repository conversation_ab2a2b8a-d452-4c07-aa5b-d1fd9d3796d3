
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Calendar as CalendarIcon, 
  X,
  DollarSign,
  Factory,
} from 'lucide-react';
import { format } from 'date-fns';
import { ar, enUS, fr } from 'date-fns/locale';

interface ProductTableFiltersProps {
  filters: {
    search: string;
    category: string;
    status: string;
    dateRange: [Date, Date] | null;
    priceRange: [number, number] | null;
    manufacturer: string;
  };
  onFilterChange: (filters: any) => void;
}

export function ProductTableFilters({ filters, onFilterChange }: ProductTableFiltersProps) {
  const { t, i18n } = useTranslation();
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: filters.dateRange ? filters.dateRange[0] : undefined,
    to: filters.dateRange ? filters.dateRange[1] : undefined,
  });
  const [priceRange, setPriceRange] = useState<[number, number]>(
    filters.priceRange || [0, 1000]
  );
  
  // Get date-fns locale based on current language
  const getLocale = () => {
    switch (i18n.language) {
      case 'ar':
        return ar;
      case 'fr':
        return fr;
      default:
        return enUS;
    }
  };
  
  // Update active filters based on current filter values
  useEffect(() => {
    const active = [];
    if (filters.category !== 'all') active.push('category');
    if (filters.status !== 'all') active.push('status');
    if (filters.dateRange) active.push('dateRange');
    if (filters.priceRange) active.push('priceRange');
    if (filters.manufacturer !== 'all') active.push('manufacturer');
    setActiveFilters(active);
  }, [filters]);
  
  // Handle date range selection
  const handleDateRangeChange = (range: { from: Date | undefined; to: Date | undefined }) => {
    setDateRange(range);
    
    if (range.from && range.to) {
      onFilterChange({
        ...filters,
        dateRange: [range.from, range.to],
      });
    } else if (!range.from && !range.to) {
      onFilterChange({
        ...filters,
        dateRange: null,
      });
    }
  };
  
  // Handle price range selection
  const handlePriceRangeChange = (value: number[]) => {
    const newPriceRange: [number, number] = [value[0], value[1]];
    setPriceRange(newPriceRange);
    
    onFilterChange({
      ...filters,
      priceRange: newPriceRange,
    });
  };
  
  // Clear a specific filter
  const clearFilter = (filterName: string) => {
    switch (filterName) {
      case 'category':
        onFilterChange({ ...filters, category: 'all' });
        break;
      case 'status':
        onFilterChange({ ...filters, status: 'all' });
        break;
      case 'dateRange':
        setDateRange({ from: undefined, to: undefined });
        onFilterChange({ ...filters, dateRange: null });
        break;
      case 'priceRange':
        setPriceRange([0, 1000]);
        onFilterChange({ ...filters, priceRange: null });
        break;
      case 'manufacturer':
        onFilterChange({ ...filters, manufacturer: 'all' });
        break;
      default:
        break;
    }
  };
  
  // Clear all filters
  const clearAllFilters = () => {
    setDateRange({ from: undefined, to: undefined });
    setPriceRange([0, 1000]);
    onFilterChange({
      search: '',
      category: 'all',
      status: 'all',
      dateRange: null,
      priceRange: null,
      manufacturer: 'all',
    });
  };
  
  return (
    <div className="space-y-2">
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="flex-1 relative">
          <Search className="absolute rtl:right-3 ltr:left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('products.search')}
            value={filters.search}
            onChange={(e) => onFilterChange({ ...filters, search: e.target.value })}
            className="rtl:pr-9 ltr:pl-9"
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.category}
            onValueChange={(value) => onFilterChange({ ...filters, category: value })}
          >
            <SelectTrigger className="w-auto min-w-[150px]">
              <SelectValue placeholder={t('products.category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allCategories')}</SelectItem>
              <SelectItem value="engine">{t('products.engineParts')}</SelectItem>
              <SelectItem value="brakes">{t('products.brakeSystem')}</SelectItem>
              <SelectItem value="suspension">{t('products.suspension')}</SelectItem>
              <SelectItem value="electrical">{t('products.electrical')}</SelectItem>
              <SelectItem value="body">{t('products.bodyParts')}</SelectItem>
            </SelectContent>
          </Select>
          
          <Select
            value={filters.status}
            onValueChange={(value) => onFilterChange({ ...filters, status: value })}
          >
            <SelectTrigger className="w-auto min-w-[150px]">
              <SelectValue placeholder={t('products.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allStatus')}</SelectItem>
              <SelectItem value="published">{t('products.published')}</SelectItem>
              <SelectItem value="draft">{t('products.draft')}</SelectItem>
              <SelectItem value="out_of_stock">{t('products.outOfStock')}</SelectItem>
            </SelectContent>
          </Select>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={filters.dateRange ? "default" : "outline"}
                size="icon"
                className={filters.dateRange ? "bg-electric-orange hover:bg-electric-orange/90" : ""}
              >
                <CalendarIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
                locale={getLocale()}
              />
            </PopoverContent>
          </Popover>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={filters.priceRange ? "default" : "outline"}
                size="icon"
                className={filters.priceRange ? "bg-electric-orange hover:bg-electric-orange/90" : ""}
              >
                <DollarSign className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">{t('products.priceRange')}</h4>
                <Slider
                  min={0}
                  max={1000}
                  step={1}
                  value={priceRange}
                  onValueChange={handlePriceRangeChange}
                  className="mt-6"
                />
                <div className="flex items-center justify-between">
                  <div>${priceRange[0]}</div>
                  <div>${priceRange[1]}</div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          <Select
            value={filters.manufacturer}
            onValueChange={(value) => onFilterChange({ ...filters, manufacturer: value })}
          >
            <SelectTrigger className="w-auto min-w-[180px]">
              <Factory className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
              <SelectValue placeholder={t('products.manufacturer')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allManufacturers')}</SelectItem>
              <SelectItem value="BrakeMaster">BrakeMaster</SelectItem>
              <SelectItem value="FilterPro">FilterPro</SelectItem>
              <SelectItem value="LightTech">LightTech</SelectItem>
              <SelectItem value="RideMaster">RideMaster</SelectItem>
              <SelectItem value="PowerGen">PowerGen</SelectItem>
              <SelectItem value="SensorTech">SensorTech</SelectItem>
              <SelectItem value="TimingMaster">TimingMaster</SelectItem>
              <SelectItem value="VisualPro">VisualPro</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2 items-center">
          <span className="text-sm text-muted-foreground">{t('products.activeFilters')}:</span>
          
          {filters.category !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.category')}: {t(`products.${filters.category === 'body' ? 'bodyParts' : 
                filters.category === 'brakes' ? 'brakeSystem' : 
                filters.category === 'engine' ? 'engineParts' : 
                filters.category === 'suspension' ? 'suspension' : 
                'electrical'}`)}</span>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0" 
                onClick={() => clearFilter('category')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.status !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.status')}: {t(`products.${filters.status === 'out_of_stock' ? 'outOfStock' : filters.status}`)}</span>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0" 
                onClick={() => clearFilter('status')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.dateRange && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>
                {format(filters.dateRange[0], 'PP', { locale: getLocale() })} - {format(filters.dateRange[1], 'PP', { locale: getLocale() })}
              </span>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0" 
                onClick={() => clearFilter('dateRange')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.priceRange && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>${filters.priceRange[0]} - ${filters.priceRange[1]}</span>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0" 
                onClick={() => clearFilter('priceRange')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.manufacturer !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.manufacturer')}: {filters.manufacturer}</span>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0" 
                onClick={() => clearFilter('manufacturer')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {activeFilters.length > 1 && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-muted-foreground text-xs ml-2 rtl:mr-2 rtl:ml-0" 
              onClick={clearAllFilters}
            >
              {t('products.clearAll')}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
