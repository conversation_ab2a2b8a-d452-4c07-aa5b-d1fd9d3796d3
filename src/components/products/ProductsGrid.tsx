import { useState } from 'react';
import { EnhancedProductCard } from './EnhancedProductCard';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { Package, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

// Type for combined product data
type AnyProduct = TyreProduct | BrakeProduct;

interface ProductsGridProps {
  products: AnyProduct[];
  isLoading: boolean;
  viewMode: 'grid' | 'list';
  selectedProductIds: string[];
  onSelectProduct: (productId: string) => void;
  onEditProduct: (product: AnyProduct) => void;
  onDeleteProduct: (productId: string) => void;
  onDuplicateProduct: (product: AnyProduct) => void;
  onFeatureProduct?: (product: AnyProduct) => void;
}

export function ProductsGrid({
  products,
  isLoading,
  viewMode,
  selectedProductIds,
  onSelectProduct,
  onEditProduct,
  onDeleteProduct,
  onDuplicateProduct,
  onFeatureProduct
}: ProductsGridProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 bg-white rounded-lg">
        <Loader2 className="h-8 w-8 text-electric-orange animate-spin mb-4" />
        <h3 className="text-lg font-medium">{t('products.loading')}</h3>
        <p className="text-muted-foreground mt-1">
          {t('products.loadingDescription')}
        </p>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 bg-white rounded-lg">
        <div className="h-16 w-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
          <Package className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium">{t('products.noProductsFound')}</h3>
        <p className="text-muted-foreground mt-1 mb-4">
          {t('products.tryAdjustingFilters')}
        </p>
        <Button
          className="gap-2 bg-electric-orange hover:bg-electric-orange/90"
          onClick={() => navigate('/app/products-table/tyres')}
        >
          {t('products.goToProductsTable')}
        </Button>
      </div>
    );
  }

  return (
    <div
      className={
        viewMode === 'grid'
          ? 'grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
          : 'flex flex-col gap-3'
      }
    >
      {products.map((product) => (
        <EnhancedProductCard
          key={product.id}
          product={product}
          isSelected={selectedProductIds.includes(product.id)}
          onSelect={onSelectProduct}
          onEdit={onEditProduct}
          onDelete={onDeleteProduct}
          onDuplicate={onDuplicateProduct}
          onFeature={onFeatureProduct}
          viewMode={viewMode}
        />
      ))}
    </div>
  );
}
