import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  Di<PERSON>Footer, 
  Di<PERSON>Header, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useTranslation } from 'react-i18next';
import { Mail, Send, Copy, Check } from 'lucide-react';
import { AnyProduct } from '@/features/products/hooks/useAllProducts';
import { toast } from 'sonner';
import { formatDZD } from '@/lib/formatters';

interface ShareProductsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  products: AnyProduct[];
}

export function ShareProductsDialog({
  isOpen,
  onClose,
  products
}: ShareProductsDialogProps) {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState(`${t('products.checkOutTheseProducts')}`);
  const [message, setMessage] = useState('');
  const [includeImages, setIncludeImages] = useState(true);
  const [includePrices, setIncludePrices] = useState(true);
  const [includeDescription, setIncludeDescription] = useState(true);
  const [isCopied, setIsCopied] = useState(false);
  
  // Generate email content
  const generateEmailContent = () => {
    let content = `${message}\n\n${t('products.selectedProducts')}:\n\n`;
    
    products.forEach((product, index) => {
      content += `${index + 1}. ${product.name}\n`;
      
      if (includeDescription && product.description) {
        content += `   ${product.description}\n`;
      }
      
      if (includePrices && product.retailPrice) {
        content += `   ${t('products.price')}: ${formatDZD(product.retailPrice)}\n`;
      }
      
      if (includeImages && product.primaryImage) {
        content += `   ${t('products.image')}: ${product.primaryImage}\n`;
      }
      
      content += '\n';
    });
    
    content += `\n${t('products.emailFooter')}`;
    
    return content;
  };
  
  // Generate mailto link
  const generateMailtoLink = () => {
    const body = encodeURIComponent(generateEmailContent());
    const encodedSubject = encodeURIComponent(subject);
    return `mailto:${email}?subject=${encodedSubject}&body=${body}`;
  };
  
  // Handle send email
  const handleSendEmail = () => {
    if (!email) {
      toast.error(t('products.emailRequired'));
      return;
    }
    
    // Open mailto link
    window.location.href = generateMailtoLink();
    
    // Close dialog after a short delay
    setTimeout(() => {
      onClose();
      toast.success(t('products.emailClientOpened'));
    }, 500);
  };
  
  // Handle copy to clipboard
  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generateEmailContent())
      .then(() => {
        setIsCopied(true);
        toast.success(t('products.copiedToClipboard'));
        
        // Reset copied state after 2 seconds
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch(() => {
        toast.error(t('products.failedToCopy'));
      });
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('products.shareProductsByEmail')}
          </DialogTitle>
          <DialogDescription>
            {t('products.shareProductsDescription', { count: products.length })}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              {t('products.recipientEmail')}
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="col-span-3"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="subject" className="text-right">
              {t('products.emailSubject')}
            </Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="col-span-3"
            />
          </div>
          
          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="message" className="text-right pt-2">
              {t('products.message')}
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={t('products.messageToClient')}
              className="col-span-3"
              rows={4}
            />
          </div>
          
          <div className="grid grid-cols-4 items-start gap-4">
            <div className="text-right pt-2">
              <Label>{t('products.includeOptions')}</Label>
            </div>
            <div className="col-span-3 space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-images"
                  checked={includeImages}
                  onCheckedChange={(checked) => setIncludeImages(!!checked)}
                />
                <Label htmlFor="include-images">{t('products.includeImages')}</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-prices"
                  checked={includePrices}
                  onCheckedChange={(checked) => setIncludePrices(!!checked)}
                />
                <Label htmlFor="include-prices">{t('products.includePrices')}</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-description"
                  checked={includeDescription}
                  onCheckedChange={(checked) => setIncludeDescription(!!checked)}
                />
                <Label htmlFor="include-description">{t('products.includeDescriptions')}</Label>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-start gap-4">
            <div className="text-right pt-2">
              <Label>{t('products.preview')}</Label>
            </div>
            <div className="col-span-3">
              <div className="bg-muted/30 p-3 rounded-md text-sm font-mono h-32 overflow-y-auto whitespace-pre-wrap">
                {generateEmailContent()}
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            className="gap-2"
            onClick={handleCopyToClipboard}
          >
            {isCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            {isCopied ? t('products.copied') : t('products.copyContent')}
          </Button>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              {t('actions.cancel')}
            </Button>
            <Button className="gap-2" onClick={handleSendEmail}>
              <Send className="h-4 w-4" />
              {t('products.sendEmail')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
