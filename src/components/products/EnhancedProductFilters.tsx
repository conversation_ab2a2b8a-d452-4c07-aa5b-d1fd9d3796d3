import { useState, useEffect } from 'react';
import { Search, Filter, X, SlidersHorizontal, Save, Check } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { ProductStatus } from '@/features/products/types/product.types';
import { ProductFilters } from '@/features/products/hooks/useAllProducts';
import { formatDZD } from '@/lib/formatters';

interface EnhancedProductFiltersProps {
  filters: ProductFilters;
  onFilterChange: (filters: Partial<ProductFilters>) => void;
  onResetFilters: () => void;
  manufacturers: string[];
  maxPrice: number;
}

export function EnhancedProductFilters({
  filters,
  onFilterChange,
  onResetFilters,
  manufacturers,
  maxPrice = 1000000
}: EnhancedProductFiltersProps) {
  const { t } = useTranslation();
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>(
    filters.priceRange || [0, maxPrice]
  );
  const [isSavePresetOpen, setIsSavePresetOpen] = useState(false);
  const [presetName, setPresetName] = useState('');

  // Update price range when filters change
  useEffect(() => {
    if (filters.priceRange) {
      setPriceRange(filters.priceRange);
    }
  }, [filters.priceRange]);

  // Apply price range filter
  const handlePriceRangeChange = (value: number[]) => {
    setPriceRange([value[0], value[1]]);
  };

  const applyPriceRange = () => {
    onFilterChange({ priceRange });
  };

  // Clear a specific filter
  const clearFilter = (filterName: keyof ProductFilters) => {
    const newFilters: Partial<ProductFilters> = {};
    newFilters[filterName] = undefined;

    if (filterName === 'priceRange') {
      setPriceRange([0, maxPrice]);
    }

    onFilterChange(newFilters);
  };

  // Save filter preset
  const handleSavePreset = () => {
    if (!presetName.trim()) return;

    // Get existing presets from localStorage
    const existingPresets = localStorage.getItem('productFilterPresets');
    const presets = existingPresets ? JSON.parse(existingPresets) : [];

    // Add new preset
    const newPreset = {
      id: Date.now().toString(),
      name: presetName,
      filters
    };

    presets.push(newPreset);

    // Save to localStorage
    localStorage.setItem('productFilterPresets', JSON.stringify(presets));

    setPresetName('');
    setIsSavePresetOpen(false);
  };

  // Load filter preset
  const loadPreset = (preset: any) => {
    onFilterChange(preset.filters);
  };

  // Get saved presets
  const getSavedPresets = () => {
    const existingPresets = localStorage.getItem('productFilterPresets');
    return existingPresets ? JSON.parse(existingPresets) : [];
  };

  // Delete preset
  const deletePreset = (presetId: string) => {
    const existingPresets = localStorage.getItem('productFilterPresets');
    if (!existingPresets) return;

    const presets = JSON.parse(existingPresets);
    const updatedPresets = presets.filter((preset: any) => preset.id !== presetId);

    localStorage.setItem('productFilterPresets', JSON.stringify(updatedPresets));
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    return (
      (filters.search && filters.search.trim() !== '') ||
      (filters.category && filters.category !== 'all') ||
      (filters.status && filters.status !== 'all') ||
      (filters.manufacturer && filters.manufacturer !== 'all') ||
      filters.priceRange !== null ||
      filters.inStock !== undefined
    );
  };

  // Get active filters as an array
  const getActiveFilters = () => {
    const activeFilters = [];

    if (filters.search && filters.search.trim() !== '') {
      activeFilters.push('search');
    }

    if (filters.category && filters.category !== 'all') {
      activeFilters.push('category');
    }

    if (filters.status && filters.status !== 'all') {
      activeFilters.push('status');
    }

    if (filters.manufacturer && filters.manufacturer !== 'all') {
      activeFilters.push('manufacturer');
    }

    if (filters.priceRange) {
      activeFilters.push('priceRange');
    }

    if (filters.inStock !== undefined) {
      activeFilters.push('inStock');
    }

    return activeFilters;
  };

  const activeFilters = getActiveFilters();

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="flex-1 relative">
          <Search className="absolute rtl:right-3 ltr:left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('products.search')}
            value={filters.search || ''}
            onChange={(e) => onFilterChange({ search: e.target.value })}
            className="rtl:pr-9 ltr:pl-9"
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.category || 'all'}
            onValueChange={(value) => onFilterChange({ category: value })}
          >
            <SelectTrigger className="w-auto min-w-[150px]">
              <SelectValue placeholder={t('products.category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allCategories')}</SelectItem>
              <SelectItem value="tyres">Tyres & Related Products</SelectItem>
              <SelectItem value="brakes">Brake Parts & Systems</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.status || 'all'}
            onValueChange={(value) => onFilterChange({ status: value as ProductStatus | 'all' })}
          >
            <SelectTrigger className="w-auto min-w-[150px]">
              <SelectValue placeholder={t('products.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allStatuses')}</SelectItem>
              <SelectItem value="active">{t('products.active')}</SelectItem>
              <SelectItem value="draft">{t('products.draft')}</SelectItem>
              <SelectItem value="pending_approval">{t('products.pendingApproval')}</SelectItem>
              <SelectItem value="out_of_stock">{t('products.outOfStock')}</SelectItem>
              <SelectItem value="discontinued">{t('products.discontinued')}</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <SlidersHorizontal className="h-4 w-4" />
                {t('products.moreFilters')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="end">
              <div className="space-y-4">
                <h4 className="font-medium">{t('products.advancedFilters')}</h4>

                <div className="space-y-2">
                  <Label>{t('products.manufacturer')}</Label>
                  <Select
                    value={filters.manufacturer || 'all'}
                    onValueChange={(value) => onFilterChange({ manufacturer: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('products.allManufacturers')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('products.allManufacturers')}</SelectItem>
                      {manufacturers.map((manufacturer) => (
                        <SelectItem key={manufacturer} value={manufacturer}>
                          {manufacturer}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label>{t('products.priceRange')}</Label>
                    <span className="text-sm text-muted-foreground">
                      {formatDZD(priceRange[0])} - {formatDZD(priceRange[1])}
                    </span>
                  </div>
                  <Slider
                    defaultValue={[priceRange[0], priceRange[1]]}
                    max={maxPrice}
                    step={1000}
                    value={[priceRange[0], priceRange[1]]}
                    onValueChange={handlePriceRangeChange}
                    onValueCommit={applyPriceRange}
                    className="my-6"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="in-stock"
                    checked={filters.inStock}
                    onCheckedChange={(checked) => onFilterChange({ inStock: checked })}
                  />
                  <Label htmlFor="in-stock">{t('products.inStockOnly')}</Label>
                </div>

                <div className="flex justify-between pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsSavePresetOpen(true)}
                    disabled={!hasActiveFilters()}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {t('products.savePreset')}
                  </Button>

                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={onResetFilters}
                    disabled={!hasActiveFilters()}
                  >
                    {t('products.resetFilters')}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">{t('products.activeFilters')}:</span>

          {filters.search && filters.search.trim() !== '' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.search')}: {filters.search}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0"
                onClick={() => clearFilter('search')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.category && filters.category !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.category')}: {t(`products.${filters.category}`)}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0"
                onClick={() => clearFilter('category')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.status && filters.status !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.status')}: {t(`products.${filters.status}`)}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0"
                onClick={() => clearFilter('status')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.manufacturer && filters.manufacturer !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.manufacturer')}: {filters.manufacturer}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0"
                onClick={() => clearFilter('manufacturer')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.priceRange && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.priceRange')}: {formatDZD(filters.priceRange[0])} - {formatDZD(filters.priceRange[1])}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0"
                onClick={() => clearFilter('priceRange')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {filters.inStock !== undefined && (
            <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
              <span>{t('products.inStockOnly')}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0"
                onClick={() => clearFilter('inStock')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-muted-foreground"
            onClick={onResetFilters}
          >
            {t('products.clearAll')}
          </Button>
        </div>
      )}

      {/* Save Preset Dialog */}
      <Dialog open={isSavePresetOpen} onOpenChange={setIsSavePresetOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('products.saveFilterPreset')}</DialogTitle>
            <DialogDescription>
              {t('products.saveFilterPresetDescription')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder={t('products.presetName')}
              value={presetName}
              onChange={(e) => setPresetName(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSavePresetOpen(false)}>
              {t('actions.cancel')}
            </Button>
            <Button onClick={handleSavePreset} disabled={!presetName.trim()}>
              <Save className="h-4 w-4 mr-2" />
              {t('actions.save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
