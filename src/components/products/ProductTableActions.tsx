
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  ChevronDown,
  Trash,
  Download,
  CheckCircle2,
  Clock,
  XCircle,
  MoreHorizontal,
} from 'lucide-react';

interface ProductTableActionsProps {
  onAction: (action: string) => void;
}

export function ProductTableActions({ onAction }: ProductTableActionsProps) {
  const { t } = useTranslation();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="gap-2">
          {t('products.actions')} <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuItem onClick={() => onAction('export')}>
          <Download className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
          <span>{t('products.export')}</span>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={() => onAction('status-published')}>
          <CheckCircle2 className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-algerian-green" />
          <span>{t('products.markAsPublished')}</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => onAction('status-draft')}>
          <Clock className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-muted-foreground" />
          <span>{t('products.markAsDraft')}</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={() => onAction('status-out_of_stock')}>
          <XCircle className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-destructive" />
          <span>{t('products.markAsOutOfStock')}</span>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={() => onAction('delete')}
          className="text-destructive focus:text-destructive"
        >
          <Trash className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
          <span>{t('products.delete')}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
