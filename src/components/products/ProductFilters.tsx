
import { Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';

interface ProductFiltersProps {
  onSearch: (term: string) => void;
  onFilterChange: (field: string, value: string) => void;
}

export function ProductFilters({ onSearch, onFilterChange }: ProductFiltersProps) {
  const { t } = useTranslation();
  
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <div className="flex-1 relative">
          <Search className="absolute rtl:right-3 ltr:left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('products.search')}
            onChange={(e) => onSearch(e.target.value)}
            className="rtl:pr-9 ltr:pl-9"
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <Select
            onValueChange={(value) => onFilterChange('category', value)}
            defaultValue="all"
          >
            <SelectTrigger className="w-full sm:w-[150px]">
              <SelectValue placeholder={t('products.category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allCategories')}</SelectItem>
              <SelectItem value="engine">{t('products.engineParts')}</SelectItem>
              <SelectItem value="brakes">{t('products.brakeSystem')}</SelectItem>
              <SelectItem value="suspension">{t('products.suspension')}</SelectItem>
              <SelectItem value="electrical">{t('products.electrical')}</SelectItem>
              <SelectItem value="body">{t('products.bodyParts')}</SelectItem>
            </SelectContent>
          </Select>
          
          <Select
            onValueChange={(value) => onFilterChange('status', value)}
            defaultValue="all"
          >
            <SelectTrigger className="w-full sm:w-[150px]">
              <SelectValue placeholder={t('products.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.allStatus')}</SelectItem>
              <SelectItem value="published">{t('products.published')}</SelectItem>
              <SelectItem value="draft">{t('products.draft')}</SelectItem>
              <SelectItem value="out_of_stock">{t('products.outOfStock')}</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" className="hidden md:flex items-center gap-2">
            <Filter className="h-4 w-4" />
            {t('products.moreFilters')}
          </Button>
        </div>
      </div>
    </div>
  );
}
