
import { useTranslation } from 'react-i18next';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Columns, Check } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface Column {
  id: string;
  label: string;
}

interface ProductTableColumnsProps {
  columns: Column[];
  visibleColumns: string[];
  onColumnVisibilityChange: (column: string, isVisible: boolean) => void;
}

export function ProductTableColumns({
  columns,
  visibleColumns,
  onColumnVisibilityChange,
}: ProductTableColumnsProps) {
  const { t } = useTranslation();
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Columns className="h-4 w-4" />
          {t('products.columns')} 
          <Badge variant="outline" className="ml-1 rtl:mr-1 rtl:ml-0 px-2 h-5">
            {visibleColumns.length}
          </Badge>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-4" align="end">
        <div className="space-y-4">
          <h4 className="font-medium">{t('products.toggleColumns')}</h4>
          <div className="grid gap-3">
            {columns.map((column) => (
              <div key={column.id} className="flex items-center space-x-2 rtl:space-x-reverse">
                <Checkbox
                  id={`column-${column.id}`}
                  checked={visibleColumns.includes(column.id)}
                  onCheckedChange={(checked) => {
                    onColumnVisibilityChange(column.id, !!checked);
                  }}
                />
                <Label htmlFor={`column-${column.id}`}>{column.label}</Label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
