import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Edit,
  Trash,
  Copy,
  Star,
  MoreHorizontal,
  Eye,
  Mail,
  ShoppingCart,
  AlertCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { formatDZD } from '@/lib/formatters';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useUser } from '@/contexts/UserContext';

// Type for combined product data
type AnyProduct = TyreProduct | BrakeProduct;

interface EnhancedProductCardProps {
  product: AnyProduct;
  isSelected: boolean;
  onSelect: (productId: string) => void;
  onEdit: (product: AnyProduct) => void;
  onDelete: (productId: string) => void;
  onDuplicate: (product: AnyProduct) => void;
  onFeature?: (product: AnyProduct) => void;
  viewMode?: 'grid' | 'list';
}

export function EnhancedProductCard({
  product,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  onDuplicate,
  onFeature,
  viewMode = 'grid'
}: EnhancedProductCardProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);
  const { isSupplier, isMerchant } = useUser();

  // Helper function to calculate display price based on user role
  const getDisplayPrice = (): { price: number; minQuantity?: number; isWholesale?: boolean } => {
    if (isSupplier()) {
      // For suppliers, show minimum wholesale price
      if (product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0) {
        // Find the tier with the lowest minimum quantity (first tier)
        const sortedTiers = [...product.wholesalePricingTiers].sort((a, b) => a.minQuantity - b.minQuantity);
        const firstTier = sortedTiers[0];
        return {
          price: firstTier.price,
          minQuantity: firstTier.minQuantity,
          isWholesale: true
        };
      }
      // Fallback if no wholesale pricing tiers - show 0 DA as requested
      return { price: 0 };
    } else if (isMerchant()) {
      // For merchants, show retail price
      return { price: product.retailPrice || 0 };
    }
    // Default fallback
    return { price: product.retailPrice || 0 };
  };

  // Handle view product - open consumer marketplace product page in new tab
  const handleViewProduct = () => {
    window.open(`/${product.id}`, '_blank');
  };

  const statusColor = {
    'active': 'bg-algerian-green text-white',
    'draft': 'bg-muted text-muted-foreground',
    'pending_approval': 'bg-yellow-500 text-white',
    'out_of_stock': 'bg-destructive text-destructive-foreground',
    'discontinued': 'bg-purple-500 text-white'
  };

  const statusText = {
    'active': t('products.active'),
    'draft': t('products.draft'),
    'pending_approval': t('products.pendingApproval'),
    'out_of_stock': t('products.outOfStock'),
    'discontinued': t('products.discontinued')
  };

  // Determine if the product is low in stock (less than 5 items)
  const isLowStock = product.stockQuantity > 0 && product.stockQuantity < 5;

  if (viewMode === 'list') {
    return (
      <Card
        className={`group relative overflow-hidden transition-all duration-300 bg-white border-2 hover:border-electric-orange/30 ${
          isSelected
            ? 'ring-2 ring-electric-orange border-electric-orange shadow-lg'
            : 'border-gray-200 hover:shadow-md'
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-center p-5">
          <div className="flex items-center gap-4 flex-1">
            <Checkbox
              checked={isSelected}
              onCheckedChange={() => onSelect(product.id)}
              className="h-5 w-5 border-2 border-gray-300 data-[state=checked]:bg-electric-orange data-[state=checked]:border-electric-orange"
            />

            <div className="relative h-16 w-16 overflow-hidden rounded-lg bg-gray-50 border border-gray-200">
              <img
                src={product.primaryImage || '/placeholder.svg'}
                alt={product.name}
                className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
              />
            </div>

            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 line-clamp-1 group-hover:text-electric-orange transition-colors">
                {product.name}
              </h3>
              <div className="flex items-center gap-3 text-sm text-gray-500 mt-1">
                <span className="font-medium">SKU: {product.sku}</span>
                <span>•</span>
                <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-700 capitalize">
                  {product.category}
                </Badge>
                <span>•</span>
                <span className="font-medium">{product.manufacturer}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="text-right space-y-1">
              <div className="space-y-1">
                <div className="text-xl font-bold text-electric-orange">
                  {(() => {
                    const displayPrice = getDisplayPrice();
                    return formatDZD(displayPrice.price);
                  })()}
                </div>
                {(() => {
                  const displayPrice = getDisplayPrice();
                  if (displayPrice.isWholesale && displayPrice.minQuantity) {
                    return (
                      <div className="text-xs text-electric-orange/80 font-medium">
                        from {displayPrice.minQuantity} units
                      </div>
                    );
                  }
                  return null;
                })()}
              </div>
              <div className="text-sm text-gray-500">
                <span className="font-medium">{t('products.stock')}:</span>
                <span className={`ml-1 font-semibold ${
                  product.stockQuantity > 0
                    ? (isLowStock ? 'text-yellow-600' : 'text-green-600')
                    : 'text-red-600'
                }`}>
                  {product.stockQuantity}
                </span>
              </div>
            </div>

            <Badge className={`${statusColor[product.status]} font-medium shadow-sm`}>
              {statusText[product.status]}
            </Badge>

            <div className="flex items-center gap-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(product)}
                      className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('products.edit')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleViewProduct}
                      className="text-electric-orange hover:bg-orange-50 transition-colors"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('actions.view')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onDuplicate(product)}
                      className="hover:bg-green-50 hover:text-green-600 transition-colors"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('products.duplicate')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onDelete(product.id)}
                      className="hover:bg-red-50 hover:text-red-600 transition-colors"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{t('products.delete')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className={`group relative overflow-hidden transition-all duration-300 h-[450px] flex flex-col bg-white border-2 hover:border-electric-orange/30 ${
        isSelected
          ? 'ring-2 ring-electric-orange border-electric-orange shadow-lg'
          : 'border-gray-200 hover:shadow-xl'
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Selection Checkbox */}
      <div className="absolute left-3 top-3 z-20">
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onSelect(product.id)}
          className="h-5 w-5 bg-white/90 border-2 border-gray-300 data-[state=checked]:bg-electric-orange data-[state=checked]:border-electric-orange shadow-sm"
        />
      </div>

      {/* Actions Menu */}
      <div className="absolute right-3 top-3 z-20 rtl:right-auto rtl:left-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-white/90 text-midnight-blue hover:bg-white hover:text-electric-orange shadow-sm border border-gray-200"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => onEdit(product)} className="cursor-pointer">
              <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-blue-600" />
              <span className="font-medium">{t('products.edit')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleViewProduct} className="cursor-pointer">
              <Eye className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-electric-orange" />
              <span className="font-medium">{t('actions.view')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onDuplicate(product)} className="cursor-pointer">
              <Copy className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-green-600" />
              <span className="font-medium">{t('products.duplicate')}</span>
            </DropdownMenuItem>
            {onFeature && (
              <DropdownMenuItem onClick={() => onFeature(product)} className="cursor-pointer">
                <Star className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-yellow-500" />
                <span className="font-medium">{t('products.feature')}</span>
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(product.id)}
              className="text-destructive focus:text-destructive cursor-pointer"
            >
              <Trash className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
              <span className="font-medium">{t('products.delete')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden bg-gray-50">
        <img
          src={product.primaryImage || '/placeholder.svg'}
          alt={product.name}
          className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
        />

        {/* Status Badge */}
        <Badge
          className={`absolute bottom-3 left-3 rtl:left-auto rtl:right-3 shadow-sm font-medium ${statusColor[product.status]}`}
        >
          {statusText[product.status]}
        </Badge>

        {/* Low Stock Warning */}
        {isLowStock && (
          <div className="absolute top-12 right-3 bg-yellow-500 text-white px-2 py-1 rounded-md text-xs font-medium flex items-center shadow-sm">
            <AlertCircle className="h-3 w-3 mr-1" />
            {t('products.lowStock')}
          </div>
        )}

        {/* Out of Stock Overlay */}
        {product.stockQuantity === 0 && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
            <div className="bg-white/90 px-4 py-2 rounded-lg">
              <span className="text-gray-900 font-bold text-sm">{t('products.outOfStock')}</span>
            </div>
          </div>
        )}
      </div>

      {/* Product Information */}
      <CardHeader className="p-5 pb-3">
        <div className="space-y-2">
          <div className="flex justify-between items-start gap-3">
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 line-clamp-1 group-hover:text-electric-orange transition-colors">
                {product.name}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="text-xs font-medium bg-gray-100 text-gray-700 capitalize">
                  {product.category}
                </Badge>
              </div>
              <div className="mt-2 space-y-1">
                <div className="text-xs text-gray-500">
                  SKU: {product.sku}
                </div>
                {product.partArticleNumber && (
                  <div className="text-xs text-gray-500">
                    Part No: {product.partArticleNumber}
                  </div>
                )}
              </div>
            </div>
            <div className="text-right">
              <div className="space-y-1">
                <div className="text-xl font-bold text-electric-orange">
                  {(() => {
                    const displayPrice = getDisplayPrice();
                    return formatDZD(displayPrice.price);
                  })()}
                </div>
                {(() => {
                  const displayPrice = getDisplayPrice();
                  if (displayPrice.isWholesale && displayPrice.minQuantity) {
                    return (
                      <div className="text-xs text-electric-orange/80 font-medium">
                        from {displayPrice.minQuantity} units
                      </div>
                    );
                  }
                  return null;
                })()}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      {/* Product Details */}
      <CardContent className="p-5 pt-0 flex-1 flex flex-col">
        <div className="space-y-3 flex-1">
          {/* Product Metrics */}
          <div className="grid grid-cols-2 gap-3 pt-2">
            <div className="space-y-1">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                {t('products.stock')}
              </div>
              <div className={`text-sm font-semibold ${
                product.stockQuantity > 0
                  ? (isLowStock ? 'text-yellow-600' : 'text-green-600')
                  : 'text-red-600'
              }`}>
                {product.stockQuantity} {t('products.units')}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                {t('products.manufacturer')}
              </div>
              <div className="text-sm font-medium text-gray-900 truncate">
                {product.manufacturer}
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Action Buttons */}
      <CardFooter className="p-5 pt-3 border-t border-gray-100 mt-auto">
        <div className="flex gap-2 w-full">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(product)}
            className="flex-1 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300 transition-colors"
          >
            <Edit className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
            {t('actions.edit')}
          </Button>
          <Button
            variant="default"
            size="sm"
            className="flex-1 bg-electric-orange hover:bg-electric-orange/90 text-white transition-colors"
            onClick={handleViewProduct}
          >
            <Eye className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" />
            {t('actions.view')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
