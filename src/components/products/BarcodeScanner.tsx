
import React, { useRef, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { X, Camera, Loader2, Scan } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { BrowserMultiFormatReader } from '@zxing/browser';

interface BarcodeScannerProps {
  isOpen: boolean;
  onClose: () => void;
  onScan: (barcode: string) => void;
}

export function BarcodeScanner({ isOpen, onClose, onScan }: BarcodeScannerProps) {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const readerRef = useRef<BrowserMultiFormatReader | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [manualInput, setManualInput] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const decodingRef = useRef(false);
  
  // Initialize ZXing reader
  useEffect(() => {
    if (!readerRef.current) {
      readerRef.current = new BrowserMultiFormatReader();
    }
    
    return () => {
      cleanupScanner();
    };
  }, []);
  
  // Listen for barcode scanner input (hardware scanner)
  useEffect(() => {
    if (!isOpen) return;
    
    let barcodeBuffer = '';
    let lastKeyTime = 0;
    const SCANNER_TIMEOUT = 50; // ms between keystrokes
    
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only process if not in an input field
      if (document.activeElement && document.activeElement.tagName === 'INPUT') {
        return;
      }
      
      const currentTime = new Date().getTime();
      
      // If there's been a delay, start a new barcode
      if (currentTime - lastKeyTime > SCANNER_TIMEOUT) {
        barcodeBuffer = '';
      }
      
      // Add character to buffer (hardware scanners usually send one character at a time rapidly)
      if (e.key !== 'Enter') {
        barcodeBuffer += e.key;
      } else if (barcodeBuffer) {
        // Enter key signals end of barcode
        if (barcodeBuffer.length >= 8) { // Most common barcodes are at least 8 characters
          onScan(barcodeBuffer.trim());
          onClose();
          e.preventDefault();
        }
        barcodeBuffer = '';
      }
      
      lastKeyTime = currentTime;
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onScan, onClose]);
  
  // Clean up all scanner resources
  const cleanupScanner = () => {
    console.log('Cleaning up scanner resources');
    setIsScanning(false);
    decodingRef.current = false;
    
    // Stop any active stream
    if (stream) {
      stream.getTracks().forEach(track => {
        try {
          track.stop();
        } catch (err) {
          console.error('Error stopping track:', err);
        }
      });
      setStream(null);
    }
    
    // Disconnect video element
    if (videoRef.current) {
      try {
        videoRef.current.srcObject = null;
      } catch (err) {
        console.error('Error clearing video source:', err);
      }
    }
  };
  
  // Function to start the camera scanning
  const startCameraScanning = async () => {
    console.log('Starting camera scanning...');
    
    if (!videoRef.current) {
      console.error('Video reference not available');
      toast.error(t('products.cameraError'));
      return;
    }
    
    setIsLoading(true);
    
    try {
      // First check if this browser supports getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported in this browser');
      }
      
      // Clean up any existing scanner resources
      cleanupScanner();
      
      setIsScanning(true);
      
      const constraints = {
        video: { 
          facingMode: 'environment', // Use back camera when available
          width: { ideal: 1280 },
          height: { ideal: 720 },
        }
      };
      
      // Request camera access
      console.log('Requesting camera access...');
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('Camera access granted');
      
      setStream(mediaStream);
      setHasPermission(true);
      
      // Set up video element with the stream
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        
        // Wait for video to be ready before starting to decode
        await new Promise<void>((resolve) => {
          if (!videoRef.current) return resolve();
          
          const onCanPlay = () => {
            videoRef.current?.removeEventListener('canplay', onCanPlay);
            resolve();
          };
          
          videoRef.current.addEventListener('canplay', onCanPlay);
          
          // If video is already ready, resolve immediately
          if (videoRef.current.readyState >= 3) {
            resolve();
            return;
          }
          
          // Try to play the video
          videoRef.current.play().catch(err => {
            console.error("Error playing video:", err);
            // Continue even if autoplay fails - user may need to interact first
            resolve();
          });
        });
      }
      
      // Start ZXing decoding
      if (readerRef.current && videoRef.current) {
        console.log('Starting ZXing decoding...');
        
        try {
          decodingRef.current = true;
          
          // Use the decodeFromVideoDevice method for continuous scanning
          const videoDeviceId = undefined; // Use default camera
          
          readerRef.current.decodeFromVideoDevice(
            videoDeviceId,
            videoRef.current,
            (result, error) => {
              // If scanner has been disabled, don't process results
              if (!decodingRef.current) return;
              
              if (result) {
                // Successfully read barcode
                const barcodeValue = result.getText();
                console.log('Scanned barcode:', barcodeValue);
                
                // Stop scanning and report result
                cleanupScanner();
                onScan(barcodeValue);
                onClose();
              }
              
              // Ignore expected errors (no QR code in frame)
              if (error && !(error instanceof TypeError)) {
                console.log('Scanning error (non-critical):', error);
              }
            }
          ).catch(err => {
            console.error('Error starting ZXing scan:', err);
            toast.error(t('products.scanningError'));
          });
        } catch (err) {
          console.error('ZXing scanning error:', err);
          toast.error(t('products.scanningError'));
          setHasPermission(false);
        }
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      setHasPermission(false);
      if (String(err).includes('Permission')) {
        toast.error(t('products.cameraPermissionDenied'));
      } else {
        toast.error(t('products.cameraError'));
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Clean up on unmount or when dialog closes
  useEffect(() => {
    if (!isOpen) {
      cleanupScanner();
      setHasPermission(null);
      setManualInput('');
    } else if (inputRef.current) {
      // Focus the input field when dialog opens
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
    
    return () => {
      cleanupScanner();
    };
  }, [isOpen]);
  
  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualInput.trim()) {
      onScan(manualInput.trim());
      onClose();
    }
  };
  
  // This handles the button click
  const handleStartCamera = () => {
    console.log('Start camera button clicked');
    setHasPermission(null); // Reset permission state
    
    // Ensure we call this on a slight delay to ensure
    // the DOM has fully updated and videoRef is available
    setTimeout(() => {
      startCameraScanning();
    }, 10);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('products.scanBarcode')}</DialogTitle>
          <DialogDescription>{t('products.scanOrEnterBarcode')}</DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col items-center gap-4">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
              <p>{t('products.accessingCamera')}</p>
            </div>
          ) : hasPermission === false ? (
            <div className="text-center p-4">
              <p className="text-destructive mb-2">{t('products.cameraPermissionDenied')}</p>
              <p className="text-sm text-muted-foreground mb-4">
                {t('products.enableCameraPermission')}
              </p>
              <Button 
                variant="outline" 
                onClick={handleStartCamera} 
                type="button"
                className="focus:ring-2"
              >
                {t('products.retryCamera')}
              </Button>
            </div>
          ) : hasPermission === true ? (
            <div className="relative w-full aspect-video bg-black rounded-md overflow-hidden">
              <video 
                ref={videoRef} 
                className="w-full h-full object-cover"
                playsInline
                muted
                autoPlay
              />
              <div className="absolute inset-0 border-2 border-white/30 rounded-md pointer-events-none">
                <div className="absolute top-1/4 left-1/4 right-1/4 bottom-1/4 border-2 border-white/50 rounded-md pointer-events-none" />
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-6 bg-muted/20 rounded-md w-full">
              <Camera className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="font-medium mb-1">{t('products.cameraAccess')}</p>
              <p className="text-sm text-muted-foreground mb-4 text-center">
                {t('products.allowCameraAccess')}
              </p>
              <Button 
                onClick={handleStartCamera} 
                type="button"
                className="focus:ring-2"
              >
                {t('products.startCamera')}
              </Button>
            </div>
          )}
          
          <form onSubmit={handleManualSubmit} className="w-full space-y-3">
            <div className="flex items-center gap-2">
              <Input
                ref={inputRef}
                type="text"
                placeholder={t('products.enterBarcodeManually')}
                value={manualInput}
                onChange={(e) => setManualInput(e.target.value)}
                className="flex-1"
              />
              <Button type="submit" variant="secondary" size="icon">
                <Scan className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="text-xs text-muted-foreground text-center">
              {t('products.scannerInstructions')}
            </div>
          </form>
          
          <div className="flex justify-end w-full gap-2">
            <Button variant="outline" onClick={onClose} type="button">
              <X className="mr-2 h-4 w-4" />
              {t('common.cancel')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
