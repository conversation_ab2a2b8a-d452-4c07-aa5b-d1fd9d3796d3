import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
  PaginationState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Product } from '@/types';
import { toast } from 'sonner';
import { getCategoryDisplayName } from '@/features/products/utils/categoryMapping';
import {
  Barcode,
  ScanBarcode,
  Check,
  CheckCircle2,
  Clock,
  Copy,
  Edit,
  MoreHorizontal,
  Plus,
  Trash,
  X,
  XCircle,
  Eye,
  PencilLine,
  Image,
  FileText,
  Tag,
  Truck,
  Upload,
  ArrowUpDown,
  ChevronFirst,
  ChevronLeft,
  ChevronRight,
  ChevronLast,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { BarcodeScanner } from './BarcodeScanner';
import { ProductEditDialog } from './ProductEditDialog';
import { EditableCell } from './EditableCell';
import { Textarea } from '@/components/ui/textarea';

// Mock data (in real app would come from API)
import { allProducts } from '@/data/products';

interface ProductTableProps {
  activeCategory: string;
  visibleColumns: string[];
}

export function ProductTableV2({ activeCategory, visibleColumns }: ProductTableProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  // State for table
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // State for product data
  const [products] = useState<Product[]>(allProducts);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [newBarcodeValue, setNewBarcodeValue] = useState<string>('');
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCell, setEditingCell] = useState<{productId: string, field: string} | null>(null);
  const [uploadingImage, setUploadingImage] = useState<{productId: string, field: string} | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Reset pagination when category changes
  useEffect(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [activeCategory]);

  // Filter products based on active category
  const filteredProducts = React.useMemo(() => {
    setIsLoading(true);
    const filtered = activeCategory === 'all'
      ? products
      : products.filter(product => product.category === 'tyres');
    setIsLoading(false);
    return filtered;
  }, [products, activeCategory]);

  // Define columns
  const columns = React.useMemo<ColumnDef<Product>[]>(() => [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllRowsSelected()}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
          className="rounded-sm"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="rounded-sm"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'id',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            {t('products.id')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => <div className="font-mono text-sm">{row.getValue('id')}</div>,
    },
    {
      accessorKey: 'primaryImage',
      header: () => (
        <div className="flex items-center">
          <Image className="h-4 w-4 mr-2 text-muted-foreground" />
          {t('products.primaryImage')}
        </div>
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="h-10 w-10 rounded-md border bg-white overflow-hidden flex items-center justify-center">
            <img
              src={product.primaryImage || product.images[0] || "/placeholder.svg"}
              className="h-full w-full object-contain"
              alt={product.name}
            />
          </div>
        );
      },
    },
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
            {t('products.name')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="font-medium">
            {product.name}
          </div>
        );
      },
    },
    {
      accessorKey: 'sku',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            <Copy className="h-4 w-4 mr-2 text-muted-foreground" />
            {t('products.sku')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => <div className="font-mono text-sm">{row.getValue('sku')}</div>,
    },
    {
      accessorKey: 'partArticleNumber',
      header: () => (
        <div className="flex items-center">
          <Barcode className="h-4 w-4 mr-2 text-muted-foreground" />
          {t('products.barcodeEan')}
        </div>
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="flex items-center gap-2">
            <Barcode className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-mono">{product.partArticleNumber || '-'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'category',
      header: t('products.category'),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <Badge variant="outline" className="bg-[#f0f7ff] text-[#0073ea] border-[#0073ea]/30">
            {getCategoryDisplayName(product.category)}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'subcategory',
      header: t('products.subcategory'),
      cell: ({ row }) => <div>{row.getValue('subcategory')}</div>,
    },
    {
      accessorKey: 'manufacturer',
      header: () => (
        <div className="flex items-center">
          <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
          {t('products.manufacturer')}
        </div>
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="flex items-center gap-2">
            {product.manufacturerLogo && (
              <div className="h-6 w-6 rounded-full overflow-hidden">
                <img
                  src={product.manufacturerLogo}
                  className="h-full w-full object-contain"
                  alt={product.manufacturer}
                />
              </div>
            )}
            <span>{product.manufacturer || '-'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'supplierName',
      header: () => (
        <div className="flex items-center">
          <Truck className="h-4 w-4 mr-2 text-muted-foreground" />
          {t('products.supplierName')}
        </div>
      ),
      cell: ({ row }) => <div>{row.getValue('supplierName') || '-'}</div>,
    },
    {
      accessorKey: 'description',
      header: () => (
        <div className="flex items-center">
          <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
          {t('products.description')}
        </div>
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="max-w-xs truncate" title={product.description}>
            {product.description ?
              (product.description.length > 50 ?
                `${product.description.substring(0, 50)}...` :
                product.description) :
              '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'additionalImages',
      header: () => (
        <div className="flex items-center">
          <Image className="h-4 w-4 mr-2 text-muted-foreground" />
          {t('products.additionalImages')}
        </div>
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="flex gap-1">
            {product.additionalImages && product.additionalImages.length > 0 ? (
              <>
                <div className="h-8 w-8 rounded-md border bg-white overflow-hidden flex items-center justify-center">
                  <img
                    src={product.additionalImages[0]}
                    className="h-full w-full object-contain"
                    alt={`${product.name} additional`}
                  />
                </div>
                {product.additionalImages.length > 1 && (
                  <Badge variant="outline" className="bg-muted">+{product.additionalImages.length - 1}</Badge>
                )}
              </>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'price',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            {t('products.price')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const price = parseFloat(row.getValue('price'));
        return <div>{price ? `$${price.toFixed(2)}` : '-'}</div>;
      },
    },
    {
      accessorKey: 'stockQuantity',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-medium"
          >
            {t('products.stock')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.getValue('stockQuantity') || 0}</div>,
    },
    {
      accessorKey: 'status',
      header: t('products.status'),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge variant={
            status === 'published' ? 'default' :
            status === 'draft' ? 'secondary' :
            'destructive'
          }>
            {status === 'published' ? t('products.published') :
             status === 'draft' ? t('products.draft') :
             t('products.outOfStock')}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const product = row.original;
        return (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 rounded-full bg-transparent hover:bg-[#dcdfec] focus:bg-[#dcdfec]"
            onClick={() => handleEditProduct(product)}
          >
            <PencilLine className="h-4 w-4 text-[#676879]" />
            <span className="sr-only">{t('products.edit')}</span>
          </Button>
        );
      },
    },
  ], [t]);

  // Initialize table
  const table = useReactTable({
    data: filteredProducts,
    columns: columns.filter(col => {
      if (col.id === 'select' || col.id === 'actions') return true;
      const columnId = 'accessorKey' in col ? col.accessorKey as string : col.id;
      return visibleColumns.includes(columnId);
    }),
    state: {
      sorting,
      columnFilters,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Handle edit product
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setIsEditDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      {/* Barcode Scanner Dialog */}
      <BarcodeScanner
        isOpen={isScannerOpen}
        onClose={() => setIsScannerOpen(false)}
        onScan={() => {}}
      />

      {/* Product Edit Dialog */}
      {editingProduct && (
        <ProductEditDialog
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          product={editingProduct}
          onSave={() => {}}
        />
      )}

      {/* Product Information Section */}
      <div className="bg-white border border-border rounded-md p-4 shadow-sm">
        <div className="border border-border rounded-md overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="border-b">
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={cn(
                          header.id === 'select' && "w-[50px]",
                          header.id === 'actions' && "w-[80px] text-center"
                        )}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      <div className="flex items-center justify-center">
                        <div className="h-6 w-6 animate-spin rounded-full border-2 border-electric-orange border-t-transparent" />
                      </div>
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      className="border-b"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            cell.column.id === 'select' && "px-3",
                            cell.column.id === 'actions' && "px-3 text-center"
                          )}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      {t('products.noResults')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center justify-between px-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredRowModel().rows.length} {t('products.itemsTotal')}
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">{t('products.rowsPerPage')}</p>
              <select
                value={table.getState().pagination.pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value));
                }}
                className="h-8 w-[70px] rounded-md border border-input bg-transparent px-2 py-1 text-sm"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              {t('products.page')} {table.getState().pagination.pageIndex + 1} {t('products.of')}{' '}
              {table.getPageCount()}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">{t('products.firstPage')}</span>
                <ChevronFirst className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">{t('products.previousPage')}</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">{t('products.nextPage')}</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">{t('products.lastPage')}</span>
                <ChevronLast className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}