
import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Check, X, Camera, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface EditableCellProps {
  value: string | number;
  onSave: (value: string | number) => void;  // Updated to accept string | number
  inputType: 'text' | 'number' | 'select' | 'textarea' | 'image' | 'barcode' | 'badge';
  options?: Array<{ value: string; label: string }>;
  className?: string;
}

export function EditableCell({
  value,
  onSave,
  inputType,
  options = [],
  className
}: EditableCellProps) {
  const [editValue, setEditValue] = useState<string | number>(value);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  useEffect(() => {
    // Focus the input when the component mounts for text/number inputs
    if (inputRef.current && (inputType === 'text' || inputType === 'number' || inputType === 'textarea')) {
      inputRef.current.focus();

      // Select all text if it's an input element
      if ('select' in inputRef.current) {
        inputRef.current.select();
      }
    }
  }, [inputType]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && inputType !== 'textarea') {
      e.preventDefault();
      onSave(editValue);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onSave(value); // Cancel by reverting to original value
    }
  };

  // Render different input types based on the inputType prop
  const renderEditInput = () => {
    switch (inputType) {
      case 'select':
        return (
          <Select
            value={String(editValue)}
            onValueChange={(val) => setEditValue(val)}
            defaultOpen={true}
          >
            <SelectTrigger className="w-full h-8">
              <SelectValue placeholder="Select..." />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'textarea':
        return (
          <Textarea
            ref={inputRef as React.RefObject<HTMLTextAreaElement>}
            value={String(editValue)}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="min-h-[80px] w-full"
            rows={3}
            autoFocus
          />
        );

      case 'image':
        return (
          <div className="flex flex-col gap-2">
            <div className="h-16 w-16 rounded-md border overflow-hidden bg-white flex items-center justify-center mb-1">
              {typeof editValue === 'string' && editValue ? (
                <img src={String(editValue)} className="h-full w-full object-contain" alt="Product" />
              ) : (
                <div className="flex items-center justify-center h-full w-full bg-gray-50">
                  <Camera className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
            </div>

            <Button
              type="button"
              variant="outline"
              size="sm"
              className="h-8 w-full gap-1 text-xs"
              onClick={() => {
                // Implement image upload logic
                // For now, use a placeholder
                const placeholders = [
                  '/placeholder.svg',
                  'https://images.unsplash.com/photo-1619252584172-a83a949b6efd',
                  'https://images.unsplash.com/photo-1551816230-ef5d99c0f3b6'
                ];
                const randomImg = placeholders[Math.floor(Math.random() * placeholders.length)];
                setEditValue(randomImg);
              }}
            >
              <Upload className="h-3 w-3" />
              Upload
            </Button>
          </div>
        );

      case 'partArticleNumber':
        return (
          <div className="flex flex-col gap-1">
            <Input
              ref={inputRef as React.RefObject<HTMLInputElement>}
              type="text"
              value={String(editValue)}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="h-8 w-full font-mono"
              placeholder="Scan or enter part article number"
            />
            <div className="text-xs text-muted-foreground">
              Part Article Number
            </div>
          </div>
        );

      case 'badge':
        return (
          <div className="flex flex-wrap gap-2">
            {options.map(option => (
              <Badge
                key={option.value}
                variant={option.value === String(editValue) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer",
                  option.value === String(editValue) ? "bg-blue-500" : "bg-transparent"
                )}
                onClick={() => setEditValue(option.value)}
              >
                {option.label}
              </Badge>
            ))}
          </div>
        );

      default:
        return (
          <Input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type={inputType}
            value={String(editValue)}
            onChange={(e) => {
              const val = inputType === 'number'
                ? parseFloat(e.target.value) || 0
                : e.target.value;
              setEditValue(val);
            }}
            onKeyDown={handleKeyDown}
            className="h-8 w-full"
            step={inputType === 'number' ? "0.01" : undefined}
          />
        );
    }
  };

  return (
    <div className={cn("flex gap-1 items-start", className)}>
      <div className="flex-1">
        {renderEditInput()}
      </div>

      <div className="flex flex-col gap-1">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={() => onSave(editValue)}
          className="h-6 w-6 bg-algerian-green/10 text-algerian-green hover:bg-algerian-green/20"
        >
          <Check className="h-3 w-3" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={() => onSave(value)} // Cancel
          className="h-6 w-6 bg-destructive/10 text-destructive hover:bg-destructive/20"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
