import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  Navigation, 
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  AlertCircle,
  ExternalLink
} from 'lucide-react';

import { useLocation } from '@/contexts/LocationContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { SmartMapSelector } from '@/components/location/SmartMapSelector';
import { CheckoutData } from '@/pages/BasketPage';

interface DeliveryAddressStepProps {
  data: CheckoutData;
  onComplete: (data: Partial<CheckoutData>) => void;
  onBack: () => void;
}

export function DeliveryAddressStep({ data, onComplete, onBack }: DeliveryAddressStepProps) {
  const { t } = useTranslation();
  const { selectedLocation, getLocationString } = useLocation();
  
  const [formData, setFormData] = useState({
    deliveryAddress: data.deliveryAddress || '',
    deliveryCoordinates: data.deliveryCoordinates || null,
    deliveryWilaya: data.deliveryWilaya || '',
    googleMapsUrl: data.googleMapsUrl || '',
    specialInstructions: data.specialInstructions || ''
  });
  
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState('');

  // Update form when location context changes (always update, not just when empty)
  useEffect(() => {
    if (selectedLocation) {
      const coordinates = selectedLocation.coordinates;
      console.log('🗺️ Location context updated, updating form data:', selectedLocation);

      setFormData(prev => ({
        ...prev,
        deliveryAddress: selectedLocation.formatted_address,
        deliveryCoordinates: coordinates,
        deliveryWilaya: selectedLocation.wilaya,
        googleMapsUrl: `https://maps.google.com/?q=${coordinates.lat},${coordinates.lng}`
      }));

      // Clear any validation errors when location is updated
      setValidationError('');
    }
  }, [selectedLocation]);

  const handleLocationSelect = (lat: number, lng: number) => {
    console.log('🗺️ DeliveryAddressStep: Location selected from map:', { lat, lng });
    // The location context will handle the update and trigger our useEffect
  };

  const handleAddressChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setValidationError('');
  };

  const validateAddress = async () => {
    setIsValidating(true);
    setValidationError('');

    try {
      // Validate required fields
      if (!formData.deliveryAddress.trim()) {
        throw new Error(t('checkout.step2.errors.addressRequired'));
      }

      if (!formData.deliveryWilaya.trim()) {
        throw new Error(t('checkout.step2.errors.wilayaRequired'));
      }

      if (!formData.deliveryCoordinates) {
        throw new Error(t('checkout.step2.errors.coordinatesRequired'));
      }

      // Validate address format (basic check)
      if (formData.deliveryAddress.length < 10) {
        throw new Error(t('checkout.step2.errors.addressTooShort'));
      }

      // Generate Google Maps URL if not present
      let googleMapsUrl = formData.googleMapsUrl;
      if (!googleMapsUrl && formData.deliveryCoordinates) {
        googleMapsUrl = `https://maps.google.com/?q=${formData.deliveryCoordinates.lat},${formData.deliveryCoordinates.lng}`;
      }

      // Simulate validation delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Complete the step
      onComplete({
        deliveryAddress: formData.deliveryAddress.trim(),
        deliveryCoordinates: formData.deliveryCoordinates,
        deliveryWilaya: formData.deliveryWilaya.trim(),
        googleMapsUrl,
        specialInstructions: formData.specialInstructions.trim()
      });

    } catch (error) {
      setValidationError(error instanceof Error ? error.message : t('checkout.step2.errors.validationFailed'));
    } finally {
      setIsValidating(false);
    }
  };

  const isFormValid = () => {
    return formData.deliveryAddress.trim() && 
           formData.deliveryWilaya.trim() && 
           formData.deliveryCoordinates;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-6 w-6 text-[#fa7b00]" />
          <span>{t('checkout.step2.title')}</span>
        </CardTitle>
        <p className="text-gray-600">
          {t('checkout.step2.description')}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Interactive Map */}
          <div className="space-y-3">
            <Label className="text-base font-medium">
              {t('checkout.step2.selectLocation')}
            </Label>
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <SmartMapSelector
                height="300px"
                showCurrentLocationButton={true}
                showSearch={true}
                className="w-full"
                onLocationSelect={handleLocationSelect}
              />
            </div>
            {selectedLocation && (
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>{t('checkout.step2.locationSelected')}</span>
              </div>
            )}
          </div>

          {/* Address Details */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="delivery-address" className="text-base font-medium">
                {t('checkout.step2.deliveryAddress')} *
              </Label>
              <Textarea
                id="delivery-address"
                placeholder={t('checkout.step2.addressPlaceholder')}
                value={formData.deliveryAddress}
                onChange={(e) => handleAddressChange('deliveryAddress', e.target.value)}
                className="min-h-[80px]"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="delivery-wilaya" className="text-base font-medium">
                  {t('checkout.step2.wilaya')} *
                </Label>
                <Input
                  id="delivery-wilaya"
                  placeholder={t('checkout.step2.wilayaPlaceholder')}
                  value={formData.deliveryWilaya}
                  onChange={(e) => handleAddressChange('deliveryWilaya', e.target.value)}
                  required
                />
              </div>

              {formData.googleMapsUrl && (
                <div className="space-y-2">
                  <Label className="text-base font-medium">
                    {t('checkout.step2.googleMapsUrl')}
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      {t('checkout.step2.confirmed')}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(formData.googleMapsUrl, '_blank')}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      {t('checkout.step2.viewOnMaps')}
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="special-instructions" className="text-base font-medium">
                {t('checkout.step2.specialInstructions')}
              </Label>
              <Textarea
                id="special-instructions"
                placeholder={t('checkout.step2.instructionsPlaceholder')}
                value={formData.specialInstructions}
                onChange={(e) => handleAddressChange('specialInstructions', e.target.value)}
                className="min-h-[60px]"
              />
              <p className="text-xs text-gray-500">
                {t('checkout.step2.instructionsHelp')}
              </p>
            </div>
          </div>

          {/* Current Location Summary */}
          {formData.deliveryAddress && (
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                <Navigation className="h-4 w-4 mr-2" />
                {t('checkout.step2.deliverySummary')}
              </h4>
              <div className="text-sm text-blue-800">
                <p className="font-medium">{formData.deliveryWilaya}</p>
                <p className="mt-1">{formData.deliveryAddress}</p>
                {formData.specialInstructions && (
                  <p className="mt-2 text-blue-700">
                    <span className="font-medium">{t('checkout.step2.instructions')}:</span> {formData.specialInstructions}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Validation Error */}
          {validationError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-red-800">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">{validationError}</span>
              </div>
            </div>
          )}

          {/* Professional Navigation */}
          <div className="flex justify-between items-center pt-8 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onBack}
              disabled={isValidating}
              className="px-6 py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 rounded-lg font-medium"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.back')}
            </Button>

            <Button
              onClick={validateAddress}
              disabled={!isFormValid() || isValidating}
              className="px-8 py-3 bg-gradient-to-r from-[#fa7b00] to-[#ff9500] hover:from-[#e56f00] hover:to-[#e58500] text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isValidating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  {t('checkout.step2.validating')}
                </>
              ) : (
                <>
                  {t('common.continue')}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );
}
