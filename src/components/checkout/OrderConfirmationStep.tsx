import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  MapPin, 
  CreditCard,
  Package,
  Truck,
  Store,
  User,
  Phone,
  ArrowLeft,
  ExternalLink,
  AlertTriangle,
  Building2
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import { useConsumerAuth } from '@/components/auth/ConsumerAuthGuard';
import { CartItem } from '@/contexts/CartContext';
import { CheckoutData } from '@/pages/BasketPage';
import { supabase } from '@/services/authService';
import { getFormattedWilaya } from '@/data/algeria-cities';
import { calculateCheckoutFees } from '@/services/arouzFeeService';

interface OrderConfirmationStepProps {
  data: CheckoutData;
  cartItems: CartItem[];
  onConfirm: () => void;
  onBack: () => void;
  isProcessing: boolean;
}

export function OrderConfirmationStep({
  data,
  cartItems,
  onConfirm,
  onBack,
  isProcessing
}: OrderConfirmationStepProps) {
  const { t } = useTranslation();
  const { user, profile } = useAuth();
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [consumerProfile, setConsumerProfile] = useState<any>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [feeBreakdown, setFeeBreakdown] = useState<any>(null);
  const [isCalculatingFees, setIsCalculatingFees] = useState(false);

  // Fetch latest consumer profile data from database
  const fetchConsumerProfile = async () => {
    try {
      setIsLoadingProfile(true);
      console.log('🔍 [OrderConfirmation] Starting consumer profile fetch...');

      // First try to get from localStorage for phone
      const phoneSession = localStorage.getItem('phone_auth_session');
      if (!phoneSession) {
        console.log('❌ [OrderConfirmation] No phone session found in localStorage');
        setConsumerProfile(null);
        return;
      }

      const session = JSON.parse(phoneSession);
      console.log('📱 [OrderConfirmation] Phone session data:', {
        user_phone: session.user?.phone,
        profile_phone: session.profile?.phone,
        profile_full_name: session.profile?.full_name,
        profile_original_full_name: session.profile?.original_full_name,
        profile_role: session.profile?.role
      });

      const phone = session.user?.phone || session.profile?.phone;

      if (!phone) {
        console.log('❌ [OrderConfirmation] No phone found in session');
        setConsumerProfile(null);
        return;
      }

      // Fetch latest profile data from database
      console.log('🔍 [OrderConfirmation] Fetching latest consumer profile for phone:', phone);
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('phone', phone)
        .eq('role', 'consumer')
        .single();

      if (error) {
        console.error('❌ [OrderConfirmation] Error fetching consumer profile from database:', error);
        console.log('🔄 [OrderConfirmation] Falling back to localStorage data');

        const fallbackProfile = {
          full_name: session.profile?.full_name || session.profile?.first_name || 'Consumer',
          original_full_name: session.profile?.original_full_name || '',
          phone: phone,
          ...session.profile
        };

        console.log('📋 [OrderConfirmation] Using fallback profile:', fallbackProfile);
        setConsumerProfile(fallbackProfile);
      } else {
        console.log('✅ [OrderConfirmation] Latest consumer profile fetched from database:', {
          id: profileData.id,
          full_name: profileData.full_name,
          original_full_name: profileData.original_full_name,
          phone: profileData.phone,
          role: profileData.role
        });

        const finalProfile = {
          full_name: profileData.full_name || profileData.first_name || 'Consumer',
          original_full_name: profileData.original_full_name || '',
          phone: profileData.phone || phone,
          ...profileData
        };

        console.log('📋 [OrderConfirmation] Final profile to display:', finalProfile);
        setConsumerProfile(finalProfile);
      }
    } catch (error) {
      console.error('❌ [OrderConfirmation] Error in fetchConsumerProfile:', error);
      setConsumerProfile(null);
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Fetch profile data on component mount and when auth changes
  useEffect(() => {
    fetchConsumerProfile();
  }, []);

  // Listen for auth success events to refresh profile
  useEffect(() => {
    const handleAuthSuccess = () => {
      console.log('🔄 [OrderConfirmation] Auth success event received, refreshing profile...');
      setTimeout(() => {
        fetchConsumerProfile();
      }, 1000); // Small delay to ensure database is updated
    };

    window.addEventListener('auth:success', handleAuthSuccess);
    return () => window.removeEventListener('auth:success', handleAuthSuccess);
  }, []);

  // Calculate fees when component mounts or shipping data changes
  useEffect(() => {
    calculateFees();
  }, [data.shippingInfo, cartItems]);

  // Calculate fees when component mounts or shipping info changes
  const calculateFees = async () => {
    try {
      setIsCalculatingFees(true);
      console.log('💰 [OrderConfirmation] Calculating fees...');

      const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const shippingCost = data.shippingInfo?.shipping_cost || 0;

      const fees = await calculateCheckoutFees(subtotal, shippingCost, cartItems);
      console.log('💰 [OrderConfirmation] Fee calculation result:', fees);

      setFeeBreakdown(fees);
    } catch (error) {
      console.error('❌ [OrderConfirmation] Error calculating fees:', error);
      setFeeBreakdown(null);
    } finally {
      setIsCalculatingFees(false);
    }
  };

  // Calculate totals with real shipping and AROUZ fees
  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shippingCost = data.shippingInfo?.shipping_cost || 0;
  const arouzFees = feeBreakdown?.total_fees || 0;
  const totalAmount = subtotal + shippingCost + arouzFees;

  // Group items by shipping origin (wilaya) since supplier names are empty
  const itemsBySupplier = cartItems.reduce((acc, item) => {
    // Use shipping origin as the key since supplier names are empty
    const supplierKey = item.shippingOrigin || 'Unknown Location';
    if (!acc[supplierKey]) {
      acc[supplierKey] = {
        items: [],
        location: item.shippingOrigin || 'Location TBD'
      };
    }
    acc[supplierKey].items.push(item);
    return acc;
  }, {} as Record<string, { items: CartItem[]; location: string }>);

  // NO shipping cost calculation - will be added in later stages

  const handleConfirmOrder = () => {
    if (termsAccepted) {
      onConfirm();
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CheckCircle className="h-6 w-6 text-[#fa7b00]" />
          <span>{t('checkout.step4.title')}</span>
        </CardTitle>
        <p className="text-gray-600">
          {t('checkout.step4.description')}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">


        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Professional Order Confirmation Sections */}
          <div className="space-y-4">
            {/* Customer Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center text-lg">
                <User className="h-5 w-5 mr-3 text-[#fa7b00]" />
                {t('checkout.step4.customerInfo')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-[#fa7b00] rounded-full"></div>
                  <div>
                    <span className="text-sm text-gray-600">{t('checkout.step4.name')}</span>
                    <p className="font-medium text-gray-900">
                      {isLoadingProfile ? (
                        <span className="text-gray-500">Loading...</span>
                      ) : (
                        consumerProfile?.full_name || t('checkout.step4.consumer')
                      )}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-[#fa7b00]" />
                  <div>
                    <span className="text-sm text-gray-600">{t('checkout.step4.phone')}</span>
                    <p className="font-medium text-gray-900">{consumerProfile?.phone}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center text-lg">
                <MapPin className="h-5 w-5 mr-3 text-[#fa7b00]" />
                {t('checkout.step4.deliveryInfo')}
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[#fa7b00] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-600">{t('checkout.step4.wilaya')}</span>
                      <p className="font-medium text-gray-900">{data.deliveryWilaya}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#fa7b00] rounded-full mt-2"></div>
                  <div className="flex-1">
                    <span className="text-sm text-gray-600">{t('checkout.step4.address')}</span>
                    <p className="font-medium text-gray-900 mt-1">{data.deliveryAddress}</p>
                  </div>
                </div>
                {data.specialInstructions && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-[#fa7b00] rounded-full mt-2"></div>
                    <div className="flex-1">
                      <span className="text-sm text-gray-600">{t('checkout.step4.instructions')}</span>
                      <p className="font-medium text-gray-900 mt-1">{data.specialInstructions}</p>
                    </div>
                  </div>
                )}
                <div className="pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(data.googleMapsUrl, '_blank')}
                    className="text-[#fa7b00] border-[#fa7b00] hover:bg-[#fa7b00] hover:text-white transition-colors"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    {t('checkout.step4.viewOnMaps')}
                  </Button>
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center text-lg">
                <CreditCard className="h-5 w-5 mr-3 text-[#fa7b00]" />
                {t('checkout.step4.paymentMethod')}
              </h3>
              <div className="flex items-center space-x-4">
                {data.paymentMethod === 'cash_on_delivery' ? (
                  <>
                    <div className="flex items-center space-x-3">
                      <Truck className="h-6 w-6 text-green-600" />
                      <div>
                        <p className="font-medium text-gray-900">{t('checkout.step4.cashOnDelivery')}</p>
                        <p className="text-sm text-gray-600">{t('checkout.step4.express24h')}</p>
                      </div>
                    </div>
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      {t('checkout.step4.express24h')}
                    </Badge>
                  </>
                ) : (
                  <>
                    <div className="flex items-center space-x-3">
                      <Store className="h-6 w-6 text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">{t('checkout.step4.storePickup')}</p>
                        <p className="text-sm text-gray-600">{t('checkout.step4.noDeliveryFee')}</p>
                      </div>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                      {t('checkout.step4.noDeliveryFee')}
                    </Badge>
                  </>
                )}
              </div>
            </div>

            {/* Shipping Origins - Show each product with its origin */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center text-lg">
                <Building2 className="h-5 w-5 mr-3 text-[#fa7b00]" />
                {t('checkout.step4.shippingOrigins')}
              </h3>
              <div className="space-y-3">
                {cartItems.map((item, index) => {
                  const formattedWilaya = getFormattedWilaya(item.shippingOrigin) || item.shippingOrigin || 'Location TBD';
                  return (
                    <div key={`${item.id}-${index}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <img
                          src={item.image || '/placeholder.svg'}
                          alt={item.name}
                          className="w-10 h-10 object-cover rounded-md"
                        />
                        <div>
                          <p className="font-medium text-gray-900 text-sm">{item.name}</p>
                          <p className="text-xs text-gray-600">
                            Ships from: {formattedWilaya}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="border-gray-300 text-gray-600 text-xs">
                        {formattedWilaya}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
              <Package className="h-5 w-5 mr-2" />
              {t('checkout.step4.orderSummary')}
            </h3>
            
            {/* Items */}
            <div className="space-y-3 mb-4">
              {cartItems.map((item) => (
                <div key={item.id} className="flex items-center space-x-3">
                  <img
                    src={item.image || '/placeholder.svg'}
                    alt={item.name}
                    className="w-12 h-12 object-cover rounded-md"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {item.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {item.quantity} × {new Intl.NumberFormat('fr-DZ', {
                        style: 'currency',
                        currency: 'DZD',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                      }).format(item.price)}
                    </p>
                  </div>
                  <p className="text-sm font-medium">
                    {new Intl.NumberFormat('fr-DZ', {
                      style: 'currency',
                      currency: 'DZD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(item.price * item.quantity)}
                  </p>
                </div>
              ))}
            </div>

            <Separator />

            {/* Pricing Breakdown - Real shipping and AROUZ fees */}
            <div className="space-y-2 mt-4">
              <div className="flex justify-between text-sm">
                <span>{t('checkout.step4.subtotal')}</span>
                <span>{new Intl.NumberFormat('fr-DZ', {
                  style: 'currency',
                  currency: 'DZD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                }).format(subtotal)}</span>
              </div>

              {/* Shipping Cost */}
              <div className="flex justify-between text-sm">
                <span>{t('checkout.step4.shipping')}</span>
                <span>
                  {isCalculatingFees ? (
                    <span className="text-gray-500">Calculating...</span>
                  ) : shippingCost > 0 ? (
                    new Intl.NumberFormat('fr-DZ', {
                      style: 'currency',
                      currency: 'DZD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(shippingCost)
                  ) : (
                    <span className="text-green-600">Free</span>
                  )}
                </span>
              </div>

              {/* AROUZ Fees */}
              <div className="flex justify-between text-sm">
                <span>{t('checkout.step4.arouzFee')}</span>
                <span>
                  {isCalculatingFees ? (
                    <span className="text-gray-500">Calculating...</span>
                  ) : arouzFees > 0 ? (
                    new Intl.NumberFormat('fr-DZ', {
                      style: 'currency',
                      currency: 'DZD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(arouzFees)
                  ) : (
                    <span className="text-green-600">Free</span>
                  )}
                </span>
              </div>
            </div>

            <Separator className="my-4" />

            <div className="flex justify-between font-bold text-lg">
              <span>{t('checkout.step4.total')}</span>
              <span className="text-[#fa7b00]">
                {new Intl.NumberFormat('fr-DZ', {
                  style: 'currency',
                  currency: 'DZD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                }).format(totalAmount)}
              </span>
            </div>
          </div>

          {/* Terms and Conditions */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <div className="flex items-start space-x-4">
              <Checkbox
                id="terms"
                checked={termsAccepted}
                onCheckedChange={(checked) => setTermsAccepted(checked as boolean)}
                className="mt-1 border-[#fa7b00] data-[state=checked]:bg-[#fa7b00] data-[state=checked]:border-[#fa7b00]"
              />
              <div className="flex-1">
                <label htmlFor="terms" className="text-sm text-gray-700 cursor-pointer leading-relaxed">
                  {t('checkout.step4.termsText')} {' '}
                  <button className="text-[#fa7b00] hover:underline font-medium">
                    {t('checkout.step4.termsLink')}
                  </button>
                  {' '} {t('checkout.step4.and')} {' '}
                  <button className="text-[#fa7b00] hover:underline font-medium">
                    {t('checkout.step4.privacyLink')}
                  </button>
                </label>
              </div>
            </div>
          </div>

          {/* Important Notice */}
          <div className="bg-white border border-amber-200 rounded-lg p-6 shadow-sm">
            <div className="flex items-start space-x-4">
              <AlertTriangle className="h-6 w-6 text-amber-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="font-semibold text-amber-800 mb-2">{t('checkout.step4.warningTitle')}</p>
                <p className="text-sm text-amber-700 leading-relaxed">{t('checkout.step4.warningText')}</p>
              </div>
            </div>
          </div>

          {/* Professional Navigation */}
          <div className="flex justify-between items-center pt-8 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onBack}
              disabled={isProcessing}
              className="px-6 py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 rounded-lg font-medium disabled:opacity-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.back')}
            </Button>

            <Button
              onClick={handleConfirmOrder}
              disabled={!termsAccepted || isProcessing}
              className="px-10 py-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none text-lg"
            >
              {isProcessing ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                  {t('checkout.step4.processing')}
                </>
              ) : (
                <>
                  <CheckCircle className="h-5 w-5 mr-3" />
                  {t('checkout.step4.confirmOrder')}
                </>
              )}
            </Button>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );
}
