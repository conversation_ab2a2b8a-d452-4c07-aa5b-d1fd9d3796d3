import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import {
  User,
  Phone,
  Shield,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

import { useConsumerAuth } from '@/components/auth/ConsumerAuthGuard';
import { CheckoutAuthTrigger } from '@/components/auth/CheckoutAuthTrigger';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/services/authService';

interface AuthenticationStepProps {
  onComplete: () => void;
  onBack: () => void;
}

export function AuthenticationStep({ onComplete, onBack }: AuthenticationStepProps) {
  const { t } = useTranslation();
  const { isAuthenticated, isLoading } = useConsumerAuth();
  const [consumerProfile, setConsumerProfile] = useState<any>(null);

  // Fetch latest consumer profile data from database
  const fetchConsumerProfile = async () => {
    try {
      // First try to get from localStorage for phone
      const phoneSession = localStorage.getItem('phone_auth_session');
      if (!phoneSession) {
        setConsumerProfile(null);
        return;
      }

      const session = JSON.parse(phoneSession);
      const phone = session.user?.phone || session.profile?.phone;

      if (!phone) {
        setConsumerProfile(null);
        return;
      }

      // Fetch latest profile data from database
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('phone', phone)
        .eq('role', 'consumer')
        .single();

      if (error) {
        console.error('Error fetching consumer profile:', error);
        // Fallback to localStorage data
        setConsumerProfile({
          full_name: session.profile?.full_name || session.profile?.first_name || 'Consumer',
          original_full_name: session.profile?.original_full_name || '',
          phone: phone,
          ...session.profile
        });
      } else {
        setConsumerProfile({
          full_name: profileData.full_name || profileData.first_name || 'Consumer',
          original_full_name: profileData.original_full_name || '',
          phone: profileData.phone || phone,
          ...profileData
        });
      }
    } catch (error) {
      console.error('Error in fetchConsumerProfile:', error);
      setConsumerProfile(null);
    }
  };

  // Fetch profile data when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchConsumerProfile();
    }
  }, [isAuthenticated]);

  // Auto-complete when authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      // Small delay to show the success state
      const timer = setTimeout(() => {
        onComplete();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, isLoading, onComplete]);

  // Handle authentication success
  const handleAuthSuccess = (user: any, action: 'login' | 'signup') => {
    console.log('✅ Authentication successful in checkout:', { user, action });
    // The useEffect will handle auto-completion when isAuthenticated becomes true
  };

  return (
    <CheckoutAuthTrigger onAuthSuccess={handleAuthSuccess}>
      {(triggerAuth) => (
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-6 w-6 text-[#fa7b00]" />
              <span>{t('checkout.step1.title')}</span>
            </CardTitle>
            <p className="text-gray-600">
              {t('checkout.step1.description')}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {!isAuthenticated ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                {/* Authentication Required */}
                <div className="text-center py-8">
                  <div className="w-20 h-20 bg-[#fa7b00]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="h-10 w-10 text-[#fa7b00]" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {t('checkout.step1.authRequired')}
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    {t('checkout.step1.authDescription')}
                  </p>
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('🔘 Verify Phone Number button clicked - triggering single modal');
                      triggerAuth();
                    }}
                    size="lg"
                    className="bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white px-8 cursor-pointer"
                    type="button"
                  >
                    <Phone className="h-5 w-5 mr-2" />
                    {t('checkout.step1.verifyPhone')}
                  </Button>
                </div>

              {/* Security Features */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                  <Shield className="h-5 w-5 text-green-600 mr-2" />
                  {t('checkout.step1.securityFeatures')}
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">
                      {t('checkout.step1.feature1')}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">
                      {t('checkout.step1.feature2')}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">
                      {t('checkout.step1.feature3')}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="space-y-6"
            >
              {/* Authentication Success */}
              <div className="text-center py-8">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-10 w-10 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {t('checkout.step1.authSuccess')}
                </h3>
                <p className="text-gray-600 mb-4">
                  {t('checkout.step1.welcomeBack')}
                </p>
                
                {/* User Info */}
                <div className="bg-green-50 rounded-lg p-4 max-w-md mx-auto">
                  <div className="flex items-center justify-center space-x-3">
                    <div className="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-green-700" />
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-green-900">
                        {consumerProfile?.full_name || t('checkout.step1.consumer')}
                      </p>
                      <p className="text-sm text-green-700">
                        {consumerProfile?.phone}
                      </p>
                    </div>
                    <Badge variant="secondary" className="bg-green-200 text-green-800">
                      {t('checkout.step1.verified')}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Auto-proceeding message */}
              <div className="text-center">
                <div className="inline-flex items-center space-x-2 text-sm text-gray-600">
                  <div className="w-4 h-4 border-2 border-[#fa7b00] border-t-transparent rounded-full animate-spin"></div>
                  <span>{t('checkout.step1.proceeding')}</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Professional Navigation */}
          <div className="flex justify-between items-center pt-8 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onBack}
              disabled={true} // First step, can't go back
              className="px-6 py-3 border-2 border-gray-200 text-gray-400 cursor-not-allowed opacity-50 rounded-lg font-medium"
            >
              {t('common.back')}
            </Button>

            {isAuthenticated && (
              <Button
                onClick={onComplete}
                className="px-8 py-3 bg-gradient-to-r from-[#fa7b00] to-[#ff9500] hover:from-[#e56f00] hover:to-[#e58500] text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                {t('common.continue')}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
            </div>
          </CardContent>
        </Card>
      )}
    </CheckoutAuthTrigger>
  );
}
