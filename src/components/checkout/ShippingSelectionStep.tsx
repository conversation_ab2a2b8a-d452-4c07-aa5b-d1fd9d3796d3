import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Truck, Package, Clock, MapPin, Star, AlertCircle, RefreshCw } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  getShippingOptionsForCheckout, 
  ShippingCalculation 
} from '@/services/shippingService';
import { CheckoutData } from '@/pages/BasketPage';

interface ShippingSelectionStepProps {
  data: CheckoutData;
  orderValue: number;
  onComplete: (shippingData: any) => void;
  onBack: () => void;
}

export function ShippingSelectionStep({
  data,
  orderValue,
  onComplete,
  onBack
}: ShippingSelectionStepProps) {
  const { t } = useTranslation();
  const [shippingOptions, setShippingOptions] = useState<ShippingCalculation[]>([]);
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [shippingNotes, setShippingNotes] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load shipping options
  useEffect(() => {
    loadShippingOptions();
  }, [data.deliveryWilaya, orderValue]);

  const loadShippingOptions = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🚚 Loading shipping options for:', {
        wilaya: data.deliveryWilaya,
        orderValue
      });

      const options = await getShippingOptionsForCheckout(orderValue, data.deliveryWilaya);
      
      console.log('✅ Shipping options loaded:', options);
      setShippingOptions(options);
      
      // Auto-select first option if available
      if (options.length > 0) {
        setSelectedOption(options[0].company_id);
      }
    } catch (err) {
      console.error('❌ Error loading shipping options:', err);
      setError('Failed to load shipping options. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    const selectedShipping = shippingOptions.find(opt => opt.company_id === selectedOption);
    
    if (!selectedShipping) {
      setError('Please select a shipping option');
      return;
    }

    const shippingData = {
      shippingInfo: {
        company_id: selectedShipping.company_id,
        company_name: selectedShipping.company_name,
        shipping_cost: selectedShipping.total_cost,
        shipping_method: 'standard',
        estimated_delivery: selectedShipping.estimated_delivery,
        shipping_notes: shippingNotes
      }
    };

    console.log('✅ Shipping selection completed:', shippingData);
    onComplete(shippingData);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-[#fa7b00] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading shipping options...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Truck className="h-6 w-6 text-[#fa7b00]" />
          <span>Select Shipping Method</span>
        </CardTitle>
        <p className="text-gray-600">
          Choose your preferred shipping company for delivery to <strong>{data.deliveryWilaya}</strong>
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-red-800 font-medium">Error</p>
              <p className="text-red-700 text-sm mt-1">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadShippingOptions}
                className="mt-3 text-red-700 border-red-300 hover:bg-red-50"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </div>
        )}

        {shippingOptions.length === 0 && !error ? (
          <div className="text-center py-8">
            <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 font-medium">No shipping options available</p>
            <p className="text-gray-500 text-sm mt-1">
              Unfortunately, no shipping companies serve {data.deliveryWilaya} for orders of this value.
            </p>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <RadioGroup value={selectedOption} onValueChange={setSelectedOption}>
              {shippingOptions.map((option) => (
                <div key={option.company_id} className="relative">
                  <Label
                    htmlFor={option.company_id}
                    className={`flex items-center space-x-4 p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                      selectedOption === option.company_id
                        ? 'border-[#fa7b00] bg-orange-50'
                        : 'border-gray-200 hover:border-[#fa7b00]/50 hover:bg-gray-50'
                    }`}
                  >
                    <RadioGroupItem value={option.company_id} id={option.company_id} />
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <h3 className="font-semibold text-gray-900">{option.company_name}</h3>
                          {option.rating > 0 && (
                            <div className="flex items-center space-x-1">
                              <div className="flex">{renderStars(option.rating)}</div>
                              <span className="text-sm text-gray-600">({option.rating.toFixed(1)})</span>
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-lg text-[#fa7b00]">
                            {formatCurrency(option.total_cost)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4" />
                          <span>{option.estimated_delivery}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Package className="h-4 w-4" />
                          <span>{option.service_types.join(', ')}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4" />
                          <span>To {data.deliveryWilaya}</span>
                        </div>
                      </div>
                      
                      {option.notes && (
                        <p className="text-xs text-gray-500 mt-2 bg-gray-100 px-2 py-1 rounded">
                          {option.notes}
                        </p>
                      )}
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>

            {/* Special Instructions */}
            <div className="space-y-2 pt-4 border-t border-gray-200">
              <Label htmlFor="shipping-notes">Special Shipping Instructions (Optional)</Label>
              <Textarea
                id="shipping-notes"
                placeholder="Any special instructions for the shipping company (e.g., call before delivery, leave at reception, etc.)"
                value={shippingNotes}
                onChange={(e) => setShippingNotes(e.target.value)}
                className="min-h-[80px] resize-none"
              />
            </div>
          </motion.div>
        )}

        {/* Navigation */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={onBack}
            className="px-6 py-3"
          >
            Back to Payment
          </Button>

          <Button
            onClick={handleNext}
            disabled={!selectedOption || shippingOptions.length === 0}
            className="px-8 py-3 bg-[#fa7b00] hover:bg-[#fa7b00]/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Continue to Review
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
