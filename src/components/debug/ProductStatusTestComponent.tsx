import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';

/**
 * Debug component to test product status filtering and wholesale pricing tiers
 * This component helps verify that:
 * 1. Products with wholesale pricing tiers are properly saved
 * 2. Status-based filtering works correctly in marketplace
 * 3. Only "active" and "out_of_stock" products appear in marketplace
 */
export function ProductStatusTestComponent() {
  const [showDetails, setShowDetails] = useState(false);

  // Fetch marketplace products from both categories (from ALL users)
  const { products: tyreProducts, isLoading: tyresLoading } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading } = useMarketplaceProducts('brakes');

  // Combine all products
  const allProducts = [...tyreProducts, ...brakeProducts];

  // Filter products by status
  const activeProducts = allProducts.filter(p => p.status === 'active');
  const draftProducts = allProducts.filter(p => p.status === 'draft');
  const outOfStockProducts = allProducts.filter(p => p.status === 'out_of_stock');
  const discontinuedProducts = allProducts.filter(p => p.status === 'discontinued');

  // Filter products by pricing type
  const wholesaleProducts = allProducts.filter(p =>
    p.wholesalePricingTiers && p.wholesalePricingTiers.length > 0
  );
  const retailProducts = allProducts.filter(p =>
    p.retailPrice && p.retailPrice > 0
  );

  // Products that should appear in wholesale marketplace
  const visibleWholesaleProducts = allProducts.filter(p => {
    const hasWholesalePricing = p.wholesalePricingTiers && p.wholesalePricingTiers.length > 0;
    const isVisible = p.status === 'active' || p.status === 'out_of_stock';
    return hasWholesalePricing && isVisible;
  });

  // Products that should appear in retail marketplace
  const visibleRetailProducts = allProducts.filter(p => {
    const hasRetailPrice = p.retailPrice && p.retailPrice > 0;
    const isVisible = p.status === 'active' || p.status === 'out_of_stock';
    return hasRetailPrice && isVisible;
  });

  // Debug: Show detailed product information
  const debugProducts = allProducts.map(product => ({
    id: product.id,
    name: product.name,
    status: product.status,
    retailPrice: product.retailPrice,
    hasWholesalePricing: product.wholesalePricingTiers && product.wholesalePricingTiers.length > 0,
    wholesalePricingTiers: product.wholesalePricingTiers,
    marketplaceSection: product.marketplaceSection,
    category: product.category
  }));

  // Issues detection
  const productsWithoutPricing = allProducts.filter(p =>
    (!p.retailPrice || p.retailPrice <= 0) &&
    (!p.wholesalePricingTiers || p.wholesalePricingTiers.length === 0)
  );

  if (tyresLoading || brakesLoading) {
    return <div>Loading products...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Product Status & Visibility Test</h2>
        <Button
          variant="outline"
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Total Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allProducts.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Wholesale Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{wholesaleProducts.length}</div>
            <div className="text-xs text-muted-foreground">
              Visible: {visibleWholesaleProducts.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Retail Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{retailProducts.length}</div>
            <div className="text-xs text-muted-foreground">
              Visible: {visibleRetailProducts.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Hidden Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {draftProducts.length + discontinuedProducts.length}
            </div>
            <div className="text-xs text-muted-foreground">
              Draft: {draftProducts.length}, Discontinued: {discontinuedProducts.length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Products by Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <Badge className="bg-green-100 text-green-800">Active</Badge>
              <div className="text-lg font-semibold mt-1">{activeProducts.length}</div>
            </div>
            <div className="text-center">
              <Badge className="bg-gray-100 text-gray-800">Draft</Badge>
              <div className="text-lg font-semibold mt-1">{draftProducts.length}</div>
            </div>
            <div className="text-center">
              <Badge className="bg-red-100 text-red-800">Out of Stock</Badge>
              <div className="text-lg font-semibold mt-1">{outOfStockProducts.length}</div>
            </div>
            <div className="text-center">
              <Badge className="bg-purple-100 text-purple-800">Discontinued</Badge>
              <div className="text-lg font-semibold mt-1">{discontinuedProducts.length}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Marketplace Visibility */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">Wholesale Marketplace</CardTitle>
            <p className="text-sm text-muted-foreground">
              Products visible in /wholesale-offers (must have pricing tiers + active/out_of_stock status)
            </p>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold mb-2">{visibleWholesaleProducts.length} visible</div>
            {showDetails && (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {visibleWholesaleProducts.map(product => (
                  <div key={product.id} className="text-sm border-l-2 border-blue-200 pl-2">
                    <div className="font-medium">{product.name}</div>
                    <div className="text-xs text-muted-foreground">
                      Status: {product.status} | Tiers: {product.wholesalePricingTiers?.length || 0}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">Retail Marketplace</CardTitle>
            <p className="text-sm text-muted-foreground">
              Products visible in /my-vehicle-parts (must have retail price + active/out_of_stock status)
            </p>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold mb-2">{visibleRetailProducts.length} visible</div>
            {showDetails && (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {visibleRetailProducts.map(product => (
                  <div key={product.id} className="text-sm border-l-2 border-green-200 pl-2">
                    <div className="font-medium">{product.name}</div>
                    <div className="text-xs text-muted-foreground">
                      Status: {product.status} | Price: {product.retailPrice || 0} DA
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Issues Detection */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="text-yellow-800">Potential Issues</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            {wholesaleProducts.length === 0 && (
              <div className="text-yellow-700">
                ⚠️ No products have wholesale pricing tiers - they won't appear in wholesale marketplace
              </div>
            )}
            {visibleWholesaleProducts.length === 0 && wholesaleProducts.length > 0 && (
              <div className="text-yellow-700">
                ⚠️ Products have wholesale pricing but wrong status - check product status settings
              </div>
            )}
            {draftProducts.length > 0 && (
              <div className="text-yellow-700">
                ℹ️ {draftProducts.length} products are in draft status (hidden from marketplace)
              </div>
            )}
            {discontinuedProducts.length > 0 && (
              <div className="text-yellow-700">
                ℹ️ {discontinuedProducts.length} products are discontinued (hidden from marketplace)
              </div>
            )}
            {productsWithoutPricing.length > 0 && (
              <div className="text-red-700">
                🚨 {productsWithoutPricing.length} products have no pricing information (neither retail price nor wholesale tiers)
              </div>
            )}
            {retailProducts.length === 0 && (
              <div className="text-red-700">
                🚨 No products have retail pricing - they won't appear in retail marketplace
              </div>
            )}
            {visibleRetailProducts.length === 0 && retailProducts.length > 0 && (
              <div className="text-yellow-700">
                ⚠️ Products have retail pricing but wrong status - check product status settings
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Product Debug Information */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Product Information</CardTitle>
            <p className="text-sm text-muted-foreground">
              Debug information for all products showing pricing and marketplace section data
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {debugProducts.map(product => (
                <div key={product.id} className="border rounded-lg p-3 bg-gray-50">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    <div>
                      <strong>Name:</strong> {product.name}
                    </div>
                    <div>
                      <strong>Status:</strong>
                      <Badge className={`ml-1 ${
                        product.status === 'active' ? 'bg-green-100 text-green-800' :
                        product.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                        product.status === 'out_of_stock' ? 'bg-red-100 text-red-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {product.status}
                      </Badge>
                    </div>
                    <div>
                      <strong>Retail Price:</strong> {product.retailPrice ? `${product.retailPrice} DA` : 'None'}
                    </div>
                    <div>
                      <strong>Wholesale Tiers:</strong> {product.hasWholesalePricing ? 'Yes' : 'No'}
                    </div>
                    <div>
                      <strong>Category:</strong> {product.category}
                    </div>
                    <div>
                      <strong>Marketplace:</strong> {product.marketplaceSection || 'Not set'}
                    </div>
                    <div className="col-span-2">
                      <strong>Visible in:</strong>
                      {(product.retailPrice && product.retailPrice > 0 && (product.status === 'active' || product.status === 'out_of_stock')) && (
                        <Badge className="ml-1 bg-green-100 text-green-800">Retail</Badge>
                      )}
                      {(product.hasWholesalePricing && (product.status === 'active' || product.status === 'out_of_stock')) && (
                        <Badge className="ml-1 bg-blue-100 text-blue-800">Wholesale</Badge>
                      )}
                      {(!product.retailPrice || product.retailPrice <= 0) && !product.hasWholesalePricing && (
                        <Badge className="ml-1 bg-red-100 text-red-800">None</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
