/**
 * Authentication Isolation Test Component
 * 
 * This component tests the complete isolation between:
 * 1. Admin authentication (Supplier/Merchant)
 * 2. Consumer authentication (Phone-based)
 * 3. Shipping company authentication
 * 
 * CRITICAL: Verifies that logout from one system does NOT affect others
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Building, 
  Phone, 
  Truck,
  LogOut,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// Import authentication services
import { signOutAdminOnly } from '@/services/authService';
import { 
  isConsumerAuthenticated, 
  getCurrentConsumerSession, 
  logoutConsumer 
} from '@/services/simplifiedConsumerAuth';
import { 
  getShippingCompanySession, 
  clearShippingCompanySession 
} from '@/services/shippingCompanyAuthService';

interface AuthStatus {
  admin: {
    supplier: boolean;
    merchant: boolean;
  };
  consumer: boolean;
  shipping: boolean;
}

export function AuthenticationIsolationTest() {
  const [authStatus, setAuthStatus] = useState<AuthStatus>({
    admin: { supplier: false, merchant: false },
    consumer: false,
    shipping: false
  });
  const [testResults, setTestResults] = useState<string[]>([]);
  const { toast } = useToast();

  // Check authentication status
  const checkAuthStatus = () => {
    const status: AuthStatus = {
      admin: {
        supplier: localStorage.getItem('supplierRole') === 'true',
        merchant: localStorage.getItem('merchantRole') === 'true'
      },
      consumer: isConsumerAuthenticated(),
      shipping: !!getShippingCompanySession()
    };
    
    setAuthStatus(status);
    return status;
  };

  // Test admin logout isolation
  const testAdminLogout = async () => {
    const initialStatus = checkAuthStatus();
    const results: string[] = [];
    
    results.push('🔍 TESTING: Admin logout isolation...');
    results.push(`Initial state - Admin: ${initialStatus.admin.supplier || initialStatus.admin.merchant}, Consumer: ${initialStatus.consumer}, Shipping: ${initialStatus.shipping}`);
    
    try {
      // Perform admin logout
      await signOutAdminOnly();
      
      // Check status after admin logout
      const afterStatus = checkAuthStatus();
      
      // Verify admin is logged out
      const adminLoggedOut = !afterStatus.admin.supplier && !afterStatus.admin.merchant;
      results.push(`✅ Admin logout: ${adminLoggedOut ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify consumer session preserved
      const consumerPreserved = afterStatus.consumer === initialStatus.consumer;
      results.push(`${consumerPreserved ? '✅' : '❌'} Consumer session preserved: ${consumerPreserved ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify shipping session preserved
      const shippingPreserved = afterStatus.shipping === initialStatus.shipping;
      results.push(`${shippingPreserved ? '✅' : '❌'} Shipping session preserved: ${shippingPreserved ? 'SUCCESS' : 'FAILED'}`);
      
      const testPassed = adminLoggedOut && consumerPreserved && shippingPreserved;
      results.push(`🎯 ADMIN LOGOUT TEST: ${testPassed ? 'PASSED ✅' : 'FAILED ❌'}`);
      
      if (testPassed) {
        toast({
          title: "Admin Logout Test Passed",
          description: "Admin logout properly isolated from other sessions",
        });
      } else {
        toast({
          title: "Admin Logout Test Failed",
          description: "Admin logout affected other authentication sessions",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      results.push(`❌ ERROR during admin logout test: ${error}`);
    }
    
    setTestResults(prev => [...prev, ...results, '']);
  };

  // Test consumer logout isolation
  const testConsumerLogout = () => {
    const initialStatus = checkAuthStatus();
    const results: string[] = [];
    
    results.push('🔍 TESTING: Consumer logout isolation...');
    results.push(`Initial state - Admin: ${initialStatus.admin.supplier || initialStatus.admin.merchant}, Consumer: ${initialStatus.consumer}, Shipping: ${initialStatus.shipping}`);
    
    try {
      // Perform consumer logout
      logoutConsumer();
      
      // Check status after consumer logout
      const afterStatus = checkAuthStatus();
      
      // Verify consumer is logged out
      const consumerLoggedOut = !afterStatus.consumer;
      results.push(`✅ Consumer logout: ${consumerLoggedOut ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify admin session preserved
      const adminPreserved = (afterStatus.admin.supplier === initialStatus.admin.supplier) && 
                            (afterStatus.admin.merchant === initialStatus.admin.merchant);
      results.push(`${adminPreserved ? '✅' : '❌'} Admin session preserved: ${adminPreserved ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify shipping session preserved
      const shippingPreserved = afterStatus.shipping === initialStatus.shipping;
      results.push(`${shippingPreserved ? '✅' : '❌'} Shipping session preserved: ${shippingPreserved ? 'SUCCESS' : 'FAILED'}`);
      
      const testPassed = consumerLoggedOut && adminPreserved && shippingPreserved;
      results.push(`🎯 CONSUMER LOGOUT TEST: ${testPassed ? 'PASSED ✅' : 'FAILED ❌'}`);
      
      if (testPassed) {
        toast({
          title: "Consumer Logout Test Passed",
          description: "Consumer logout properly isolated from other sessions",
        });
      } else {
        toast({
          title: "Consumer Logout Test Failed", 
          description: "Consumer logout affected other authentication sessions",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      results.push(`❌ ERROR during consumer logout test: ${error}`);
    }
    
    setTestResults(prev => [...prev, ...results, '']);
  };

  // Test shipping logout isolation
  const testShippingLogout = () => {
    const initialStatus = checkAuthStatus();
    const results: string[] = [];
    
    results.push('🔍 TESTING: Shipping logout isolation...');
    results.push(`Initial state - Admin: ${initialStatus.admin.supplier || initialStatus.admin.merchant}, Consumer: ${initialStatus.consumer}, Shipping: ${initialStatus.shipping}`);
    
    try {
      // Perform shipping logout
      clearShippingCompanySession();
      
      // Check status after shipping logout
      const afterStatus = checkAuthStatus();
      
      // Verify shipping is logged out
      const shippingLoggedOut = !afterStatus.shipping;
      results.push(`✅ Shipping logout: ${shippingLoggedOut ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify admin session preserved
      const adminPreserved = (afterStatus.admin.supplier === initialStatus.admin.supplier) && 
                            (afterStatus.admin.merchant === initialStatus.admin.merchant);
      results.push(`${adminPreserved ? '✅' : '❌'} Admin session preserved: ${adminPreserved ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify consumer session preserved
      const consumerPreserved = afterStatus.consumer === initialStatus.consumer;
      results.push(`${consumerPreserved ? '✅' : '❌'} Consumer session preserved: ${consumerPreserved ? 'SUCCESS' : 'FAILED'}`);
      
      const testPassed = shippingLoggedOut && adminPreserved && consumerPreserved;
      results.push(`🎯 SHIPPING LOGOUT TEST: ${testPassed ? 'PASSED ✅' : 'FAILED ❌'}`);
      
      if (testPassed) {
        toast({
          title: "Shipping Logout Test Passed",
          description: "Shipping logout properly isolated from other sessions",
        });
      } else {
        toast({
          title: "Shipping Logout Test Failed",
          description: "Shipping logout affected other authentication sessions", 
          variant: "destructive"
        });
      }
      
    } catch (error) {
      results.push(`❌ ERROR during shipping logout test: ${error}`);
    }
    
    setTestResults(prev => [...prev, ...results, '']);
  };

  // Run all tests
  const runAllTests = async () => {
    setTestResults(['🚀 STARTING AUTHENTICATION ISOLATION TESTS...', '']);
    
    // Wait between tests to ensure proper state updates
    await testAdminLogout();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testConsumerLogout();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    testShippingLogout();
    
    setTestResults(prev => [...prev, '🏁 ALL TESTS COMPLETED']);
  };

  // Clear test results
  const clearResults = () => {
    setTestResults([]);
  };

  // Initialize auth status check
  useEffect(() => {
    checkAuthStatus();
  }, []);

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Authentication Isolation Test
          </CardTitle>
          <CardDescription>
            Test complete isolation between Admin, Consumer, and Shipping Company authentication systems
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Authentication Status */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Current Authentication Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Building className="h-4 w-4" />
                    <span className="font-medium">Admin</span>
                  </div>
                  <div className="space-y-1">
                    <Badge variant={authStatus.admin.supplier ? "default" : "secondary"}>
                      Supplier: {authStatus.admin.supplier ? "Active" : "Inactive"}
                    </Badge>
                    <Badge variant={authStatus.admin.merchant ? "default" : "secondary"}>
                      Merchant: {authStatus.admin.merchant ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Phone className="h-4 w-4" />
                    <span className="font-medium">Consumer</span>
                  </div>
                  <Badge variant={authStatus.consumer ? "default" : "secondary"}>
                    {authStatus.consumer ? "Authenticated" : "Not Authenticated"}
                  </Badge>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Truck className="h-4 w-4" />
                    <span className="font-medium">Shipping</span>
                  </div>
                  <Badge variant={authStatus.shipping ? "default" : "secondary"}>
                    {authStatus.shipping ? "Authenticated" : "Not Authenticated"}
                  </Badge>
                </CardContent>
              </Card>
            </div>
          </div>

          <Separator />

          {/* Test Controls */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Isolation Tests</h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={testAdminLogout} variant="outline">
                <LogOut className="h-4 w-4 mr-2" />
                Test Admin Logout
              </Button>
              <Button onClick={testConsumerLogout} variant="outline">
                <Phone className="h-4 w-4 mr-2" />
                Test Consumer Logout
              </Button>
              <Button onClick={testShippingLogout} variant="outline">
                <Truck className="h-4 w-4 mr-2" />
                Test Shipping Logout
              </Button>
              <Button onClick={runAllTests} className="bg-orange-500 hover:bg-orange-600">
                Run All Tests
              </Button>
              <Button onClick={checkAuthStatus} variant="ghost">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Status
              </Button>
              <Button onClick={clearResults} variant="ghost">
                Clear Results
              </Button>
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Test Results</h3>
              <Card>
                <CardContent className="p-4">
                  <pre className="text-sm whitespace-pre-wrap font-mono bg-gray-50 p-3 rounded max-h-96 overflow-y-auto">
                    {testResults.join('\n')}
                  </pre>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
