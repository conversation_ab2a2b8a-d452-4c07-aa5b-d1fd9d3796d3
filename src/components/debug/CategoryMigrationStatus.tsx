import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertCircle, RefreshCw, Database, HardDrive } from 'lucide-react';
import { useLegacyCategories, useMigrationStatus } from '@/data/categoryDataSupabase';
import { checkMigrationStatus, verifyMigrationIntegrity, getMigrationInstructions } from '@/utils/categoryMigration';

interface MigrationStatusData {
  isMigrated: boolean;
  categoriesCount: number;
  subcategoriesCount: number;
  error?: string;
}

interface IntegrityData {
  isValid: boolean;
  issues: string[];
  summary: {
    categoriesInSupabase: number;
    subcategoriesInSupabase: number;
    categoriesInLocalStorage: number;
    subcategoriesInLocalStorage: number;
  };
}

export const CategoryMigrationStatus: React.FC = () => {
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatusData | null>(null);
  const [integrityData, setIntegrityData] = useState<IntegrityData | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  // Use the hooks to get real-time status
  const { categories, isLoading, error } = useLegacyCategories();
  const { isMigrated, shouldUseFallback, categoriesCount } = useMigrationStatus();

  const checkStatus = async () => {
    setIsChecking(true);
    try {
      const status = await checkMigrationStatus();
      setMigrationStatus(status);

      if (status.isMigrated) {
        const integrity = await verifyMigrationIntegrity();
        setIntegrityData(integrity);
      }
    } catch (error) {
      console.error('Error checking migration status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  const getStatusIcon = () => {
    if (isChecking) return <RefreshCw className="h-5 w-5 animate-spin" />;
    if (isMigrated) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (migrationStatus?.error) return <XCircle className="h-5 w-5 text-red-500" />;
    return <AlertCircle className="h-5 w-5 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (isChecking) return 'Checking...';
    if (isMigrated) return 'Migration Completed';
    if (migrationStatus?.error) return 'Migration Failed';
    return 'Migration Pending';
  };

  const getStatusColor = () => {
    if (isMigrated) return 'bg-green-100 text-green-800';
    if (migrationStatus?.error) return 'bg-red-100 text-red-800';
    return 'bg-yellow-100 text-yellow-800';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Categories Migration Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="font-medium">{getStatusText()}</span>
            </div>
            <Badge className={getStatusColor()}>
              {isMigrated ? 'Supabase' : 'localStorage'}
            </Badge>
          </div>

          {/* Data Source Indicator */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2 p-3 rounded-lg bg-muted/50">
              <Database className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-sm font-medium">Supabase</div>
                <div className="text-xs text-muted-foreground">
                  {migrationStatus ? `${migrationStatus.categoriesCount} categories` : 'Checking...'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2 p-3 rounded-lg bg-muted/50">
              <HardDrive className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-sm font-medium">localStorage</div>
                <div className="text-xs text-muted-foreground">
                  {categories.length} categories
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {migrationStatus?.error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                Migration Error: {migrationStatus.error}
              </AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {isMigrated && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                ✅ Migration completed successfully! Using Supabase categories system.
              </AlertDescription>
            </Alert>
          )}

          {/* Integrity Check Results */}
          {integrityData && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Data Integrity Check</h4>
              {integrityData.isValid ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    ✅ All data integrity checks passed
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div>⚠️ Data integrity issues found:</div>
                    <ul className="mt-2 list-disc list-inside text-xs">
                      {integrityData.issues.map((issue, index) => (
                        <li key={index}>{issue}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={checkStatus}
              disabled={isChecking}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
              Refresh Status
            </Button>
            
            {!isMigrated && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowInstructions(!showInstructions)}
              >
                {showInstructions ? 'Hide' : 'Show'} Migration Instructions
              </Button>
            )}
          </div>

          {/* Migration Instructions */}
          {showInstructions && !isMigrated && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <pre className="text-xs whitespace-pre-wrap font-mono">
                  {getMigrationInstructions()}
                </pre>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
