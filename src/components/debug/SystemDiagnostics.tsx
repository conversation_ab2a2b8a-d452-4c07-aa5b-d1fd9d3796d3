import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, RefreshCw, Database, Users, ShoppingCart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';
import { createClient } from '@supabase/supabase-js';

export function SystemDiagnostics() {
  const [diagnostics, setDiagnostics] = useState({
    supabaseConnection: 'checking',
    authService: 'checking',
    productService: 'checking',
    marketplaceProducts: 'checking'
  });
  const [details, setDetails] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const { isAuthenticated, user, profile } = useAuth();
  const { products: tyreProducts, isLoading: tyresLoading, error: tyresError } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading, error: brakesError } = useMarketplaceProducts('brakes');
  const { products: filterProducts, isLoading: filtersLoading, error: filtersError } = useMarketplaceProducts('filters');

  const runDiagnostics = async () => {
    setIsRunning(true);
    const newDiagnostics = { ...diagnostics };
    const newDetails = {};

    try {
      // Test Supabase Connection
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseAnonKey) {
        newDiagnostics.supabaseConnection = 'error';
        newDetails.supabaseConnection = 'Missing environment variables';
      } else {
        try {
          const supabase = createClient(supabaseUrl, supabaseAnonKey);
          const { data, error } = await supabase.from('products').select('count', { count: 'exact', head: true });
          
          if (error) {
            newDiagnostics.supabaseConnection = 'error';
            newDetails.supabaseConnection = error.message;
          } else {
            newDiagnostics.supabaseConnection = 'success';
            newDetails.supabaseConnection = `Connected. Products count: ${data}`;
          }
        } catch (err) {
          newDiagnostics.supabaseConnection = 'error';
          newDetails.supabaseConnection = err.message;
        }
      }

      // Test Auth Service
      try {
        newDiagnostics.authService = isAuthenticated ? 'success' : 'warning';
        newDetails.authService = isAuthenticated 
          ? `Authenticated as ${user?.email} (${profile?.role})` 
          : 'Not authenticated';
      } catch (err) {
        newDiagnostics.authService = 'error';
        newDetails.authService = err.message;
      }

      // Test Product Service
      try {
        const productCounts = {
          tyres: tyreProducts.length,
          brakes: brakeProducts.length,
          filters: filterProducts.length
        };
        
        const hasProducts = Object.values(productCounts).some(count => count > 0);
        newDiagnostics.productService = hasProducts ? 'success' : 'warning';
        newDetails.productService = `Products: ${JSON.stringify(productCounts)}`;
        
        if (tyresError || brakesError || filtersError) {
          newDiagnostics.productService = 'error';
          newDetails.productService += ` Errors: ${[tyresError, brakesError, filtersError].filter(Boolean).map(e => e.message).join(', ')}`;
        }
      } catch (err) {
        newDiagnostics.productService = 'error';
        newDetails.productService = err.message;
      }

      // Test Marketplace Products
      try {
        const isLoading = tyresLoading || brakesLoading || filtersLoading;
        const totalProducts = tyreProducts.length + brakeProducts.length + filterProducts.length;
        
        newDiagnostics.marketplaceProducts = isLoading ? 'checking' : (totalProducts > 0 ? 'success' : 'warning');
        newDetails.marketplaceProducts = isLoading 
          ? 'Loading...' 
          : `Total marketplace products: ${totalProducts}`;
      } catch (err) {
        newDiagnostics.marketplaceProducts = 'error';
        newDetails.marketplaceProducts = err.message;
      }

    } catch (error) {
      console.error('Diagnostics error:', error);
    }

    setDiagnostics(newDiagnostics);
    setDetails(newDetails);
    setIsRunning(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, [isAuthenticated, tyreProducts, brakeProducts, filterProducts]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'checking':
        return <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />;
      default:
        return <RefreshCw className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status) => {
    const variants = {
      success: 'default',
      warning: 'secondary',
      error: 'destructive',
      checking: 'outline'
    };
    return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">System Diagnostics</h1>
        <Button onClick={runDiagnostics} disabled={isRunning}>
          {isRunning ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
          Run Diagnostics
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Supabase Connection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Supabase Connection
              {getStatusBadge(diagnostics.supabaseConnection)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-2">
              {getStatusIcon(diagnostics.supabaseConnection)}
              <span className="font-medium">Database Connection</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {details.supabaseConnection || 'Checking connection...'}
            </p>
          </CardContent>
        </Card>

        {/* Auth Service */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Authentication Service
              {getStatusBadge(diagnostics.authService)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-2">
              {getStatusIcon(diagnostics.authService)}
              <span className="font-medium">Auth Status</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {details.authService || 'Checking authentication...'}
            </p>
          </CardContent>
        </Card>

        {/* Product Service */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Product Service
              {getStatusBadge(diagnostics.productService)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-2">
              {getStatusIcon(diagnostics.productService)}
              <span className="font-medium">Product Data</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {details.productService || 'Checking product service...'}
            </p>
          </CardContent>
        </Card>

        {/* Marketplace Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Marketplace Products
              {getStatusBadge(diagnostics.marketplaceProducts)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-2">
              {getStatusIcon(diagnostics.marketplaceProducts)}
              <span className="font-medium">Marketplace Data</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {details.marketplaceProducts || 'Checking marketplace products...'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Environment Info */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not set'}
            </div>
            <div>
              <strong>Use Supabase:</strong> {import.meta.env.VITE_USE_SUPABASE || 'false'}
            </div>
            <div>
              <strong>Use Supabase Backend:</strong> {import.meta.env.VITE_USE_SUPABASE_BACKEND || 'false'}
            </div>
            <div>
              <strong>Environment:</strong> {import.meta.env.MODE || 'unknown'}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
