import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import Header from '@/components/Header'

// Mock the hooks and components
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: null,
    isAuthenticated: false,
    login: vi.fn(),
    logout: vi.fn(),
    loading: false,
  }),
}))

vi.mock('@/hooks/useCart', () => ({
  useCart: () => ({
    items: [],
    totalItems: 0,
    totalPrice: 0,
    addItem: vi.fn(),
    removeItem: vi.fn(),
    clearCart: vi.fn(),
  }),
}))

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('Header Component', () => {
  it('renders the AROUZ MARKET logo', () => {
    renderWithRouter(<Header />)
    
    const logo = screen.getByAltText(/arouz market/i)
    expect(logo).toBeInTheDocument()
  })

  it('displays navigation menu items', () => {
    renderWithRouter(<Header />)
    
    expect(screen.getByText(/home/<USER>
    expect(screen.getByText(/parts search engine/i)).toBeInTheDocument()
    expect(screen.getByText(/about/i)).toBeInTheDocument()
    expect(screen.getByText(/contact/i)).toBeInTheDocument()
  })

  it('shows login button when user is not authenticated', () => {
    renderWithRouter(<Header />)
    
    const loginButton = screen.getByText(/sign in/i)
    expect(loginButton).toBeInTheDocument()
  })

  it('displays cart icon with item count', () => {
    renderWithRouter(<Header />)
    
    const cartIcon = screen.getByTestId('cart-icon')
    expect(cartIcon).toBeInTheDocument()
  })

  it('opens mobile menu when hamburger is clicked', () => {
    renderWithRouter(<Header />)
    
    const hamburgerButton = screen.getByTestId('mobile-menu-button')
    fireEvent.click(hamburgerButton)
    
    // Check if mobile menu is visible
    const mobileMenu = screen.getByTestId('mobile-menu')
    expect(mobileMenu).toBeVisible()
  })

  it('has correct brand colors applied', () => {
    renderWithRouter(<Header />)
    
    const header = screen.getByRole('banner')
    expect(header).toHaveClass('bg-white')
  })

  it('is responsive and adapts to different screen sizes', () => {
    renderWithRouter(<Header />)
    
    // Check for responsive classes
    const nav = screen.getByRole('navigation')
    expect(nav).toHaveClass('hidden', 'md:flex')
  })
})

describe('Header Accessibility', () => {
  it('has proper ARIA labels', () => {
    renderWithRouter(<Header />)
    
    const nav = screen.getByRole('navigation')
    expect(nav).toHaveAttribute('aria-label')
  })

  it('supports keyboard navigation', () => {
    renderWithRouter(<Header />)
    
    const firstLink = screen.getByText(/home/<USER>
    firstLink.focus()
    expect(firstLink).toHaveFocus()
  })
})

describe('Header Performance', () => {
  it('renders quickly without performance issues', () => {
    const startTime = performance.now()
    renderWithRouter(<Header />)
    const endTime = performance.now()
    
    // Should render in less than 100ms
    expect(endTime - startTime).toBeLessThan(100)
  })
})
