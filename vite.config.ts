import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react({
      jsxRuntime: 'automatic',
      jsxImportSource: 'react',
      babel: {
        plugins: [
          ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
        ]
      }
    }),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': JSON.stringify(mode),
    __DEV__: mode === 'development',
  },
  // Copy .htaccess file to dist for SPA routing
  publicDir: 'public',
  build: {
    copyPublicDir: true,
    // Production optimizations - SIMPLIFIED FOR COMPATIBILITY
    target: 'es2020',
    minify: mode === 'production' ? 'esbuild' : false,
    sourcemap: false,
    cssCodeSplit: false,
    // Chunk size warnings
    chunkSizeWarningLimit: 2000,
    rollupOptions: {
      output: {
        // SIMPLIFIED chunk splitting to avoid React context issues
        manualChunks: undefined,
        // Standard chunk names
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        entryFileNames: 'assets/[name]-[hash].js',
      },
    },
  },
}));
