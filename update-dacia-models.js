import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the cars.json file
const carsJsonPath = path.join(__dirname, 'src', 'services', 'data', 'cars.json');
const carsJson = JSON.parse(fs.readFileSync(carsJsonPath, 'utf8'));

// Read the Dacia models file
const daciaModelsPath = path.join(__dirname, 'src', 'services', 'data', 'dacia-models.json');
const daciaModels = JSON.parse(fs.readFileSync(daciaModelsPath, 'utf8'));

// Find the Dacia brand in the cars.json file
const daciaBrandIndex = carsJson.findIndex(brand => brand.brand === 'Dacia');

if (daciaBrandIndex !== -1) {
  // Update the Dacia models
  carsJson[daciaBrandIndex].models = daciaModels;

  // Write the updated cars.json file
  fs.writeFileSync(carsJsonPath, JSON.stringify(carsJson, null, 2), 'utf8');

  console.log(`Added ${daciaModels.length} Dacia models to the cars.json file.`);
} else {
  console.error('Dacia brand not found in the cars.json file.');
}

// Clean up the temporary file
fs.unlinkSync(daciaModelsPath);

console.log('Temporary file cleaned up.');
