// Test script to verify the complete order confirmation flow
// Run this in browser console after adding items to cart and going through checkout

async function testOrderConfirmationFlow() {
  console.log('🧪 TESTING ORDER CONFIRMATION FLOW');
  console.log('==================================');
  
  try {
    // 1. Check cart items have shipping origin
    console.log('\n1️⃣ CHECKING CART ITEMS:');
    const cartData = localStorage.getItem('arouz_cart');
    if (cartData) {
      const cartItems = JSON.parse(cartData);
      console.log('📦 Cart items count:', cartItems.length);
      
      cartItems.forEach((item, index) => {
        console.log(`📋 Item ${index + 1}:`, {
          name: item.name,
          supplierName: item.supplierName,
          shippingOrigin: item.shippingOrigin,
          supplierAccountId: item.supplierAccountId,
          originalProductId: item.originalProductId
        });
      });
    } else {
      console.log('❌ No cart items found');
      return;
    }

    // 2. Check consumer authentication
    console.log('\n2️⃣ CHECKING CONSUMER AUTHENTICATION:');
    const phoneSession = localStorage.getItem('phone_auth_session');
    if (phoneSession) {
      const session = JSON.parse(phoneSession);
      console.log('✅ Consumer authenticated:', {
        phone: session.user?.phone,
        full_name: session.profile?.full_name,
        original_full_name: session.profile?.original_full_name
      });
    } else {
      console.log('❌ Consumer not authenticated');
      return;
    }

    // 3. Test order creation data preparation
    console.log('\n3️⃣ TESTING ORDER CREATION DATA:');
    const { createOrder } = await import('/src/services/orderService.ts');
    
    // Mock checkout data
    const mockCheckoutData = {
      deliveryAddress: 'Test Address, Alger',
      deliveryCoordinates: { lat: 36.7538, lng: 3.0588 },
      deliveryWilaya: 'Alger',
      googleMapsUrl: 'https://maps.google.com/?q=36.7538,3.0588',
      paymentMethod: 'cash_on_delivery',
      specialInstructions: 'Test order'
    };

    console.log('📋 Mock checkout data:', mockCheckoutData);
    console.log('📦 Cart items for order:', JSON.parse(cartData));

    // 4. Test supplier grouping logic
    console.log('\n4️⃣ TESTING SUPPLIER GROUPING:');
    const cartItems = JSON.parse(cartData);
    const itemsBySupplier = cartItems.reduce((acc, item) => {
      const supplierKey = item.supplierName || 'Unknown Supplier';
      if (!acc[supplierKey]) {
        acc[supplierKey] = {
          items: [],
          location: item.shippingOrigin || 'Location TBD'
        };
      }
      acc[supplierKey].items.push(item);
      return acc;
    }, {});

    console.log('🏢 Suppliers and locations:');
    Object.entries(itemsBySupplier).forEach(([supplier, data]) => {
      console.log(`   ${supplier}: ${data.location} (${data.items.length} items)`);
    });

    // 5. Test order item transformation
    console.log('\n5️⃣ TESTING ORDER ITEM TRANSFORMATION:');
    const transformedItems = cartItems.map(item => ({
      product_id: item.originalProductId || item.id.toString(),
      product_name: item.name,
      quantity: item.quantity,
      unit_price: item.price,
      total_price: item.price * item.quantity,
      supplier_name: item.supplierName || 'Unknown Supplier',
      supplier_wilaya: item.shippingOrigin || '',
      supplier_account_id: item.supplierAccountId || '',
      marketplace_section: item.marketplaceSection || 'retail'
    }));

    console.log('🔄 Transformed order items:');
    transformedItems.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.product_name} - ${item.supplier_name} (${item.supplier_wilaya})`);
    });

    // 6. Calculate totals
    console.log('\n6️⃣ CALCULATING TOTALS:');
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    console.log('💰 Subtotal:', new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(subtotal));
    console.log('🚚 Shipping: Calculated on next steps');
    console.log('🏢 AROUZ Fee: Calculated on next steps');
    console.log('💰 Total:', new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(subtotal));

    console.log('\n✅ ORDER CONFIRMATION FLOW TEST COMPLETED!');
    console.log('🎯 All data is properly structured for order creation');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testOrderConfirmationFlow();
