<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simplified Consumer Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #fa7b00;
            outline: none;
        }
        button {
            background: linear-gradient(to right, #fa7b00, #ff9500);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(250, 123, 0, 0.3);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #fa7b00;
            margin-bottom: 10px;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background-color: #e9ecef;
            border: 1px solid #ced4da;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Simplified Consumer Authentication</h1>
            <p>Test the new consumer authentication flow without SMS verification</p>
        </div>

        <form id="authForm">
            <div class="form-group">
                <label for="fullName">Full Name *</label>
                <input type="text" id="fullName" placeholder="Enter your full name" required>
            </div>

            <div class="form-group">
                <label for="phone">Phone Number *</label>
                <input type="tel" id="phone" placeholder="05XXXXXXXX" required>
            </div>

            <button type="submit" id="submitBtn">
                🚀 Test Authentication
            </button>
        </form>

        <div class="status">
            <strong>Current Authentication Status:</strong>
            <div id="authStatus">Checking...</div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        // Mock simplified consumer authentication for testing
        function validateAlgerianPhone(phone) {
            const cleanPhone = phone.replace(/\D/g, '');
            
            if (cleanPhone.length === 9 && cleanPhone.startsWith('5')) {
                return { isValid: true, formatted: `+213${cleanPhone}` };
            } else if (cleanPhone.length === 10 && cleanPhone.startsWith('05')) {
                return { isValid: true, formatted: `+213${cleanPhone.substring(1)}` };
            } else if (cleanPhone.length === 12 && cleanPhone.startsWith('213')) {
                return { isValid: true, formatted: `+${cleanPhone}` };
            } else if (cleanPhone.length === 13 && cleanPhone.startsWith('2135')) {
                return { isValid: true, formatted: `+${cleanPhone}` };
            }
            
            return { isValid: false, error: 'Please enter a valid Algerian phone number' };
        }

        function convertToPrivacyProtectedName(fullName) {
            const trimmedName = fullName.trim();
            const nameParts = trimmedName.split(' ').filter(part => part.length > 0);
            
            if (nameParts.length === 1) {
                const name = nameParts[0];
                const displayName = name.length > 3 ? `${name.substring(0, 3)}...` : name;
                return { displayName, fullName: trimmedName };
            } else {
                const firstName = nameParts[0];
                const lastInitial = nameParts[nameParts.length - 1].charAt(0).toUpperCase();
                const displayName = `${firstName} ${lastInitial}`;
                return { displayName, fullName: trimmedName };
            }
        }

        function mockAuthenticateConsumer(fullName, phone) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    const phoneValidation = validateAlgerianPhone(phone);
                    
                    if (!phoneValidation.isValid) {
                        resolve({
                            success: false,
                            error: phoneValidation.error
                        });
                        return;
                    }

                    if (!fullName || fullName.trim().length < 2) {
                        resolve({
                            success: false,
                            error: 'Please enter your full name (at least 2 characters)'
                        });
                        return;
                    }

                    const { displayName, fullName: originalName } = convertToPrivacyProtectedName(fullName);
                    const userId = 'test-' + Math.random().toString(36).substr(2, 9);
                    
                    // Check if user exists (mock)
                    const existingUser = localStorage.getItem(`consumer_${phoneValidation.formatted}`);
                    const action = existingUser ? 'login' : 'signup';

                    const user = {
                        id: userId,
                        phone: phoneValidation.formatted,
                        role: 'consumer'
                    };

                    const profile = {
                        id: userId,
                        phone: phoneValidation.formatted,
                        role: 'consumer',
                        full_name: displayName,
                        original_full_name: originalName,
                        phone_verified: true,
                        created_at: new Date().toISOString()
                    };

                    // Store session
                    const sessionData = {
                        user,
                        profile,
                        expires_at: Date.now() + (30 * 24 * 60 * 60 * 1000),
                        created_at: Date.now()
                    };

                    localStorage.setItem('phone_auth_session', JSON.stringify(sessionData));
                    localStorage.setItem(`consumer_${phoneValidation.formatted}`, JSON.stringify(profile));

                    resolve({
                        success: true,
                        user,
                        profile,
                        action
                    });
                }, 1000);
            });
        }

        function checkAuthStatus() {
            try {
                const phoneSession = localStorage.getItem('phone_auth_session');
                if (phoneSession) {
                    const session = JSON.parse(phoneSession);
                    
                    if (session.expires_at && Date.now() > session.expires_at) {
                        localStorage.removeItem('phone_auth_session');
                        return 'Not authenticated (session expired)';
                    }
                    
                    if (session.profile?.role === 'consumer') {
                        return `✅ Authenticated as: ${session.profile.full_name} (${session.profile.phone})`;
                    }
                }
                return '❌ Not authenticated';
            } catch (error) {
                return '❌ Error checking authentication';
            }
        }

        function updateAuthStatus() {
            document.getElementById('authStatus').textContent = checkAuthStatus();
        }

        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // Initialize
        updateAuthStatus();

        // Form submission
        document.getElementById('authForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fullName = document.getElementById('fullName').value;
            const phone = document.getElementById('phone').value;
            const submitBtn = document.getElementById('submitBtn');
            
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 Testing...';
            
            showResult('Testing authentication...', 'info');
            
            try {
                const result = await mockAuthenticateConsumer(fullName, phone);
                
                if (result.success) {
                    showResult(`✅ Authentication ${result.action} successful!

User ID: ${result.user.id}
Phone: ${result.user.phone}
Display Name: ${result.profile.full_name}
Original Name: ${result.profile.original_full_name}
Action: ${result.action}

Session stored in localStorage successfully.`, 'success');
                    
                    updateAuthStatus();
                } else {
                    showResult(`❌ Authentication failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 Test Authentication';
            }
        });

        // Clear session button
        const clearBtn = document.createElement('button');
        clearBtn.textContent = '🗑️ Clear Session';
        clearBtn.style.marginTop = '10px';
        clearBtn.style.backgroundColor = '#dc3545';
        clearBtn.onclick = () => {
            localStorage.removeItem('phone_auth_session');
            updateAuthStatus();
            showResult('Session cleared successfully', 'info');
        };
        document.querySelector('.container').appendChild(clearBtn);
    </script>
</body>
</html>
