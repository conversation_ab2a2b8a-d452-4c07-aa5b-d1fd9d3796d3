# AROUZ MARKET Checkout Data Flow - Testing Checklist

## 🎯 CRITICAL TESTING REQUIREMENTS

### **PHASE 1: Consumer Authentication Testing**

#### ✅ **New User Registration Flow**
1. **Navigate to marketplace** → Add product to cart → Proceed to checkout
2. **Phone Authentication Step 1**: Enter Algerian phone number (9 digits)
3. **Name Collection Step 2**: Enter full name (minimum 2 characters)
4. **OTP Verification Step 3**: Enter 6-digit verification code
5. **Verify Profile Creation**: Check that consumer profile is created with:
   - `phone`: +213XXXXXXXXX
   - `full_name`: Entered name
   - `role`: 'consumer'
   - `phone_verified`: true

#### ✅ **Existing User Login Flow**
1. **Navigate to marketplace** → Add product to cart → Proceed to checkout
2. **Phone Authentication Step 1**: Enter existing phone number
3. **OTP Verification Step 2**: Enter 6-digit verification code (skip name collection)
4. **Verify Profile Retrieval**: Check that existing consumer profile is loaded

### **PHASE 2: Multi-Supplier Cart Testing**

#### ✅ **Single Supplier Order**
1. **Add products from ONE supplier** to cart
2. **Verify cart items contain**:
   - `supplierAccountId`: Supplier's user_id
   - `supplierName`: Supplier's name
   - `originalProductId`: Product ID
   - `marketplaceSection`: 'wholesale' or 'retail'

#### ✅ **Multi-Supplier Order**
1. **Add products from MULTIPLE suppliers** to cart
2. **Verify each cart item contains**:
   - Correct `supplierAccountId` for each product
   - Different supplier names for different products
   - Proper product attribution

### **PHASE 3: Checkout Data Collection Testing**

#### ✅ **Step 1: Phone Authentication**
- Consumer phone number collected and verified
- Consumer name collected (new users) or retrieved (existing users)

#### ✅ **Step 2: Delivery Address**
- Delivery address entered and validated
- Wilaya selection from Algerian administrative divisions
- Google Maps URL generated and stored

#### ✅ **Step 3: Payment Method**
- Payment method selection (cash_on_delivery or store_pickup)
- Special instructions (optional)

#### ✅ **Step 4: Order Confirmation**
- **Consumer Information Display**:
  - Name: `profile.full_name` (NOT hardcoded "Consumer")
  - Phone: `user.phone`
- **Delivery Information Display**:
  - Address: Complete delivery address
  - Wilaya: Selected wilaya
  - Google Maps link: Clickable and functional
- **Supplier Information Display**:
  - Multiple suppliers listed correctly
  - Item count per supplier accurate

### **PHASE 4: Order Creation Testing**

#### ✅ **Database Order Creation**
1. **Click "Confirm Order"** in final step
2. **Verify orders table contains**:
   - `consumer_phone`: +213XXXXXXXXX
   - `consumer_name`: Actual consumer name (NOT "Consumer")
   - `order_number`: ARZ-YYYYMMDD-NNNN format
   - `delivery_address`: Complete address
   - `delivery_wilaya`: Selected wilaya
   - `google_maps_url`: Generated URL
   - `payment_method`: Selected method
   - `status`: 'pending'

#### ✅ **Database Order Items Creation**
1. **Verify order_items table contains**:
   - `product_id`: Original product ID
   - `product_name`: Product name
   - `quantity`: Selected quantity
   - `unit_price`: Product price
   - `total_price`: quantity × unit_price
   - `supplier_account_id`: Supplier's UUID
   - `supplier_name`: Supplier's name
   - `supplier_phone`: Supplier's phone from profiles table
   - `marketplace_section`: 'wholesale' or 'retail'
   - `category`: Product category
   - `subcategory`: Product subcategory

#### ✅ **Database Shipments Creation**
1. **Verify shipments table contains**:
   - `order_id`: Created order ID
   - `order_item_ids`: Array of order item IDs
   - `supplier_contact_info`: JSONB array with:
     ```json
     [
       {
         "supplier_name": "Supplier Name",
         "supplier_phone": "+213XXXXXXXXX",
         "supplier_city": "City",
         "supplier_wilaya": "Wilaya",
         "supplier_account_id": "uuid"
       }
     ]
     ```

### **PHASE 5: End-to-End Data Integrity Testing**

#### ✅ **Complete Flow Verification**
1. **Start**: Anonymous user visits marketplace
2. **Add Products**: Multiple products from different suppliers
3. **Authentication**: Phone + name collection
4. **Checkout**: Complete 4-step process
5. **Order Creation**: Successful order creation
6. **Data Verification**: All database tables populated correctly

#### ✅ **Critical Data Points Verification**
- ✅ Consumer name: NEVER "Consumer", always actual name
- ✅ Consumer phone: Always +213 format
- ✅ Supplier attribution: Each product linked to correct supplier
- ✅ Supplier phone: Populated from profiles table
- ✅ Order totals: Accurate calculations
- ✅ Delivery information: Complete and accurate

### **PHASE 6: Error Handling Testing**

#### ✅ **Authentication Errors**
- Invalid phone number format
- OTP verification failure
- Network connectivity issues

#### ✅ **Checkout Errors**
- Missing delivery address
- Invalid wilaya selection
- Empty cart scenarios

#### ✅ **Order Creation Errors**
- Database connection failures
- Missing supplier information
- Invalid product data

## 🚀 TESTING ENVIRONMENT

### **Local Development**
- URL: http://localhost:8081/
- Database: Supabase local instance
- Authentication: Dexatel SMS service

### **Required Test Data**
- Multiple supplier accounts with products
- Valid Algerian phone numbers for testing
- Complete product catalog with supplier attribution

## 📊 SUCCESS CRITERIA

### **100% Data Accuracy Requirements**
1. ✅ Consumer names displayed correctly (never hardcoded)
2. ✅ All supplier information captured and linked
3. ✅ Complete order data in all database tables
4. ✅ Shipping companies have access to supplier contact info
5. ✅ No data loss during cart → order transformation

### **Performance Requirements**
- Order creation: < 3 seconds
- Authentication flow: < 30 seconds total
- UI responsiveness: < 75ms transitions

### **User Experience Requirements**
- Intuitive 4-step checkout flow
- Clear error messages and validation
- Professional order confirmation display
- Seamless multi-supplier handling

## 🔧 DEBUGGING TOOLS

### **Browser Console Logs**
- Check for transformation logs in orderService
- Verify cart item structure
- Monitor authentication events

### **Database Queries**
```sql
-- Check consumer profiles
SELECT * FROM profiles WHERE role = 'consumer' ORDER BY created_at DESC;

-- Check recent orders
SELECT * FROM orders ORDER BY created_at DESC LIMIT 5;

-- Check order items with supplier info
SELECT oi.*, p.phone as supplier_phone_lookup 
FROM order_items oi 
LEFT JOIN profiles p ON oi.supplier_account_id = p.id 
ORDER BY oi.created_at DESC;

-- Check shipments with supplier contact info
SELECT id, order_id, supplier_contact_info 
FROM shipments 
ORDER BY created_at DESC;
```

### **Network Tab Monitoring**
- Monitor API calls to Supabase
- Check SMS OTP requests to Dexatel
- Verify order creation requests

## ✅ FINAL VERIFICATION

After completing all tests, verify:
1. 🎯 Consumer names are NEVER hardcoded
2. 🎯 Multi-supplier orders work perfectly
3. 🎯 All database columns are populated
4. 🎯 Shipping companies have supplier contact info
5. 🎯 Order confirmation displays accurate data
