# AROUZ MARKET Admin Panel Testing Guide

**🎯 CRITICAL: This guide will help you test the complete order-to-shipping workflow that was missing from your system.**

---

## **🚀 STEP 1: SET UP ADMIN USER**

### **1.1 Run Database Migration**
```bash
# Apply the admin user migration
supabase db push
```

### **1.2 Create Admin User in Supabase Auth**
1. Go to [Supabase Dashboard](https://app.supabase.io/) → Your Project → Authentication → Users
2. Click "Add User"
3. Create user with:
   - **Email:** `<EMAIL>`
   - **Password:** `AdminArouz2025!`
   - **Email Confirmed:** ✅ Yes

### **1.3 Update Admin Profile**
```sql
-- Replace USER_ID with the actual UUID from Supabase Auth
UPDATE profiles 
SET 
    id = 'YOUR_ACTUAL_ADMIN_USER_ID_HERE',
    role = 'admin',
    is_verified = true
WHERE email = '<EMAIL>';
```

---

## **🧪 STEP 2: TEST ADMIN LOGIN**

### **2.1 Access Admin Login**
1. Go to: `http://localhost:8080/admin/login`
2. Enter credentials:
   - **Email:** `<EMAIL>`
   - **Password:** `AdminArouz2025!`
3. Click "Access Admin Panel"

### **2.2 Verify Admin Dashboard**
- Should redirect to: `http://localhost:8080/admin/dashboard`
- Should show admin statistics and overview
- Should display "Orders Requiring Action" section

---

## **🛒 STEP 3: CREATE TEST ORDER (Consumer Side)**

### **3.1 Place Consumer Order**
1. Go to: `http://localhost:8080`
2. Add products to cart
3. Go to checkout: `http://localhost:8080/basket`
4. Complete phone authentication
5. Fill delivery details
6. Confirm order

### **3.2 Confirm Order (Supplier Side)**
1. Login as supplier/merchant
2. Go to: `http://localhost:8080/app/orders`
3. Find the pending order
4. Click "Confirm Order"
5. **CRITICAL:** Order status should change to "confirmed"

---

## **🏢 STEP 4: TEST ADMIN ORDER ASSIGNMENT**

### **4.1 Access Admin Orders Management**
1. Login to admin panel: `http://localhost:8080/admin/login`
2. Go to: `http://localhost:8080/admin/orders`
3. Should see the confirmed order in "Needs Assignment" status

### **4.2 Assign Order to Shipping Company**
1. Find the confirmed order
2. Select shipping company from dropdown
3. Click "Assign to Shipping"
4. **CRITICAL:** Should see success message
5. Order should show "Assigned" status

---

## **🚚 STEP 5: VERIFY SHIPPING PORTAL**

### **5.1 Check Shipping Company Dashboard**
1. Go to: `http://localhost:8080/shipping/dashboard`
2. Login with shipping company credentials
3. **CRITICAL:** The assigned order should now appear in "Assigned Orders"
4. Order should show all details including supplier contact info

### **5.2 Test Order Status Updates**
1. In shipping portal, update order status:
   - Assigned → Picked Up → In Transit → Delivered
2. Verify status updates are reflected across all dashboards

---

## **📊 STEP 6: VERIFY COMPLETE WORKFLOW**

### **6.1 End-to-End Verification**
```
✅ Consumer places order
✅ Supplier confirms order  
✅ Admin assigns to shipping company
✅ Shipment record created automatically
✅ Order appears in shipping portal
✅ Status updates work across all dashboards
```

### **6.2 Database Verification**
```sql
-- Check order was created
SELECT * FROM orders WHERE order_number LIKE 'ARZ-%' ORDER BY created_at DESC LIMIT 5;

-- Check shipment was created
SELECT 
    s.*,
    o.order_number,
    sc.company_name
FROM shipments s
JOIN orders o ON o.id = s.order_id
JOIN shipping_companies sc ON sc.id = s.shipping_company_id
ORDER BY s.created_at DESC LIMIT 5;

-- Check supplier contact info is included
SELECT 
    supplier_contact_info,
    company_name,
    status
FROM shipments 
WHERE supplier_contact_info IS NOT NULL
ORDER BY created_at DESC LIMIT 3;
```

---

## **🎯 CRITICAL SUCCESS CRITERIA**

### **✅ MUST WORK:**
1. **Admin Login:** Access admin panel with credentials
2. **Order Visibility:** Confirmed orders appear in admin orders list
3. **Assignment Interface:** Can select and assign shipping companies
4. **Shipment Creation:** Shipment records created automatically
5. **Shipping Portal:** Orders appear in shipping company dashboard
6. **Supplier Contact:** Shipping companies can see supplier phone numbers

### **🚨 TROUBLESHOOTING:**

#### **Problem: Admin login fails**
- Check user exists in Supabase Auth
- Verify profile has `role = 'admin'`
- Check RLS policies are applied

#### **Problem: Orders don't appear in admin panel**
- Verify orders have `status = 'confirmed'`
- Check admin RLS policies
- Ensure order_items exist

#### **Problem: Assignment fails**
- Check shipping company exists and is active
- Verify coverage areas include order wilaya
- Check shipments table permissions

#### **Problem: Orders don't appear in shipping portal**
- Verify shipment record was created
- Check shipping company login code
- Ensure supplier_contact_info is populated

---

## **🚀 NEXT STEPS AFTER SUCCESSFUL TESTING**

1. **✅ Workflow Confirmed:** Admin panel successfully bridges the gap
2. **📈 Scale Testing:** Test with multiple orders and companies
3. **🔧 Refinements:** Add more shipping companies and coverage areas
4. **📊 Analytics:** Implement detailed reporting and analytics
5. **🎨 UI Polish:** Enhance admin interface based on usage

---

## **🎯 EXPECTED RESULTS**

**After following this guide, you should have:**
- ✅ Working admin panel with order assignment
- ✅ Complete order-to-shipping workflow
- ✅ Automatic shipment record creation
- ✅ Orders appearing in shipping portals
- ✅ End-to-end order fulfillment system

**This solves the critical missing piece: orders now automatically flow from confirmation to shipping assignment to delivery tracking!** 🚀
