# =============================================================================
# AROUZ MARKET - DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains development-specific environment variables
# Used for: GitHub Pages deployment, testing, and development builds

# Application Configuration
VITE_APP_ENV=development
VITE_APP_URL=https://hamzaarou.github.io/parts-library-central
VITE_APP_NAME=AROUZ MARKET (Dev)

# Feature Flags - Development has all features enabled for testing
VITE_ENABLE_PRODUCT_FEATURES=true
VITE_ENABLE_REAL_TIME_UPDATES=true
VITE_ENABLE_IMAGE_UPLOAD=true
VITE_ENABLE_DEBUG_MODE=true
VITE_SHOW_PERFORMANCE_METRICS=true
VITE_ENABLE_TESTING_TOOLS=true

# Supabase Configuration - PRODUCTION Database (SAME as production)
# Uses the SAME database as production for testing
VITE_SUPABASE_URL=https://irkwpzcskeqtasutqnxp.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94
VITE_USE_SUPABASE=true
VITE_USE_SUPABASE_BACKEND=true

# External Services - PRODUCTION Keys (SAME as production)
VITE_GOOGLE_MAPS_API_KEY=AIzaSyDaxGYLwxp9HWdISSJN7UONErkEltUbuPY
VITE_DEXATEL_API_KEY=4595fd59fb0655b92b79c6fa00fb5d36

# Development-specific settings
VITE_API_TIMEOUT=10000
VITE_ENABLE_MOCK_DATA=false
VITE_LOG_LEVEL=debug
VITE_ENABLE_ANALYTICS=false

# Build Configuration
VITE_BUILD_TARGET=development
VITE_SOURCE_MAPS=true
VITE_MINIFY=false

# Security Settings (Relaxed for development)
VITE_ENABLE_CSP=false
VITE_ENABLE_HTTPS_ONLY=false

# Performance Settings
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_BUNDLE_ANALYZER=false
